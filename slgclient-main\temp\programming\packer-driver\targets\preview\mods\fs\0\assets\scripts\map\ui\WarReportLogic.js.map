{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "Prefab", "instantiate", "MapUICommand", "WarReportDesLogic", "EventMgr", "ListLogic", "AudioManager", "LogicEvent", "ccclass", "property", "WarReportLogic", "onEnable", "on", "upateWarReport", "initView", "clickWarReport", "openWarPortDes", "closeReport", "close", "onDisable", "targetOff", "node", "active", "onClickClose", "instance", "playClick", "report", "getInstance", "proxy", "getWarReport", "comp", "scrollView", "getComponent", "setData", "updateView", "qryWarReport", "data", "console", "log", "_warPortDesNode", "warPortDesPrefab", "parent", "allRead", "warRead"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,W,OAAAA,W;;AAGnDC,MAAAA,Y;;AAEAC,MAAAA,iB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;AACEC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OARH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAWTa,c,WADpBF,OAAO,CAAC,gBAAD,C,UAGHC,QAAQ,CAACV,UAAD,C,UAGRU,QAAQ,CAACT,MAAD,C,oCANb,MACqBU,cADrB,SAC4CZ,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,mDAOnB,IAPmB;AAAA;;AASxCa,QAAAA,QAAQ,GAAO;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,cAAvB,EAAuC,KAAKC,QAA5C,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,cAAvB,EAAuC,KAAKC,cAA5C,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,WAAvB,EAAoC,KAAKC,KAAzC,EAAgD,IAAhD;AACH;;AAGSC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOF,QAAAA,KAAK,GAAG;AACZ,eAAKG,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,eAAKP,KAAL;AACH;;AAGSJ,QAAAA,QAAQ,GAAO;AACrB,cAAIY,MAAkB,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,YAAjC,EAAzB;AAEA,cAAIC,IAAI,GAAG,KAAKC,UAAL,CAAgBV,IAAhB,CAAqBW,YAArB;AAAA;AAAA,qCAAX;AACAF,UAAAA,IAAI,CAACG,OAAL,CAAaP,MAAb;AACH;;AAEMQ,QAAAA,UAAU,GAAO;AACpB,eAAKpB,QAAL;AACA;AAAA;AAAA,4CAAaa,WAAb,GAA2BQ,YAA3B;AACH;;AAESnB,QAAAA,cAAc,CAACoB,IAAD,EAAqB;AACzCC,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;;AACA,cAAI,KAAKC,eAAL,IAAwB,IAA5B,EAAkC;AAC9B,iBAAKA,eAAL,GAAuBtC,WAAW,CAAC,KAAKuC,gBAAN,CAAlC;AACA,iBAAKD,eAAL,CAAqBE,MAArB,GAA8B,KAAKpB,IAAnC;AACH,WAHD,MAGO;AACH,iBAAKkB,eAAL,CAAqBjB,MAArB,GAA8B,IAA9B;AACH;;AAED,eAAKiB,eAAL,CAAqBP,YAArB;AAAA;AAAA,sDAAqDC,OAArD,CAA6DG,IAA7D;AACH;;AAESM,QAAAA,OAAO,GAAO;AACpB;AAAA;AAAA,4CAAalB,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaE,WAAb,GAA2BgB,OAA3B,CAAmC,CAAnC;AACH;;AAzDiD,O;;;;;iBAG1B,I;;;;;;;iBAGG,I", "sourcesContent": ["// // Learn TypeScript:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html\n// // Learn Attribute:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html\n// // Learn life-cycle callbacks:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html\n\nimport { _decorator, Component, ScrollView, Prefab, Node, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport MapUICommand from \"./MapUICommand\";\nimport { WarReport } from \"./MapUIProxy\";\nimport WarReportDesLogic from './WarReportDesLogic';\nimport { EventMgr } from '../../utils/EventMgr';\nimport ListLogic from '../../utils/ListLogic';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('WarReportLogic')\nexport default class WarReportLogic extends Component {\n\n    @property(ScrollView)\n    scrollView:ScrollView = null;\n\n    @property(Prefab)\n    warPortDesPrefab: Prefab = null;\n    private _warPortDesNode:Node = null;\n\n    protected onEnable():void{\n        EventMgr.on(LogicEvent.upateWarReport, this.initView, this);\n        EventMgr.on(LogicEvent.clickWarReport, this.openWarPortDes, this);\n        EventMgr.on(LogicEvent.closeReport, this.close, this);\n    }\n\n    \n    protected onDisable():void{\n        EventMgr.targetOff(this);\n    }\n\n    private close() {\n        this.node.active = false;\n    }\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.close();\n    }\n\n\n    protected initView():void{\n        var report:WarReport[] = MapUICommand.getInstance().proxy.getWarReport();\n        \n        var comp = this.scrollView.node.getComponent(ListLogic);\n        comp.setData(report);\n    }\n\n    public updateView():void{\n        this.initView();\n        MapUICommand.getInstance().qryWarReport();\n    }\n\n    protected openWarPortDes(data:WarReport):void{\n        console.log(\"openWarPortDes\");\n        if (this._warPortDesNode == null) {\n            this._warPortDesNode = instantiate(this.warPortDesPrefab);\n            this._warPortDesNode.parent = this.node;\n        } else {\n            this._warPortDesNode.active = true;\n        }\n\n        this._warPortDesNode.getComponent(WarReportDesLogic).setData(data);\n    }\n\n    protected allRead():void{\n        AudioManager.instance.playClick();\n        MapUICommand.getInstance().warRead(0);\n    }\n}\n"]}