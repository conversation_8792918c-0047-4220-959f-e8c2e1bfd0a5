{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"], "names": ["EventHandler", "mgr", "constructor", "handler", "target", "Map", "instance", "_instance", "on", "name", "events", "has", "set", "get", "i", "length", "eh", "console", "log", "push", "targetInName", "Set", "add", "off", "splice", "emit", "args", "apply", "targetOff", "for<PERSON>ach", "clear", "EventMgr"], "mappings": ";;;iBACMA,Y,EAUAC,G;;;;;;;;;;;;;AAVAD,MAAAA,Y,GAAN,MAAMA,YAAN,CAAmB;AAIfE,QAAAA,WAAW,CAACC,OAAD,EAAmBC,MAAnB,EAAiC;AACxC,eAAKD,OAAL,GAAeA,OAAf;AACA,eAAKC,MAAL,GAAcA,MAAd;AACH;;AAPc,O;AAUbH,MAAAA,G,GAAN,MAAMA,GAAN,CAAU;AAAA;AAAA,0CAEuC,IAAII,GAAJ,EAFvC;;AAAA,gDAG0C,IAAIA,GAAJ,EAH1C;AAAA;;AAKgB,eAARC,QAAQ,GAAQ;AAC1B,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIN,GAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKM,SAAZ;AACH;;AAEMC,QAAAA,EAAE,CAACC,IAAD,EAAcN,OAAd,EAAgCC,MAAhC,EAA8C;AACnD,cAAG,CAAC,KAAKM,MAAL,CAAYC,GAAZ,CAAgBF,IAAhB,CAAJ,EAA0B;AACtB,iBAAKC,MAAL,CAAYE,GAAZ,CAAgBH,IAAhB,EAAsB,EAAtB;AACH;;AAED,cAAIC,MAAM,GAAG,KAAKA,MAAL,CAAYG,GAAZ,CAAgBJ,IAAhB,CAAb;;AACA,eAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,MAAM,CAACK,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACpC,gBAAME,GAAe,GAAGN,MAAM,CAACI,CAAD,CAA9B;;AACA,gBAAGE,GAAE,CAACb,OAAH,IAAcA,OAAd,IAAyBa,GAAE,CAACZ,MAAH,IAAaA,MAAzC,EAAgD;AAC5C;AACAa,cAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuBT,IAAvB,EAA6BN,OAA7B,EAAsCC,MAAtC;AACA;AACH;AACJ;;AAED,cAAIY,EAAe,GAAG,IAAIhB,YAAJ,CAAiBG,OAAjB,EAA0BC,MAA1B,CAAtB;AACAM,UAAAA,MAAM,CAACS,IAAP,CAAYH,EAAZ;;AAEA,cAAG,CAAC,KAAKI,YAAL,CAAkBT,GAAlB,CAAsBP,MAAtB,CAAJ,EAAkC;AAC9B,iBAAKgB,YAAL,CAAkBR,GAAlB,CAAsBR,MAAtB,EAA8B,IAAIiB,GAAJ,EAA9B;AACH;;AACD,cAAIT,GAAG,GAAG,KAAKQ,YAAL,CAAkBP,GAAlB,CAAsBT,MAAtB,CAAV;AACAQ,UAAAA,GAAG,CAACU,GAAJ,CAAQb,IAAR;AACH;;AAEMc,QAAAA,GAAG,CAACd,IAAD,EAAcN,OAAd,EAAgCC,MAAhC,EAA8C;AACpD,cAAG,CAAC,KAAKM,MAAL,CAAYC,GAAZ,CAAgBF,IAAhB,CAAJ,EAA0B;AACtB;AACH;;AAED,cAAIC,MAAM,GAAG,KAAKA,MAAL,CAAYG,GAAZ,CAAgBJ,IAAhB,CAAb;;AACA,eAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,MAAM,CAACK,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACpC,gBAAME,EAAe,GAAGN,MAAM,CAACI,CAAD,CAA9B;;AACA,gBAAGE,EAAE,CAACb,OAAH,IAAcA,OAAd,IAAyBa,EAAE,CAACZ,MAAH,IAAaA,MAAzC,EAAgD;AAC5CM,cAAAA,MAAM,CAACc,MAAP,CAAcV,CAAd,EAAiBA,CAAC,GAAC,CAAnB;AACA;AACH;AACJ;AACJ;;AAEMW,QAAAA,IAAI,CAAChB,IAAD,EAA0B;AACjC,cAAG,CAAC,KAAKC,MAAL,CAAYC,GAAZ,CAAgBF,IAAhB,CAAJ,EAA0B;AACtB;AACH;;AAED,cAAIC,MAAM,GAAG,KAAKA,MAAL,CAAYG,GAAZ,CAAgBJ,IAAhB,CAAb;;AALiC,4CAATiB,IAAS;AAATA,YAAAA,IAAS;AAAA;;AAMjC,eAAK,IAAIZ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,MAAM,CAACK,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACpC,gBAAME,EAAe,GAAGN,MAAM,CAACI,CAAD,CAA9B;AACAE,YAAAA,EAAE,CAACb,OAAH,CAAWwB,KAAX,CAAiBX,EAAE,CAACZ,MAApB,EAA4BsB,IAA5B;AACH;AACJ;;AAEME,QAAAA,SAAS,CAACxB,MAAD,EAAe;AAE3B,cAAG,CAAC,KAAKgB,YAAL,CAAkBT,GAAlB,CAAsBP,MAAtB,CAAJ,EAAkC;AAC9B;AACH;;AAED,cAAIgB,YAAY,GAAG,KAAKA,YAAL,CAAkBP,GAAlB,CAAsBT,MAAtB,CAAnB;AAEAgB,UAAAA,YAAY,CAACS,OAAb,CAAqBpB,IAAI,IAAI;AACzB,gBAAG,KAAKC,MAAL,CAAYC,GAAZ,CAAgBF,IAAhB,CAAH,EAAyB;AACtB,kBAAIC,MAAM,GAAG,KAAKA,MAAL,CAAYG,GAAZ,CAAgBJ,IAAhB,CAAb;;AACA,mBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,MAAM,CAACK,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACpC,oBAAME,EAAE,GAAGN,MAAM,CAACI,CAAD,CAAjB;;AACA,oBAAGE,EAAE,CAACZ,MAAH,IAAaA,MAAhB,EAAuB;AAClBM,kBAAAA,MAAM,CAACc,MAAP,CAAcV,CAAd,EAAiBA,CAAC,GAAC,CAAnB;AACAA,kBAAAA,CAAC,GAAGA,CAAC,GAAC,CAAN;AACJ;AACJ;AACH;AACJ,WAXD;AAaAM,UAAAA,YAAY,CAACU,KAAb;AACH;;AAtFK,O;;sBAAJ7B,G,eAC8B,I;;0BAwFhC8B,Q,GAAW9B,GAAG,CAACK,QAAJ,E", "sourcesContent": ["\nclass EventHandler {\n    public handler!:Function;\n    public target!:object;\n\n    constructor(handler:Function, target:object){\n        this.handler = handler;\n        this.target = target;\n    }\n}\n\nclass mgr {\n    private static _instance: mgr = null;\n    private events:Map<string, EventHandler[]> = new Map();\n    private targetInName:Map<object, Set<string>> = new Map();\n\n    public static instance(): mgr {\n        if (this._instance == null) {\n            this._instance = new mgr();\n        }\n        return this._instance;\n    }\n\n    public on(name:string, handler:Function, target:object){\n        if(!this.events.has(name)){\n            this.events.set(name, []);\n        }\n\n        var events = this.events.get(name);\n        for (let i = 0; i < events.length; i++) {\n            const eh:EventHandler = events[i];\n            if(eh.handler == handler && eh.target == target){\n                //已经添加过了\n                console.log(\"已经添加过了:\", name, handler, target);\n                return;\n            }\n        }\n\n        var eh:EventHandler = new EventHandler(handler, target);\n        events.push(eh);\n\n        if(!this.targetInName.has(target)){\n            this.targetInName.set(target, new Set())\n        }\n        var set = this.targetInName.get(target);\n        set.add(name);\n    }\n\n    public off(name:string, handler:Function, target:object){\n        if(!this.events.has(name)){\n            return;\n        }\n\n        var events = this.events.get(name);\n        for (let i = 0; i < events.length; i++) {\n            const eh:EventHandler = events[i];\n            if(eh.handler == handler && eh.target == target){\n                events.splice(i, i+1);\n                break;\n            }\n        }\n    }\n\n    public emit(name:string, ...args:any){\n        if(!this.events.has(name)){\n            return;\n        }\n\n        var events = this.events.get(name);\n        for (let i = 0; i < events.length; i++) {\n            const eh:EventHandler = events[i];\n            eh.handler.apply(eh.target, args);\n        }\n    }\n\n    public targetOff(target:object){\n\n        if(!this.targetInName.has(target)){\n            return;\n        }\n\n        var targetInName = this.targetInName.get(target);\n\n        targetInName.forEach(name => {\n            if(this.events.has(name)){\n               let events = this.events.get(name);\n               for (let i = 0; i < events.length; i++) {\n                   const eh = events[i];\n                   if(eh.target == target){\n                        events.splice(i, i+1);\n                        i = i-1;\n                   }\n               }\n            }\n        });\n        \n        targetInName.clear();\n    }\n}\n\nvar EventMgr = mgr.instance();\nexport { EventMgr };\n"]}