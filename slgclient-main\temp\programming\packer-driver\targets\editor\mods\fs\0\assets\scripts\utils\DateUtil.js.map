{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"], "names": ["DateUtil", "setServerTime", "time", "_serverTime", "_getServerTime", "Date", "now", "getServerTime", "nowTime", "isAfterServerTime", "stms", "st", "leftTime", "leftTimeStr", "diff", "converSecondStr", "fillZero", "str", "num", "length", "converTimeStr", "ms", "format", "date", "year", "getFullYear", "month", "getMonth", "dat", "getDate", "hour", "getHours", "minute", "getMinutes", "second", "getSeconds", "minSecond", "getMilliseconds", "replace", "Math", "floor"], "mappings": ";;;iBACqBA,Q;;;;;;;;;;;;;;;yBAAAA,Q,GAAN,MAAMA,QAAN,CAAe;AAIC,eAAbC,aAAa,CAACC,IAAD,EAAqB;AAC5C,eAAKC,WAAL,GAAmBD,IAAnB;AACA,eAAKE,cAAL,GAAsBC,IAAI,CAACC,GAAL,EAAtB;AACH;;AAE0B,eAAbC,aAAa,GAAW;AAClC,cAAIC,OAAe,GAAGH,IAAI,CAACC,GAAL,EAAtB;AACA,iBAAOE,OAAO,GAAG,KAAKJ,cAAf,GAAgC,KAAKD,WAA5C;AACH,SAZyB,CAc1B;;;AAC+B,eAAjBM,iBAAiB,CAACC,IAAD,EAAqB;AAChD,cAAIC,EAAE,GAAG,KAAKJ,aAAL,EAAT;AACA,iBAAOI,EAAE,GAAGD,IAAL,GAAY,CAAnB;AACH;;AAEqB,eAARE,QAAQ,CAACF,IAAD,EAAoB;AACtC,cAAIC,EAAE,GAAG,KAAKJ,aAAL,EAAT;AACA,iBAAOG,IAAI,GAAGC,EAAd;AACH;;AAEwB,eAAXE,WAAW,CAACH,IAAD,EAAoB;AACzC,cAAII,IAAI,GAAG,KAAKF,QAAL,CAAcF,IAAd,CAAX;AACA,iBAAO,KAAKK,eAAL,CAAqBD,IAArB,CAAP;AACH;AAED;;;AACsB,eAARE,QAAQ,CAACC,GAAD,EAAcC,GAAW,GAAG,CAA5B,EAAuC;AACzD,iBAAOD,GAAG,CAACE,MAAJ,GAAaD,GAApB,EAAyB;AACrBD,YAAAA,GAAG,GAAG,MAAMA,GAAZ;AACH;;AACD,iBAAOA,GAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC+B,eAAbG,aAAa,CAACC,EAAD,EAAaC,MAAc,GAAG,UAA9B,EAAkD;AACzE,cAAIC,IAAU,GAAG,IAAIlB,IAAJ,CAASgB,EAAT,CAAjB;AACA,cAAIG,IAAY,GAAG,KAAKR,QAAL,CAAcO,IAAI,CAACE,WAAL,KAAqB,EAAnC,EAAuC,CAAvC,CAAnB;AACA,cAAIC,KAAa,GAAG,KAAKV,QAAL,CAAeO,IAAI,CAACI,QAAL,KAAkB,CAAnB,GAAwB,EAAtC,EAA0C,CAA1C,CAApB;AACA,cAAIC,GAAW,GAAG,KAAKZ,QAAL,CAAcO,IAAI,CAACM,OAAL,KAAiB,EAA/B,EAAmC,CAAnC,CAAlB;AACA,cAAIC,IAAY,GAAG,KAAKd,QAAL,CAAcO,IAAI,CAACQ,QAAL,KAAkB,EAAhC,EAAoC,CAApC,CAAnB;AACA,cAAIC,MAAc,GAAG,KAAKhB,QAAL,CAAcO,IAAI,CAACU,UAAL,KAAoB,EAAlC,EAAsC,CAAtC,CAArB;AACA,cAAIC,MAAc,GAAG,KAAKlB,QAAL,CAAcO,IAAI,CAACY,UAAL,KAAoB,EAAlC,EAAsC,CAAtC,CAArB;AACA,cAAIC,SAAiB,GAAG,KAAKpB,QAAL,CAAcO,IAAI,CAACc,eAAL,KAAyB,EAAvC,EAA2C,CAA3C,CAAxB;AAEA,cAAIpB,GAAW,GAAGK,MAAM,GAAG,EAA3B;AACAL,UAAAA,GAAG,GAAGK,MAAM,CAACgB,OAAP,CAAe,MAAf,EAAuBd,IAAvB,CAAN;AACAP,UAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,IAAZ,EAAkBZ,KAAlB,CAAN;AACAT,UAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,IAAZ,EAAkBV,GAAlB,CAAN;AACAX,UAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,IAAZ,EAAkBR,IAAlB,CAAN;AACAb,UAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,IAAZ,EAAkBN,MAAlB,CAAN;AACAf,UAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,IAAZ,EAAkBJ,MAAlB,CAAN;AACAjB,UAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,KAAZ,EAAmBF,SAAnB,CAAN;AACA,iBAAOnB,GAAP;AACH;AAED;;;AAC6B,eAAfF,eAAe,CAACM,EAAD,EAAaC,MAAc,GAAG,UAA9B,EAAkD;AAC3E,cAAIY,MAAc,GAAGK,IAAI,CAACC,KAAL,CAAWnB,EAAE,GAAG,IAAhB,CAArB;AACA,cAAIS,IAAY,GAAGS,IAAI,CAACC,KAAL,CAAWN,MAAM,GAAG,IAApB,CAAnB,CAF2E,CAG3E;;AAEAA,UAAAA,MAAM,IAAIJ,IAAI,GAAG,IAAjB;AACA,cAAIE,MAAa,GAAGO,IAAI,CAACC,KAAL,CAAWN,MAAM,GAAG,EAApB,CAApB;AACAA,UAAAA,MAAM,IAAIF,MAAM,GAAG,EAAnB;AAEA,cAAIf,GAAW,GAAGK,MAAM,GAAG,EAA3B;;AACA,cAAIQ,IAAI,GAAG,CAAX,EAAc;AACVb,YAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,IAAZ,EAAkB,KAAKtB,QAAL,CAAcc,IAAI,GAAG,EAArB,EAAyB,CAAzB,CAAlB,CAAN;AACH,WAFD,MAEO;AACHb,YAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,KAAZ,EAAmB,EAAnB,CAAN;AACH;;AACDrB,UAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,IAAZ,EAAkB,KAAKtB,QAAL,CAAcgB,MAAM,GAAG,EAAvB,EAA2B,CAA3B,CAAlB,CAAN;AACAf,UAAAA,GAAG,GAAGA,GAAG,CAACqB,OAAJ,CAAY,IAAZ,EAAkB,KAAKtB,QAAL,CAAckB,MAAM,GAAG,EAAvB,EAA2B,CAA3B,CAAlB,CAAN;AACA,iBAAOjB,GAAP;AACH;;AAtFyB,O;;sBAATjB,Q,iBACsB,C;;sBADtBA,Q,oBAEyB,C", "sourcesContent": ["\nexport default class DateUtil {\n    protected static _serverTime: number = 0;\n    protected static _getServerTime: number = 0;\n\n    public static setServerTime(time: number): void {\n        this._serverTime = time;\n        this._getServerTime = Date.now();\n    }\n\n    public static getServerTime(): number {\n        let nowTime: number = Date.now();\n        return nowTime - this._getServerTime + this._serverTime;\n    }\n\n    //是否在该时间之后\n    public static isAfterServerTime(stms:number):boolean{\n        var st = this.getServerTime();\n        return st - stms > 0;\n    }\n\n    public static leftTime(stms:number):number{\n        var st = this.getServerTime();\n        return stms - st;\n    }\n\n    public static leftTimeStr(stms:number):string{\n        var diff = this.leftTime(stms);\n        return this.converSecondStr(diff);\n    }\n\n    /**补零*/\n    public static fillZero(str: string, num: number = 2): string {\n        while (str.length < num) {\n            str = \"0\" + str;\n        }\n        return str;\n    }\n\n    /**时间字符串格式转换\n     * 年 YYYY\n     * 月 MM\n     * 日 DD\n     * 时 hh\n     * 分 mm\n     * 秒 ss\n     * 毫秒 zzz*/\n    public static converTimeStr(ms: number, format: string = \"hh:mm:ss\"): string {\n        let date: Date = new Date(ms);\n        let year: string = this.fillZero(date.getFullYear() + \"\", 4);\n        let month: string = this.fillZero((date.getMonth() + 1) + \"\", 2);\n        let dat: string = this.fillZero(date.getDate() + \"\", 2);\n        let hour: string = this.fillZero(date.getHours() + \"\", 2);\n        let minute: string = this.fillZero(date.getMinutes() + \"\", 2);\n        let second: string = this.fillZero(date.getSeconds() + \"\", 2);\n        let minSecond: string = this.fillZero(date.getMilliseconds() + \"\", 3);\n\n        let str: string = format + \"\";\n        str = format.replace(/YYYY/, year);\n        str = str.replace(/MM/, month);\n        str = str.replace(/DD/, dat);\n        str = str.replace(/hh/, hour);\n        str = str.replace(/mm/, minute);\n        str = str.replace(/ss/, second);\n        str = str.replace(/zzz/, minSecond);\n        return str;\n    }\n\n    /**简易时间字符串格式转换*/\n    public static converSecondStr(ms: number, format: string = \"hh:mm:ss\"): string {\n        let second: number = Math.floor(ms / 1000);\n        let hour: number = Math.floor(second / 3600);\n        // console.log(\"hour:\", hour);\n\n        second -= hour * 3600;\n        let minute:number = Math.floor(second / 60);\n        second -= minute * 60;\n        \n        let str: string = format + \"\";\n        if (hour > 0) {\n            str = str.replace(/hh/, this.fillZero(hour + \"\", 2));\n        } else {\n            str = str.replace(/hh:/, \"\");\n        }\n        str = str.replace(/mm/, this.fillZero(minute + \"\", 2));\n        str = str.replace(/ss/, this.fillZero(second + \"\", 2));\n        return str;\n    }\n}\n"]}