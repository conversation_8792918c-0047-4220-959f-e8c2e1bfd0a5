{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "Node", "instantiate", "AudioManager", "WarReportDesItemLogic", "ccclass", "property", "WarReportDesLogic", "onLoad", "item", "active", "scrollView", "node", "on", "scrollToBottom", "onEnable", "scrollToTop", "setData", "data", "content", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_curData", "_curNum", "make", "max", "Math", "min", "rounds", "length", "index", "r", "parent", "getComponent", "onClickClose", "instance", "playClick", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AACzCC,MAAAA,Y,iBAAAA,Y;;AAIFC,MAAAA,qB;;;;;;;OAFD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAKTS,iB,WADpBF,OAAO,CAAC,mBAAD,C,UAMHC,QAAQ,CAACN,UAAD,C,UAGRM,QAAQ,CAACL,IAAD,C,oCATb,MACqBM,iBADrB,SAC+CR,SAD/C,CACyD;AAAA;AAAA;;AAAA,4CAGxB,IAHwB;;AAAA;;AAAA;;AAAA,0CAWrC,CAXqC;;AAAA,2CAapC,CAboC;AAAA;;AAerDS,QAAAA,MAAM,GAAE;AACJ,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA,eAAKC,UAAL,CAAgBC,IAAhB,CAAqBC,EAArB,CAAwB,kBAAxB,EAA4C,KAAKC,cAAjD,EAAiE,IAAjE;AACH;;AAGDC,QAAAA,QAAQ,GAAE;AACN,eAAKJ,UAAL,CAAgBK,WAAhB;AAEH;;AAEMC,QAAAA,OAAO,CAACC,IAAD,EAAe;AAEzB,eAAKP,UAAL,CAAgBQ,OAAhB,CAAwBC,iBAAxB;AACA,eAAKC,QAAL,GAAgBH,IAAhB;AACA,eAAKI,OAAL,GAAgB,CAAhB;AACA,eAAKC,IAAL;AAEA,eAAKZ,UAAL,CAAgBK,WAAhB;AACH;;AAEOO,QAAAA,IAAI,GAAG;AACX,cAAIC,GAAG,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAKL,QAAL,CAAcM,MAAd,CAAqBC,MAArB,GAA4B,KAAKN,OAA7C,CAAV;;AAEA,eAAK,IAAIO,KAAK,GAAG,KAAKP,OAAtB,EAA+BO,KAAK,GAAG,KAAKP,OAAL,GAAeE,GAAtD,EAA2DK,KAAK,EAAhE,EAAoE;AAChE,gBAAIC,CAAC,GAAG,KAAKT,QAAL,CAAcM,MAAd,CAAqBE,KAArB,CAAR;AAEA,gBAAIpB,IAAI,GAAGP,WAAW,CAAC,KAAKO,IAAN,CAAtB;AACAA,YAAAA,IAAI,CAACC,MAAL,GAAc,IAAd;AACAD,YAAAA,IAAI,CAACsB,MAAL,GAAc,KAAKpB,UAAL,CAAgBQ,OAA9B;AAEAV,YAAAA,IAAI,CAACuB,YAAL;AAAA;AAAA,gEAAyCf,OAAzC,CAAiDa,CAAjD,EAAoD,KAAKT,QAAzD,EAAmEQ,KAAK,IAAI,KAAKR,QAAL,CAAcM,MAAd,CAAqBC,MAArB,GAA4B,CAAxG;AACH;;AAED,eAAKN,OAAL,IAAgBE,GAAhB;AACH;;AAGSS,QAAAA,YAAY,GAAS;AAC3B,eAAKrB,IAAL,CAAUF,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAawB,QAAb,CAAsBC,SAAtB;AACH;;AAGSrB,QAAAA,cAAc,GAAS;AAC7BsB,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AACA,eAAKd,IAAL;AACH;;AA9DoD,O;;;;;iBAM7B,I;;;;;;;iBAGZ,I", "sourcesContent": ["import { _decorator, Component, ScrollView, Node, instantiate } from 'cc';\nimport { AudioManager } from '../../common/AudioManager';\n\nconst { ccclass, property } = _decorator;\nimport { WarReport } from \"./MapUIProxy\";\nimport WarReportDesItemLogic from './WarReportDesItemLogic';\n\n@ccclass('WarReportDesLogic')\nexport default class WarReportDesLogic extends Component {\n\n\n    private _curData:WarReport = null;\n\n    @property(ScrollView)\n    scrollView:ScrollView = null;\n\n    @property(Node)\n    item:Node = null;\n\n    _lastY:number = 0;\n\n    _curNum:number = 0;\n\n    onLoad(){\n        this.item.active = false;\n        this.scrollView.node.on(\"scroll-to-bottom\", this.scrollToBottom, this);\n    }\n\n\n    onEnable(){\n        this.scrollView.scrollToTop();\n        \n    }\n\n    public setData(data:any):void{\n        \n        this.scrollView.content.removeAllChildren();\n        this._curData = data;\n        this._curNum =  0;\n        this.make();\n\n        this.scrollView.scrollToTop();\n    }\n\n    private make() {\n        let max = Math.min(6, this._curData.rounds.length-this._curNum);\n        \n        for (let index = this._curNum; index < this._curNum + max; index++) {\n            let r = this._curData.rounds[index];\n            \n            let item = instantiate(this.item);\n            item.active = true;\n            item.parent = this.scrollView.content;\n\n            item.getComponent(WarReportDesItemLogic).setData(r, this._curData, index == this._curData.rounds.length-1);\n        }\n\n        this._curNum += max;\n    }\n\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n\n    protected scrollToBottom(): void {\n        console.log(\"scrollToBottom\");\n        this.make();\n    }\n    \n\n}\n"]}