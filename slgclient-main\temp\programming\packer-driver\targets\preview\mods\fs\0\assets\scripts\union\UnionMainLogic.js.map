{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts"], "names": ["_decorator", "Component", "Label", "Node", "EditBox", "<PERSON><PERSON>", "UnionCommand", "MapCommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "UnionMainLogic", "onLoad", "on", "unionNotice", "onUnionNotice", "unionInfo", "onInfo", "updateUnionApply", "onUnionApply", "onDestroy", "targetOff", "onEnable", "city", "getInstance", "cityProxy", "getMyMainCity", "unionId", "updateRedDot", "cnt", "proxy", "getApplyCnt", "applyRedDot", "active", "unionData", "getUnion", "nameLab", "string", "name", "console", "log", "notice", "noticeLab", "meng<PERSON>hu<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "editNode", "ok", "<PERSON><PERSON><PERSON><PERSON>", "rid", "modifyBtn", "node", "applyBtn", "data", "text", "onEditSubmit", "instance", "playClick", "str", "editBox", "modNotice", "onModify", "onCancel"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;;AAG/CC,MAAAA,Y;;AAGAC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OARH;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBZ,U;;yBAWPa,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACV,KAAD,C,UAERU,QAAQ,CAACV,KAAD,C,UAERU,QAAQ,CAACV,KAAD,C,UAERU,QAAQ,CAACT,IAAD,C,UAERS,QAAQ,CAACR,OAAD,C,UAERQ,QAAQ,CAACP,MAAD,C,UAERO,QAAQ,CAACP,MAAD,C,UAERO,QAAQ,CAACT,IAAD,C,oCAhBb,MACqBU,cADrB,SAC4CZ,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAkBlDa,QAAAA,MAAM,GAAI;AACN;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,WAAvB,EAAmC,KAAKC,aAAxC,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,SAAvB,EAAiC,KAAKC,MAAtC,EAA8C,IAA9C;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,gBAAvB,EAAyC,KAAKC,YAA9C,EAA4D,IAA5D;AACH;;AAESC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACDC,QAAAA,QAAQ,GAAG;AACP,cAAIC,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA;AAAA;AAAA,4CAAaF,WAAb,GAA2BR,SAA3B,CAAqCO,IAAI,CAACI,OAA1C;AACA,eAAKC,YAAL;AACH;;AACDA,QAAAA,YAAY,GAAE;AACV,cAAIL,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIG,GAAG,GAAG;AAAA;AAAA,4CAAaL,WAAb,GAA2BM,KAA3B,CAAiCC,WAAjC,CAA6CR,IAAI,CAACI,OAAlD,CAAV;AACA,eAAKK,WAAL,CAAiBC,MAAjB,GAA0BJ,GAAG,GAAG,CAAhC;AACH;;AACDZ,QAAAA,MAAM,GAAE;AAEJ,cAAIM,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIQ,SAAe,GAAG;AAAA;AAAA,4CAAaV,WAAb,GAA2BM,KAA3B,CAAiCK,QAAjC,CAA0CZ,IAAI,CAACI,OAA/C,CAAtB;AACA,eAAKS,OAAL,CAAaC,MAAb,GAAsB,QAAQH,SAAS,CAACI,IAAxC;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BN,SAA1B;;AACA,cAAIA,SAAS,CAACO,MAAV,IAAoB,EAAxB,EAA2B;AACvB,iBAAKC,SAAL,CAAeL,MAAf,GAAwB,MAAxB;AACH,WAFD,MAEK;AACD,iBAAKK,SAAL,CAAeL,MAAf,GAAwBH,SAAS,CAACO,MAAlC;AACH;;AACD,eAAKE,UAAL,CAAgBN,MAAhB,GAAyB,QAAQH,SAAS,CAACU,WAAV,GAAwBN,IAAzD;AACA,eAAKO,QAAL,CAAcZ,MAAd,GAAuB,KAAvB;AAEA,cAAIa,EAAE,GAAGZ,SAAS,CAACa,OAAV,CAAkBxB,IAAI,CAACyB,GAAvB,CAAT;AACA,eAAKC,SAAL,CAAeC,IAAf,CAAoBjB,MAApB,GAA6Ba,EAA7B;AACA,eAAKK,QAAL,CAAcD,IAAd,CAAmBjB,MAAnB,GAA4Ba,EAA5B;AACH;;AACD/B,QAAAA,aAAa,CAACqC,IAAD,EAAM;AACf,cAAIA,IAAI,CAACC,IAAL,IAAa,EAAjB,EAAoB;AAChB,iBAAKX,SAAL,CAAeL,MAAf,GAAwB,MAAxB;AACH,WAFD,MAEK;AACD,iBAAKK,SAAL,CAAeL,MAAf,GAAwBe,IAAI,CAACC,IAA7B;AACH;AACJ;;AAEDlC,QAAAA,YAAY,GAAE;AACV,eAAKS,YAAL;AACH;;AAED0B,QAAAA,YAAY,GAAE;AACV;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,eAAKd,SAAL,CAAeQ,IAAf,CAAoBjB,MAApB,GAA6B,IAA7B;AACA,eAAKY,QAAL,CAAcZ,MAAd,GAAuB,KAAvB;AACA,eAAKgB,SAAL,CAAeC,IAAf,CAAoBjB,MAApB,GAA6B,IAA7B;AAEA,cAAIwB,GAAG,GAAG,KAAKC,OAAL,CAAarB,MAAvB;AACA;AAAA;AAAA,4CAAab,WAAb,GAA2BmC,SAA3B,CAAqCF,GAArC;AACH;;AAEDG,QAAAA,QAAQ,GAAE;AACN;AAAA;AAAA,4CAAaL,QAAb,CAAsBC,SAAtB;AACA,eAAKd,SAAL,CAAeQ,IAAf,CAAoBjB,MAApB,GAA6B,KAA7B;AACA,eAAKY,QAAL,CAAcZ,MAAd,GAAuB,IAAvB;AACA,eAAKgB,SAAL,CAAeC,IAAf,CAAoBjB,MAApB,GAA6B,KAA7B;AACH;;AAED4B,QAAAA,QAAQ,GAAE;AACN;AAAA;AAAA,4CAAaN,QAAb,CAAsBC,SAAtB;AACA,eAAKd,SAAL,CAAeQ,IAAf,CAAoBjB,MAApB,GAA6B,IAA7B;AACA,eAAKY,QAAL,CAAcZ,MAAd,GAAuB,KAAvB;AACA,eAAKgB,SAAL,CAAeC,IAAf,CAAoBjB,MAApB,GAA6B,IAA7B;AACH;;AAzFiD,O;;;;;iBAE1B,I;;;;;;;iBAEG,I;;;;;;;iBAED,I;;;;;;;iBAEF,I;;;;;;;iBAEE,I;;;;;;;iBAEC,I;;;;;;;iBAED,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Component, Label, Node, EditBox, Button } from 'cc';\nconst {ccclass, property} = _decorator;\n\nimport UnionCommand from \"./UnionCommand\";\nimport { Union } from \"./UnionProxy\";\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('UnionMainLogic')\nexport default class UnionMainLogic extends Component {\n    @property(Label)\n    nameLab: Label | null = null;\n    @property(Label)\n    mengZhuLab: Label | null = null;\n    @property(Label)\n    noticeLab: Label | null = null;\n    @property(Node)\n    editNode: Node | null = null;\n    @property(EditBox)\n    editBox: EditBox | null = null;\n    @property(Button)\n    modifyBtn: Button | null = null;\n    @property(Button)\n    applyBtn: Button | null = null;\n    @property(Node)\n    applyRedDot: Node | null = null;\n    \n    onLoad () {\n        EventMgr.on(LogicEvent.unionNotice,this.onUnionNotice,this);\n        EventMgr.on(LogicEvent.unionInfo,this.onInfo, this);\n        EventMgr.on(LogicEvent.updateUnionApply, this.onUnionApply, this);\n    }\n    \n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n    onEnable() {\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        UnionCommand.getInstance().unionInfo(city.unionId);\n        this.updateRedDot()\n    }\n    updateRedDot(){\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let cnt = UnionCommand.getInstance().proxy.getApplyCnt(city.unionId);\n        this.applyRedDot.active = cnt > 0;\n    }\n    onInfo(){\n        \n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let unionData:Union = UnionCommand.getInstance().proxy.getUnion(city.unionId);\n        this.nameLab.string = \"联盟:\" + unionData.name;\n        console.log(\"unionData:\", unionData);\n        if (unionData.notice == \"\"){\n            this.noticeLab.string = \"暂无公告\";\n        }else{\n            this.noticeLab.string = unionData.notice;\n        }\n        this.mengZhuLab.string = \"盟主:\" + unionData.getChairman().name\n        this.editNode.active = false;\n\n        let ok = unionData.isMajor(city.rid);\n        this.modifyBtn.node.active = ok;\n        this.applyBtn.node.active = ok;\n    }\n    onUnionNotice(data){\n        if (data.text == \"\"){\n            this.noticeLab.string = \"暂无公告\";\n        }else{\n            this.noticeLab.string = data.text;\n        }\n    }\n    \n    onUnionApply(){\n        this.updateRedDot();\n    }\n\n    onEditSubmit(){\n        AudioManager.instance.playClick();\n        this.noticeLab.node.active = true;\n        this.editNode.active = false;\n        this.modifyBtn.node.active = true;\n\n        var str = this.editBox.string\n        UnionCommand.getInstance().modNotice(str);\n    }\n\n    onModify(){\n        AudioManager.instance.playClick();\n        this.noticeLab.node.active = false;\n        this.editNode.active = true;\n        this.modifyBtn.node.active = false;\n    }\n    \n    onCancel(){\n        AudioManager.instance.playClick();\n        this.noticeLab.node.active = true;\n        this.editNode.active = false;\n        this.modifyBtn.node.active = true;\n    }\n}\n"]}