{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts"], "names": ["_decorator", "Component", "Node", "Prefab", "NodePool", "instantiate", "ArmyCommand", "ArmyCmd", "ArmyLogic", "MapCommand", "MapUtil", "EventMgr", "LogicEvent", "ccclass", "property", "MapArmyLogic", "Map", "onLoad", "on", "updateArmyList", "onUpdateArmyList", "updateArmy", "onUpdateArmy", "initArmys", "schedule", "checkVisible", "onDestroy", "targetOff", "_armyPool", "clear", "_arrowPool", "_armyLogics", "for<PERSON>ach", "logic", "destroy", "update", "datas", "getInstance", "proxy", "getAllArmys", "length", "i", "cmd", "data", "console", "log", "aniNode", "arrowNode", "Idle", "Conscript", "removeArmyById", "id", "get", "createArmy", "parent", "parent<PERSON>ayer", "createArrow", "set", "setArmyData", "size", "armyPrefab", "arrowPrefab", "has", "put", "delete", "city", "cityProxy", "getMyCityById", "cityId", "rid", "buildProxy", "myId", "visible1", "armyIsInView", "x", "y", "visible2", "toX", "toY", "visible3", "fromX", "fromY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;;AAGjDC,MAAAA,W;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,S;;AACAC,MAAAA,U;;AACAC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OARH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;yBAWTe,Y,WADpBF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACX,MAAD,C,UAERW,QAAQ,CAACX,MAAD,C,oCANb,MACqBY,YADrB,SAC0Cd,SAD1C,CACoD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,+CAQA,IAAIe,GAAJ,EARA;;AAAA,6CAShB,IAAIZ,QAAJ,EATgB;;AAAA,8CAUf,IAAIA,QAAJ,EAVe;AAAA;;AAYtCa,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,cAAvB,EAAuC,KAAKC,gBAA5C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,UAAvB,EAAmC,KAAKC,YAAxC,EAAsD,IAAtD;AACA,eAAKC,SAAL;AAEA,eAAKC,QAAL,CAAc,KAAKC,YAAnB,EAAiC,GAAjC;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;;AACA,eAAKC,SAAL,CAAeC,KAAf;;AACA,eAAKC,UAAL,CAAgBD,KAAhB;;AACA,eAAKE,WAAL,CAAiBC,OAAjB,CAA0BC,KAAD,IAAqB;AAC1CA,YAAAA,KAAK,CAACC,OAAN;AACH,WAFD;AAGH;;AAESC,QAAAA,MAAM,GAAQ;AACpB,eAAKJ,WAAL,CAAiBC,OAAjB,CAA0BC,KAAD,IAAqB;AAC1CA,YAAAA,KAAK,CAACE,MAAN;AACH,WAFD;AAGH;;AAESZ,QAAAA,SAAS,GAAS;AACxB,cAAIa,KAAiB,GAAG;AAAA;AAAA,0CAAYC,WAAZ,GAA0BC,KAA1B,CAAgCC,WAAhC,EAAxB;;AACA,cAAIH,KAAK,IAAIA,KAAK,CAACI,MAAN,GAAe,CAA5B,EAA+B;AAC3B,iBAAKpB,gBAAL,CAAsBgB,KAAtB;AACH;AACJ;;AAEShB,QAAAA,gBAAgB,CAACgB,KAAD,EAA0B;AAChD,eAAK,IAAIK,CAAQ,GAAG,CAApB,EAAuBA,CAAC,GAAGL,KAAK,CAACI,MAAjC,EAAyCC,CAAC,EAA1C,EAA8C;AAC1C,gBAAIL,KAAK,CAACK,CAAD,CAAL,IAAYL,KAAK,CAACK,CAAD,CAAL,CAASC,GAAT,GAAe,CAA/B,EAAkC;AAC9B,mBAAKpB,YAAL,CAAkBc,KAAK,CAACK,CAAD,CAAvB;AACH;AACJ;AACJ;;AAESnB,QAAAA,YAAY,CAACqB,IAAD,EAAuB;AACzCC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BF,IAA5B;AACA,cAAIG,OAAa,GAAG,IAApB;AACA,cAAIC,SAAe,GAAG,IAAtB;;AACA,cAAIJ,IAAI,CAACD,GAAL,IAAY;AAAA;AAAA,kCAAQM,IAApB,IAA4BL,IAAI,CAACD,GAAL,IAAY;AAAA;AAAA,kCAAQO,SAApD,EAA+D;AAC3D;AACA,iBAAKC,cAAL,CAAoBP,IAAI,CAACQ,EAAzB;AACA;AACH;;AACD,cAAIlB,KAAe,GAAG,KAAKF,WAAL,CAAiBqB,GAAjB,CAAqBT,IAAI,CAACQ,EAA1B,CAAtB;;AACAP,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCZ,KAAlC;;AAEA,cAAIA,KAAK,IAAI,IAAb,EAAmB;AACfA,YAAAA,KAAK,GAAG;AAAA;AAAA,yCAAR;AACAa,YAAAA,OAAO,GAAG,KAAKO,UAAL,EAAV;AACAP,YAAAA,OAAO,CAACQ,MAAR,GAAiB,KAAKC,WAAtB;AACAR,YAAAA,SAAS,GAAG,KAAKS,WAAL,EAAZ;AACAT,YAAAA,SAAS,CAACO,MAAV,GAAmB,KAAKC,WAAxB;;AACA,iBAAKxB,WAAL,CAAiB0B,GAAjB,CAAqBd,IAAI,CAACQ,EAA1B,EAA8BlB,KAA9B;;AAEAW,YAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCZ,KAAlC;AACH,WATD,MASO;AACHa,YAAAA,OAAO,GAAGb,KAAK,CAACa,OAAhB;AACAC,YAAAA,SAAS,GAAGd,KAAK,CAACc,SAAlB;AACAd,YAAAA,KAAK,GAAG,KAAKF,WAAL,CAAiBqB,GAAjB,CAAqBT,IAAI,CAACQ,EAA1B,CAAR;AACH;;AACDP,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCZ,KAAlC;AACAA,UAAAA,KAAK,CAACyB,WAAN,CAAkBf,IAAlB,EAAwBG,OAAxB,EAAiCC,SAAjC;AACH;;AAESM,QAAAA,UAAU,GAAS;AACzB,cAAI,KAAKzB,SAAL,CAAe+B,IAAf,KAAwB,CAA5B,EAA+B;AAC3B,mBAAO,KAAK/B,SAAL,CAAewB,GAAf,EAAP;AACH,WAFD,MAEO;AACH,mBAAO/C,WAAW,CAAC,KAAKuD,UAAN,CAAlB;AACH;AACJ;;AAESJ,QAAAA,WAAW,GAAQ;AACzB,cAAI,KAAK1B,UAAL,CAAgB6B,IAAhB,KAAyB,CAA7B,EAAgC;AAC5B,mBAAO,KAAK7B,UAAL,CAAgBsB,GAAhB,EAAP;AACH,WAFD,MAEO;AACH,mBAAO/C,WAAW,CAAC,KAAKwD,WAAN,CAAlB;AACH;AACJ;;AAESX,QAAAA,cAAc,CAACC,EAAD,EAAmB;AACvC,cAAI,KAAKpB,WAAL,CAAiB+B,GAAjB,CAAqBX,EAArB,CAAJ,EAA8B;AAC1B,gBAAIlB,KAAe,GAAG,KAAKF,WAAL,CAAiBqB,GAAjB,CAAqBD,EAArB,CAAtB;;AACA,iBAAKvB,SAAL,CAAemC,GAAf,CAAmB9B,KAAK,CAACa,OAAzB;;AACA,iBAAKhB,UAAL,CAAgBiC,GAAhB,CAAoB9B,KAAK,CAACc,SAA1B;;AACAd,YAAAA,KAAK,CAACJ,KAAN;;AACA,iBAAKE,WAAL,CAAiBiC,MAAjB,CAAwBb,EAAxB;;AACAP,YAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BM,EAA9B;AACH;AACJ;;AAGS1B,QAAAA,YAAY,GAAS;AAE3B,eAAKM,WAAL,CAAiBC,OAAjB,CAA0BC,KAAD,IAAqB;AAC1C,gBAAIgC,IAAI,GAAG;AAAA;AAAA,0CAAW5B,WAAX,GAAyB6B,SAAzB,CAAmCC,aAAnC,CAAiDlC,KAAK,CAACU,IAAN,CAAWyB,MAA5D,CAAX;;AACA,gBAAG,CAACH,IAAD,IAASA,IAAI,CAACI,GAAL,IAAY;AAAA;AAAA,0CAAWhC,WAAX,GAAyBiC,UAAzB,CAAoCC,IAA5D,EAAiE;AAE7D,kBAAIC,QAAQ,GAAG;AAAA;AAAA,sCAAQC,YAAR,CAAqBxC,KAAK,CAACU,IAAN,CAAW+B,CAAhC,EAAmCzC,KAAK,CAACU,IAAN,CAAWgC,CAA9C,CAAf;AACA,kBAAIC,QAAQ,GAAG;AAAA;AAAA,sCAAQH,YAAR,CAAqBxC,KAAK,CAACU,IAAN,CAAWkC,GAAhC,EAAqC5C,KAAK,CAACU,IAAN,CAAWmC,GAAhD,CAAf;AACA,kBAAIC,QAAQ,GAAG;AAAA;AAAA,sCAAQN,YAAR,CAAqBxC,KAAK,CAACU,IAAN,CAAWqC,KAAhC,EAAuC/C,KAAK,CAACU,IAAN,CAAWsC,KAAlD,CAAf;;AACA,kBAAG,CAACT,QAAD,IAAa,CAACI,QAAd,IAA0B,CAACG,QAA9B,EAAuC;AACnC,qBAAK7B,cAAL,CAAoBjB,KAAK,CAACU,IAAN,CAAWQ,EAA/B;AACH;AACJ;AAEJ,WAZD;AAaH;;AA3H+C,O;;;;;iBAE5B,I;;;;;;;iBAEC,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Component, Node, Prefab, NodePool, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport ArmyCommand from \"../general/ArmyCommand\";\nimport { ArmyCmd, ArmyData } from \"../general/ArmyProxy\";\nimport ArmyLogic from \"./entries/ArmyLogic\";\nimport MapCommand from \"./MapCommand\";\nimport MapUtil from \"./MapUtil\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('MapArmyLogic')\nexport default class MapArmyLogic extends Component {\n    @property(Node)\n    parentLayer: Node = null;\n    @property(Prefab)\n    armyPrefab: Prefab = null;\n    @property(Prefab)\n    arrowPrefab: Prefab = null;\n\n    protected _armyLogics: Map<number, ArmyLogic> = new Map<number, ArmyLogic>();\n    protected _armyPool: NodePool = new NodePool();\n    protected _arrowPool: NodePool = new NodePool();\n\n    protected onLoad(): void {\n        EventMgr.on(LogicEvent.updateArmyList, this.onUpdateArmyList, this);\n        EventMgr.on(LogicEvent.updateArmy, this.onUpdateArmy, this);\n        this.initArmys();\n\n        this.schedule(this.checkVisible, 0.5);\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._armyPool.clear();\n        this._arrowPool.clear();\n        this._armyLogics.forEach((logic:ArmyLogic) => {\n            logic.destroy();\n        })\n    }\n\n    protected update():void {\n        this._armyLogics.forEach((logic:ArmyLogic) => {\n            logic.update();\n        });\n    }\n\n    protected initArmys(): void {\n        let datas: ArmyData[] = ArmyCommand.getInstance().proxy.getAllArmys();\n        if (datas && datas.length > 0) {\n            this.onUpdateArmyList(datas);\n        }\n    }\n\n    protected onUpdateArmyList(datas: ArmyData[]): void {\n        for (let i:number = 0; i < datas.length; i++) {\n            if (datas[i] && datas[i].cmd > 0) {\n                this.onUpdateArmy(datas[i]);\n            }\n        }\n    }\n\n    protected onUpdateArmy(data: ArmyData): void {\n        console.log(\"onUpdateArmy\", data);\n        let aniNode: Node = null;\n        let arrowNode: Node = null;\n        if (data.cmd == ArmyCmd.Idle || data.cmd == ArmyCmd.Conscript) {\n            //代表不在地图上\n            this.removeArmyById(data.id);\n            return;\n        }\n        let logic:ArmyLogic = this._armyLogics.get(data.id);\n        console.log(\"onUpdateArmy 1111:\", logic);\n\n        if (logic == null) {\n            logic = new ArmyLogic();\n            aniNode = this.createArmy();\n            aniNode.parent = this.parentLayer;\n            arrowNode = this.createArrow();\n            arrowNode.parent = this.parentLayer;\n            this._armyLogics.set(data.id, logic);\n\n            console.log(\"onUpdateArmy 2222:\", logic);\n        } else {\n            aniNode = logic.aniNode;\n            arrowNode = logic.arrowNode;\n            logic = this._armyLogics.get(data.id);\n        }\n        console.log(\"onUpdateArmy 3333:\", logic);\n        logic.setArmyData(data, aniNode, arrowNode);\n    }\n\n    protected createArmy(): Node {\n        if (this._armyPool.size() > 0) {\n            return this._armyPool.get();\n        } else {\n            return instantiate(this.armyPrefab);\n        }\n    }\n\n    protected createArrow():Node {\n        if (this._arrowPool.size() > 0) {\n            return this._arrowPool.get();\n        } else {\n            return instantiate(this.arrowPrefab);\n        }\n    }\n\n    protected removeArmyById(id: number): void {\n        if (this._armyLogics.has(id)) {\n            let logic:ArmyLogic = this._armyLogics.get(id);\n            this._armyPool.put(logic.aniNode);\n            this._arrowPool.put(logic.arrowNode);\n            logic.clear();\n            this._armyLogics.delete(id);\n            console.log(\"removeArmyById\", id);\n        }\n    }\n\n\n    protected checkVisible(): void {\n\n        this._armyLogics.forEach((logic:ArmyLogic) => {\n            let city = MapCommand.getInstance().cityProxy.getMyCityById(logic.data.cityId);\n            if(!city || city.rid != MapCommand.getInstance().buildProxy.myId){\n            \n                var visible1 = MapUtil.armyIsInView(logic.data.x, logic.data.y);\n                var visible2 = MapUtil.armyIsInView(logic.data.toX, logic.data.toY);\n                var visible3 = MapUtil.armyIsInView(logic.data.fromX, logic.data.fromY);\n                if(!visible1 && !visible2 && !visible3){\n                    this.removeArmyById(logic.data.id);\n                }\n            }\n            \n        });\n    }\n}\n"]}