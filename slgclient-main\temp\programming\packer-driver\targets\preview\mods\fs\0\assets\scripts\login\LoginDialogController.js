System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Node, EditBox, Button, Label, Color, Toggle, AudioManager, EventMgr, LogicEvent, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _temp, _crd, ccclass, property, LoginDialogController;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfAudioManager(extras) {
    _reporterNs.report("AudioManager", "../common/AudioManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../utils/EventMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLogicEvent(extras) {
    _reporterNs.report("LogicEvent", "../common/LogicEvent", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      EditBox = _cc.EditBox;
      Button = _cc.Button;
      Label = _cc.Label;
      Color = _cc.Color;
      Toggle = _cc.Toggle;
    }, function (_unresolved_2) {
      AudioManager = _unresolved_2.AudioManager;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      LogicEvent = _unresolved_4.LogicEvent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7f25eysF0BCUpPqgdswnYxJ", "LoginDialogController", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 登录对话框控制器
       * 处理手机号登录、验证码获取等功能
       */

      _export("LoginDialogController", LoginDialogController = (_dec = ccclass('LoginDialogController'), _dec2 = property(EditBox), _dec3 = property(EditBox), _dec4 = property(Button), _dec5 = property(Button), _dec6 = property(Node), _dec7 = property(Toggle), _dec8 = property(Label), _dec9 = property(Label), _dec(_class = (_class2 = (_temp = class LoginDialogController extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "phoneInput", _descriptor, this);

          _initializerDefineProperty(this, "codeInput", _descriptor2, this);

          _initializerDefineProperty(this, "getCodeButton", _descriptor3, this);

          _initializerDefineProperty(this, "loginButton", _descriptor4, this);

          _initializerDefineProperty(this, "maskNode", _descriptor5, this);

          _initializerDefineProperty(this, "agreementToggle", _descriptor6, this);

          _initializerDefineProperty(this, "phoneStatusLabel", _descriptor7, this);

          _initializerDefineProperty(this, "codeStatusLabel", _descriptor8, this);

          _defineProperty(this, "codeCountdown", 0);

          _defineProperty(this, "countdownTimer", 0);
        }

        onLoad() {
          console.log('[LoginDialogController] 登录对话框控制器加载');
          this.initializeDialog();
          this.setupEventListeners();
        }

        start() {
          console.log('[LoginDialogController] 登录对话框控制器启动');
        }
        /**
         * 初始化对话框
         */


        initializeDialog() {
          // 隐藏提示标签
          if (this.phoneStatusLabel) {
            this.phoneStatusLabel.node.active = false;
          }

          if (this.codeStatusLabel) {
            this.codeStatusLabel.node.active = false;
          } // 设置输入框限制


          if (this.phoneInput) {
            this.phoneInput.maxLength = 11;
          }

          if (this.codeInput) {
            this.codeInput.maxLength = 6;
          }
        }
        /**
         * 设置事件监听器
         */


        setupEventListeners() {
          if (this.getCodeButton) {
            this.getCodeButton.node.on(Button.EventType.CLICK, this.onGetCodeButtonClick, this);
          }

          if (this.loginButton) {
            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
          } // 设置遮罩点击事件（点击对话框外区域关闭）


          if (this.maskNode) {
            this.maskNode.on(Node.EventType.TOUCH_END, this.onMaskClick, this);
          }

          if (this.phoneInput) {
            this.phoneInput.node.on('text-changed', this.onPhoneInputChanged, this);
          }

          if (this.codeInput) {
            this.codeInput.node.on('text-changed', this.onCodeInputChanged, this);
          }
        }
        /**
         * 手机号输入变化事件
         */


        onPhoneInputChanged() {
          var phone = this.phoneInput.string; // 只允许输入数字

          var numericPhone = phone.replace(/[^0-9]/g, '');

          if (numericPhone !== phone) {
            this.phoneInput.string = numericPhone;
          } // 隐藏手机号状态提示


          if (this.phoneStatusLabel) {
            this.phoneStatusLabel.node.active = false;
          }
        }
        /**
         * 验证码输入变化事件
         */


        onCodeInputChanged() {
          var code = this.codeInput.string; // 只允许输入数字

          var numericCode = code.replace(/[^0-9]/g, '');

          if (numericCode !== code) {
            this.codeInput.string = numericCode;
          } // 隐藏验证码状态提示


          if (this.codeStatusLabel) {
            this.codeStatusLabel.node.active = false;
          }
        }
        /**
         * 获取验证码按钮点击事件
         */


        onGetCodeButtonClick() {
          console.log('[LoginDialogController] 获取验证码按钮被点击');
          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick();
          var phone = this.phoneInput.string.trim(); // 验证手机号

          if (!this.validatePhone(phone)) {
            return;
          } // 开始倒计时


          this.startCodeCountdown(); // 这里应该调用获取验证码的API

          console.log("[LoginDialogController] \u5411\u624B\u673A\u53F7 " + phone + " \u53D1\u9001\u9A8C\u8BC1\u7801");
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
            error: Error()
          }), LogicEvent) : LogicEvent).showToast, "\u9A8C\u8BC1\u7801\u5DF2\u53D1\u9001\u5230 " + phone);
        }
        /**
         * 登录按钮点击事件
         */


        onLoginButtonClick() {
          console.log('[LoginDialogController] 登录按钮被点击');
          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick();
          var phone = this.phoneInput.string.trim();
          var code = this.codeInput.string.trim(); // 验证输入

          if (!this.validatePhone(phone) || !this.validateCode(code) || !this.validateAgreement()) {
            return;
          } // 执行登录


          this.performLogin(phone, code);
        }
        /**
         * 遮罩点击事件（点击对话框外区域关闭）
         */


        onMaskClick() {
          console.log('[LoginDialogController] 点击对话框外区域，关闭对话框');
          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick();
          this.node.destroy();
        }
        /**
         * 验证手机号
         */


        validatePhone(phone) {
          if (!phone) {
            this.showPhoneStatus('请输入手机号', new Color(255, 100, 100, 255));
            return false;
          }

          if (phone.length !== 11) {
            this.showPhoneStatus('手机号必须是11位数字', new Color(255, 100, 100, 255));
            return false;
          }

          if (!/^1[3-9]\d{9}$/.test(phone)) {
            this.showPhoneStatus('请输入正确的手机号格式', new Color(255, 100, 100, 255));
            return false;
          }

          return true;
        }
        /**
         * 验证验证码
         */


        validateCode(code) {
          if (!code) {
            this.showCodeStatus('请输入验证码', new Color(255, 100, 100, 255));
            return false;
          }

          if (code.length !== 6) {
            this.showCodeStatus('验证码必须是6位数字', new Color(255, 100, 100, 255));
            return false;
          }

          return true;
        }
        /**
         * 验证协议勾选
         */


        validateAgreement() {
          if (!this.agreementToggle || !this.agreementToggle.isChecked) {
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
              error: Error()
            }), LogicEvent) : LogicEvent).showToast, '请先同意用户协议');
            return false;
          }

          return true;
        }
        /**
         * 执行登录
         */


        performLogin(phone, code) {
          console.log("[LoginDialogController] \u5F00\u59CB\u767B\u5F55: \u624B\u673A\u53F7=" + phone + ", \u9A8C\u8BC1\u7801=" + code); // 这里应该调用登录API
          // 模拟登录成功

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
            error: Error()
          }), LogicEvent) : LogicEvent).showToast, '登录成功！'); // 关闭对话框

          this.scheduleOnce(() => {
            this.node.destroy();
          }, 1);
        }
        /**
         * 显示手机号状态
         */


        showPhoneStatus(message, color) {
          if (this.phoneStatusLabel) {
            this.phoneStatusLabel.string = message;
            this.phoneStatusLabel.color = color;
            this.phoneStatusLabel.node.active = true;
          }
        }
        /**
         * 显示验证码状态
         */


        showCodeStatus(message, color) {
          if (this.codeStatusLabel) {
            this.codeStatusLabel.string = message;
            this.codeStatusLabel.color = color;
            this.codeStatusLabel.node.active = true;
          }
        }
        /**
         * 开始验证码倒计时
         */


        startCodeCountdown() {
          this.codeCountdown = 60;
          this.updateCodeButtonText();
          this.countdownTimer = setInterval(() => {
            this.codeCountdown--;
            this.updateCodeButtonText();

            if (this.codeCountdown <= 0) {
              this.stopCodeCountdown();
            }
          }, 1000);
        }
        /**
         * 停止验证码倒计时
         */


        stopCodeCountdown() {
          if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = 0;
          }

          this.codeCountdown = 0;
          this.updateCodeButtonText();
        }
        /**
         * 更新获取验证码按钮文本
         */


        updateCodeButtonText() {
          if (!this.getCodeButton) return;
          var label = this.getCodeButton.node.getComponentInChildren(Label);

          if (label) {
            if (this.codeCountdown > 0) {
              label.string = this.codeCountdown + "\u79D2\u540E\u91CD\u8BD5";
              this.getCodeButton.interactable = false;
            } else {
              label.string = '获取验证码';
              this.getCodeButton.interactable = true;
            }
          }
        }

        onDestroy() {
          // 清理倒计时
          this.stopCodeCountdown(); // 清理事件监听器

          if (this.getCodeButton) {
            this.getCodeButton.node.off(Button.EventType.CLICK, this.onGetCodeButtonClick, this);
          }

          if (this.loginButton) {
            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);
          }

          if (this.maskNode) {
            this.maskNode.off(Node.EventType.TOUCH_END, this.onMaskClick, this);
          }

          if (this.phoneInput) {
            this.phoneInput.node.off('text-changed', this.onPhoneInputChanged, this);
          }

          if (this.codeInput) {
            this.codeInput.node.off('text-changed', this.onCodeInputChanged, this);
          }

          console.log('[LoginDialogController] 登录对话框控制器销毁');
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "phoneInput", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "codeInput", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "getCodeButton", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "loginButton", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "maskNode", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "agreementToggle", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "phoneStatusLabel", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "codeStatusLabel", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=LoginDialogController.js.map