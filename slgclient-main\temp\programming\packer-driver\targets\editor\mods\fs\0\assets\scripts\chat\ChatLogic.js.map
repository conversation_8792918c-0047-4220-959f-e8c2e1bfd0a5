{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts"], "names": ["_decorator", "Component", "EditBox", "ScrollView", "MapCommand", "ChatCommand", "ListLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "on", "updateChatHistory", "updateChat", "unionChange", "onDisable", "targetOff", "onEnable", "console", "log", "updateUnion", "updateView", "city", "getInstance", "cityProxy", "getMyMainCity", "unionId", "join", "exit", "data", "_type", "comp", "chatView", "node", "getComponent", "list", "proxy", "getWorldChatList", "setData", "getUnionChatList", "onClickClose", "instance", "playClick", "active", "chatHistory", "onClickChat", "editConent", "string", "emit", "showToast", "chat", "onClickWorld", "onClickUnion"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAESA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,U,OAAAA,U;;AAIlCC,MAAAA,U;;AACAC,MAAAA,W;;AAEAC,MAAAA,S;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OATH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAaTY,S,WADpBF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACT,OAAD,C,UAGRS,QAAQ,CAACR,UAAD,C,oCANb,MACqBS,SADrB,SACuCX,SADvC,CACiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,yCAQ9B,CAR8B;AAAA;;AAUnCY,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,iBAAvB,EAA0C,KAAKC,UAA/C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,WAAvB,EAAoC,KAAKD,UAAzC,EAAqD,IAArD;AACH;;AAESE,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,QAAQ,GAAO;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA,eAAKC,WAAL;AACA,eAAKC,UAAL;AACH;;AAESD,QAAAA,WAAW,GAAO;AACxB,cAAIE,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;;AACA,cAAIH,IAAI,CAACI,OAAL,GAAe,CAAnB,EAAqB;AACjB;AACA;AAAA;AAAA,4CAAYH,WAAZ,GAA0BI,IAA1B,CAA+B,CAA/B,EAAkCL,IAAI,CAACI,OAAvC;AACH,WAHD,MAGK;AACD;AAAA;AAAA,4CAAYH,WAAZ,GAA0BK,IAA1B,CAA+B,CAA/B,EAAkC,CAAlC;AACH;AACJ;;AAESf,QAAAA,UAAU,CAACgB,IAAD,EAAY;AAC5B,cAAG,KAAKC,KAAL,IAAc,CAAjB,EAAmB;AACf,gBAAIC,IAAI,GAAG,KAAKC,QAAL,CAAcC,IAAd,CAAmBC,YAAnB;AAAA;AAAA,uCAAX;AACA,gBAAIC,IAAc,GAAG;AAAA;AAAA,4CAAYZ,WAAZ,GAA0Ba,KAA1B,CAAgCC,gBAAhC,EAArB;AACAN,YAAAA,IAAI,CAACO,OAAL,CAAaH,IAAb;AACH,WAJD,MAIM,IAAI,KAAKL,KAAL,IAAc,CAAlB,EAAoB;AACtB,gBAAIC,IAAI,GAAG,KAAKC,QAAL,CAAcC,IAAd,CAAmBC,YAAnB;AAAA;AAAA,uCAAX;AACA,gBAAIC,IAAc,GAAG;AAAA;AAAA,4CAAYZ,WAAZ,GAA0Ba,KAA1B,CAAgCG,gBAAhC,EAArB;AACArB,YAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBgB,IAArB;AACAJ,YAAAA,IAAI,CAACO,OAAL,CAAaH,IAAb;AACH;AACJ;;AAESK,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,eAAKT,IAAL,CAAUU,MAAV,GAAmB,KAAnB;AACH;;AAEMtB,QAAAA,UAAU,GAAO;AACpBH,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqB,KAAKW,KAA1B;AACA;AAAA;AAAA,0CAAYP,WAAZ,GAA0BqB,WAA1B,CAAsC,KAAKd,KAA3C;AACH;;AAESe,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKI,UAAL,CAAgBC,MAAhB,IAA0B,EAA7B,EAAgC;AAC5B;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,UAApC;AACA;AACH;;AAED,cAAI,KAAKnB,KAAL,IAAc,CAAlB,EAAoB;AAChB;AAAA;AAAA,4CAAYP,WAAZ,GAA0B2B,IAA1B,CAA+B,KAAKJ,UAAL,CAAgBC,MAA/C,EAAuD,KAAKjB,KAA5D;AACH,WAFD,MAEM,IAAI,KAAKA,KAAL,IAAc,CAAlB,EAAoB;AACtB,gBAAIR,IAAgB,GAAG;AAAA;AAAA,0CAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;;AACA,gBAAIH,IAAI,CAACI,OAAL,GAAe,CAAnB,EAAqB;AACjB;AAAA;AAAA,8CAAYH,WAAZ,GAA0B2B,IAA1B,CAA+B,KAAKJ,UAAL,CAAgBC,MAA/C,EAAuD,KAAKjB,KAA5D;AACH;AACJ;;AACD,eAAKgB,UAAL,CAAgBC,MAAhB,GAAyB,EAAzB;AACH;;AAESI,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaV,QAAb,CAAsBC,SAAtB;AACA,eAAKZ,KAAL,GAAa,CAAb;AACA,eAAKT,UAAL;AACH;;AAES+B,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaX,QAAb,CAAsBC,SAAtB;AACA,eAAKZ,KAAL,GAAa,CAAb;AACA,eAAKT,UAAL;AACH;;AAtF4C,O;;;;;iBAGvB,I;;;;;;;iBAGA,I", "sourcesContent": ["\n\nimport { _decorator, Component, EditBox, ScrollView } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport ChatCommand from \"./ChatCommand\";\nimport { ChatMsg } from \"./ChatProxy\";\nimport ListLogic from '../utils/ListLogic';\nimport { EventMgr } from '../utils/EventMgr';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\n\n\n@ccclass('ChatLogic')\nexport default class ChatLogic extends Component {\n\n    @property(EditBox)\n    editConent: EditBox = null;\n\n    @property(ScrollView)\n    chatView:ScrollView = null;\n\n    _type:number = 0;\n\n    protected onLoad():void{\n        EventMgr.on(LogicEvent.updateChatHistory, this.updateChat, this);\n        EventMgr.on(LogicEvent.unionChange, this.updateChat, this);\n    }\n\n    protected onDisable():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onEnable():void{\n        console.log(\"onEnable\")\n        this.updateUnion();\n        this.updateView();\n    }\n\n    protected updateUnion():void{\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        if (city.unionId > 0){\n            //加入联盟频道\n            ChatCommand.getInstance().join(1, city.unionId);\n        }else{\n            ChatCommand.getInstance().exit(1, 0);\n        }\n    }\n\n    protected updateChat(data:any[]){\n        if(this._type == 0){\n            var comp = this.chatView.node.getComponent(ListLogic);\n            var list:ChatMsg[] = ChatCommand.getInstance().proxy.getWorldChatList();\n            comp.setData(list);\n        }else if (this._type == 1){\n            var comp = this.chatView.node.getComponent(ListLogic);\n            var list:ChatMsg[] = ChatCommand.getInstance().proxy.getUnionChatList();\n            console.log(\"list:\", list)\n            comp.setData(list);\n        }\n    }\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n    }\n\n    public updateView():void{\n        console.log(\"type:\", this._type)\n        ChatCommand.getInstance().chatHistory(this._type);\n    }\n\n    protected onClickChat(): void {\n        AudioManager.instance.playClick();\n        if(this.editConent.string == \"\"){\n            EventMgr.emit(LogicEvent.showToast, \"聊天内容不能为空\");\n            return;\n        }\n\n        if (this._type == 0){\n            ChatCommand.getInstance().chat(this.editConent.string, this._type);\n        }else if (this._type == 1){\n            let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n            if (city.unionId > 0){\n                ChatCommand.getInstance().chat(this.editConent.string, this._type);\n            }\n        }\n        this.editConent.string = \"\";\n    }\n\n    protected onClickWorld(): void {\n        AudioManager.instance.playClick();\n        this._type = 0;\n        this.updateView();\n    }\n\n    protected onClickUnion(): void {\n        AudioManager.instance.playClick();\n        this._type = 1;\n        this.updateView();\n    }\n}\n"]}