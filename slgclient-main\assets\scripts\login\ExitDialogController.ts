import { _decorator, Component, Node, Button, Label, game } from 'cc';
import { AudioManager } from '../common/AudioManager';

const { ccclass, property } = _decorator;

/**
 * 离开游戏对话框控制器
 * 处理离开游戏确认功能
 */
@ccclass('ExitDialogController')
export class ExitDialogController extends Component {
    
    @property(Label)
    messageLabel: Label = null!;
    
    @property(Button)
    confirmButton: Button = null!;
    
    @property(Button)
    cancelButton: Button = null!;

    @property(Node)
    maskNode: Node = null!;

    onLoad() {
        console.log('[ExitDialogController] 离开游戏对话框控制器加载');
        this.initializeDialog();
        this.setupEventListeners();
    }

    start() {
        console.log('[ExitDialogController] 离开游戏对话框控制器启动');
    }

    /**
     * 初始化对话框
     */
    private initializeDialog(): void {
        // 设置询问文本
        if (this.messageLabel) {
            this.messageLabel.string = '确定要离开游戏吗？\n离开后当前进度可能会丢失';
        }
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        if (this.confirmButton) {
            this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmButtonClick, this);
            console.log('[ExitDialogController] 确认按钮事件监听器已设置');
        }

        if (this.cancelButton) {
            this.cancelButton.node.on(Button.EventType.CLICK, this.onCancelButtonClick, this);
            console.log('[ExitDialogController] 取消按钮事件监听器已设置');
        }

        // 设置遮罩点击事件（点击对话框外区域关闭）
        if (this.maskNode) {
            this.maskNode.on(Node.EventType.TOUCH_END, this.onMaskClick, this);
            console.log('[ExitDialogController] 遮罩点击事件监听器已设置');
        }
    }

    /**
     * 确认离开按钮点击事件
     */
    private onConfirmButtonClick(): void {
        console.log('[ExitDialogController] 确认离开按钮被点击');
        
        // 播放点击音效
        AudioManager.instance.playClick();
        
        // 显示离开提示
        console.log('[ExitDialogController] 玩家确认离开游戏');
        
        // 延迟一下再退出，让音效播放完
        this.scheduleOnce(() => {
            this.exitGame();
        }, 0.2);
    }

    /**
     * 再玩一会按钮点击事件
     */
    private onCancelButtonClick(): void {
        console.log('[ExitDialogController] 再玩一会按钮被点击');

        // 播放点击音效
        AudioManager.instance.playClick();

        // 关闭对话框
        this.node.destroy();
    }

    /**
     * 遮罩点击事件（点击对话框外区域关闭）
     */
    private onMaskClick(): void {
        console.log('[ExitDialogController] 点击对话框外区域，关闭对话框');

        // 播放点击音效
        AudioManager.instance.playClick();

        // 关闭对话框
        this.node.destroy();
    }

    /**
     * 退出游戏
     */
    private exitGame(): void {
        console.log('[ExitDialogController] 正在退出游戏...');
        
        // 在不同平台上退出游戏
        if (typeof window !== 'undefined') {
            // Web平台
            if (window.close) {
                window.close();
            } else {
                // 如果无法关闭窗口，显示提示
                alert('请手动关闭浏览器标签页');
            }
        } else {
            // 原生平台
            game.end();
        }
    }

    onDestroy() {
        // 清理事件监听器
        if (this.confirmButton) {
            this.confirmButton.node.off(Button.EventType.CLICK, this.onConfirmButtonClick, this);
        }
        if (this.cancelButton) {
            this.cancelButton.node.off(Button.EventType.CLICK, this.onCancelButtonClick, this);
        }
        if (this.maskNode) {
            this.maskNode.off(Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        console.log('[ExitDialogController] 离开游戏对话框控制器销毁');
    }
}
