{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts"], "names": ["_decorator", "Component", "Label", "Node", "UnionCommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "UnionItemLogic", "onLoad", "joinButtonNode", "active", "updateItem", "data", "_unionData", "name<PERSON><PERSON><PERSON>", "string", "name", "isCanJoin", "getInstance", "proxy", "isMeInUnion", "join", "instance", "playClick", "unionJoin", "id", "click", "isCanjoin", "emit", "openMyUnion"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAEhCC,MAAAA,Y;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAQTU,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACP,KAAD,C,UAERO,QAAQ,CAACN,IAAD,C,oCAJb,MACqBO,cADrB,SAC4CT,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,8CAKrB,IALqB;AAAA;;AAMxCU,QAAAA,MAAM,GAAO;AACnB,eAAKC,cAAL,CAAoBC,MAApB,GAA6B,KAA7B;AACH;;AACSC,QAAAA,UAAU,CAACC,IAAD,EAAiB;AACjC,eAAKC,UAAL,GAAkBD,IAAlB;AACA,eAAKE,SAAL,CAAeC,MAAf,GAAwB,KAAKF,UAAL,CAAgBG,IAAxC;AACA,eAAKP,cAAL,CAAoBC,MAApB,GAA6B,KAAKO,SAAL,EAA7B;AAGH;;AACSA,QAAAA,SAAS,GAAU;AACzB,iBAAO,CAAC;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,WAAjC,EAAR;AACH;;AACSC,QAAAA,IAAI,GAAO;AACjB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaL,WAAb,GAA2BM,SAA3B,CAAqC,KAAKX,UAAL,CAAgBY,EAArD;AACH;;AACSC,QAAAA,KAAK,GAAO;AAClB;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;AACA,cAAII,SAAiB,GAAG,KAAKV,SAAL,EAAxB;;AACA,cAAG,CAACU,SAAJ,EAAc;AACV;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,WAAzB,EAAqC,KAAKhB,UAA1C;AACH;AACJ;;AA7BiD,O;;;;;iBAExB,I;;;;;;;iBAEI,I", "sourcesContent": ["import { _decorator, Component, Label, Node } from 'cc';\nconst { ccclass, property } = _decorator;\nimport UnionCommand from \"./UnionCommand\";\nimport { Union } from \"./UnionProxy\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('UnionItemLogic')\nexport default class UnionItemLogic extends Component {\n    @property(Label)\n    nameLabel: Label | null = null;\n    @property(Node)\n    joinButtonNode: Node | null = null;\n    protected _unionData:Union = null;\n    protected onLoad():void{\n        this.joinButtonNode.active = false;\n    }\n    protected updateItem(data:Union):void{\n        this._unionData = data;\n        this.nameLabel.string = this._unionData.name;\n        this.joinButtonNode.active = this.isCanJoin();\n\n\n    }\n    protected isCanJoin():boolean{\n        return !UnionCommand.getInstance().proxy.isMeInUnion();\n    }\n    protected join():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionJoin(this._unionData.id)\n    }\n    protected click():void{\n        AudioManager.instance.playClick();\n        var isCanjoin:boolean = this.isCanJoin();\n        if(!isCanjoin){\n            EventMgr.emit(LogicEvent.openMyUnion,this._unionData)\n        }\n    }\n}\n"]}