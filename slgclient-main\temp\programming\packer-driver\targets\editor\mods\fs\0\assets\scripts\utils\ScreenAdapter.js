System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, view, ResolutionPolicy, sys, UITransform, Widget, screen, GameConfig, _dec, _class, _class2, _temp, _crd, ccclass, property, ScreenAdapter;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../config/GameConfig", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      view = _cc.view;
      ResolutionPolicy = _cc.ResolutionPolicy;
      sys = _cc.sys;
      UITransform = _cc.UITransform;
      Widget = _cc.Widget;
      screen = _cc.screen;
    }, function (_unresolved_2) {
      GameConfig = _unresolved_2.GameConfig;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9b4ef9eqyVNl7EoAwYDY1SK", "ScreenAdapter", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 屏幕适配管理器
       * 统一处理不同设备的分辨率适配问题
       */

      _export("ScreenAdapter", ScreenAdapter = (_dec = ccclass('ScreenAdapter'), _dec(_class = (_temp = _class2 = class ScreenAdapter {
        constructor() {
          _defineProperty(this, "_isInitialized", false);
        }

        // 设计分辨率（从配置文件读取）
        static get DESIGN_WIDTH() {
          return (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.designWidth;
        }

        static get DESIGN_HEIGHT() {
          return (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.designHeight;
        } // 最小和最大宽高比


        static get instance() {
          if (!this._instance) {
            this._instance = new ScreenAdapter();
          }

          return this._instance;
        }
        /**
         * 初始化屏幕适配
         */


        init() {
          if (this._isInitialized) {
            return;
          }

          if ((_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.enableDebugInfo) {
            console.log('[ScreenAdapter] 开始初始化屏幕适配');
          } // 获取屏幕实际尺寸（使用新API）


          const windowSize = screen.windowSize;
          const screenRatio = windowSize.width / windowSize.height;
          console.log(`[ScreenAdapter] 屏幕尺寸: ${windowSize.width}x${windowSize.height}, 比例: ${screenRatio.toFixed(3)}`); // 对于竖屏游戏，使用更保守的适配策略

          let designWidth = ScreenAdapter.DESIGN_WIDTH;
          let designHeight = ScreenAdapter.DESIGN_HEIGHT;
          let policy; // 计算设计分辨率比例

          const designRatio = designWidth / designHeight; // 1080/1920 = 0.5625

          console.log(`[ScreenAdapter] 设计比例: ${designRatio.toFixed(3)}, 屏幕比例: ${screenRatio.toFixed(3)}`); // 对于竖屏游戏，优先使用SHOW_ALL策略确保UI不变形

          if (screenRatio > designRatio * 1.5) {
            // 屏幕太宽（如桌面环境），使用FIXED_WIDTH避免UI过度拉伸
            policy = ResolutionPolicy.FIXED_WIDTH;
            console.log('[ScreenAdapter] 屏幕过宽，使用FIXED_WIDTH策略');
          } else if (screenRatio < designRatio * 0.8) {
            // 屏幕太窄，使用FIXED_HEIGHT
            policy = ResolutionPolicy.FIXED_HEIGHT;
            console.log('[ScreenAdapter] 屏幕过窄，使用FIXED_HEIGHT策略');
          } else {
            // 正常比例，使用SHOW_ALL保证完整显示
            policy = ResolutionPolicy.SHOW_ALL;
            console.log('[ScreenAdapter] 使用SHOW_ALL策略保证完整显示');
          } // 设置设计分辨率和适配策略


          view.setDesignResolutionSize(designWidth, designHeight, policy); // 输出最终的可视区域信息

          const visibleSize = view.getVisibleSize();
          const visibleOrigin = view.getVisibleOrigin();
          console.log(`[ScreenAdapter] 设计分辨率: ${designWidth}x${designHeight}`);
          console.log(`[ScreenAdapter] 可视区域: ${visibleSize.width}x${visibleSize.height}`);
          console.log(`[ScreenAdapter] 可视原点: (${visibleOrigin.x}, ${visibleOrigin.y})`); // 检查可视原点偏移

          if (visibleOrigin.x !== 0 || visibleOrigin.y !== 0) {
            console.warn(`[ScreenAdapter] 警告：可视原点不为(0,0)，这可能导致点击偏移！`);
          } // 检查缩放比例和可视原点


          const scaleX = visibleSize.width / designWidth;
          const scaleY = visibleSize.height / designHeight;
          console.log(`[ScreenAdapter] 实际缩放比例: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`); // 检查是否有严重的缩放异常（比如Y轴被压缩到0.3）

          if (scaleY < 0.5 || scaleX > 4.0) {
            console.error(`[ScreenAdapter] 严重错误：缩放比例异常！Y=${scaleY.toFixed(3)}, X=${scaleX.toFixed(3)}`);
            console.error(`[ScreenAdapter] 这会导致严重的点击偏移问题！`); // 强制使用SHOW_ALL策略重新设置

            console.log(`[ScreenAdapter] 强制重置为SHOW_ALL策略`);
            view.setDesignResolutionSize(designWidth, designHeight, ResolutionPolicy.SHOW_ALL); // 重新获取信息

            const newVisibleSize = view.getVisibleSize();
            const newVisibleOrigin = view.getVisibleOrigin();
            const newScaleX = newVisibleSize.width / designWidth;
            const newScaleY = newVisibleSize.height / designHeight;
            console.log(`[ScreenAdapter] 重置后信息:`);
            console.log(`[ScreenAdapter] 可视区域: ${newVisibleSize.width.toFixed(1)}x${newVisibleSize.height.toFixed(1)}`);
            console.log(`[ScreenAdapter] 可视原点: (${newVisibleOrigin.x.toFixed(1)}, ${newVisibleOrigin.y.toFixed(1)})`);
            console.log(`[ScreenAdapter] 缩放比例: X=${newScaleX.toFixed(3)}, Y=${newScaleY.toFixed(3)}`);
          }

          this._isInitialized = true;
        }
        /**
         * 获取屏幕适配信息
         */


        getAdaptInfo() {
          const windowSize = screen.windowSize;
          const visibleSize = view.getVisibleSize();
          const visibleOrigin = view.getVisibleOrigin();
          const designSize = view.getDesignResolutionSize();
          return {
            frameSize: windowSize,
            visibleSize: visibleSize,
            visibleOrigin: visibleOrigin,
            designSize: designSize,
            scaleX: visibleSize.width / designSize.width,
            scaleY: visibleSize.height / designSize.height
          };
        }
        /**
         * 适配UI节点到安全区域
         * @param node 需要适配的节点
         * @param alignTop 是否对齐到顶部安全区域
         * @param alignBottom 是否对齐到底部安全区域
         */


        adaptToSafeArea(node, alignTop = false, alignBottom = false) {
          if (!node) return;
          const widget = node.getComponent(Widget);

          if (!widget) {
            console.warn('[ScreenAdapter] 节点没有Widget组件，无法适配安全区域');
            return;
          } // 获取安全区域信息


          const safeArea = sys.getSafeAreaRect();
          const frameSize = view.getFrameSize();
          const visibleSize = view.getVisibleSize();

          if (alignTop && safeArea.y > 0) {
            // 适配顶部安全区域（如刘海屏）
            const topOffset = safeArea.y / frameSize.height * visibleSize.height;
            widget.top = topOffset;
            widget.isAlignTop = true;
          }

          if (alignBottom && safeArea.y + safeArea.height < frameSize.height) {
            // 适配底部安全区域（如虚拟按键）
            const bottomOffset = (frameSize.height - safeArea.y - safeArea.height) / frameSize.height * visibleSize.height;
            widget.bottom = bottomOffset;
            widget.isAlignBottom = true;
          }

          widget.updateAlignment();
        }
        /**
         * 检查点击位置是否在节点范围内
         * @param node 目标节点
         * @param worldPos 世界坐标点击位置
         */


        isPointInNode(node, worldPos) {
          if (!node) return false;
          const uiTransform = node.getComponent(UITransform);
          if (!uiTransform) return false; // 将世界坐标转换为节点本地坐标

          const localPos = uiTransform.convertToNodeSpaceAR(worldPos); // 检查是否在节点范围内

          const size = uiTransform.contentSize;
          const anchorPoint = uiTransform.anchorPoint;
          const minX = -size.width * anchorPoint.x;
          const maxX = size.width * (1 - anchorPoint.x);
          const minY = -size.height * anchorPoint.y;
          const maxY = size.height * (1 - anchorPoint.y);
          return localPos.x >= minX && localPos.x <= maxX && localPos.y >= minY && localPos.y <= maxY;
        }
        /**
         * 获取当前设备类型
         */


        getDeviceType() {
          const windowSize = screen.windowSize;
          const ratio = windowSize.width / windowSize.height;

          if (sys.isMobile) {
            if (ratio < 0.6) {
              return 'mobile_narrow'; // 窄屏手机
            } else if (ratio < 0.75) {
              return 'mobile_normal'; // 普通手机
            } else {
              return 'mobile_wide'; // 宽屏手机
            }
          } else {
            return 'desktop';
          }
        }
        /**
         * 打印调试信息
         */


        printDebugInfo() {
          const info = this.getAdaptInfo();
          console.log('=== 屏幕适配调试信息 ===');
          console.log(`设备类型: ${this.getDeviceType()}`);
          console.log(`窗口尺寸: ${info.frameSize.width}x${info.frameSize.height}`);
          console.log(`设计分辨率: ${info.designSize.width}x${info.designSize.height}`);
          console.log(`可视区域: ${info.visibleSize.width}x${info.visibleSize.height}`);
          console.log(`可视原点: (${info.visibleOrigin.x}, ${info.visibleOrigin.y})`);
          console.log(`缩放比例: X=${info.scaleX.toFixed(3)}, Y=${info.scaleY.toFixed(3)}`);

          if (sys.isMobile) {
            const safeArea = sys.getSafeAreaRect();
            console.log(`安全区域: x=${safeArea.x}, y=${safeArea.y}, w=${safeArea.width}, h=${safeArea.height}`);
          }

          console.log('========================');
        }

      }, _defineProperty(_class2, "MIN_RATIO", 9 / 21), _defineProperty(_class2, "MAX_RATIO", 9 / 16), _defineProperty(_class2, "_instance", null), _temp)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ScreenAdapter.js.map