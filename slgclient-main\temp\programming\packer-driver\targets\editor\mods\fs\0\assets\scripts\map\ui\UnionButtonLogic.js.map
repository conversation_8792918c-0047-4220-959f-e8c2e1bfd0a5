{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts"], "names": ["_decorator", "Component", "Node", "UnionCommand", "MapCommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "<PERSON><PERSON><PERSON>onL<PERSON><PERSON>", "onLoad", "on", "updateUnionApply", "updateView", "tipsNode", "active", "city", "getInstance", "cityProxy", "getMyMainCity", "cnt", "proxy", "getApplyCnt", "unionId", "onDestroy", "targetOff", "onClickClose", "node", "instance", "playClick"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAGzBC,MAAAA,Y;;AAEAC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAPH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAUTU,c,WADpBF,OAAO,CAAC,kBAAD,C,UAIHC,QAAQ,CAACP,IAAD,C,oCAJb,MACqBQ,cADrB,SAC4CT,SAD5C,CACsD;AAAA;AAAA;;AAAA;AAAA;;AAMxCU,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,gBAAvB,EAAyC,KAAKC,UAA9C,EAA0D,IAA1D;AACA,eAAKC,QAAL,CAAcC,MAAd,GAAuB,KAAvB;AACH;;AAGSF,QAAAA,UAAU,GAAO;AACvB,cAAIG,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIC,GAAG,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BI,KAA3B,CAAiCC,WAAjC,CAA6CN,IAAI,CAACO,OAAlD,CAAV;AACA,eAAKT,QAAL,CAAcC,MAAd,GAAuBK,GAAG,GAAG,CAA7B;AACH;;AAESI,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B,eAAKC,IAAL,CAAUZ,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaa,QAAb,CAAsBC,SAAtB;AACH;;AAzBiD,O;;;;;iBAIlC,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport UnionCommand from \"../../union/UnionCommand\";\nimport { MapCityData } from \"../MapCityProxy\";\nimport MapCommand from \"../MapCommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('UnionButtonLogic')\nexport default class WarButtonLogic extends Component {\n\n\n    @property(Node)\n    tipsNode:Node = null;\n\n    protected onLoad():void{\n        EventMgr.on(LogicEvent.updateUnionApply, this.updateView, this);\n        this.tipsNode.active = false;\n    }\n\n\n    protected updateView():void{\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let cnt = UnionCommand.getInstance().proxy.getApplyCnt(city.unionId);\n        this.tipsNode.active = cnt > 0;\n    }\n\n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n\n}\n"]}