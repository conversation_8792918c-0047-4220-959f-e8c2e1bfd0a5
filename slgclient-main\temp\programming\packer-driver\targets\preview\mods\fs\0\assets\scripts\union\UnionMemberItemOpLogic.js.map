{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "Node", "AudioManager", "MapCommand", "UnionCommand", "ccclass", "property", "UnionMemberItemOpLogic", "onLoad", "node", "on", "EventType", "TOUCH_END", "click", "onDestroy", "off", "active", "instance", "playClick", "setData", "data", "_menberData", "city", "getInstance", "cityProxy", "getMyMainCity", "unionData", "proxy", "getUnion", "unionId", "rid", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "getVice<PERSON>hairman", "unAppoint<PERSON><PERSON>on", "kickButton", "<PERSON><PERSON><PERSON><PERSON>", "ab<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "kick", "unionKick", "appoint", "unionAppoint", "unAppoint", "abdicate", "unionAbdicate"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;;AAC/BC,MAAAA,Y,iBAAAA,Y;;AAIFC,MAAAA,U;;AACAC,MAAAA,Y;;;;;;;OAJD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAQTS,sB,WADpBF,OAAO,CAAC,wBAAD,C,UAIHC,QAAQ,CAACN,MAAD,C,UAGRM,QAAQ,CAACN,MAAD,C,UAGRM,QAAQ,CAACN,MAAD,C,UAIRM,QAAQ,CAACN,MAAD,C,oCAdb,MACqBO,sBADrB,SACoDR,SADpD,CAC8D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,+CAiB3B,IAjB2B;AAAA;;AAmBhDS,QAAAA,MAAM,GAAO;AACnB,eAAKC,IAAL,CAAUC,EAAV,CAAaT,IAAI,CAACU,SAAL,CAAeC,SAA5B,EAAuC,KAAKC,KAA5C,EAAmD,IAAnD;AACH;;AAESC,QAAAA,SAAS,GAAO;AACtB,eAAKL,IAAL,CAAUM,GAAV,CAAcd,IAAI,CAACU,SAAL,CAAeC,SAA7B,EAAwC,KAAKC,KAA7C,EAAoD,IAApD;AACH;;AAESA,QAAAA,KAAK,GAAO;AAClB,eAAKJ,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACH;;AAEMC,QAAAA,OAAO,CAACC,IAAD,EAAW;AACrB,eAAKC,WAAL,GAAmBD,IAAnB;AACA,cAAIE,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIC,SAAe,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BI,KAA3B,CAAiCC,QAAjC,CAA0CN,IAAI,CAACO,OAA/C,CAAtB;;AACA,cAAIH,SAAJ,EAAc;AACV,gBAAG,KAAKL,WAAL,CAAiBS,GAAjB,IAAwBR,IAAI,CAACQ,GAAhC,EAAoC;AAChC,mBAAKrB,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACH,aAFD,MAEK;AACD,kBAAGU,SAAS,CAACK,WAAV,GAAwBD,GAAxB,IAA+BR,IAAI,CAACQ,GAAvC,EAA2C;AACvCE,gBAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BP,SAA1B,EAAqCA,SAAS,CAACQ,eAAV,EAArC,EAAkE,KAAKb,WAAvE;AAEA,qBAAKc,eAAL,CAAqB1B,IAArB,CAA0BO,MAA1B,GAAmCU,SAAS,CAACQ,eAAV,GAA4BJ,GAA5B,IAAmC,KAAKT,WAAL,CAAiBS,GAAvF;AACA,qBAAKM,UAAL,CAAgB3B,IAAhB,CAAqBO,MAArB,GAA8BU,SAAS,CAACW,OAAV,CAAkBf,IAAI,CAACQ,GAAvB,CAA9B;AACA,qBAAKQ,cAAL,CAAoB7B,IAApB,CAAyBO,MAAzB,GAAkCU,SAAS,CAACK,WAAV,GAAwBD,GAAxB,IAA+BR,IAAI,CAACQ,GAAtE;AACA,qBAAKS,aAAL,CAAmB9B,IAAnB,CAAwBO,MAAxB,GAAiCU,SAAS,CAACK,WAAV,GAAwBD,GAAxB,IAA+BR,IAAI,CAACQ,GAApC,IAA2CJ,SAAS,CAACQ,eAAV,GAA4BJ,GAA5B,IAAmC,KAAKT,WAAL,CAAiBS,GAAhI;AACH,eAPD,MAOM,IAAGJ,SAAS,CAACQ,eAAV,GAA4BJ,GAA5B,IAAmCR,IAAI,CAACQ,GAA3C,EAA+C;AACjD,oBAAGJ,SAAS,CAACK,WAAV,GAAwBD,GAAxB,IAA+B,KAAKT,WAAL,CAAiBS,GAAnD,EAAuD;AACnD,uBAAKrB,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACH,iBAFD,MAEK;AACD,uBAAKmB,eAAL,CAAqB1B,IAArB,CAA0BO,MAA1B,GAAmC,KAAnC;AACA,uBAAKoB,UAAL,CAAgB3B,IAAhB,CAAqBO,MAArB,GAA8B,IAA9B;AACA,uBAAKsB,cAAL,CAAoB7B,IAApB,CAAyBO,MAAzB,GAAkCU,SAAS,CAACQ,eAAV,GAA4BJ,GAA5B,IAAmC,KAAKT,WAAL,CAAiBS,GAAtF;AACA,uBAAKS,aAAL,CAAmB9B,IAAnB,CAAwBO,MAAxB,GAAiC,KAAjC;AACA,uBAAKP,IAAL,CAAUO,MAAV,GAAmB,IAAnB;AACH;AACJ,eAVK,MAUD;AACD,qBAAKP,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACH;AACJ;AACJ,WAzBD,MAyBK;AACD,iBAAKP,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACH;AAEJ;;AAESwB,QAAAA,IAAI,GAAO;AACjB;AAAA;AAAA,4CAAavB,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaK,WAAb,GAA2BkB,SAA3B,CAAqC,KAAKpB,WAAL,CAAiBS,GAAtD;AACA,eAAKrB,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACH;;AAGS0B,QAAAA,OAAO,GAAO;AACpB;AAAA;AAAA,4CAAazB,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaK,WAAb,GAA2BoB,YAA3B,CAAwC,KAAKtB,WAAL,CAAiBS,GAAzD,EAA8D,CAA9D;AACA,eAAKrB,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACH;;AAES4B,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,4CAAa3B,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaK,WAAb,GAA2BoB,YAA3B,CAAwC,KAAKtB,WAAL,CAAiBS,GAAzD,EAA8D,CAA9D;AACA,eAAKrB,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACH;;AAES6B,QAAAA,QAAQ,GAAO;AACrB;AAAA;AAAA,4CAAa5B,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaK,WAAb,GAA2BuB,aAA3B,CAAyC,KAAKzB,WAAL,CAAiBS,GAA1D;AACA,eAAKrB,IAAL,CAAUO,MAAV,GAAmB,KAAnB;AACH;;AA1FyD,O;;;;;iBAIrC,I;;;;;;;iBAGI,I;;;;;;;iBAGD,I;;;;;;;iBAIE,I", "sourcesContent": ["import { _decorator, Component, Button, Node } from 'cc';\nimport { AudioManager } from '../common/AudioManager';\nconst { ccclass, property } = _decorator;\n\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport UnionCommand from \"./UnionCommand\";\nimport { Member, Union } from \"./UnionProxy\";\n\n@ccclass('UnionMemberItemOpLogic')\nexport default class UnionMemberItemOpLogic extends Component {\n\n\n    @property(Button)\n    kickButton: Button = null;\n\n    @property(Button)\n    abdicateButton: Button = null;\n\n    @property(Button)\n    appointButton: Button = null;\n\n    \n    @property(Button)\n    unAppointButton: Button = null;\n    \n\n    protected _menberData:Member = null;\n\n    protected onLoad():void{\n        this.node.on(Node.EventType.TOUCH_END, this.click, this);\n    }\n\n    protected onDestroy():void{\n        this.node.off(Node.EventType.TOUCH_END, this.click, this);\n    }\n\n    protected click():void{\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n    \n    public setData(data):void{\n        this._menberData = data;\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let unionData:Union = UnionCommand.getInstance().proxy.getUnion(city.unionId);\n        if (unionData){\n            if(this._menberData.rid == city.rid){\n                this.node.active = false;\n            }else{\n                if(unionData.getChairman().rid == city.rid){\n                    console.log(\"unionData:\", unionData, unionData.getViceChairman(), this._menberData);\n\n                    this.unAppointButton.node.active = unionData.getViceChairman().rid == this._menberData.rid;\n                    this.kickButton.node.active = unionData.isMajor(city.rid);\n                    this.abdicateButton.node.active = unionData.getChairman().rid == city.rid;\n                    this.appointButton.node.active = unionData.getChairman().rid == city.rid && unionData.getViceChairman().rid != this._menberData.rid;\n                }else if(unionData.getViceChairman().rid == city.rid){\n                    if(unionData.getChairman().rid == this._menberData.rid){\n                        this.node.active = false;\n                    }else{\n                        this.unAppointButton.node.active = false;\n                        this.kickButton.node.active = true;\n                        this.abdicateButton.node.active = unionData.getViceChairman().rid != this._menberData.rid;\n                        this.appointButton.node.active = false;\n                        this.node.active = true;\n                    }\n                }else{\n                    this.node.active = false;\n                }\n            }\n        }else{\n            this.node.active = false;\n        }\n       \n    }\n\n    protected kick():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionKick(this._menberData.rid);\n        this.node.active = false;\n    }\n\n    \n    protected appoint():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionAppoint(this._menberData.rid, 1);\n        this.node.active = false;\n    }\n\n    protected unAppoint():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionAppoint(this._menberData.rid, 2);\n        this.node.active = false;\n    }\n\n    protected abdicate():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionAbdicate(this._menberData.rid);\n        this.node.active = false;\n    }\n    \n\n}\n"]}