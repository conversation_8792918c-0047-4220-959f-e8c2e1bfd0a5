import { _decorator, Component, Canvas, view, UITransform, sys, director } from 'cc';
import { ScreenAdapter } from './ScreenAdapter';

const { ccclass, property, requireComponent } = _decorator;

/**
 * Canvas适配组件
 * 解决Canvas在不同分辨率下的点击偏移问题
 */
@ccclass('CanvasAdapter')
@requireComponent(Canvas)
export class CanvasAdapter extends Component {
    
    private _canvas: Canvas = null;
    private _uiTransform: UITransform = null;
    
    protected onLoad(): void {
        this._canvas = this.getComponent(Canvas);
        this._uiTransform = this.getComponent(UITransform);

        // 确保Canvas不自动对齐屏幕，避免坐标系统混乱
        this._canvas.alignCanvasWithScreen = false;

        // 确保Canvas位置为原点
        this.node.setPosition(0, 0, 0);

        // 确保Canvas的锚点为(0.5, 0.5)，这是默认值
        if (this._uiTransform) {
            this._uiTransform.setAnchorPoint(0.5, 0.5);
        }

        console.log(`[CanvasAdapter] Canvas适配完成: position=(0,0,0), anchor=(0.5,0.5), alignCanvasWithScreen=false`);
    }
    
    protected onDestroy(): void {
        // 移除了屏幕尺寸变化监听，简化处理
    }
    
    /**
     * 获取Canvas适配信息
     */
    public getCanvasInfo(): any {
        const visibleSize = view.getVisibleSize();
        const designSize = view.getDesignResolutionSize();
        const canvasSize = this._uiTransform.contentSize;
        
        return {
            visibleSize: visibleSize,
            designSize: designSize,
            canvasSize: canvasSize,
            scaleX: visibleSize.width / designSize.width,
            scaleY: visibleSize.height / designSize.height
        };
    }
}
