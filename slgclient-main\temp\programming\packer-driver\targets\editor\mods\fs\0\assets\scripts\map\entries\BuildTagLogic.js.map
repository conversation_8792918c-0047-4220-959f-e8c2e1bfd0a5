{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts"], "names": ["_decorator", "Component", "Node", "MapCommand", "EventMgr", "LogicEvent", "ccclass", "property", "BuildTagLogic", "onLoad", "tagIconNode", "active", "onEnable", "on", "updateTag", "onUpdateTag", "onDisable", "_data", "targetOff", "setResourceData", "data", "updateUI", "getInstance", "proxy", "isPosTag", "x", "y"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACzBC,MAAAA,U;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;yBAITQ,a,WADpBF,OAAO,CAAC,eAAD,C,UAKHC,QAAQ,CAACL,IAAD,C,oCALb,MACqBM,aADrB,SAC2CP,SAD3C,CACqD;AAAA;AAAA;;AAAA,yCAEnB,IAFmB;;AAAA;AAAA;;AAOvCQ,QAAAA,MAAM,GAAS;AACrB,eAAKC,WAAL,CAAiBC,MAAjB,GAA0B,KAA1B;AACH;;AAESC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,SAAvB,EAAkC,KAAKC,WAAvC,EAAoD,IAApD;AAEH;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKC,KAAL,GAAa,IAAb;AACA;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOC,QAAAA,eAAe,CAACC,IAAD,EAAyB;AAC5C,eAAKH,KAAL,GAAaG,IAAb;AACA,eAAKC,QAAL;AACF;;AAEKA,QAAAA,QAAQ,GAAS;AACpB,cAAI,KAAKJ,KAAT,EAAgB;AACZ,iBAAKP,WAAL,CAAiBC,MAAjB,GAA0B;AAAA;AAAA,0CAAWW,WAAX,GAAyBC,KAAzB,CAA+BC,QAA/B,CAAwC,KAAKP,KAAL,CAAWQ,CAAnD,EAAsD,KAAKR,KAAL,CAAWS,CAAjE,CAA1B;AACH;AACJ;;AAESX,QAAAA,WAAW,GAAG;AACpB,eAAKM,QAAL;AACH;;AAlCgD,O;;;;;iBAK7B,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\nimport MapCommand from \"../MapCommand\";\nimport { MapResData } from \"../MapProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { LogicEvent } from '../../common/LogicEvent';\nconst { ccclass, property } = _decorator;\n\n\n@ccclass('BuildTagLogic')\nexport default class BuildTagLogic extends Component {\n   \n    protected _data: MapResData = null;\n\n    @property(Node)\n    tagIconNode: Node = null;\n\n    protected onLoad(): void {\n        this.tagIconNode.active = false;\n    }\n\n    protected onEnable(): void {\n        EventMgr.on(LogicEvent.updateTag, this.onUpdateTag, this);\n\n    }\n\n    protected onDisable(): void {\n        this._data = null;\n        EventMgr.targetOff(this);\n    }\n\n     public setResourceData(data: MapResData): void {\n        this._data = data;\n        this.updateUI();\n     }\n\n    public updateUI(): void {\n        if (this._data) {\n            this.tagIconNode.active = MapCommand.getInstance().proxy.isPosTag(this._data.x, this._data.y);\n        }\n    }\n\n    protected onUpdateTag() {\n        this.updateUI();\n    }\n}\n"]}