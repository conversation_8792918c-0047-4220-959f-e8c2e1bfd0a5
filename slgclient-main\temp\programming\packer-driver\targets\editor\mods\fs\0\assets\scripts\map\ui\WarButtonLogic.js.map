{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts"], "names": ["_decorator", "Component", "Node", "MapUICommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "<PERSON><PERSON><PERSON>onL<PERSON><PERSON>", "onLoad", "on", "upateWarReport", "updateView", "tipsNode", "active", "getInstance", "proxy", "isReadNum", "onDestroy", "targetOff", "onClickClose", "node", "instance", "playClick"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAGzBC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAQTS,c,WADpBF,OAAO,CAAC,gBAAD,C,UAIHC,QAAQ,CAACN,IAAD,C,oCAJb,MACqBO,cADrB,SAC4CR,SAD5C,CACsD;AAAA;AAAA;;AAAA;AAAA;;AAMxCS,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,cAAvB,EAAuC,KAAKC,UAA5C,EAAwD,IAAxD;AACA,eAAKA,UAAL;AACH;;AAGSA,QAAAA,UAAU,GAAO;AACvB,eAAKC,QAAL,CAAcC,MAAd,GAAuB;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,SAAjC,KAA6C,CAA7C,GAA+C,IAA/C,GAAoD,KAA3E;AACH;;AAESC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B,eAAKC,IAAL,CAAUP,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaQ,QAAb,CAAsBC,SAAtB;AACH;;AAvBiD,O;;;;;iBAIlC,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport MapUICommand from \"./MapUICommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('WarButtonLogic')\nexport default class WarButtonLogic extends Component {\n\n\n    @property(Node)\n    tipsNode:Node = null;\n\n    protected onLoad():void{\n        EventMgr.on(LogicEvent.upateWarReport, this.updateView, this);\n        this.updateView();\n    }\n\n\n    protected updateView():void{\n        this.tipsNode.active = MapUICommand.getInstance().proxy.isReadNum()>0?true:false;\n    }\n\n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n\n}\n"]}