{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"], "names": ["GeneralConfig", "GenaralLevelConfig", "GeneralCampType", "GeneralCommonConfig", "gSkill", "GeneralData", "GeneralProxy", "createFromServer", "serverData", "generalData", "generalCfg", "data", "id", "cfgId", "exp", "level", "order", "physical_power", "star_lv", "parentId", "state", "hasPrPoint", "usePrPoint", "force_added", "strategy_added", "defense_added", "speed_added", "destroy_added", "config", "skills", "getPrValue", "pr", "group", "add", "getPrStr", "lv", "grow", "Map", "clearData", "_myGenerals", "clear", "initGeneralConfig", "cfgs", "bCost", "cfgData", "levelData", "i", "length", "console", "_name", "json", "list", "levels", "_generalConfigs", "cfg", "name", "force", "strategy", "defense", "speed", "destroy", "cost", "force_grow", "strategy_grow", "defense_grow", "speed_grow", "destroy_grow", "physical_power_limit", "general", "cost_physical_power", "star", "arms", "camp", "set", "_levelConfigs", "levelCfg", "soldiers", "_commonConfig", "recovery_physical_power", "reclamation_time", "draw_general_cost", "initGeneralTex", "texs", "_generalTexs", "Number", "String", "split", "updateMyGenerals", "datas", "get", "updateGeneral", "delete", "ids", "push", "removeMyGenerals", "for<PERSON>ach", "getGeneralCfg", "has", "getGeneralAllCfg", "getGeneralLevelCfg", "getMaxLevel", "getGeneralTex", "setGeneralTex", "frame", "getCommonCfg", "getMyActiveGeneralCnt", "arr", "getMyGenerals", "cnt", "g", "Array", "from", "values", "getMyGeneralsNotUse", "generals", "obj", "getMyGeneral", "getGeneralIds", "myGenerals", "tempGenerals", "sortStar", "a", "b", "getUseGenerals", "tempArr", "concat", "sort", "temp", "log", "splice", "getComposeGenerals"], "mappings": ";;;iBAIaA,a,EAyBAC,kB,EAOAC,e,EASAC,mB,EAQAC,M,EAOAC,W,EA6DQC,Y;;;;;;;;;;;;;;;;;;;;;;;AAtHrB;+BACaN,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,wCACR,EADQ;;AAAA,yCAEP,CAFO;;AAAA,yCAGP,CAHO;;AAAA,4CAIJ,CAJI;;AAAA,2CAKL,CALK;;AAAA,yCAMP,CANO;;AAAA,2CAOL,CAPK;;AAAA,wCAQR,CARQ;;AAAA,8CAUF,CAVE;;AAAA,iDAWC,CAXD;;AAAA,gDAYA,CAZA;;AAAA,8CAaF,CAbE;;AAAA,gDAcA,CAdA;;AAAA,wDAeQ,CAfR;;AAAA,uDAgBO,CAhBP;;AAAA,+CAiBD,CAjBC;;AAAA,wCAmBR,CAnBQ;;AAAA,wCAoBN,EApBM;;AAAA,wCAqBR,CArBQ;AAAA;;AAAA,O;AAwB3B;;;oCACaC,kB,GAAN,MAAMA,kBAAN,CAAyB;AAAA;AAAA,yCACZ,CADY;;AAAA,uCAEd,CAFc;;AAAA,4CAGT,CAHS;AAAA;;AAAA,O;AAMhC;;;iCACaC,e,GAAN,MAAMA,eAAN,CAAsB,E;AAQ7B;;;sBARaA,e,SACY,C;;sBADZA,e,SAEY,C;;sBAFZA,e,SAGY,C;;sBAHZA,e,SAIY,C;;sBAJZA,e,QAKW,C;;qCAIXC,mB,GAAN,MAAMA,mBAAN,CAA0B;AAAA;AAAA,wDACE,GADF;;AAAA,uDAEC,EAFD;;AAAA,2DAGK,EAHL;;AAAA,oDAIF,IAJE;;AAAA,qDAKD,CALC;AAAA;;AAAA,O;;wBAQpBC,M,GAAN,MAAMA,MAAN,CAAa;AAAA;AAAA,sCACH,CADG;;AAAA,sCAEN,CAFM;;AAAA,yCAGH,CAHG;AAAA;;AAAA,O;AAMpB;;;6BACaC,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA,sCACR,CADQ;;AAAA,yCAEL,CAFK;;AAAA,uCAGP,CAHO;;AAAA,yCAIL,CAJK;;AAAA,kDAKI,CALJ;;AAAA,yCAML,CANK;;AAAA,2CAOH,CAPG;;AAAA,4CAQF,CARE;;AAAA,yCASL,CATK;;AAAA,8CAWA,CAXA;;AAAA,8CAYA,CAZA;;AAAA,+CAaC,CAbD;;AAAA,kDAcI,CAdJ;;AAAA,iDAeG,CAfH;;AAAA,+CAgBC,CAhBD;;AAAA,iDAiBG,CAjBH;;AAAA,0CAkBG,IAAIL,aAAJ,EAlBH;;AAAA,0CAmBF,EAnBE;AAAA;;AAsBS,eAAhBO,gBAAgB,CAACC,UAAD,EAAkBC,WAAwB,GAAG,IAA7C,EAAmDC,UAAnD,EAA2F;AACrH,cAAIC,IAAiB,GAAGF,WAAxB;;AACA,cAAIE,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIN,WAAJ,EAAP;AACH;;AACDM,UAAAA,IAAI,CAACC,EAAL,GAAUJ,UAAU,CAACI,EAArB;AACAD,UAAAA,IAAI,CAACE,KAAL,GAAaL,UAAU,CAACK,KAAxB;AACAF,UAAAA,IAAI,CAACG,GAAL,GAAWN,UAAU,CAACM,GAAtB;AACAH,UAAAA,IAAI,CAACI,KAAL,GAAaP,UAAU,CAACO,KAAxB;AACAJ,UAAAA,IAAI,CAACK,KAAL,GAAaR,UAAU,CAACQ,KAAxB;AACAL,UAAAA,IAAI,CAACM,cAAL,GAAsBT,UAAU,CAACS,cAAjC;AACAN,UAAAA,IAAI,CAACO,OAAL,GAAeV,UAAU,CAACU,OAA1B;AACAP,UAAAA,IAAI,CAACQ,QAAL,GAAgBX,UAAU,CAACW,QAA3B;AACAR,UAAAA,IAAI,CAACS,KAAL,GAAaZ,UAAU,CAACY,KAAxB;AAEAT,UAAAA,IAAI,CAACU,UAAL,GAAkBb,UAAU,CAACa,UAA7B;AACAV,UAAAA,IAAI,CAACW,UAAL,GAAkBd,UAAU,CAACc,UAA7B;AACAX,UAAAA,IAAI,CAACY,WAAL,GAAmBf,UAAU,CAACe,WAA9B;AACAZ,UAAAA,IAAI,CAACa,cAAL,GAAsBhB,UAAU,CAACgB,cAAjC;AACAb,UAAAA,IAAI,CAACc,aAAL,GAAqBjB,UAAU,CAACiB,aAAhC;AACAd,UAAAA,IAAI,CAACe,WAAL,GAAmBlB,UAAU,CAACkB,WAA9B;AACAf,UAAAA,IAAI,CAACgB,aAAL,GAAqBnB,UAAU,CAACmB,aAAhC;AACAhB,UAAAA,IAAI,CAACiB,MAAL,GAAclB,UAAd;AACAC,UAAAA,IAAI,CAACkB,MAAL,GAAcrB,UAAU,CAACqB,MAAzB,CAvBqH,CAyBrH;;AAEA,iBAAOlB,IAAP;AACH;;AAEuB,eAAVmB,UAAU,CAACC,EAAU,GAAG,CAAd,EAAiBC,KAAjB,EAA+BC,GAAW,GAAG,CAA7C,EAAwD;AAC5E,iBAAO,CAACF,EAAE,GAAGC,KAAL,GAAaC,GAAd,IAAqB,GAA5B;AACH;;AAEqB,eAARC,QAAQ,CAACH,EAAU,GAAG,CAAd,EAAiBE,GAAW,GAAG,CAA/B,EAAkCE,EAAU,GAAG,CAA/C,EAAkDC,IAAY,GAAG,CAAjE,EAA4E;AAC9F,iBAAO,CAACL,EAAE,GAAGE,GAAN,IAAa,GAAb,GAAmB,IAAnB,GAA0BE,EAAE,GAAGC,IAAL,GAAY,GAAtC,GAA4C,GAAnD;AACH;;AA1DoB,O;;yBA6DJ9B,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,mDAE0B,IAAI+B,GAAJ,EAF1B;;AAAA,iDAGkB,EAHlB;;AAAA,iDAIiB,IAAIlC,mBAAJ,EAJjB;;AAAA,gDAKqB,IAAIkC,GAAJ,EALrB;;AAAA,+CAMoB,IAAIA,GAAJ,EANpB;AAAA;;AAQvBC,QAAAA,SAAS,GAAS;AACrB,eAAKC,WAAL,CAAiBC,KAAjB;AACH;;AAEMC,QAAAA,iBAAiB,CAACC,IAAD,EAAcC,KAAd,EAAgC;AACpD,cAAIC,OAAY,GAAG,IAAnB;AACA,cAAIC,SAAc,GAAG,IAArB;;AACA,eAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGJ,IAAI,CAACK,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1CE,YAAAA,OAAO;;AACP,gBAAIN,IAAI,CAACI,CAAD,CAAJ,CAAQG,KAAR,IAAiB,SAArB,EAAgC;AAC5BL,cAAAA,OAAO,GAAGF,IAAI,CAACI,CAAD,CAAJ,CAAQI,IAAR,CAAaC,IAAvB;AACH,aAFD,MAEO,IAAIT,IAAI,CAACI,CAAD,CAAJ,CAAQG,KAAR,IAAiB,eAArB,EAAsC;AACzCJ,cAAAA,SAAS,GAAGH,IAAI,CAACI,CAAD,CAAJ,CAAQI,IAAR,CAAaE,MAAzB;AACH;AACJ;;AAED,cAAIR,OAAJ,EAAa;AACT,iBAAKS,eAAL,CAAqBb,KAArB;;AACA,iBAAK,IAAIM,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGF,OAAO,CAACG,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC7C,kBAAIQ,GAAkB,GAAG,IAAItD,aAAJ,EAAzB;AACAsD,cAAAA,GAAG,CAACzC,KAAJ,GAAY+B,OAAO,CAACE,CAAD,CAAP,CAAWjC,KAAvB;AACAyC,cAAAA,GAAG,CAACC,IAAJ,GAAWX,OAAO,CAACE,CAAD,CAAP,CAAWS,IAAtB;AACAD,cAAAA,GAAG,CAACE,KAAJ,GAAYZ,OAAO,CAACE,CAAD,CAAP,CAAWU,KAAvB;AACAF,cAAAA,GAAG,CAACG,QAAJ,GAAeb,OAAO,CAACE,CAAD,CAAP,CAAWW,QAA1B;AACAH,cAAAA,GAAG,CAACI,OAAJ,GAAcd,OAAO,CAACE,CAAD,CAAP,CAAWY,OAAzB;AACAJ,cAAAA,GAAG,CAACK,KAAJ,GAAYf,OAAO,CAACE,CAAD,CAAP,CAAWa,KAAvB;AACAL,cAAAA,GAAG,CAACM,OAAJ,GAAchB,OAAO,CAACE,CAAD,CAAP,CAAWc,OAAzB;AACAN,cAAAA,GAAG,CAACO,IAAJ,GAAWjB,OAAO,CAACE,CAAD,CAAP,CAAWe,IAAtB;AAEAP,cAAAA,GAAG,CAACQ,UAAJ,GAAiBlB,OAAO,CAACE,CAAD,CAAP,CAAWgB,UAA5B;AACAR,cAAAA,GAAG,CAACS,aAAJ,GAAoBnB,OAAO,CAACE,CAAD,CAAP,CAAWiB,aAA/B;AACAT,cAAAA,GAAG,CAACU,YAAJ,GAAmBpB,OAAO,CAACE,CAAD,CAAP,CAAWkB,YAA9B;AACAV,cAAAA,GAAG,CAACW,UAAJ,GAAiBrB,OAAO,CAACE,CAAD,CAAP,CAAWmB,UAA5B;AACAX,cAAAA,GAAG,CAACY,YAAJ,GAAmBtB,OAAO,CAACE,CAAD,CAAP,CAAWoB,YAA9B;AACAZ,cAAAA,GAAG,CAACa,oBAAJ,GAA2BxB,KAAK,CAACyB,OAAN,CAAcD,oBAAzC;AACAb,cAAAA,GAAG,CAACe,mBAAJ,GAA0B1B,KAAK,CAACyB,OAAN,CAAcC,mBAAxC;AAEAf,cAAAA,GAAG,CAACgB,IAAJ,GAAW1B,OAAO,CAACE,CAAD,CAAP,CAAWwB,IAAtB;AACAhB,cAAAA,GAAG,CAACiB,IAAJ,GAAW3B,OAAO,CAACE,CAAD,CAAP,CAAWyB,IAAtB;AACAjB,cAAAA,GAAG,CAACkB,IAAJ,GAAW5B,OAAO,CAACE,CAAD,CAAP,CAAW0B,IAAtB;;AACA,mBAAKnB,eAAL,CAAqBoB,GAArB,CAAyBnB,GAAG,CAACzC,KAA7B,EAAoCyC,GAApC;AACH;AACJ;;AAED,cAAIT,SAAJ,EAAe;AACX,iBAAK6B,aAAL,CAAmB3B,MAAnB,GAA4BF,SAAS,CAACE,MAAtC;;AACA,iBAAK,IAAID,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGD,SAAS,CAACE,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,kBAAI6B,QAA4B,GAAG,IAAI1E,kBAAJ,EAAnC;AACA0E,cAAAA,QAAQ,CAAC5D,KAAT,GAAiB8B,SAAS,CAACC,CAAD,CAAT,CAAa/B,KAA9B;AACA4D,cAAAA,QAAQ,CAAC7D,GAAT,GAAe+B,SAAS,CAACC,CAAD,CAAT,CAAahC,GAA5B;AACA6D,cAAAA,QAAQ,CAACC,QAAT,GAAoB/B,SAAS,CAACC,CAAD,CAAT,CAAa8B,QAAjC;AACA,mBAAKF,aAAL,CAAmBC,QAAQ,CAAC5D,KAAT,GAAiB,CAApC,IAAyC4D,QAAzC;AACH;AACJ;;AAED,eAAKE,aAAL,CAAmBV,oBAAnB,GAA0CxB,KAAK,CAACyB,OAAN,CAAcD,oBAAxD;AACA,eAAKU,aAAL,CAAmBR,mBAAnB,GAAyC1B,KAAK,CAACyB,OAAN,CAAcC,mBAAvD;AACA,eAAKQ,aAAL,CAAmBC,uBAAnB,GAA6CnC,KAAK,CAACyB,OAAN,CAAcU,uBAA3D;AACA,eAAKD,aAAL,CAAmBE,gBAAnB,GAAsCpC,KAAK,CAACyB,OAAN,CAAcW,gBAApD;AACA,eAAKF,aAAL,CAAmBG,iBAAnB,GAAuCrC,KAAK,CAACyB,OAAN,CAAcY,iBAArD;AACH;;AAEMC,QAAAA,cAAc,CAACC,IAAD,EAA4B;AAC7C,cAAGA,IAAI,CAACnC,MAAL,IAAe,CAAlB,EAAoB;AAChB;AACH;;AAED,eAAKoC,YAAL,CAAkB3C,KAAlB;;AACA,eAAK,IAAIM,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGoC,IAAI,CAACnC,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1C,gBAAIlC,EAAU,GAAGwE,MAAM,CAACC,MAAM,CAACH,IAAI,CAACpC,CAAD,CAAJ,CAAQS,IAAT,CAAN,CAAqB+B,KAArB,CAA2B,GAA3B,EAAgC,CAAhC,CAAD,CAAvB;;AACA,iBAAKH,YAAL,CAAkBV,GAAlB,CAAsB7D,EAAtB,EAA0BsE,IAAI,CAACpC,CAAD,CAA9B;AACH;AACJ;;AAEMyC,QAAAA,gBAAgB,CAACC,KAAD,EAAqB;AACxC,eAAK,IAAI1C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0C,KAAK,CAACzC,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACnC,gBAAInC,IAAiB,GAAGN,WAAW,CAACE,gBAAZ,CAA6BiF,KAAK,CAAC1C,CAAD,CAAlC,EAAuC,IAAvC,EAA6C,KAAKO,eAAL,CAAqBoC,GAArB,CAAyBD,KAAK,CAAC1C,CAAD,CAAL,CAASjC,KAAlC,CAA7C,CAAxB;;AACA,iBAAK0B,WAAL,CAAiBkC,GAAjB,CAAqB9D,IAAI,CAACC,EAA1B,EAA8BD,IAA9B;AACH;AACJ;;AAEM+E,QAAAA,aAAa,CAAC/E,IAAD,EAAY;AAC5B,cAAGA,IAAI,CAACS,KAAL,IAAc,CAAjB,EAAmB;AACf,iBAAKmB,WAAL,CAAiBoD,MAAjB,CAAwBhF,IAAI,CAACC,EAA7B;AACH,WAFD,MAEK;AACD,gBAAIgF,GAAa,GAAG,EAApB;AACA,gBAAIxB,OAAoB,GAAG/D,WAAW,CAACE,gBAAZ,CAA6BI,IAA7B,EAAmC,KAAK4B,WAAL,CAAiBkD,GAAjB,CAAqB9E,IAAI,CAACC,EAA1B,CAAnC,EAAkE,KAAKyC,eAAL,CAAqBoC,GAArB,CAAyB9E,IAAI,CAACE,KAA9B,CAAlE,CAA3B;;AACA,iBAAK0B,WAAL,CAAiBkC,GAAjB,CAAqBL,OAAO,CAACxD,EAA7B,EAAiCwD,OAAjC;;AACAwB,YAAAA,GAAG,CAACC,IAAJ,CAASzB,OAAO,CAACxD,EAAjB;AACH;AACJ;;AAEMkF,QAAAA,gBAAgB,CAACF,GAAD,EAAgB;AACnCA,UAAAA,GAAG,CAACG,OAAJ,CAAYnF,EAAE,IAAI;AACd,iBAAK2B,WAAL,CAAiBoD,MAAjB,CAAwB/E,EAAxB;AACH,WAFD;AAGH;AAED;;;AACOoF,QAAAA,aAAa,CAACnF,KAAD,EAA+B;AAC/C,cAAI,KAAKwC,eAAL,CAAqB4C,GAArB,CAAyBpF,KAAzB,CAAJ,EAAqC;AACjC,mBAAO,KAAKwC,eAAL,CAAqBoC,GAArB,CAAyB5E,KAAzB,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAEMqF,QAAAA,gBAAgB,GAA8B;AACjD,iBAAO,KAAK7C,eAAZ;AACH;AAED;;;AACO8C,QAAAA,kBAAkB,CAACpF,KAAD,EAAoC;AACzD,cAAIA,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAI,KAAK2D,aAAL,CAAmB3B,MAA7C,EAAqD;AACjD,mBAAO,KAAK2B,aAAL,CAAmB3D,KAAK,GAAG,CAA3B,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAEMqF,QAAAA,WAAW,GAAW;AACzB,iBAAO,KAAK1B,aAAL,CAAmB3B,MAA1B;AACH;AAED;;;AACOsD,QAAAA,aAAa,CAACxF,KAAD,EAA6B;AAC7C,cAAI,KAAKsE,YAAL,CAAkBc,GAAlB,CAAsBpF,KAAtB,CAAJ,EAAkC;AAC9B,mBAAO,KAAKsE,YAAL,CAAkBM,GAAlB,CAAsB5E,KAAtB,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAEMyF,QAAAA,aAAa,CAACzF,KAAD,EAAgB0F,KAAhB,EAAoC;AACpD,eAAKpB,YAAL,CAAkBV,GAAlB,CAAsB5D,KAAtB,EAA6B0F,KAA7B;AACH;AAED;;;AACOC,QAAAA,YAAY,GAAwB;AACvC,iBAAO,KAAK3B,aAAZ;AACH;;AAGM4B,QAAAA,qBAAqB,GAAG;AAC3B,cAAIC,GAAG,GAAG,KAAKC,aAAL,EAAV;AACA,cAAIC,GAAG,GAAG,CAAV;AACAF,UAAAA,GAAG,CAACX,OAAJ,CAAYc,CAAC,IAAI;AACb,gBAAGA,CAAC,CAACzF,KAAF,IAAW,CAAd,EAAgB;AACZwF,cAAAA,GAAG,IAAI,CAAP;AACH;AACJ,WAJD;AAKA,iBAAOA,GAAP;AACH;AAED;;;AACOD,QAAAA,aAAa,GAAkB;AAClC,iBAAOG,KAAK,CAACC,IAAN,CAAW,KAAKxE,WAAL,CAAiByE,MAAjB,EAAX,CAAP;AACH;;AAEMC,QAAAA,mBAAmB,GAAkB;AACxC,cAAIP,GAAG,GAAGI,KAAK,CAACC,IAAN,CAAW,KAAKxE,WAAL,CAAiByE,MAAjB,EAAX,CAAV;AACA,cAAIE,QAAuB,GAAG,EAA9B;;AACA,eAAK,IAAIpE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4D,GAAG,CAAC3D,MAAxB,EAAgCD,CAAC,EAAjC,EAAqC;AACjC,kBAAMqE,GAAG,GAAGT,GAAG,CAAC5D,CAAD,CAAf;;AACA,gBAAIqE,GAAG,CAACnG,KAAJ,IAAa,CAAb,IAAkBmG,GAAG,CAAC/F,KAAJ,IAAa,CAAnC,EAAqC;AACjC8F,cAAAA,QAAQ,CAACrB,IAAT,CAAcsB,GAAd;AACH;AACJ;;AACD,iBAAOD,QAAP;AACH;AAGD;;;AACOE,QAAAA,YAAY,CAACxG,EAAD,EAA0B;AACzC,cAAI,KAAK2B,WAAL,CAAiB0D,GAAjB,CAAqBrF,EAArB,CAAJ,EAA8B;AAC1B,mBAAO,KAAK2B,WAAL,CAAiBkD,GAAjB,CAAqB7E,EAArB,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAGD;;;AACOyG,QAAAA,aAAa,CAACxG,KAAD,EAA0B;AAC1C,cAAIyG,UAAyB,GAAG,KAAKX,aAAL,EAAhC;AACA,cAAIY,YAAsB,GAAG,EAA7B;;AACA,eAAK,IAAIzE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwE,UAAU,CAACvE,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AACxC,gBAAIwE,UAAU,CAACxE,CAAD,CAAV,CAAcjC,KAAd,IAAuBA,KAA3B,EAAkC;AAC9B0G,cAAAA,YAAY,CAAC1B,IAAb,CAAkByB,UAAU,CAACxE,CAAD,CAAV,CAAclC,EAAhC;AACH;AACJ;;AACD,iBAAO2G,YAAP;AACH;;AAGSC,QAAAA,QAAQ,CAACC,CAAD,EAAiBC,CAAjB,EAAyC;AAEvD,cAAGD,CAAC,CAAC7F,MAAF,CAAS0C,IAAT,GAAgBoD,CAAC,CAAC9F,MAAF,CAAS0C,IAA5B,EAAiC;AAC7B,mBAAO,CAAP;AACH,WAFD,MAEM,IAAGmD,CAAC,CAAC7F,MAAF,CAAS0C,IAAT,IAAiBoD,CAAC,CAAC9F,MAAF,CAAS0C,IAA7B,EAAkC;AACpC,mBAAOmD,CAAC,CAAC5G,KAAF,GAAU6G,CAAC,CAAC7G,KAAnB;AACH,WAFK,MAED;AACD,mBAAO,CAAC,CAAR;AACH;AACJ;AAED;AACJ;AACA;;;AACW8G,QAAAA,cAAc,GAAkB;AACnC,cAAIC,OAAsB,GAAG,KAAKjB,aAAL,GAAqBkB,MAArB,EAA7B;AACAD,UAAAA,OAAO,CAACE,IAAR,CAAa,KAAKN,QAAlB;AACA,cAAIO,IAAmB,GAAG,EAA1B;;AAEA,eAAK,IAAIjF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8E,OAAO,CAAC7E,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,gBAAI8E,OAAO,CAAC9E,CAAD,CAAP,CAAW9B,KAAX,GAAmB,CAAvB,EAA0B;AACtBgC,cAAAA,OAAO,CAACgF,GAAR,CAAY,mBAAZ,EAAgCJ,OAAO,CAAC9E,CAAD,CAAP,CAAW9B,KAA3C,EAAiD4G,OAAO,CAAC9E,CAAD,CAAP,CAAWlB,MAAX,CAAkB2B,IAAnE;AACAwE,cAAAA,IAAI,CAAClC,IAAL,CAAU+B,OAAO,CAAC9E,CAAD,CAAjB;AACA8E,cAAAA,OAAO,CAACK,MAAR,CAAenF,CAAf,EAAkB,CAAlB;AACAA,cAAAA,CAAC;AACJ;AACJ;;AAED,eAAK,IAAIA,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8E,OAAO,CAAC7E,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,gBAAI8E,OAAO,CAAC9E,CAAD,CAAP,CAAW3B,QAAX,GAAsB,CAA1B,EAA6B;AACzByG,cAAAA,OAAO,CAACK,MAAR,CAAenF,CAAf,EAAkB,CAAlB;AACAA,cAAAA,CAAC;AACJ;AACJ;;AAEDiF,UAAAA,IAAI,GAAGA,IAAI,CAACF,MAAL,CAAYD,OAAZ,CAAP;AAEA,iBAAOG,IAAP;AACH;;AAEMG,QAAAA,kBAAkB,CAACrH,KAAa,GAAG,CAAjB,EAAoBD,EAAU,GAAG,CAAjC,EAAmD;AACxE,cAAImH,IAAmB,GAAG,EAA1B;AACA,cAAIH,OAAsB,GAAG,KAAKjB,aAAL,GAAqBkB,MAArB,EAA7B;;AAEA,eAAK,IAAI/E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8E,OAAO,CAAC7E,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,gBAAI8E,OAAO,CAAC9E,CAAD,CAAP,CAAW9B,KAAX,GAAmB,CAAnB,IAAwB4G,OAAO,CAAC9E,CAAD,CAAP,CAAWlC,EAAX,IAAiBA,EAAzC,IAA+CgH,OAAO,CAAC9E,CAAD,CAAP,CAAWjC,KAAX,IAAoBA,KAAnE,IAA4E+G,OAAO,CAAC9E,CAAD,CAAP,CAAW3B,QAAX,GAAsB,CAAtG,EAAyG;AACrG;AACH;;AAED4G,YAAAA,IAAI,CAAClC,IAAL,CAAU+B,OAAO,CAAC9E,CAAD,CAAjB;AACH;;AAED,iBAAOiF,IAAP;AACH;;AA5P6B,O", "sourcesContent": ["\nimport { Sprite<PERSON>rame } from \"cc\";\n\n/**武将(配置)*/\nexport class GeneralConfig {\n    name: string = \"\";\n    cfgId: number = 0;\n    force: number = 0;\n    strategy: number = 0;\n    defense: number = 0;\n    speed: number = 0;\n    destroy: number = 0;\n    cost: number = 0;\n\n    force_grow: number = 0;\n    strategy_grow: number = 0;\n    defense_grow: number = 0;\n    speed_grow: number = 0;\n    destroy_grow: number = 0;\n    physical_power_limit: number = 0;\n    cost_physical_power: number = 0;\n    probability: number = 0;\n\n    star: number = 0;\n    arms: number[] = [];\n    camp: number = 0;\n}\n\n/**武将等级配置*/\nexport class GenaralLevelConfig {\n    level: number = 0;\n    exp: number = 0;\n    soldiers: number = 0;\n}\n\n/**武将阵营枚举*/\nexport class GeneralCampType {\n    static Han: number = 1;\n    static Qun: number = 2;\n    static Wei: number = 3;\n    static Shu: number = 4;\n    static Wu: number = 5;\n}\n\n/**武将共有配置*/\nexport class GeneralCommonConfig {\n    physical_power_limit: number = 100;\n    cost_physical_power: number = 10;\n    recovery_physical_power: number = 10;\n    reclamation_time: number = 3600;\n    draw_general_cost: number = 0;\n}\n\nexport class gSkill {\n    id: number = 0;\n\tlv: number = 0;\n\tcfgId: number = 0;\n}\n\n/**武将数据*/\nexport class GeneralData {\n    id: number = 0;\n    cfgId: number = 0;\n    exp: number = 0;\n    level: number = 0;\n    physical_power: number = 0;\n    order: number = 0;\n    star_lv: number = 0;\n    parentId: number = 0;\n    state: number = 0;\n\n    hasPrPoint: number = 0;\n    usePrPoint: number = 0;\n    force_added: number = 0;\n    strategy_added: number = 0;\n    defense_added: number = 0;\n    speed_added: number = 0;\n    destroy_added: number = 0;\n    config: GeneralConfig = new GeneralConfig();\n    skills: gSkill[] = [];\n\n\n    public static createFromServer(serverData: any, generalData: GeneralData = null, generalCfg: GeneralConfig): GeneralData {\n        let data: GeneralData = generalData;\n        if (data == null) {\n            data = new GeneralData();\n        }\n        data.id = serverData.id;\n        data.cfgId = serverData.cfgId;\n        data.exp = serverData.exp;\n        data.level = serverData.level;\n        data.order = serverData.order;\n        data.physical_power = serverData.physical_power;\n        data.star_lv = serverData.star_lv;\n        data.parentId = serverData.parentId;\n        data.state = serverData.state;\n\n        data.hasPrPoint = serverData.hasPrPoint;\n        data.usePrPoint = serverData.usePrPoint;\n        data.force_added = serverData.force_added;\n        data.strategy_added = serverData.strategy_added;\n        data.defense_added = serverData.defense_added;\n        data.speed_added = serverData.speed_added;\n        data.destroy_added = serverData.destroy_added;\n        data.config = generalCfg;\n        data.skills = serverData.skills;\n\n        // console.log(\"createFromServer:\", data);\n\n        return data;\n    }\n\n    public static getPrValue(pr: number = 0, group:number, add: number = 0): number {\n        return (pr + group + add) / 100;\n    }\n\n    public static getPrStr(pr: number = 0, add: number = 0, lv: number = 0, grow: number = 0): string {\n        return (pr + add) / 100 + \"+(\" + lv * grow / 100 + \")\";\n    }\n}\n\nexport default class GeneralProxy {\n    //武将基础配置数据\n    protected _generalConfigs: Map<number, GeneralConfig> = new Map<number, GeneralConfig>();\n    protected _levelConfigs: GenaralLevelConfig[] = [];\n    protected _commonConfig: GeneralCommonConfig = new GeneralCommonConfig();\n    protected _generalTexs: Map<number, SpriteFrame> = new Map<number, SpriteFrame>();\n    protected _myGenerals: Map<number, GeneralData> = new Map<number, GeneralData>();\n\n    public clearData(): void {\n        this._myGenerals.clear();\n    }\n\n    public initGeneralConfig(cfgs: any[], bCost: any): void {\n        let cfgData: any = null;\n        let levelData: any = null;\n        for (let i: number = 0; i < cfgs.length; i++) {\n            console\n            if (cfgs[i]._name == \"general\") {\n                cfgData = cfgs[i].json.list;\n            } else if (cfgs[i]._name == \"general_basic\") {\n                levelData = cfgs[i].json.levels;\n            }\n        }\n\n        if (cfgData) {\n            this._generalConfigs.clear();\n            for (let i: number = 0; i < cfgData.length; i++) {\n                var cfg: GeneralConfig = new GeneralConfig();\n                cfg.cfgId = cfgData[i].cfgId;\n                cfg.name = cfgData[i].name;\n                cfg.force = cfgData[i].force;\n                cfg.strategy = cfgData[i].strategy;\n                cfg.defense = cfgData[i].defense;\n                cfg.speed = cfgData[i].speed;\n                cfg.destroy = cfgData[i].destroy;\n                cfg.cost = cfgData[i].cost;\n\n                cfg.force_grow = cfgData[i].force_grow;\n                cfg.strategy_grow = cfgData[i].strategy_grow;\n                cfg.defense_grow = cfgData[i].defense_grow;\n                cfg.speed_grow = cfgData[i].speed_grow;\n                cfg.destroy_grow = cfgData[i].destroy_grow;\n                cfg.physical_power_limit = bCost.general.physical_power_limit;\n                cfg.cost_physical_power = bCost.general.cost_physical_power;\n\n                cfg.star = cfgData[i].star;\n                cfg.arms = cfgData[i].arms;\n                cfg.camp = cfgData[i].camp;\n                this._generalConfigs.set(cfg.cfgId, cfg);\n            }\n        }\n\n        if (levelData) {\n            this._levelConfigs.length = levelData.length;\n            for (let i: number = 0; i < levelData.length; i++) {\n                var levelCfg: GenaralLevelConfig = new GenaralLevelConfig();\n                levelCfg.level = levelData[i].level;\n                levelCfg.exp = levelData[i].exp;\n                levelCfg.soldiers = levelData[i].soldiers;\n                this._levelConfigs[levelCfg.level - 1] = levelCfg;\n            }\n        }\n\n        this._commonConfig.physical_power_limit = bCost.general.physical_power_limit;\n        this._commonConfig.cost_physical_power = bCost.general.cost_physical_power;\n        this._commonConfig.recovery_physical_power = bCost.general.recovery_physical_power;\n        this._commonConfig.reclamation_time = bCost.general.reclamation_time;\n        this._commonConfig.draw_general_cost = bCost.general.draw_general_cost;\n    }\n\n    public initGeneralTex(texs: SpriteFrame[]): void {\n        if(texs.length == 0){\n            return;\n        }\n        \n        this._generalTexs.clear();\n        for (let i: number = 0; i < texs.length; i++) {\n            let id: number = Number(String(texs[i].name).split(\"_\")[1]);\n            this._generalTexs.set(id, texs[i]);\n        }\n    }\n\n    public updateMyGenerals(datas: any[]): void {\n        for (var i = 0; i < datas.length; i++) {\n            let data: GeneralData = GeneralData.createFromServer(datas[i], null, this._generalConfigs.get(datas[i].cfgId));\n            this._myGenerals.set(data.id, data);\n        }\n    }\n\n    public updateGeneral(data: any) {\n        if(data.state != 0){\n            this._myGenerals.delete(data.id);\n        }else{\n            let ids: number[] = [];\n            let general: GeneralData = GeneralData.createFromServer(data, this._myGenerals.get(data.id), this._generalConfigs.get(data.cfgId));\n            this._myGenerals.set(general.id, general);\n            ids.push(general.id);\n        }  \n    }\n\n    public removeMyGenerals(ids: number[]) {\n        ids.forEach(id => {\n            this._myGenerals.delete(id);\n        });\n    }\n\n    /**武将配置*/\n    public getGeneralCfg(cfgId: number): GeneralConfig {\n        if (this._generalConfigs.has(cfgId)) {\n            return this._generalConfigs.get(cfgId);\n        }\n        return null;\n    }\n\n    public getGeneralAllCfg(): Map<number, GeneralConfig>{\n        return this._generalConfigs\n    }\n\n    /**武将等级配置*/\n    public getGeneralLevelCfg(level: number): GenaralLevelConfig {\n        if (level > 0 && level <= this._levelConfigs.length) {\n            return this._levelConfigs[level - 1];\n        }\n        return null;\n    }\n\n    public getMaxLevel(): number {\n        return this._levelConfigs.length;\n    }\n\n    /**武将头像素材*/\n    public getGeneralTex(cfgId: number): SpriteFrame {\n        if (this._generalTexs.has(cfgId)) {\n            return this._generalTexs.get(cfgId);\n        }\n        return null;\n    }\n\n    public setGeneralTex(cfgId: number, frame: SpriteFrame) {\n        this._generalTexs.set(cfgId, frame);\n    }\n\n    /**武将相关公有配置*/\n    public getCommonCfg(): GeneralCommonConfig {\n        return this._commonConfig;\n    }\n\n\n    public getMyActiveGeneralCnt() {\n        var arr = this.getMyGenerals()\n        var cnt = 0\n        arr.forEach(g => {\n            if(g.state == 0){\n                cnt += 1\n            }\n        });\n        return cnt\n    }\n\n    /**我的武将列表*/\n    public getMyGenerals(): GeneralData[] {\n        return Array.from(this._myGenerals.values());\n    }\n\n    public getMyGeneralsNotUse(): GeneralData[] {\n        var arr = Array.from(this._myGenerals.values());\n        let generals: GeneralData[] = [];\n        for (let i = 0; i < arr.length; i++) {\n            const obj = arr[i];\n            if (obj.order == 0 && obj.state == 0){\n                generals.push(obj);\n            }\n        }\n        return generals;\n    }\n\n\n    /**我的武将*/\n    public getMyGeneral(id: number): GeneralData {\n        if (this._myGenerals.has(id)) {\n            return this._myGenerals.get(id);\n        }\n        return null;\n    }\n\n\n    /**相同类型的武将id */\n    public getGeneralIds(cfgId: number): number[] {\n        let myGenerals: GeneralData[] = this.getMyGenerals();\n        let tempGenerals: number[] = [];\n        for (var i = 0; i < myGenerals.length; i++) {\n            if (myGenerals[i].cfgId == cfgId) {\n                tempGenerals.push(myGenerals[i].id)\n            }\n        }\n        return tempGenerals;\n    }\n\n\n    protected sortStar(a: GeneralData, b: GeneralData): number {\n\n        if(a.config.star < b.config.star){\n            return 1;\n        }else if(a.config.star == b.config.star){\n            return a.cfgId - b.cfgId;\n        }else{\n            return -1;\n        }\n    }\n\n    /**\n     * 排序 已经使用的\n     */\n    public getUseGenerals(): GeneralData[] {\n        var tempArr: GeneralData[] = this.getMyGenerals().concat();\n        tempArr.sort(this.sortStar);\n        var temp: GeneralData[] = [];\n    \n        for (var i = 0; i < tempArr.length; i++) {\n            if (tempArr[i].order > 0) {\n                console.log(\"tempArr[i].order:\",tempArr[i].order,tempArr[i].config.name)\n                temp.push(tempArr[i]);\n                tempArr.splice(i, 1);\n                i--;\n            }\n        }\n\n        for (var i = 0; i < tempArr.length; i++) {\n            if (tempArr[i].parentId > 0) {\n                tempArr.splice(i, 1);\n                i--;\n            }\n        }\n\n        temp = temp.concat(tempArr);\n\n        return temp;\n    }\n\n    public getComposeGenerals(cfgId: number = 0, id: number = 0): GeneralData[] {\n        var temp: GeneralData[] = [];\n        var tempArr: GeneralData[] = this.getMyGenerals().concat();\n\n        for (var i = 0; i < tempArr.length; i++) {\n            if (tempArr[i].order > 0 || tempArr[i].id == id || tempArr[i].cfgId != cfgId || tempArr[i].parentId > 0) {\n                continue\n            }\n\n            temp.push(tempArr[i]);\n        }\n\n        return temp;\n    }\n}\n"]}