{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts"], "names": ["_decorator", "Component", "EditBox", "<PERSON><PERSON>", "Label", "Color", "LoginCommand", "EventMgr", "LogicEvent", "AudioManager", "ccclass", "property", "LoginFormController", "onLoad", "console", "log", "initializeForm", "setupEventListeners", "start", "on", "loginComplete", "onLoginComplete", "updateStatus", "usernameInput", "error", "passwordInput", "loginButton", "statusLabel", "node", "EventType", "CLICK", "onLoginButtonClick", "onUsernameReturn", "onPasswordReturn", "focus", "instance", "playClick", "isConnecting", "username", "string", "trim", "password", "emit", "showToast", "length", "performLogin", "setButtonEnabled", "getInstance", "accountLogin", "active", "message", "color", "enabled", "interactable", "getComponent", "set", "onDestroy", "targetOff", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AACvDC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAE9B;AACA;AACA;AACA;;qCAEaY,mB,WADZF,OAAO,CAAC,qBAAD,C,UAGHC,QAAQ,CAACT,OAAD,C,UAGRS,QAAQ,CAACT,OAAD,C,UAGRS,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACP,KAAD,C,oCAZb,MACaQ,mBADb,SACyCX,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,gDAcf,KAde;AAAA;;AAgB/CY,QAAAA,MAAM,GAAG;AACLC,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ;AACA,eAAKC,cAAL;AACA,eAAKC,mBAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJJ,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ,EADI,CAEJ;;AACA;AAAA;AAAA,oCAASI,EAAT,CAAY;AAAA;AAAA,wCAAWC,aAAvB,EAAsC,KAAKC,eAA3C,EAA4D,IAA5D;AACH;AAED;AACJ;AACA;;;AACYL,QAAAA,cAAc,GAAS;AAC3B;AACA,eAAKM,YAAL,CAAkB,WAAlB,EAA+B,IAAIjB,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA/B,EAF2B,CAI3B;;AACA,cAAI,CAAC,KAAKkB,aAAV,EAAyB;AACrBT,YAAAA,OAAO,CAACU,KAAR,CAAc,iCAAd;AACH;;AACD,cAAI,CAAC,KAAKC,aAAV,EAAyB;AACrBX,YAAAA,OAAO,CAACU,KAAR,CAAc,gCAAd;AACH;;AACD,cAAI,CAAC,KAAKE,WAAV,EAAuB;AACnBZ,YAAAA,OAAO,CAACU,KAAR,CAAc,+BAAd;AACH;;AACD,cAAI,CAAC,KAAKG,WAAV,EAAuB;AACnBb,YAAAA,OAAO,CAACU,KAAR,CAAc,+BAAd;AACH;AACJ;AAED;AACJ;AACA;;;AACYP,QAAAA,mBAAmB,GAAS;AAChC,cAAI,KAAKS,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBE,IAAjB,CAAsBT,EAAtB,CAAyBhB,MAAM,CAAC0B,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,kBAAtD,EAA0E,IAA1E;AACAjB,YAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACH,WAJ+B,CAMhC;;;AACA,cAAI,KAAKQ,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBK,IAAnB,CAAwBT,EAAxB,CAA2B,gBAA3B,EAA6C,KAAKa,gBAAlD,EAAoE,IAApE;AACH;;AACD,cAAI,KAAKP,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBG,IAAnB,CAAwBT,EAAxB,CAA2B,gBAA3B,EAA6C,KAAKc,gBAAlD,EAAoE,IAApE;AACH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,gBAAgB,GAAS;AAC7BlB,UAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ;;AACA,cAAI,KAAKU,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBS,KAAnB;AACH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,gBAAgB,GAAS;AAC7BnB,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACA,eAAKgB,kBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,kBAAkB,GAAS;AAAA;;AAC/BjB,UAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAD+B,CAG/B;;AACA;AAAA;AAAA,4CAAaoB,QAAb,CAAsBC,SAAtB;;AAEA,cAAI,KAAKC,YAAT,EAAuB;AACnBvB,YAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACA;AACH;;AAED,cAAMuB,QAAQ,GAAG,6BAAKf,aAAL,qGAAoBgB,MAApB,gFAA4BC,IAA5B,OAAsC,EAAvD;AACA,cAAMC,QAAQ,GAAG,6BAAKhB,aAAL,qGAAoBc,MAApB,gFAA4BC,IAA5B,OAAsC,EAAvD,CAZ+B,CAc/B;;AACA,cAAI,CAACF,QAAL,EAAe;AACX,iBAAKhB,YAAL,CAAkB,QAAlB,EAA4B,IAAIjB,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA5B;AACA;AAAA;AAAA,sCAASqC,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,QAApC;AACA;AACH;;AAED,cAAI,CAACF,QAAL,EAAe;AACX,iBAAKnB,YAAL,CAAkB,OAAlB,EAA2B,IAAIjB,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA3B;AACA;AAAA;AAAA,sCAASqC,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,OAApC;AACA;AACH;;AAED,cAAIL,QAAQ,CAACM,MAAT,GAAkB,CAAtB,EAAyB;AACrB,iBAAKtB,YAAL,CAAkB,WAAlB,EAA+B,IAAIjB,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA/B;AACA;AAAA;AAAA,sCAASqC,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,WAApC;AACA;AACH;;AAED,cAAIF,QAAQ,CAACG,MAAT,GAAkB,CAAtB,EAAyB;AACrB,iBAAKtB,YAAL,CAAkB,UAAlB,EAA8B,IAAIjB,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA9B;AACA;AAAA;AAAA,sCAASqC,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,UAApC;AACA;AACH,WArC8B,CAuC/B;;;AACA,eAAKE,YAAL,CAAkBP,QAAlB,EAA4BG,QAA5B;AACH;AAED;AACJ;AACA;;;AACYI,QAAAA,YAAY,CAACP,QAAD,EAAmBG,QAAnB,EAA2C;AAC3D,eAAKJ,YAAL,GAAoB,IAApB;AACA,eAAKf,YAAL,CAAkB,SAAlB,EAA6B,IAAIjB,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA7B;AACA,eAAKyC,gBAAL,CAAsB,KAAtB;AAEAhC,UAAAA,OAAO,CAACC,GAAR,sDAA2CuB,QAA3C,EAL2D,CAO3D;;AACA;AAAA;AAAA,4CAAaS,WAAb,GAA2BC,YAA3B,CAAwCV,QAAxC,EAAkDG,QAAlD;AACH;AAED;AACJ;AACA;;;AACYpB,QAAAA,eAAe,GAAS;AAC5BP,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACA,eAAKO,YAAL,CAAkB,OAAlB,EAA2B,IAAIjB,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA3B;AACA,eAAKgC,YAAL,GAAoB,KAApB;AACA,eAAKS,gBAAL,CAAsB,IAAtB,EAJ4B,CAM5B;;AACA,eAAKlB,IAAL,CAAUqB,MAAV,GAAmB,KAAnB;AACH;AAED;AACJ;AACA;;;AACY3B,QAAAA,YAAY,CAAC4B,OAAD,EAAkBC,KAAlB,EAAsC;AACtD,cAAI,KAAKxB,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBY,MAAjB,GAA0BW,OAA1B;AACA,iBAAKvB,WAAL,CAAiBwB,KAAjB,GAAyBA,KAAzB;AACH;;AACDrC,UAAAA,OAAO,CAACC,GAAR,sDAA2CmC,OAA3C;AACH;AAED;AACJ;AACA;;;AACYJ,QAAAA,gBAAgB,CAACM,OAAD,EAAyB;AAC7C,cAAI,KAAK1B,WAAT,EAAsB;AAAA;;AAClB,iBAAKA,WAAL,CAAiB2B,YAAjB,GAAgCD,OAAhC,CADkB,CAElB;;AACA,gBAAMD,KAAK,GAAGC,OAAO,GAAG,IAAI/C,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAH,GAAmC,IAAIA,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAxD;AACA,0CAAKqB,WAAL,CAAiBE,IAAjB,CAAsB0B,YAAtB,CAAmClD,KAAnC,iFAA2C+C,KAA3C,CAAiDI,GAAjD,CAAqDJ,KAArD;AACH;AACJ;;AAEDK,QAAAA,SAAS,GAAG;AACR;AACA;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;;AAEA,cAAI,KAAK/B,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBE,IAAjB,CAAsB8B,GAAtB,CAA0BvD,MAAM,CAAC0B,SAAP,CAAiBC,KAA3C,EAAkD,KAAKC,kBAAvD,EAA2E,IAA3E;AACH;;AACD,cAAI,KAAKR,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBK,IAAnB,CAAwB8B,GAAxB,CAA4B,gBAA5B,EAA8C,KAAK1B,gBAAnD,EAAqE,IAArE;AACH;;AACD,cAAI,KAAKP,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBG,IAAnB,CAAwB8B,GAAxB,CAA4B,gBAA5B,EAA8C,KAAKzB,gBAAnD,EAAqE,IAArE;AACH;;AACDnB,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ;AACH;;AApM8C,O;;;;;iBAGtB,I;;;;;;;iBAGA,I;;;;;;;iBAGH,I;;;;;;;iBAGD,I", "sourcesContent": ["import { _decorator, Component, Node, EditBox, Button, Label, Color } from 'cc';\nimport LoginCommand from './LoginCommand';\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\nimport { AudioManager } from '../common/AudioManager';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 登录表单控制器\n * 使用预制体创建的登录表单，解决点击偏移问题\n */\n@ccclass('LoginFormController')\nexport class LoginFormController extends Component {\n    \n    @property(EditBox)\n    usernameInput: EditBox = null!;\n    \n    @property(EditBox)\n    passwordInput: EditBox = null!;\n    \n    @property(Button)\n    loginButton: Button = null!;\n    \n    @property(Label)\n    statusLabel: Label = null!;\n    \n    private isConnecting: boolean = false;\n\n    onLoad() {\n        console.log('[LoginFormController] 登录表单控制器加载');\n        this.initializeForm();\n        this.setupEventListeners();\n    }\n\n    start() {\n        console.log('[LoginFormController] 登录表单控制器启动');\n        // 监听登录完成事件\n        EventMgr.on(LogicEvent.loginComplete, this.onLoginComplete, this);\n    }\n\n    /**\n     * 初始化表单\n     */\n    private initializeForm(): void {\n        // 设置默认状态\n        this.updateStatus('请输入用户名和密码', new Color(255, 255, 255, 255));\n        \n        // 验证组件引用\n        if (!this.usernameInput) {\n            console.error('[LoginFormController] 用户名输入框未设置');\n        }\n        if (!this.passwordInput) {\n            console.error('[LoginFormController] 密码输入框未设置');\n        }\n        if (!this.loginButton) {\n            console.error('[LoginFormController] 登录按钮未设置');\n        }\n        if (!this.statusLabel) {\n            console.error('[LoginFormController] 状态标签未设置');\n        }\n    }\n\n    /**\n     * 设置事件监听器\n     */\n    private setupEventListeners(): void {\n        if (this.loginButton) {\n            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);\n            console.log('[LoginFormController] 登录按钮事件监听器已设置');\n        }\n\n        // 输入框回车事件\n        if (this.usernameInput) {\n            this.usernameInput.node.on('editing-return', this.onUsernameReturn, this);\n        }\n        if (this.passwordInput) {\n            this.passwordInput.node.on('editing-return', this.onPasswordReturn, this);\n        }\n    }\n\n    /**\n     * 用户名输入框回车事件\n     */\n    private onUsernameReturn(): void {\n        console.log('[LoginFormController] 用户名输入框回车，焦点转移到密码框');\n        if (this.passwordInput) {\n            this.passwordInput.focus();\n        }\n    }\n\n    /**\n     * 密码输入框回车事件\n     */\n    private onPasswordReturn(): void {\n        console.log('[LoginFormController] 密码输入框回车，执行登录');\n        this.onLoginButtonClick();\n    }\n\n    /**\n     * 登录按钮点击事件\n     */\n    private onLoginButtonClick(): void {\n        console.log('[LoginFormController] 登录按钮被点击');\n\n        // 播放点击音效\n        AudioManager.instance.playClick();\n\n        if (this.isConnecting) {\n            console.log('[LoginFormController] 正在连接中，忽略重复点击');\n            return;\n        }\n\n        const username = this.usernameInput?.string?.trim() || '';\n        const password = this.passwordInput?.string?.trim() || '';\n\n        // 输入验证\n        if (!username) {\n            this.updateStatus('请输入用户名', new Color(255, 100, 100, 255));\n            EventMgr.emit(LogicEvent.showToast, \"请输入用户名\");\n            return;\n        }\n\n        if (!password) {\n            this.updateStatus('请输入密码', new Color(255, 100, 100, 255));\n            EventMgr.emit(LogicEvent.showToast, \"请输入密码\");\n            return;\n        }\n\n        if (username.length < 3) {\n            this.updateStatus('用户名至少3个字符', new Color(255, 100, 100, 255));\n            EventMgr.emit(LogicEvent.showToast, \"用户名至少3个字符\");\n            return;\n        }\n\n        if (password.length < 3) {\n            this.updateStatus('密码至少3个字符', new Color(255, 100, 100, 255));\n            EventMgr.emit(LogicEvent.showToast, \"密码至少3个字符\");\n            return;\n        }\n\n        // 执行登录\n        this.performLogin(username, password);\n    }\n\n    /**\n     * 执行登录\n     */\n    private performLogin(username: string, password: string): void {\n        this.isConnecting = true;\n        this.updateStatus('正在登录...', new Color(100, 200, 255, 255));\n        this.setButtonEnabled(false);\n\n        console.log(`[LoginFormController] 开始登录: ${username}`);\n\n        // 使用现有的登录命令\n        LoginCommand.getInstance().accountLogin(username, password);\n    }\n\n    /**\n     * 登录完成回调\n     */\n    private onLoginComplete(): void {\n        console.log('[LoginFormController] 登录完成');\n        this.updateStatus('登录成功！', new Color(100, 255, 100, 255));\n        this.isConnecting = false;\n        this.setButtonEnabled(true);\n\n        // 隐藏登录表单\n        this.node.active = false;\n    }\n\n    /**\n     * 更新状态显示\n     */\n    private updateStatus(message: string, color: Color): void {\n        if (this.statusLabel) {\n            this.statusLabel.string = message;\n            this.statusLabel.color = color;\n        }\n        console.log(`[LoginFormController] 状态更新: ${message}`);\n    }\n\n    /**\n     * 设置按钮启用状态\n     */\n    private setButtonEnabled(enabled: boolean): void {\n        if (this.loginButton) {\n            this.loginButton.interactable = enabled;\n            // 视觉反馈\n            const color = enabled ? new Color(255, 255, 255, 255) : new Color(128, 128, 128, 255);\n            this.loginButton.node.getComponent(Label)?.color.set(color);\n        }\n    }\n\n    onDestroy() {\n        // 清理事件监听器\n        EventMgr.targetOff(this);\n\n        if (this.loginButton) {\n            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);\n        }\n        if (this.usernameInput) {\n            this.usernameInput.node.off('editing-return', this.onUsernameReturn, this);\n        }\n        if (this.passwordInput) {\n            this.passwordInput.node.off('editing-return', this.onPasswordReturn, this);\n        }\n        console.log('[LoginFormController] 登录表单控制器销毁');\n    }\n}\n"]}