{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts"], "names": ["_decorator", "Component", "Node", "Label", "AudioManager", "LogicEvent", "MapCommand", "EventMgr", "ccclass", "property", "UnionLogic", "onLoad", "visible<PERSON>iew", "on", "openMyUnion", "dismissUnionSuccess", "on<PERSON><PERSON><PERSON>", "closeUnion", "createUnionSuccess", "onDestroy", "targetOff", "onClickClose", "console", "log", "instance", "playClick", "onClickMember", "memberNode", "active", "mainNode", "onClickApply", "applyNode", "onClickLog", "logNode", "openCreate", "createNode", "lobbyNode", "node", "onEnable", "city", "getInstance", "cityProxy", "getMyMainCity", "unionId", "onDisable", "back"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AAC7BC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AAIFC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;;;;;;OAJH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAOTU,U,WADpBF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ,CAACP,IAAD,C,UAERO,QAAQ,CAACP,IAAD,C,UAERO,QAAQ,CAACP,IAAD,C,UAERO,QAAQ,CAACP,IAAD,C,UAERO,QAAQ,CAACP,IAAD,C,UAGRO,QAAQ,CAACP,IAAD,C,UAERO,QAAQ,CAACN,KAAD,C,oCAfb,MACqBO,UADrB,SACwCT,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAgBpCU,QAAAA,MAAM,GAAO;AACnB,eAAKC,WAAL;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,WAAvB,EAAmC,KAAKA,WAAxC,EAAoD,IAApD;AACA;AAAA;AAAA,oCAASD,EAAT,CAAY;AAAA;AAAA,wCAAWE,mBAAvB,EAA2C,KAAKC,SAAhD,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASH,EAAT,CAAY;AAAA;AAAA,wCAAWI,UAAvB,EAAkC,KAAKA,UAAvC,EAAkD,IAAlD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,kBAAvB,EAA0C,KAAKJ,WAA/C,EAA2D,IAA3D;AACH;;AACSK,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACSC,QAAAA,YAAY,GAAS;AAC3BC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,eAAKR,UAAL;AACH;;AACSS,QAAAA,aAAa,GAAS;AAC5B;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,SAAtB;AACA,eAAKE,UAAL,CAAgBC,MAAhB,GAAyB,IAAzB;AACA,eAAKC,QAAL,CAAcD,MAAd,GAAuB,KAAvB;AACH;;AACSE,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaN,QAAb,CAAsBC,SAAtB;AACA,eAAKI,QAAL,CAAcD,MAAd,GAAuB,KAAvB;AACA,eAAKG,SAAL,CAAeH,MAAf,GAAwB,IAAxB;AACH;;AACSI,QAAAA,UAAU,GAAS;AACzB;AAAA;AAAA,4CAAaR,QAAb,CAAsBC,SAAtB;AACA,eAAKI,QAAL,CAAcD,MAAd,GAAuB,KAAvB;AACA,eAAKK,OAAL,CAAaL,MAAb,GAAsB,IAAtB;AACH;;AACSM,QAAAA,UAAU,GAAO;AACvB;AAAA;AAAA,4CAAaV,QAAb,CAAsBC,SAAtB;AACA,eAAKU,UAAL,CAAgBP,MAAhB,GAAyB,IAAzB;AACH;;AACShB,QAAAA,WAAW,GAAO;AACxB,eAAKe,UAAL,CAAgBC,MAAhB,GACA,KAAKO,UAAL,CAAgBP,MAAhB,GACA,KAAKQ,SAAL,CAAeR,MAAf,GACA,KAAKG,SAAL,CAAeH,MAAf,GACA,KAAKD,UAAL,CAAgBC,MAAhB,GACA,KAAKK,OAAL,CAAaL,MAAb,GAAsB,KALtB;AAMH;;AAESX,QAAAA,UAAU,GAAE;AAClB,eAAKoB,IAAL,CAAUT,MAAV,GAAmB,KAAnB;AACH;;AAESd,QAAAA,WAAW,GAAO;AAExB,eAAKF,WAAL;AACA,eAAKiB,QAAL,CAAcD,MAAd,GAAuB,IAAvB;AACH;;AACSU,QAAAA,QAAQ,GAAO;AAErB,cAAIC,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;;AACA,cAAGH,IAAI,CAACI,OAAL,GAAe,CAAlB,EAAoB;AAChB,iBAAK7B,WAAL;AACH,WAFD,MAEK;AACD,iBAAKe,QAAL,CAAcD,MAAd,GAAuB,KAAvB;AACA,iBAAKQ,SAAL,CAAeR,MAAf,GAAwB,IAAxB;AACH;AACJ;;AACSgB,QAAAA,SAAS,GAAO;AACtB,eAAKhC,WAAL;AACH;;AACSiC,QAAAA,IAAI,GAAO;AACjB;AAAA;AAAA,4CAAarB,QAAb,CAAsBC,SAAtB;AACA,eAAKX,WAAL;AACH;;AACSE,QAAAA,SAAS,GAAO;AACtB,eAAKJ,WAAL;AACA,eAAKwB,SAAL,CAAeR,MAAf,GAAwB,IAAxB;AACH;;AAxF6C,O;;;;;iBAErB,I;;;;;;;iBAEF,I;;;;;;;iBAEC,I;;;;;;;iBAEC,I;;;;;;;iBAED,I;;;;;;;iBAGF,I;;;;;;;iBAEC,I", "sourcesContent": ["\nimport { _decorator, Component, Node, Label } from 'cc';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\nconst { ccclass, property } = _decorator;\n\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport { EventMgr } from '../utils/EventMgr';\n\n@ccclass('UnionLogic')\nexport default class UnionLogic extends Component {\n    @property(Node)\n    createNode:Node | null = null;\n    @property(Node)\n    mainNode:Node | null = null;\n    @property(Node)\n    lobbyNode:Node | null = null;\n    @property(Node)\n    memberNode:Node | null = null;\n    @property(Node)\n    applyNode:Node | null = null;\n    \n    @property(Node)\n    logNode:Node | null = null;\n    @property(Label)\n    nameLab:Label | null = null;\n    protected onLoad():void{\n        this.visibleView();\n        EventMgr.on(LogicEvent.openMyUnion,this.openMyUnion,this);\n        EventMgr.on(LogicEvent.dismissUnionSuccess,this.onDismiss,this);\n        EventMgr.on(LogicEvent.closeUnion,this.closeUnion,this);\n        EventMgr.on(LogicEvent.createUnionSuccess,this.openMyUnion,this);\n    }\n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n    protected onClickClose(): void {\n        console.log(\"onClickClose\");\n        AudioManager.instance.playClick();\n        this.closeUnion();\n    }\n    protected onClickMember(): void {\n        AudioManager.instance.playClick();\n        this.memberNode.active = true;\n        this.mainNode.active = false;\n    }\n    protected onClickApply(): void {\n        AudioManager.instance.playClick();\n        this.mainNode.active = false;\n        this.applyNode.active = true;\n    }\n    protected onClickLog(): void {\n        AudioManager.instance.playClick();\n        this.mainNode.active = false;\n        this.logNode.active = true;\n    }\n    protected openCreate():void{\n        AudioManager.instance.playClick();\n        this.createNode.active = true;\n    }\n    protected visibleView():void{\n        this.memberNode.active =\n        this.createNode.active =\n        this.lobbyNode.active =\n        this.applyNode.active =\n        this.memberNode.active =\n        this.logNode.active = false;\n    }\n\n    protected closeUnion(){\n        this.node.active = false;\n    }\n\n    protected openMyUnion():void{\n        \n        this.visibleView();\n        this.mainNode.active = true\n    }\n    protected onEnable():void{\n\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        if(city.unionId > 0){\n            this.openMyUnion();\n        }else{\n            this.mainNode.active = false;\n            this.lobbyNode.active = true;\n        }\n    }\n    protected onDisable():void{\n        this.visibleView();\n    }\n    protected back():void{\n        AudioManager.instance.playClick();\n        this.openMyUnion();\n    }\n    protected onDismiss():void{\n        this.visibleView();\n        this.lobbyNode.active = true\n    }\n}\n\n"]}