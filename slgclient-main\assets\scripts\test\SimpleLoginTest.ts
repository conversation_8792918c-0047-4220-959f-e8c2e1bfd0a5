import { _decorator, Component, Node, Button, Label, EditBox, Canvas, UITransform, Color, Sprite, SpriteFrame, resources, view, screen } from 'cc';
import { SimpleLoginUI } from '../login/SimpleLoginUI';

const { ccclass, property } = _decorator;

/**
 * 简单登录测试组件
 * 用于测试点击响应是否正常
 */
@ccclass('SimpleLoginTest')
export class SimpleLoginTest extends Component {
    
    private testButton: Button = null;
    private testLabel: Label = null;
    private clickCount: number = 0;
    
    protected onLoad(): void {
        console.log('[SimpleLoginTest] 开始创建简单登录测试界面');
        
        // 创建测试UI
        this.createTestUI();
        
        // 打印调试信息
        this.printDebugInfo();
    }
    
    /**
     * 创建测试UI
     */
    private createTestUI(): void {
        // 确保当前节点有Canvas组件
        let canvas = this.getComponent(Canvas);
        if (!canvas) {
            canvas = this.addComponent(Canvas);
        }
        
        // 设置Canvas
        canvas.alignCanvasWithScreen = false;
        this.node.setPosition(0, 0, 0);
        
        // 设置UITransform
        const uiTransform = this.getComponent(UITransform);
        if (uiTransform) {
            const windowSize = screen.windowSize;
            uiTransform.setContentSize(windowSize.width, windowSize.height);
            uiTransform.setAnchorPoint(0.5, 0.5);
            console.log(`[SimpleLoginTest] Canvas尺寸设置为: ${windowSize.width}x${windowSize.height}`);
        }
        
        // 创建背景
        this.createBackground();
        
        // 创建测试按钮
        this.createTestButton();
        
        // 创建状态标签
        this.createStatusLabel();

        // 添加切换到实际登录UI的按钮
        this.addSwitchToLoginUIButton();

        console.log('[SimpleLoginTest] 测试UI创建完成');
    }
    
    /**
     * 创建背景
     */
    private createBackground(): void {
        const bgNode = new Node('Background');
        bgNode.setParent(this.node);
        
        const bgTransform = bgNode.addComponent(UITransform);
        const windowSize = screen.windowSize;
        bgTransform.setContentSize(windowSize.width, windowSize.height);
        bgTransform.setAnchorPoint(0.5, 0.5);
        
        const bgSprite = bgNode.addComponent(Sprite);
        bgSprite.color = new Color(50, 50, 50, 255); // 深灰色背景
        
        bgNode.setPosition(0, 0, 0);
        
        console.log('[SimpleLoginTest] 背景创建完成');
    }
    
    /**
     * 创建测试按钮
     */
    private createTestButton(): void {
        const buttonNode = new Node('TestButton');
        buttonNode.setParent(this.node);
        
        // 设置按钮尺寸和位置
        const buttonTransform = buttonNode.addComponent(UITransform);
        buttonTransform.setContentSize(200, 60);
        buttonTransform.setAnchorPoint(0.5, 0.5);
        buttonNode.setPosition(0, 0, 0); // 屏幕中央
        
        // 添加按钮背景
        const buttonSprite = buttonNode.addComponent(Sprite);
        buttonSprite.color = new Color(0, 150, 255, 255); // 蓝色按钮
        
        // 添加按钮组件
        this.testButton = buttonNode.addComponent(Button);
        this.testButton.transition = Button.Transition.COLOR;
        this.testButton.normalColor = new Color(0, 150, 255, 255);
        this.testButton.pressedColor = new Color(0, 100, 200, 255);
        this.testButton.hoverColor = new Color(50, 180, 255, 255);
        this.testButton.disabledColor = new Color(100, 100, 100, 255);
        
        // 添加按钮文字
        const labelNode = new Node('Label');
        labelNode.setParent(buttonNode);
        
        const labelTransform = labelNode.addComponent(UITransform);
        labelTransform.setContentSize(200, 60);
        labelTransform.setAnchorPoint(0.5, 0.5);
        labelNode.setPosition(0, 0, 0);
        
        const label = labelNode.addComponent(Label);
        label.string = `点击测试 (${this.clickCount})`;
        label.fontSize = 24;
        label.color = new Color(255, 255, 255, 255);
        
        // 绑定点击事件
        this.testButton.node.on(Button.EventType.CLICK, this.onTestButtonClick, this);
        
        console.log('[SimpleLoginTest] 测试按钮创建完成，位置: (0, 0, 0)');
    }
    
    /**
     * 创建状态标签
     */
    private createStatusLabel(): void {
        const labelNode = new Node('StatusLabel');
        labelNode.setParent(this.node);
        
        const labelTransform = labelNode.addComponent(UITransform);
        labelTransform.setContentSize(400, 100);
        labelTransform.setAnchorPoint(0.5, 0.5);
        labelNode.setPosition(0, -150, 0); // 按钮下方
        
        this.testLabel = labelNode.addComponent(Label);
        this.testLabel.string = '等待点击测试...';
        this.testLabel.fontSize = 18;
        this.testLabel.color = new Color(255, 255, 255, 255);
        
        console.log('[SimpleLoginTest] 状态标签创建完成');
    }
    
    /**
     * 测试按钮点击事件
     */
    private onTestButtonClick(): void {
        this.clickCount++;
        
        console.log(`[SimpleLoginTest] 按钮被点击！点击次数: ${this.clickCount}`);
        
        // 更新按钮文字
        const buttonLabel = this.testButton.node.getComponentInChildren(Label);
        if (buttonLabel) {
            buttonLabel.string = `点击测试 (${this.clickCount})`;
        }
        
        // 更新状态标签
        if (this.testLabel) {
            this.testLabel.string = `✅ 点击成功！总计: ${this.clickCount} 次\n点击响应正常，UI适配工作正常`;
            this.testLabel.color = new Color(0, 255, 0, 255); // 绿色表示成功
        }
        
        // 打印详细的点击信息
        this.printClickInfo();
    }
    
    /**
     * 打印点击信息
     */
    private printClickInfo(): void {
        const windowSize = screen.windowSize;
        const visibleSize = view.getVisibleSize();
        const visibleOrigin = view.getVisibleOrigin();
        const designSize = view.getDesignResolutionSize();
        
        console.log('=== 点击测试成功信息 ===');
        console.log(`点击次数: ${this.clickCount}`);
        console.log(`窗口尺寸: ${windowSize.width}x${windowSize.height}`);
        console.log(`设计分辨率: ${designSize.width}x${designSize.height}`);
        console.log(`可视区域: ${visibleSize.width.toFixed(1)}x${visibleSize.height.toFixed(1)}`);
        console.log(`可视原点: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);
        console.log('========================');
    }
    
    /**
     * 打印调试信息
     */
    private printDebugInfo(): void {
        const windowSize = screen.windowSize;
        const visibleSize = view.getVisibleSize();
        const visibleOrigin = view.getVisibleOrigin();
        const designSize = view.getDesignResolutionSize();
        
        console.log('=== 简单登录测试调试信息 ===');
        console.log(`窗口尺寸: ${windowSize.width}x${windowSize.height}`);
        console.log(`设计分辨率: ${designSize.width}x${designSize.height}`);
        console.log(`可视区域: ${visibleSize.width.toFixed(1)}x${visibleSize.height.toFixed(1)}`);
        console.log(`可视原点: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);
        
        // 检查Canvas设置
        const canvas = this.getComponent(Canvas);
        if (canvas) {
            console.log(`Canvas alignCanvasWithScreen: ${canvas.alignCanvasWithScreen}`);
        }
        
        console.log('============================');
    }
    
    /**
     * 添加一个简单的输入框测试
     */
    public addInputTest(): void {
        const inputNode = new Node('TestInput');
        inputNode.setParent(this.node);

        const inputTransform = inputNode.addComponent(UITransform);
        inputTransform.setContentSize(300, 50);
        inputTransform.setAnchorPoint(0.5, 0.5);
        inputNode.setPosition(0, 100, 0); // 按钮上方

        // 添加白色背景
        const inputSprite = inputNode.addComponent(Sprite);
        inputSprite.color = new Color(255, 255, 255, 255);

        // 添加黑色边框
        const borderNode = new Node('Border');
        borderNode.setParent(inputNode);
        const borderTransform = borderNode.addComponent(UITransform);
        borderTransform.setContentSize(304, 54);
        borderTransform.setAnchorPoint(0.5, 0.5);
        borderNode.setPosition(0, 0, -1);
        const borderSprite = borderNode.addComponent(Sprite);
        borderSprite.color = new Color(0, 0, 0, 255);

        const editBox = inputNode.addComponent(EditBox);
        editBox.string = '';
        editBox.placeholder = '点击这里输入测试文字';
        editBox.fontSize = 18;
        editBox.fontColor = new Color(0, 0, 0, 255); // 黑色文字
        editBox.placeholderFontColor = new Color(128, 128, 128, 255); // 灰色占位符

        // 添加输入框标签
        const labelNode = new Node('InputLabel');
        labelNode.setParent(this.node);
        const labelTransform = labelNode.addComponent(UITransform);
        labelTransform.setContentSize(300, 30);
        labelTransform.setAnchorPoint(0.5, 0.5);
        labelNode.setPosition(0, 140, 0);

        const label = labelNode.addComponent(Label);
        label.string = '输入框测试：';
        label.fontSize = 16;
        label.color = new Color(255, 255, 255, 255);

        console.log('[SimpleLoginTest] 输入框测试添加完成');
    }

    /**
     * 添加切换到实际登录UI的按钮
     */
    private addSwitchToLoginUIButton(): void {
        const buttonNode = new Node('SwitchToLoginButton');
        buttonNode.setParent(this.node);

        const buttonTransform = buttonNode.addComponent(UITransform);
        buttonTransform.setContentSize(200, 50);
        buttonTransform.setAnchorPoint(0.5, 0.5);
        buttonNode.setPosition(0, -200, 0);

        // 按钮背景
        const buttonSprite = buttonNode.addComponent(Sprite);
        buttonSprite.color = new Color(0, 200, 0, 255); // 绿色

        // 按钮组件
        const button = buttonNode.addComponent(Button);
        button.transition = Button.Transition.COLOR;
        button.normalColor = new Color(0, 200, 0, 255);
        button.pressedColor = new Color(0, 150, 0, 255);
        button.hoverColor = new Color(50, 220, 50, 255);

        // 按钮文字
        const labelNode = new Node('Label');
        labelNode.setParent(buttonNode);

        const labelTransform = labelNode.addComponent(UITransform);
        labelTransform.setContentSize(200, 50);
        labelTransform.setAnchorPoint(0.5, 0.5);
        labelNode.setPosition(0, 0, 0);

        const label = labelNode.addComponent(Label);
        label.string = '切换到登录界面';
        label.fontSize = 18;
        label.color = new Color(255, 255, 255, 255);

        // 绑定点击事件
        button.node.on(Button.EventType.CLICK, this.createActualLoginUI, this);

        console.log('[SimpleLoginTest] 切换按钮添加完成');
    }

    /**
     * 创建实际的登录UI（替代测试UI）
     */
    public createActualLoginUI(): void {
        console.log('[SimpleLoginTest] 开始创建实际登录UI');

        // 清除当前的测试UI
        this.node.removeAllChildren();

        // 创建登录UI节点
        const loginUINode = new Node('LoginUI');
        loginUINode.setParent(this.node);

        // 添加SimpleLoginUI组件
        const loginUI = loginUINode.addComponent(SimpleLoginUI);

        console.log('[SimpleLoginTest] 实际登录UI创建完成');
    }
}
