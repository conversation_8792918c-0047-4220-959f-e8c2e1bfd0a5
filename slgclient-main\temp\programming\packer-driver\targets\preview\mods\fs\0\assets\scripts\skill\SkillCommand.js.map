{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"], "names": ["SkillCommand", "ServerConfig", "NetManager", "SkillProxy", "EventMgr", "LogicEvent", "getInstance", "_instance", "destory", "onDestory", "constructor", "on", "skill_list", "onSkillList", "skill_push", "onSkillPush", "targetOff", "proxy", "_proxy", "qrySkillList", "sendData", "name", "msg", "send", "data", "console", "log", "code", "updateSkills", "list", "emit", "skillListInfo", "updateGeneral"], "mappings": ";;;0FAOqBA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANZC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;yBAEYL,Y,GAAN,MAAMA,YAAN,CAAmB;AAClC;AAE6B,eAAXM,WAAW,GAAiB;AACtC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AAC5B,iBAAKA,SAAL,GAAiB,IAAIP,YAAJ,EAAjB;AACC;;AACD,iBAAO,KAAKO,SAAZ;AACH;;AACoB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AACpB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACC;;AACD,iBAAO,KAAP;AACH;;AACDG,QAAAA,WAAW,GAAG;AAAA,0CAIiB;AAAA;AAAA,yCAJjB;;AACV;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACH;;AAEMN,QAAAA,SAAS,GAAS;AACrB;AAAA;AAAA,oCAASO,SAAT,CAAmB,IAAnB;AACH;;AACe,YAALC,KAAK,GAAe;AAC3B,iBAAO,KAAKC,MAAZ;AACH;;AACMC,QAAAA,YAAY,GAAS;AACxB,cAAIC,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAaT,UADH;AAEhBU,YAAAA,GAAG,EAAE;AAFW,WAApB;AAIA;AAAA;AAAA,wCAAWhB,WAAX,GAAyBiB,IAAzB,CAA8BH,QAA9B;AACH;;AAESP,QAAAA,WAAW,CAACW,IAAD,EAAkB;AACnCC,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BF,IAA3B;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKT,MAAL,CAAYU,YAAZ,CAAyBJ,IAAI,CAACF,GAAL,CAASO,IAAlC;;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,aAAzB;AACH;AACJ;;AACShB,QAAAA,WAAW,CAACS,IAAD,EAAkB;AACnCC,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BF,IAA3B;;AACA,eAAKN,MAAL,CAAYU,YAAZ,CAAyB,CAACJ,IAAI,CAACF,GAAN,CAAzB;;AACA;AAAA;AAAA,oCAASQ,IAAT,CAAc;AAAA;AAAA,wCAAWE,aAAzB;AACH;;AA/C6B,O;;sBAAbhC,Y", "sourcesContent": ["import { _decorator } from 'cc';\nimport { ServerConfig } from \"../config/ServerConfig\";\nimport { NetManager } from \"../network/socket/NetManager\";\nimport SkillProxy from \"./SkillProxy\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\nexport default class SkillCommand {\n// //单例\n    protected static _instance: SkillCommand;\n    public static getInstance(): SkillCommand {\n        if (this._instance == null) {\n        this._instance = new SkillCommand();\n        }\n        return this._instance;\n    }\n    public static destory(): boolean {\n        if (this._instance) {\n        this._instance.onDestory();\n        this._instance = null;\n        return true;\n        }\n        return false;\n    }\n    constructor() {\n        EventMgr.on(ServerConfig.skill_list, this.onSkillList, this);\n        EventMgr.on(ServerConfig.skill_push, this.onSkillPush, this);\n    }\n    protected _proxy: SkillProxy = new SkillProxy();\n    public onDestory(): void {\n        EventMgr.targetOff(this);\n    }\n    public get proxy(): SkillProxy {\n        return this._proxy;\n    }\n    public qrySkillList(): void {\n        let sendData: any = {\n            name: ServerConfig.skill_list,\n            msg: {}\n        };\n        NetManager.getInstance().send(sendData);\n    }\n    \n    protected onSkillList(data: any): void {\n        console.log(\"onSkillList\", data);\n        if (data.code == 0) {\n            this._proxy.updateSkills(data.msg.list);\n            EventMgr.emit(LogicEvent.skillListInfo);\n        }\n    }\n    protected onSkillPush(data: any): void {\n        console.log(\"onSkillPush\", data);\n        this._proxy.updateSkills([data.msg]);\n        EventMgr.emit(LogicEvent.updateGeneral);\n    }\n}\n"]}