System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, EditBox, Button, Label, Color, LoginCommand, EventMgr, LogicEvent, AudioManager, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _temp, _crd, ccclass, property, LoginFormController;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLoginCommand(extras) {
    _reporterNs.report("LoginCommand", "./LoginCommand", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../utils/EventMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLogicEvent(extras) {
    _reporterNs.report("LogicEvent", "../common/LogicEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAudioManager(extras) {
    _reporterNs.report("AudioManager", "../common/AudioManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      EditBox = _cc.EditBox;
      Button = _cc.Button;
      Label = _cc.Label;
      Color = _cc.Color;
    }, function (_unresolved_2) {
      LoginCommand = _unresolved_2.default;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      LogicEvent = _unresolved_4.LogicEvent;
    }, function (_unresolved_5) {
      AudioManager = _unresolved_5.AudioManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bb52fu/xJRGpZF78HRiZkyT", "LoginFormController", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 登录表单控制器
       * 使用预制体创建的登录表单，解决点击偏移问题
       */

      _export("LoginFormController", LoginFormController = (_dec = ccclass('LoginFormController'), _dec2 = property(EditBox), _dec3 = property(EditBox), _dec4 = property(Button), _dec5 = property(Label), _dec(_class = (_class2 = (_temp = class LoginFormController extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "usernameInput", _descriptor, this);

          _initializerDefineProperty(this, "passwordInput", _descriptor2, this);

          _initializerDefineProperty(this, "loginButton", _descriptor3, this);

          _initializerDefineProperty(this, "statusLabel", _descriptor4, this);

          _defineProperty(this, "isConnecting", false);
        }

        onLoad() {
          console.log('[LoginFormController] 登录表单控制器加载');
          this.initializeForm();
          this.setupEventListeners();
        }

        start() {
          console.log('[LoginFormController] 登录表单控制器启动'); // 监听登录完成事件

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
            error: Error()
          }), LogicEvent) : LogicEvent).loginComplete, this.onLoginComplete, this);
        }
        /**
         * 初始化表单
         */


        initializeForm() {
          // 设置默认状态
          this.updateStatus('请输入用户名和密码', new Color(255, 255, 255, 255)); // 验证组件引用

          if (!this.usernameInput) {
            console.error('[LoginFormController] 用户名输入框未设置');
          }

          if (!this.passwordInput) {
            console.error('[LoginFormController] 密码输入框未设置');
          }

          if (!this.loginButton) {
            console.error('[LoginFormController] 登录按钮未设置');
          }

          if (!this.statusLabel) {
            console.error('[LoginFormController] 状态标签未设置');
          }
        }
        /**
         * 设置事件监听器
         */


        setupEventListeners() {
          if (this.loginButton) {
            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
            console.log('[LoginFormController] 登录按钮事件监听器已设置');
          } // 输入框回车事件


          if (this.usernameInput) {
            this.usernameInput.node.on('editing-return', this.onUsernameReturn, this);
          }

          if (this.passwordInput) {
            this.passwordInput.node.on('editing-return', this.onPasswordReturn, this);
          }
        }
        /**
         * 用户名输入框回车事件
         */


        onUsernameReturn() {
          console.log('[LoginFormController] 用户名输入框回车，焦点转移到密码框');

          if (this.passwordInput) {
            this.passwordInput.focus();
          }
        }
        /**
         * 密码输入框回车事件
         */


        onPasswordReturn() {
          console.log('[LoginFormController] 密码输入框回车，执行登录');
          this.onLoginButtonClick();
        }
        /**
         * 登录按钮点击事件
         */


        onLoginButtonClick() {
          var _this$usernameInput, _this$usernameInput$s, _this$passwordInput, _this$passwordInput$s;

          console.log('[LoginFormController] 登录按钮被点击'); // 播放点击音效

          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick();

          if (this.isConnecting) {
            console.log('[LoginFormController] 正在连接中，忽略重复点击');
            return;
          }

          const username = ((_this$usernameInput = this.usernameInput) === null || _this$usernameInput === void 0 ? void 0 : (_this$usernameInput$s = _this$usernameInput.string) === null || _this$usernameInput$s === void 0 ? void 0 : _this$usernameInput$s.trim()) || '';
          const password = ((_this$passwordInput = this.passwordInput) === null || _this$passwordInput === void 0 ? void 0 : (_this$passwordInput$s = _this$passwordInput.string) === null || _this$passwordInput$s === void 0 ? void 0 : _this$passwordInput$s.trim()) || ''; // 输入验证

          if (!username) {
            this.updateStatus('请输入用户名', new Color(255, 100, 100, 255));
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
              error: Error()
            }), LogicEvent) : LogicEvent).showToast, "请输入用户名");
            return;
          }

          if (!password) {
            this.updateStatus('请输入密码', new Color(255, 100, 100, 255));
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
              error: Error()
            }), LogicEvent) : LogicEvent).showToast, "请输入密码");
            return;
          }

          if (username.length < 3) {
            this.updateStatus('用户名至少3个字符', new Color(255, 100, 100, 255));
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
              error: Error()
            }), LogicEvent) : LogicEvent).showToast, "用户名至少3个字符");
            return;
          }

          if (password.length < 3) {
            this.updateStatus('密码至少3个字符', new Color(255, 100, 100, 255));
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
              error: Error()
            }), LogicEvent) : LogicEvent).showToast, "密码至少3个字符");
            return;
          } // 执行登录


          this.performLogin(username, password);
        }
        /**
         * 执行登录
         */


        performLogin(username, password) {
          this.isConnecting = true;
          this.updateStatus('正在登录...', new Color(100, 200, 255, 255));
          this.setButtonEnabled(false);
          console.log(`[LoginFormController] 开始登录: ${username}`); // 使用现有的登录命令

          (_crd && LoginCommand === void 0 ? (_reportPossibleCrUseOfLoginCommand({
            error: Error()
          }), LoginCommand) : LoginCommand).getInstance().accountLogin(username, password);
        }
        /**
         * 登录完成回调
         */


        onLoginComplete() {
          console.log('[LoginFormController] 登录完成');
          this.updateStatus('登录成功！', new Color(100, 255, 100, 255));
          this.isConnecting = false;
          this.setButtonEnabled(true); // 隐藏登录表单

          this.node.active = false;
        }
        /**
         * 更新状态显示
         */


        updateStatus(message, color) {
          if (this.statusLabel) {
            this.statusLabel.string = message;
            this.statusLabel.color = color;
          }

          console.log(`[LoginFormController] 状态更新: ${message}`);
        }
        /**
         * 设置按钮启用状态
         */


        setButtonEnabled(enabled) {
          if (this.loginButton) {
            var _this$loginButton$nod;

            this.loginButton.interactable = enabled; // 视觉反馈

            const color = enabled ? new Color(255, 255, 255, 255) : new Color(128, 128, 128, 255);
            (_this$loginButton$nod = this.loginButton.node.getComponent(Label)) === null || _this$loginButton$nod === void 0 ? void 0 : _this$loginButton$nod.color.set(color);
          }
        }

        onDestroy() {
          // 清理事件监听器
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);

          if (this.loginButton) {
            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);
          }

          if (this.usernameInput) {
            this.usernameInput.node.off('editing-return', this.onUsernameReturn, this);
          }

          if (this.passwordInput) {
            this.passwordInput.node.off('editing-return', this.onPasswordReturn, this);
          }

          console.log('[LoginFormController] 登录表单控制器销毁');
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "usernameInput", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "passwordInput", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "loginButton", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "statusLabel", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=LoginFormController.js.map