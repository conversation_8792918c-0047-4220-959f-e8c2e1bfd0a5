{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"], "names": ["MapUtil", "size", "v2", "UITransform", "view", "MapCommand", "initMapConfig", "map", "uit", "node", "getComponent", "_mapPixelSize", "width", "height", "_mapOffsetPoint", "anchorX", "anchorY", "_tileSize", "getTileSize", "_mapSize", "getMapSize", "_zeroPixelPoint", "x", "y", "vsize", "getVisibleSize", "showH", "Math", "min", "ceil", "_areaCellSize", "_areaSize", "mapPixcelSize", "mapSize", "mapCellCount", "areaCellSize", "areaSize", "areaCount", "getIdByCellPoint", "getCellPointById", "id", "floor", "getIdByAreaPoint", "getAreaPointById", "get9GridCellIds", "getSideIdsForRoleCity", "getSideIdsForSysCity", "level", "ids", "dis", "tx", "ty", "push", "get9GridAreaIds", "get9GridVaildAreaIds", "list", "totalList", "i", "length", "isVaildAreaId", "getAreaPointByCellPoint", "getAreaIdByCellPoint", "point", "getStartCellPointByAreaPoint", "getEndCellPointByAreaPoint", "getVaildAreaIdsByPixelPoints", "points", "cellPoint", "mapPixelToCellPoint", "areaPoint", "index", "indexOf", "isVaildCellPoint", "isVaildAreaPoint", "worldPixelToMapCellPoint", "mapCellToWorldPixelPoint", "pixelX", "pixelY", "mapCellToPixelPoint", "worldPoint", "subtract", "temp", "clone", "add", "armyIsInView", "buildProxy", "getInstance", "cityProxy", "myId", "getMyPlayerId", "myUnionId", "max", "j", "b", "getBuild", "rid", "unionId", "parentId", "c", "getCity"], "mappings": ";;;uEAGqBA,O;;;;;;;;;;;;;;;AAHYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAcC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,I,OAAAA,I;;AACjEC,MAAAA,U;;;;;;;yBAEcL,O,GAAN,MAAMA,OAAN,CAAc;AACzB;AAEA;AAEA;AAEA;AAEA;AAEA;AAIA;AAC2B,eAAbM,aAAa,CAACC,GAAD,EAAsB;AAC7C,cAAIC,GAAG,GAAGD,GAAG,CAACE,IAAJ,CAASC,YAAT,CAAsBP,WAAtB,CAAV;AAEA,eAAKQ,aAAL,GAAqBV,IAAI,CAACO,GAAG,CAACI,KAAL,EAAYJ,GAAG,CAACK,MAAhB,CAAzB;AAEA,eAAKC,eAAL,GAAuBZ,EAAE,CAACM,GAAG,CAACI,KAAJ,GAAYJ,GAAG,CAACO,OAAjB,EAA0BP,GAAG,CAACK,MAAJ,GAAaL,GAAG,CAACQ,OAA3C,CAAzB;AACA,eAAKC,SAAL,GAAiBV,GAAG,CAACW,WAAJ,EAAjB;AACA,eAAKC,QAAL,GAAgBZ,GAAG,CAACa,UAAJ,EAAhB;AACA,eAAKT,aAAL,GAAqBV,IAAI,CAACO,GAAG,CAACI,KAAL,EAAYJ,GAAG,CAACK,MAAhB,CAAzB;AAEA,eAAKQ,eAAL,CAAqBC,CAArB,GAAyB,KAAKH,QAAL,CAAcP,KAAd,GAAsB,KAAKK,SAAL,CAAeL,KAArC,GAA6C,GAAtE;AACA,eAAKS,eAAL,CAAqBE,CAArB,GAAyB,KAAKJ,QAAL,CAAcN,MAAd,GAAuB,KAAKI,SAAL,CAAeJ,MAAtC,GAA+C,KAAKI,SAAL,CAAeJ,MAAf,GAAwB,GAAhG;AAEA,cAAIW,KAAK,GAAGpB,IAAI,CAACqB,cAAL,EAAZ,CAb6C,CAe7C;;AACA,cAAIC,KAAa,GAAGC,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,IAAL,CAAUL,KAAK,CAACX,MAAN,GAAe,KAAKI,SAAL,CAAeJ,MAA9B,GAAuC,CAAjD,IAAsD,CAAtD,GAA0D,CAAnE,EAAsE,KAAKM,QAAL,CAAcN,MAApF,CAApB;AACA,eAAKiB,aAAL,GAAqB7B,IAAI,CAACyB,KAAD,EAAQA,KAAR,CAAzB;AACA,eAAKK,SAAL,GAAiB9B,IAAI,CAAC0B,IAAI,CAACE,IAAL,CAAU,KAAKV,QAAL,CAAcP,KAAd,GAAsBc,KAAhC,CAAD,EAAyCC,IAAI,CAACE,IAAL,CAAU,KAAKV,QAAL,CAAcN,MAAd,GAAuBa,KAAjC,CAAzC,CAArB;AACH;AAKD;;;AAC+B,mBAAbM,aAAa,GAAS;AACpC,iBAAO,KAAKrB,aAAZ;AACH;;AAEwB,mBAAPsB,OAAO,GAAS;AAC9B,iBAAO,KAAKd,QAAZ;AACH;AAED;;;AAC8B,mBAAZe,YAAY,GAAW;AACrC,iBAAO,KAAKf,QAAL,CAAcP,KAAd,GAAsB,KAAKO,QAAL,CAAcN,MAA3C;AACH;AAED;;;AAC8B,mBAAZsB,YAAY,GAAS;AACnC,iBAAO,KAAKL,aAAZ;AACH;AAED;;;AAC0B,mBAARM,QAAQ,GAAS;AAC/B,iBAAO,KAAKL,SAAZ;AACH;AAED;;;AAC2B,mBAATM,SAAS,GAAW;AAClC,iBAAO,KAAKN,SAAL,CAAenB,KAAf,GAAuB,KAAKmB,SAAL,CAAelB,MAA7C;AACH;AAED;;;AAC8B,eAAhByB,gBAAgB,CAAChB,CAAD,EAAYC,CAAZ,EAA+B;AACzD,iBAAOD,CAAC,GAAGC,CAAC,GAAG,KAAKJ,QAAL,CAAcP,KAA7B;AACH;AAED;;;AAC8B,eAAhB2B,gBAAgB,CAACC,EAAD,EAAmB;AAC7C,iBAAOtC,EAAE,CAACsC,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAApB,EAA2Be,IAAI,CAACc,KAAL,CAAWD,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAA9B,CAA3B,CAAT;AACH;AAED;;;AAC8B,eAAhB8B,gBAAgB,CAACpB,CAAD,EAAYC,CAAZ,EAA+B;AACzD,iBAAOD,CAAC,GAAGC,CAAC,GAAG,KAAKQ,SAAL,CAAenB,KAA9B;AACH;AAED;;;AAC8B,eAAhB+B,gBAAgB,CAACH,EAAD,EAAmB;AAC7C,iBAAOtC,EAAE,CAACsC,EAAE,GAAG,KAAKT,SAAL,CAAenB,KAArB,EAA4Be,IAAI,CAACc,KAAL,CAAWD,EAAE,GAAG,KAAKT,SAAL,CAAenB,KAA/B,CAA5B,CAAT;AACH;AAED;;;AAC6B,eAAfgC,eAAe,CAACJ,EAAD,EAAqB;AAC9C,iBAAO,CACHA,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CADxB,EAC2B4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAD9C,EACqD4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CADhF,EAEH4B,EAAE,GAAG,CAFF,EAEKA,EAFL,EAESA,EAAE,GAAG,CAFd,EAGHA,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CAHxB,EAG2B4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAH9C,EAGqD4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CAHhF,CAAP;AAKH;;AAEkC,eAArBiC,qBAAqB,CAACL,EAAD,EAAqB;AACpD,iBAAO,CACHA,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAA3B,GAA+B,CAD5B,EAC+B4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAA3B,GAA+B,CAD9D,EACiE4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAD5F,EAC+F4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAA3B,GAA+B,CAD9H,EACiI4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAA3B,GAA+B,CADhK,EAEH4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CAFxB,EAE2B4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CAFtD,EAGH4B,EAAE,GAAG,CAHF,EAGKA,EAAE,GAAG,CAHV,EAIHA,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CAJxB,EAI2B4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CAJtD,EAKH4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAA3B,GAA+B,CAL5B,EAK+B4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAA3B,GAA+B,CAL9D,EAKiE4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAnB,GAA2B,CAL5F,EAK+F4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAA3B,GAA+B,CAL9H,EAKiI4B,EAAE,GAAG,KAAKrB,QAAL,CAAcP,KAAd,GAAsB,CAA3B,GAA+B,CALhK,CAAP;AAOH;;AAGiC,eAApBkC,oBAAoB,CAACxB,CAAD,EAAIC,CAAJ,EAAOwB,KAAP,EAAwB;AACtD,cAAIC,GAAa,GAAG,EAApB;AACA,cAAIC,GAAG,GAAG,CAAV;;AACA,cAAGF,KAAK,IAAI,CAAZ,EAAc;AACVE,YAAAA,GAAG,GAAG,CAAN;AACH,WAFD,MAEM,IAAGF,KAAK,IAAI,CAAZ,EAAc;AAChBE,YAAAA,GAAG,GAAG,CAAN;AACH,WAFK,MAEA;AACFA,YAAAA,GAAG,GAAG,CAAN;AACH,WATqD,CAWtD;;;AACA,eAAK,IAAIC,EAAE,GAAG5B,CAAC,GAAC2B,GAAhB,EAAqBC,EAAE,IAAI5B,CAAC,GAAC2B,GAA7B,EAAkCC,EAAE,EAApC,EAAwC;AACpC,gBAAIC,EAAS,GAAG5B,CAAC,GAAG0B,GAApB;AACA,gBAAIT,EAAE,GAAGxC,OAAO,CAACsC,gBAAR,CAAyBY,EAAzB,EAA6BC,EAA7B,CAAT;AACAH,YAAAA,GAAG,CAACI,IAAJ,CAASZ,EAAT;AACH,WAhBqD,CAkBtD;;;AACA,eAAK,IAAIU,EAAE,GAAG5B,CAAC,GAAC2B,GAAhB,EAAqBC,EAAE,IAAI5B,CAAC,GAAC2B,GAA7B,EAAkCC,EAAE,EAApC,EAAwC;AACpC,gBAAIC,EAAS,GAAG5B,CAAC,GAAG0B,GAApB;AACA,gBAAIT,EAAE,GAAGxC,OAAO,CAACsC,gBAAR,CAAyBY,EAAzB,EAA6BC,EAA7B,CAAT;AACAH,YAAAA,GAAG,CAACI,IAAJ,CAASZ,EAAT;AACH,WAvBqD,CA0BtD;;;AACA,eAAK,IAAIW,EAAE,GAAG5B,CAAC,GAAC0B,GAAhB,EAAqBE,EAAE,IAAI5B,CAAC,GAAC0B,GAA7B,EAAkCE,EAAE,EAApC,EAAwC;AACpC,gBAAID,EAAS,GAAG5B,CAAC,GAAG2B,GAApB;AACA,gBAAIT,EAAE,GAAGxC,OAAO,CAACsC,gBAAR,CAAyBY,EAAzB,EAA6BC,EAA7B,CAAT;AACAH,YAAAA,GAAG,CAACI,IAAJ,CAASZ,EAAT;AACH,WA/BqD,CAiCtD;;;AACA,eAAK,IAAIW,EAAE,GAAG5B,CAAC,GAAC0B,GAAhB,EAAqBE,EAAE,IAAI5B,CAAC,GAAC0B,GAA7B,EAAkCE,EAAE,EAApC,EAAwC;AACpC,gBAAID,EAAS,GAAG5B,CAAC,GAAG2B,GAApB;AACA,gBAAIT,EAAE,GAAGxC,OAAO,CAACsC,gBAAR,CAAyBY,EAAzB,EAA6BC,EAA7B,CAAT;AACAH,YAAAA,GAAG,CAACI,IAAJ,CAASZ,EAAT;AACH;;AAED,iBAAOQ,GAAP;AACH;AAED;;;AAC6B,eAAfK,eAAe,CAACb,EAAD,EAAuB;AAChD,iBAAO,CACHA,EAAE,GAAG,KAAKT,SAAL,CAAenB,KAApB,GAA4B,CADzB,EAC4B4B,EAAE,GAAG,KAAKT,SAAL,CAAenB,KADhD,EACuD4B,EAAE,GAAG,KAAKT,SAAL,CAAenB,KAApB,GAA4B,CADnF,EAEH4B,EAAE,GAAG,CAFF,EAEKA,EAFL,EAESA,EAAE,GAAG,CAFd,EAGHA,EAAE,GAAG,KAAKT,SAAL,CAAenB,KAApB,GAA4B,CAHzB,EAG4B4B,EAAE,GAAG,KAAKT,SAAL,CAAenB,KAHhD,EAGuD4B,EAAE,GAAG,KAAKT,SAAL,CAAenB,KAApB,GAA4B,CAHnF,CAAP;AAKH;;AAEiC,eAApB0C,oBAAoB,CAACd,EAAD,EAAuB;AACrD,cAAIe,IAAc,GAAG,EAArB;AACA,cAAIC,SAAmB,GAAG,KAAKH,eAAL,CAAqBb,EAArB,CAA1B;;AACA,eAAK,IAAIiB,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGD,SAAS,CAACE,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,gBAAI,KAAKE,aAAL,CAAmBH,SAAS,CAACC,CAAD,CAA5B,CAAJ,EAAsC;AAClCF,cAAAA,IAAI,CAACH,IAAL,CAAUI,SAAS,CAACC,CAAD,CAAnB;AACH;AACJ;;AACD,iBAAOF,IAAP;AACH;;AAEoC,eAAvBK,uBAAuB,CAACtC,CAAD,EAAYC,CAAZ,EAA6B;AAC9D,iBAAOrB,EAAE,CAACyB,IAAI,CAACc,KAAL,CAAWnB,CAAC,GAAG,KAAKQ,aAAL,CAAmBlB,KAAlC,CAAD,EAA2Ce,IAAI,CAACc,KAAL,CAAWlB,CAAC,GAAG,KAAKO,aAAL,CAAmBjB,MAAlC,CAA3C,CAAT;AACH;AAED;;;AACkC,eAApBgD,oBAAoB,CAACvC,CAAD,EAAYC,CAAZ,EAA+B;AAC7D,cAAIuC,KAAW,GAAG,KAAKF,uBAAL,CAA6BtC,CAA7B,EAAgCC,CAAhC,CAAlB;AACA,iBAAO,KAAKmB,gBAAL,CAAsBoB,KAAK,CAACxC,CAA5B,EAA+BwC,KAAK,CAACvC,CAArC,CAAP;AACH;;AAEyC,eAA5BwC,4BAA4B,CAACzC,CAAD,EAAYC,CAAZ,EAA6B;AACnE,iBAAOrB,EAAE,CAACoB,CAAC,GAAG,KAAKQ,aAAL,CAAmBlB,KAAxB,EAA+BW,CAAC,GAAG,KAAKO,aAAL,CAAmBjB,MAAtD,CAAT;AACH;;AAEuC,eAA1BmD,0BAA0B,CAAC1C,CAAD,EAAYC,CAAZ,EAA6B;AACjE,iBAAOrB,EAAE,CAAC,CAACoB,CAAC,GAAG,CAAL,IAAU,KAAKQ,aAAL,CAAmBlB,KAA9B,EAAqC,CAACW,CAAC,GAAG,CAAL,IAAU,KAAKO,aAAL,CAAmBjB,MAAlE,CAAT;AACH;;AAEyC,eAA5BoD,4BAA4B,CAAC,GAAGC,MAAJ,EAA8B;AACpE,cAAIX,IAAc,GAAG,EAArB;;AACA,eAAK,IAAIE,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGS,MAAM,CAACR,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;AAC5C,gBAAIU,SAAe,GAAG,KAAKC,mBAAL,CAAyBF,MAAM,CAACT,CAAD,CAA/B,CAAtB;AACA,gBAAIY,SAAe,GAAG,KAAKT,uBAAL,CAA6BO,SAAS,CAAC7C,CAAvC,EAA0C6C,SAAS,CAAC5C,CAApD,CAAtB;AACA,gBAAI+C,KAAa,GAAG,KAAK5B,gBAAL,CAAsB2B,SAAS,CAAC/C,CAAhC,EAAmC+C,SAAS,CAAC9C,CAA7C,CAApB;;AACA,gBAAI,KAAKoC,aAAL,CAAmBW,KAAnB,KACGf,IAAI,CAACgB,OAAL,CAAaD,KAAb,KAAuB,CAAC,CAD/B,EACkC;AAC9Bf,cAAAA,IAAI,CAACH,IAAL,CAAUkB,KAAV;AACH;AACJ;;AACD,iBAAOf,IAAP;AACH,SA1MwB,CA4MzB;;;AAC8B,eAAhBiB,gBAAgB,CAACV,KAAD,EAAuB;AACjD,cAAIA,KAAK,CAACxC,CAAN,IAAW,CAAX,IAAgBwC,KAAK,CAACxC,CAAN,GAAU,KAAKH,QAAL,CAAcP,KAAxC,IACGkD,KAAK,CAACvC,CAAN,IAAW,CADd,IACmBuC,KAAK,CAACvC,CAAN,GAAU,KAAKJ,QAAL,CAAcN,MAD/C,EACuD;AACnD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SAnNwB,CAqNzB;;;AAC8B,eAAhB4D,gBAAgB,CAACX,KAAD,EAAuB;AACjD,cAAIA,KAAK,CAACxC,CAAN,IAAW,CAAX,IAAgBwC,KAAK,CAACxC,CAAN,GAAU,KAAKS,SAAL,CAAenB,KAAzC,IACGkD,KAAK,CAACvC,CAAN,IAAW,CADd,IACmBuC,KAAK,CAACvC,CAAN,GAAU,KAAKQ,SAAL,CAAelB,MADhD,EACwD;AACpD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAE0B,eAAb8C,aAAa,CAACnB,EAAD,EAAa;AACpC,cAAIA,EAAE,IAAI,CAAN,IAAWA,EAAE,GAAG,KAAKH,SAAzB,EAAoC;AAChC,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SAnOwB,CAqOzB;;;AACsC,eAAxBqC,wBAAwB,CAACZ,KAAD,EAAoB;AACtD;AACA;AACA;AACA,cAAIxC,CAAS,GAAGK,IAAI,CAACc,KAAL,CAAW,MAAM,KAAKtB,QAAL,CAAcN,MAApB,GAA6BiD,KAAK,CAACxC,CAAN,GAAU,KAAKL,SAAL,CAAeL,KAAtD,GAA8DkD,KAAK,CAACvC,CAAN,GAAU,KAAKN,SAAL,CAAeJ,MAAlG,CAAhB;AACA,cAAIU,CAAS,GAAGI,IAAI,CAACc,KAAL,CAAW,MAAM,KAAKtB,QAAL,CAAcP,KAApB,GAA4BkD,KAAK,CAACxC,CAAN,GAAU,KAAKL,SAAL,CAAeL,KAArD,GAA6DkD,KAAK,CAACvC,CAAN,GAAU,KAAKN,SAAL,CAAeJ,MAAjG,CAAhB;AACA,iBAAOX,EAAE,CAACoB,CAAD,EAAIC,CAAJ,CAAT;AACH,SA7OwB,CA+OzB;;;AACsC,eAAxBoD,wBAAwB,CAACb,KAAD,EAAoB;AACtD,cAAIc,MAAc,GAAG,KAAKvD,eAAL,CAAqBC,CAArB,GAAyB,CAACwC,KAAK,CAACvC,CAAN,GAAUuC,KAAK,CAACxC,CAAjB,IAAsB,KAAKL,SAAL,CAAeL,KAArC,GAA6C,GAA3F;AACA,cAAIiE,MAAc,GAAG,KAAKxD,eAAL,CAAqBE,CAArB,GAAyB,CAACuC,KAAK,CAACxC,CAAN,GAAUwC,KAAK,CAACvC,CAAjB,IAAsB,KAAKN,SAAL,CAAeJ,MAArC,GAA8C,GAA5F;AACA,iBAAOX,EAAE,CAAC0E,MAAD,EAASC,MAAT,CAAT;AACH,SApPwB,CAsPzB;;;AACiC,eAAnBC,mBAAmB,CAAChB,KAAD,EAAoB;AACjD,cAAIiB,UAAgB,GAAG,KAAKJ,wBAAL,CAA8Bb,KAA9B,CAAvB;AACA,iBAAOiB,UAAU,CAACC,QAAX,CAAoB,KAAKlE,eAAzB,CAAP;AACH,SA1PwB,CA4PzB;;;AACiC,eAAnBsD,mBAAmB,CAACN,KAAD,EAAoB;AACjD,cAAImB,IAAI,GAAGnB,KAAK,CAACoB,KAAN,EAAX;AACA,cAAIH,UAAgB,GAAGE,IAAI,CAACE,GAAL,CAAS,KAAKrE,eAAd,CAAvB;AACA,iBAAO,KAAK4D,wBAAL,CAA8BK,UAA9B,CAAP;AACH;;AAEyB,eAAZK,YAAY,CAAC9D,CAAD,EAAWC,CAAX,EAA8B;AACpD,cAAI8D,UAAU,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBD,UAA1C;AACA,cAAIE,SAAS,GAAG;AAAA;AAAA,wCAAWD,WAAX,GAAyBC,SAAzC;AAEA,cAAIC,IAAI,GAAGD,SAAS,CAACE,aAAV,EAAX;AACA,cAAIC,SAAS,GAAGH,SAAS,CAACG,SAA1B,CALoD,CAMpD;AAEA;;AACA,eAAK,IAAIjC,CAAC,GAAG9B,IAAI,CAACgE,GAAL,CAAS,CAAT,EAAYrE,CAAC,GAAC,CAAd,CAAb,EAA+BmC,CAAC,IAAG9B,IAAI,CAACC,GAAL,CAASN,CAAC,GAAC,CAAX,EAAc,KAAKH,QAAL,CAAcP,KAA5B,CAAnC,EAAuE6C,CAAC,EAAxE,EAA4E;AACxE,iBAAK,IAAImC,CAAC,GAAGjE,IAAI,CAACgE,GAAL,CAAS,CAAT,EAAYpE,CAAC,GAAC,CAAd,CAAb,EAA+BqE,CAAC,IAAGjE,IAAI,CAACC,GAAL,CAASL,CAAC,GAAC,CAAX,EAAc,KAAKJ,QAAL,CAAcN,MAA5B,CAAnC,EAAwE+E,CAAC,EAAzE,EAA6E;AACzE,kBAAIpD,EAAU,GAAGxC,OAAO,CAACsC,gBAAR,CAAyBmB,CAAzB,EAA4BmC,CAA5B,CAAjB;AACA,kBAAIC,CAAC,GAAGR,UAAU,CAACS,QAAX,CAAoBtD,EAApB,CAAR;;AACA,kBAAI,CAACqD,CAAL,EAAO;AACH;AACH;;AAED,kBAAGA,CAAC,CAACE,GAAF,IAASP,IAAT,IAAkBE,SAAS,IAAI,CAAb,KAAmBG,CAAC,CAACG,OAAF,IAAaN,SAAb,IAA0BG,CAAC,CAACI,QAAF,IAAcP,SAA3D,CAArB,EAA4F;AACxF,uBAAO,IAAP;AACH;;AAED,kBAAIQ,CAAC,GAAGX,SAAS,CAACY,OAAV,CAAkB3D,EAAlB,CAAR;;AACA,kBAAI,CAAC0D,CAAL,EAAO;AACH;AACH;;AAED,kBAAGA,CAAC,CAACH,GAAF,IAASP,IAAT,IAAkBE,SAAS,IAAI,CAAb,KAAmBG,CAAC,CAACG,OAAF,IAAaN,SAAb,IAA0BG,CAAC,CAACI,QAAF,IAAcP,SAA3D,CAArB,EAA4F;AACxF,uBAAO,IAAP;AACH;AACJ;AACJ;;AAED,iBAAO,KAAP;AACH;;AApSwB,O;;sBAAR1F,O,mBAEsB,I;;sBAFtBA,O,qBAIwB,I;;sBAJxBA,O,eAMkBC,IAAI,CAAC,GAAD,EAAM,GAAN,C;;sBANtBD,O,cAQiBC,IAAI,CAAC,EAAD,EAAK,EAAL,C;;sBARrBD,O,qBAUwBE,EAAE,CAAC,CAAD,EAAI,CAAJ,C;;sBAV1BF,O,mBAYsB,I;;sBAZtBA,O,eAakB,I", "sourcesContent": ["import { _decorator, Size, Vec2, size, v2, TiledMap, UITransform, game, view } from 'cc';\nimport MapCommand from \"./MapCommand\";\n\nexport default class MapUtil {\n    //地图像素大小\n    protected static _mapPixelSize: Size = null;\n    //地图锚点偏移量\n    protected static _mapOffsetPoint: Vec2 = null;\n    //格子大小\n    protected static _tileSize: Size = size(256, 128);;\n    //地图大小 宽高需要相同\n    protected static _mapSize: Size = size(20, 20);\n    //地图 (0, 0)点对应的像素坐标\n    protected static _zeroPixelPoint: Vec2 = v2(0, 0);\n    //划分区域的格子大小\n    protected static _areaCellSize: Size = null;\n    protected static _areaSize: Size = null;\n\n    // 初始化地图配置\n    public static initMapConfig(map: TiledMap): void {\n        var uit = map.node.getComponent(UITransform);\n\n        this._mapPixelSize = size(uit.width, uit.height);\n   \n        this._mapOffsetPoint = v2(uit.width * uit.anchorX, uit.height * uit.anchorY);\n        this._tileSize = map.getTileSize();\n        this._mapSize = map.getMapSize();\n        this._mapPixelSize = size(uit.width, uit.height);\n\n        this._zeroPixelPoint.x = this._mapSize.width * this._tileSize.width * 0.5;\n        this._zeroPixelPoint.y = this._mapSize.height * this._tileSize.height - this._tileSize.height * 0.5;\n\n        var vsize = view.getVisibleSize();\n\n        //划分区域的大小\n        let showH: number = Math.min(Math.ceil(vsize.height / this._tileSize.height / 2) * 2 + 2, this._mapSize.height);\n        this._areaCellSize = size(showH, showH);\n        this._areaSize = size(Math.ceil(this._mapSize.width / showH), Math.ceil(this._mapSize.height / showH));\n    }\n\n\n\n\n    /**地图的像素大小*/\n    public static get mapPixcelSize(): Size {\n        return this._mapPixelSize;\n    }\n\n    public static get mapSize(): Size {\n        return this._mapSize;\n    }\n\n    /**格子数量*/\n    public static get mapCellCount(): number {\n        return this._mapSize.width * this._mapSize.height;\n    }\n\n    /**每个区域包含的格子数量*/\n    public static get areaCellSize(): Size {\n        return this._areaCellSize;\n    }\n\n    /**区域大小*/\n    public static get areaSize(): Size {\n        return this._areaSize;\n    }\n\n    /**区域数量*/\n    public static get areaCount(): number {\n        return this._areaSize.width * this._areaSize.height;\n    }\n\n    /**获取格子id*/\n    public static getIdByCellPoint(x: number, y: number): number {\n        return x + y * this._mapSize.width;\n    }\n\n    /**获取格子坐标*/\n    public static getCellPointById(id: number): Vec2 {\n        return v2(id % this._mapSize.width, Math.floor(id / this._mapSize.width));\n    }\n\n    /**获取区域id*/\n    public static getIdByAreaPoint(x: number, y: number): number {\n        return x + y * this._areaSize.width;\n    }\n\n    /**获取区域坐标*/\n    public static getAreaPointById(id: number): Vec2 {\n        return v2(id % this._areaSize.width, Math.floor(id / this._areaSize.width));\n    }\n\n    /**获取格子为中点的九宫格id列表*/\n    public static get9GridCellIds(id:number):number[] {\n        return [\n            id + this._mapSize.width - 1, id + this._mapSize.width, id + this._mapSize.width + 1,\n            id - 1, id, id + 1,\n            id - this._mapSize.width - 1, id - this._mapSize.width, id - this._mapSize.width + 1\n        ];\n    }\n\n    public static getSideIdsForRoleCity(id:number):number[] {\n        return [\n            id + this._mapSize.width * 2 - 2, id + this._mapSize.width * 2 - 1, id + this._mapSize.width * 2, id + this._mapSize.width * 2 + 1, id + this._mapSize.width * 2 + 2,\n            id + this._mapSize.width - 2, id + this._mapSize.width + 2,\n            id - 2, id + 2,\n            id - this._mapSize.width - 2, id - this._mapSize.width + 2,\n            id - this._mapSize.width * 2 - 2, id - this._mapSize.width * 2 - 1, id - this._mapSize.width - 1, id - this._mapSize.width * 2 + 1, id - this._mapSize.width * 2 + 2\n        ];\n    }\n\n\n    public static getSideIdsForSysCity(x, y, level): number[] {\n        let ids: number[] = [];\n        var dis = 0;\n        if(level >= 8){\n            dis = 3;\n        }else if(level >= 5){\n            dis = 2;\n        }else {\n            dis = 1;\n        }\n\n        //上\n        for (let tx = x-dis; tx <= x+dis; tx++) {\n            var ty:number = y + dis;\n            var id = MapUtil.getIdByCellPoint(tx, ty);\n            ids.push(id);\n        }\n\n        //下\n        for (let tx = x-dis; tx <= x+dis; tx++) {\n            var ty:number = y - dis;\n            var id = MapUtil.getIdByCellPoint(tx, ty);\n            ids.push(id);\n        }\n\n\n        //左\n        for (let ty = y-dis; ty <= y+dis; ty++) {\n            var tx:number = x - dis;\n            var id = MapUtil.getIdByCellPoint(tx, ty);\n            ids.push(id);\n        }\n\n        //右\n        for (let ty = y-dis; ty <= y+dis; ty++) {\n            var tx:number = x + dis;\n            var id = MapUtil.getIdByCellPoint(tx, ty);\n            ids.push(id);\n        }\n\n        return ids;\n    }\n\n    /**获取区域为中点的九宫格id列表*/\n    public static get9GridAreaIds(id: number): number[] {\n        return [\n            id + this._areaSize.width - 1, id + this._areaSize.width, id + this._areaSize.width + 1,\n            id - 1, id, id + 1,\n            id - this._areaSize.width - 1, id - this._areaSize.width, id - this._areaSize.width + 1\n        ];\n    }\n\n    public static get9GridVaildAreaIds(id: number): number[] {\n        let list: number[] = [];\n        let totalList: number[] = this.get9GridAreaIds(id);\n        for (let i: number = 0; i < totalList.length; i++) {\n            if (this.isVaildAreaId(totalList[i])) {\n                list.push(totalList[i]);\n            }\n        }\n        return list;\n    }\n\n    public static getAreaPointByCellPoint(x: number, y: number): Vec2 {\n        return v2(Math.floor(x / this._areaCellSize.width), Math.floor(y / this._areaCellSize.height));\n    }\n\n    /**获取区域id*/\n    public static getAreaIdByCellPoint(x: number, y: number): number {\n        let point: Vec2 = this.getAreaPointByCellPoint(x, y);\n        return this.getIdByAreaPoint(point.x, point.y);\n    }\n\n    public static getStartCellPointByAreaPoint(x: number, y: number): Vec2 {\n        return v2(x * this._areaCellSize.width, y * this._areaCellSize.height);\n    }\n\n    public static getEndCellPointByAreaPoint(x: number, y: number): Vec2 {\n        return v2((x + 1) * this._areaCellSize.width, (y + 1) * this._areaCellSize.height);\n    }\n\n    public static getVaildAreaIdsByPixelPoints(...points: Vec2[]): number[] {\n        let list: number[] = [];\n        for (let i: number = 0; i < points.length; i++) {\n            let cellPoint: Vec2 = this.mapPixelToCellPoint(points[i]);\n            let areaPoint: Vec2 = this.getAreaPointByCellPoint(cellPoint.x, cellPoint.y);\n            let index: number = this.getIdByAreaPoint(areaPoint.x, areaPoint.y);\n            if (this.isVaildAreaId(index)\n                && list.indexOf(index) == -1) {\n                list.push(index);\n            }\n        }\n        return list;\n    }\n\n    //是否是有效的格子\n    public static isVaildCellPoint(point: Vec2): boolean {\n        if (point.x >= 0 && point.x < this._mapSize.width\n            && point.y >= 0 && point.y < this._mapSize.height) {\n            return true;\n        }\n        return false;\n    }\n\n    //是否是有效的格子\n    public static isVaildAreaPoint(point: Vec2): boolean {\n        if (point.x >= 0 && point.x < this._areaSize.width\n            && point.y >= 0 && point.y < this._areaSize.height) {\n            return true;\n        }\n        return false;\n    }\n\n    public static isVaildAreaId(id: number) {\n        if (id >= 0 && id < this.areaCount) {\n            return true;\n        }\n        return false;\n    }\n\n    // 世界像素坐标转地图坐标\n    public static worldPixelToMapCellPoint(point: Vec2): Vec2 {\n        //  转换原理 \n        //  tiledMap 45度地图是已上方为(0,0)点 以左上方边界为y轴 右上方边界为x轴的坐标系\n        //  所以只需要将点击坐标点的平行映射到地图坐标系的边界上 求解出映射点的像素坐标 / 格子大小 即可计算出对饮的 格子坐标\n        let x: number = Math.floor(0.5 * this._mapSize.height + point.x / this._tileSize.width - point.y / this._tileSize.height);\n        let y: number = Math.floor(1.5 * this._mapSize.width - point.x / this._tileSize.width - point.y / this._tileSize.height);\n        return v2(x, y);\n    }\n\n    //地图坐标(格子的中心点)转世界像素坐标\n    public static mapCellToWorldPixelPoint(point: Vec2): Vec2 {\n        let pixelX: number = this._zeroPixelPoint.x - (point.y - point.x) * this._tileSize.width * 0.5;\n        let pixelY: number = this._zeroPixelPoint.y - (point.x + point.y) * this._tileSize.height * 0.5;\n        return v2(pixelX, pixelY);\n    }\n\n    // 地图坐标转地图像素坐标\n    public static mapCellToPixelPoint(point: Vec2): Vec2 {\n        let worldPoint: Vec2 = this.mapCellToWorldPixelPoint(point);\n        return worldPoint.subtract(this._mapOffsetPoint);\n    }\n\n    //地图像素转地图坐标\n    public static mapPixelToCellPoint(point: Vec2): Vec2 {\n        let temp = point.clone();\n        let worldPoint: Vec2 = temp.add(this._mapOffsetPoint);\n        return this.worldPixelToMapCellPoint(worldPoint);\n    }\n\n    public static armyIsInView(x:number, y:number): boolean {\n        let buildProxy = MapCommand.getInstance().buildProxy;\n        let cityProxy = MapCommand.getInstance().cityProxy;\n        \n        let myId = cityProxy.getMyPlayerId();\n        let myUnionId = cityProxy.myUnionId;\n        // let parentId = cityProxy.myParentId;\n\n        //可视觉区域以当前为原点，半径为5\n        for (let i = Math.max(0, x-5); i<= Math.min(x+5, this._mapSize.width); i++) {\n            for (let j = Math.max(0, y-5); j<= Math.min(y+5, this._mapSize.height); j++) {\n                let id: number = MapUtil.getIdByCellPoint(i, j);\n                var b = buildProxy.getBuild(id);\n                if (!b){\n                    continue\n                }\n\n                if(b.rid == myId || (myUnionId != 0 && (b.unionId == myUnionId || b.parentId == myUnionId))){\n                    return true;\n                }\n\n                var c = cityProxy.getCity(id)\n                if (!c){\n                    continue\n                }\n\n                if(c.rid == myId || (myUnionId != 0 && (b.unionId == myUnionId || b.parentId == myUnionId))){\n                    return true;\n                }\n            }\n        }\n\n        return false\n    }\n}\n"]}