{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts"], "names": ["GeneralItemType", "_decorator", "Component", "Label", "Sprite", "Layout", "Node", "color", "GeneralCommand", "GeneralCampType", "GeneralHeadLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "GeneralItemLogic", "onLoad", "delNode", "active", "_isSelect", "setData", "curData", "type", "position", "updateItem", "updateView", "_type", "_curData", "undefined", "_position", "cfgData", "getInstance", "proxy", "getGeneralCfg", "cfgId", "name<PERSON><PERSON><PERSON>", "string", "name", "lvLabel", "level", "spritePic", "getComponent", "setHeadId", "showStar", "star", "star_lv", "camp", "Han", "camp<PERSON>abel", "<PERSON><PERSON>", "<PERSON>", "Shu", "<PERSON>", "arm<PERSON>abel", "armstr", "arms", "useNode", "GeneralInfo", "order", "costLabel", "cost", "select", "str", "indexOf", "flag", "selectNode", "childen", "starLayout", "node", "children", "i", "length", "setOtherData", "cityData", "orderId", "_cityData", "_orderId", "onClickGeneral", "event", "instance", "playClick", "config", "console", "log", "emit", "openGeneralDes", "GeneralDispose", "chosedGeneral", "GeneralConScript", "openArmyConscript", "GeneralSelect", "openGeneralSelect", "onDelete", "setWarReportData"], "mappings": ";;;gLAYaA,e;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AAGtDC,MAAAA,c;;AACEC,MAAAA,e,iBAAAA,e;;AACFC,MAAAA,gB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAPH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;AAS9B;iCACaD,e,GAAN,MAAMA,eAAN,CAAsB,E;;sBAAhBA,e,iBACoB,C;;sBADpBA,e,oBAEuB,C;;sBAFvBA,e,sBAGyB,C;;sBAHzBA,e,oBAIuB,C;;sBAJvBA,e,mBAKsB,C;;yBAKdgB,gB,WADpBF,OAAO,CAAC,kBAAD,C,UAGHC,QAAQ,CAACZ,KAAD,C,UAGRY,QAAQ,CAACZ,KAAD,C,UAGRY,QAAQ,CAACX,MAAD,C,UAGRW,QAAQ,CAACZ,KAAD,C,UAGRY,QAAQ,CAACZ,KAAD,C,UAGRY,QAAQ,CAACZ,KAAD,C,UAGRY,QAAQ,CAACV,MAAD,C,UAGRU,QAAQ,CAACT,IAAD,C,WAGRS,QAAQ,CAACT,IAAD,C,WAIRS,QAAQ,CAACT,IAAD,C,oCA/Bb,MACqBU,gBADrB,SAC8Cd,SAD9C,CACwD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,4CAiC7B,IAjC6B;;AAAA,yCAkC7B,CAAC,CAlC4B;;AAAA,6CAmCzB,CAnCyB;;AAAA,6CAoC5B,IApC4B;;AAAA,4CAqC1B,CArC0B;;AAAA,6CAsCxB,KAtCwB;AAAA;;AAwC1Ce,QAAAA,MAAM,GAAO;AACnB,eAAKC,OAAL,CAAaC,MAAb,GAAsB,KAAtB;AACA,eAAKC,SAAL,GAAiB,KAAjB;AACH;;AAGMC,QAAAA,OAAO,CAACC,OAAD,EAAqBC,IAAW,GAAG,CAAnC,EAAqCC,QAAe,GAAG,CAAvD,EAA8D;AACxE,eAAKC,UAAL,CAAgBH,OAAhB;AACH;;AAIMG,QAAAA,UAAU,CAACH,OAAD,EAAkB;AAC/B,eAAKI,UAAL,CAAgBJ,OAAhB;AACA,eAAKK,KAAL,GAAa,KAAKC,QAAL,CAAcL,IAAd,IAAsBM,SAAtB,GAAgC,CAAC,CAAjC,GAAmC,KAAKD,QAAL,CAAcL,IAA9D;AACA,eAAKO,SAAL,GAAiB,KAAKF,QAAL,CAAcJ,QAAd,IAA0BK,SAA1B,GAAoC,CAApC,GAAsC,KAAKD,QAAL,CAAcJ,QAArE;AACH;;AAGSE,QAAAA,UAAU,CAACJ,OAAD,EAAkB;AAClC,eAAKM,QAAL,GAAgBN,OAAhB;AAEA,cAAIS,OAAO,GAAG;AAAA;AAAA,gDAAeC,WAAf,GAA6BC,KAA7B,CAAmCC,aAAnC,CAAiD,KAAKN,QAAL,CAAcO,KAA/D,CAAd;AACA,eAAKC,SAAL,CAAeC,MAAf,GAAwBN,OAAO,CAACO,IAAhC;AACA,eAAKC,OAAL,CAAaF,MAAb,GAAsB,SAAU,KAAKT,QAAL,CAAcY,KAA9C;AACA,eAAKC,SAAL,CAAeC,YAAf;AAAA;AAAA,oDAA8CC,SAA9C,CAAwD,KAAKf,QAAL,CAAcO,KAAtE;AACA,eAAKS,QAAL,CAAcb,OAAO,CAACc,IAAtB,EAA2B,KAAKjB,QAAL,CAAckB,OAAzC;AACA,eAAK5B,OAAL,CAAaC,MAAb,GAAsB,KAAtB;;AAEA,cAAGY,OAAO,CAACgB,IAAR,IAAgB;AAAA;AAAA,kDAAgBC,GAAnC,EAAuC;AACnC,iBAAKC,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH,WAFD,MAEM,IAAGN,OAAO,CAACgB,IAAR,IAAgB;AAAA;AAAA,kDAAgBG,GAAnC,EAAuC;AACzC,iBAAKD,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH,WAFK,MAEA,IAAGN,OAAO,CAACgB,IAAR,IAAgB;AAAA;AAAA,kDAAgBI,GAAnC,EAAuC;AACzC,iBAAKF,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH,WAFK,MAEA,IAAGN,OAAO,CAACgB,IAAR,IAAgB;AAAA;AAAA,kDAAgBK,GAAnC,EAAuC;AACzC,iBAAKH,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH,WAFK,MAEA,IAAGN,OAAO,CAACgB,IAAR,IAAgB;AAAA;AAAA,kDAAgBM,EAAnC,EAAsC;AACxC,iBAAKJ,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH;;AAED,eAAKiB,QAAL,CAAcjB,MAAd,GAAuB,KAAKkB,MAAL,CAAYxB,OAAO,CAACyB,IAApB,CAAvB;;AAEA,cAAG,KAAKC,OAAR,EAAgB;AACZ,gBAAG,KAAK9B,KAAL,IAAc3B,eAAe,CAAC0D,WAA9B,IAA6C,KAAK9B,QAAL,CAAc+B,KAAd,GAAsB,CAAtE,EAAwE;AACpE,mBAAKF,OAAL,CAAatC,MAAb,GAAsB,IAAtB;AACH,aAFD,MAEK;AACD,mBAAKsC,OAAL,CAAatC,MAAb,GAAsB,KAAtB;AACH;AACJ;;AAED,cAAG,KAAKyC,SAAR,EAAkB;AACd,iBAAKA,SAAL,CAAevB,MAAf,GAAwBN,OAAO,CAAC8B,IAAR,GAAe,EAAvC;AACH;;AACD,eAAKC,MAAL,CAAY,KAAZ;AACH;;AAESP,QAAAA,MAAM,CAACC,IAAD,EAAwB;AACpC;AAEA,cAAIO,GAAG,GAAG,EAAV;;AACA,cAAGP,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AAC9DD,YAAAA,GAAG,IAAI,GAAP;AACH,WAFD,MAEM,IAAGP,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AACpED,YAAAA,GAAG,IAAI,GAAP;AACH,WAFK,MAEA,IAAGP,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AACpED,YAAAA,GAAG,IAAI,GAAP;AACH;;AACD,iBAAOA,GAAP;AACH;;AAGMD,QAAAA,MAAM,CAACG,IAAD,EAAmB;AAC5B,cAAG,KAAKC,UAAR,EAAmB;AACf,iBAAKA,UAAL,CAAgB/C,MAAhB,GAAyB8C,IAAzB;AACH;;AACD,eAAK7C,SAAL,GAAiB6C,IAAjB;AACH;;AAGSrB,QAAAA,QAAQ,CAACC,IAAW,GAAG,CAAf,EAAiBC,OAAc,GAAG,CAAlC,EAAyC;AACvD,cAAIqB,OAAO,GAAG,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBC,QAAnC;;AACA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAcA,CAAC,GAACJ,OAAO,CAACK,MAAxB,EAA+BD,CAAC,EAAhC,EAAmC;AAC/B,gBAAGA,CAAC,GAAG1B,IAAP,EAAY;AACRsB,cAAAA,OAAO,CAACI,CAAD,CAAP,CAAWpD,MAAX,GAAoB,IAApB;;AACA,kBAAGoD,CAAC,GAAGzB,OAAP,EAAe;AACXqB,gBAAAA,OAAO,CAACI,CAAD,CAAP,CAAW7B,YAAX,CAAwBtC,MAAxB,EAAgCG,KAAhC,GAAwCA,KAAK,CAAC,GAAD,EAAK,CAAL,EAAO,CAAP,CAA7C;AACH,eAFD,MAEK;AACD4D,gBAAAA,OAAO,CAACI,CAAD,CAAP,CAAW7B,YAAX,CAAwBtC,MAAxB,EAAgCG,KAAhC,GAAwCA,KAAK,CAAC,GAAD,EAAK,GAAL,EAAS,GAAT,CAA7C;AACH;AACJ,aAPD,MAOK;AACD4D,cAAAA,OAAO,CAACI,CAAD,CAAP,CAAWpD,MAAX,GAAoB,KAApB;AACH;AACJ;AACJ;;AAESsD,QAAAA,YAAY,CAACC,QAAD,EAAcC,OAAc,GAAG,CAA/B,EAAsC;AACxD,eAAKC,SAAL,GAAiBF,QAAjB;AACA,eAAKG,QAAL,GAAgBF,OAAhB;AACA,eAAKzD,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACH;;AAGS2D,QAAAA,cAAc,CAACC,KAAD,EAAkB;AACtC;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKrD,QAAR,EAAiB;AACb,gBAAIG,OAAO,GAAG,KAAKH,QAAL,CAAcsD,MAA5B;AACAC,YAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B,KAAKzD,KAApC,EAFa,CAIb;;AACC,gBAAG,KAAKA,KAAL,IAAc3B,eAAe,CAAC0D,WAAjC,EAA6C;AACzC;AAAA;AAAA,wCAAS2B,IAAT,CAAc;AAAA;AAAA,4CAAWC,cAAzB,EAAyCvD,OAAzC,EAAkD,KAAKH,QAAvD;AACH,aAFD,CAIA;AAJA,iBAKK,IAAG,KAAKD,KAAL,IAAc3B,eAAe,CAACuF,cAAjC,EAAgD;AACjD;AAAA;AAAA,0CAASF,IAAT,CAAc;AAAA;AAAA,8CAAWG,aAAzB,EAAwCzD,OAAxC,EAAiD,KAAKH,QAAtD,EAAgE,KAAKE,SAArE;AACH,eAFI,CAIL;AAJK,mBAKA,IAAG,KAAKH,KAAL,IAAc3B,eAAe,CAACyF,gBAAjC,EAAkD;AACnD;AAAA;AAAA,4CAASJ,IAAT,CAAc;AAAA;AAAA,gDAAWK,iBAAzB,EAA4C,KAAKb,QAAjD,EAA2D,KAAKD,SAAhE;AACH,iBAFI,MAIA,IAAG,KAAKjD,KAAL,IAAc3B,eAAe,CAAC2F,aAAjC,EAA+C;AACjD,uBAAKvE,SAAL,GAAiB,CAAC,KAAKA,SAAvB;AACA,uBAAK0C,MAAL,CAAY,KAAK1C,SAAjB;AACA;AAAA;AAAA,4CAASiE,IAAT,CAAc;AAAA;AAAA,gDAAWO,iBAAzB,EAA4C7D,OAA5C,EAAqD,KAAKH,QAA1D,EAAoE,KAAKyC,IAAzE;AACF;AACL;AAGJ;AAKD;AACJ;AACA;;;AACcwB,QAAAA,QAAQ,GAAO;AACrB,cAAI9D,OAAO,GAAG,KAAKH,QAAL,CAAcsD,MAA5B;AACA;AAAA;AAAA,oCAASG,IAAT,CAAc;AAAA;AAAA,wCAAWG,aAAzB,EAAuCzD,OAAvC,EAA+C,KAAKH,QAApD,EAA6D,CAAC,CAA9D;AACH;AAKD;AACJ;AACA;AACA;;;AACWkE,QAAAA,gBAAgB,CAACxE,OAAD,EAAkB;AACrC,eAAKI,UAAL,CAAgBJ,OAAhB;AACH;;AAlMmD,O;;;;;iBAGjC,I;;;;;;;iBAGF,I;;;;;;;iBAGE,I;;;;;;;iBAGA,I;;;;;;;iBAGA,I;;;;;;;iBAGD,I;;;;;;;iBAGE,I;;;;;;;iBAGL,I;;;;;;;iBAGA,I;;;;;;;iBAIG,I", "sourcesContent": ["\nimport { _decorator, Component, Label, Sprite, Layout, Node, color } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport { GeneralCampType, GeneralData } from \"../../general/GeneralProxy\";\nimport GeneralHeadLogic from \"./GeneralHeadLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n// /**军队命令*/\nexport class GeneralItemType {\n    static GeneralInfo: number = 0;//武将详情\n    static GeneralDispose: number = 1;//武将上阵\n    static GeneralConScript: number = 2;//武将征兵\n    static GeneralNoThing: number = 3;//无用\n    static GeneralSelect: number = 4;//选择\n}\n\n\n@ccclass('GeneralItemLogic')\nexport default class GeneralItemLogic extends Component {\n\n    @property(Label)\n    nameLabel: Label = null;\n\n    @property(Label)\n    lvLabel: Label = null;\n\n    @property(Sprite)\n    spritePic:Sprite = null;\n\n    @property(Label)\n    costLabel: Label = null;\n\n    @property(Label)\n    campLabel: Label = null;\n\n    @property(Label)\n    armLabel: Label = null;\n    \n    @property(Layout)\n    starLayout:Layout = null;\n\n    @property(Node)\n    delNode:Node = null;\n\n    @property(Node)\n    useNode:Node = null;\n\n\n    @property(Node)\n    selectNode:Node = null;\n\n    private _curData:any = null;\n    private _type:number = -1;\n    private _position:number = 0;\n    private _cityData:any = null;\n    private _orderId:number = 1;\n    private _isSelect:boolean = false;\n\n    protected onLoad():void{\n        this.delNode.active = false;\n        this._isSelect = false;\n    }\n\n\n    public setData(curData:GeneralData,type:number = 0,position:number = 0):void{\n        this.updateItem(curData);\n    }\n\n\n\n    public updateItem(curData:any):void{\n        this.updateView(curData);\n        this._type = this._curData.type == undefined?-1:this._curData.type;\n        this._position = this._curData.position == undefined?0:this._curData.position;\n    }\n\n\n    protected updateView(curData:any):void{\n        this._curData = curData;\n\n        var cfgData = GeneralCommand.getInstance().proxy.getGeneralCfg(this._curData.cfgId);\n        this.nameLabel.string = cfgData.name \n        this.lvLabel.string = \" Lv.\" +  this._curData.level ;\n        this.spritePic.getComponent(GeneralHeadLogic).setHeadId(this._curData.cfgId);\n        this.showStar(cfgData.star,this._curData.star_lv);\n        this.delNode.active = false;\n\n        if(cfgData.camp == GeneralCampType.Han){\n            this.campLabel.string = \"汉\";\n        }else if(cfgData.camp == GeneralCampType.Qun){\n            this.campLabel.string = \"群\";\n        }else if(cfgData.camp == GeneralCampType.Wei){\n            this.campLabel.string = \"魏\";\n        }else if(cfgData.camp == GeneralCampType.Shu){\n            this.campLabel.string = \"蜀\";\n        }else if(cfgData.camp == GeneralCampType.Wu){\n            this.campLabel.string = \"吴\";\n        }\n        \n        this.armLabel.string = this.armstr(cfgData.arms);\n\n        if(this.useNode){\n            if(this._type == GeneralItemType.GeneralInfo && this._curData.order > 0){\n                this.useNode.active = true;\n            }else{\n                this.useNode.active = false; \n            }\n        }\n\n        if(this.costLabel){\n            this.costLabel.string = cfgData.cost + \"\";\n        }\n        this.select(false);\n    }\n\n    protected armstr(arms:number []): string{\n        // console.log(\"armstr:\", arms);\n\n        var str = \"\"\n        if(arms.indexOf(1)>=0 || arms.indexOf(4)>=0 || arms.indexOf(7)>=0){\n            str += \"步\"\n        }else if(arms.indexOf(2)>=0 || arms.indexOf(5)>=0 || arms.indexOf(8)>=0){\n            str += \"弓\"\n        }else if(arms.indexOf(3)>=0 || arms.indexOf(6)>=0 || arms.indexOf(9)>=0){\n            str += \"骑\"\n        }\n        return str;\n    }\n\n\n    public select(flag:boolean):void{\n        if(this.selectNode){\n            this.selectNode.active = flag;\n        }\n        this._isSelect = flag;\n    }\n\n\n    protected showStar(star:number = 3,star_lv:number = 0):void{\n        var childen = this.starLayout.node.children;\n        for(var i = 0;i<childen.length;i++){\n            if(i < star){\n                childen[i].active = true;\n                if(i < star_lv){\n                    childen[i].getComponent(Sprite).color = color(255,0,0);\n                }else{\n                    childen[i].getComponent(Sprite).color = color(255,255,255);\n                }\n            }else{\n                childen[i].active = false; \n            }\n        }\n    }\n\n    protected setOtherData(cityData:any,orderId:number = 1):void{\n        this._cityData = cityData;\n        this._orderId = orderId\n        this.delNode.active = true;\n    }\n\n\n    protected onClickGeneral(event:any): void {\n        AudioManager.instance.playClick();\n        if(this._curData){\n            var cfgData = this._curData.config;\n            console.log(\"onClickGeneral:\", this._type);\n            \n            //武将详情\n             if(this._type == GeneralItemType.GeneralInfo){\n                 EventMgr.emit(LogicEvent.openGeneralDes, cfgData, this._curData);\n             }\n             \n             //上阵\n             else if(this._type == GeneralItemType.GeneralDispose){\n                 EventMgr.emit(LogicEvent.chosedGeneral, cfgData, this._curData, this._position);\n             }\n\n             //征兵\n             else if(this._type == GeneralItemType.GeneralConScript){\n                 EventMgr.emit(LogicEvent.openArmyConscript, this._orderId, this._cityData);\n             }\n\n             else if(this._type == GeneralItemType.GeneralSelect){\n                this._isSelect = !this._isSelect;\n                this.select(this._isSelect);\n                EventMgr.emit(LogicEvent.openGeneralSelect, cfgData, this._curData, this.node);\n             }\n        }\n\n        \n    }\n\n\n\n\n    /**\n     * 下阵\n     */\n    protected onDelete():void{\n        var cfgData = this._curData.config;\n        EventMgr.emit(LogicEvent.chosedGeneral,cfgData,this._curData,-1);\n    }\n\n\n\n\n    /**\n     * 战报的\n     * @param curData \n     */\n    public setWarReportData(curData:any):void{\n        this.updateView(curData)\n    }\n\n}\n"]}