{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts"], "names": ["_decorator", "Component", "Prefab", "Node", "instantiate", "TiledMapAsset", "JsonAsset", "SpriteFrame", "sys", "AudioSource", "assert", "resources", "GameConfig", "LoaderManager", "LoadData", "LoadDataType", "ArmyCommand", "GeneralCommand", "LoginCommand", "MapCommand", "MapUICommand", "HttpManager", "NetEvent", "NetManager", "NetNodeType", "SkillCommand", "Toast", "Tools", "EventMgr", "AudioManager", "LogicEvent", "FixedScreenAdapter", "ClickTestHelper", "SimpleLoginTest", "ccclass", "property", "Main", "onLoad", "console", "log", "node", "getComponent", "addComponent", "scheduleOnce", "instance", "init", "screen", "enableDebugInfo", "audioSource", "_audioSource", "on", "enterMap", "onEnterMap", "enterLogin", "showToast", "onShowToast", "showWaiting", "showWaitNode", "hideWaiting", "hideWaitNode", "ServerRequesting", "ServerRequestSucess", "onServerRequest", "getInstance", "connect", "url", "serverUrl", "type", "BaseServer", "setWebUrl", "webUrl", "onDestroy", "targetOff", "clearData", "clearAllScene", "loginScenePrefab", "_loginScene", "parent", "error", "createSimpleLoginTest", "testNode", "setParent", "testComponent", "addInputTest", "dataList", "push", "FILE", "DIR", "<PERSON><PERSON><PERSON><PERSON>", "addLoadingNode", "startLoadList", "paths", "datas", "proxy", "tiledMapAsset", "initMapResConfig", "json", "setAllFacilityCfg", "initGeneralConfig", "initGeneralTex", "setBasic", "initSkillConfig", "d", "set<PERSON><PERSON><PERSON><PERSON>", "war_free", "cityId", "cityProxy", "getMyMainCity", "qryMyGenerals", "qryArmyList", "qryWarReport", "qrySkillList", "_mapScene", "mapScenePrefab", "_mapUIScene", "mapUIScenePrefab", "h5LoadGeneralTex", "_h5GeneralPic", "length", "generalpic", "getDirWithPath", "for<PERSON>ach", "v", "ctor", "f", "index", "pic", "name", "path", "replaceAll", "id", "Number", "String", "split", "frame", "getGeneralTex", "_h5GeneralPicIndex", "load", "finish", "total", "asset", "message", "setGeneralTex", "unschedule", "schedule", "loadingPrefab", "_loadingNode", "setSiblingIndex", "topLayer", "_waitNode", "waitPrefab", "active", "showTopToast", "text", "toastNode", "toast", "toastPrefab", "setText", "msg", "code", "undefined", "_retryTimes", "role_enterServer", "getSession", "getCodeStr", "destroy", "children"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAGnHC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,a;AAAiBC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Y,iBAAAA,Y;;AAC3BC,MAAAA,W;;AACAC,MAAAA,c;;AACAC,MAAAA,Y;;AACAC,MAAAA,U;;AACAC,MAAAA,Y;;AACEC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,W,kBAAAA,W;;AACFC,MAAAA,Y;;AACAC,MAAAA,K;;AACEC,MAAAA,K,kBAAAA,K;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,kB,kBAAAA,kB;;AACAC,MAAAA,e,kBAAAA,e;;AACAC,MAAAA,e,kBAAAA,e;;;;;;;OArBH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnC,U;;yBAwBToC,I,WADpBF,OAAO,CAAC,MAAD,C,UAEHC,QAAQ,CAACjC,MAAD,C,UAGRiC,QAAQ,CAACjC,MAAD,C,UAERiC,QAAQ,CAACjC,MAAD,C,UAGRiC,QAAQ,CAACjC,MAAD,C,UAIRiC,QAAQ,CAACjC,MAAD,C,UAGRiC,QAAQ,CAACjC,MAAD,C,oCAjBb,MACqBkC,IADrB,SACkCnC,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,gDAmBJ,IAnBI;;AAAA,6CAoBd,IApBc;;AAAA,+CAqBV,IArBU;;AAAA,6CAsBZ,IAtBY;;AAAA,+CAuBV,IAvBU;;AAAA,gDAwBT,IAxBS;;AAAA,6CAyBZ,IAzBY;;AAAA,+CA0BV,CA1BU;;AAAA,sDA2BH,CA3BG;;AAAA,iDA4BhB,EA5BgB;AAAA;;AA+B9BoC,QAAAA,MAAM,GAAS;AAErBC,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAFqB,CAIrB;;AACA,cAAI,CAAC,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,uDAAL,EAAiD;AAC7C,iBAAKD,IAAL,CAAUE,YAAV;AAAA;AAAA;AACH,WAPoB,CASrB;;;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB;AACA;AAAA;AAAA,0DAAmBC,QAAnB,CAA4BC,IAA5B,GAFoB,CAIpB;;AACA,gBAAI;AAAA;AAAA,0CAAWC,MAAX,CAAkBC,eAAlB,IAAqC,CAAC,KAAKP,IAAL,CAAUC,YAAV;AAAA;AAAA,mDAA1C,EAAmF;AAC/E,mBAAKD,IAAL,CAAUE,YAAV;AAAA;AAAA;AACH;AACJ,WARD,EAQG,GARH;AAUA,cAAMM,WAAW,GAAG,KAAKP,YAAL,CAAkBhC,WAAlB,CAApB;AACAC,UAAAA,MAAM,CAACsC,WAAD,CAAN;AACA,eAAKC,YAAL,GAAoBD,WAApB;AAEA;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,IAAtB,CAA2B,KAAKI,YAAhC;AAEA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,QAAvB,EAAiC,KAAKC,UAAtC,EAAkD,IAAlD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,UAAvB,EAAmC,KAAKA,UAAxC,EAAoD,IAApD;AACA;AAAA;AAAA,oCAASH,EAAT,CAAY;AAAA;AAAA,wCAAWI,SAAvB,EAAkC,KAAKC,WAAvC,EAAoD,IAApD;AACA;AAAA;AAAA,oCAASL,EAAT,CAAY;AAAA;AAAA,wCAAWM,WAAvB,EAAoC,KAAKC,YAAzC,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASP,EAAT,CAAY;AAAA;AAAA,wCAAWQ,WAAvB,EAAoC,KAAKC,YAAzC,EAAuD,IAAvD;AAEA;AAAA;AAAA,oCAAST,EAAT,CAAY;AAAA;AAAA,oCAASU,gBAArB,EAAuC,KAAKH,YAA5C,EAAyD,IAAzD;AACA;AAAA;AAAA,oCAASP,EAAT,CAAY;AAAA;AAAA,oCAASW,mBAArB,EAAyC,KAAKC,eAA9C,EAA8D,IAA9D,EAjCqB,CAoCrB;;AACA;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,OAAzB,CAAiC;AAAEC,YAAAA,GAAG,EAAE;AAAA;AAAA,0CAAWC,SAAlB;AAA8BC,YAAAA,IAAI,EAAC;AAAA;AAAA,4CAAYC;AAA/C,WAAjC;AACA;AAAA;AAAA,0CAAYL,WAAZ,GAA0BM,SAA1B,CAAoC;AAAA;AAAA,wCAAWC,MAA/C,EAtCqB,CAwCrB;;AACA;AAAA;AAAA,4CAAaP,WAAb;AACA;AAAA;AAAA,wCAAWA,WAAX;AACA;AAAA;AAAA,4CAAaA,WAAb;AACA;AAAA;AAAA,gDAAeA,WAAf;AACA;AAAA;AAAA,0CAAYA,WAAZ;AAEA,eAAKV,UAAL;AAEH;;AAESkB,QAAAA,SAAS,GAAS;AACxBjC,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AAEA;AAAA;AAAA,oCAASiC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,wCAAWV,WAAX,GAAyBU,SAAzB;AACA;AAAA;AAAA,gDAAeV,WAAf,GAA6BU,SAA7B;AACA;AAAA;AAAA,0CAAYV,WAAZ,GAA0BU,SAA1B;AACH;;AAEOpB,QAAAA,UAAU,GAAS;AACvB,eAAKqB,aAAL;AACA,eAAKD,SAAL,GAFuB,CAIvB;;AACA,cAAI,KAAKE,gBAAT,EAA2B;AACvB,iBAAKC,WAAL,GAAmBxE,WAAW,CAAC,KAAKuE,gBAAN,CAA9B;AACA,iBAAKC,WAAL,CAAiBC,MAAjB,GAA0B,KAAKrC,IAA/B;AACAF,YAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AACH,WAJD,MAIO;AACHD,YAAAA,OAAO,CAACwC,KAAR,CAAc,qCAAd,EADG,CAEH;;AACA,iBAAKC,qBAAL;AACH,WAbsB,CAevB;;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,qBAAqB,GAAS;AAClCzC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AAEA,cAAMyC,QAAQ,GAAG,IAAI7E,IAAJ,CAAS,iBAAT,CAAjB;AACA6E,UAAAA,QAAQ,CAACC,SAAT,CAAmB,KAAKzC,IAAxB,EAJkC,CAMlC;;AACA,cAAM0C,aAAa,GAAGF,QAAQ,CAACtC,YAAT;AAAA;AAAA,iDAAtB,CAPkC,CASlC;;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpBuC,YAAAA,aAAa,CAACC,YAAd;AACH,WAFD,EAEG,GAFH;AAIA,eAAKP,WAAL,GAAmBI,QAAnB,CAdkC,CAcL;;AAE7B1C,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ;AACH;;AAESa,QAAAA,UAAU,GAAS;AACzB,cAAIgC,QAAoB,GAAG,EAA3B;AACAA,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,aAAb,EAA4B;AAAA;AAAA,4CAAaC,IAAzC,EAA+CjF,aAA/C,CAAd;AACA+E,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,mBAAb,EAAkC;AAAA;AAAA,4CAAaC,IAA/C,EAAqDhF,SAArD,CAAd;AACA8E,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,yBAAb,EAAwC;AAAA;AAAA,4CAAaE,GAArD,EAA0DjF,SAA1D,CAAd;AACA8E,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,wBAAb,EAAuC;AAAA;AAAA,4CAAaE,GAApD,EAAyDjF,SAAzD,CAAd;;AAEA,cAAGE,GAAG,CAACgF,SAAP,EAAiB;AACbJ,YAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,sCAAa,eAAb,EAA8B;AAAA;AAAA,8CAAaE,GAA3C,EAAgDhF,WAAhD,CAAd;AACH,WAFD,MAEK;AACD6E,YAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,sCAAa,cAAb,EAA6B;AAAA;AAAA,8CAAaE,GAA1C,EAA+ChF,WAA/C,CAAd;AACH;;AAED6E,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,gBAAb,EAA+B;AAAA;AAAA,4CAAaC,IAA5C,EAAkDhF,SAAlD,CAAd;AACA8E,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,sBAAb,EAAqC;AAAA;AAAA,4CAAaE,GAAlD,EAAuDjF,SAAvD,CAAd;AAEA,eAAKmF,cAAL;AACAnD,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACA;AAAA;AAAA,8CAAcwB,WAAd,GAA4B2B,aAA5B,CAA0CN,QAA1C,EAAoD,IAApD,EACI,CAACN,KAAD,EAAea,KAAf,EAAgCC,KAAhC,KAAiD;AAC7C,gBAAId,KAAK,IAAI,IAAb,EAAmB;AACfxC,cAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA;AACH;;AACDD,YAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BoD,KAA5B,EAAmCC,KAAnC;AACA;AAAA;AAAA,0CAAW7B,WAAX,GAAyB8B,KAAzB,CAA+BC,aAA/B,GAA+CF,KAAK,CAAC,CAAD,CAApD;AACA;AAAA;AAAA,0CAAW7B,WAAX,GAAyB8B,KAAzB,CAA+BE,gBAA/B,CAAiDH,KAAK,CAAC,CAAD,CAAN,CAAwBI,IAAxE;AAEA;AAAA;AAAA,8CAAajC,WAAb,GAA2B8B,KAA3B,CAAiCI,iBAAjC,CAAmDL,KAAK,CAAC,CAAD,CAAxD;AACA;AAAA;AAAA,kDAAe7B,WAAf,GAA6B8B,KAA7B,CAAmCK,iBAAnC,CAAqDN,KAAK,CAAC,CAAD,CAA1D,EAA+DA,KAAK,CAAC,CAAD,CAAN,CAAwBI,IAAtF;AACA;AAAA;AAAA,kDAAejC,WAAf,GAA6B8B,KAA7B,CAAmCM,cAAnC,CAAkDP,KAAK,CAAC,CAAD,CAAvD;AACA;AAAA;AAAA,8CAAa7B,WAAb,GAA2B8B,KAA3B,CAAiCO,QAAjC,CAA0CR,KAAK,CAAC,CAAD,CAA/C;AACA;AAAA;AAAA,8CAAa7B,WAAb,GAA2B8B,KAA3B,CAAiCQ,eAAjC,CAAiDT,KAAK,CAAC,CAAD,CAAtD;AAEA,gBAAIU,CAAC,GAAIV,KAAK,CAAC,CAAD,CAAN,CAAwBI,IAAhC;AACA;AAAA;AAAA,0CAAWjC,WAAX,GAAyB8B,KAAzB,CAA+BU,UAA/B,CAA0CD,CAAC,CAAC,OAAD,CAAD,CAAWE,QAArD;AAEA,gBAAIC,MAAc,GAAG;AAAA;AAAA,0CAAW1C,WAAX,GAAyB2C,SAAzB,CAAmCC,aAAnC,GAAmDF,MAAxE;AACA;AAAA;AAAA,kDAAe1C,WAAf,GAA6B6C,aAA7B;AACA;AAAA;AAAA,4CAAY7C,WAAZ,GAA0B8C,WAA1B,CAAsCJ,MAAtC;AACA;AAAA;AAAA,8CAAa1C,WAAb,GAA2B+C,YAA3B;AACA;AAAA;AAAA,8CAAa/C,WAAb,GAA2BgD,YAA3B;AAEA,iBAAKrC,aAAL;AAGA,iBAAKsC,SAAL,GAAiB5G,WAAW,CAAC,KAAK6G,cAAN,CAA5B;AACA,iBAAKD,SAAL,CAAenC,MAAf,GAAwB,KAAKrC,IAA7B;AAEA,iBAAK0E,WAAL,GAAmB9G,WAAW,CAAC,KAAK+G,gBAAN,CAA9B;AACA,iBAAKD,WAAL,CAAiBrC,MAAjB,GAA0B,KAAKrC,IAA/B;AAEA,iBAAKiD,cAAL;AAEH,WApCL,EAqCI,IArCJ;AAwCH;;AAEO2B,QAAAA,gBAAgB,GAAG;AAAA;;AACvB,cAAG,CAAC5G,GAAG,CAACgF,SAAR,EAAkB;AACd;AACH;;AAED,cAAG,KAAK6B,aAAL,CAAmBC,MAAnB,IAA6B,CAAhC,EAAkC;AAC9B,gBAAIC,UAAU,GAAG5G,SAAS,CAAC6G,cAAV,CAAyB,cAAzB,CAAjB,CAD8B,CAE9B;;AACAD,YAAAA,UAAU,CAACE,OAAX,CAAmBC,CAAC,IAAI;AACpB,kBAAIA,CAAC,CAACC,IAAF,IAAUpH,WAAd,EAA0B;AACtB,qBAAK8G,aAAL,CAAmBhC,IAAnB,CAAwBqC,CAAxB;AACH;AACJ,aAJD;AAKH;;AAED,cAAIE,CAAC,GAAG,MAAI;AAAA,uCAECC,KAFD;AAGJ,kBAAMC,GAAG,GAAG,KAAI,CAACT,aAAL,CAAmBQ,KAAnB,CAAZ;AAEA,kBAAIE,IAAI,GAAGD,GAAG,CAACE,IAAJ,CAASC,UAAT,CAAoB,aAApB,EAAmC,EAAnC,CAAX;AACAF,cAAAA,IAAI,GAAGA,IAAI,CAACE,UAAL,CAAgB,GAAhB,EAAqB,EAArB,CAAP;AACAF,cAAAA,IAAI,GAAGA,IAAI,CAACE,UAAL,CAAgB,IAAhB,EAAsB,EAAtB,CAAP;AAEA,kBAAIC,EAAU,GAAGC,MAAM,CAACC,MAAM,CAACL,IAAD,CAAN,CAAaM,KAAb,CAAmB,GAAnB,EAAwB,CAAxB,CAAD,CAAvB;AACA,kBAAIC,KAAK,GAAG;AAAA;AAAA,oDAAevE,WAAf,GAA6B8B,KAA7B,CAAmC0C,aAAnC,CAAiDL,EAAjD,CAAZ;AACA,cAAA,KAAI,CAACM,kBAAL,GAA0BX,KAAK,GAAC,CAAhC,CAXI,CAYJ;;AAEA,kBAAG,CAACS,KAAJ,EAAU;AACN3H,gBAAAA,SAAS,CAAC8H,IAAV,CAAeX,GAAG,CAACE,IAAnB,EAAyBzH,WAAzB,EACA,CAACmI,MAAD,EAAiBC,KAAjB,KAAmC,CAClC,CAFD,EAGA,CAAC7D,KAAD,EAAe8D,KAAf,KAA8B;AAC1B,sBAAI9D,KAAK,IAAI,IAAb,EAAmB;AACfxC,oBAAAA,OAAO,CAACC,GAAR,CAAY,yBAAZ,EAAuCuC,KAAK,CAAC+D,OAA7C;AACH,mBAFD,MAEK;AACD;AAAA;AAAA,0DAAe9E,WAAf,GAA6B8B,KAA7B,CAAmCiD,aAAnC,CAAiDZ,EAAjD,EAAqDU,KAArD;AACH;AACJ,iBATD;AAUA;AACH;AA1BG;;AAER,iBAAK,IAAIf,KAAK,GAAG,KAAKW,kBAAtB,EAA0CX,KAAK,GAAG,KAAKR,aAAL,CAAmBC,MAArE,EAA6EO,KAAK,EAAlF,EAAsF;AAAA,+BAA7EA,KAA6E;;AAAA,oCAuB9E;AAEP;;AACD,gBAAG,KAAKW,kBAAL,IAA2B,KAAKnB,aAAL,CAAmBC,MAAjD,EAAwD;AACpD,mBAAKyB,UAAL,CAAgBnB,CAAhB;AACAtF,cAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ;AACH;AACJ,WAhCD;;AAkCA,eAAKyG,QAAL,CAAcpB,CAAd,EAAiB,IAAjB;AAEH;;AAESnC,QAAAA,cAAc,GAAS;AAC7B,cAAI,KAAKwD,aAAT,EAAwB;AACpB,gBAAI,KAAKC,YAAL,IAAqB,IAAzB,EAA+B;AAC3B,mBAAKA,YAAL,GAAoB9I,WAAW,CAAC,KAAK6I,aAAN,CAA/B;AACH;;AAED,iBAAKC,YAAL,CAAkBrE,MAAlB,GAA2B,KAAKrC,IAAhC;;AACA,iBAAK0G,YAAL,CAAkBC,eAAlB,CAAkC,KAAKC,QAAL,KAAgB,CAAlD;AACH;AACJ;;AAGS3F,QAAAA,YAAY,GAAO;AACzB,cAAI,KAAK4F,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiBjJ,WAAW,CAAC,KAAKkJ,UAAN,CAA5B;AACA,iBAAKD,SAAL,CAAexE,MAAf,GAAwB,KAAKrC,IAA7B;AACH;;AACD,eAAK6G,SAAL,CAAeF,eAAf,CAA+B,KAAKC,QAAL,KAAgB,CAA/C;;AACA,eAAKC,SAAL,CAAeE,MAAf,GAAwB,IAAxB;AACH;;AAES5F,QAAAA,YAAY,GAAO;AACzB,cAAI,KAAK0F,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiBjJ,WAAW,CAAC,KAAKkJ,UAAN,CAA5B;AACA,iBAAKD,SAAL,CAAexE,MAAf,GAAwB,KAAKrC,IAA7B;;AACA,iBAAK6G,SAAL,CAAeF,eAAf,CAA+B,KAAKC,QAAL,KAAgB,CAA/C;AACH;;AACD,eAAKC,SAAL,CAAeE,MAAf,GAAwB,KAAxB;AACH;;AAGSC,QAAAA,YAAY,CAACC,IAAD,EAAuB;AAAA,cAAtBA,IAAsB;AAAtBA,YAAAA,IAAsB,GAAR,EAAQ;AAAA;;AACzC,cAAG,KAAKC,SAAL,IAAkB,IAArB,EAA0B;AACtB,gBAAIC,KAAK,GAAGvJ,WAAW,CAAC,KAAKwJ,WAAN,CAAvB;AACAD,YAAAA,KAAK,CAAC9E,MAAN,GAAe,KAAKrC,IAApB;AACA,iBAAKkH,SAAL,GAAiBC,KAAjB;AACH;;AACD,eAAKD,SAAL,CAAeH,MAAf,GAAwB,IAAxB;AACA,eAAKG,SAAL,CAAeP,eAAf,CAA+B,KAAKC,QAAL,KAAgB,EAA/C;AACA,eAAKM,SAAL,CAAejH,YAAf;AAAA;AAAA,8BAAmCoH,OAAnC,CAA2CJ,IAA3C;AACH;;AAGO3F,QAAAA,eAAe,CAACgG,GAAD,EAAc;AACjC,cAAGA,GAAG,CAACC,IAAJ,IAAYC,SAAZ,IAAyBF,GAAG,CAACC,IAAJ,IAAY,CAArC,IAA0CD,GAAG,CAACC,IAAJ,IAAY,CAAzD,EAA2D;AACvD,iBAAKE,WAAL,GAAmB,CAAnB;AACA;AACH;;AAED,cAAGH,GAAG,CAACC,IAAJ,IAAY,CAAC,CAAb,IAAkBD,GAAG,CAACC,IAAJ,IAAY,CAAC,CAA/B,IAAoCD,GAAG,CAACC,IAAJ,IAAY,CAAC,CAAjD,IAAsDD,GAAG,CAACC,IAAJ,IAAY,CAAC,CAAtE,EAAyE;AACrE,gBAAI,KAAKE,WAAL,GAAmB,CAAvB,EAAyB;AACrB;AAAA;AAAA,gDAAalG,WAAb,GAA2BmG,gBAA3B,CAA4C;AAAA;AAAA,gDAAanG,WAAb,GAA2B8B,KAA3B,CAAiCsE,UAAjC,EAA5C,EAA2F,KAA3F;AACA,mBAAKF,WAAL,IAAoB,CAApB;AACA;AACH;AACJ;;AAED,eAAKT,YAAL,CAAkB;AAAA;AAAA,8BAAMY,UAAN,CAAiBN,GAAG,CAACC,IAArB,CAAlB;AACH;;AAEOxG,QAAAA,WAAW,CAACuG,GAAD,EAAa;AAC5B,eAAKN,YAAL,CAAkBM,GAAlB;AACH;;AAESpF,QAAAA,aAAa,GAAG;AACtB,cAAI,KAAKsC,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeqD,OAAf;;AACA,iBAAKrD,SAAL,GAAiB,IAAjB;AACH;;AAED,cAAI,KAAKE,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBmD,OAAjB;;AACA,iBAAKnD,WAAL,GAAmB,IAAnB;AACH;;AAED,cAAI,KAAKtC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiByF,OAAjB;;AACA,iBAAKzF,WAAL,GAAmB,IAAnB;AACH;;AAED,cAAI,KAAKyE,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAegB,OAAf;;AACA,iBAAKhB,SAAL,GAAiB,IAAjB;AACH;AAEJ;;AAEMD,QAAAA,QAAQ,GAAU;AACrB,iBAAO,KAAK5G,IAAL,CAAU8H,QAAV,CAAmBhD,MAAnB,GAA0B,CAAjC;AACH;;AAhVuC,O;;;;;iBAEb,I;;;;;;;iBAGF,I;;;;;;;iBAEE,I;;;;;;;iBAGH,I;;;;;;;iBAIH,I;;;;;;;iBAGC,I", "sourcesContent": ["import { _decorator, Component, Prefab, Node, instantiate, TiledMapAsset, JsonAsset, SpriteFrame, sys, AudioSource, assert, resources } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { GameConfig } from \"./config/GameConfig\";\nimport LoaderManager, { LoadData, LoadDataType } from \"./core/LoaderManager\";\nimport ArmyCommand from \"./general/ArmyCommand\";\nimport GeneralCommand from \"./general/GeneralCommand\";\nimport LoginCommand from \"./login/LoginCommand\";\nimport MapCommand from \"./map/MapCommand\";\nimport MapUICommand from \"./map/ui/MapUICommand\";\nimport { HttpManager } from \"./network/http/HttpManager\";\nimport { NetEvent } from \"./network/socket/NetInterface\";\nimport { NetManager } from \"./network/socket/NetManager\";\nimport { NetNodeType } from \"./network/socket/NetNode\";\nimport SkillCommand from \"./skill/SkillCommand\";\nimport Toast from \"./utils/Toast\";\nimport { Tools } from \"./utils/Tools\";\nimport { EventMgr } from './utils/EventMgr';\nimport { AudioManager } from './common/AudioManager';\nimport { LogicEvent } from './common/LogicEvent';\nimport { FixedScreenAdapter } from './utils/FixedScreenAdapter';\nimport { ClickTestHelper } from './utils/ClickTestHelper';\nimport { SimpleLoginTest } from './test/SimpleLoginTest';\n\n@ccclass('Main')\nexport default class Main extends Component {\n    @property(Prefab)\n    loginScenePrefab: Prefab = null;\n\n    @property(Prefab)\n    mapScenePrefab: Prefab = null;\n    @property(Prefab)\n    mapUIScenePrefab: Prefab = null;\n\n    @property(Prefab)\n    loadingPrefab: Prefab = null;\n\n\n    @property(Prefab)\n    waitPrefab: Prefab = null;\n\n    @property(Prefab)\n    toastPrefab: Prefab = null;\n\n    private _audioSource: AudioSource = null!;\n    private toastNode: Node = null;\n    protected _loginScene: Node = null;\n    protected _mapScene: Node = null;\n    protected _mapUIScene: Node = null;\n    protected _loadingNode: Node = null;\n    protected _waitNode: Node = null;\n    private _retryTimes: number = 0;\n    private _h5GeneralPicIndex: number = 0;\n    private _h5GeneralPic = [];\n\n\n    protected onLoad(): void {\n\n        console.log(\"main load\");\n\n        // 第一步：初始化固定屏幕适配（必须在其他所有初始化之前执行）\n        if (!this.node.getComponent(FixedScreenAdapter)) {\n            this.node.addComponent(FixedScreenAdapter);\n        }\n\n        // 第二步：等待一帧，确保适配器组件加载完成\n        this.scheduleOnce(() => {\n            // 初始化屏幕适配\n            FixedScreenAdapter.instance.init();\n\n            // 添加点击测试助手（仅在调试模式下）\n            if (GameConfig.screen.enableDebugInfo && !this.node.getComponent(ClickTestHelper)) {\n                this.node.addComponent(ClickTestHelper);\n            }\n        }, 0.1);\n\n        const audioSource = this.getComponent(AudioSource)!;\n        assert(audioSource);\n        this._audioSource = audioSource;\n\n        AudioManager.instance.init(this._audioSource);\n  \n        EventMgr.on(LogicEvent.enterMap, this.onEnterMap, this);\n        EventMgr.on(LogicEvent.enterLogin, this.enterLogin, this);\n        EventMgr.on(LogicEvent.showToast, this.onShowToast, this);\n        EventMgr.on(LogicEvent.showWaiting, this.showWaitNode, this);\n        EventMgr.on(LogicEvent.hideWaiting, this.hideWaitNode, this);\n\n        EventMgr.on(NetEvent.ServerRequesting, this.showWaitNode,this);\n        EventMgr.on(NetEvent.ServerRequestSucess,this.onServerRequest,this);\n\n\n        //初始化连接\n        NetManager.getInstance().connect({ url: GameConfig.serverUrl , type:NetNodeType.BaseServer });\n        HttpManager.getInstance().setWebUrl(GameConfig.webUrl);\n\n        //初始化业务模块\n        LoginCommand.getInstance();\n        MapCommand.getInstance();\n        MapUICommand.getInstance();\n        GeneralCommand.getInstance();\n        ArmyCommand.getInstance();\n\n        this.enterLogin();\n       \n    }\n\n    protected onDestroy(): void {\n        console.log(\"main onDestroy\");\n\n        EventMgr.targetOff(this);\n    }\n\n    protected clearData(): void {\n        MapCommand.getInstance().clearData();\n        GeneralCommand.getInstance().clearData();\n        ArmyCommand.getInstance().clearData();\n    }\n\n    private enterLogin(): void {\n        this.clearAllScene();\n        this.clearData();\n\n        // 使用正常的登录界面\n        if (this.loginScenePrefab) {\n            this._loginScene = instantiate(this.loginScenePrefab);\n            this._loginScene.parent = this.node;\n            console.log('[Main] 登录场景已创建');\n        } else {\n            console.error('[Main] loginScenePrefab未设置，使用简单测试界面');\n            // 备用方案：使用简单测试登录界面\n            this.createSimpleLoginTest();\n        }\n\n        //this.h5LoadGeneralTex();\n    }\n\n    /**\n     * 创建简单的登录测试界面\n     */\n    private createSimpleLoginTest(): void {\n        console.log('[Main] 创建简单登录测试界面');\n\n        const testNode = new Node('SimpleLoginTest');\n        testNode.setParent(this.node);\n\n        // 添加测试组件\n        const testComponent = testNode.addComponent(SimpleLoginTest);\n\n        // 延迟添加输入框测试\n        this.scheduleOnce(() => {\n            testComponent.addInputTest();\n        }, 1.0);\n\n        this._loginScene = testNode; // 设置为当前登录场景\n\n        console.log('[Main] 简单登录测试界面创建完成');\n    }\n\n    protected onEnterMap(): void {\n        let dataList: LoadData[] = [];\n        dataList.push(new LoadData(\"./world/map\", LoadDataType.FILE, TiledMapAsset));\n        dataList.push(new LoadData(\"./config/mapRes_0\", LoadDataType.FILE, JsonAsset));\n        dataList.push(new LoadData(\"./config/json/facility/\", LoadDataType.DIR, JsonAsset));\n        dataList.push(new LoadData(\"./config/json/general/\", LoadDataType.DIR, JsonAsset));\n\n        if(sys.isBrowser){\n            dataList.push(new LoadData(\"./generalpic1\", LoadDataType.DIR, SpriteFrame));\n        }else{\n            dataList.push(new LoadData(\"./generalpic\", LoadDataType.DIR, SpriteFrame));\n        }\n       \n        dataList.push(new LoadData(\"./config/basic\", LoadDataType.FILE, JsonAsset));\n        dataList.push(new LoadData(\"./config/json/skill/\", LoadDataType.DIR, JsonAsset));\n\n        this.addLoadingNode();\n        console.log(\"onEnterMap\");\n        LoaderManager.getInstance().startLoadList(dataList, null,\n            (error: Error, paths: string[], datas: any[]) => {\n                if (error != null) {\n                    console.log(\"加载配置文件失败\");\n                    return;\n                }\n                console.log(\"loadComplete\", paths, datas);\n                MapCommand.getInstance().proxy.tiledMapAsset = datas[0] as TiledMapAsset;\n                MapCommand.getInstance().proxy.initMapResConfig((datas[1] as JsonAsset).json);\n\n                MapUICommand.getInstance().proxy.setAllFacilityCfg(datas[2]);\n                GeneralCommand.getInstance().proxy.initGeneralConfig(datas[3],(datas[5] as JsonAsset).json);\n                GeneralCommand.getInstance().proxy.initGeneralTex(datas[4]);\n                MapUICommand.getInstance().proxy.setBasic(datas[5]);\n                SkillCommand.getInstance().proxy.initSkillConfig(datas[6]);\n\n                var d = (datas[5] as JsonAsset).json\n                MapCommand.getInstance().proxy.setWarFree(d[\"build\"].war_free);\n\n                let cityId: number = MapCommand.getInstance().cityProxy.getMyMainCity().cityId;\n                GeneralCommand.getInstance().qryMyGenerals();\n                ArmyCommand.getInstance().qryArmyList(cityId);\n                MapUICommand.getInstance().qryWarReport();\n                SkillCommand.getInstance().qrySkillList();\n\n                this.clearAllScene();\n\n            \n                this._mapScene = instantiate(this.mapScenePrefab);\n                this._mapScene.parent = this.node;\n              \n                this._mapUIScene = instantiate(this.mapUIScenePrefab);\n                this._mapUIScene.parent = this.node;\n\n                this.addLoadingNode();\n             \n            },\n            this\n        );\n        \n    }\n\n    private h5LoadGeneralTex() {\n        if(!sys.isBrowser){\n            return;\n        }\n\n        if(this._h5GeneralPic.length == 0){\n            let generalpic = resources.getDirWithPath(\"./generalpic\");\n            // console.log(\"generalpic:\", generalpic);\n            generalpic.forEach(v => {\n                if (v.ctor == SpriteFrame){\n                    this._h5GeneralPic.push(v);\n                }\n            });\n        }\n        \n        let f = ()=>{\n           \n            for (let index = this._h5GeneralPicIndex; index < this._h5GeneralPic.length; index++) {\n                const pic = this._h5GeneralPic[index];\n\n                let name = pic.path.replaceAll(\"spriteFrame\", \"\");\n                name = name.replaceAll(\"/\", \"\");\n                name = name.replaceAll(\"\\\\\", \"\");\n\n                let id: number = Number(String(name).split(\"_\")[1]);\n                let frame = GeneralCommand.getInstance().proxy.getGeneralTex(id);\n                this._h5GeneralPicIndex = index+1;\n                // console.log(\"load index 1111:\", index);\n\n                if(!frame){\n                    resources.load(pic.path, SpriteFrame, \n                    (finish: number, total: number) => {\n                    },\n                    (error: Error, asset: any) => {\n                        if (error != null) {\n                            console.log(\"h5LoadGeneralTex error:\", error.message);\n                        }else{\n                            GeneralCommand.getInstance().proxy.setGeneralTex(id, asset);\n                        }\n                    });\n                    break;\n                }\n            }\n            if(this._h5GeneralPicIndex >= this._h5GeneralPic.length){\n                this.unschedule(f);\n                console.log(\"h5 load generalPic finish\");\n            }\n        }\n\n        this.schedule(f, 0.01);\n    \n    }\n\n    protected addLoadingNode(): void {\n        if (this.loadingPrefab) {\n            if (this._loadingNode == null) {\n                this._loadingNode = instantiate(this.loadingPrefab);\n            }\n\n            this._loadingNode.parent = this.node;\n            this._loadingNode.setSiblingIndex(this.topLayer()+1);\n        }\n    }\n\n\n    protected showWaitNode():void{\n        if (this._waitNode == null) {\n            this._waitNode = instantiate(this.waitPrefab);\n            this._waitNode.parent = this.node;\n        }\n        this._waitNode.setSiblingIndex(this.topLayer()+2);\n        this._waitNode.active = true;\n    }\n\n    protected hideWaitNode():void{\n        if (this._waitNode == null) {\n            this._waitNode = instantiate(this.waitPrefab);\n            this._waitNode.parent = this.node;\n            this._waitNode.setSiblingIndex(this.topLayer()+2);\n        }\n        this._waitNode.active = false;\n    }\n\n\n    protected showTopToast(text:string = \"\"):void{\n        if(this.toastNode == null){\n            let toast = instantiate(this.toastPrefab);\n            toast.parent = this.node;\n            this.toastNode = toast;\n        }\n        this.toastNode.active = true;\n        this.toastNode.setSiblingIndex(this.topLayer()+10);\n        this.toastNode.getComponent(Toast).setText(text);\n    }\n\n\n    private onServerRequest(msg:any):void{\n        if(msg.code == undefined || msg.code == 0 || msg.code == 9){\n            this._retryTimes = 0;\n            return;\n        }\n\n        if(msg.code == -1 || msg.code == -2 || msg.code == -3 || msg.code == -4 ){\n            if (this._retryTimes < 3){\n                LoginCommand.getInstance().role_enterServer(LoginCommand.getInstance().proxy.getSession(), false);\n                this._retryTimes += 1;\n                return\n            }\n        }\n\n        this.showTopToast(Tools.getCodeStr(msg.code));\n    }\n\n    private onShowToast(msg:string) {\n        this.showTopToast(msg);\n    }\n\n    protected clearAllScene() {\n        if (this._mapScene) {\n            this._mapScene.destroy();\n            this._mapScene = null;\n        }\n\n        if (this._mapUIScene) {\n            this._mapUIScene.destroy();\n            this._mapUIScene = null;\n        }\n\n        if (this._loginScene) {\n            this._loginScene.destroy();\n            this._loginScene = null;\n        }\n        \n        if (this._waitNode) {\n            this._waitNode.destroy();\n            this._waitNode = null;\n        }\n        \n    }\n\n    public topLayer():number {\n        return this.node.children.length+1;\n    }\n}\n"]}