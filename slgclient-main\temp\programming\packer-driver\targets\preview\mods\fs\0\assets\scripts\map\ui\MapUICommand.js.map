{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"], "names": ["MapUICommand", "ServerConfig", "LoginCommand", "NetManager", "MapCommand", "MapUIProxy", "EventMgr", "LogicEvent", "getInstance", "_instance", "destory", "onDestory", "constructor", "on", "city_facilities", "onCityFacilities", "city_upFacility", "onCityUpFacility", "role_myRoleRes", "onRoleMyRoleRes", "war_report", "onUpdataWarReports", "war_reportPush", "onUpdataWarReport", "war_read", "onUpdataWarRead", "interior_collect", "onCollect", "interior_openCollect", "onOpenCollect", "roleRes_push", "updataRoleRes", "setInterval", "list", "_proxy", "getMyAllFacilitys", "for<PERSON>ach", "fs", "key", "f", "isNeedUpdateLevel", "console", "log", "qryCityFacilities", "data", "code", "updateMyFacilityList", "msg", "cityId", "facilities", "emit", "updateMyFacilities", "cityData", "cityProxy", "getMyCityById", "addition", "updateMyCityAdditions", "maxDurable", "getMyCityMaxDurable", "updateCityAddition", "facilityData", "updateMyFacility", "facility", "proxy", "saveEnterData", "upateMyRoleRes", "setRoleResData", "updateWarReports", "upateWarReport", "updateWarReport", "id", "updateAllWarRead", "updateWarRead", "interiorCollect", "interiorOpenCollect", "targetOff", "sendData", "name", "send", "upFacility", "ftype", "fType", "qryMyRoleRes", "updateMyProperty", "qryWarReport", "warRead", "interiorTransform", "from", "to", "interior_transform"], "mappings": ";;;oHAUqBA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATZC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,Y;;AACEC,MAAAA,U,iBAAAA,U;;AAEFC,MAAAA,U;;AACAC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;yBAEYP,Y,GAAN,MAAMA,YAAN,CAAmB;AAC9B;AAEyB,eAAXQ,WAAW,GAAiB;AACtC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIT,YAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKS,SAAZ;AACH;;AAEoB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SAjB6B,CAmB9B;;;AAGAG,QAAAA,WAAW,GAAG;AAAA,0CAFiB;AAAA;AAAA,yCAEjB;;AACV;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,eAAzB,EAA0C,KAAKC,gBAA/C,EAAiE,IAAjE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,eAAzB,EAA0C,KAAKC,gBAA/C,EAAiE,IAAjE;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,4CAAaK,cAAzB,EAAyC,KAAKC,eAA9C,EAA+D,IAA/D;AACA;AAAA;AAAA,oCAASN,EAAT,CAAY;AAAA;AAAA,4CAAaO,UAAzB,EAAqC,KAAKC,kBAA1C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASR,EAAT,CAAY;AAAA;AAAA,4CAAaS,cAAzB,EAAyC,KAAKC,iBAA9C,EAAiE,IAAjE;AACA;AAAA;AAAA,oCAASV,EAAT,CAAY;AAAA;AAAA,4CAAaW,QAAzB,EAAmC,KAAKC,eAAxC,EAAyD,IAAzD;AACA;AAAA;AAAA,oCAASZ,EAAT,CAAY;AAAA;AAAA,4CAAaa,gBAAzB,EAA2C,KAAKC,SAAhD,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASd,EAAT,CAAY;AAAA;AAAA,4CAAae,oBAAzB,EAA+C,KAAKC,aAApD,EAAmE,IAAnE;AAEA;AAAA;AAAA,oCAAShB,EAAT,CAAY;AAAA;AAAA,4CAAaiB,YAAzB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AAGAC,UAAAA,WAAW,CAAC,MAAM;AACf,gBAAIC,IAAwC,GAAG,KAAKC,MAAL,CAAYC,iBAAZ,EAA/C;;AACAF,YAAAA,IAAI,CAACG,OAAL,CAAa,CAACC,EAAD,EAAKC,GAAL,KAAa;AACrBD,cAAAA,EAAE,CAACD,OAAH,CAAWG,CAAC,IAAI;AACZ,oBAAGA,CAAC,CAACC,iBAAF,EAAH,EAAyB;AACrB;AACAC,kBAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ;AACA,uBAAKC,iBAAL,CAAuBL,GAAvB;AACA;AACH;AACJ,eAPD;AAQJ,aATD;AAUF,WAZU,EAYR,IAZQ,CAAX;AAaH;;AAESvB,QAAAA,gBAAgB,CAAC6B,IAAD,EAAkB;AACxCH,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCE,IAAlC;;AACA,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKX,MAAL,CAAYY,oBAAZ,CAAiCF,IAAI,CAACG,GAAL,CAASC,MAA1C,EAAkDJ,IAAI,CAACG,GAAL,CAASE,UAA3D;;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,kBAAzB,EAFgB,CAIhB;;AACA,gBAAIC,QAAqB,GAAG;AAAA;AAAA,0CAAW5C,WAAX,GAAyB6C,SAAzB,CAAmCC,aAAnC,CAAiDV,IAAI,CAACG,GAAL,CAASC,MAA1D,CAA5B;;AACA,gBAAIO,QAAsB,GAAG,KAAKrB,MAAL,CAAYsB,qBAAZ,CAAkCJ,QAAQ,CAACJ,MAA3C,CAA7B;;AACAI,YAAAA,QAAQ,CAACK,UAAT,GAAsB,KAAKvB,MAAL,CAAYwB,mBAAZ,CAAgCd,IAAI,CAACG,GAAL,CAASC,MAAzC,CAAtB;AACA;AAAA;AAAA,sCAASE,IAAT,CAAc;AAAA;AAAA,0CAAWS,kBAAzB,EAA6Cf,IAAI,CAACG,GAAL,CAASC,MAAtD,EAA8DO,QAA9D;AACH;AACJ;;AAGStC,QAAAA,gBAAgB,CAAC2B,IAAD,EAAkB;AACxCH,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCE,IAAlC;;AACA,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAIe,YAAsB,GAAG,KAAK1B,MAAL,CAAY2B,gBAAZ,CAA6BjB,IAAI,CAACG,GAAL,CAASC,MAAtC,EAA8CJ,IAAI,CAACG,GAAL,CAASe,QAAvD,CAA7B;;AACA;AAAA;AAAA,sCAASZ,IAAT,CAAc;AAAA;AAAA,0CAAWW,gBAAzB,EAA2CjB,IAAI,CAACG,GAAL,CAASC,MAApD,EAA4DY,YAA5D;AACA;AAAA;AAAA,8CAAapD,WAAb,GAA2BuD,KAA3B,CAAiCC,aAAjC,CAA+CpB,IAAI,CAACG,GAApD;AACA;AAAA;AAAA,sCAASG,IAAT,CAAc;AAAA;AAAA,0CAAWe,cAAzB,EAJgB,CAMhB;;AACA,gBAAIb,QAAqB,GAAG;AAAA;AAAA,0CAAW5C,WAAX,GAAyB6C,SAAzB,CAAmCC,aAAnC,CAAiDV,IAAI,CAACG,GAAL,CAASC,MAA1D,CAA5B;;AACA,gBAAIO,QAAsB,GAAG,KAAKrB,MAAL,CAAYsB,qBAAZ,CAAkCZ,IAAI,CAACG,GAAL,CAASC,MAA3C,CAA7B;;AACAI,YAAAA,QAAQ,CAACK,UAAT,GAAsB,KAAKvB,MAAL,CAAYwB,mBAAZ,CAAgCd,IAAI,CAACG,GAAL,CAASC,MAAzC,CAAtB;AACA;AAAA;AAAA,sCAASE,IAAT,CAAc;AAAA;AAAA,0CAAWS,kBAAzB,EAA6Cf,IAAI,CAACG,GAAL,CAASC,MAAtD,EAA8DO,QAA9D;AACH;AACJ;;AAGSpC,QAAAA,eAAe,CAACyB,IAAD,EAAkB;AACvCH,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCE,IAAlC;;AACA,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,8CAAarC,WAAb,GAA2BuD,KAA3B,CAAiCC,aAAjC,CAA+CpB,IAAI,CAACG,GAApD;AACA;AAAA;AAAA,sCAASG,IAAT,CAAc;AAAA;AAAA,0CAAWe,cAAzB;AACH;AACJ;;AAGSlC,QAAAA,aAAa,CAACa,IAAD,EAAkB;AACrC,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,8CAAarC,WAAb,GAA2BuD,KAA3B,CAAiCG,cAAjC,CAAgDtB,IAAI,CAACG,GAArD;AACA;AAAA;AAAA,sCAASG,IAAT,CAAc;AAAA;AAAA,0CAAWe,cAAzB;AACH;AACJ;;AAGS5C,QAAAA,kBAAkB,CAACuB,IAAD,EAAkB;AAC1CH,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCE,IAAnC;;AACA,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKX,MAAL,CAAYiC,gBAAZ,CAA6BvB,IAAI,CAACG,GAAlC;;AACA;AAAA;AAAA,sCAASG,IAAT,CAAc;AAAA;AAAA,0CAAWkB,cAAzB;AACH;AACJ;;AAIS7C,QAAAA,iBAAiB,CAACqB,IAAD,EAAkB;AACzCH,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCE,IAAnC;;AACA,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKX,MAAL,CAAYmC,eAAZ,CAA4BzB,IAAI,CAACG,GAAjC;;AACA;AAAA;AAAA,sCAASG,IAAT,CAAc;AAAA;AAAA,0CAAWkB,cAAzB;AACH;AACJ;;AAES3C,QAAAA,eAAe,CAACmB,IAAD,EAAkB;AACvCH,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCE,IAAjC;;AACA,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAIyB,EAAE,GAAG1B,IAAI,CAACG,GAAL,CAASuB,EAAlB;;AACA,gBAAIA,EAAE,IAAI,CAAV,EAAa;AACT,mBAAKpC,MAAL,CAAYqC,gBAAZ,CAA6B,IAA7B;AACH,aAFD,MAEK;AACD,mBAAKrC,MAAL,CAAYsC,aAAZ,CAA0BF,EAA1B,EAA8B,IAA9B;AACH;;AAED;AAAA;AAAA,sCAASpB,IAAT,CAAc;AAAA;AAAA,0CAAWkB,cAAzB;AACH;AACJ;;AAESzC,QAAAA,SAAS,CAACiB,IAAD,EAAgB;AAC/BH,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BE,IAA3B;;AACA,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,sCAASK,IAAT,CAAc;AAAA;AAAA,0CAAWuB,eAAzB,EAA0C7B,IAAI,CAACG,GAA/C;AACH;AACJ;;AAESlB,QAAAA,aAAa,CAACe,IAAD,EAAe;AAClCH,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BE,IAA/B;;AACA,cAAIA,IAAI,CAACC,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,sCAASK,IAAT,CAAc;AAAA;AAAA,0CAAWwB,mBAAzB,EAA8C9B,IAAI,CAACG,GAAnD;AACH;AACJ;;AAEMpC,QAAAA,SAAS,GAAS;AACrB;AAAA;AAAA,oCAASgE,SAAT,CAAmB,IAAnB;AACH;;AAEe,YAALZ,KAAK,GAAe;AAC3B,iBAAO,KAAK7B,MAAZ;AACH;AAGD;AACJ;AACA;AACA;;;AACWS,QAAAA,iBAAiB,CAACK,MAAD,EAA2B;AAAA,cAA1BA,MAA0B;AAA1BA,YAAAA,MAA0B,GAAT,CAAS;AAAA;;AAC/C,cAAI4B,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa/D,eADH;AAEhBiC,YAAAA,GAAG,EAAE;AACDC,cAAAA,MAAM,EAAEA;AADP;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAWxC,WAAX,GAAyBsE,IAAzB,CAA8BF,QAA9B;AACH;AAGD;AACJ;AACA;AACA;AACA;;;AACWG,QAAAA,UAAU,CAAC/B,MAAD,EAAqBgC,KAArB,EAA8C;AAAA,cAA7ChC,MAA6C;AAA7CA,YAAAA,MAA6C,GAA5B,CAA4B;AAAA;;AAAA,cAAzBgC,KAAyB;AAAzBA,YAAAA,KAAyB,GAAT,CAAS;AAAA;;AAC3D,cAAIJ,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa7D,eADH;AAEhB+B,YAAAA,GAAG,EAAE;AACDC,cAAAA,MAAM,EAAEA,MADP;AAEDiC,cAAAA,KAAK,EAAED;AAFN;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWxE,WAAX,GAAyBsE,IAAzB,CAA8BF,QAA9B;AACH;AAED;AACJ;AACA;AACA;;;AACWM,QAAAA,YAAY,GAAS;AACxB,cAAIN,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa3D,cADH;AAEhB6B,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWvC,WAAX,GAAyBsE,IAAzB,CAA8BF,QAA9B;AACH;AAGD;AACJ;AACA;AACA;;;AACWO,QAAAA,gBAAgB,CAACvC,IAAD,EAAkB;AACrC;AAAA;AAAA,4CAAapC,WAAb,GAA2BuD,KAA3B,CAAiCC,aAAjC,CAA+CpB,IAAI,CAACG,GAApD;AACA;AAAA;AAAA,oCAASG,IAAT,CAAc;AAAA;AAAA,wCAAWe,cAAzB;AACH;AAID;AACJ;AACA;;;AACWmB,QAAAA,YAAY,GAAS;AACxB,cAAIR,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAazD,UADH;AAEhB2B,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWvC,WAAX,GAAyBsE,IAAzB,CAA8BF,QAA9B;AACH;AAID;AACJ;AACA;;;AACWS,QAAAA,OAAO,CAACf,EAAD,EAAuB;AAAA,cAAtBA,EAAsB;AAAtBA,YAAAA,EAAsB,GAAT,CAAS;AAAA;;AACjC,cAAIM,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAarD,QADH;AAEhBuB,YAAAA,GAAG,EAAE;AACDuB,cAAAA,EAAE,EAAEA;AADH;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAW9D,WAAX,GAAyBsE,IAAzB,CAA8BF,QAA9B;AACH;;AAEMH,QAAAA,eAAe,GAAS;AAC3B,cAAIG,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAanD,gBADH;AAEhBqB,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWvC,WAAX,GAAyBsE,IAAzB,CAA8BF,QAA9B;AACH;;AAEMF,QAAAA,mBAAmB,GAAS;AAC/B,cAAIE,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAajD,oBADH;AAEhBmB,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWvC,WAAX,GAAyBsE,IAAzB,CAA8BF,QAA9B;AACH;;AAEMU,QAAAA,iBAAiB,CAACC,IAAD,EAAeC,EAAf,EAAkC;AACtD,cAAIZ,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAaY,kBADH;AAEhB1C,YAAAA,GAAG,EAAE;AACDwC,cAAAA,IAAI,EAACA,IADJ;AAEDC,cAAAA,EAAE,EAACA;AAFF;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWhF,WAAX,GAAyBsE,IAAzB,CAA8BF,QAA9B;AACH;;AAxQ6B,O;;sBAAb5E,Y", "sourcesContent": ["import { _decorator } from 'cc';\nimport { ServerConfig } from \"../../config/ServerConfig\";\nimport LoginCommand from \"../../login/LoginCommand\";\nimport { NetManager } from \"../../network/socket/NetManager\";\nimport { MapCityData } from \"../MapCityProxy\";\nimport MapCommand from \"../MapCommand\";\nimport MapUIProxy, { CityAddition, Facility } from \"./MapUIProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { LogicEvent } from '../../common/LogicEvent';\n\nexport default class MapUICommand {\n    //单例\n    protected static _instance: MapUICommand;\n    public static getInstance(): MapUICommand {\n        if (this._instance == null) {\n            this._instance = new MapUICommand();\n        }\n        return this._instance;\n    }\n\n    public static destory(): boolean {\n        if (this._instance) {\n            this._instance.onDestory();\n            this._instance = null;\n            return true;\n        }\n        return false;\n    }\n\n    //数据model\n    protected _proxy: MapUIProxy = new MapUIProxy();\n\n    constructor() {\n        EventMgr.on(ServerConfig.city_facilities, this.onCityFacilities, this);\n        EventMgr.on(ServerConfig.city_upFacility, this.onCityUpFacility, this);\n        EventMgr.on(ServerConfig.role_myRoleRes, this.onRoleMyRoleRes, this);\n        EventMgr.on(ServerConfig.war_report, this.onUpdataWarReports, this);\n        EventMgr.on(ServerConfig.war_reportPush, this.onUpdataWarReport, this);\n        EventMgr.on(ServerConfig.war_read, this.onUpdataWarRead, this);\n        EventMgr.on(ServerConfig.interior_collect, this.onCollect, this);\n        EventMgr.on(ServerConfig.interior_openCollect, this.onOpenCollect, this);\n\n        EventMgr.on(ServerConfig.roleRes_push, this.updataRoleRes, this);\n        \n\n        setInterval(() => {\n           let list: Map<number, Map<number, Facility>> = this._proxy.getMyAllFacilitys();\n           list.forEach((fs, key) => { \n                fs.forEach(f => {\n                    if(f.isNeedUpdateLevel()){\n                        //倒计时完，请求最新的等级\n                        console.log(\"有设施升级完了，需要刷新\");\n                        this.qryCityFacilities(key);\n                        return\n                    }\n                });\n           });\n        }, 1000);\n    }\n\n    protected onCityFacilities(data: any): void {\n        console.log(\"onCityFacilities :\", data);\n        if (data.code == 0) {\n            this._proxy.updateMyFacilityList(data.msg.cityId, data.msg.facilities);\n            EventMgr.emit(LogicEvent.updateMyFacilities);\n\n            //刷新城池附加加成\n            let cityData: MapCityData = MapCommand.getInstance().cityProxy.getMyCityById(data.msg.cityId);\n            let addition: CityAddition = this._proxy.updateMyCityAdditions(cityData.cityId);\n            cityData.maxDurable = this._proxy.getMyCityMaxDurable(data.msg.cityId);\n            EventMgr.emit(LogicEvent.updateCityAddition, data.msg.cityId, addition);\n        }\n    }\n\n\n    protected onCityUpFacility(data: any): void {\n        console.log(\"onCityUpFacility :\", data);\n        if (data.code == 0) {\n            let facilityData: Facility = this._proxy.updateMyFacility(data.msg.cityId, data.msg.facility);\n            EventMgr.emit(LogicEvent.updateMyFacility, data.msg.cityId, facilityData);\n            LoginCommand.getInstance().proxy.saveEnterData(data.msg);\n            EventMgr.emit(LogicEvent.upateMyRoleRes);\n\n            //刷新城池附加加成\n            let cityData: MapCityData = MapCommand.getInstance().cityProxy.getMyCityById(data.msg.cityId);\n            let addition: CityAddition = this._proxy.updateMyCityAdditions(data.msg.cityId);\n            cityData.maxDurable = this._proxy.getMyCityMaxDurable(data.msg.cityId);\n            EventMgr.emit(LogicEvent.updateCityAddition, data.msg.cityId, addition);\n        }\n    }\n\n\n    protected onRoleMyRoleRes(data: any): void {\n        console.log(\"onRoleMyProperty :\", data);\n        if (data.code == 0) {\n            LoginCommand.getInstance().proxy.saveEnterData(data.msg);\n            EventMgr.emit(LogicEvent.upateMyRoleRes);\n        }\n    }\n\n\n    protected updataRoleRes(data: any): void {\n        if (data.code == 0) {\n            LoginCommand.getInstance().proxy.setRoleResData(data.msg);\n            EventMgr.emit(LogicEvent.upateMyRoleRes);\n        }\n    }\n\n\n    protected onUpdataWarReports(data: any): void {\n        console.log(\"onUpdataWarReport :\", data);\n        if (data.code == 0) {\n            this._proxy.updateWarReports(data.msg);\n            EventMgr.emit(LogicEvent.upateWarReport);\n        }\n    }\n\n\n\n    protected onUpdataWarReport(data: any): void {\n        console.log(\"onUpdataWarReport :\", data);\n        if (data.code == 0) {\n            this._proxy.updateWarReport(data.msg);\n            EventMgr.emit(LogicEvent.upateWarReport);\n        }\n    }\n\n    protected onUpdataWarRead(data: any): void {\n        console.log(\"onUpdataWarRead :\", data);\n        if (data.code == 0) {\n            var id = data.msg.id;\n            if (id == 0) {\n                this._proxy.updateAllWarRead(true);\n            }else{\n                this._proxy.updateWarRead(id, true);\n            }\n           \n            EventMgr.emit(LogicEvent.upateWarReport);\n        }\n    }\n\n    protected onCollect(data:any):void {\n        console.log(\"onCollect :\", data);\n        if (data.code == 0) {\n            EventMgr.emit(LogicEvent.interiorCollect, data.msg);\n        }\n    }\n\n    protected onOpenCollect(data:any):void{\n        console.log(\"onOpenCollect :\", data);\n        if (data.code == 0) {\n            EventMgr.emit(LogicEvent.interiorOpenCollect, data.msg);\n        }\n    }\n\n    public onDestory(): void {\n        EventMgr.targetOff(this);\n    }\n\n    public get proxy(): MapUIProxy {\n        return this._proxy;\n    }\n\n\n    /**\n     * 设施\n     * @param cityId \n     */\n    public qryCityFacilities(cityId: number = 0): void {\n        let sendData: any = {\n            name: ServerConfig.city_facilities,\n            msg: {\n                cityId: cityId,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    /**\n     * 升级设施\n     * @param cityId \n     * @param ftype \n     */\n    public upFacility(cityId: number = 0, ftype: number = 0): void {\n        let sendData: any = {\n            name: ServerConfig.city_upFacility,\n            msg: {\n                cityId: cityId,\n                fType: ftype,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    /**\n     * 我的角色资源属性\n     * @param cityId \n     */\n    public qryMyRoleRes(): void {\n        let sendData: any = {\n            name: ServerConfig.role_myRoleRes,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    /**\n     * 我的角色资源属性(全)\n     * @param \n     */\n    public updateMyProperty(data: any): void {\n        LoginCommand.getInstance().proxy.saveEnterData(data.msg);\n        EventMgr.emit(LogicEvent.upateMyRoleRes);\n    }\n\n\n\n    /**\n     * 战报查询\n     */\n    public qryWarReport(): void {\n        let sendData: any = {\n            name: ServerConfig.war_report,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n\n    /**\n     * 读取\n     */\n    public warRead(id: number = 0): void {\n        let sendData: any = {\n            name: ServerConfig.war_read,\n            msg: {\n                id: id,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public interiorCollect(): void {\n        let sendData: any = {\n            name: ServerConfig.interior_collect,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public interiorOpenCollect(): void {\n        let sendData: any = {\n            name: ServerConfig.interior_openCollect,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n    \n    public interiorTransform(from:number[],to:number[]): void {\n        let sendData: any = {\n            name: ServerConfig.interior_transform,\n            msg: {\n                from:from,\n                to:to\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n}\n"]}