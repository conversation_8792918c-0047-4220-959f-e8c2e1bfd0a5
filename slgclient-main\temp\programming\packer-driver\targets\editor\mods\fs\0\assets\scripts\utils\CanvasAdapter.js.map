{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "view", "UITransform", "ccclass", "property", "requireComponent", "CanvasAdapter", "onLoad", "_canvas", "getComponent", "_uiTransform", "alignCanvasWithScreen", "node", "setPosition", "setAnchorPoint", "console", "log", "onDestroy", "getCanvasInfo", "visibleSize", "getVisibleSize", "designSize", "getDesignResolutionSize", "canvasSize", "contentSize", "scaleX", "width", "scaleY", "height"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;;;;;;OAGxC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA0CP,U;AAEhD;AACA;AACA;AACA;;+BAGaQ,a,WAFZH,OAAO,CAAC,eAAD,C,UACPE,gBAAgB,CAACL,MAAD,C,wCADjB,MAEaM,aAFb,SAEmCP,SAFnC,CAE6C;AAAA;AAAA;;AAAA,2CAEf,IAFe;;AAAA,gDAGL,IAHK;AAAA;;AAK/BQ,QAAAA,MAAM,GAAS;AACrB,eAAKC,OAAL,GAAe,KAAKC,YAAL,CAAkBT,MAAlB,CAAf;AACA,eAAKU,YAAL,GAAoB,KAAKD,YAAL,CAAkBP,WAAlB,CAApB,CAFqB,CAIrB;;AACA,eAAKM,OAAL,CAAaG,qBAAb,GAAqC,KAArC,CALqB,CAOrB;;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EARqB,CAUrB;;AACA,cAAI,KAAKH,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBI,cAAlB,CAAiC,GAAjC,EAAsC,GAAtC;AACH;;AAEDC,UAAAA,OAAO,CAACC,GAAR,CAAa,6FAAb;AACH;;AAESC,QAAAA,SAAS,GAAS,CACxB;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,aAAa,GAAQ;AACxB,gBAAMC,WAAW,GAAGlB,IAAI,CAACmB,cAAL,EAApB;AACA,gBAAMC,UAAU,GAAGpB,IAAI,CAACqB,uBAAL,EAAnB;AACA,gBAAMC,UAAU,GAAG,KAAKb,YAAL,CAAkBc,WAArC;AAEA,iBAAO;AACHL,YAAAA,WAAW,EAAEA,WADV;AAEHE,YAAAA,UAAU,EAAEA,UAFT;AAGHE,YAAAA,UAAU,EAAEA,UAHT;AAIHE,YAAAA,MAAM,EAAEN,WAAW,CAACO,KAAZ,GAAoBL,UAAU,CAACK,KAJpC;AAKHC,YAAAA,MAAM,EAAER,WAAW,CAACS,MAAZ,GAAqBP,UAAU,CAACO;AALrC,WAAP;AAOH;;AA1CwC,O", "sourcesContent": ["import { _decorator, Component, Canvas, view, UITransform, sys, director } from 'cc';\nimport { ScreenAdapter } from './ScreenAdapter';\n\nconst { ccclass, property, requireComponent } = _decorator;\n\n/**\n * Canvas适配组件\n * 解决Canvas在不同分辨率下的点击偏移问题\n */\n@ccclass('CanvasAdapter')\n@requireComponent(Canvas)\nexport class CanvasAdapter extends Component {\n    \n    private _canvas: Canvas = null;\n    private _uiTransform: UITransform = null;\n    \n    protected onLoad(): void {\n        this._canvas = this.getComponent(Canvas);\n        this._uiTransform = this.getComponent(UITransform);\n\n        // 确保Canvas不自动对齐屏幕，避免坐标系统混乱\n        this._canvas.alignCanvasWithScreen = false;\n\n        // 确保Canvas位置为原点\n        this.node.setPosition(0, 0, 0);\n\n        // 确保Canvas的锚点为(0.5, 0.5)，这是默认值\n        if (this._uiTransform) {\n            this._uiTransform.setAnchorPoint(0.5, 0.5);\n        }\n\n        console.log(`[CanvasAdapter] Canvas适配完成: position=(0,0,0), anchor=(0.5,0.5), alignCanvasWithScreen=false`);\n    }\n    \n    protected onDestroy(): void {\n        // 移除了屏幕尺寸变化监听，简化处理\n    }\n    \n    /**\n     * 获取Canvas适配信息\n     */\n    public getCanvasInfo(): any {\n        const visibleSize = view.getVisibleSize();\n        const designSize = view.getDesignResolutionSize();\n        const canvasSize = this._uiTransform.contentSize;\n        \n        return {\n            visibleSize: visibleSize,\n            designSize: designSize,\n            canvasSize: canvasSize,\n            scaleX: visibleSize.width / designSize.width,\n            scaleY: visibleSize.height / designSize.height\n        };\n    }\n}\n"]}