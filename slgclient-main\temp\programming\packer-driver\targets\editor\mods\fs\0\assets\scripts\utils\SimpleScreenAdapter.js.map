{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "view", "sys", "director", "screen", "ResolutionPolicy", "GameConfig", "ccclass", "property", "SimpleScreenAdapter", "instance", "_instance", "onLoad", "console", "log", "onDestroy", "init", "windowSize", "screenWidth", "width", "screenHeight", "height", "screenRatio", "toFixed", "designWidth", "baseWidth", "designHeight", "baseHeight", "setDesignResolutionSize", "SHOW_ALL", "fixAllCanvas", "on", "EVENT_AFTER_SCENE_LAUNCH", "onSceneLaunched", "setupResizeListener", "scheduleOnce", "printFinalInfo", "scene", "getScene", "canvasComponents", "getComponentsInChildren", "for<PERSON>ach", "canvas", "index", "alignCanvasWithScreen", "node", "setPosition", "window", "addEventListener", "handleResize", "isMobile", "enableDebugInfo", "visibleSize", "getVisibleSize", "visible<PERSON><PERSON>in", "getVisibleOrigin", "designSize", "getDesignResolutionSize", "x", "y", "scaleX", "scaleY", "Math", "abs", "warn", "getAdaptInfo", "printDebugInfo"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,Q,OAAAA,Q;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,gB,OAAAA,gB;;AACnEC,MAAAA,U,iBAAAA,U;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;AAE9B;AACA;AACA;AACA;;qCAEaW,mB,WADZF,OAAO,CAAC,qBAAD,C,mCAAR,MACaE,mBADb,SACyCV,SADzC,CACmD;AAIrB,mBAARW,QAAQ,GAAwB;AAC9C,iBAAOD,mBAAmB,CAACE,SAA3B;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrBH,UAAAA,mBAAmB,CAACE,SAApB,GAAgC,IAAhC;AACAE,UAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,cAAIN,mBAAmB,CAACE,SAApB,KAAkC,IAAtC,EAA4C;AACxCF,YAAAA,mBAAmB,CAACE,SAApB,GAAgC,IAAhC;AACH;AACJ;AAED;AACJ;AACA;;;AACWK,QAAAA,IAAI,GAAS;AAChBH,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ,EADgB,CAGhB;;AACA,gBAAMG,UAAU,GAAGb,MAAM,CAACa,UAA1B;AACA,gBAAMC,WAAW,GAAGD,UAAU,CAACE,KAA/B;AACA,gBAAMC,YAAY,GAAGH,UAAU,CAACI,MAAhC;AACA,gBAAMC,WAAW,GAAGJ,WAAW,GAAGE,YAAlC;AAEAP,UAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8BI,WAAY,IAAGE,YAAa,EAAvE;AACAP,UAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8BQ,WAAW,CAACC,OAAZ,CAAoB,CAApB,CAAuB,EAAlE,EAVgB,CAYhB;;AACA,gBAAMC,WAAW,GAAG;AAAA;AAAA,wCAAWpB,MAAX,CAAkBqB,SAAtC;AACA,gBAAMC,YAAY,GAAG;AAAA;AAAA,wCAAWtB,MAAX,CAAkBuB,UAAvC;AAEA1B,UAAAA,IAAI,CAAC2B,uBAAL,CAA6BJ,WAA7B,EAA0CE,YAA1C,EAAwDrB,gBAAgB,CAACwB,QAAzE;AAEAhB,UAAAA,OAAO,CAACC,GAAR,CAAa,gCAA+BU,WAAY,IAAGE,YAAa,aAAxE,EAlBgB,CAoBhB;;AACA,eAAKI,YAAL,GArBgB,CAuBhB;;AACA3B,UAAAA,QAAQ,CAAC4B,EAAT,CAAY5B,QAAQ,CAAC6B,wBAArB,EAA+C,KAAKC,eAApD,EAAqE,IAArE,EAxBgB,CA0BhB;;AACA,eAAKC,mBAAL,GA3BgB,CA6BhB;;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAKC,cAAL;AACH,WAFD,EAEG,GAFH;AAIAvB,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ;AACH;AAED;AACJ;AACA;;;AACYmB,QAAAA,eAAe,CAACI,KAAD,EAAqB;AACxC,eAAKF,YAAL,CAAkB,MAAM;AACpB,iBAAKL,YAAL;AACH,WAFD,EAEG,CAFH;AAGH;AAED;AACJ;AACA;;;AACYA,QAAAA,YAAY,GAAS;AACzB,gBAAMO,KAAK,GAAGlC,QAAQ,CAACmC,QAAT,EAAd;AACA,cAAI,CAACD,KAAL,EAAY;AAEZ,gBAAME,gBAAgB,GAAGF,KAAK,CAACG,uBAAN,CAA8BxC,MAA9B,CAAzB;AAEAuC,UAAAA,gBAAgB,CAACE,OAAjB,CAAyB,CAACC,MAAD,EAASC,KAAT,KAAmB;AACxC9B,YAAAA,OAAO,CAACC,GAAR,CAAa,kCAAiC6B,KAAM,GAApD,EADwC,CAGxC;;AACAD,YAAAA,MAAM,CAACE,qBAAP,GAA+B,KAA/B,CAJwC,CAMxC;;AACAF,YAAAA,MAAM,CAACG,IAAP,CAAYC,WAAZ,CAAwB,CAAxB,EAA2B,CAA3B,EAA8B,CAA9B;AAEAjC,YAAAA,OAAO,CAACC,GAAR,CAAa,gCAA+B6B,KAAM,uDAAlD;AACH,WAVD;AAWH;AAED;AACJ;AACA;;;AACYT,QAAAA,mBAAmB,GAAS;AAChC;AACAa,UAAAA,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,MAAM;AACpC,iBAAKb,YAAL,CAAkB,MAAM;AACpBtB,cAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ;AACA,mBAAKmC,YAAL;AACH,aAHD,EAGG,GAHH;AAIH,WALD,EAFgC,CAShC;;AACA,cAAI/C,GAAG,CAACgD,QAAR,EAAkB;AACdH,YAAAA,MAAM,CAACC,gBAAP,CAAwB,mBAAxB,EAA6C,MAAM;AAC/C,mBAAKb,YAAL,CAAkB,MAAM;AACpBtB,gBAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ;AACA,qBAAKmC,YAAL;AACH,eAHD,EAGG,GAHH;AAIH,aALD;AAMH;AACJ;AAED;AACJ;AACA;;;AACYA,QAAAA,YAAY,GAAS;AACzB;AACA,eAAKjC,IAAL;AACH;AAED;AACJ;AACA;;;AACYoB,QAAAA,cAAc,GAAS;AAC3B,cAAI,CAAC;AAAA;AAAA,wCAAWhC,MAAX,CAAkB+C,eAAvB,EAAwC;AAExC,gBAAMlC,UAAU,GAAGb,MAAM,CAACa,UAA1B;AACA,gBAAMmC,WAAW,GAAGnD,IAAI,CAACoD,cAAL,EAApB;AACA,gBAAMC,aAAa,GAAGrD,IAAI,CAACsD,gBAAL,EAAtB;AACA,gBAAMC,UAAU,GAAGvD,IAAI,CAACwD,uBAAL,EAAnB;AAEA5C,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQG,UAAU,CAACE,KAAM,IAAGF,UAAU,CAACI,MAAO,EAA3D;AACAR,UAAAA,OAAO,CAACC,GAAR,CAAa,UAAS0C,UAAU,CAACrC,KAAM,IAAGqC,UAAU,CAACnC,MAAO,EAA5D;AACAR,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQsC,WAAW,CAACjC,KAAZ,CAAkBI,OAAlB,CAA0B,CAA1B,CAA6B,IAAG6B,WAAW,CAAC/B,MAAZ,CAAmBE,OAAnB,CAA2B,CAA3B,CAA8B,EAAnF;AACAV,UAAAA,OAAO,CAACC,GAAR,CAAa,UAASwC,aAAa,CAACI,CAAd,CAAgBnC,OAAhB,CAAwB,CAAxB,CAA2B,KAAI+B,aAAa,CAACK,CAAd,CAAgBpC,OAAhB,CAAwB,CAAxB,CAA2B,GAAhF,EAZ2B,CAc3B;;AACA,gBAAMqC,MAAM,GAAGR,WAAW,CAACjC,KAAZ,GAAoBqC,UAAU,CAACrC,KAA9C;AACA,gBAAM0C,MAAM,GAAGT,WAAW,CAAC/B,MAAZ,GAAqBmC,UAAU,CAACnC,MAA/C;AACAR,UAAAA,OAAO,CAACC,GAAR,CAAa,WAAU8C,MAAM,CAACrC,OAAP,CAAe,CAAf,CAAkB,OAAMsC,MAAM,CAACtC,OAAP,CAAe,CAAf,CAAkB,EAAjE,EAjB2B,CAmB3B;;AACA,cAAIuC,IAAI,CAACC,GAAL,CAAST,aAAa,CAACI,CAAvB,IAA4B,CAA5B,IAAiCI,IAAI,CAACC,GAAL,CAAST,aAAa,CAACK,CAAvB,IAA4B,CAAjE,EAAoE;AAChE9C,YAAAA,OAAO,CAACmD,IAAR,CAAc,iBAAgBV,aAAa,CAACI,CAAd,CAAgBnC,OAAhB,CAAwB,CAAxB,CAA2B,KAAI+B,aAAa,CAACK,CAAd,CAAgBpC,OAAhB,CAAwB,CAAxB,CAA2B,GAAxF;AACH;;AAED,cAAIqC,MAAM,GAAG,GAAT,IAAgBC,MAAM,GAAG,GAAzB,IAAgCD,MAAM,GAAG,CAAzC,IAA8CC,MAAM,GAAG,CAA3D,EAA8D;AAC1DhD,YAAAA,OAAO,CAACmD,IAAR,CAAc,gBAAeJ,MAAM,CAACrC,OAAP,CAAe,CAAf,CAAkB,OAAMsC,MAAM,CAACtC,OAAP,CAAe,CAAf,CAAkB,EAAvE;AACH;;AAEDV,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;AACH;AAED;AACJ;AACA;;;AACWmD,QAAAA,YAAY,GAAQ;AACvB,gBAAMhD,UAAU,GAAGb,MAAM,CAACa,UAA1B;AACA,gBAAMmC,WAAW,GAAGnD,IAAI,CAACoD,cAAL,EAApB;AACA,gBAAMC,aAAa,GAAGrD,IAAI,CAACsD,gBAAL,EAAtB;AACA,gBAAMC,UAAU,GAAGvD,IAAI,CAACwD,uBAAL,EAAnB;AAEA,iBAAO;AACHxC,YAAAA,UADG;AAEHuC,YAAAA,UAFG;AAGHJ,YAAAA,WAHG;AAIHE,YAAAA,aAJG;AAKHM,YAAAA,MAAM,EAAER,WAAW,CAACjC,KAAZ,GAAoBqC,UAAU,CAACrC,KALpC;AAMH0C,YAAAA,MAAM,EAAET,WAAW,CAAC/B,MAAZ,GAAqBmC,UAAU,CAACnC;AANrC,WAAP;AAQH;AAED;AACJ;AACA;;;AACW6C,QAAAA,cAAc,GAAS;AAC1B,eAAK9B,cAAL;AACH;;AAnL8C,O,wCAEC,I", "sourcesContent": ["import { _decorator, Component, Canvas, view, sys, director, Scene, screen, ResolutionPolicy } from 'cc';\nimport { GameConfig } from '../config/GameConfig';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 简单屏幕适配器\n * 专门解决点击偏移问题的轻量级方案\n */\n@ccclass('SimpleScreenAdapter')\nexport class SimpleScreenAdapter extends Component {\n    \n    private static _instance: SimpleScreenAdapter = null;\n    \n    public static get instance(): SimpleScreenAdapter {\n        return SimpleScreenAdapter._instance;\n    }\n    \n    protected onLoad(): void {\n        SimpleScreenAdapter._instance = this;\n        console.log('[SimpleScreenAdapter] 简单屏幕适配器初始化');\n    }\n    \n    protected onDestroy(): void {\n        if (SimpleScreenAdapter._instance === this) {\n            SimpleScreenAdapter._instance = null;\n        }\n    }\n    \n    /**\n     * 初始化屏幕适配\n     */\n    public init(): void {\n        console.log('[SimpleScreenAdapter] 开始初始化屏幕适配...');\n        \n        // 获取屏幕信息\n        const windowSize = screen.windowSize;\n        const screenWidth = windowSize.width;\n        const screenHeight = windowSize.height;\n        const screenRatio = screenWidth / screenHeight;\n        \n        console.log(`[SimpleScreenAdapter] 屏幕尺寸: ${screenWidth}x${screenHeight}`);\n        console.log(`[SimpleScreenAdapter] 屏幕比例: ${screenRatio.toFixed(3)}`);\n        \n        // 使用SHOW_ALL策略，确保内容完整显示且不变形\n        const designWidth = GameConfig.screen.baseWidth;\n        const designHeight = GameConfig.screen.baseHeight;\n        \n        view.setDesignResolutionSize(designWidth, designHeight, ResolutionPolicy.SHOW_ALL);\n        \n        console.log(`[SimpleScreenAdapter] 设计分辨率: ${designWidth}x${designHeight} (SHOW_ALL)`);\n        \n        // 修复所有Canvas设置\n        this.fixAllCanvas();\n        \n        // 监听场景切换\n        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);\n        \n        // 监听屏幕尺寸变化\n        this.setupResizeListener();\n        \n        // 打印最终信息\n        this.scheduleOnce(() => {\n            this.printFinalInfo();\n        }, 0.1);\n        \n        console.log('[SimpleScreenAdapter] 屏幕适配初始化完成');\n    }\n    \n    /**\n     * 场景启动后的回调\n     */\n    private onSceneLaunched(scene: Scene): void {\n        this.scheduleOnce(() => {\n            this.fixAllCanvas();\n        }, 0);\n    }\n    \n    /**\n     * 修复所有Canvas设置\n     */\n    private fixAllCanvas(): void {\n        const scene = director.getScene();\n        if (!scene) return;\n        \n        const canvasComponents = scene.getComponentsInChildren(Canvas);\n        \n        canvasComponents.forEach((canvas, index) => {\n            console.log(`[SimpleScreenAdapter] 修复Canvas[${index}]`);\n            \n            // 关键设置：禁用自动对齐屏幕\n            canvas.alignCanvasWithScreen = false;\n            \n            // 确保Canvas位置为原点\n            canvas.node.setPosition(0, 0, 0);\n            \n            console.log(`[SimpleScreenAdapter] Canvas[${index}] 修复完成: alignCanvasWithScreen=false, position=(0,0,0)`);\n        });\n    }\n    \n    /**\n     * 设置屏幕尺寸变化监听\n     */\n    private setupResizeListener(): void {\n        // 监听窗口尺寸变化\n        window.addEventListener('resize', () => {\n            this.scheduleOnce(() => {\n                console.log('[SimpleScreenAdapter] 检测到窗口尺寸变化，重新适配...');\n                this.handleResize();\n            }, 0.1);\n        });\n        \n        // 监听屏幕方向变化（移动设备）\n        if (sys.isMobile) {\n            window.addEventListener('orientationchange', () => {\n                this.scheduleOnce(() => {\n                    console.log('[SimpleScreenAdapter] 检测到屏幕方向变化，重新适配...');\n                    this.handleResize();\n                }, 0.2);\n            });\n        }\n    }\n    \n    /**\n     * 处理屏幕尺寸变化\n     */\n    private handleResize(): void {\n        // 重新初始化适配\n        this.init();\n    }\n    \n    /**\n     * 打印最终适配信息\n     */\n    private printFinalInfo(): void {\n        if (!GameConfig.screen.enableDebugInfo) return;\n        \n        const windowSize = screen.windowSize;\n        const visibleSize = view.getVisibleSize();\n        const visibleOrigin = view.getVisibleOrigin();\n        const designSize = view.getDesignResolutionSize();\n        \n        console.log('=== 简单屏幕适配最终信息 ===');\n        console.log(`窗口尺寸: ${windowSize.width}x${windowSize.height}`);\n        console.log(`设计分辨率: ${designSize.width}x${designSize.height}`);\n        console.log(`可视区域: ${visibleSize.width.toFixed(1)}x${visibleSize.height.toFixed(1)}`);\n        console.log(`可视原点: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);\n        \n        // 计算缩放比例\n        const scaleX = visibleSize.width / designSize.width;\n        const scaleY = visibleSize.height / designSize.height;\n        console.log(`缩放比例: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);\n        \n        // 检查是否有异常\n        if (Math.abs(visibleOrigin.x) > 1 || Math.abs(visibleOrigin.y) > 1) {\n            console.warn(`⚠️ 可视原点偏移异常: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);\n        }\n        \n        if (scaleX < 0.5 || scaleY < 0.5 || scaleX > 3 || scaleY > 3) {\n            console.warn(`⚠️ 缩放比例异常: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);\n        }\n        \n        console.log('============================');\n    }\n    \n    /**\n     * 获取当前适配信息\n     */\n    public getAdaptInfo(): any {\n        const windowSize = screen.windowSize;\n        const visibleSize = view.getVisibleSize();\n        const visibleOrigin = view.getVisibleOrigin();\n        const designSize = view.getDesignResolutionSize();\n        \n        return {\n            windowSize,\n            designSize,\n            visibleSize,\n            visibleOrigin,\n            scaleX: visibleSize.width / designSize.width,\n            scaleY: visibleSize.height / designSize.height\n        };\n    }\n    \n    /**\n     * 手动触发适配信息打印\n     */\n    public printDebugInfo(): void {\n        this.printFinalInfo();\n    }\n}\n"]}