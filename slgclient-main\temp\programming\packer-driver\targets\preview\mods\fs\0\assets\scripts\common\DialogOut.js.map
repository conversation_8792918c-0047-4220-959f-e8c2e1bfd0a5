{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts"], "names": ["_decorator", "Component", "Vec3", "tween", "ccclass", "property", "DialogOut", "onEnable", "console", "log", "node", "setScale", "lt", "to", "scale", "easing", "start"], "mappings": ";;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;;;;;;OAChC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;2BAIjBM,S,WADZF,OAAO,CAAC,WAAD,C,gBAAR,MACaE,SADb,SAC+BL,SAD/B,CACyC;AAErCM,QAAAA,QAAQ,GAAE;AACNC,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACA,eAAKC,IAAL,CAAUC,QAAV,CAAmB,IAAIT,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB,CAAnB;AACA,cAAIU,EAAE,GAAGT,KAAK,CAAC,KAAKO,IAAN,CAAL,CAAiBG,EAAjB,CAAoB,GAApB,EAAyB;AAAEC,YAAAA,KAAK,EAAE,IAAIZ,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,WAAzB,EAAuD;AAAEa,YAAAA,MAAM,EAAE;AAAV,WAAvD,CAAT;AACAH,UAAAA,EAAE,CAACI,KAAH;AACH;;AAPoC,O", "sourcesContent": ["\nimport { _decorator, Component, Vec3, tween } from 'cc';\nconst { ccclass, property } = _decorator;\n\n\n@ccclass('DialogOut')\nexport class DialogOut extends Component {\n    \n    onEnable(){\n        console.log(\"DialogOut onEnable\");\n        this.node.setScale(new Vec3(0.2, 0.2, 0.2));\n        let lt = tween(this.node).to(0.2, { scale: new Vec3(1, 1, 1) }, { easing: 'sineOut' })\n        lt.start();\n    }\n}\n\n"]}