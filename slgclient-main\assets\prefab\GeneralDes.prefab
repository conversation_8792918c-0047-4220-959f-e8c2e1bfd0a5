[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "GeneralDes", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 16}, {"__id__": 168}], "_active": true, "_components": [{"__id__": 337}, {"__id__": 339}, {"__id__": 341}], "_prefab": {"__id__": 343}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "nameLab", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": -305.799, "y": 220, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecsSRLzwhCiZ2piKe9sAVX"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4g+IuJCJODoHGIZQFAsb5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "441cG3boFF/b7DmpP2sFbr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4hSIpJBJOxLWYWhkRbDVq"}, {"__type__": "cc.Node", "_name": "general", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}], "_prefab": {"__id__": 15}, "_lpos": {"__type__": "cc.Vec3", "x": -312.27, "y": -44.408, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73jjdUXSRDKYO+914Qyv2v"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 14}, "_contentSize": {"__type__": "cc.Size", "width": 315, "height": 432}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fh9jGfT5ASpahFM2YSPXz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7fr/v+YJtE+43oH4azNXD4"}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 17}, {"__id__": 33}, {"__id__": 49}, {"__id__": 65}, {"__id__": 81}, {"__id__": 97}, {"__id__": 113}, {"__id__": 129}, {"__id__": 145}], "_active": true, "_components": [{"__id__": 161}, {"__id__": 163}, {"__id__": 165}], "_prefab": {"__id__": 167}, "_lpos": {"__type__": "cc.Vec3", "x": 179.135, "y": 70, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 18}], "_active": true, "_components": [{"__id__": 26}, {"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": -105, "y": 109, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}, {"__id__": 23}], "_prefab": {"__id__": 25}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ad4RAj4EFIeK6u9xBlhrdb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bWy/NcRVAios1iNS+6JDw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 24}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bZ2Ue8O1CCZPahXwJRfLw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "feRWu1679E7aPH56evOWYn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 27}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bTN84Ti5Dv42aDiSCvFrf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 29}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcIC4sqr5Km4MnhxMW9eZw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbBnDPr6VKULOWq17L/EZf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bdVj5Xju1PmYygQ4A1AWQ0"}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 34}], "_active": true, "_components": [{"__id__": 42}, {"__id__": 44}, {"__id__": 46}], "_prefab": {"__id__": 48}, "_lpos": {"__type__": "cc.Vec3", "x": 105, "y": 109, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 37}, {"__id__": 39}], "_prefab": {"__id__": 41}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 36}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15d5jj5qZLIofs4SN41gxf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 38}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9tGyajDhNLq9/NHJa1Tx9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 40}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7PV80wqRDhLdmjLy0zm/e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85rj55MbFL0qcyw5x95wsU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 43}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20O6lvpM9IvokKFM8oyZ4j"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 45}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "478o+uecxDapCkGSWS051/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 47}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33AD8Fix9EI5IKTCe+fVDC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8cUzXf7PpGzZ0Dt9DAh9mX"}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 50}], "_active": true, "_components": [{"__id__": 58}, {"__id__": 60}, {"__id__": 62}], "_prefab": {"__id__": 64}, "_lpos": {"__type__": "cc.Vec3", "x": -105, "y": 57, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 51}, {"__id__": 53}, {"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 52}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6a87+gBNNAF4wAMWZw8k9V"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 54}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4d6avSvyhPW5vF4yuqg7HE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 56}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5epavR9uNHv55enrOIJoN3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4JKaPI/ZF54Jh8Ypz2HAz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 59}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fCV45+tNOoJUYgVPKoKgm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 61}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fL3k9fNVFbLS03TQSahWS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 63}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6f0lR1Wk5Kvq57sXsw+Grx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7aEgtZM7VJ8qKqX/VIE/pv"}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 66}], "_active": true, "_components": [{"__id__": 74}, {"__id__": 76}, {"__id__": 78}], "_prefab": {"__id__": 80}, "_lpos": {"__type__": "cc.Vec3", "x": 105, "y": 57, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 65}, "_children": [], "_active": true, "_components": [{"__id__": 67}, {"__id__": 69}, {"__id__": 71}], "_prefab": {"__id__": 73}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 68}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46VwrQMpJIf4zn/FumoRCH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 70}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1ywzVwURPNrQJZ+18tt/a"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 72}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07kaQABbRP/avzusXd7ArU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6X5FGaVBAN6vj+vtxxB5E"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 75}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fxqO9EVtEeJKBCDUO1WAe"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 77}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73WzWyrxdG4pS8D0mvoYhl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 79}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adZpEdgW5Lap1D7lYieqyy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1A/Q3YVdNe4VJOJYzYmy9"}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 82}], "_active": true, "_components": [{"__id__": 90}, {"__id__": 92}, {"__id__": 94}], "_prefab": {"__id__": 96}, "_lpos": {"__type__": "cc.Vec3", "x": -105, "y": 5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 85}, {"__id__": 87}], "_prefab": {"__id__": 89}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 84}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90NV7HJkFN1Z+iwTmCeDhP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 86}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbiGnPmrRGSYMOAbF3uIHl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 88}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caztwHKsFI+6+mO8/wdl95"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a95CwxbcdA0rIrZnz+hT9X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 91}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02Cuix2lRMPZM7Vm34ndGo"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 93}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07OsPiG2lK0KkRLK+HaUmC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 95}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78sFhKB2NLW5DIl78w8J5M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2eBFFNfq9OAJM9eQipNQ4z"}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 98}], "_active": true, "_components": [{"__id__": 106}, {"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": 105, "y": 5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 99}, {"__id__": 101}, {"__id__": 103}], "_prefab": {"__id__": 105}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 100}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28t1CqKYFAeLSH0sPe2rT2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 102}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0adCNE64xJ+qUP8C5rzq5v"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 104}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0e9wL4QUFNMIj9a4EcKhxg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9fC9AOHbJHL4yco1nLPmIk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 107}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dYt5YUxlDE6NK6lYDvabA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 109}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eVOpdSANLG7aMX2ezbexJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 111}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feynTd+iVG8LT6qI0Hwkh9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4wXYEUH9EtKO/GCo2DpRJ"}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 114}], "_active": true, "_components": [{"__id__": 122}, {"__id__": 124}, {"__id__": 126}], "_prefab": {"__id__": 128}, "_lpos": {"__type__": "cc.Vec3", "x": -105, "y": -47, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 115}, {"__id__": 117}, {"__id__": 119}], "_prefab": {"__id__": 121}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 116}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30EH6W2rNOH5BirAAUAzjz"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 118}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10wY8zKQZMwYJ6tiCIRhaS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 120}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83W1FB/bJNjr0bgbpZnfk6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efH8jaNdZAiop+EGwuf/65"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 123}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5FNYN9ZxN54sIVKns0vrV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 125}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cDvCT7H9BlL5jqXLxmFkC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 127}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e61fyeqW1Hj6UuZv8IUTwn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "15LHmo71FBV5MdcPqVXy0N"}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 130}], "_active": true, "_components": [{"__id__": 138}, {"__id__": 140}, {"__id__": 142}], "_prefab": {"__id__": 144}, "_lpos": {"__type__": "cc.Vec3", "x": 105, "y": -47, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 129}, "_children": [], "_active": true, "_components": [{"__id__": 131}, {"__id__": 133}, {"__id__": 135}], "_prefab": {"__id__": 137}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "__prefab": {"__id__": 132}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4JU2CUfxGEaSEwF+TdLzK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "__prefab": {"__id__": 134}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "da7RsqHPlMAZePtRUKDnFl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5xN968lFF8IgXDFxfG63M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6hfF8Y2dPiore0yaKS0Y4"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 139}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97YMkq9ChEN4sbdaxrzBDk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 141}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeqTR38EtCmLmZM82tgsos"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 143}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbW6HjSXBLcKI9XedKb97s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "14HB5hSRNGpraPLxIWcj6e"}, {"__type__": "cc.Node", "_name": "bg_team_item4", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [{"__id__": 146}], "_active": true, "_components": [{"__id__": 154}, {"__id__": 156}, {"__id__": 158}], "_prefab": {"__id__": 160}, "_lpos": {"__type__": "cc.Vec3", "x": -105, "y": -99, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 145}, "_children": [], "_active": true, "_components": [{"__id__": 147}, {"__id__": 149}, {"__id__": 151}], "_prefab": {"__id__": 153}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 148}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27bJViwelKBb17fxjG2GuP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 150}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85vBuIH6tFXq/tV3nQ2uC9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 152}, "_contentSize": {"__type__": "cc.Size", "width": 51.38, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "838lfTdAtDhLfSPG0N1N/c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6eFTm8n91NyoWhGgeuWdJo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 155}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "812884e0-ebed-4ee0-a86a-372c8cec104b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beAakjoHpNv64yCTHGUkTq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 157}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8bNx6ANNInagfl0ZUCzC5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 159}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "897GY+b+xEsohQ8hUt1hys"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acaUbIiHVBgqepPSqnQjPe"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 162}, "_resizeMode": 0, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 10, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afSy8A7iFHZYOPo8oWzWic"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 164}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5+vmFb6tE3ZZDG3x4MAuU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 166}, "_contentSize": {"__type__": "cc.Size", "width": 410, "height": 260}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f39eRc+SFElZG8wX6siuk+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1eo+4so2NMFIB/5nrW3drG"}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 169}, {"__id__": 222}, {"__id__": 275}], "_active": true, "_components": [{"__id__": 328}, {"__id__": 330}, {"__id__": 332}, {"__id__": 334}], "_prefab": {"__id__": 336}, "_lpos": {"__type__": "cc.Vec3", "x": 177.568, "y": -179.207, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_objFlags": 0, "_parent": {"__id__": 168}, "_children": [{"__id__": 170}, {"__id__": 178}, {"__id__": 186}], "_active": true, "_components": [{"__id__": 215}, {"__id__": 217}, {"__id__": 219}], "_prefab": {"__id__": 221}, "_lpos": {"__type__": "cc.Vec3", "x": -134, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_old_jia_edd", "_objFlags": 0, "_parent": {"__id__": 169}, "_children": [], "_active": true, "_components": [{"__id__": 171}, {"__id__": 173}, {"__id__": 175}], "_prefab": {"__id__": 177}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 172}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4d960245-e403-4704-90a5-b9adb4761779@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "db7EnAebpIVIq3mArtwGMg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 174}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bcKP5JyRGLYFq7DSXxfad"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 176}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34HtiRRkFEupdhg72ceban"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "baTO8881JGzamrOZt2MZ3Q"}, {"__type__": "cc.Node", "_name": "skillNameLab", "_objFlags": 0, "_parent": {"__id__": 169}, "_children": [], "_active": true, "_components": [{"__id__": 179}, {"__id__": 181}, {"__id__": 183}], "_prefab": {"__id__": 185}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -78, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 180}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f32eZz0h9M8LvztpWtX/Ar"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 182}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fikb5mF5HY4ia1w/2I3jq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 184}, "_contentSize": {"__type__": "cc.Size", "width": 63.62, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a5gB0i29IY5ML/nUg3e8q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44tD03mNpOTqhvgqCKGKC+"}, {"__type__": "cc.Node", "_name": "SkillIcon", "_objFlags": 0, "_parent": {"__id__": 169}, "_children": [{"__id__": 187}, {"__id__": 195}], "_active": true, "_components": [{"__id__": 203}, {"__id__": 205}, {"__id__": 207}, {"__id__": 209}, {"__id__": 211}], "_prefab": {"__id__": 214}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.5}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "wrap", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 188}, {"__id__": 190}, {"__id__": 192}], "_prefab": {"__id__": 194}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 2}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 189}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "07005111-95b8-40fd-8b1b-713bbdec9bcf@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dMr8DbEhPArNDYgqlVoA/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 191}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0r0E/cMhAD5dz+qtmvR8A"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 193}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 148}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ee7n0rafdC66F1EWjqn2/1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 186}, "asset": {"__id__": 0}, "fileId": "85UxxkITpFP5N1LrFddxsu"}, {"__type__": "cc.Node", "_name": "lvLab", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 196}, {"__id__": 198}, {"__id__": 200}], "_prefab": {"__id__": 202}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 197}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aemx+yEIRAMJ39HYQBmYnA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 199}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eNS+h0dtLh72NBDP+k3rF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 201}, "_contentSize": {"__type__": "cc.Size", "width": 97.87, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "140dttAKFFxabtDPql0LIy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 186}, "asset": {"__id__": 0}, "fileId": "bd2cMHAR5PZYw3VxJtyugA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 204}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86MSp5/h5BnLeKnR9yDzjS"}, {"__type__": "11559QlfDNF86kg5ya1McF9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 206}, "sps": [{"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "890aca29-71c6-46c5-a44f-12d689ef6eca@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "0677877d-bde7-4a7a-9066-4bb82f9f6a38@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "46e9d6c6-eaf5-4b11-bc56-c67c9603fc57@f9941", "__expectedType__": "cc.SpriteFrame"}], "lvLab": {"__id__": 196}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12pv+UeX9O25/Wo3F1Pnb/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 208}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dAScZjuFG8oodkW9hRIg+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 210}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 148}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d50J94OAhOQK9g0hufJ5vA"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 212}, "clickEvents": [{"__id__": 213}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0uG3aHJNKoroglawnHS96"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "4986fQ2cPVDc5gTYaRI81t3", "handler": "onClickSkill", "customEventData": "0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 186}, "asset": {"__id__": 0}, "fileId": "77JHwWa6xA7ZU+2wP5PoX0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 169}, "_enabled": true, "__prefab": {"__id__": 216}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 59, "g": 51, "b": 51, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60Y5Rbo85MxKuIpye4dQvb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 169}, "_enabled": true, "__prefab": {"__id__": 218}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18z4yfYhxBSo10O9dsSBhy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 169}, "_enabled": true, "__prefab": {"__id__": 220}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dmuc1nShBGa+ic9jCPeVf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4VTLEL2VAhKXch+JcBIhN"}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_objFlags": 0, "_parent": {"__id__": 168}, "_children": [{"__id__": 223}, {"__id__": 231}, {"__id__": 239}], "_active": true, "_components": [{"__id__": 268}, {"__id__": 270}, {"__id__": 272}], "_prefab": {"__id__": 274}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_old_jia_edd", "_objFlags": 0, "_parent": {"__id__": 222}, "_children": [], "_active": true, "_components": [{"__id__": 224}, {"__id__": 226}, {"__id__": 228}], "_prefab": {"__id__": 230}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 225}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4d960245-e403-4704-90a5-b9adb4761779@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1XNgsji5Aq7h7i8zMI7CA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 227}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82+lvT7d1Haqkcqv1bKld+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 229}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eb1Isop5BN9K3tDI6K3S8Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0vl/ttutNE62OVoLnQxb6"}, {"__type__": "cc.Node", "_name": "skillNameLab", "_objFlags": 0, "_parent": {"__id__": 222}, "_children": [], "_active": true, "_components": [{"__id__": 232}, {"__id__": 234}, {"__id__": 236}], "_prefab": {"__id__": 238}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -78, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 233}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eevM4MvZCCJhYi/Z8pTE5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 235}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98lyn81stLpboIvxFWkkwy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 237}, "_contentSize": {"__type__": "cc.Size", "width": 63.62, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbiWhSeoxDmaV6uMMyaE4t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9c/SZqAo1CcqC8Y7ik6YE6"}, {"__type__": "cc.Node", "_name": "SkillIcon", "_objFlags": 0, "_parent": {"__id__": 222}, "_children": [{"__id__": 240}, {"__id__": 248}], "_active": true, "_components": [{"__id__": 256}, {"__id__": 258}, {"__id__": 260}, {"__id__": 262}, {"__id__": 264}], "_prefab": {"__id__": 267}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.8}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "wrap", "_objFlags": 0, "_parent": {"__id__": 239}, "_children": [], "_active": true, "_components": [{"__id__": 241}, {"__id__": 243}, {"__id__": 245}], "_prefab": {"__id__": 247}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 2}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "__prefab": {"__id__": 242}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "07005111-95b8-40fd-8b1b-713bbdec9bcf@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7pxjGButPQpiwOpYTFQsZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "__prefab": {"__id__": 244}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54yiO57HNIAb2mwj6vPwzo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 240}, "_enabled": true, "__prefab": {"__id__": 246}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 148}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7Mm5MzwZFF6oT5x8pgeNR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 239}, "asset": {"__id__": 0}, "fileId": "85UxxkITpFP5N1LrFddxsu"}, {"__type__": "cc.Node", "_name": "lvLab", "_objFlags": 0, "_parent": {"__id__": 239}, "_children": [], "_active": true, "_components": [{"__id__": 249}, {"__id__": 251}, {"__id__": 253}], "_prefab": {"__id__": 255}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 250}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8nuQmRe1Bi40sFPaN/H2I"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 252}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7KvKAfn5DfZMqEXbGPZfV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 254}, "_contentSize": {"__type__": "cc.Size", "width": 97.87, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8JbRt/+ZEDYr7fo/cF83x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 239}, "asset": {"__id__": 0}, "fileId": "bd2cMHAR5PZYw3VxJtyugA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "__prefab": {"__id__": 257}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54Bg/nyz5HCba5XYwsiEJN"}, {"__type__": "11559QlfDNF86kg5ya1McF9", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "__prefab": {"__id__": 259}, "sps": [{"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "890aca29-71c6-46c5-a44f-12d689ef6eca@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "0677877d-bde7-4a7a-9066-4bb82f9f6a38@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "46e9d6c6-eaf5-4b11-bc56-c67c9603fc57@f9941", "__expectedType__": "cc.SpriteFrame"}], "lvLab": {"__id__": 249}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91FvfwxitPgKfFB7cKivpC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "__prefab": {"__id__": 261}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "911HpnUfFPKacv/GvYPOwd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "__prefab": {"__id__": 263}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 148}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff0Z+I7RxL/azDxfXcIKz1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "__prefab": {"__id__": 265}, "clickEvents": [{"__id__": 266}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82+ufYB49E8YJKSuIGOnW5"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "4986fQ2cPVDc5gTYaRI81t3", "handler": "onClickSkill", "customEventData": "1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 239}, "asset": {"__id__": 0}, "fileId": "edhKcTka9PbKQd3qGElaw8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 269}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 59, "g": 51, "b": 51, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41F99kQF9KoYGQX7SG/960"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 271}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4pXO4ClJBS7ZcPT6gTH2K"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 273}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0bBWuyEVPyLQwTpnzg7Cf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8bCrV0EtBLN4eA8mIltmGZ"}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_objFlags": 0, "_parent": {"__id__": 168}, "_children": [{"__id__": 276}, {"__id__": 284}, {"__id__": 292}], "_active": true, "_components": [{"__id__": 321}, {"__id__": 323}, {"__id__": 325}], "_prefab": {"__id__": 327}, "_lpos": {"__type__": "cc.Vec3", "x": 134, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_old_jia_edd", "_objFlags": 0, "_parent": {"__id__": 275}, "_children": [], "_active": true, "_components": [{"__id__": 277}, {"__id__": 279}, {"__id__": 281}], "_prefab": {"__id__": 283}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 276}, "_enabled": true, "__prefab": {"__id__": 278}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4d960245-e403-4704-90a5-b9adb4761779@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbgsnMT6JMQZ+Rl8Zdz3Sd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 276}, "_enabled": true, "__prefab": {"__id__": 280}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "984f5ly7xNgrUydO+RoKij"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 276}, "_enabled": true, "__prefab": {"__id__": 282}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fTEMRghdBmKe6u/Pbddav"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "desd3amYxNy5Y/l8Ax4kTC"}, {"__type__": "cc.Node", "_name": "skillNameLab", "_objFlags": 0, "_parent": {"__id__": 275}, "_children": [], "_active": true, "_components": [{"__id__": 285}, {"__id__": 287}, {"__id__": 289}], "_prefab": {"__id__": 291}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -78, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 284}, "_enabled": true, "__prefab": {"__id__": 286}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61zfX3yZ9HsL73e/TBSur7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 284}, "_enabled": true, "__prefab": {"__id__": 288}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bfLtLdiBGDKtdohM7b46F"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 284}, "_enabled": true, "__prefab": {"__id__": 290}, "_contentSize": {"__type__": "cc.Size", "width": 63.62, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33Bk/KQ19NOoug+s5pPWkG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cE96GB0BBn4AiRtHM7ePx"}, {"__type__": "cc.Node", "_name": "SkillIcon", "_objFlags": 0, "_parent": {"__id__": 275}, "_children": [{"__id__": 293}, {"__id__": 301}], "_active": true, "_components": [{"__id__": 309}, {"__id__": 311}, {"__id__": 313}, {"__id__": 315}, {"__id__": 317}], "_prefab": {"__id__": 320}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.5}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "wrap", "_objFlags": 0, "_parent": {"__id__": 292}, "_children": [], "_active": true, "_components": [{"__id__": 294}, {"__id__": 296}, {"__id__": 298}], "_prefab": {"__id__": 300}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 2}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 295}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "07005111-95b8-40fd-8b1b-713bbdec9bcf@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1gQqnG1ZMgLiGLh7g1P/z"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 297}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67dYtuSeVEpbt367B2Utmp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 299}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 148}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26xTLGccBC+qF/+5aQONoF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 292}, "asset": {"__id__": 0}, "fileId": "85UxxkITpFP5N1LrFddxsu"}, {"__type__": "cc.Node", "_name": "lvLab", "_objFlags": 0, "_parent": {"__id__": 292}, "_children": [], "_active": true, "_components": [{"__id__": 302}, {"__id__": 304}, {"__id__": 306}], "_prefab": {"__id__": 308}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 301}, "_enabled": true, "__prefab": {"__id__": 303}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fauS++tI1G7bzoMaBpN1mP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 301}, "_enabled": true, "__prefab": {"__id__": 305}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "012exzwSlDPq1BScgrM077"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 301}, "_enabled": true, "__prefab": {"__id__": 307}, "_contentSize": {"__type__": "cc.Size", "width": 97.87, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1O9o1VztOYpVet3+S+qKQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 292}, "asset": {"__id__": 0}, "fileId": "bd2cMHAR5PZYw3VxJtyugA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 310}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fyQKigp9EV5Qi+enR7Y/Q"}, {"__type__": "11559QlfDNF86kg5ya1McF9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 312}, "sps": [{"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "890aca29-71c6-46c5-a44f-12d689ef6eca@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "0677877d-bde7-4a7a-9066-4bb82f9f6a38@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "46e9d6c6-eaf5-4b11-bc56-c67c9603fc57@f9941", "__expectedType__": "cc.SpriteFrame"}], "lvLab": {"__id__": 302}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e88asWDaBC/oDqRZayiONz"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 314}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9HupdcINFlJiYBG/KAsY7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 316}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 148}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d8BuoHp5C76Gb63ueNids"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 318}, "clickEvents": [{"__id__": 319}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "674df64a-e247-4e99-a87a-80298656fc11@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd+xw9sdVKC7ae0LJEf5LY"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "4986fQ2cPVDc5gTYaRI81t3", "handler": "onClickSkill", "customEventData": "2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 292}, "asset": {"__id__": 0}, "fileId": "23e5t6UNhBpp6JTBRsatqX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 322}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 59, "g": 51, "b": 51, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26czs+Ua5Pxb5BDGI47uzE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 324}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63fMw0SblNQLPaelBx3riF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 326}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5evmJgZudLqaJD8BlUWscL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38E8nJtMNI/rP3FzonDTzo"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 329}, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 6, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 4, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "desJZvZnBMJp6WZWVqTP4K"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 331}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 73, "g": 62, "b": 62, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff9kptVlFII6G599P17h+Z"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 333}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eCeMgOsNDQZzzSPQL9Uzb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 335}, "_contentSize": {"__type__": "cc.Size", "width": 410, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6bZmbstRHg4/OY6YGIyDB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7wsJ7ofdHjaIF9XHrK0Xc"}, {"__type__": "4986fQ2cPVDc5gTYaRI81t3", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 338}, "nameLab": {"__id__": 3}, "srollLayout": {"__id__": 161}, "lvLabel": {"__id__": 19}, "foreLabel": {"__id__": 35}, "defenseLabel": {"__id__": 51}, "speedLabel": {"__id__": 67}, "strategyLabel": {"__id__": 83}, "destroyLabel": {"__id__": 99}, "expLabel": {"__id__": 115}, "powerLabel": {"__id__": 131}, "costLabel": {"__id__": 147}, "generalItemPrefab": {"__uuid__": "474e595e-61cf-48e7-ba58-8e0aaaa96134", "__expectedType__": "cc.Prefab"}, "generalItemParent": {"__id__": 10}, "skillIcons": [{"__id__": 186}, {"__id__": 239}, {"__id__": 292}], "skillNameLab": [{"__id__": 179}, {"__id__": 232}, {"__id__": 285}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86JGsGMS9PWa0q07SoUI/j"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 340}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62ZFYgQiNOqLdrUnHS9aKv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 342}, "_contentSize": {"__type__": "cc.Size", "width": 1200, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aRtKsimVFeZnmekcdbLjp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38/BpsMZ5Ay5N3xklOyu2l", "targetOverrides": [{"__id__": 344}, {"__id__": 346}, {"__id__": 349}, {"__id__": 352}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 337}, "sourceInfo": null, "propertyPath": ["skillIcons", "0"], "target": {"__id__": 186}, "targetInfo": {"__id__": 345}}, {"__type__": "cc.TargetInfo", "localID": ["deioNKiRJNirhRn6c+JSKm"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 186}, "sourceInfo": {"__id__": 347}, "propertyPath": ["lvLab"], "target": {"__id__": 186}, "targetInfo": {"__id__": 348}}, {"__type__": "cc.TargetInfo", "localID": ["44szvr0XtH1pxRAbPBIVBS"]}, {"__type__": "cc.TargetInfo", "localID": ["11/WPH0BdPN69bbn2GBBrh"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 239}, "sourceInfo": {"__id__": 350}, "propertyPath": ["lvLab"], "target": {"__id__": 239}, "targetInfo": {"__id__": 351}}, {"__type__": "cc.TargetInfo", "localID": ["44szvr0XtH1pxRAbPBIVBS"]}, {"__type__": "cc.TargetInfo", "localID": ["11/WPH0BdPN69bbn2GBBrh"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 292}, "sourceInfo": {"__id__": 353}, "propertyPath": ["lvLab"], "target": {"__id__": 292}, "targetInfo": {"__id__": 354}}, {"__type__": "cc.TargetInfo", "localID": ["44szvr0XtH1pxRAbPBIVBS"]}, {"__type__": "cc.TargetInfo", "localID": ["11/WPH0BdPN69bbn2GBBrh"]}]