{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts"], "names": ["_decorator", "Component", "EditBox", "MapCommand", "MapUtil", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "SmallMapLogic", "onLoad", "on", "mapEenterChange", "onMapCenterChange", "scrollToMap", "onScrollToMap", "updateView", "onDestroy", "targetOff", "centerPoint", "getInstance", "proxy", "getCurCenterPoint", "editBoxX", "string", "x", "toString", "editBoxY", "y", "onClickJump", "instance", "playClick", "Number", "mapSize", "width", "height", "emit", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;;AAGzBC,MAAAA,U;;AACAC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBASTU,a,WADpBF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACP,OAAD,C,UAERO,QAAQ,CAACP,OAAD,C,oCAJb,MACqBQ,aADrB,SAC2CT,SAD3C,CACqD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,0CAMtB,EANsB;;AAAA,0CAOtB,EAPsB;AAAA;;AASvCU,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,eAAvB,EAAwC,KAAKC,iBAA7C,EAAgE,IAAhE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AACA,eAAKC,UAAL;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESF,QAAAA,UAAU,GAAG;AACnB,cAAIG,WAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,KAAzB,CAA+BC,iBAA/B,EAAvB;;AACA,cAAIH,WAAJ,EAAiB;AACb,iBAAKI,QAAL,CAAcC,MAAd,GAAuBL,WAAW,CAACM,CAAZ,CAAcC,QAAd,EAAvB;AACA,iBAAKC,QAAL,CAAcH,MAAd,GAAuBL,WAAW,CAACS,CAAZ,CAAcF,QAAd,EAAvB;AACH;AACJ;;AAESb,QAAAA,iBAAiB,GAAS;AAChC,eAAKG,UAAL;AACH;;AAESD,QAAAA,aAAa,GAAQ;AAC3B,eAAKC,UAAL;AACH;;AAEDa,QAAAA,WAAW,GAAS;AAChB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,cAAIN,CAAS,GAAGO,MAAM,CAAC,KAAKT,QAAL,CAAcC,MAAf,CAAtB;AACA,cAAII,CAAS,GAAGI,MAAM,CAAC,KAAKL,QAAL,CAAcH,MAAf,CAAtB;;AACA,cAAIC,CAAC,IAAI,CAAL,IACGG,CAAC,IAAI,CADR,IAEGH,CAAC,GAAG;AAAA;AAAA,kCAAQQ,OAAR,CAAgBC,KAFvB,IAGGN,CAAC,GAAG;AAAA;AAAA,kCAAQK,OAAR,CAAgBE,MAH3B,EAGmC;AAC3B;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWtB,WAAzB,EAAsCW,CAAtC,EAAyCG,CAAzC;AACP,WALD,MAKO;AACHS,YAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsBb,CAAtB,EAAyBG,CAAzB;AACH;AACJ;;AA/CgD,O;;;;;iBAE7B,I;;;;;;;iBAEA,I", "sourcesContent": ["import { _decorator, Component, EditBox, Node, Vec2 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport MapCommand from \"../MapCommand\";\nimport MapUtil from \"../MapUtil\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('SmallMapLogic')\nexport default class SmallMapLogic extends Component {\n    @property(EditBox)\n    editBoxX: EditBox = null;\n    @property(EditBox)\n    editBoxY: EditBox = null;\n\n    protected _armys: Node[] = [];\n    protected _citys: Node[] = [];\n\n    protected onLoad(): void {\n        EventMgr.on(LogicEvent.mapEenterChange, this.onMapCenterChange, this);\n        EventMgr.on(LogicEvent.scrollToMap, this.onScrollToMap, this);\n        this.updateView();\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n    }\n\n    protected updateView() {\n        let centerPoint:Vec2 = MapCommand.getInstance().proxy.getCurCenterPoint();\n        if (centerPoint) {\n            this.editBoxX.string = centerPoint.x.toString();\n            this.editBoxY.string = centerPoint.y.toString();\n        }\n    }\n\n    protected onMapCenterChange(): void {\n        this.updateView();\n    }\n\n    protected onScrollToMap():void {\n        this.updateView();\n    }\n\n    onClickJump(): void {\n        AudioManager.instance.playClick();\n        let x: number = Number(this.editBoxX.string);\n        let y: number = Number(this.editBoxY.string);\n        if (x >= 0 \n            && y >= 0 \n            && x < MapUtil.mapSize.width \n            && y < MapUtil.mapSize.height) {\n                EventMgr.emit(LogicEvent.scrollToMap, x, y);\n        } else {\n            console.log(\"跳转无效位置\", x, y);\n        }\n    }\n}\n"]}