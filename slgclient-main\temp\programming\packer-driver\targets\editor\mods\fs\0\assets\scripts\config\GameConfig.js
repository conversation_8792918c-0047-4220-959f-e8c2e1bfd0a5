System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, GameConfig;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c7559IVL1JBup4UY18YGBnF", "GameConfig", undefined);

      _export("GameConfig", GameConfig = {
        serverUrl: "ws://127.0.0.1:8004",
        webUrl: "http://127.0.0.1:8088",
        // 屏幕适配配置
        screen: {
          // 响应式设计基准尺寸
          baseWidth: 1080,
          // 基准宽度（用于计算缩放比例）
          baseHeight: 1920,
          // 基准高度（用于计算缩放比例）
          // 响应式设计开关
          enableResponsiveDesign: true,
          // 启用响应式设计模式
          // 缩放限制
          minScale: 0.5,
          // 最小缩放比例
          maxScale: 2.0,
          // 最大缩放比例
          // 其他配置
          enableSafeArea: true,
          // 是否启用安全区域适配
          enableDebugInfo: true,
          // 是否显示适配调试信息（开发阶段开启，发布时关闭）
          enableClickTest: true // 是否启用点击测试助手

        }
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=GameConfig.js.map