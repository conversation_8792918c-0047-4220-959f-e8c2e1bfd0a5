{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts"], "names": ["_decorator", "Component", "Node", "Prefab", "instantiate", "ArmyCommand", "MapCommand", "ArmySelectItemLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "ArmySelectNodeLogic", "onLoad", "on", "closeArmyAelectUi", "onClickBack", "onDestroy", "targetOff", "instance", "playClick", "node", "active", "setData", "cmd", "x", "y", "armyContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myCity", "getInstance", "cityProxy", "getMyMainCity", "armyList", "proxy", "getArmyList", "cityId", "i", "length", "generals", "item", "itemPrefab", "parent", "getComponent", "setArmyData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAGvCC,MAAAA,W;;AAGAC,MAAAA,U;;AACAC,MAAAA,mB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OATH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAYTa,mB,WADpBF,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACT,MAAD,C,oCAJb,MACqBU,mBADrB,SACiDZ,SADjD,CAC2D;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAM7Ca,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,iBAAvB,EAA0C,KAAKC,WAA/C,EAA4D,IAA5D;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESF,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaG,QAAb,CAAsBC,SAAtB;AACA,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAEMC,QAAAA,OAAO,CAACC,GAAD,EAAcC,CAAd,EAAyBC,CAAzB,EAA0C;AACpD,eAAKC,aAAL,CAAmBC,iBAAnB;AACA,cAAIC,MAAmB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAA1B;AACA,cAAIC,QAAoB,GAAG;AAAA;AAAA,0CAAYH,WAAZ,GAA0BI,KAA1B,CAAgCC,WAAhC,CAA4CN,MAAM,CAACO,MAAnD,CAA3B;;AACA,eAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGJ,QAAQ,CAACK,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,gBAAIJ,QAAQ,CAACI,CAAD,CAAR,IAAeJ,QAAQ,CAACI,CAAD,CAAR,CAAYE,QAAZ,CAAqB,CAArB,IAA0B,CAA7C,EAAgD;AAC5C,kBAAIC,IAAU,GAAGrC,WAAW,CAAC,KAAKsC,UAAN,CAA5B;AACAD,cAAAA,IAAI,CAACE,MAAL,GAAc,KAAKf,aAAnB;AACAa,cAAAA,IAAI,CAACG,YAAL;AAAA;AAAA,8DAAuCC,WAAvC,CAAmDX,QAAQ,CAACI,CAAD,CAA3D,EAAgEb,GAAhE,EAAqEC,CAArE,EAAwEC,CAAxE;AACH;AACJ;AACJ;;AA9BsD,O;;;;;iBAEjC,I;;;;;;;iBAED,I", "sourcesContent": ["import { _decorator, Component, Node, Prefab, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport { ArmyData } from \"../../general/ArmyProxy\";\nimport { MapCityData } from \"../MapCityProxy\";\nimport MapCommand from \"../MapCommand\";\nimport ArmySelectItemLogic from \"./ArmySelectItemLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('ArmySelectNodeLogic')\nexport default class ArmySelectNodeLogic extends Component {\n    @property(Node)\n    armyContainer: Node = null;\n    @property(Prefab)\n    itemPrefab: Prefab = null;\n    \n    protected onLoad(): void {\n        EventMgr.on(LogicEvent.closeArmyAelectUi, this.onClickBack, this);\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n    }\n\n    protected onClickBack(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n    }\n\n    public setData(cmd: number, x: number, y: number): void {\n        this.armyContainer.removeAllChildren();\n        let myCity: MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let armyList: ArmyData[] = ArmyCommand.getInstance().proxy.getArmyList(myCity.cityId);\n        for (let i: number = 0; i < armyList.length; i++) {\n            if (armyList[i] && armyList[i].generals[0] > 0) {\n                let item: Node = instantiate(this.itemPrefab);\n                item.parent = this.armyContainer;\n                item.getComponent(ArmySelectItemLogic).setArmyData(armyList[i], cmd, x, y);\n            }\n        }\n    }\n}\n"]}