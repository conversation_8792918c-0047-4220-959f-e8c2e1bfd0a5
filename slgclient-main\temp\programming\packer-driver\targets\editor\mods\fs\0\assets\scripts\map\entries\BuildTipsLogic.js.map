{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts"], "names": ["_decorator", "Component", "Sprite", "Node", "Label", "DateUtil", "MapCommand", "EventMgr", "ccclass", "property", "BuildTipsLogic", "onLoad", "onEnable", "giveUpNode", "active", "_warFreeTime", "getInstance", "proxy", "getWarFree", "onDisable", "_data", "unscheduleAllCallbacks", "targetOff", "setBuildData", "data", "updateUI", "diff", "getServerTime", "occupyTime", "isShow", "rid", "warFreeSprite", "node", "stopWarFree", "schedule", "countDownWarFree", "cityProxy", "myId", "startGiveUp", "unschedule", "stopGiveUp", "updateGiveUpTime", "isInGiveUp", "giveUpLabTime", "string", "leftTimeStr", "giveUpTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AAGvCC,MAAAA,Q;;AAEAC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAQTU,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACP,MAAD,C,UAIRO,QAAQ,CAACN,IAAD,C,UAERM,QAAQ,CAACL,KAAD,C,oCARb,MACqBM,cADrB,SAC4CT,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA,yCAGlB,IAHkB;;AAAA,gDAIjB,CAJiB;;AAAA;;AAAA;AAAA;;AAUxCU,QAAAA,MAAM,GAAS,CAExB;;AACSC,QAAAA,QAAQ,GAAS;AACvB,eAAKC,UAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACA,eAAKC,YAAL,GAAoB;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,KAAzB,CAA+BC,UAA/B,EAApB;AACH;;AACSC,QAAAA,SAAS,GAAS;AACxB,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKC,sBAAL;AACA;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACOC,QAAAA,YAAY,CAACC,IAAD,EAA2B;AAC3C,eAAKJ,KAAL,GAAaI,IAAb;AACA,eAAKC,QAAL;AACF;;AACKA,QAAAA,QAAQ,GAAS;AACpB,cAAI,KAAKL,KAAT,EAAgB;AAChB,gBAAIM,IAAI,GAAG;AAAA;AAAA,sCAASC,aAAT,KAA2B,KAAKP,KAAL,CAAWQ,UAAjD;;AACA,gBAAIC,MAAM,GAAGH,IAAI,GAAC,KAAKX,YAAV,IAA0B,KAAKK,KAAL,CAAWU,GAAX,GAAiB,CAAxD;AACA,iBAAKC,aAAL,CAAmBC,IAAnB,CAAwBlB,MAAxB,GAAiCe,MAAjC;;AAEA,gBAAIA,MAAJ,EAAW;AACX,mBAAKI,WAAL;AACA,mBAAKC,QAAL,CAAc,KAAKC,gBAAnB,EAAqC,GAArC;AACA,mBAAKA,gBAAL;AACC;;AACD,gBAAG,KAAKf,KAAL,CAAWU,GAAX,IAAkB;AAAA;AAAA,0CAAWd,WAAX,GAAyBoB,SAAzB,CAAmCC,IAAxD,EAA6D;AAC7D,mBAAKC,WAAL;AACC;AACA;AACJ;;AACMH,QAAAA,gBAAgB,GAAG;AACtB,cAAIT,IAAI,GAAG;AAAA;AAAA,oCAASC,aAAT,KAA2B,KAAKP,KAAL,CAAWQ,UAAjD;;AACA,cAAIF,IAAI,GAAC,KAAKX,YAAd,EAA2B;AAC3B,iBAAKkB,WAAL;AACA,iBAAKF,aAAL,CAAmBC,IAAnB,CAAwBlB,MAAxB,GAAiC,KAAjC;AACC;AACJ;;AACMmB,QAAAA,WAAW,GAAG;AACjB,eAAKM,UAAL,CAAgB,KAAKJ,gBAArB;AACH;;AACSG,QAAAA,WAAW,GAAE;AACnB,eAAKE,UAAL;AACA,eAAKN,QAAL,CAAc,KAAKO,gBAAnB,EAAqC,CAArC;AACA,eAAKA,gBAAL;AACH;;AACSD,QAAAA,UAAU,GAAE;AAClB,eAAKD,UAAL,CAAgB,KAAKE,gBAArB;AACA,eAAK5B,UAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACH;;AACS2B,QAAAA,gBAAgB,GAAE;AACxB,cAAI,KAAKrB,KAAL,CAAWsB,UAAX,MAA2B,KAA/B,EAAqC;AACrC,iBAAKF,UAAL;AACC,WAFD,MAEK;AACL,iBAAK3B,UAAL,CAAgBC,MAAhB,GAAyB,IAAzB;AACA,iBAAK6B,aAAL,CAAmBC,MAAnB,GAA4B;AAAA;AAAA,sCAASC,WAAT,CAAqB,KAAKzB,KAAL,CAAW0B,UAAhC,CAA5B;AACC;AACJ;;AApEiD,O;;;;;iBAEnB,I;;;;;;;iBAIL,I;;;;;;;iBAEI,I", "sourcesContent": ["import { _decorator, Component, Sprite, Node, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport DateUtil from \"../../utils/DateUtil\";\nimport { MapBuildData } from \"../MapBuildProxy\";\nimport MapCommand from \"../MapCommand\";\nimport { EventMgr } from '../../utils/EventMgr';\n\n@ccclass('BuildTipsLogic')\nexport default class BuildTipsLogic extends Component {\n    @property(Sprite)\n    warFreeSprite: Sprite | null = null;\n    protected _data: MapBuildData = null;\n    protected _warFreeTime: number = 0;\n    @property(Node)\n    giveUpNode: Node | null = null;\n    @property(Label)\n    giveUpLabTime: Label | null = null;\n \n    protected onLoad(): void {\n\n    }\n    protected onEnable(): void {\n        this.giveUpNode.active = false;\n        this._warFreeTime = MapCommand.getInstance().proxy.getWarFree();\n    }\n    protected onDisable(): void {\n        this._data = null;\n        this.unscheduleAllCallbacks();\n        EventMgr.targetOff(this);\n    }\n     public setBuildData(data: MapBuildData): void {\n        this._data = data;\n        this.updateUI();\n     }\n    public updateUI(): void {\n        if (this._data) {\n        var diff = DateUtil.getServerTime() - this._data.occupyTime;\n        var isShow = diff<this._warFreeTime && this._data.rid > 0;\n        this.warFreeSprite.node.active = isShow;\n\n        if (isShow){\n        this.stopWarFree();\n        this.schedule(this.countDownWarFree, 1.0);\n        this.countDownWarFree();\n        }\n        if(this._data.rid == MapCommand.getInstance().cityProxy.myId){\n        this.startGiveUp();\n        }\n        }\n    }\n    public countDownWarFree() {\n        var diff = DateUtil.getServerTime() - this._data.occupyTime;\n        if (diff>this._warFreeTime){\n        this.stopWarFree();\n        this.warFreeSprite.node.active = false;\n        }\n    }\n    public stopWarFree() {\n        this.unschedule(this.countDownWarFree);\n    }\n    protected startGiveUp(){\n        this.stopGiveUp();\n        this.schedule(this.updateGiveUpTime, 1);\n        this.updateGiveUpTime();\n    }\n    protected stopGiveUp(){\n        this.unschedule(this.updateGiveUpTime);\n        this.giveUpNode.active = false;\n    }\n    protected updateGiveUpTime(){\n        if (this._data.isInGiveUp() == false){\n        this.stopGiveUp();\n        }else{\n        this.giveUpNode.active = true;\n        this.giveUpLabTime.string = DateUtil.leftTimeStr(this._data.giveUpTime);\n        }\n    }\n}\n\n"]}