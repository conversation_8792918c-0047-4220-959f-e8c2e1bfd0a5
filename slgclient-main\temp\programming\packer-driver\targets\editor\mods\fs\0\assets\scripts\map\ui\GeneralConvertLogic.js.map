{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "Node", "Prefab", "instantiate", "UITransform", "Vec3", "GeneralCommand", "GeneralItemLogic", "GeneralItemType", "EventMgr", "AudioManager", "LogicEvent", "ListLogic", "ccclass", "property", "GeneralConvertLogic", "Map", "onEnable", "initGeneralCfg", "on", "openGeneralSelect", "onSelectGeneral", "general<PERSON><PERSON><PERSON>", "onGeneralConvert", "onDisable", "targetOff", "onClickClose", "node", "active", "instance", "playClick", "emit", "openGeneral", "list", "getInstance", "proxy", "getMyGeneralsNotUse", "listTemp", "concat", "for<PERSON>ach", "item", "type", "GeneralSelect", "i", "length", "_cunGeneral", "indexOf", "id", "splice", "comp", "scrollView", "getComponent", "setData", "cfgData", "curData", "has", "_upMap", "obj", "get", "parent", "delete", "g", "_selectMap", "select", "size", "generalPrefab", "width", "height", "scale", "contentNode", "set", "msg", "showToast", "add_gold", "clear", "onClickOK", "keys", "ids", "Array", "from", "convert"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAESA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAG7EC,MAAAA,c;;AACAC,MAAAA,gB;AAAoBC,MAAAA,e,iBAAAA,e;;AAClBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,S;;;;;;;OAPD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;yBAUTiB,mB,WADpBF,OAAO,CAAC,qBAAD,C,UAGHC,QAAQ,CAACd,UAAD,C,UAGRc,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAACZ,MAAD,C,oCATb,MACqBa,mBADrB,SACiDhB,SADjD,CAC2D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,+CAWxB,EAXwB;;AAAA,0CAapB,IAAIiB,GAAJ,EAboB;;AAAA,8CAehB,IAAIA,GAAJ,EAfgB;AAAA;;AAiB7CC,QAAAA,QAAQ,GAAO;AACtB,eAAKC,cAAL;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,iBAAvB,EAA0C,KAAKC,eAA/C,EAAgE,IAAhE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,cAAvB,EAAuC,KAAKC,gBAA5C,EAA8D,IAA9D;AACF;;AAGSC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,WAAzB;AACH;;AAESd,QAAAA,cAAc,GAAO;AAE3B,cAAIe,IAAU,GAAG;AAAA;AAAA,gDAAeC,WAAf,GAA6BC,KAA7B,CAAmCC,mBAAnC,EAAjB;AACA,cAAIC,QAAQ,GAAGJ,IAAI,CAACK,MAAL,EAAf;AAEAD,UAAAA,QAAQ,CAACE,OAAT,CAAiBC,IAAI,IAAI;AACrBA,YAAAA,IAAI,CAACC,IAAL,GAAY;AAAA;AAAA,oDAAgBC,aAA5B;AACH,WAFD;;AAKA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGN,QAAQ,CAACO,MAA5B,EAAoCD,CAAC,EAArC,EAAwC;AACpC,gBAAG,KAAKE,WAAL,CAAiBC,OAAjB,CAAyBT,QAAQ,CAACM,CAAD,CAAR,CAAYI,EAArC,KAA4C,CAA/C,EAAkD;AAC9CV,cAAAA,QAAQ,CAACW,MAAT,CAAgBL,CAAhB,EAAkB,CAAlB;AACAA,cAAAA,CAAC;AACJ;AACJ;;AAED,cAAIM,IAAI,GAAG,KAAKC,UAAL,CAAgBvB,IAAhB,CAAqBwB,YAArB;AAAA;AAAA,qCAAX;AACAF,UAAAA,IAAI,CAACG,OAAL,CAAaf,QAAb;AACH;;AAEShB,QAAAA,eAAe,CAACgC,OAAD,EAAeC,OAAf,EAA6B3B,IAA7B,EAA8C;AACnE;AAEA,cAAI4B,GAAG,GAAG,KAAKC,MAAL,CAAYD,GAAZ,CAAgBD,OAAO,CAACP,EAAxB,CAAV;;AACA,cAAIQ,GAAJ,EAAQ;AACJ,gBAAIE,GAAG,GAAG,KAAKD,MAAL,CAAYE,GAAZ,CAAgBJ,OAAO,CAACP,EAAxB,CAAV;;AACAU,YAAAA,GAAG,CAACE,MAAJ,GAAa,IAAb;;AACA,iBAAKH,MAAL,CAAYI,MAAZ,CAAmBN,OAAO,CAACP,EAA3B;;AAEA,gBAAIc,CAAC,GAAG,KAAKC,UAAL,CAAgBJ,GAAhB,CAAoBJ,OAAO,CAACP,EAA5B,CAAR;;AACA,gBAAIc,CAAJ,EAAM;AACFA,cAAAA,CAAC,CAACV,YAAF;AAAA;AAAA,wDAAiCY,MAAjC,CAAwC,KAAxC;AACH;AAEJ,WAVD,MAUK;AAED,gBAAI,KAAKP,MAAL,CAAYQ,IAAZ,IAAoB,CAAxB,EAA0B;AACtBrC,cAAAA,IAAI,CAACwB,YAAL;AAAA;AAAA,wDAAoCY,MAApC,CAA2C,KAA3C;AACA;AACH;;AAED,gBAAIF,CAAM,GAAG1D,WAAW,CAAC,KAAK8D,aAAN,CAAxB;AACAJ,YAAAA,CAAC,CAACV,YAAF;AAAA;AAAA,sDAAiCC,OAAjC,CAAyCE,OAAzC,EAAmD;AAAA;AAAA,oDAAgBZ,aAAnE;AACAmB,YAAAA,CAAC,CAACV,YAAF;AAAA;AAAA,sDAAiCY,MAAjC,CAAwC,IAAxC;AACAF,YAAAA,CAAC,CAACV,YAAF,CAAe/C,WAAf,EAA4B8D,KAA5B,IAAoC,GAApC;AACAL,YAAAA,CAAC,CAACV,YAAF,CAAe/C,WAAf,EAA4B+D,MAA5B,IAAoC,GAApC;AACAN,YAAAA,CAAC,CAACO,KAAF,GAAU,IAAI/D,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB,CAAV;AACAwD,YAAAA,CAAC,CAACF,MAAF,GAAW,KAAKU,WAAhB;;AACA,iBAAKb,MAAL,CAAYc,GAAZ,CAAgBhB,OAAO,CAACP,EAAxB,EAA4Bc,CAA5B;;AACA,iBAAKC,UAAL,CAAgBQ,GAAhB,CAAoBhB,OAAO,CAACP,EAA5B,EAAgCpB,IAAhC;AACH;AACJ;;AAGSJ,QAAAA,gBAAgB,CAACgD,GAAD,EAAc;AACpC;AAAA;AAAA,oCAASxC,IAAT,CAAc;AAAA;AAAA,wCAAWyC,SAAzB,EAAoC,UAAQD,GAAG,CAACE,QAAhD;;AACA,eAAKjB,MAAL,CAAYjB,OAAZ,CAAqBsB,CAAD,IAAY;AAC5BA,YAAAA,CAAC,CAACF,MAAF,GAAW,IAAX;AACH,WAFD;;AAIA,eAAKH,MAAL,CAAYkB,KAAZ;;AACA,eAAKZ,UAAL,CAAgBY,KAAhB;;AAEA,eAAKxD,cAAL;AACH;;AAESyD,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,4CAAa9C,QAAb,CAAsBC,SAAtB;;AACA,cAAI8C,IAAI,GAAG,KAAKpB,MAAL,CAAYoB,IAAZ,EAAX;;AACA,cAAIC,GAAG,GAAGC,KAAK,CAACC,IAAN,CAAWH,IAAX,CAAV;AACA;AAAA;AAAA,gDAAe1C,WAAf,GAA6B8C,OAA7B,CAAqCH,GAArC;AACH;;AA1GsD,O;;;;;iBAG/B,I;;;;;;;iBAGL,I;;;;;;;iBAGH,I", "sourcesContent": ["\n\nimport { _decorator, Component, ScrollView, Node, Prefab, instantiate, UITransform, Vec3 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport GeneralItemLogic, { GeneralItemType } from \"./GeneralItemLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\nimport ListLogic from '../../utils/ListLogic';\n\n@ccclass('GeneralConvertLogic')\nexport default class GeneralConvertLogic extends Component {\n\n    @property(ScrollView)\n    scrollView:ScrollView = null;\n\n    @property(Node)\n    contentNode:Node = null;\n\n    @property(Prefab)\n    generalPrefab = null;\n\n    private _cunGeneral:number[] = [];\n\n    private _upMap:Map<number, Node> = new Map<number, Node>();\n\n    private _selectMap:Map<number, Node> = new Map<number, Node>();\n\n    protected onEnable():void{\n       this.initGeneralCfg();\n       EventMgr.on(LogicEvent.openGeneralSelect, this.onSelectGeneral, this);\n       EventMgr.on(LogicEvent.generalConvert, this.onGeneralConvert, this);\n    }\n\n\n    protected onDisable():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.openGeneral);\n    }\n\n    protected initGeneralCfg():void{\n\n        let list:any[] = GeneralCommand.getInstance().proxy.getMyGeneralsNotUse();\n        let listTemp = list.concat();\n\n        listTemp.forEach(item => {\n            item.type = GeneralItemType.GeneralSelect;\n        })\n\n\n        for(var i = 0; i < listTemp.length ;i++){\n            if(this._cunGeneral.indexOf(listTemp[i].id) >= 0 ){\n                listTemp.splice(i,1);\n                i--;\n            }\n        }\n\n        var comp = this.scrollView.node.getComponent(ListLogic);\n        comp.setData(listTemp);\n    }\n\n    protected onSelectGeneral(cfgData: any, curData: any, node:Node): void {\n        //console.log(\"curData:\", curData, this._upMap.size);\n\n        var has = this._upMap.has(curData.id);\n        if (has){\n            var obj = this._upMap.get(curData.id);\n            obj.parent = null;\n            this._upMap.delete(curData.id);\n\n            var g = this._selectMap.get(curData.id);\n            if (g){\n                g.getComponent(GeneralItemLogic).select(false);\n            }\n\n        }else{\n\n            if (this._upMap.size >= 9){\n                node.getComponent(GeneralItemLogic).select(false);\n                return\n            }\n\n            var g:Node = instantiate(this.generalPrefab);\n            g.getComponent(GeneralItemLogic).setData(curData,  GeneralItemType.GeneralSelect);\n            g.getComponent(GeneralItemLogic).select(true);\n            g.getComponent(UITransform).width *=0.5;\n            g.getComponent(UITransform).height*=0.5;\n            g.scale = new Vec3(0.5, 0.5, 0.5);\n            g.parent = this.contentNode;\n            this._upMap.set(curData.id, g);\n            this._selectMap.set(curData.id, node);\n        }\n    }\n\n   \n    protected onGeneralConvert(msg:any):void{\n        EventMgr.emit(LogicEvent.showToast, \"获得金币:\"+msg.add_gold);\n        this._upMap.forEach((g:Node) => {\n            g.parent = null;\n        });\n\n        this._upMap.clear();\n        this._selectMap.clear();\n\n        this.initGeneralCfg();\n    }\n\n    protected onClickOK():void{\n        AudioManager.instance.playClick();\n        var keys = this._upMap.keys();\n        var ids = Array.from(keys);\n        GeneralCommand.getInstance().convert(ids);\n    }\n    \n\n}\n"]}