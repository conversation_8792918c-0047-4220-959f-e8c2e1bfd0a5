import { _decorator, Component, Can<PERSON>, director, Scene } from 'cc';

const { ccclass, property } = _decorator;

/**
 * Canvas修复器
 * 在运行时修复所有Canvas的alignCanvasWithScreen设置
 */
@ccclass('CanvasFixer')
export class CanvasFixer extends Component {
    
    protected onLoad(): void {
        // 修复当前场景的Canvas
        this.fixCurrentSceneCanvas();
        
        // 监听场景切换，修复新场景的Canvas
        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);
    }
    
    protected onDestroy(): void {
        director.off(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);
    }
    
    /**
     * 场景启动后的回调
     */
    private onSceneLaunched(scene: Scene): void {
        this.scheduleOnce(() => {
            this.fixSceneCanvas(scene);
        }, 0);
    }
    
    /**
     * 修复当前场景的Canvas
     */
    private fixCurrentSceneCanvas(): void {
        const scene = director.getScene();
        if (scene) {
            this.fixSceneCanvas(scene);
        }
    }
    
    /**
     * 修复指定场景的Canvas
     */
    private fixSceneCanvas(scene: Scene): void {
        // 查找场景中的所有Canvas组件
        const canvasComponents = scene.getComponentsInChildren(Canvas);
        
        canvasComponents.forEach((canvas, index) => {
            console.log(`[CanvasFixer] 检查Canvas[${index}]: alignCanvasWithScreen=${canvas.alignCanvasWithScreen}`);

            // 强制设置为false，无论当前值是什么
            canvas.alignCanvasWithScreen = false;

            // 确保Canvas位置为原点
            canvas.node.setPosition(0, 0, 0);

            console.log(`[CanvasFixer] 修复Canvas[${index}]: alignCanvasWithScreen=false, position=(0,0,0)`);

            // 不再自动添加CanvasAdapter，避免冲突
        });
    }
}
