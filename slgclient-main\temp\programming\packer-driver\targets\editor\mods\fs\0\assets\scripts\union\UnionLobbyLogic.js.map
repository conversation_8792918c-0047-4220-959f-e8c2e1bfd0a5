{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "UnionCommand", "EventMgr", "ListLogic", "LogicEvent", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "on", "updateUnionList", "updateUnion", "onDestroy", "targetOff", "data", "comp", "scrollView", "node", "getComponent", "list", "getInstance", "proxy", "getUnionList", "setData", "onEnable", "unionList"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;;AAGzBC,MAAAA,Y;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;AACEC,MAAAA,U,iBAAAA,U;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBASTS,e,WADpBF,OAAO,CAAC,iBAAD,C,UAEHC,QAAQ,CAACN,UAAD,C,oCAFb,MACqBO,eADrB,SAC6CR,SAD7C,CACuD;AAAA;AAAA;;AAAA;AAAA;;AAGzCS,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,eAAvB,EAAuC,KAAKC,WAA5C,EAAwD,IAAxD;AACH;;AAESC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACSF,QAAAA,WAAW,CAACG,IAAD,EAAY;AAC7B,cAAIC,IAAI,GAAG,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBC,YAArB;AAAA;AAAA,qCAAX;AACA,cAAIC,IAAY,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,YAAjC,EAAnB;AACAP,UAAAA,IAAI,CAACQ,OAAL,CAAaJ,IAAb;AACH;;AACSK,QAAAA,QAAQ,GAAO;AAErB;AAAA;AAAA,4CAAaJ,WAAb,GAA2BK,SAA3B;AACH;;AAlBkD,O;;;;;iBAEpB,I", "sourcesContent": ["import { _decorator, Component, ScrollView } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport UnionCommand from \"./UnionCommand\";\nimport { Union } from \"./UnionProxy\";\nimport { EventMgr } from '../utils/EventMgr';\nimport ListLogic from '../utils/ListLogic';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('UnionLobbyLogic')\nexport default class UnionLobbyLogic extends Component {\n    @property(ScrollView)\n    scrollView:ScrollView | null = null;\n    protected onLoad():void{\n        EventMgr.on(LogicEvent.updateUnionList,this.updateUnion,this);\n    }\n    \n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n    protected updateUnion(data:any[]){\n        var comp = this.scrollView.node.getComponent(ListLogic);\n        var list:Union[] = UnionCommand.getInstance().proxy.getUnionList();\n        comp.setData(list);\n    }\n    protected onEnable():void{\n \n        UnionCommand.getInstance().unionList();\n    }\n  \n}\n\n"]}