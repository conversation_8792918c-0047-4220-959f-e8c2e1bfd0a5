{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts"], "names": ["MapCityData", "MapCityProxy", "MapUtil", "EventMgr", "LogicEvent", "equalsServerData", "data", "cityId", "rid", "name", "x", "y", "is<PERSON><PERSON>", "is_main", "level", "curDurable", "cur_durable", "maxDurable", "unionId", "union_id", "parentId", "parent_id", "unionName", "union_name", "occupyTime", "occupy_time", "createCityData", "id", "cityData", "city", "max_durable", "getCellRadius", "Map", "initData", "_mapCitys", "length", "mapCellCount", "_lastCityCellIds", "clear", "updateMyCityIds", "clearData", "addCityData", "cellId", "mapSize", "width", "removeCityData", "checkAndRemoveCityCell", "initMyCitys", "citys", "_myCitys", "i", "getIdByCellPoint", "unshift", "push", "updateCity", "myId", "myUnionId", "myParentId", "setMapScanBlock", "scanDatas", "areaId", "cBuilds", "mc_builds", "lastCityCellIds", "has", "get", "cityCellIds", "addCityCellIds", "updateCityCellIds", "removeCityCellIds", "areaIndex", "getAreaIdByCellPoint", "index", "indexOf", "splice", "set", "emit", "updateCitys", "getCity", "getMyMainCity", "isMyCity", "getMyCityById", "getMyCitys", "getMyPlayerId"], "mappings": ";;;6DAOaA,W,EA8DQC,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;AApEdC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;AAGT;6BACaJ,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA,sCACR,CADQ;;AAAA,0CAEJ,CAFI;;AAAA,uCAGP,CAHO;;AAAA,wCAIN,EAJM;;AAAA,qCAKT,IALS;;AAAA,qCAMT,CANS;;AAAA,0CAOJ,CAPI;;AAAA,yCAQL,CARK;;AAAA,8CASA,CATA;;AAAA,8CAUA,CAVA;;AAAA,2CAWJ,CAXI;;AAAA,4CAYH,CAZG;;AAAA,6CAaF,EAbE;;AAAA;AAAA;;AAgBdK,QAAAA,gBAAgB,CAACC,IAAD,EAAY;AAC/B,cAAI,KAAKC,MAAL,IAAeD,IAAI,CAACC,MAApB,IACG,KAAKC,GAAL,IAAYF,IAAI,CAACE,GADpB,IAEG,KAAKC,IAAL,IAAaH,IAAI,CAACG,IAFrB,IAGG,KAAKC,CAAL,IAAUJ,IAAI,CAACI,CAHlB,IAIG,KAAKC,CAAL,IAAUL,IAAI,CAACK,CAJlB,IAKG,KAAKC,MAAL,IAAeN,IAAI,CAACO,OALvB,IAMG,KAAKC,KAAL,IAAcR,IAAI,CAACQ,KANtB,IAOG,KAAKC,UAAL,IAAmBT,IAAI,CAACU,WAP3B,IAQG,KAAKC,UAAL,IAAmBX,IAAI,CAACW,UAR3B,IASG,KAAKC,OAAL,IAAgBZ,IAAI,CAACa,QATxB,IAUG,KAAKC,QAAL,IAAiBd,IAAI,CAACe,SAVzB,IAWG,KAAKC,SAAL,IAAkBhB,IAAI,CAACiB,UAX1B,IAYG,KAAKC,UAAL,IAAmBlB,IAAI,CAACmB,WAZ/B,EAY4C;AACxC,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAE2B,eAAdC,cAAc,CAACpB,IAAD,EAAYqB,EAAZ,EAAwBC,QAAqB,GAAG,IAAhD,EAAmE;AAC3F,cAAIC,IAAiB,GAAGD,QAAxB;;AACA,cAAIA,QAAQ,IAAI,IAAhB,EAAsB;AAClBC,YAAAA,IAAI,GAAG,IAAI7B,WAAJ,EAAP;AACH;;AACD6B,UAAAA,IAAI,CAACF,EAAL,GAAUA,EAAV;AACAE,UAAAA,IAAI,CAACtB,MAAL,GAAcD,IAAI,CAACC,MAAnB;AACAsB,UAAAA,IAAI,CAACrB,GAAL,GAAWF,IAAI,CAACE,GAAhB;AACAqB,UAAAA,IAAI,CAACpB,IAAL,GAAYH,IAAI,CAACG,IAAjB;AACAoB,UAAAA,IAAI,CAACnB,CAAL,GAASJ,IAAI,CAACI,CAAd;AACAmB,UAAAA,IAAI,CAAClB,CAAL,GAASL,IAAI,CAACK,CAAd;AACAkB,UAAAA,IAAI,CAACjB,MAAL,GAAcN,IAAI,CAACO,OAAnB;AACAgB,UAAAA,IAAI,CAACf,KAAL,GAAaR,IAAI,CAACQ,KAAlB;AACAe,UAAAA,IAAI,CAACd,UAAL,GAAkBT,IAAI,CAACU,WAAvB;AACAa,UAAAA,IAAI,CAACZ,UAAL,GAAkBX,IAAI,CAACwB,WAAvB;AACAD,UAAAA,IAAI,CAACX,OAAL,GAAeZ,IAAI,CAACa,QAApB;AACAU,UAAAA,IAAI,CAACT,QAAL,GAAgBd,IAAI,CAACe,SAArB;AACAQ,UAAAA,IAAI,CAACP,SAAL,GAAiBhB,IAAI,CAACiB,UAAtB;AACAM,UAAAA,IAAI,CAACL,UAAL,GAAkBlB,IAAI,CAACmB,WAAvB;AACA,iBAAOI,IAAP;AACH;;AAEME,QAAAA,aAAa,GAAW;AAC3B,iBAAO,CAAP;AACH;;AA3DoB,O;;yBA8DJ9B,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,6CACO,EADP;;AAAA,oDAEsB,IAAI+B,GAAJ,EAFtB;;AAAA,4CAGM,EAHN;;AAAA,wCAIR,CAJQ;;AAAA,6CAKH,CALG;;AAAA,8CAMF,CANE;AAAA;;AAQ9B;AACOC,QAAAA,QAAQ,GAAS;AACpB,eAAKC,SAAL,CAAeC,MAAf,GAAwB;AAAA;AAAA,kCAAQC,YAAhC;;AACA,eAAKC,gBAAL,CAAsBC,KAAtB;;AACA,eAAKC,eAAL;AACH;;AAEMC,QAAAA,SAAS,GAAS;AACrB,eAAKN,SAAL,CAAeC,MAAf,GAAwB,CAAxB;;AACA,eAAKE,gBAAL,CAAsBC,KAAtB;AACH;;AAEMG,QAAAA,WAAW,CAACnC,IAAD,EAAYoC,MAAZ,EAAkC;AAChD,cAAId,QAAqB,GAAG5B,WAAW,CAAC0B,cAAZ,CAA2BpB,IAA3B,EAAiCoC,MAAjC,EAAyC,KAAKR,SAAL,CAAeQ,MAAf,CAAzC,CAA5B;AACA,eAAKR,SAAL,CAAeQ,MAAf,IAAyBd,QAAzB;AACA,eAAKM,SAAL,CAAeQ,MAAM,GAAG,CAAxB,IAA6Bd,QAA7B;AACA,eAAKM,SAAL,CAAeQ,MAAM,GAAG,CAAxB,IAA6Bd,QAA7B;AACA,eAAKM,SAAL,CAAeQ,MAAM,GAAG;AAAA;AAAA,kCAAQC,OAAR,CAAgBC,KAAxC,IAAiDhB,QAAjD;AACA,eAAKM,SAAL,CAAeQ,MAAM,GAAG;AAAA;AAAA,kCAAQC,OAAR,CAAgBC,KAAzB,GAAiC,CAAhD,IAAqDhB,QAArD;AACA,eAAKM,SAAL,CAAeQ,MAAM,GAAG;AAAA;AAAA,kCAAQC,OAAR,CAAgBC,KAAzB,GAAiC,CAAhD,IAAqDhB,QAArD;AACA,eAAKM,SAAL,CAAeQ,MAAM,GAAG;AAAA;AAAA,kCAAQC,OAAR,CAAgBC,KAAxC,IAAiDhB,QAAjD;AACA,eAAKM,SAAL,CAAeQ,MAAM,GAAG;AAAA;AAAA,kCAAQC,OAAR,CAAgBC,KAAzB,GAAiC,CAAhD,IAAqDhB,QAArD;AACA,eAAKM,SAAL,CAAeQ,MAAM,GAAG;AAAA;AAAA,kCAAQC,OAAR,CAAgBC,KAAzB,GAAiC,CAAhD,IAAqDhB,QAArD;AACH;;AAEMiB,QAAAA,cAAc,CAACH,MAAD,EAAuB;AACxC,cAAId,QAAqB,GAAG,KAAKM,SAAL,CAAeQ,MAAf,CAA5B;;AACA,cAAId,QAAJ,EAAc;AACV,iBAAKM,SAAL,CAAeQ,MAAf,IAAyB,IAAzB;AACA,iBAAKI,sBAAL,CAA4BJ,MAAM,GAAG,CAArC,EAAwCd,QAAQ,CAACrB,MAAjD;AACA,iBAAKuC,sBAAL,CAA4BJ,MAAM,GAAG,CAArC,EAAwCd,QAAQ,CAACrB,MAAjD;AACA,iBAAKuC,sBAAL,CAA4BJ,MAAM,GAAG;AAAA;AAAA,oCAAQC,OAAR,CAAgBC,KAArD,EAA4DhB,QAAQ,CAACrB,MAArE;AACA,iBAAKuC,sBAAL,CAA4BJ,MAAM,GAAG;AAAA;AAAA,oCAAQC,OAAR,CAAgBC,KAAzB,GAAiC,CAA7D,EAAgEhB,QAAQ,CAACrB,MAAzE;AACA,iBAAKuC,sBAAL,CAA4BJ,MAAM,GAAG;AAAA;AAAA,oCAAQC,OAAR,CAAgBC,KAAzB,GAAiC,CAA7D,EAAgEhB,QAAQ,CAACrB,MAAzE;AACA,iBAAKuC,sBAAL,CAA4BJ,MAAM,GAAG;AAAA;AAAA,oCAAQC,OAAR,CAAgBC,KAArD,EAA4DhB,QAAQ,CAACrB,MAArE;AACA,iBAAKuC,sBAAL,CAA4BJ,MAAM,GAAG;AAAA;AAAA,oCAAQC,OAAR,CAAgBC,KAAzB,GAAiC,CAA7D,EAAgEhB,QAAQ,CAACrB,MAAzE;AACA,iBAAKuC,sBAAL,CAA4BJ,MAAM,GAAG;AAAA;AAAA,oCAAQC,OAAR,CAAgBC,KAAzB,GAAiC,CAA7D,EAAgEhB,QAAQ,CAACrB,MAAzE;AACH;AACJ;;AAEMuC,QAAAA,sBAAsB,CAACJ,MAAD,EAAiBnC,MAAjB,EAA0C;AACnE,cAAIqB,QAAqB,GAAG,KAAKM,SAAL,CAAeQ,MAAf,CAA5B;;AACA,cAAId,QAAQ,IAAIA,QAAQ,CAACrB,MAAT,IAAmBA,MAAnC,EAA2C;AACvC,iBAAK2B,SAAL,CAAe3B,MAAf,IAAyB,IAAzB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;;;AACOwC,QAAAA,WAAW,CAACC,KAAD,EAAqB;AACnC,eAAKC,QAAL,CAAcd,MAAd,GAAuB,CAAvB;;AACA,eAAK,IAAIe,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGF,KAAK,CAACb,MAAlC,EAA0Ce,CAAC,EAA3C,EAA+C;AAC3C,gBAAIvB,EAAU,GAAG;AAAA;AAAA,oCAAQwB,gBAAR,CAAyBH,KAAK,CAACE,CAAD,CAAL,CAASxC,CAAlC,EAAqCsC,KAAK,CAACE,CAAD,CAAL,CAASvC,CAA9C,CAAjB;AACA,gBAAIkB,IAAiB,GAAG7B,WAAW,CAAC0B,cAAZ,CAA2BsB,KAAK,CAACE,CAAD,CAAhC,EAAqCvB,EAArC,CAAxB;;AACA,gBAAIE,IAAI,CAACjB,MAAT,EAAiB;AACb,mBAAKqC,QAAL,CAAcG,OAAd,CAAsBvB,IAAtB;AACH,aAFD,MAEO;AACH,mBAAKoB,QAAL,CAAcI,IAAd,CAAmBxB,IAAnB;AACH;AACJ;AACJ;AAED;;;AACOU,QAAAA,eAAe,GAAS;AAC3B,eAAK,IAAIW,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKD,QAAL,CAAcd,MAA1C,EAAkDe,CAAC,EAAnD,EAAuD;AACnD,gBAAIvB,EAAU,GAAG;AAAA;AAAA,oCAAQwB,gBAAR,CAAyB,KAAKF,QAAL,CAAcC,CAAd,EAAiBxC,CAA1C,EAA6C,KAAKuC,QAAL,CAAcC,CAAd,EAAiBvC,CAA9D,CAAjB;AACA,iBAAKsC,QAAL,CAAcC,CAAd,EAAiBvB,EAAjB,GAAsBA,EAAtB;AACA,iBAAKO,SAAL,CAAeP,EAAf,IAAqB,KAAKsB,QAAL,CAAcC,CAAd,CAArB;AACH;AAEJ;AAED;;;AACOI,QAAAA,UAAU,CAACzB,IAAD,EAAyB;AACtC,cAAIF,EAAU,GAAG;AAAA;AAAA,kCAAQwB,gBAAR,CAAyBtB,IAAI,CAACnB,CAA9B,EAAiCmB,IAAI,CAAClB,CAAtC,CAAjB;AACA,cAAIiB,QAAqB,GAAG,IAA5B;;AACA,cAAI,KAAKM,SAAL,CAAeP,EAAf,KAAsB,IAA1B,EAAgC;AAC5B;AACAC,YAAAA,QAAQ,GAAG5B,WAAW,CAAC0B,cAAZ,CAA2BG,IAA3B,EAAiCF,EAAjC,CAAX;AACA,iBAAKO,SAAL,CAAeP,EAAf,IAAqBC,QAArB;;AACA,gBAAIC,IAAI,CAACrB,GAAL,IAAY,KAAK+C,IAArB,EAA2B;AACvB,mBAAKN,QAAL,CAAcI,IAAd,CAAmBzB,QAAnB;;AACA,mBAAK4B,SAAL,GAAiB5B,QAAQ,CAACV,OAA1B;AACA,mBAAKuC,UAAL,GAAkB7B,QAAQ,CAACR,QAA3B;AACH;AACJ,WATD,MASO;AACHQ,YAAAA,QAAQ,GAAG5B,WAAW,CAAC0B,cAAZ,CAA2BG,IAA3B,EAAiCF,EAAjC,EAAqC,KAAKO,SAAL,CAAeP,EAAf,CAArC,CAAX;;AACA,gBAAIE,IAAI,CAACrB,GAAL,IAAY,KAAK+C,IAArB,EAA2B;AACvB,mBAAKC,SAAL,GAAiB5B,QAAQ,CAACV,OAA1B;AACA,mBAAKuC,UAAL,GAAkB7B,QAAQ,CAACR,QAA3B;AACH;AACJ;;AACD,iBAAOQ,QAAP;AACH;;AAEM8B,QAAAA,eAAe,CAACC,SAAD,EAAiBC,MAAc,GAAG,CAAlC,EAA2C;AAC7D,cAAIC,OAAc,GAAGF,SAAS,CAACG,SAA/B;;AACA,cAAID,OAAO,CAAC1B,MAAR,GAAiB,CAArB,EAAwB;AACpB,gBAAI4B,eAAyB,GAAG,IAAhC;;AACA,gBAAI,KAAK1B,gBAAL,CAAsB2B,GAAtB,CAA0BJ,MAA1B,CAAJ,EAAuC;AACnCG,cAAAA,eAAe,GAAG,KAAK1B,gBAAL,CAAsB4B,GAAtB,CAA0BL,MAA1B,CAAlB;AACH;;AACD,gBAAIM,WAAqB,GAAG,EAA5B;AACA,gBAAIC,cAAwB,GAAG,EAA/B;AACA,gBAAIC,iBAA2B,GAAG,EAAlC;AACA,gBAAIC,iBAA2B,GAAG,EAAlC;;AACA,iBAAK,IAAInB,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGW,OAAO,CAAC1B,MAApC,EAA4Ce,CAAC,EAA7C,EAAiD;AAC7C,kBAAIoB,SAAiB,GAAG;AAAA;AAAA,sCAAQC,oBAAR,CAA6BV,OAAO,CAACX,CAAD,CAAP,CAAWxC,CAAxC,EAA2CmD,OAAO,CAACX,CAAD,CAAP,CAAWvC,CAAtD,CAAxB;;AACA,kBAAI2D,SAAS,IAAIV,MAAjB,EAAyB;AACrB;AACA;AACH;;AACD,kBAAIlB,MAAc,GAAG;AAAA;AAAA,sCAAQS,gBAAR,CAAyBU,OAAO,CAACX,CAAD,CAAP,CAAWxC,CAApC,EAAuCmD,OAAO,CAACX,CAAD,CAAP,CAAWvC,CAAlD,CAArB;AACAuD,cAAAA,WAAW,CAACb,IAAZ,CAAiBX,MAAjB;;AACA,kBAAIqB,eAAJ,EAAqB;AACjB,oBAAIS,KAAa,GAAGT,eAAe,CAACU,OAAhB,CAAwB/B,MAAxB,CAApB;;AACA,oBAAI8B,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACA,sBAAI,KAAKtC,SAAL,CAAeQ,MAAf,EAAuBrC,gBAAvB,CAAwCwD,OAAO,CAACX,CAAD,CAA/C,KAAuD,KAA3D,EAAkE;AAC9D;AACA,yBAAKT,WAAL,CAAiBoB,OAAO,CAACX,CAAD,CAAxB,EAA6BR,MAA7B;AACA0B,oBAAAA,iBAAiB,CAACf,IAAlB,CAAuBX,MAAvB;AACH;;AACDqB,kBAAAA,eAAe,CAACW,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B,EAPa,CAOoB;;AACjC;AACH;AACJ,eApB4C,CAqB7C;;;AACA,mBAAK/B,WAAL,CAAiBoB,OAAO,CAACX,CAAD,CAAxB,EAA6BR,MAA7B;AACAyB,cAAAA,cAAc,CAACd,IAAf,CAAoBX,MAApB;AACH;;AACD,gBAAIqB,eAAe,IAAIA,eAAe,CAAC5B,MAAhB,GAAyB,CAAhD,EAAmD;AAC/C;AACAkC,cAAAA,iBAAiB,GAAGN,eAApB;;AACA,mBAAK,IAAIb,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGmB,iBAAiB,CAAClC,MAA9C,EAAsDe,CAAC,EAAvD,EAA2D;AACvD,qBAAKL,cAAL,CAAoBwB,iBAAiB,CAACnB,CAAD,CAArC;AACH;AACJ;;AACD,iBAAKb,gBAAL,CAAsBsC,GAAtB,CAA0Bf,MAA1B,EAAkCM,WAAlC;;AACA,gBAAIC,cAAc,CAAChC,MAAf,GAAwB,CAAxB,IAA6BkC,iBAAiB,CAAClC,MAAlB,GAA2B,CAAxD,IAA6DiC,iBAAiB,CAACjC,MAAlB,GAA2B,CAA5F,EAA+F;AAC3F;AAAA;AAAA,wCAASyC,IAAT,CAAc;AAAA;AAAA,4CAAWC,WAAzB,EAAsCjB,MAAtC,EAA8CO,cAA9C,EAA8DE,iBAA9D,EAAiFD,iBAAjF;AACH;AACJ;AACJ;;AAEMU,QAAAA,OAAO,CAACnD,EAAD,EAA0B;AACpC,iBAAO,KAAKO,SAAL,CAAeP,EAAf,CAAP;AACH;;AAEMoD,QAAAA,aAAa,GAAgB;AAChC,cAAI,KAAK9B,QAAL,CAAcd,MAAd,GAAuB,CAA3B,EAA8B;AAC1B,mBAAO,KAAKc,QAAL,CAAc,CAAd,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAEM+B,QAAAA,QAAQ,CAACzE,MAAD,EAA0B;AACrC,iBAAO,KAAK0E,aAAL,CAAmB1E,MAAnB,KAA8B,IAArC;AACH;;AAEM0E,QAAAA,aAAa,CAAC1E,MAAD,EAA8B;AAC9C,eAAK,IAAI2C,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKD,QAAL,CAAcd,MAA1C,EAAkDe,CAAC,EAAnD,EAAuD;AACnD,gBAAI,KAAKD,QAAL,CAAcC,CAAd,EAAiB3C,MAAjB,IAA2BA,MAA/B,EAAuC;AACnC,qBAAO,KAAK0C,QAAL,CAAcC,CAAd,CAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;;AAEMgC,QAAAA,UAAU,GAAkB;AAC/B,iBAAO,KAAKjC,QAAZ;AACH;;AAEMkC,QAAAA,aAAa,GAAW;AAC3B,cAAI,KAAKlC,QAAL,CAAcd,MAAd,GAAuB,CAA3B,EAA8B;AAC1B,mBAAO,KAAKc,QAAL,CAAc,CAAd,EAAiBzC,GAAxB;AACH;;AACD,iBAAO,CAAP;AACH;;AA3L6B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport MapUtil from \"./MapUtil\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\n\n/**地图城池配置*/\nexport class MapCityData {\n    id: number = 0;\n    cityId: number = 0;\n    rid: number = 0;\n    name: string = \"\";\n    x: number = null;\n    y: number = 0;\n    isMain: number = 0;\n    level: number = 0;\n    curDurable: number = 0;\n    maxDurable: number = 0;\n    unionId:number = 0;\n    parentId:number = 0;\n    unionName:string = \"\";\n    occupyTime:number;\n\n    public equalsServerData(data: any) {\n        if (this.cityId == data.cityId\n            && this.rid == data.rid\n            && this.name == data.name\n            && this.x == data.x\n            && this.y == data.y\n            && this.isMain == data.is_main\n            && this.level == data.level\n            && this.curDurable == data.cur_durable\n            && this.maxDurable == data.maxDurable\n            && this.unionId == data.union_id\n            && this.parentId == data.parent_id \n            && this.unionName == data.union_name\n            && this.occupyTime == data.occupy_time) {\n            return true;\n        }\n        return false;\n    }\n\n    public static createCityData(data: any, id: number, cityData: MapCityData = null): MapCityData {\n        let city: MapCityData = cityData;\n        if (cityData == null) {\n            city = new MapCityData();\n        }\n        city.id = id;\n        city.cityId = data.cityId;\n        city.rid = data.rid;\n        city.name = data.name;\n        city.x = data.x;\n        city.y = data.y;\n        city.isMain = data.is_main;\n        city.level = data.level;\n        city.curDurable = data.cur_durable;\n        city.maxDurable = data.max_durable;\n        city.unionId = data.union_id;\n        city.parentId = data.parent_id;\n        city.unionName = data.union_name;\n        city.occupyTime = data.occupy_time;\n        return city;\n    }\n\n    public getCellRadius() :number {\n        return 2;\n    }\n}\n\nexport default class MapCityProxy {\n    protected _mapCitys: MapCityData[] = [];\n    protected _lastCityCellIds: Map<number, number[]> = new Map<number, number[]>();\n    protected _myCitys: MapCityData[] = [];\n    public myId: number = 0;\n    public myUnionId: number = 0;\n    public myParentId: number = 0;\n\n    // 初始化数据\n    public initData(): void {\n        this._mapCitys.length = MapUtil.mapCellCount;\n        this._lastCityCellIds.clear();\n        this.updateMyCityIds();\n    }\n\n    public clearData(): void {\n        this._mapCitys.length = 0;\n        this._lastCityCellIds.clear();\n    }\n\n    public addCityData(data: any, cellId: number): void {\n        let cityData: MapCityData = MapCityData.createCityData(data, cellId, this._mapCitys[cellId]);\n        this._mapCitys[cellId] = cityData;\n        this._mapCitys[cellId - 1] = cityData;\n        this._mapCitys[cellId + 1] = cityData;\n        this._mapCitys[cellId - MapUtil.mapSize.width] = cityData;\n        this._mapCitys[cellId - MapUtil.mapSize.width - 1] = cityData;\n        this._mapCitys[cellId - MapUtil.mapSize.width + 1] = cityData;\n        this._mapCitys[cellId + MapUtil.mapSize.width] = cityData;\n        this._mapCitys[cellId + MapUtil.mapSize.width - 1] = cityData;\n        this._mapCitys[cellId + MapUtil.mapSize.width + 1] = cityData;\n    }\n\n    public removeCityData(cellId: number): void {\n        let cityData: MapCityData = this._mapCitys[cellId];\n        if (cityData) {\n            this._mapCitys[cellId] = null;\n            this.checkAndRemoveCityCell(cellId - 1, cityData.cityId);\n            this.checkAndRemoveCityCell(cellId + 1, cityData.cityId);\n            this.checkAndRemoveCityCell(cellId - MapUtil.mapSize.width, cityData.cityId);\n            this.checkAndRemoveCityCell(cellId - MapUtil.mapSize.width - 1, cityData.cityId);\n            this.checkAndRemoveCityCell(cellId - MapUtil.mapSize.width + 1, cityData.cityId);\n            this.checkAndRemoveCityCell(cellId + MapUtil.mapSize.width, cityData.cityId);\n            this.checkAndRemoveCityCell(cellId + MapUtil.mapSize.width - 1, cityData.cityId);\n            this.checkAndRemoveCityCell(cellId + MapUtil.mapSize.width + 1, cityData.cityId);\n        }\n    }\n\n    public checkAndRemoveCityCell(cellId: number, cityId: number): boolean {\n        let cityData: MapCityData = this._mapCitys[cellId];\n        if (cityData && cityData.cityId == cityId) {\n            this._mapCitys[cityId] = null;\n            return true;\n        }\n        return false;\n    }\n\n    /**我的建筑信息*/\n    public initMyCitys(citys: any[]): void {\n        this._myCitys.length = 0;\n        for (let i: number = 0; i < citys.length; i++) {\n            let id: number = MapUtil.getIdByCellPoint(citys[i].x, citys[i].y);\n            let city: MapCityData = MapCityData.createCityData(citys[i], id);\n            if (city.isMain) {\n                this._myCitys.unshift(city);\n            } else {\n                this._myCitys.push(city);\n            }\n        }\n    }\n\n    /**更新建筑id*/\n    public updateMyCityIds(): void {\n        for (let i: number = 0; i < this._myCitys.length; i++) {\n            let id: number = MapUtil.getIdByCellPoint(this._myCitys[i].x, this._myCitys[i].y);\n            this._myCitys[i].id = id;\n            this._mapCitys[id] = this._myCitys[i];\n        }\n\n    }\n\n    /**更新建筑*/\n    public updateCity(city: any): MapCityData {\n        let id: number = MapUtil.getIdByCellPoint(city.x, city.y);\n        let cityData: MapCityData = null;\n        if (this._mapCitys[id] == null) {\n            //代表是新增\n            cityData = MapCityData.createCityData(city, id);\n            this._mapCitys[id] = cityData;\n            if (city.rid == this.myId) {\n                this._myCitys.push(cityData);\n                this.myUnionId = cityData.unionId\n                this.myParentId = cityData.parentId\n            }\n        } else {\n            cityData = MapCityData.createCityData(city, id, this._mapCitys[id]);\n            if (city.rid == this.myId) {\n                this.myUnionId = cityData.unionId\n                this.myParentId = cityData.parentId\n            }\n        }\n        return cityData;\n    }\n\n    public setMapScanBlock(scanDatas: any, areaId: number = 0): void {\n        let cBuilds: any[] = scanDatas.mc_builds;\n        if (cBuilds.length > 0) {\n            let lastCityCellIds: number[] = null;\n            if (this._lastCityCellIds.has(areaId)) {\n                lastCityCellIds = this._lastCityCellIds.get(areaId);\n            }\n            let cityCellIds: number[] = [];\n            let addCityCellIds: number[] = [];\n            let updateCityCellIds: number[] = [];\n            let removeCityCellIds: number[] = [];\n            for (let i: number = 0; i < cBuilds.length; i++) {\n                let areaIndex: number = MapUtil.getAreaIdByCellPoint(cBuilds[i].x, cBuilds[i].y);\n                if (areaIndex != areaId) {\n                    //代表服务端给过来的数据不在当前区域\n                    continue;\n                }\n                let cellId: number = MapUtil.getIdByCellPoint(cBuilds[i].x, cBuilds[i].y);\n                cityCellIds.push(cellId);\n                if (lastCityCellIds) {\n                    let index: number = lastCityCellIds.indexOf(cellId);\n                    if (index != -1) {\n                        //存在就列表中 就代表是已存在的数据\n                        if (this._mapCitys[cellId].equalsServerData(cBuilds[i]) == false) {\n                            //代表数据不一样需要刷新\n                            this.addCityData(cBuilds[i], cellId);\n                            updateCityCellIds.push(cellId);\n                        }\n                        lastCityCellIds.splice(index, 1);//移除重复数据\n                        continue;\n                    }\n                }\n                //其他情况就是新数据了\n                this.addCityData(cBuilds[i], cellId);\n                addCityCellIds.push(cellId);\n            }\n            if (lastCityCellIds && lastCityCellIds.length > 0) {\n                //代表有需要删除的数据\n                removeCityCellIds = lastCityCellIds;\n                for (let i: number = 0; i < removeCityCellIds.length; i++) {\n                    this.removeCityData(removeCityCellIds[i]);\n                }\n            }\n            this._lastCityCellIds.set(areaId, cityCellIds);\n            if (addCityCellIds.length > 0 || removeCityCellIds.length > 0 || updateCityCellIds.length > 0) {\n                EventMgr.emit(LogicEvent.updateCitys, areaId, addCityCellIds, removeCityCellIds, updateCityCellIds);\n            }\n        }\n    }\n\n    public getCity(id: number): MapCityData {\n        return this._mapCitys[id];\n    }\n\n    public getMyMainCity(): MapCityData {\n        if (this._myCitys.length > 0) {\n            return this._myCitys[0];\n        }\n        return null;\n    }\n\n    public isMyCity(cityId: number): boolean {\n        return this.getMyCityById(cityId) != null;\n    }\n\n    public getMyCityById(cityId: number): MapCityData {\n        for (let i: number = 0; i < this._myCitys.length; i++) {\n            if (this._myCitys[i].cityId == cityId) {\n                return this._myCitys[i];\n            }\n        }\n        return null;\n    }\n\n    public getMyCitys(): MapCityData[] {\n        return this._myCitys;\n    }\n\n    public getMyPlayerId(): number {\n        if (this._myCitys.length > 0) {\n            return this._myCitys[0].rid;\n        }\n        return 0;\n    }\n}\n"]}