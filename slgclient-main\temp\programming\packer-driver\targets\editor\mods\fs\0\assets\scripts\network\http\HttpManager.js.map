{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts"], "names": ["HttpManager", "HttpInvoke", "HttpInvokeType", "getInstance", "_instance", "setWebUrl", "url", "_url", "doGet", "name", "apiUrl", "params", "otherData", "invoke", "init", "doSend", "GET", "doPost", "POST"], "mappings": ";;;0DAGaA,W;;;;;;;;;;;;;;;;;;;;AAFJC,MAAAA,U,iBAAAA,U;AAAWC,MAAAA,c,iBAAAA,c;;;;;;;6BAEPF,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA,wCAUG,EAVH;AAAA;;AAEI,eAAXG,WAAW,GAAgB;AACrC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIJ,WAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKI,SAAZ;AACH;;AAIMC,QAAAA,SAAS,CAACC,GAAD,EAAiB;AAC7B,cAAG,KAAKC,IAAL,IAAa,EAAb,IAAmB,KAAKA,IAAL,IAAaD,GAAnC,EAAuC;AACnC,iBAAKC,IAAL,GAAYD,GAAZ;AACH;AACJ;;AAGME,QAAAA,KAAK,CAACC,IAAD,EAAaC,MAAb,EAA2BC,MAA3B,EAAsCC,SAAa,GAAG,IAAtD,EAAyE;AACjF,cAAIC,MAAM,GAAG;AAAA;AAAA,yCAAb;AACAA,UAAAA,MAAM,CAACC,IAAP,CAAYL,IAAZ,EAAiBG,SAAjB;AACA,iBAAOC,MAAM,CAACE,MAAP,CAAc,KAAKR,IAAL,GAAYG,MAA1B,EAAiCC,MAAjC,EAAwC;AAAA;AAAA,gDAAeK,GAAvD,CAAP;AACH;;AAIMC,QAAAA,MAAM,CAACR,IAAD,EAAaC,MAAb,EAA2BC,MAA3B,EAAsCC,SAAa,GAAG,IAAtD,EAAyE;AAClF,cAAIC,MAAM,GAAG;AAAA;AAAA,yCAAb;AACAA,UAAAA,MAAM,CAACC,IAAP,CAAYL,IAAZ,EAAiBG,SAAjB;AACA,iBAAOC,MAAM,CAACE,MAAP,CAAc,KAAKR,IAAL,GAAYG,MAA1B,EAAiCC,MAAjC,EAAwC;AAAA;AAAA,gDAAeO,IAAvD,CAAP;AACH;;AA9BoB,O;;sBAAZlB,W,eAC+B,I", "sourcesContent": ["import { _decorator } from 'cc';\nimport { HttpInvoke,HttpInvokeType } from \"./HttpInvoke\";\n\nexport class HttpManager {\n    private static _instance: HttpManager = null;\n    public static getInstance(): HttpManager {\n        if (this._instance == null) {\n            this._instance = new HttpManager();\n        }\n        return this._instance;\n    }\n\n\n    protected _url:string = \"\";\n    public setWebUrl(url:string):void{\n        if(this._url == \"\" || this._url != url){\n            this._url = url;\n        } \n    }\n\n\n    public doGet(name:string,apiUrl:string,params:any,otherData:any = null): Promise<any>{\n        var invoke = new HttpInvoke();\n        invoke.init(name,otherData);\n        return invoke.doSend(this._url + apiUrl,params,HttpInvokeType.GET);\n    }\n\n\n\n    public doPost(name:string,apiUrl:string,params:any,otherData:any = null): Promise<any>{\n        var invoke = new HttpInvoke();\n        invoke.init(name,otherData);\n        return invoke.doSend(this._url + apiUrl,params,HttpInvokeType.POST);\n    }\n}\n"]}