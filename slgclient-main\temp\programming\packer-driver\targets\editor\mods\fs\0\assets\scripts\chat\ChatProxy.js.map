{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts"], "names": ["ChatMsg", "ChatProxy", "clearData", "updateWorldChatList", "data", "_worldMsgList", "i", "length", "chat", "msg", "rid", "type", "time", "nick_name", "nick<PERSON><PERSON>", "push", "updateUnionChatList", "_unionMsgList", "updateWorldChat", "updateUnionChat", "getWorldChatList", "getUnionChatList"], "mappings": ";;;iBAEaA,O,EASQC,S;;;;;;;;;;;;;;;;;;yBATRD,O,GAAN,MAAMA,OAAN,CAAc;AAAA;AAAA,uCACH,CADG;;AAAA,6CAEG,EAFH;;AAAA,wCAGH,CAHG;;AAAA,uCAIJ,EAJI;;AAAA,wCAKH,CALG;AAAA;;AAAA,O;;yBASAC,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,iDAGO,EAHP;;AAAA,iDAIO,EAJP;AAAA;;AAMpBC,QAAAA,SAAS,GAAS,CAExB;;AAEMC,QAAAA,mBAAmB,CAACC,IAAD,EAAiB;AACvC,eAAKC,aAAL,GAAqB,EAArB;;AACA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGF,IAAI,CAACG,MAAxB,EAA+BD,CAAC,EAAhC,EAAmC;AAC/B,gBAAIE,IAAI,GAAG,IAAIR,OAAJ,EAAX;AACAQ,YAAAA,IAAI,CAACC,GAAL,GAAWL,IAAI,CAACE,CAAD,CAAJ,CAAQG,GAAnB;AACAD,YAAAA,IAAI,CAACE,GAAL,GAAWN,IAAI,CAACE,CAAD,CAAJ,CAAQI,GAAnB;AACAF,YAAAA,IAAI,CAACG,IAAL,GAAYP,IAAI,CAACE,CAAD,CAAJ,CAAQK,IAApB;AACAH,YAAAA,IAAI,CAACI,IAAL,GAAYR,IAAI,CAACE,CAAD,CAAJ,CAAQM,IAApB;AACAJ,YAAAA,IAAI,CAACK,SAAL,GAAiBT,IAAI,CAACE,CAAD,CAAJ,CAAQQ,QAAzB;;AACA,iBAAKT,aAAL,CAAmBU,IAAnB,CAAwBP,IAAxB;AACH;AACJ;;AAGMQ,QAAAA,mBAAmB,CAACZ,IAAD,EAAiB;AACvC,eAAKa,aAAL,GAAqB,EAArB;;AACA,eAAI,IAAIX,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGF,IAAI,CAACG,MAAxB,EAA+BD,CAAC,EAAhC,EAAmC;AAC/B,gBAAIE,IAAI,GAAG,IAAIR,OAAJ,EAAX;AACAQ,YAAAA,IAAI,CAACC,GAAL,GAAWL,IAAI,CAACE,CAAD,CAAJ,CAAQG,GAAnB;AACAD,YAAAA,IAAI,CAACE,GAAL,GAAWN,IAAI,CAACE,CAAD,CAAJ,CAAQI,GAAnB;AACAF,YAAAA,IAAI,CAACG,IAAL,GAAYP,IAAI,CAACE,CAAD,CAAJ,CAAQK,IAApB;AACAH,YAAAA,IAAI,CAACI,IAAL,GAAYR,IAAI,CAACE,CAAD,CAAJ,CAAQM,IAApB;AACAJ,YAAAA,IAAI,CAACK,SAAL,GAAiBT,IAAI,CAACE,CAAD,CAAJ,CAAQQ,QAAzB;;AACA,iBAAKG,aAAL,CAAmBF,IAAnB,CAAwBP,IAAxB;AACH;AACJ;;AAGMU,QAAAA,eAAe,CAACd,IAAD,EAAe;AACjC,cAAII,IAAI,GAAG,IAAIR,OAAJ,EAAX;AACAQ,UAAAA,IAAI,CAACC,GAAL,GAAWL,IAAI,CAACK,GAAhB;AACAD,UAAAA,IAAI,CAACE,GAAL,GAAWN,IAAI,CAACM,GAAhB;AACAF,UAAAA,IAAI,CAACG,IAAL,GAAYP,IAAI,CAACO,IAAjB;AACAH,UAAAA,IAAI,CAACI,IAAL,GAAYR,IAAI,CAACQ,IAAjB;AACAJ,UAAAA,IAAI,CAACK,SAAL,GAAiBT,IAAI,CAACU,QAAtB;;AACA,eAAKT,aAAL,CAAmBU,IAAnB,CAAwBP,IAAxB;AACH;;AAEMW,QAAAA,eAAe,CAACf,IAAD,EAAe;AACjC,cAAII,IAAI,GAAG,IAAIR,OAAJ,EAAX;AACAQ,UAAAA,IAAI,CAACC,GAAL,GAAWL,IAAI,CAACK,GAAhB;AACAD,UAAAA,IAAI,CAACE,GAAL,GAAWN,IAAI,CAACM,GAAhB;AACAF,UAAAA,IAAI,CAACG,IAAL,GAAYP,IAAI,CAACO,IAAjB;AACAH,UAAAA,IAAI,CAACI,IAAL,GAAYR,IAAI,CAACQ,IAAjB;AACAJ,UAAAA,IAAI,CAACK,SAAL,GAAiBT,IAAI,CAACU,QAAtB;;AACA,eAAKG,aAAL,CAAmBF,IAAnB,CAAwBP,IAAxB;AACH;;AAGMY,QAAAA,gBAAgB,GAAY;AAC/B,iBAAO,KAAKf,aAAZ;AACH;;AAEMgB,QAAAA,gBAAgB,GAAY;AAC/B,iBAAO,KAAKJ,aAAZ;AACH;;AAjE0B,O", "sourcesContent": ["import { _decorator } from 'cc';\n\nexport class ChatMsg {\n    rid: number = 0;\n    nick_name: string = \"\";\n    type:number = 0;\n    msg:string = \"\";\n    time:number = 0;\n}\n\n\nexport default class ChatProxy {\n\n\n    private _worldMsgList:ChatMsg[] = [];\n    private _unionMsgList:ChatMsg[] = [];\n    \n    public clearData(): void {\n\n    }\n\n    public updateWorldChatList(data:any[]):void{\n        this._worldMsgList = [];\n        for(var i = 0; i < data.length;i++){\n            var chat = new ChatMsg();\n            chat.msg = data[i].msg;\n            chat.rid = data[i].rid;\n            chat.type = data[i].type;\n            chat.time = data[i].time;\n            chat.nick_name = data[i].nickName\n            this._worldMsgList.push(chat);\n        }\n    }\n\n\n    public updateUnionChatList(data:any[]):void{\n        this._unionMsgList = [];\n        for(var i = 0; i < data.length;i++){\n            var chat = new ChatMsg();\n            chat.msg = data[i].msg;\n            chat.rid = data[i].rid;\n            chat.type = data[i].type;\n            chat.time = data[i].time;\n            chat.nick_name = data[i].nickName\n            this._unionMsgList.push(chat);\n        }\n    }\n\n\n    public updateWorldChat(data:any):void{\n        var chat = new ChatMsg();\n        chat.msg = data.msg;\n        chat.rid = data.rid;\n        chat.type = data.type;\n        chat.time = data.time;\n        chat.nick_name = data.nickName\n        this._worldMsgList.push(chat);\n    }\n\n    public updateUnionChat(data:any):void{\n        var chat = new ChatMsg();\n        chat.msg = data.msg;\n        chat.rid = data.rid;\n        chat.type = data.type;\n        chat.time = data.time;\n        chat.nick_name = data.nickName\n        this._unionMsgList.push(chat);\n    }\n\n\n    public getWorldChatList():ChatMsg[]{\n        return this._worldMsgList;\n    }\n\n    public getUnionChatList():ChatMsg[]{\n        return this._unionMsgList;\n    }\n}\n"]}