{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts"], "names": ["_decorator", "Vec2", "Vec3", "BuildTagLogic", "MapBaseLayerLogic", "MapUtil", "ccclass", "MapBuildTagLogic", "onLoad", "onDestroy", "setItemData", "item", "data", "resData", "position", "mapCellToPixelPoint", "x", "y", "setPosition", "getComponent", "setResourceData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG1BC,MAAAA,a;;AACAC,MAAAA,iB;;AAEAC,MAAAA,O;;;;;;;OALD;AAAEC,QAAAA;AAAF,O,GAAcN,U;;yBAQCO,gB,WADpBD,OAAO,CAAC,kBAAD,C,gBAAR,MACqBC,gBADrB;AAAA;AAAA,kDACgE;AAElDC,QAAAA,MAAM,GAAS;AACrB,gBAAMA,MAAN;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,gBAAMA,SAAN;AACH;;AAEMC,QAAAA,WAAW,CAACC,IAAD,EAAaC,IAAb,EAA8B;AAC5C,cAAIC,OAAmB,GAAGD,IAA1B;AACA,cAAIE,QAAc,GAAG;AAAA;AAAA,kCAAQC,mBAAR,CAA4B,IAAId,IAAJ,CAASY,OAAO,CAACG,CAAjB,EAAoBH,OAAO,CAACI,CAA5B,CAA5B,CAArB;AACAN,UAAAA,IAAI,CAACO,WAAL,CAAiB,IAAIhB,IAAJ,CAASY,QAAQ,CAACE,CAAlB,EAAqBF,QAAQ,CAACG,CAA9B,EAAiC,CAAjC,CAAjB;AACAN,UAAAA,IAAI,CAACQ,YAAL;AAAA;AAAA,8CAAiCC,eAAjC,CAAiDP,OAAjD;AACH;;AAf2D,O", "sourcesContent": ["import { _decorator, Node, Vec2, Vec3 } from 'cc';\nconst { ccclass } = _decorator;\n\nimport BuildTagLogic from \"./entries/BuildTagLogic\";\nimport MapBaseLayerLogic from \"./MapBaseLayerLogic\";\nimport { MapResData } from \"./MapProxy\";\nimport MapUtil from \"./MapUtil\";\n\n@ccclass('MapBuildTagLogic')\nexport default class MapBuildTagLogic extends MapBaseLayerLogic {\n\n    protected onLoad(): void {\n        super.onLoad();\n    }\n\n    protected onDestroy(): void {\n        super.onDestroy();\n    }\n\n    public setItemData(item: Node, data: any): void {\n        let resData: MapResData = data as MapResData;\n        let position: Vec2 = MapUtil.mapCellToPixelPoint(new Vec2(resData.x, resData.y));\n        item.setPosition(new Vec3(position.x, position.y, 0));\n        item.getComponent(BuildTagLogic).setResourceData(resData);\n    }\n}\n"]}