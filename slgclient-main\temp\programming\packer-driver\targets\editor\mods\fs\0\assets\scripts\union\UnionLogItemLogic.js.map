{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts"], "names": ["_decorator", "Component", "Label", "DateUtil", "ccclass", "property", "UnionApplyItemLogic", "updateItem", "data", "<PERSON><PERSON><PERSON><PERSON>", "string", "des", "time<PERSON><PERSON><PERSON>", "converTimeStr", "ctime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AAGzBC,MAAAA,Q;;;;;;;OAFD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAITM,mB,WADpBF,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAACH,KAAD,C,UAERG,QAAQ,CAACH,KAAD,C,oCAJb,MACqBI,mBADrB,SACiDL,SADjD,CAC2D;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAM7CM,QAAAA,UAAU,CAACC,IAAD,EAAe;AAC/B,eAAKC,QAAL,CAAcC,MAAd,GAAuBF,IAAI,CAACG,GAA5B;AACA,eAAKC,SAAL,CAAeF,MAAf,GAAwB;AAAA;AAAA,oCAASG,aAAT,CAAuBL,IAAI,CAACM,KAA5B,EAAoC,qBAApC,CAAxB;AACH;;AATsD,O;;;;;iBAE9B,I;;;;;;;iBAEC,I", "sourcesContent": ["\nimport { _decorator, Component, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport DateUtil from \"../utils/DateUtil\";\n@ccclass('UnionLogItemLogic')\nexport default class UnionApplyItemLogic extends Component {\n    @property(Label)\n    desLabel: Label | null = null;\n    @property(Label)\n    timeLabel: Label | null = null;\n\n    protected updateItem(data:any):void{\n        this.desLabel.string = data.des;\n        this.timeLabel.string = DateUtil.converTimeStr(data.ctime,  \"YYYY-MM-DD hh:mm:ss\");\n    }\n}\n\n"]}