{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts"], "names": ["_decorator", "Component", "Label", "RichText", "<PERSON><PERSON>", "Node", "Prefab", "NodePool", "instantiate", "UITransform", "AudioManager", "LoginCommand", "DateUtil", "FacilityAdditionItemLogic", "MapUICommand", "ccclass", "property", "FacilityDesLogic", "onLoad", "schedule", "updateNeedTime", "onDestroy", "removeAllAdditionItems", "children", "additionNode", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "length", "_additionPool", "put", "getAdditionItem", "size", "get", "additionItemPrefab", "updateAdditionView", "_cfg", "additions", "item", "parent", "getComponent", "setData", "_data", "updateContidionView", "_isUnLock", "conditions", "contidionList", "data", "getInstance", "proxy", "getMyFacilityByType", "_cityId", "type", "cfg", "getFacilityCfgByType", "level", "push", "name", "labelConditions", "node", "active", "string", "join", "height", "updateNeedView", "_isNeedComplete", "curLevel", "upLevels", "roleRes", "getRoleResData", "upLevel", "needStrList", "grain", "wood", "iron", "stone", "decree", "labelNeed", "_isLevelMax", "isUping", "labelNeedTime", "converSecondStr", "time", "upLastTime", "updateUpBtn", "btnUp", "interactable", "labelUp", "cityId", "labelTitle", "labelDes", "des", "onClickUp", "instance", "playClick", "upFacility"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;;AACrFC,MAAAA,Y,iBAAAA,Y;;AAGFC,MAAAA,Y;;AACAC,MAAAA,Q;;AACAC,MAAAA,yB;;AACAC,MAAAA,Y;;;;;;;OALD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;yBASTiB,gB,WADpBF,OAAO,CAAC,kBAAD,C,UAEHC,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACb,QAAD,C,UAERa,QAAQ,CAACb,QAAD,C,UAERa,QAAQ,CAACZ,MAAD,C,UAERY,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACX,IAAD,C,WAERW,QAAQ,CAACV,MAAD,C,oCApBb,MACqBW,gBADrB,SAC8ChB,SAD9C,CACwD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,2CAsBxB,CAtBwB;;AAAA,yCAuBxB,IAvBwB;;AAAA,wCAwBnB,IAxBmB;;AAAA,+CAyBP,IAzBO;;AAAA,6CA0BrB,KA1BqB;;AAAA,mDA2Bf,KA3Be;;AAAA,+CA4BnB,KA5BmB;;AAAA,iDA6BhB,IAAIM,QAAJ,EA7BgB;AAAA;;AA+B1CW,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL,CAAc,KAAKC,cAAnB;AAEH;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AAESC,QAAAA,sBAAsB,GAAS;AACrC,cAAIC,QAAgB,GAAG,KAAKC,YAAL,CAAkBD,QAAlB,CAA2BE,MAA3B,EAAvB;AACA,eAAKD,YAAL,CAAkBE,iBAAlB;;AACA,eAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGJ,QAAQ,CAACK,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,iBAAKE,aAAL,CAAmBC,GAAnB,CAAuBP,QAAQ,CAACI,CAAD,CAA/B;AACH;AACJ;;AAESI,QAAAA,eAAe,GAAS;AAC9B,cAAI,KAAKF,aAAL,CAAmBG,IAAnB,KAA4B,CAAhC,EAAmC;AAC/B,mBAAO,KAAKH,aAAL,CAAmBI,GAAnB,EAAP;AACH,WAFD,MAEO;AACH,mBAAOzB,WAAW,CAAC,KAAK0B,kBAAN,CAAlB;AACH;AACJ,SAtDmD,CAwDpD;;;AACOC,QAAAA,kBAAkB,GAAG;AACxB,eAAKb,sBAAL;;AACA,eAAK,IAAIK,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKS,IAAL,CAAUC,SAAV,CAAoBT,MAAhD,EAAwDD,CAAC,EAAzD,EAA6D;AACzD,gBAAIW,IAAU,GAAG,KAAKP,eAAL,EAAjB;AACAO,YAAAA,IAAI,CAACC,MAAL,GAAc,KAAKf,YAAnB;AACAc,YAAAA,IAAI,CAACE,YAAL;AAAA;AAAA,wEAA6CC,OAA7C,CAAqD,KAAKC,KAA1D,EAAiE,KAAKN,IAAtE,EAA4ET,CAA5E;AACH;AACJ,SAhEmD,CAkEpD;;;AACOgB,QAAAA,mBAAmB,GAAG;AACzB,eAAKC,SAAL,GAAiB,IAAjB;;AACA,cAAI,KAAKR,IAAL,CAAUS,UAAV,CAAqBjB,MAArB,GAA8B,CAAlC,EAAqC;AACjC;AACA,gBAAIkB,aAAuB,GAAG,EAA9B;;AACA,iBAAK,IAAInB,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKS,IAAL,CAAUS,UAAV,CAAqBjB,MAAjD,EAAyDD,CAAC,EAA1D,EAA8D;AAC1D,kBAAIoB,IAAc,GAAG;AAAA;AAAA,gDAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,mBAAjC,CAAqD,KAAKC,OAA1D,EAAmE,KAAKf,IAAL,CAAUS,UAAV,CAAqBlB,CAArB,EAAwByB,IAA3F,CAArB;AACA,kBAAIC,GAAmB,GAAG;AAAA;AAAA,gDAAaL,WAAb,GAA2BC,KAA3B,CAAiCK,oBAAjC,CAAsD,KAAKlB,IAAL,CAAUS,UAAV,CAAqBlB,CAArB,EAAwByB,IAA9E,CAA1B;;AACA,kBAAIL,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAACQ,KAAL,GAAa,KAAKnB,IAAL,CAAUS,UAAV,CAAqBlB,CAArB,EAAwB4B,KAAzD,EAAgE;AAC5D;AACAT,gBAAAA,aAAa,CAACU,IAAd,CAAmB,oBAAoBH,GAAG,CAACI,IAAxB,GAA+B,KAAKrB,IAAL,CAAUS,UAAV,CAAqBlB,CAArB,EAAwB4B,KAAvD,GAA+D,WAAlF;AACA,qBAAKX,SAAL,GAAiB,KAAjB;AACH,eAJD,MAIO;AACH;AACAE,gBAAAA,aAAa,CAACU,IAAd,CAAmB,oBAAoBH,GAAG,CAACI,IAAxB,GAA+B,KAAKrB,IAAL,CAAUS,UAAV,CAAqBlB,CAArB,EAAwB4B,KAAvD,GAA+D,WAAlF;AACH;AACJ;;AACD,iBAAKG,eAAL,CAAqBC,IAArB,CAA0BpB,MAA1B,CAAiCqB,MAAjC,GAA0C,IAA1C;AACA,iBAAKF,eAAL,CAAqBG,MAArB,GAA8Bf,aAAa,CAACgB,IAAd,CAAmB,OAAnB,CAA9B;AACA,iBAAKJ,eAAL,CAAqBC,IAArB,CAA0BpB,MAA1B,CAAiCC,YAAjC,CAA8C/B,WAA9C,EAA2DsD,MAA3D,GAAoE,KAAKL,eAAL,CAAqBC,IAArB,CAA0BnB,YAA1B,CAAuC/B,WAAvC,EAAoDsD,MAApD,GAA6D,EAAjI;AACH,WAlBD,MAkBO;AACH,iBAAKL,eAAL,CAAqBC,IAArB,CAA0BpB,MAA1B,CAAiCqB,MAAjC,GAA0C,KAA1C;AACH;AACJ,SA1FmD,CA4FpD;;;AACOI,QAAAA,cAAc,GAAS;AAC1B,eAAKC,eAAL,GAAuB,IAAvB;AAEA,cAAIC,QAAgB,GAAG,KAAKxB,KAAL,CAAWa,KAAlC;;AACA,cAAIW,QAAQ,IAAI,CAAZ,IAAiBA,QAAQ,GAAG,KAAK9B,IAAL,CAAU+B,QAAV,CAAmBvC,MAAnD,EAA2D;AACvD;AACA,gBAAIwC,OAAY,GAAG;AAAA;AAAA,8CAAapB,WAAb,GAA2BC,KAA3B,CAAiCoB,cAAjC,EAAnB;AACA,gBAAIC,OAAwB,GAAG,KAAKlC,IAAL,CAAU+B,QAAV,CAAmBD,QAAnB,CAA/B;AACA,gBAAIK,WAAqB,GAAG,EAA5B;;AACA,gBAAID,OAAO,CAACE,KAAR,GAAgB,CAApB,EAAuB;AACnB,kBAAIJ,OAAO,CAACI,KAAR,GAAgBF,OAAO,CAACE,KAA5B,EAAmC;AAC/B,qBAAKP,eAAL,GAAuB,KAAvB;AACAM,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACE,KAA/B,GAAuC,GAAvC,GAA6CJ,OAAO,CAACI,KAArD,GAA6D,UAA9E;AACH,eAHD,MAGO;AACHD,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACE,KAA/B,GAAuC,GAAvC,GAA6CJ,OAAO,CAACI,KAArD,GAA6D,UAA9E;AACH;AACJ;;AACD,gBAAIF,OAAO,CAACG,IAAR,GAAe,CAAnB,EAAsB;AAClB,kBAAIL,OAAO,CAACK,IAAR,GAAeH,OAAO,CAACG,IAA3B,EAAiC;AAC7B,qBAAKR,eAAL,GAAuB,KAAvB;AACAM,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACG,IAA/B,GAAsC,GAAtC,GAA4CL,OAAO,CAACK,IAApD,GAA2D,UAA5E;AACH,eAHD,MAGO;AACHF,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACG,IAA/B,GAAsC,GAAtC,GAA4CL,OAAO,CAACK,IAApD,GAA2D,UAA5E;AACH;AACJ;;AACD,gBAAIH,OAAO,CAACI,IAAR,GAAe,CAAnB,EAAsB;AAClB,kBAAIN,OAAO,CAACM,IAAR,GAAeJ,OAAO,CAACI,IAA3B,EAAiC;AAC7B,qBAAKT,eAAL,GAAuB,KAAvB;AACAM,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACI,IAA/B,GAAsC,GAAtC,GAA4CN,OAAO,CAACM,IAApD,GAA2D,UAA5E;AACH,eAHD,MAGO;AACHH,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACI,IAA/B,GAAsC,GAAtC,GAA4CN,OAAO,CAACM,IAApD,GAA2D,UAA5E;AACH;AACJ;;AACD,gBAAIJ,OAAO,CAACK,KAAR,GAAgB,CAApB,EAAuB;AACnB,kBAAIP,OAAO,CAACO,KAAR,GAAgBL,OAAO,CAACK,KAA5B,EAAmC;AAC/B,qBAAKV,eAAL,GAAuB,KAAvB;AACAM,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACK,KAA/B,GAAuC,GAAvC,GAA6CP,OAAO,CAACO,KAArD,GAA6D,UAA9E;AACH,eAHD,MAGO;AACHJ,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACK,KAA/B,GAAuC,GAAvC,GAA6CP,OAAO,CAACO,KAArD,GAA6D,UAA9E;AACH;AACJ;;AACD,gBAAIL,OAAO,CAACM,MAAR,GAAiB,CAArB,EAAwB;AACpB,kBAAIR,OAAO,CAACQ,MAAR,GAAiBN,OAAO,CAACM,MAA7B,EAAqC;AACjC,qBAAKX,eAAL,GAAuB,KAAvB;AACAM,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACM,MAA/B,GAAwC,GAAxC,GAA8CR,OAAO,CAACQ,MAAtD,GAA+D,UAAhF;AACH,eAHD,MAGO;AACHL,gBAAAA,WAAW,CAACf,IAAZ,CAAiB,uBAAuBc,OAAO,CAACM,MAA/B,GAAwC,GAAxC,GAA8CR,OAAO,CAACQ,MAAtD,GAA+D,UAAhF;AACH;AACJ;;AACD,iBAAKC,SAAL,CAAelB,IAAf,CAAoBpB,MAApB,CAA2BqB,MAA3B,GAAoC,IAApC;AACA,iBAAKiB,SAAL,CAAehB,MAAf,GAAwBU,WAAW,CAACT,IAAZ,CAAiB,OAAjB,CAAxB;AACA,iBAAKe,SAAL,CAAelB,IAAf,CAAoBpB,MAApB,CAA2BC,YAA3B,CAAwC/B,WAAxC,EAAqDsD,MAArD,GAA8D,KAAKc,SAAL,CAAelB,IAAf,CAAoBnB,YAApB,CAAiC/B,WAAjC,EAA8CsD,MAA9C,GAAuD,EAArH;AACA,iBAAKe,WAAL,GAAmB,KAAnB;AACH,WAjDD,MAiDO;AACH,iBAAKD,SAAL,CAAelB,IAAf,CAAoBpB,MAApB,CAA2BqB,MAA3B,GAAoC,KAApC;AACA,iBAAKkB,WAAL,GAAmB,IAAnB;AACH;AACJ;;AAEM1D,QAAAA,cAAc,GAAS;AAC1B,cAAG,KAAK0D,WAAL,IAAoB,KAAvB,EAA6B;AACzB,gBAAIvB,KAAK,GAAG,KAAKnB,IAAL,CAAU+B,QAAV,CAAmB,KAAKzB,KAAL,CAAWa,KAA9B,CAAZ;;AACA,gBAAI,KAAKb,KAAL,CAAWqC,OAAX,MAAwB,KAA5B,EAAkC;AAC9B,mBAAKC,aAAL,CAAmBnB,MAAnB,GAA6B;AAAA;AAAA,wCAASoB,eAAT,CAAyB1B,KAAK,CAAC2B,IAAN,GAAW,IAApC,CAA7B;AACH,aAFD,MAEK;AACD,mBAAKF,aAAL,CAAmBnB,MAAnB,GAA4B;AAAA;AAAA,wCAASoB,eAAT,CAAyB,KAAKvC,KAAL,CAAWyC,UAAX,EAAzB,CAA5B;AACH;AACJ,WAPD,MAOK;AACD,iBAAKH,aAAL,CAAmBnB,MAAnB,GAA4B,MAA5B;AACH;AACJ,SAnKmD,CAqKpD;;;AACOuB,QAAAA,WAAW,GAAS;AACvB,cAAI,KAAKN,WAAT,EAAsB;AAClB;AACA,iBAAKO,KAAL,CAAW1B,IAAX,CAAgBC,MAAhB,GAAyB,KAAzB;AACH,WAHD,MAGO;AACH,iBAAKyB,KAAL,CAAW1B,IAAX,CAAgBC,MAAhB,GAAyB,IAAzB;;AACA,gBAAI,KAAKhB,SAAL,IAAkB,KAAtB,EAA6B;AACzB;AACA,mBAAKyC,KAAL,CAAWC,YAAX,GAA0B,KAA1B;AACA,mBAAKC,OAAL,CAAa1B,MAAb,GAAsB,KAAtB;AACH,aAJD,MAIO,IAAI,KAAKI,eAAL,IAAwB,KAA5B,EAAmC;AACtC;AACA,mBAAKoB,KAAL,CAAWC,YAAX,GAA0B,KAA1B;AACA,mBAAKC,OAAL,CAAa1B,MAAb,GAAsB,IAAtB;AACH,aAJM,MAIA,IAAG,KAAKnB,KAAL,CAAWqC,OAAX,EAAH,EAAwB;AAC3B;AACA,mBAAKM,KAAL,CAAWC,YAAX,GAA0B,KAA1B;AACA,mBAAKC,OAAL,CAAa1B,MAAb,GAAsB,KAAtB;AACH,aAJM,MAKF;AACD,mBAAKwB,KAAL,CAAWC,YAAX,GAA0B,IAA1B;AACA,mBAAKC,OAAL,CAAa1B,MAAb,GAAsB,IAAtB;AACH;AACJ;AACJ;;AAEMpB,QAAAA,OAAO,CAAC+C,MAAD,EAAiBzC,IAAjB,EAAiCM,GAAjC,EAA4D;AACtE,eAAKF,OAAL,GAAeqC,MAAf;AACA,eAAK9C,KAAL,GAAaK,IAAb;AACA,eAAKX,IAAL,GAAYiB,GAAZ;AACA,eAAKoC,UAAL,CAAgB5B,MAAhB,GAAyBR,GAAG,CAACI,IAA7B;AACA,eAAKiC,QAAL,CAAc7B,MAAd,GAAuBR,GAAG,CAACsC,GAA3B;AACA,eAAKxD,kBAAL;AACA,eAAKQ,mBAAL;AACA,eAAKqB,cAAL;AACA,eAAK5C,cAAL;AACA,eAAKgE,WAAL;AACH;;AAESQ,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAa9C,WAAb,GAA2B+C,UAA3B,CAAsC,KAAK5C,OAA3C,EAAoD,KAAKT,KAAL,CAAWU,IAA/D;AACH;;AAhNmD,O;;;;;iBAEhC,I;;;;;;;iBAEF,I;;;;;;;iBAEU,I;;;;;;;iBAEN,I;;;;;;;iBAEN,I;;;;;;;iBAEC,I;;;;;;;iBAGM,I;;;;;;;iBAGF,I;;;;;;;iBAEQ,I", "sourcesContent": ["import { _decorator, Component, Label, RichText, Button, Node, Prefab, NodePool, instantiate, UITransform } from 'cc';\nimport { AudioManager } from '../../common/AudioManager';\nconst { ccclass, property } = _decorator;\n\nimport LoginCommand from \"../../login/LoginCommand\";\nimport DateUtil from \"../../utils/DateUtil\";\nimport FacilityAdditionItemLogic from \"./FacilityAdditionItemLogic\";\nimport MapUICommand from \"./MapUICommand\";\nimport { Facility, FacilityAdditionCfg, FacilityConfig, FacilityUpLevel } from \"./MapUIProxy\";\n\n@ccclass('FacilityDesLogic')\nexport default class FacilityDesLogic extends Component {\n    @property(Label)\n    labelTitle: Label = null;\n    @property(Label)\n    labelDes: Label = null;\n    @property(RichText)\n    labelConditions: RichText = null;\n    @property(RichText)\n    labelNeed: RichText = null;\n    @property(Button)\n    btnUp: Button = null;\n    @property(Label)\n    labelUp: Label = null;\n\n    @property(Label)\n    labelNeedTime: Label = null;\n\n    @property(Node)\n    additionNode: Node = null;\n    @property(Prefab)\n    additionItemPrefab: Prefab = null;\n\n    protected _cityId: number = 0;\n    protected _data: Facility = null;\n    protected _cfg: FacilityConfig = null;\n    protected _additonCfg: FacilityAdditionCfg = null;\n    protected _isUnLock: boolean = false;//是否解锁\n    protected _isNeedComplete: boolean = false;//是否满足升级需求\n    protected _isLevelMax: boolean = false;//是否已达最高等级\n    protected _additionPool: NodePool = new NodePool();\n\n    protected onLoad(): void {\n        this.schedule(this.updateNeedTime);\n        \n    }\n\n    protected onDestroy(): void {\n\n    }\n\n    protected removeAllAdditionItems(): void {\n        let children: Node[] = this.additionNode.children.concat();\n        this.additionNode.removeAllChildren();\n        for (let i: number = 0; i < children.length; i++) {\n            this._additionPool.put(children[i]);\n        }\n    }\n\n    protected getAdditionItem(): Node {\n        if (this._additionPool.size() > 0) {\n            return this._additionPool.get();\n        } else {\n            return instantiate(this.additionItemPrefab);\n        }\n    }\n\n    //更新加成描述界面\n    public updateAdditionView() {\n        this.removeAllAdditionItems();\n        for (let i: number = 0; i < this._cfg.additions.length; i++) {\n            let item: Node = this.getAdditionItem();\n            item.parent = this.additionNode;\n            item.getComponent(FacilityAdditionItemLogic).setData(this._data, this._cfg, i);\n        }\n    }\n\n    //更新解锁条件\n    public updateContidionView() {\n        this._isUnLock = true;\n        if (this._cfg.conditions.length > 0) {\n            //有解锁条件\n            let contidionList: string[] = [];\n            for (let i: number = 0; i < this._cfg.conditions.length; i++) {\n                let data: Facility = MapUICommand.getInstance().proxy.getMyFacilityByType(this._cityId, this._cfg.conditions[i].type);\n                let cfg: FacilityConfig = MapUICommand.getInstance().proxy.getFacilityCfgByType(this._cfg.conditions[i].type);\n                if (data == null || data.level < this._cfg.conditions[i].level) {\n                    //不满足条件\n                    contidionList.push(\"<color=#ff0000>\" + cfg.name + this._cfg.conditions[i].level + \"级</color>\");\n                    this._isUnLock = false;\n                } else {\n                    //满足条件\n                    contidionList.push(\"<color=#00ff00>\" + cfg.name + this._cfg.conditions[i].level + \"级</color>\");\n                }\n            }\n            this.labelConditions.node.parent.active = true;\n            this.labelConditions.string = contidionList.join(\"<br/>\");\n            this.labelConditions.node.parent.getComponent(UITransform).height = this.labelConditions.node.getComponent(UITransform).height + 30;\n        } else {\n            this.labelConditions.node.parent.active = false;\n        }\n    }\n\n    //更新资源需求\n    public updateNeedView(): void {\n        this._isNeedComplete = true;\n        \n        let curLevel: number = this._data.level;\n        if (curLevel >= 0 && curLevel < this._cfg.upLevels.length) {\n            //未达到最高级时\n            let roleRes: any = LoginCommand.getInstance().proxy.getRoleResData();\n            let upLevel: FacilityUpLevel = this._cfg.upLevels[curLevel];\n            let needStrList: string[] = [];\n            if (upLevel.grain > 0) {\n                if (roleRes.grain < upLevel.grain) {\n                    this._isNeedComplete = false;\n                    needStrList.push(\"粮食：<color=#ff0000>\" + upLevel.grain + \"/\" + roleRes.grain + \"</color>\");\n                } else {\n                    needStrList.push(\"粮食：<color=#00ff00>\" + upLevel.grain + \"/\" + roleRes.grain + \"</color>\");\n                }\n            }\n            if (upLevel.wood > 0) {\n                if (roleRes.wood < upLevel.wood) {\n                    this._isNeedComplete = false;\n                    needStrList.push(\"木材：<color=#ff0000>\" + upLevel.wood + \"/\" + roleRes.wood + \"</color>\");\n                } else {\n                    needStrList.push(\"木材：<color=#00ff00>\" + upLevel.wood + \"/\" + roleRes.wood + \"</color>\");\n                }\n            }\n            if (upLevel.iron > 0) {\n                if (roleRes.iron < upLevel.iron) {\n                    this._isNeedComplete = false;\n                    needStrList.push(\"铁矿：<color=#ff0000>\" + upLevel.iron + \"/\" + roleRes.iron + \"</color>\");\n                } else {\n                    needStrList.push(\"铁矿：<color=#00ff00>\" + upLevel.iron + \"/\" + roleRes.iron + \"</color>\");\n                }\n            }\n            if (upLevel.stone > 0) {\n                if (roleRes.stone < upLevel.stone) {\n                    this._isNeedComplete = false;\n                    needStrList.push(\"石头：<color=#ff0000>\" + upLevel.stone + \"/\" + roleRes.stone + \"</color>\");\n                } else {\n                    needStrList.push(\"石头：<color=#00ff00>\" + upLevel.stone + \"/\" + roleRes.stone + \"</color>\");\n                }\n            }\n            if (upLevel.decree > 0) {\n                if (roleRes.decree < upLevel.decree) {\n                    this._isNeedComplete = false;\n                    needStrList.push(\"政令：<color=#ff0000>\" + upLevel.decree + \"/\" + roleRes.decree + \"</color>\");\n                } else {\n                    needStrList.push(\"政令：<color=#00ff00>\" + upLevel.decree + \"/\" + roleRes.decree + \"</color>\");\n                }\n            }\n            this.labelNeed.node.parent.active = true;\n            this.labelNeed.string = needStrList.join(\"<br/>\");\n            this.labelNeed.node.parent.getComponent(UITransform).height = this.labelNeed.node.getComponent(UITransform).height + 30;\n            this._isLevelMax = false;\n        } else {\n            this.labelNeed.node.parent.active = false;\n            this._isLevelMax = true;\n        }\n    }\n\n    public updateNeedTime(): void {\n        if(this._isLevelMax == false){\n            var level = this._cfg.upLevels[this._data.level];\n            if (this._data.isUping() == false){\n                this.labelNeedTime.string =  DateUtil.converSecondStr(level.time*1000);\n            }else{\n                this.labelNeedTime.string = DateUtil.converSecondStr(this._data.upLastTime());\n            }\n        }else{\n            this.labelNeedTime.string = \"等级已满\";\n        }\n    }\n\n    //更新升级按钮\n    public updateUpBtn(): void {\n        if (this._isLevelMax) {\n            //升满级了\n            this.btnUp.node.active = false;\n        } else {\n            this.btnUp.node.active = true;\n            if (this._isUnLock == false) {\n                //未解锁\n                this.btnUp.interactable = false;\n                this.labelUp.string = \"未解锁\";\n            } else if (this._isNeedComplete == false) {\n                //资源不足\n                this.btnUp.interactable = false;\n                this.labelUp.string = \"升级\";\n            } else if(this._data.isUping()){\n                //正在升级中\n                this.btnUp.interactable = false;\n                this.labelUp.string = \"升级中\";\n            }\n            else {\n                this.btnUp.interactable = true;\n                this.labelUp.string = \"升级\";\n            }\n        }\n    }\n\n    public setData(cityId: number, data: Facility, cfg: FacilityConfig): void {\n        this._cityId = cityId;\n        this._data = data;\n        this._cfg = cfg;\n        this.labelTitle.string = cfg.name;\n        this.labelDes.string = cfg.des;\n        this.updateAdditionView();\n        this.updateContidionView();\n        this.updateNeedView();\n        this.updateNeedTime();\n        this.updateUpBtn();\n    }\n\n    protected onClickUp(): void {\n        AudioManager.instance.playClick();\n        MapUICommand.getInstance().upFacility(this._cityId, this._data.type);\n    }\n}\n"]}