{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts"], "names": ["_decorator", "Component", "Label", "Node", "<PERSON><PERSON>", "GeneralCommand", "SkillCommand", "SkillIconLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "SkillInfoLogic", "onEnable", "learnBtn", "node", "active", "onClickClose", "instance", "playClick", "setData", "data", "type", "general", "skillPos", "console", "log", "conf", "getInstance", "proxy", "getSkillCfg", "cfgId", "icon", "getComponent", "outLine", "_cfg", "_data", "_type", "_general", "_skillPos", "giveUpBtn", "nameLab", "string", "name", "isShowLv", "lv", "index", "skills", "length", "gskill", "levels", "lvBtn", "lvLab", "triggerLab", "trigger_type", "list", "trigger", "des", "rateLab", "probability", "targetLab", "target_type", "target", "armLab", "armstr", "arms", "des1", "effect_value", "str", "replace", "curDesLab", "des2", "nextDesLab", "indexOf", "onClickLearn", "upSkill", "id", "emit", "closeSkill", "onClickLv", "lvSkill", "onClickForget", "downSkill"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AAItCC,MAAAA,c;;AAEAC,MAAAA,Y;;AAEAC,MAAAA,c;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAVH;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBZ,U;;yBAaPa,c,WADpBF,OAAO,CAAC,gBAAD,C,UAGHC,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACT,IAAD,C,UAIRS,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,WAGRU,QAAQ,CAACV,KAAD,C,WAGRU,QAAQ,CAACR,MAAD,C,WAGRQ,QAAQ,CAACR,MAAD,C,WAGRQ,QAAQ,CAACR,MAAD,C,oCArCb,MACqBS,cADrB,SAC4CZ,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAuCnC,IAvCmC;;AAAA,wCAwChC,IAxCgC;;AAAA,4CA0C1B,IA1C0B;;AAAA,yCA2ClC,CA3CkC;;AAAA,6CA4C7B,CAAC,CA5C4B;AAAA;;AA8CxCa,QAAAA,QAAQ,GAAG;AACjB,eAAKC,QAAL,CAAcC,IAAd,CAAmBC,MAAnB,GAA4B,KAA5B;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B,eAAKF,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaE,QAAb,CAAsBC,SAAtB;AACH;;AAEMC,QAAAA,OAAO,CAACC,IAAD,EAAcC,IAAd,EAA2BC,OAA3B,EAAgDC,QAAhD,EAAkE;AAE5EC,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BL,IAA9B,EAAoCE,OAApC;AAEA,cAAII,IAAI,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,WAAjC,CAA6CT,IAAI,CAACU,KAAlD,CAAX;AACA,eAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,gDAAuCb,OAAvC,CAA+CC,IAA/C,EAAqD,IAArD;AACA,cAAIa,OAAqB,GAAG;AAAA;AAAA,4CAAaN,WAAb,GAA2BC,KAA3B,CAAiCK,OAA7D;AAEA,eAAKC,IAAL,GAAYR,IAAZ;AACA,eAAKS,KAAL,GAAaf,IAAb;AACA,eAAKgB,KAAL,GAAaf,IAAb;AACA,eAAKgB,QAAL,GAAgBf,OAAhB;AACA,eAAKgB,SAAL,GAAiBf,QAAjB;AAEA,eAAKV,QAAL,CAAcC,IAAd,CAAmBC,MAAnB,GAA4BM,IAAI,IAAI,CAApC;AACA,eAAKkB,SAAL,CAAezB,IAAf,CAAoBC,MAApB,GAA6BM,IAAI,IAAI,CAArC;AAEA,eAAKmB,OAAL,CAAaC,MAAb,GAAsBf,IAAI,CAACgB,IAA3B;AAEA,cAAIC,QAAQ,GAAG,KAAf;AACA,cAAIC,EAAE,GAAG,CAAT;;AACA,cAAGvB,IAAI,IAAI,CAAX,EAAa;AACT,iBAAK,IAAIwB,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGvB,OAAO,CAACwB,MAAR,CAAeC,MAA3C,EAAmDF,KAAK,EAAxD,EAA4D;AACxD,oBAAMG,MAAM,GAAI1B,OAAO,CAACwB,MAAR,CAAeD,KAAf,CAAhB;;AACA,kBAAIG,MAAM,IAAIA,MAAM,CAAClB,KAAP,IAAgBV,IAAI,CAACU,KAA/B,IAAwCkB,MAAM,CAACJ,EAAP,IAAalB,IAAI,CAACuB,MAAL,CAAYF,MAArE,EAA4E;AACxEJ,gBAAAA,QAAQ,GAAG,IAAX;AACAC,gBAAAA,EAAE,GAAGI,MAAM,CAACJ,EAAZ;AACA;AACH;AACJ;AACJ;;AAED,eAAKM,KAAL,CAAWpC,IAAX,CAAgBC,MAAhB,GAAyB4B,QAAzB;;AACA,cAAGA,QAAH,EAAY;AACR,iBAAKQ,KAAL,CAAWV,MAAX,GAAoB,QAAQG,EAA5B;AACH,WAFD,MAEK;AACD,iBAAKO,KAAL,CAAWV,MAAX,GAAoB,EAApB;AACH;;AAED,eAAKW,UAAL,CAAgBX,MAAhB,GAA0BR,OAAO,CAACoB,YAAR,CAAqBC,IAArB,CAA0B5B,IAAI,CAAC6B,OAAL,GAAa,CAAvC,EAA0CC,GAApE;AACA,eAAKC,OAAL,CAAahB,MAAb,GAAsBf,IAAI,CAACuB,MAAL,CAAY,CAAZ,EAAeS,WAAf,GAA6B,GAAnD;AACA,eAAKC,SAAL,CAAelB,MAAf,GAAwBR,OAAO,CAAC2B,WAAR,CAAoBN,IAApB,CAAyB5B,IAAI,CAACmC,MAAL,GAAY,CAArC,EAAwCL,GAAhE;AACA,eAAKM,MAAL,CAAYrB,MAAZ,GAAqB,KAAKsB,MAAL,CAAYrC,IAAI,CAACsC,IAAjB,CAArB;AAEA,cAAIC,IAAI,GAAGvC,IAAI,CAAC8B,GAAhB;;AACA,eAAK,IAAIX,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGnB,IAAI,CAACuB,MAAL,CAAY,CAAZ,EAAeiB,YAAf,CAA4BnB,MAAxD,EAAgEF,KAAK,EAArE,EAAyE;AACrE,gBAAIsB,GAAG,GAAGzC,IAAI,CAACuB,MAAL,CAAY,CAAZ,EAAeiB,YAAf,CAA4BrB,KAA5B,IAAqC,EAA/C;AACAoB,YAAAA,IAAI,GAAGA,IAAI,CAACG,OAAL,CAAa,KAAb,EAAoBD,GAApB,CAAP;AACH;;AAED,eAAKE,SAAL,CAAe5B,MAAf,GAAwBwB,IAAxB;AAEA,cAAIK,IAAI,GAAG5C,IAAI,CAAC8B,GAAhB;;AACA,eAAK,IAAIX,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGnB,IAAI,CAACuB,MAAL,CAAY,CAAZ,EAAeiB,YAAf,CAA4BnB,MAAxD,EAAgEF,KAAK,EAArE,EAAyE;AACrE,gBAAIsB,GAAG,GAAGzC,IAAI,CAACuB,MAAL,CAAY,CAAZ,EAAeiB,YAAf,CAA4BrB,KAA5B,IAAqC,EAA/C;AACAyB,YAAAA,IAAI,GAAGA,IAAI,CAACF,OAAL,CAAa,KAAb,EAAoBD,GAApB,CAAP;AACH;;AAED,eAAKI,UAAL,CAAgB9B,MAAhB,GAAyB6B,IAAzB;AAEH;;AAESP,QAAAA,MAAM,CAACC,IAAD,EAAwB;AACpC;AAEA,cAAIG,GAAG,GAAG,EAAV;;AACA,cAAGH,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AAC9DL,YAAAA,GAAG,IAAI,GAAP;AACH;;AAED,cAAGH,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AAC9DL,YAAAA,GAAG,IAAI,GAAP;AACH;;AAED,cAAGH,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CR,IAAI,CAACQ,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AAC9DL,YAAAA,GAAG,IAAI,GAAP;AACH;;AACD,iBAAOA,GAAP;AACH;;AAGSM,QAAAA,YAAY,GAAQ;AAC1B;AAAA;AAAA,4CAAaxD,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKmB,QAAR,EAAiB;AACb;AAAA;AAAA,kDAAeV,WAAf,GAA6B+C,OAA7B,CAAqC,KAAKrC,QAAL,CAAcsC,EAAnD,EAAuD,KAAKzC,IAAL,CAAUJ,KAAjE,EAAwE,KAAKQ,SAA7E;AACA,iBAAKxB,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,sCAAS6D,IAAT,CAAc;AAAA;AAAA,0CAAWC,UAAzB;AACH;AACJ;;AAESC,QAAAA,SAAS,GAAQ;AACvB;AAAA;AAAA,4CAAa7D,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKmB,QAAR,EAAiB;AACb;AAAA;AAAA,kDAAeV,WAAf,GAA6BoD,OAA7B,CAAqC,KAAK1C,QAAL,CAAcsC,EAAnD,EAAuD,KAAKrC,SAA5D;AACA,iBAAKxB,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,sCAAS6D,IAAT,CAAc;AAAA;AAAA,0CAAWC,UAAzB;AACH;AACJ;;AAESG,QAAAA,aAAa,GAAQ;AAC3B;AAAA;AAAA,4CAAa/D,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKmB,QAAR,EAAiB;AACb;AAAA;AAAA,kDAAeV,WAAf,GAA6BsD,SAA7B,CAAuC,KAAK5C,QAAL,CAAcsC,EAArD,EAAyD,KAAKzC,IAAL,CAAUJ,KAAnE,EAA0E,KAAKQ,SAA/E;AACA,iBAAKxB,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,sCAAS6D,IAAT,CAAc;AAAA;AAAA,0CAAWC,UAAzB;AACH;AACJ;;AAjKiD,O;;;;;iBAGjC,I;;;;;;;iBAGL,I;;;;;;;iBAIG,I;;;;;;;iBAGK,I;;;;;;;iBAGD,I;;;;;;;iBAGH,I;;;;;;;iBAGC,I;;;;;;;iBAGE,I;;;;;;;iBAGC,I;;;;;;;iBAGD,I;;;;;;;iBAGH,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, Component, Label, Node, Button } from 'cc';\nconst {ccclass, property} = _decorator;\n\nimport { SkillConf, SkillOutline } from \"../../config/skill/Skill\";\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport { GeneralData } from \"../../general/GeneralProxy\";\nimport SkillCommand from \"../../skill/SkillCommand\";\nimport { Skill } from \"../../skill/SkillProxy\";\nimport SkillIconLogic from \"./SkillIconLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('SkillInfoLogic')\nexport default class SkillInfoLogic extends Component {\n\n    @property(Label)\n    nameLab: Label = null;\n\n    @property(Node)\n    icon:Node = null;\n\n \n    @property(Label)\n    lvLab: Label = null;\n\n    @property(Label)\n    triggerLab: Label = null;\n\n    @property(Label)\n    targetLab: Label = null;\n\n    @property(Label)\n    armLab: Label = null;\n\n    @property(Label)\n    rateLab: Label = null;\n\n    @property(Label)\n    curDesLab: Label = null;\n\n    @property(Label)\n    nextDesLab: Label = null;\n\n    @property(Button)\n    learnBtn: Button = null;\n\n    @property(Button)\n    lvBtn: Button = null;\n\n    @property(Button)\n    giveUpBtn: Button = null;\n\n    _data: Skill = null;\n    _cfg: SkillConf = null;\n\n    _general: GeneralData = null;\n    _type: number = 0;\n    _skillPos : number = -1;\n\n    protected onEnable() {\n        this.learnBtn.node.active = false;\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n    public setData(data: Skill, type:number, general:GeneralData, skillPos: number) {\n\n        console.log(\"setData Skill:\", data, general);\n\n        var conf = SkillCommand.getInstance().proxy.getSkillCfg(data.cfgId);\n        this.icon.getComponent(SkillIconLogic).setData(data, null);\n        var outLine: SkillOutline = SkillCommand.getInstance().proxy.outLine;\n\n        this._cfg = conf;\n        this._data = data;\n        this._type = type;\n        this._general = general;\n        this._skillPos = skillPos;\n\n        this.learnBtn.node.active = type == 1;\n        this.giveUpBtn.node.active = type == 2;\n\n        this.nameLab.string = conf.name;\n\n        let isShowLv = false;\n        let lv = 0;\n        if(type == 2){\n            for (let index = 0; index < general.skills.length; index++) {\n                const gskill =  general.skills[index];\n                if (gskill && gskill.cfgId == data.cfgId && gskill.lv <= conf.levels.length){\n                    isShowLv = true;\n                    lv = gskill.lv;\n                    break\n                }\n            }\n        }\n\n        this.lvBtn.node.active = isShowLv;\n        if(isShowLv){\n            this.lvLab.string = \"lv:\" + lv;\n        }else{\n            this.lvLab.string = \"\";\n        }\n    \n        this.triggerLab.string =  outLine.trigger_type.list[conf.trigger-1].des;\n        this.rateLab.string = conf.levels[0].probability + \"%\";\n        this.targetLab.string = outLine.target_type.list[conf.target-1].des;\n        this.armLab.string = this.armstr(conf.arms);\n\n        var des1 = conf.des\n        for (let index = 0; index < conf.levels[0].effect_value.length; index++) {\n            var str = conf.levels[0].effect_value[index] + \"\";\n            des1 = des1.replace(\"%n%\", str);\n        }\n\n        this.curDesLab.string = des1;\n\n        var des2 = conf.des\n        for (let index = 0; index < conf.levels[1].effect_value.length; index++) {\n            var str = conf.levels[1].effect_value[index] + \"\";\n            des2 = des2.replace(\"%n%\", str);\n        }\n\n        this.nextDesLab.string = des2;\n\n    }\n\n    protected armstr(arms:number []): string{\n        // console.log(\"armstr:\", arms);\n\n        var str = \"\"\n        if(arms.indexOf(1)>=0 || arms.indexOf(4)>=0 || arms.indexOf(7)>=0){\n            str += \"步\"\n        }\n\n        if(arms.indexOf(2)>=0 || arms.indexOf(5)>=0 || arms.indexOf(8)>=0){\n            str += \"弓\"\n        }\n        \n        if(arms.indexOf(3)>=0 || arms.indexOf(6)>=0 || arms.indexOf(9)>=0){\n            str += \"骑\"\n        }\n        return str;\n    }\n\n    \n    protected onClickLearn():void {\n        AudioManager.instance.playClick();\n        if(this._general){\n            GeneralCommand.getInstance().upSkill(this._general.id, this._cfg.cfgId, this._skillPos);\n            this.node.active = false;\n            EventMgr.emit(LogicEvent.closeSkill);\n        }\n    }\n\n    protected onClickLv():void {\n        AudioManager.instance.playClick();\n        if(this._general){\n            GeneralCommand.getInstance().lvSkill(this._general.id, this._skillPos);\n            this.node.active = false;\n            EventMgr.emit(LogicEvent.closeSkill);\n        }\n    }\n\n    protected onClickForget():void {\n        AudioManager.instance.playClick();\n        if(this._general){\n            GeneralCommand.getInstance().downSkill(this._general.id, this._cfg.cfgId, this._skillPos);\n            this.node.active = false;\n            EventMgr.emit(LogicEvent.closeSkill);\n        }\n    }\n}\n"]}