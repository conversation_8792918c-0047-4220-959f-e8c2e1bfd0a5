{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts"], "names": ["_decorator", "Component", "Sprite", "SpriteAtlas", "Node", "Vec3", "DateUtil", "MapCommand", "MapResType", "EventMgr", "LogicEvent", "ccclass", "property", "SysCityLogic", "onLoad", "_limitTime", "getInstance", "proxy", "getWarFree", "onEnable", "on", "unionChange", "onUnionChange", "onDisable", "_data", "unscheduleAllCallbacks", "targetOff", "setCityData", "data", "updateUI", "rid", "unionId", "parentId", "type", "SYS_CITY", "node", "active", "level", "scale", "upSpr", "spriteFrame", "downSpr", "buildProxy", "myId", "resourceAtlas", "getSpriteFrame", "myUnionId", "myParentId", "diff", "getServerTime", "occupyTime", "console", "log", "mian<PERSON>ode", "stopCountDown", "schedule", "countDown"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAGpDC,MAAAA,Q;;AACAC,MAAAA,U;;AACEC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBASTa,Y,WADpBF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ,CAACV,MAAD,C,UAGRU,QAAQ,CAACV,MAAD,C,UAGRU,QAAQ,CAACT,WAAD,C,UAGRS,QAAQ,CAACR,IAAD,C,oCAZb,MACqBS,YADrB,SAC0CZ,SAD1C,CACoD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,8CAcjB,CAdiB;;AAAA,yCAezB,IAfyB;AAAA;;AAkBtCa,QAAAA,MAAM,GAAS;AACrB,eAAKC,UAAL,GAAkB;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,KAAzB,CAA+BC,UAA/B,EAAlB;AACH;;AAESC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKC,sBAAL;AACA;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEMC,QAAAA,WAAW,CAACC,IAAD,EAAkB;AAChC;AACA,eAAKJ,KAAL,GAAaI,IAAb;AACA,eAAKC,QAAL;AACH;;AAESP,QAAAA,aAAa,CAACQ,GAAD,EAAcC,OAAd,EAA+BC,QAA/B,EAAuD;AAC1E,cAAI,KAAKR,KAAL,CAAWM,GAAX,IAAkBA,GAAtB,EAA2B;AACvB,iBAAKN,KAAL,CAAWO,OAAX,GAAqBA,OAArB;AACA,iBAAKP,KAAL,CAAWQ,QAAX,GAAsBA,QAAtB;AACH;;AACD,eAAKH,QAAL;AACH;;AAGMA,QAAAA,QAAQ,GAAS;AACpB,cAAG,KAAKL,KAAL,CAAWS,IAAX,IAAmB;AAAA;AAAA,wCAAWC,QAAjC,EAA0C;AACtC,iBAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AACH,WAHD,MAGK;AACD,iBAAKD,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAED,cAAG,KAAKZ,KAAL,CAAWa,KAAX,IAAoB,CAAvB,EAAyB;AACrB,iBAAKF,IAAL,CAAUG,KAAV,GAAkB,IAAIjC,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB,CAAlB;AACH,WAFD,MAEM,IAAG,KAAKmB,KAAL,CAAWa,KAAX,IAAoB,CAAvB,EAAyB;AAC3B,iBAAKF,IAAL,CAAUG,KAAV,GAAkB,IAAIjC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAlB;AACH,WAFK,MAEA;AACF,iBAAK8B,IAAL,CAAUG,KAAV,GAAkB,IAAIjC,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB,CAAlB;AACH;;AAED,cAAG,CAAC,KAAKmB,KAAL,CAAWM,GAAf,EAAmB;AACf,iBAAKS,KAAL,CAAWC,WAAX,GAAyB,IAAzB;AACA,iBAAKC,OAAL,CAAaD,WAAb,GAA2B,IAA3B;AACH,WAHD,MAGM,IAAI,KAAKhB,KAAL,CAAWM,GAAX,IAAkB;AAAA;AAAA,wCAAWd,WAAX,GAAyB0B,UAAzB,CAAoCC,IAA1D,EAAgE;AAClE,iBAAKJ,KAAL,CAAWC,WAAX,GAAyB,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,UAAlC,CAAzB;AACA,iBAAKJ,OAAL,CAAaD,WAAb,GAA2B,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,UAAlC,CAA3B;AACH,WAHK,MAGC,IAAI,KAAKrB,KAAL,CAAWO,OAAX,GAAqB,CAArB,IAA0B,KAAKP,KAAL,CAAWO,OAAX,IAAsB;AAAA;AAAA,wCAAWf,WAAX,GAAyB0B,UAAzB,CAAoCI,SAAxF,EAAmG;AACtG,iBAAKP,KAAL,CAAWC,WAAX,GAAyB,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,WAAlC,CAAzB;AACA,iBAAKJ,OAAL,CAAaD,WAAb,GAA2B,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,WAAlC,CAA3B;AACH,WAHM,MAGD,IAAI,KAAKrB,KAAL,CAAWO,OAAX,GAAqB,CAArB,IAA0B,KAAKP,KAAL,CAAWO,OAAX,IAAsB;AAAA;AAAA,wCAAWf,WAAX,GAAyB0B,UAAzB,CAAoCK,UAAxF,EAAoG;AACtG,iBAAKR,KAAL,CAAWC,WAAX,GAAyB,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAAzB;AACA,iBAAKJ,OAAL,CAAaD,WAAb,GAA2B,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAA3B;AACH,WAHK,MAGC,IAAI,KAAKrB,KAAL,CAAWQ,QAAX,GAAsB,CAAtB,IAA2B,KAAKR,KAAL,CAAWQ,QAAX,IAAuB;AAAA;AAAA,wCAAWhB,WAAX,GAAyB0B,UAAzB,CAAoCI,SAA1F,EAAqG;AACxG,iBAAKP,KAAL,CAAWC,WAAX,GAAyB,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAAzB;AACA,iBAAKJ,OAAL,CAAaD,WAAb,GAA2B,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAA3B;AACH,WAHM,MAGD;AACF,iBAAKN,KAAL,CAAWC,WAAX,GAAyB,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,SAAlC,CAAzB;AACA,iBAAKJ,OAAL,CAAaD,WAAb,GAA2B,KAAKI,aAAL,CAAmBC,cAAnB,CAAkC,SAAlC,CAA3B;AACH;;AAED,cAAIG,IAAI,GAAG;AAAA;AAAA,oCAASC,aAAT,KAA2B,KAAKzB,KAAL,CAAW0B,UAAjD;;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,MAAZ,EAAoBJ,IAApB,EAA0B,KAAKjC,UAA/B;;AACA,cAAI,KAAKS,KAAL,CAAWQ,QAAX,GAAsB,CAAtB,IAA2BgB,IAAI,GAAC,KAAKjC,UAAzC,EAAoD;AAChD,iBAAKsC,QAAL,CAAcjB,MAAd,GAAuB,IAAvB;AACA,iBAAKkB,aAAL;AACA,iBAAKC,QAAL,CAAc,KAAKC,SAAnB,EAA8B,GAA9B;AACH,WAJD,MAIK;AACD,iBAAKH,QAAL,CAAcjB,MAAd,GAAuB,KAAvB;AACH;AACJ;;AAEMoB,QAAAA,SAAS,GAAG;AACf,cAAIR,IAAI,GAAG;AAAA;AAAA,oCAASC,aAAT,KAA2B,KAAKzB,KAAL,CAAW0B,UAAjD;;AACA,cAAIF,IAAI,GAAC,KAAKjC,UAAd,EAAyB;AACrB,iBAAKuC,aAAL;AACA,iBAAKD,QAAL,CAAcjB,MAAd,GAAuB,KAAvB;AACH;AACJ;;AAEMkB,QAAAA,aAAa,GAAG;AACnB,eAAK7B,sBAAL;AACH;;AAxG+C,O;;;;;iBAGhC,I;;;;;;;iBAGE,I;;;;;;;iBAGW,I;;;;;;;iBAGZ,I", "sourcesContent": ["import { _decorator, Component, Sprite, Sprite<PERSON>tlas, Node, Vec3 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport DateUtil from \"../../utils/DateUtil\";\nimport MapCommand from \"../MapCommand\";\nimport { MapResType } from \"../MapProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('SysCityLogic')\nexport default class SysCityLogic extends Component {\n\n    @property(Sprite)\n    upSpr: Sprite = null;\n\n    @property(Sprite)\n    downSpr: Sprite = null;\n\n    @property(SpriteAtlas)\n    resourceAtlas: SpriteAtlas = null;\n\n    @property(Node)\n    mianNode: Node = null;\n\n    protected _limitTime: number = 0;\n    protected _data: any = null;\n\n\n    protected onLoad(): void {\n        this._limitTime = MapCommand.getInstance().proxy.getWarFree();\n    }\n\n    protected onEnable(): void {\n        EventMgr.on(LogicEvent.unionChange, this.onUnionChange, this);\n    }\n\n    protected onDisable(): void {\n        this._data = null;\n        this.unscheduleAllCallbacks();\n        EventMgr.targetOff(this);\n    }\n\n    public setCityData(data: any): void {\n        // console.log(\"setCityData:\", data);\n        this._data = data;\n        this.updateUI();\n    }\n\n    protected onUnionChange(rid: number, unionId: number, parentId: number): void {\n        if (this._data.rid == rid ){\n            this._data.unionId = unionId;\n            this._data.parentId = parentId;\n        }\n        this.updateUI();\n    }\n\n\n    public updateUI(): void {\n        if(this._data.type != MapResType.SYS_CITY){\n            this.node.active = false;\n            return\n        }else{\n            this.node.active = true;\n        }\n\n        if(this._data.level >= 8){\n            this.node.scale = new Vec3(1.5, 1.5, 1.5);\n        }else if(this._data.level >= 5){\n            this.node.scale = new Vec3(1, 1, 1);\n        }else {\n            this.node.scale = new Vec3(0.5, 0.5, 0.5);\n        }\n\n        if(!this._data.rid){\n            this.upSpr.spriteFrame = null;\n            this.downSpr.spriteFrame = null;\n        }else if (this._data.rid == MapCommand.getInstance().buildProxy.myId) {\n            this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"blue_2_3\");\n            this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"blue_1_3\");\n        } else if (this._data.unionId > 0 && this._data.unionId == MapCommand.getInstance().buildProxy.myUnionId) {\n            this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"green_2_3\");\n            this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"green_1_3\");\n        }else if (this._data.unionId > 0 && this._data.unionId == MapCommand.getInstance().buildProxy.myParentId) {\n            this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"purple_2_3\");\n            this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"purple_1_3\");\n        } else if (this._data.parentId > 0 && this._data.parentId == MapCommand.getInstance().buildProxy.myUnionId) {\n            this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"yellow_2_3\");\n            this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"yellow_1_3\");\n        }else {\n            this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"red_2_3\");\n            this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"red_1_3\");\n        }\n\n        var diff = DateUtil.getServerTime() - this._data.occupyTime;\n        console.log(\"diff\", diff, this._limitTime);\n        if (this._data.parentId > 0 && diff<this._limitTime){\n            this.mianNode.active = true;\n            this.stopCountDown();\n            this.schedule(this.countDown, 1.0);\n        }else{\n            this.mianNode.active = false;\n        }\n    }\n\n    public countDown() {\n        var diff = DateUtil.getServerTime() - this._data.occupyTime;\n        if (diff>this._limitTime){\n            this.stopCountDown();\n            this.mianNode.active = false;\n        }\n    }\n\n    public stopCountDown() {\n        this.unscheduleAllCallbacks();\n    }\n}\n"]}