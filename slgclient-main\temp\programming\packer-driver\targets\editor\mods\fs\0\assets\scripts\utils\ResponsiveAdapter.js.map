{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "view", "sys", "UITransform", "director", "screen", "GameConfig", "ccclass", "property", "ResponsiveAdapter", "baseWidth", "baseHeight", "_baseWidth", "_baseHeight", "instance", "_instance", "onLoad", "console", "log", "onDestroy", "init", "enableResponsiveDesign", "updateScreenInfo", "setupResponsiveCanvas", "setupScreenResizeListener", "printDebugInfo", "windowSize", "_screenWidth", "width", "_screenHeight", "height", "_screenRatio", "scaleX", "scaleY", "_scale", "Math", "min", "max", "minScale", "maxScale", "toFixed", "_baseRatio", "setDesignResolutionSize", "scene", "getScene", "adaptSceneCanvas", "on", "EVENT_AFTER_SCENE_LAUNCH", "onSceneLaunched", "scheduleOnce", "canvasComponents", "getComponentsInChildren", "for<PERSON>ach", "canvas", "index", "alignCanvasWithScreen", "node", "setPosition", "uiTransform", "getComponent", "setContentSize", "setAnchorPoint", "isMobile", "window", "addEventListener", "handleScreenResize", "oldWidth", "oldHeight", "getScale", "getScreenInfo", "ratio", "scale", "enableDebugInfo"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,W,OAAAA,W;AAAiCC,MAAAA,Q,OAAAA,Q;AAAiBC,MAAAA,M,OAAAA,M;;AAC5FC,MAAAA,U,iBAAAA,U;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;AAE9B;AACA;AACA;AACA;AACA;;mCAEaW,iB,WADZF,OAAO,CAAC,mBAAD,C,mCAAR,MACaE,iBADb,SACuCV,SADvC,CACiD;AAAA;AAAA;;AAAA,gDAKd,CALc;;AAAA,iDAMb,CANa;;AAAA,gDAOd,CAPc;;AAAA,0CAQpB,GARoB;;AAAA,8CAWhB;AAAA;AAAA,wCAAWM,MAAX,CAAkBK,SAXF;;AAAA,+CAYf;AAAA;AAAA,wCAAWL,MAAX,CAAkBM,UAZH;;AAAA,8CAahB,KAAKC,UAAL,GAAkB,KAAKC,WAbP;AAAA;;AAenB,mBAARC,QAAQ,GAAsB;AAC5C,iBAAOL,iBAAiB,CAACM,SAAzB;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrBP,UAAAA,iBAAiB,CAACM,SAAlB,GAA8B,IAA9B;AACAE,UAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,cAAIV,iBAAiB,CAACM,SAAlB,KAAgC,IAApC,EAA0C;AACtCN,YAAAA,iBAAiB,CAACM,SAAlB,GAA8B,IAA9B;AACH;AACJ;AAED;AACJ;AACA;;;AACWK,QAAAA,IAAI,GAAS;AAChB,cAAI,CAAC;AAAA;AAAA,wCAAWf,MAAX,CAAkBgB,sBAAvB,EAA+C;AAC3CJ,YAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACA;AACH;;AAEDD,UAAAA,OAAO,CAACC,GAAR,CAAY,mCAAZ,EANgB,CAQhB;;AACA,eAAKI,gBAAL,GATgB,CAWhB;;AACA,eAAKC,qBAAL,GAZgB,CAchB;;AACA,eAAKC,yBAAL;AAEAP,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ;AACA,eAAKO,cAAL;AACH;AAED;AACJ;AACA;;;AACYH,QAAAA,gBAAgB,GAAS;AAC7B;AACA,gBAAMI,UAAU,GAAGrB,MAAM,CAACqB,UAA1B;AACA,eAAKC,YAAL,GAAoBD,UAAU,CAACE,KAA/B;AACA,eAAKC,aAAL,GAAqBH,UAAU,CAACI,MAAhC;AACA,eAAKC,YAAL,GAAoB,KAAKJ,YAAL,GAAoB,KAAKE,aAA7C,CAL6B,CAO7B;;AACA,gBAAMG,MAAM,GAAG,KAAKL,YAAL,GAAoB,KAAKf,UAAxC;AACA,gBAAMqB,MAAM,GAAG,KAAKJ,aAAL,GAAqB,KAAKhB,WAAzC;AACA,eAAKqB,MAAL,GAAcC,IAAI,CAACC,GAAL,CAASJ,MAAT,EAAiBC,MAAjB,CAAd,CAV6B,CAY7B;;AACA,eAAKC,MAAL,GAAcC,IAAI,CAACE,GAAL,CAAS;AAAA;AAAA,wCAAWhC,MAAX,CAAkBiC,QAA3B,EACQH,IAAI,CAACC,GAAL,CAAS;AAAA;AAAA,wCAAW/B,MAAX,CAAkBkC,QAA3B,EAAqC,KAAKL,MAA1C,CADR,CAAd;AAGAjB,UAAAA,OAAO,CAACC,GAAR,CAAa,6BAAb;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAa,6BAA4B,KAAKS,YAAa,IAAG,KAAKE,aAAc,EAAjF;AACAZ,UAAAA,OAAO,CAACC,GAAR,CAAa,6BAA4B,KAAKa,YAAL,CAAkBS,OAAlB,CAA0B,CAA1B,CAA6B,EAAtE;AACAvB,UAAAA,OAAO,CAACC,GAAR,CAAa,6BAA4B,KAAKuB,UAAL,CAAgBD,OAAhB,CAAwB,CAAxB,CAA2B,EAApE;AACAvB,UAAAA,OAAO,CAACC,GAAR,CAAa,6BAA4B,KAAKgB,MAAL,CAAYM,OAAZ,CAAoB,CAApB,CAAuB,EAAhE;AACH;AAED;AACJ;AACA;;;AACYjB,QAAAA,qBAAqB,GAAS;AAClC;AACAtB,UAAAA,IAAI,CAACyC,uBAAL,CAA6B,KAAK9B,UAAlC,EAA8C,KAAKC,WAAnD,EAAgE,CAAhE,EAFkC,CAEkC;;AAEpEI,UAAAA,OAAO,CAACC,GAAR,CAAa,gCAA+B,KAAKN,UAAW,IAAG,KAAKC,WAAY,aAAhF,EAJkC,CAMlC;;AACA,gBAAM8B,KAAK,GAAGvC,QAAQ,CAACwC,QAAT,EAAd;;AACA,cAAID,KAAJ,EAAW;AACP,iBAAKE,gBAAL,CAAsBF,KAAtB;AACH,WAViC,CAYlC;;;AACAvC,UAAAA,QAAQ,CAAC0C,EAAT,CAAY1C,QAAQ,CAAC2C,wBAArB,EAA+C,KAAKC,eAApD,EAAqE,IAArE;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,eAAe,CAACL,KAAD,EAAqB;AACxC,eAAKM,YAAL,CAAkB,MAAM;AACpB,iBAAKJ,gBAAL,CAAsBF,KAAtB;AACH,WAFD,EAEG,CAFH;AAGH;AAED;AACJ;AACA;;;AACYE,QAAAA,gBAAgB,CAACF,KAAD,EAAqB;AACzC,gBAAMO,gBAAgB,GAAGP,KAAK,CAACQ,uBAAN,CAA8BnD,MAA9B,CAAzB;AAEAkD,UAAAA,gBAAgB,CAACE,OAAjB,CAAyB,CAACC,MAAD,EAASC,KAAT,KAAmB;AACxCrC,YAAAA,OAAO,CAACC,GAAR,CAAa,gCAA+BoC,KAAM,GAAlD,EADwC,CAGxC;;AACAD,YAAAA,MAAM,CAACE,qBAAP,GAA+B,KAA/B,CAJwC,CAMxC;;AACAF,YAAAA,MAAM,CAACG,IAAP,CAAYC,WAAZ,CAAwB,CAAxB,EAA2B,CAA3B,EAA8B,CAA9B,EAPwC,CASxC;;AACA,kBAAMC,WAAW,GAAGL,MAAM,CAACM,YAAP,CAAoBxD,WAApB,CAApB;;AACA,gBAAIuD,WAAJ,EAAiB;AACbA,cAAAA,WAAW,CAACE,cAAZ,CAA2B,KAAKhD,UAAhC,EAA4C,KAAKC,WAAjD;AACA6C,cAAAA,WAAW,CAACG,cAAZ,CAA2B,GAA3B,EAAgC,GAAhC;AACH;;AAED5C,YAAAA,OAAO,CAACC,GAAR,CAAa,8BAA6BoC,KAAM,cAAa,KAAK1C,UAAW,IAAG,KAAKC,WAAY,cAAjG;AACH,WAjBD;AAkBH,SApI4C,CAsI7C;AACA;;AAEA;AACJ;AACA;;;AACYW,QAAAA,yBAAyB,GAAS;AACtC;AACA,cAAItB,GAAG,CAAC4D,QAAR,EAAkB;AACdC,YAAAA,MAAM,CAACC,gBAAP,CAAwB,mBAAxB,EAA6C,MAAM;AAC/C,mBAAKf,YAAL,CAAkB,MAAM;AACpBhC,gBAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACA,qBAAK+C,kBAAL;AACH,eAHD,EAGG,GAHH,EAD+C,CAItC;AACZ,aALD;AAMH,WATqC,CAWtC;;;AACAF,UAAAA,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,MAAM;AACpC,iBAAKf,YAAL,CAAkB,MAAM;AACpBhC,cAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACA,mBAAK+C,kBAAL;AACH,aAHD,EAGG,GAHH;AAIH,WALD;AAOAhD,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ;AACH;AAED;AACJ;AACA;;;AACY+C,QAAAA,kBAAkB,GAAS;AAC/B,gBAAMC,QAAQ,GAAG,KAAKvC,YAAtB;AACA,gBAAMwC,SAAS,GAAG,KAAKtC,aAAvB,CAF+B,CAI/B;;AACA,eAAKP,gBAAL,GAL+B,CAO/B;;AACA,cAAI4C,QAAQ,KAAK,KAAKvC,YAAlB,IAAkCwC,SAAS,KAAK,KAAKtC,aAAzD,EAAwE;AACpEZ,YAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8BgD,QAAS,IAAGC,SAAU,MAAK,KAAKxC,YAAa,IAAG,KAAKE,aAAc,EAA9G,EADoE,CAGpE;;AACA,iBAAKN,qBAAL;AAEAN,YAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ;AACA,iBAAKO,cAAL;AACH,WARD,MAQO;AACHR,YAAAA,OAAO,CAACC,GAAR,CAAY,wCAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACWkD,QAAAA,QAAQ,GAAW;AACtB,iBAAO,KAAKlC,MAAZ;AACH;AAED;AACJ;AACA;;;AACWmC,QAAAA,aAAa,GAAoE;AACpF,iBAAO;AACHzC,YAAAA,KAAK,EAAE,KAAKD,YADT;AAEHG,YAAAA,MAAM,EAAE,KAAKD,aAFV;AAGHyC,YAAAA,KAAK,EAAE,KAAKvC,YAHT;AAIHwC,YAAAA,KAAK,EAAE,KAAKrC;AAJT,WAAP;AAMH;AAED;AACJ;AACA;;;AACWT,QAAAA,cAAc,GAAS;AAC1B,cAAI,CAAC;AAAA;AAAA,wCAAWpB,MAAX,CAAkBmE,eAAvB,EAAwC;AAExCvD,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQ,KAAKS,YAAa,IAAG,KAAKE,aAAc,EAA7D;AACAZ,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQ,KAAKN,UAAW,IAAG,KAAKC,WAAY,EAAzD;AACAI,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQ,KAAKa,YAAL,CAAkBS,OAAlB,CAA0B,CAA1B,CAA6B,EAAlD;AACAvB,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQ,KAAKuB,UAAL,CAAgBD,OAAhB,CAAwB,CAAxB,CAA2B,EAAhD;AACAvB,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQ,KAAKgB,MAAL,CAAYM,OAAZ,CAAoB,CAApB,CAAuB,EAA5C;AACAvB,UAAAA,OAAO,CAACC,GAAR,CAAa,UAAS,KAAKS,YAAa,IAAG,KAAKE,aAAc,OAA9D;AACAZ,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACH;;AA3N4C,O,wCAEC,I", "sourcesContent": ["import { _decorator, Component, Canvas, view, sys, UITransform, Widget, No<PERSON>, Vec<PERSON>, director, Scene, screen } from 'cc';\nimport { GameConfig } from '../config/GameConfig';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 响应式UI适配器\n * 使用响应式设计理念，根据屏幕尺寸动态调整UI元素\n * 解决固定分辨率适配导致的点击偏移问题\n */\n@ccclass('ResponsiveAdapter')\nexport class ResponsiveAdapter extends Component {\n    \n    private static _instance: ResponsiveAdapter = null;\n    \n    // 当前屏幕信息\n    private _screenWidth: number = 0;\n    private _screenHeight: number = 0;\n    private _screenRatio: number = 0;\n    private _scale: number = 1.0;\n    \n    // 基准信息\n    private _baseWidth: number = GameConfig.screen.baseWidth;\n    private _baseHeight: number = GameConfig.screen.baseHeight;\n    private _baseRatio: number = this._baseWidth / this._baseHeight;\n    \n    public static get instance(): ResponsiveAdapter {\n        return ResponsiveAdapter._instance;\n    }\n    \n    protected onLoad(): void {\n        ResponsiveAdapter._instance = this;\n        console.log('[ResponsiveAdapter] 响应式适配器初始化');\n    }\n    \n    protected onDestroy(): void {\n        if (ResponsiveAdapter._instance === this) {\n            ResponsiveAdapter._instance = null;\n        }\n    }\n    \n    /**\n     * 初始化响应式适配\n     */\n    public init(): void {\n        if (!GameConfig.screen.enableResponsiveDesign) {\n            console.log('[ResponsiveAdapter] 响应式设计已禁用，跳过初始化');\n            return;\n        }\n        \n        console.log('[ResponsiveAdapter] 开始初始化响应式适配...');\n        \n        // 获取屏幕信息\n        this.updateScreenInfo();\n        \n        // 设置Canvas为响应式模式\n        this.setupResponsiveCanvas();\n        \n        // 监听屏幕尺寸变化\n        this.setupScreenResizeListener();\n        \n        console.log('[ResponsiveAdapter] 响应式适配初始化完成');\n        this.printDebugInfo();\n    }\n    \n    /**\n     * 更新屏幕信息\n     */\n    private updateScreenInfo(): void {\n        // 使用新的API获取屏幕尺寸\n        const windowSize = screen.windowSize;\n        this._screenWidth = windowSize.width;\n        this._screenHeight = windowSize.height;\n        this._screenRatio = this._screenWidth / this._screenHeight;\n\n        // 计算缩放比例（基于较小的维度）\n        const scaleX = this._screenWidth / this._baseWidth;\n        const scaleY = this._screenHeight / this._baseHeight;\n        this._scale = Math.min(scaleX, scaleY);\n\n        // 限制缩放范围\n        this._scale = Math.max(GameConfig.screen.minScale,\n                              Math.min(GameConfig.screen.maxScale, this._scale));\n\n        console.log(`[ResponsiveAdapter] 屏幕信息更新:`);\n        console.log(`[ResponsiveAdapter] 屏幕尺寸: ${this._screenWidth}x${this._screenHeight}`);\n        console.log(`[ResponsiveAdapter] 屏幕比例: ${this._screenRatio.toFixed(3)}`);\n        console.log(`[ResponsiveAdapter] 基准比例: ${this._baseRatio.toFixed(3)}`);\n        console.log(`[ResponsiveAdapter] 计算缩放: ${this._scale.toFixed(3)}`);\n    }\n    \n    /**\n     * 设置Canvas为响应式模式\n     */\n    private setupResponsiveCanvas(): void {\n        // 使用SHOW_ALL策略，确保内容完整显示且不变形\n        view.setDesignResolutionSize(this._baseWidth, this._baseHeight, 2); // 2 = SHOW_ALL\n\n        console.log(`[ResponsiveAdapter] 设置设计分辨率: ${this._baseWidth}x${this._baseHeight} (SHOW_ALL)`);\n\n        // 确保所有Canvas都使用正确的设置\n        const scene = director.getScene();\n        if (scene) {\n            this.adaptSceneCanvas(scene);\n        }\n\n        // 监听场景切换\n        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);\n    }\n    \n    /**\n     * 场景启动后的回调\n     */\n    private onSceneLaunched(scene: Scene): void {\n        this.scheduleOnce(() => {\n            this.adaptSceneCanvas(scene);\n        }, 0);\n    }\n    \n    /**\n     * 适配场景中的Canvas\n     */\n    private adaptSceneCanvas(scene: Scene): void {\n        const canvasComponents = scene.getComponentsInChildren(Canvas);\n\n        canvasComponents.forEach((canvas, index) => {\n            console.log(`[ResponsiveAdapter] 适配Canvas[${index}]`);\n\n            // 禁用自动对齐屏幕，避免坐标系统混乱\n            canvas.alignCanvasWithScreen = false;\n\n            // 设置Canvas位置为原点\n            canvas.node.setPosition(0, 0, 0);\n\n            // 设置Canvas尺寸为设计分辨率\n            const uiTransform = canvas.getComponent(UITransform);\n            if (uiTransform) {\n                uiTransform.setContentSize(this._baseWidth, this._baseHeight);\n                uiTransform.setAnchorPoint(0.5, 0.5);\n            }\n\n            console.log(`[ResponsiveAdapter] Canvas[${index}] 设置完成: 尺寸=${this._baseWidth}x${this._baseHeight}, 位置=(0,0,0)`);\n        });\n    }\n    \n    // 移除复杂的UI元素适配逻辑\n    // 使用SHOW_ALL策略让Cocos Creator自动处理适配\n    \n    /**\n     * 设置屏幕尺寸变化监听\n     */\n    private setupScreenResizeListener(): void {\n        // 监听屏幕方向变化\n        if (sys.isMobile) {\n            window.addEventListener('orientationchange', () => {\n                this.scheduleOnce(() => {\n                    console.log('[ResponsiveAdapter] 检测到屏幕方向变化');\n                    this.handleScreenResize();\n                }, 0.2); // 增加延迟确保方向变化完成\n            });\n        }\n\n        // 监听窗口尺寸变化（桌面环境）\n        window.addEventListener('resize', () => {\n            this.scheduleOnce(() => {\n                console.log('[ResponsiveAdapter] 检测到窗口尺寸变化');\n                this.handleScreenResize();\n            }, 0.1);\n        });\n\n        console.log('[ResponsiveAdapter] 屏幕尺寸变化监听已设置');\n    }\n    \n    /**\n     * 处理屏幕尺寸变化\n     */\n    private handleScreenResize(): void {\n        const oldWidth = this._screenWidth;\n        const oldHeight = this._screenHeight;\n\n        // 更新屏幕信息\n        this.updateScreenInfo();\n\n        // 检查尺寸是否真的发生了变化\n        if (oldWidth !== this._screenWidth || oldHeight !== this._screenHeight) {\n            console.log(`[ResponsiveAdapter] 屏幕尺寸变化: ${oldWidth}x${oldHeight} → ${this._screenWidth}x${this._screenHeight}`);\n\n            // 重新设置Canvas\n            this.setupResponsiveCanvas();\n\n            console.log('[ResponsiveAdapter] 屏幕尺寸变化适配完成');\n            this.printDebugInfo();\n        } else {\n            console.log('[ResponsiveAdapter] 屏幕尺寸未发生实际变化，跳过重新适配');\n        }\n    }\n    \n    /**\n     * 获取当前缩放比例\n     */\n    public getScale(): number {\n        return this._scale;\n    }\n    \n    /**\n     * 获取屏幕信息\n     */\n    public getScreenInfo(): { width: number, height: number, ratio: number, scale: number } {\n        return {\n            width: this._screenWidth,\n            height: this._screenHeight,\n            ratio: this._screenRatio,\n            scale: this._scale\n        };\n    }\n    \n    /**\n     * 打印调试信息\n     */\n    public printDebugInfo(): void {\n        if (!GameConfig.screen.enableDebugInfo) return;\n        \n        console.log('=== 响应式适配调试信息 ===');\n        console.log(`屏幕尺寸: ${this._screenWidth}x${this._screenHeight}`);\n        console.log(`基准尺寸: ${this._baseWidth}x${this._baseHeight}`);\n        console.log(`屏幕比例: ${this._screenRatio.toFixed(3)}`);\n        console.log(`基准比例: ${this._baseRatio.toFixed(3)}`);\n        console.log(`缩放比例: ${this._scale.toFixed(3)}`);\n        console.log(`设计分辨率: ${this._screenWidth}x${this._screenHeight} (原生)`);\n        console.log('========================');\n    }\n}\n"]}