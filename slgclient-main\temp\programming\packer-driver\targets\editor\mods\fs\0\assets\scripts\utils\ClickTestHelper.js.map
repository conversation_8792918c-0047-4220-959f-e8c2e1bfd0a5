{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts"], "names": ["_decorator", "Component", "Node", "UITransform", "Vec3", "Label", "view", "sys", "director", "screen", "FixedScreenAdapter", "ccclass", "property", "ClickTestHelper", "onLoad", "node", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "createDebugDisplay", "schedule", "updateDebugInfo", "onDestroy", "off", "unschedule", "debugLabel", "debugNode", "setParent", "uiTransform", "addComponent", "setContentSize", "fontSize", "lineHeight", "string", "visibleSize", "getVisibleSize", "setPosition", "width", "height", "event", "_touchCount", "touchPos", "getUILocation", "worldPos", "x", "y", "adaptInfo", "instance", "getAdaptInfo", "console", "log", "toFixed", "windowSize", "visible<PERSON><PERSON>in", "scaleX", "scaleY", "designSize", "checkClickedElement", "updateTouchDebugInfo", "delta", "<PERSON><PERSON><PERSON><PERSON>", "Math", "abs", "canvas", "root", "mainWindow", "designResolution", "action", "deviceType", "isMobile", "debugText", "safeAreaInfo", "safeArea", "getSafeAreaRect", "printAdaptInfo", "printDebugInfo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAkBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;;AACxFC,MAAAA,kB,iBAAAA,kB;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;AAE9B;AACA;AACA;AACA;;iCAEaa,e,WADZF,OAAO,CAAC,iBAAD,C,UAGHC,QAAQ,CAACP,KAAD,C,oCAHb,MACaQ,eADb,SACqCZ,SADrC,CAC+C;AAAA;AAAA;;AAAA;;AAAA,+CAKb,CALa;AAAA;;AAOjCa,QAAAA,MAAM,GAAS;AACrB;AACA,eAAKC,IAAL,CAAUC,EAAV,CAAad,IAAI,CAACe,SAAL,CAAeC,WAA5B,EAAyC,KAAKC,YAA9C,EAA4D,IAA5D;AACA,eAAKJ,IAAL,CAAUC,EAAV,CAAad,IAAI,CAACe,SAAL,CAAeG,UAA5B,EAAwC,KAAKC,WAA7C,EAA0D,IAA1D;AACA,eAAKN,IAAL,CAAUC,EAAV,CAAad,IAAI,CAACe,SAAL,CAAeK,SAA5B,EAAuC,KAAKC,UAA5C,EAAwD,IAAxD,EAJqB,CAMrB;;AACA,eAAKC,kBAAL,GAPqB,CASrB;;AACA,eAAKC,QAAL,CAAc,KAAKC,eAAnB,EAAoC,GAApC;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKZ,IAAL,CAAUa,GAAV,CAAc1B,IAAI,CAACe,SAAL,CAAeC,WAA7B,EAA0C,KAAKC,YAA/C,EAA6D,IAA7D;AACA,eAAKJ,IAAL,CAAUa,GAAV,CAAc1B,IAAI,CAACe,SAAL,CAAeG,UAA7B,EAAyC,KAAKC,WAA9C,EAA2D,IAA3D;AACA,eAAKN,IAAL,CAAUa,GAAV,CAAc1B,IAAI,CAACe,SAAL,CAAeK,SAA7B,EAAwC,KAAKC,UAA7C,EAAyD,IAAzD;AACA,eAAKM,UAAL,CAAgB,KAAKH,eAArB;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,kBAAkB,GAAS;AAC/B,cAAI,CAAC,KAAKM,UAAV,EAAsB;AAClB;AACA,kBAAMC,SAAS,GAAG,IAAI7B,IAAJ,CAAS,YAAT,CAAlB;AACA6B,YAAAA,SAAS,CAACC,SAAV,CAAoB,KAAKjB,IAAzB,EAHkB,CAKlB;;AACA,kBAAMkB,WAAW,GAAGF,SAAS,CAACG,YAAV,CAAuB/B,WAAvB,CAApB;AACA8B,YAAAA,WAAW,CAACE,cAAZ,CAA2B,GAA3B,EAAgC,GAAhC,EAPkB,CASlB;;AACA,iBAAKL,UAAL,GAAkBC,SAAS,CAACG,YAAV,CAAuB7B,KAAvB,CAAlB;AACA,iBAAKyB,UAAL,CAAgBM,QAAhB,GAA2B,EAA3B;AACA,iBAAKN,UAAL,CAAgBO,UAAhB,GAA6B,EAA7B;AACA,iBAAKP,UAAL,CAAgBQ,MAAhB,GAAyB,WAAzB,CAbkB,CAelB;;AACA,kBAAMC,WAAW,GAAGjC,IAAI,CAACkC,cAAL,EAApB;AACAT,YAAAA,SAAS,CAACU,WAAV,CAAsB,CAACF,WAAW,CAACG,KAAb,GAAmB,CAAnB,GAAuB,GAA7C,EAAkDH,WAAW,CAACI,MAAZ,GAAmB,CAAnB,GAAuB,GAAzE,EAA8E,CAA9E;AACH;AACJ;AAED;AACJ;AACA;;;AACYxB,QAAAA,YAAY,CAACyB,KAAD,EAA0B;AAC1C,eAAKC,WAAL;AACA,gBAAMC,QAAQ,GAAGF,KAAK,CAACG,aAAN,EAAjB;AACA,gBAAMC,QAAQ,GAAG,IAAI5C,IAAJ,CAAS0C,QAAQ,CAACG,CAAlB,EAAqBH,QAAQ,CAACI,CAA9B,EAAiC,CAAjC,CAAjB,CAH0C,CAK1C;;AACA,gBAAMC,SAAS,GAAG;AAAA;AAAA,wDAAmBC,QAAnB,GAA8B;AAAA;AAAA,wDAAmBA,QAAnB,CAA4BC,YAA5B,EAA9B,GAA2E,IAA7F;AAEAC,UAAAA,OAAO,CAACC,GAAR,CAAa,qBAAoB,KAAKV,WAAY,GAAlD;AACAS,UAAAA,OAAO,CAACC,GAAR,CAAa,YAAWT,QAAQ,CAACG,CAAT,CAAWO,OAAX,CAAmB,CAAnB,CAAsB,KAAIV,QAAQ,CAACI,CAAT,CAAWM,OAAX,CAAmB,CAAnB,CAAsB,GAAxE;;AACA,cAAIL,SAAJ,EAAe;AACXG,YAAAA,OAAO,CAACC,GAAR,CAAa,WAAUJ,SAAS,CAACM,UAAV,CAAqBf,KAAM,IAAGS,SAAS,CAACM,UAAV,CAAqBd,MAAO,EAAjF;AACAW,YAAAA,OAAO,CAACC,GAAR,CAAa,WAAUJ,SAAS,CAACZ,WAAV,CAAsBG,KAAtB,CAA4Bc,OAA5B,CAAoC,CAApC,CAAuC,IAAGL,SAAS,CAACZ,WAAV,CAAsBI,MAAtB,CAA6Ba,OAA7B,CAAqC,CAArC,CAAwC,EAAzG;AACAF,YAAAA,OAAO,CAACC,GAAR,CAAa,YAAWJ,SAAS,CAACO,aAAV,CAAwBT,CAAxB,CAA0BO,OAA1B,CAAkC,CAAlC,CAAqC,KAAIL,SAAS,CAACO,aAAV,CAAwBR,CAAxB,CAA0BM,OAA1B,CAAkC,CAAlC,CAAqC,GAAtG;AACAF,YAAAA,OAAO,CAACC,GAAR,CAAa,aAAYJ,SAAS,CAACQ,MAAV,CAAiBH,OAAjB,CAAyB,CAAzB,CAA4B,OAAML,SAAS,CAACS,MAAV,CAAiBJ,OAAjB,CAAyB,CAAzB,CAA4B,EAAvF;AACH;;AACDF,UAAAA,OAAO,CAACC,GAAR,CAAa,YAAWJ,SAAS,CAACU,UAAV,CAAqBnB,KAAM,IAAGS,SAAS,CAACU,UAAV,CAAqBlB,MAAO,EAAlF;AACAW,UAAAA,OAAO,CAACC,GAAR,CAAa,WAAUJ,SAAS,CAACZ,WAAV,CAAsBG,KAAtB,CAA4Bc,OAA5B,CAAoC,CAApC,CAAuC,IAAGL,SAAS,CAACZ,WAAV,CAAsBI,MAAtB,CAA6Ba,OAA7B,CAAqC,CAArC,CAAwC,EAAzG;AACAF,UAAAA,OAAO,CAACC,GAAR,CAAa,aAAYJ,SAAS,CAACQ,MAAV,CAAiBH,OAAjB,CAAyB,CAAzB,CAA4B,OAAML,SAAS,CAACS,MAAV,CAAiBJ,OAAjB,CAAyB,CAAzB,CAA4B,EAAvF,EAlB0C,CAoB1C;;AACA,eAAKM,mBAAL,CAAyBd,QAAzB,EArB0C,CAuB1C;;AACA,eAAKe,oBAAL,CAA0BjB,QAA1B,EAAoC,IAApC;AACH;AAED;AACJ;AACA;;;AACYzB,QAAAA,WAAW,CAACuB,KAAD,EAA0B;AACzC,gBAAME,QAAQ,GAAGF,KAAK,CAACG,aAAN,EAAjB,CADyC,CAEzC;;AACA,gBAAMiB,KAAK,GAAGpB,KAAK,CAACqB,QAAN,EAAd;;AACA,cAAIC,IAAI,CAACC,GAAL,CAASH,KAAK,CAACf,CAAf,IAAoB,CAApB,IAAyBiB,IAAI,CAACC,GAAL,CAASH,KAAK,CAACd,CAAf,IAAoB,CAAjD,EAAoD;AAChD;AACA,kBAAMkB,MAAM,GAAG5D,QAAQ,CAAC6D,IAAT,CAAeC,UAA9B;AACA,kBAAMT,UAAU,GAAGpD,MAAM,CAAC8D,gBAA1B;AACA,kBAAMd,UAAU,GAAGhD,MAAM,CAACgD,UAA1B;AAEAH,YAAAA,OAAO,CAACC,GAAR,CAAa,0BAAyBT,QAAQ,CAACG,CAAT,CAAWO,OAAX,CAAmB,CAAnB,CAAsB,KAAIV,QAAQ,CAACI,CAAT,CAAWM,OAAX,CAAmB,CAAnB,CAAsB,GAAtF;AACAF,YAAAA,OAAO,CAACC,GAAR,CAAa,0BAAyBM,UAAU,CAACnB,KAAM,IAAGmB,UAAU,CAAClB,MAAO,SAAQc,UAAU,CAACf,KAAM,IAAGe,UAAU,CAACd,MAAO,EAA1H;AACH;AACJ;AAED;AACJ;AACA;;;AACYpB,QAAAA,UAAU,CAACqB,KAAD,EAA0B;AACxC,gBAAME,QAAQ,GAAGF,KAAK,CAACG,aAAN,EAAjB;AACAO,UAAAA,OAAO,CAACC,GAAR,CAAa,0BAAyBT,QAAQ,CAACG,CAAT,CAAWO,OAAX,CAAmB,CAAnB,CAAsB,KAAIV,QAAQ,CAACI,CAAT,CAAWM,OAAX,CAAmB,CAAnB,CAAsB,GAAtF,EAFwC,CAIxC;;AACA,eAAKO,oBAAL,CAA0BjB,QAA1B,EAAoC,IAApC;AACH;AAED;AACJ;AACA;;;AACYgB,QAAAA,mBAAmB,CAACd,QAAD,EAAuB;AAC9C;AACA;AACAM,UAAAA,OAAO,CAACC,GAAR,CAAa,sBAAqBP,QAAQ,CAACC,CAAT,CAAWO,OAAX,CAAmB,CAAnB,CAAsB,KAAIR,QAAQ,CAACE,CAAT,CAAWM,OAAX,CAAmB,CAAnB,CAAsB,SAAlF;AACH;AAED;AACJ;AACA;;;AACYO,QAAAA,oBAAoB,CAACjB,QAAD,EAAgB0B,MAAhB,EAAsC;AAC9D,cAAI,CAAC,KAAK1C,UAAV,EAAsB;AAEtB,gBAAMqB,SAAS,GAAG;AAAA;AAAA,wDAAmBC,QAAnB,GAA8B;AAAA;AAAA,wDAAmBA,QAAnB,CAA4BC,YAA5B,EAA9B,GAA2E,IAA7F;AACA,gBAAMoB,UAAU,GAAGlE,GAAG,CAACmE,QAAJ,GAAe,QAAf,GAA0B,SAA7C;AAEA,gBAAMC,SAAS,GAAI;AAC3B,QAAQF,UAAW;AACnB,QAAQ,KAAK5B,WAAY;AACzB,IAAI2B,MAAO,MAAK1B,QAAQ,CAACG,CAAT,CAAWO,OAAX,CAAmB,CAAnB,CAAsB,KAAIV,QAAQ,CAACI,CAAT,CAAWM,OAAX,CAAmB,CAAnB,CAAsB;AAChE,EAAEL,SAAS,GAAI,SAAQA,SAAS,CAACM,UAAV,CAAqBf,KAAM,IAAGS,SAAS,CAACM,UAAV,CAAqBd,MAAO;AACjF,QAAQQ,SAAS,CAACZ,WAAV,CAAsBG,KAAtB,CAA4Bc,OAA5B,CAAoC,CAApC,CAAuC,IAAGL,SAAS,CAACZ,WAAV,CAAsBI,MAAtB,CAA6Ba,OAA7B,CAAqC,CAArC,CAAwC;AAC1F,UAAUL,SAAS,CAACQ,MAAV,CAAiBH,OAAjB,CAAyB,CAAzB,CAA4B,OAAML,SAAS,CAACS,MAAV,CAAiBJ,OAAjB,CAAyB,CAAzB,CAA4B,EAF7D,GAEiE,SAAU,EAN9E;AAQA,eAAK1B,UAAL,CAAgBQ,MAAhB,GAAyBqC,SAAzB;AACH;AAED;AACJ;AACA;;;AACYjD,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAKI,UAAV,EAAsB;AAEtB,gBAAMqB,SAAS,GAAG;AAAA;AAAA,wDAAmBC,QAAnB,GAA8B;AAAA;AAAA,wDAAmBA,QAAnB,CAA4BC,YAA5B,EAA9B,GAA2E,IAA7F;AACA,gBAAMoB,UAAU,GAAGlE,GAAG,CAACmE,QAAJ,GAAe,QAAf,GAA0B,SAA7C;AAEA,cAAIE,YAAY,GAAG,EAAnB;;AACA,cAAIrE,GAAG,CAACmE,QAAR,EAAkB;AACd,kBAAMG,QAAQ,GAAGtE,GAAG,CAACuE,eAAJ,EAAjB;AACAF,YAAAA,YAAY,GAAI,WAAUC,QAAQ,CAACnC,KAAT,CAAec,OAAf,CAAuB,CAAvB,CAA0B,IAAGqB,QAAQ,CAAClC,MAAT,CAAgBa,OAAhB,CAAwB,CAAxB,CAA2B,EAAlF;AACH;;AAED,gBAAMmB,SAAS,GAAI;AAC3B,QAAQF,UAAW;AACnB,QAAQ,KAAK5B,WAAY;AACzB,EAAEM,SAAS,GAAI,SAAQA,SAAS,CAACM,UAAV,CAAqBf,KAAM,IAAGS,SAAS,CAACM,UAAV,CAAqBd,MAAO;AACjF,QAAQQ,SAAS,CAACZ,WAAV,CAAsBG,KAAtB,CAA4Bc,OAA5B,CAAoC,CAApC,CAAuC,IAAGL,SAAS,CAACZ,WAAV,CAAsBI,MAAtB,CAA6Ba,OAA7B,CAAqC,CAArC,CAAwC;AAC1F,UAAUL,SAAS,CAACQ,MAAV,CAAiBH,OAAjB,CAAyB,CAAzB,CAA4B,OAAML,SAAS,CAACS,MAAV,CAAiBJ,OAAjB,CAAyB,CAAzB,CAA4B,EAF7D,GAEiE,SAAU,GAAEoB,YAAa;AACrG,YANQ;AAQA,eAAK9C,UAAL,CAAgBQ,MAAhB,GAAyBqC,SAAzB;AACH;AAED;AACJ;AACA;;;AACWI,QAAAA,cAAc,GAAS;AAC1B,cAAI;AAAA;AAAA,wDAAmB3B,QAAvB,EAAiC;AAC7B;AAAA;AAAA,0DAAmBA,QAAnB,CAA4B4B,cAA5B;AACH;;AACD1B,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAa,UAAS,KAAKV,WAAY,EAAvC;AACAS,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACH;;AAhL0C,O;;;;;iBAGvB,I", "sourcesContent": ["import { _decorator, Component, Node, EventTouch, UITransform, Vec3, Label, view, sys, director, screen } from 'cc';\nimport { FixedScreenAdapter } from './FixedScreenAdapter';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 点击测试助手\n * 用于测试和调试点击功能是否正常\n */\n@ccclass('ClickTestHelper')\nexport class ClickTestHelper extends Component {\n    \n    @property(Label)\n    debugLabel: Label = null;\n    \n    private _touchCount: number = 0;\n    \n    protected onLoad(): void {\n        // 监听全局触摸事件\n        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);\n        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);\n        \n        // 创建调试信息显示\n        this.createDebugDisplay();\n        \n        // 定期更新调试信息\n        this.schedule(this.updateDebugInfo, 1.0);\n    }\n    \n    protected onDestroy(): void {\n        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);\n        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);\n        this.unschedule(this.updateDebugInfo);\n    }\n    \n    /**\n     * 创建调试信息显示\n     */\n    private createDebugDisplay(): void {\n        if (!this.debugLabel) {\n            // 创建调试标签节点\n            const debugNode = new Node('DebugLabel');\n            debugNode.setParent(this.node);\n            \n            // 添加UITransform组件\n            const uiTransform = debugNode.addComponent(UITransform);\n            uiTransform.setContentSize(400, 200);\n            \n            // 添加Label组件\n            this.debugLabel = debugNode.addComponent(Label);\n            this.debugLabel.fontSize = 20;\n            this.debugLabel.lineHeight = 25;\n            this.debugLabel.string = '点击测试助手已启动';\n            \n            // 设置位置（左上角）\n            const visibleSize = view.getVisibleSize();\n            debugNode.setPosition(-visibleSize.width/2 + 200, visibleSize.height/2 - 100, 0);\n        }\n    }\n    \n    /**\n     * 触摸开始事件\n     */\n    private onTouchStart(event: EventTouch): void {\n        this._touchCount++;\n        const touchPos = event.getUILocation();\n        const worldPos = new Vec3(touchPos.x, touchPos.y, 0);\n\n        // 获取更多调试信息\n        const adaptInfo = FixedScreenAdapter.instance ? FixedScreenAdapter.instance.getAdaptInfo() : null;\n\n        console.log(`[ClickTest] 触摸开始 #${this._touchCount}:`);\n        console.log(`  UI坐标: (${touchPos.x.toFixed(1)}, ${touchPos.y.toFixed(1)})`);\n        if (adaptInfo) {\n            console.log(`  窗口尺寸: ${adaptInfo.windowSize.width}x${adaptInfo.windowSize.height}`);\n            console.log(`  可视区域: ${adaptInfo.visibleSize.width.toFixed(1)}x${adaptInfo.visibleSize.height.toFixed(1)}`);\n            console.log(`  可视原点: (${adaptInfo.visibleOrigin.x.toFixed(1)}, ${adaptInfo.visibleOrigin.y.toFixed(1)})`);\n            console.log(`  缩放比例: X=${adaptInfo.scaleX.toFixed(3)}, Y=${adaptInfo.scaleY.toFixed(3)}`);\n        }\n        console.log(`  设计分辨率: ${adaptInfo.designSize.width}x${adaptInfo.designSize.height}`);\n        console.log(`  可视区域: ${adaptInfo.visibleSize.width.toFixed(0)}x${adaptInfo.visibleSize.height.toFixed(0)}`);\n        console.log(`  缩放比例: X=${adaptInfo.scaleX.toFixed(3)}, Y=${adaptInfo.scaleY.toFixed(3)}`);\n\n        // 检查点击的UI元素\n        this.checkClickedElement(worldPos);\n\n        // 更新调试信息\n        this.updateTouchDebugInfo(touchPos, '开始');\n    }\n    \n    /**\n     * 触摸移动事件\n     */\n    private onTouchMove(event: EventTouch): void {\n        const touchPos = event.getUILocation();\n        // 只在移动距离较大时记录\n        const delta = event.getDelta();\n        if (Math.abs(delta.x) > 5 || Math.abs(delta.y) > 5) {\n            // 获取屏幕适配信息\n            const canvas = director.root!.mainWindow!;\n            const designSize = screen.designResolution;\n            const windowSize = screen.windowSize;\n\n            console.log(`[ClickTest] 触摸移动: UI坐标(${touchPos.x.toFixed(1)}, ${touchPos.y.toFixed(1)})`);\n            console.log(`[ClickTest] 屏幕适配: 设计分辨率${designSize.width}x${designSize.height}, 窗口大小${windowSize.width}x${windowSize.height}`);\n        }\n    }\n    \n    /**\n     * 触摸结束事件\n     */\n    private onTouchEnd(event: EventTouch): void {\n        const touchPos = event.getUILocation();\n        console.log(`[ClickTest] 触摸结束: UI坐标(${touchPos.x.toFixed(1)}, ${touchPos.y.toFixed(1)})`);\n        \n        // 更新调试信息\n        this.updateTouchDebugInfo(touchPos, '结束');\n    }\n    \n    /**\n     * 检查点击的UI元素\n     */\n    private checkClickedElement(worldPos: Vec3): void {\n        // 这里可以添加具体的UI元素检测逻辑\n        // 例如检查是否点击了按钮、输入框等\n        console.log(`[ClickTest] 检查世界坐标(${worldPos.x.toFixed(1)}, ${worldPos.y.toFixed(1)})处的UI元素`);\n    }\n    \n    /**\n     * 更新触摸调试信息\n     */\n    private updateTouchDebugInfo(touchPos: any, action: string): void {\n        if (!this.debugLabel) return;\n        \n        const adaptInfo = FixedScreenAdapter.instance ? FixedScreenAdapter.instance.getAdaptInfo() : null;\n        const deviceType = sys.isMobile ? 'Mobile' : 'Desktop';\n        \n        const debugText = `点击测试助手\n设备类型: ${deviceType}\n触摸次数: ${this._touchCount}\n最后${action}: (${touchPos.x.toFixed(1)}, ${touchPos.y.toFixed(1)})\n${adaptInfo ? `窗口尺寸: ${adaptInfo.windowSize.width}x${adaptInfo.windowSize.height}\n可视区域: ${adaptInfo.visibleSize.width.toFixed(0)}x${adaptInfo.visibleSize.height.toFixed(0)}\n缩放比例: X=${adaptInfo.scaleX.toFixed(3)}, Y=${adaptInfo.scaleY.toFixed(3)}` : '适配器未初始化'}`;\n        \n        this.debugLabel.string = debugText;\n    }\n    \n    /**\n     * 定期更新调试信息\n     */\n    private updateDebugInfo(): void {\n        if (!this.debugLabel) return;\n        \n        const adaptInfo = FixedScreenAdapter.instance ? FixedScreenAdapter.instance.getAdaptInfo() : null;\n        const deviceType = sys.isMobile ? 'Mobile' : 'Desktop';\n        \n        let safeAreaInfo = '';\n        if (sys.isMobile) {\n            const safeArea = sys.getSafeAreaRect();\n            safeAreaInfo = `\\n安全区域: ${safeArea.width.toFixed(0)}x${safeArea.height.toFixed(0)}`;\n        }\n        \n        const debugText = `点击测试助手\n设备类型: ${deviceType}\n触摸次数: ${this._touchCount}\n${adaptInfo ? `窗口尺寸: ${adaptInfo.windowSize.width}x${adaptInfo.windowSize.height}\n可视区域: ${adaptInfo.visibleSize.width.toFixed(0)}x${adaptInfo.visibleSize.height.toFixed(0)}\n缩放比例: X=${adaptInfo.scaleX.toFixed(3)}, Y=${adaptInfo.scaleY.toFixed(3)}` : '适配器未初始化'}${safeAreaInfo}\n状态: 等待点击...`;\n        \n        this.debugLabel.string = debugText;\n    }\n    \n    /**\n     * 手动触发适配信息打印\n     */\n    public printAdaptInfo(): void {\n        if (FixedScreenAdapter.instance) {\n            FixedScreenAdapter.instance.printDebugInfo();\n        }\n        console.log('=== 点击测试统计 ===');\n        console.log(`总触摸次数: ${this._touchCount}`);\n        console.log('==================');\n    }\n}\n"]}