{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "AudioManager", "LogicEvent", "GeneralCommand", "EventMgr", "ListLogic", "ccclass", "property", "GeneralRosterListL<PERSON><PERSON>", "onEnable", "initGeneralCfg", "onClickClose", "instance", "playClick", "node", "active", "emit", "openGeneral", "cfgs", "getInstance", "proxy", "getGeneralAllCfg", "arr", "Array", "from", "values", "sort", "sortStar", "comp", "scrollView", "getComponent", "setData", "a", "b", "star", "cfgId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;;AACvBC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AAGFC,MAAAA,c;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;;;;;;OALD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAQTU,sB,WADpBF,OAAO,CAAC,wBAAD,C,UAIHC,QAAQ,CAACP,UAAD,C,oCAJb,MACqBQ,sBADrB,SACoDT,SADpD,CAC8D;AAAA;AAAA;;AAAA;AAAA;;AAMhDU,QAAAA,QAAQ,GAAS;AACvB,eAAKC,cAAL;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,WAAzB;AACH;;AAGSP,QAAAA,cAAc,GAAO;AAE3B,cAAIQ,IAAI,GAAG;AAAA;AAAA,gDAAeC,WAAf,GAA6BC,KAA7B,CAAmCC,gBAAnC,EAAX;AACA,cAAIC,GAAG,GAAGC,KAAK,CAACC,IAAN,CAAWN,IAAI,CAACO,MAAL,EAAX,CAAV;AACAH,UAAAA,GAAG,CAACI,IAAJ,CAAS,KAAKC,QAAd;AAEA,cAAIC,IAAI,GAAG,KAAKC,UAAL,CAAgBf,IAAhB,CAAqBgB,YAArB;AAAA;AAAA,qCAAX;AACAF,UAAAA,IAAI,CAACG,OAAL,CAAaT,GAAb;AAEH;;AAESK,QAAAA,QAAQ,CAACK,CAAD,EAAmBC,CAAnB,EAA6C;AAE3D,cAAGD,CAAC,CAACE,IAAF,GAASD,CAAC,CAACC,IAAd,EAAmB;AACf,mBAAO,CAAP;AACH,WAFD,MAEM,IAAGF,CAAC,CAACE,IAAF,IAAUD,CAAC,CAACC,IAAf,EAAoB;AACtB,mBAAOF,CAAC,CAACG,KAAF,GAAUF,CAAC,CAACE,KAAnB;AACH,WAFK,MAED;AACD,mBAAO,CAAC,CAAR;AACH;AACJ;;AArCyD,O;;;;;iBAIlC,I", "sourcesContent": ["\nimport { _decorator, Component, ScrollView } from 'cc';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport { GeneralConfig } from \"../../general/GeneralProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport ListLogic from '../../utils/ListLogic';\n\n@ccclass('GeneralRosterListLogic')\nexport default class GeneralRosterListLogic extends Component {\n\n\n    @property(ScrollView)\n    scrollView:ScrollView = null;\n\n    protected onEnable(): void {\n        this.initGeneralCfg();\n    }\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n        EventMgr.emit(LogicEvent.openGeneral);\n    }\n\n\n    protected initGeneralCfg():void{\n\n        let cfgs = GeneralCommand.getInstance().proxy.getGeneralAllCfg();\n        var arr = Array.from(cfgs.values());\n        arr.sort(this.sortStar);\n\n        var comp = this.scrollView.node.getComponent(ListLogic);\n        comp.setData(arr);\n\n    }\n\n    protected sortStar(a: GeneralConfig, b: GeneralConfig): number {\n\n        if(a.star < b.star){\n            return 1;\n        }else if(a.star == b.star){\n            return a.cfgId - b.cfgId;\n        }else{\n            return -1;\n        }\n    }\n}\n"]}