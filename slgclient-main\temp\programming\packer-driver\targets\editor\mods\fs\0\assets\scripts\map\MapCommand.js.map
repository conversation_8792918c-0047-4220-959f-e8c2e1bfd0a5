{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"], "names": ["MapCommand", "ServerConfig", "GeneralCommand", "NetManager", "DateUtil", "MapBuildProxy", "MapCityProxy", "MapProxy", "MapUtil", "EventMgr", "LogicEvent", "getInstance", "_instance", "destory", "onDestory", "constructor", "on", "role_myProperty", "onRoleMyProperty", "roleBuild_push", "onRoleBuildStatePush", "nationMap_config", "onNationMapConfig", "nationMap_scanBlock", "onNationMapScanBlock", "nationMap_giveUp", "onNationMapGiveUp", "nationMap_build", "onNationMapBuild", "nationMap_upBuild", "onNationMapUpBuild", "roleCity_push", "onRoleCityPush", "role_posTagList", "onPosTagList", "role_opPosTag", "onOpPosTag", "getMyMainCity", "onGetMyMainCity", "targetOff", "initData", "_proxy", "_cityProxy", "_buildProxy", "clearData", "_isQryMyProperty", "proxy", "cityProxy", "buildProxy", "data", "console", "log", "code", "MapUICommand", "updateMyProperty", "msg", "generals", "ArmyCommand", "armys", "initMyCitys", "citys", "initMyBuilds", "mr_builds", "myId", "getMyPlayerId", "myUnionId", "unionId", "myParentId", "parentId", "posTagList", "enterMap", "updateBuild", "setNationMapConfig", "Confs", "otherData", "setMapScanBlock", "id", "updateMapPosTags", "pos_tags", "type", "removeMapPosTag", "x", "y", "emit", "updateTag", "addMapPosTag", "name", "updateSub", "rid", "union_id", "parent_id", "updateCity", "unionChange", "isBuildSub", "buiildData", "getBuild", "isBuildWarFree", "isWarFree", "isCitySub", "cityData", "getCity", "isCityWarFree", "diff", "getServerTime", "occupyTime", "getWarFree", "isCanMoveCell", "getIdByCellPoint", "isCanOccupyCell", "radius", "getCellRadius", "buildData", "tx", "ty", "absX", "Math", "abs", "absY", "ok", "hasResConfig", "qryNationMapConfig", "qryRoleMyProperty", "sendData", "send", "qryRoleMyCity", "role_myCity", "qryNationMapScanBlock", "qryData", "startCellX", "startCellY", "length", "len", "giveUpBuild", "build", "upBuild", "delBuild", "nationMap_delBuild", "upPosition", "role_upPosition", "opPosTag", "callback", "myCity"], "mappings": ";;;wJAgBqBA,U;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAfZC,MAAAA,Y,iBAAAA,Y;;AAGFC,MAAAA,c;;AACEC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,Q;;AACAC,MAAAA,a;;AACAC,MAAAA,Y;;AACAC,MAAAA,Q;;AACAC,MAAAA,O;;AAGEC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;yBAEYV,U,GAAN,MAAMA,UAAN,CAAiB;AAC5B;AAEyB,eAAXW,WAAW,GAAe;AACpC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIZ,UAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKY,SAAZ;AACH;;AAEoB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SAjB2B,CAmB5B;;;AAMAG,QAAAA,WAAW,GAAG;AAAA,0CALe;AAAA;AAAA,qCAKf;;AAAA,8CAJuB;AAAA;AAAA,6CAIvB;;AAAA,+CAHyB;AAAA;AAAA,+CAGzB;;AAAA,oDAFwB,KAExB;;AACV;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,eAAzB,EAA0C,KAAKC,gBAA/C,EAAiE,IAAjE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,cAAzB,EAAyC,KAAKC,oBAA9C,EAAoE,IAApE;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,4CAAaK,gBAAzB,EAA2C,KAAKC,iBAAhD,EAAmE,IAAnE;AACA;AAAA;AAAA,oCAASN,EAAT,CAAY;AAAA;AAAA,4CAAaO,mBAAzB,EAA8C,KAAKC,oBAAnD,EAAyE,IAAzE;AACA;AAAA;AAAA,oCAASR,EAAT,CAAY;AAAA;AAAA,4CAAaS,gBAAzB,EAA2C,KAAKC,iBAAhD,EAAmE,IAAnE;AACA;AAAA;AAAA,oCAASV,EAAT,CAAY;AAAA;AAAA,4CAAaW,eAAzB,EAA0C,KAAKC,gBAA/C,EAAiE,IAAjE;AACA;AAAA;AAAA,oCAASZ,EAAT,CAAY;AAAA;AAAA,4CAAaa,iBAAzB,EAA4C,KAAKC,kBAAjD,EAAqE,IAArE;AACA;AAAA;AAAA,oCAASd,EAAT,CAAY;AAAA;AAAA,4CAAae,aAAzB,EAAwC,KAAKC,cAA7C,EAA6D,IAA7D;AACA;AAAA;AAAA,oCAAShB,EAAT,CAAY;AAAA;AAAA,4CAAaiB,eAAzB,EAA0C,KAAKC,YAA/C,EAA6D,IAA7D;AACA;AAAA;AAAA,oCAASlB,EAAT,CAAY;AAAA;AAAA,4CAAamB,aAAzB,EAAwC,KAAKC,UAA7C,EAAyD,IAAzD,EAVU,CAYV;;AACA;AAAA;AAAA,oCAASpB,EAAT,CAAY;AAAA;AAAA,wCAAWqB,aAAvB,EAAsC,KAAKC,eAA3C,EAA4D,IAA5D;AACH;;AAEMxB,QAAAA,SAAS,GAAS;AACrB;AAAA;AAAA,oCAASyB,SAAT,CAAmB,IAAnB;AACH;;AAEMC,QAAAA,QAAQ,GAAS;AACpB,eAAKC,MAAL,CAAYD,QAAZ;;AACA,eAAKE,UAAL,CAAgBF,QAAhB;;AACA,eAAKG,WAAL,CAAiBH,QAAjB;AACH;;AAEMI,QAAAA,SAAS,GAAS;AACrB,eAAKH,MAAL,CAAYG,SAAZ;;AACA,eAAKF,UAAL,CAAgBE,SAAhB;;AACA,eAAKD,WAAL,CAAiBC,SAAjB;;AACA,eAAKC,gBAAL,GAAwB,KAAxB;AACH;;AAEe,YAALC,KAAK,GAAa;AACzB,iBAAO,KAAKL,MAAZ;AACH;;AAEmB,YAATM,SAAS,GAAiB;AACjC,iBAAO,KAAKL,UAAZ;AACH;;AAEoB,YAAVM,UAAU,GAAkB;AACnC,iBAAO,KAAKL,WAAZ;AACH;;AAESzB,QAAAA,gBAAgB,CAAC+B,IAAD,EAAkB;AACxCC,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCF,IAAhC;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKP,gBAAL,GAAwB,IAAxB;AACAQ,YAAAA,YAAY,CAAC1C,WAAb,GAA2B2C,gBAA3B,CAA4CL,IAA5C;AACA;AAAA;AAAA,kDAAetC,WAAf,GAA6B2C,gBAA7B,CAA8CL,IAAI,CAACM,GAAL,CAASC,QAAvD;AACAC,YAAAA,WAAW,CAAC9C,WAAZ,GAA0B2C,gBAA1B,CAA2CL,IAAI,CAACM,GAAL,CAASG,KAApD;;AACA,iBAAKhB,UAAL,CAAgBiB,WAAhB,CAA4BV,IAAI,CAACM,GAAL,CAASK,KAArC;;AACA,iBAAKjB,WAAL,CAAiBkB,YAAjB,CAA8BZ,IAAI,CAACM,GAAL,CAASO,SAAvC;;AACA,iBAAKpB,UAAL,CAAgBqB,IAAhB,GAAuB,KAAKrB,UAAL,CAAgBsB,aAAhB,EAAvB;AACA,iBAAKrB,WAAL,CAAiBoB,IAAjB,GAAwB,KAAKrB,UAAL,CAAgBsB,aAAhB,EAAxB;AACA,iBAAKtB,UAAL,CAAgBuB,SAAhB,GAA4B,KAAKvB,UAAL,CAAgBL,aAAhB,GAAgC6B,OAA5D;AACA,iBAAKxB,UAAL,CAAgByB,UAAhB,GAA6B,KAAKzB,UAAL,CAAgBL,aAAhB,GAAgC+B,QAA7D;AACA,iBAAKzB,WAAL,CAAiBsB,SAAjB,GAA6B,KAAKvB,UAAL,CAAgBL,aAAhB,GAAgC6B,OAA7D;AACA,iBAAKvB,WAAL,CAAiBwB,UAAjB,GAA8B,KAAKzB,UAAL,CAAgBL,aAAhB,GAAgC+B,QAA9D;AACApE,YAAAA,UAAU,CAACW,WAAX,GAAyB0D,UAAzB;AAEA,iBAAKC,QAAL;AACH;AACJ;;AAESlD,QAAAA,oBAAoB,CAAC6B,IAAD,EAAkB;AAC5CC,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoCF,IAApC;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKT,WAAL,CAAiB4B,WAAjB,CAA6BtB,IAAI,CAACM,GAAlC;AACH;AACJ;;AAESjC,QAAAA,iBAAiB,CAAC2B,IAAD,EAAkB;AACzCC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCF,IAAjC;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKX,MAAL,CAAY+B,kBAAZ,CAA+BvB,IAAI,CAACM,GAAL,CAASkB,KAAxC;;AACA,iBAAKH,QAAL;AACH;AACJ;;AAES9C,QAAAA,oBAAoB,CAACyB,IAAD,EAAYyB,SAAZ,EAAkC;AAC5DxB,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BF,IAA/B,EAAqCyB,SAArC;;AACA,cAAIzB,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKV,UAAL,CAAgBiC,eAAhB,CAAgC1B,IAAI,CAACM,GAArC,EAA0CmB,SAAS,CAACE,EAApD;;AACA,iBAAKjC,WAAL,CAAiBgC,eAAjB,CAAiC1B,IAAI,CAACM,GAAtC,EAA2CmB,SAAS,CAACE,EAArD;AACH;AACJ;;AAESlD,QAAAA,iBAAiB,CAACuB,IAAD,EAAYyB,SAAZ,EAAkC;AACzDxB,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCF,IAAjC,EAAuCyB,SAAvC;AACH;;AAES9C,QAAAA,gBAAgB,CAACqB,IAAD,EAAYyB,SAAZ,EAAkC;AACxDxB,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCF,IAAhC,EAAsCyB,SAAtC;AACH;;AAES5C,QAAAA,kBAAkB,CAACmB,IAAD,EAAYyB,SAAZ,EAAkC;AAC1DxB,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCF,IAAlC,EAAwCyB,SAAxC;AACH;;AAESxC,QAAAA,YAAY,CAACe,IAAD,EAAYyB,SAAZ,EAAkC;AACpDxB,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BF,IAA5B,EAAkCyB,SAAlC;;AACA,cAAGzB,IAAI,CAACG,IAAL,IAAa,CAAhB,EAAkB;AACd,iBAAKX,MAAL,CAAYoC,gBAAZ,CAA6B5B,IAAI,CAACM,GAAL,CAASuB,QAAtC;AACH;AACJ;;AAES1C,QAAAA,UAAU,CAACa,IAAD,EAAYyB,SAAZ,EAAkC;AAClDxB,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BF,IAA1B,EAAgCyB,SAAhC;;AACA,cAAGzB,IAAI,CAACG,IAAL,IAAa,CAAhB,EAAkB;AACd,gBAAGH,IAAI,CAACM,GAAL,CAASwB,IAAT,IAAiB,CAApB,EAAsB;AAClB,mBAAKtC,MAAL,CAAYuC,eAAZ,CAA4B/B,IAAI,CAACM,GAAL,CAAS0B,CAArC,EAAwChC,IAAI,CAACM,GAAL,CAAS2B,CAAjD;;AACA;AAAA;AAAA,wCAASC,IAAT,CAAc;AAAA;AAAA,4CAAWC,SAAzB;AACH,aAHD,MAGM,IAAGnC,IAAI,CAACM,GAAL,CAASwB,IAAT,IAAiB,CAApB,EAAsB;AACxB,mBAAKtC,MAAL,CAAY4C,YAAZ,CAAyBpC,IAAI,CAACM,GAAL,CAAS0B,CAAlC,EAAqChC,IAAI,CAACM,GAAL,CAAS2B,CAA9C,EAAiDjC,IAAI,CAACM,GAAL,CAAS+B,IAA1D;;AACA;AAAA;AAAA,wCAASH,IAAT,CAAc;AAAA;AAAA,4CAAWC,SAAzB;AACH;AACJ;AACJ;;AAGSpD,QAAAA,cAAc,CAACiB,IAAD,EAAkB;AACtCC,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BF,IAA/B;;AACA,eAAKN,WAAL,CAAiB4C,SAAjB,CAA2BtC,IAAI,CAACM,GAAL,CAASiC,GAApC,EAAyCvC,IAAI,CAACM,GAAL,CAASkC,QAAlD,EAA4DxC,IAAI,CAACM,GAAL,CAASmC,SAArE;;AACA,eAAKhD,UAAL,CAAgBiD,UAAhB,CAA2B1C,IAAI,CAACM,GAAhC;;AACA;AAAA;AAAA,oCAAS4B,IAAT,CAAc;AAAA;AAAA,wCAAWS,WAAzB,EAAsC3C,IAAI,CAACM,GAAL,CAASiC,GAA/C,EAAoDvC,IAAI,CAACM,GAAL,CAASkC,QAA7D,EAAuExC,IAAI,CAACM,GAAL,CAASmC,SAAhF;AAEH;;AAEMG,QAAAA,UAAU,CAACjB,EAAD,EAAsB;AACnC,cAAIkB,UAAwB,GAAG,KAAK9C,UAAL,CAAgB+C,QAAhB,CAAyBnB,EAAzB,CAA/B;;AACA,cAAIkB,UAAJ,EAAgB;AACZ,gBAAIA,UAAU,CAACN,GAAX,IAAkB,KAAKxC,UAAL,CAAgBe,IAAtC,EAA2C;AACvC,qBAAO,IAAP;AACH;;AAED,gBAAI+B,UAAU,CAAC5B,OAAX,GAAqB,CAArB,IAA0B4B,UAAU,CAAC5B,OAAX,IAAsB,KAAKlB,UAAL,CAAgBiB,SAApE,EAA8E;AAC1E,qBAAO,IAAP;AACH;;AAED,gBAAI6B,UAAU,CAAC1B,QAAX,GAAsB,CAAtB,IAA2B0B,UAAU,CAAC1B,QAAX,IAAuB,KAAKpB,UAAL,CAAgBiB,SAAtE,EAAgF;AAC5E,qBAAO,IAAP;AACH;AACJ;;AACD,iBAAO,KAAP;AACH;;AAEM+B,QAAAA,cAAc,CAACpB,EAAD,EAAsB;AACvC,cAAIkB,UAAwB,GAAG,KAAK9C,UAAL,CAAgB+C,QAAhB,CAAyBnB,EAAzB,CAA/B;;AACA,cAAGkB,UAAH,EAAc;AACV,mBAAOA,UAAU,CAACG,SAAX,EAAP;AACH,WAFD,MAEK;AACD,mBAAO,KAAP;AACH;AACJ;;AAGMC,QAAAA,SAAS,CAACtB,EAAD,EAAsB;AAClC,cAAIuB,QAAqB,GAAG,KAAKpD,SAAL,CAAeqD,OAAf,CAAuBxB,EAAvB,CAA5B;;AACA,cAAIuB,QAAJ,EAAc;AACV,gBAAIA,QAAQ,CAACX,GAAT,IAAgB,KAAKzC,SAAL,CAAegB,IAAnC,EAAwC;AACpC,qBAAO,IAAP;AACH;;AAED,gBAAIoC,QAAQ,CAACjC,OAAT,GAAmB,CAAnB,IAAwBiC,QAAQ,CAACjC,OAAT,IAAoB,KAAKnB,SAAL,CAAekB,SAA/D,EAAyE;AACrE,qBAAO,IAAP;AACH;;AAED,gBAAIkC,QAAQ,CAAC/B,QAAT,GAAoB,CAApB,IAAyB+B,QAAQ,CAAC/B,QAAT,IAAqB,KAAKrB,SAAL,CAAekB,SAAjE,EAA2E;AACvE,qBAAO,IAAP;AACH;AACJ;;AACD,iBAAO,KAAP;AACH;;AAEMoC,QAAAA,aAAa,CAACzB,EAAD,EAAsB;AACtC,cAAIuB,QAAqB,GAAG,KAAKpD,SAAL,CAAeqD,OAAf,CAAuBxB,EAAvB,CAA5B;;AACA,cAAIuB,QAAQ,IAAIA,QAAQ,CAAC/B,QAAT,GAAoB,CAApC,EAAuC;AACnC,gBAAIkC,IAAI,GAAG;AAAA;AAAA,sCAASC,aAAT,KAA2BJ,QAAQ,CAACK,UAA/C;;AACA,gBAAGF,IAAI,GAAGtG,UAAU,CAACW,WAAX,GAAyBmC,KAAzB,CAA+B2D,UAA/B,EAAV,EAAsD;AAClD,qBAAO,IAAP;AACH;AACJ;;AACD,iBAAO,KAAP;AACH;AAGD;;;AACOC,QAAAA,aAAa,CAACzB,CAAD,EAAYC,CAAZ,EAAgC;AAChD,cAAIN,EAAU,GAAG;AAAA;AAAA,kCAAQ+B,gBAAR,CAAyB1B,CAAzB,EAA4BC,CAA5B,CAAjB;;AACA,cAAI,KAAKW,UAAL,CAAgBjB,EAAhB,CAAJ,EAAwB;AACpB,mBAAO,IAAP;AACH;;AAED,cAAI,KAAKsB,SAAL,CAAetB,EAAf,CAAJ,EAAuB;AACnB,mBAAO,IAAP;AACH;;AAED,iBAAO,KAAP;AACH;;AAEMgC,QAAAA,eAAe,CAAC3B,CAAD,EAAYC,CAAZ,EAAgC;AAClD,cAAI2B,MAAM,GAAG,CAAb;AACA,cAAIjC,EAAU,GAAG;AAAA;AAAA,kCAAQ+B,gBAAR,CAAyB1B,CAAzB,EAA4BC,CAA5B,CAAjB;AACA,cAAIiB,QAAqB,GAAG,KAAKpD,SAAL,CAAeqD,OAAf,CAAuBxB,EAAvB,CAA5B;;AACA,cAAIuB,QAAJ,EAAc;AACV,gBAAG,KAAKE,aAAL,CAAmBzB,EAAnB,CAAH,EAA0B;AACtB,qBAAO,KAAP;AACH;;AACDiC,YAAAA,MAAM,GAAGV,QAAQ,CAACW,aAAT,EAAT;AACH;;AAED,cAAIC,SAAuB,GAAG,KAAK/D,UAAL,CAAgB+C,QAAhB,CAAyBnB,EAAzB,CAA9B;;AACA,cAAImC,SAAJ,EAAe;AACX,gBAAG,KAAKf,cAAL,CAAoBpB,EAApB,CAAH,EAA2B;AACvB,qBAAO,KAAP;AACH,aAHU,CAKX;;;AACAiC,YAAAA,MAAM,GAAGE,SAAS,CAACD,aAAV,EAAT;AACH,WAnBiD,CAqBlD;;;AACA,eAAK,IAAIE,EAAE,GAAG/B,CAAC,GAAC,EAAhB,EAAoB+B,EAAE,IAAI/B,CAAC,GAAC,EAA5B,EAAgC+B,EAAE,EAAlC,EAAsC;AAClC,iBAAK,IAAIC,EAAE,GAAG/B,CAAC,GAAC,EAAhB,EAAoB+B,EAAE,IAAI/B,CAAC,GAAC,EAA5B,EAAgC+B,EAAE,EAAlC,EAAsC;AAElC,kBAAIrC,EAAU,GAAG;AAAA;AAAA,sCAAQ+B,gBAAR,CAAyBK,EAAzB,EAA6BC,EAA7B,CAAjB;AACA,kBAAId,QAAqB,GAAG,KAAKpD,SAAL,CAAeqD,OAAf,CAAuBxB,EAAvB,CAA5B;;AACA,kBAAIuB,QAAJ,EAAc;AACV,oBAAIe,IAAI,GAAGC,IAAI,CAACC,GAAL,CAASnC,CAAC,GAAC+B,EAAX,CAAX;AACA,oBAAIK,IAAI,GAAGF,IAAI,CAACC,GAAL,CAASlC,CAAC,GAAC+B,EAAX,CAAX;;AACA,oBAAIC,IAAI,IAAIL,MAAM,GAACV,QAAQ,CAACW,aAAT,EAAP,GAAgC,CAAxC,IAA6CO,IAAI,IAAIR,MAAM,GAACV,QAAQ,CAACW,aAAT,EAAP,GAAgC,CAAzF,EAA2F;AACvF,sBAAIQ,EAAE,GAAG,KAAKpB,SAAL,CAAetB,EAAf,CAAT;;AACA,sBAAG0C,EAAH,EAAM;AACF,2BAAO,IAAP;AACH;AACJ;AACJ;;AAED,kBAAIP,SAAuB,GAAG,KAAK/D,UAAL,CAAgB+C,QAAhB,CAAyBnB,EAAzB,CAA9B;;AACA,kBAAImC,SAAJ,EAAe;AACX,oBAAIG,IAAI,GAAGC,IAAI,CAACC,GAAL,CAASnC,CAAC,GAAC+B,EAAX,CAAX;AACA,oBAAIK,IAAI,GAAGF,IAAI,CAACC,GAAL,CAASlC,CAAC,GAAC+B,EAAX,CAAX,CAFW,CAGX;;AACA,oBAAIC,IAAI,IAAIL,MAAM,GAACE,SAAS,CAACD,aAAV,EAAP,GAAiC,CAAzC,IAA8CO,IAAI,IAAIR,MAAM,GAACE,SAAS,CAACD,aAAV,EAAP,GAAiC,CAA3F,EAA6F;AACzF,sBAAIQ,EAAE,GAAG,KAAKzB,UAAL,CAAgBjB,EAAhB,CAAT;;AACA,sBAAG0C,EAAH,EAAM;AACF,2BAAO,IAAP;AACH;AACJ;AACJ;AACJ;AACJ;;AACD,iBAAO,KAAP;AACH;;AAEMhD,QAAAA,QAAQ,GAAS;AACpB,cAAI,KAAK7B,MAAL,CAAY8E,YAAZ,MAA8B,KAAlC,EAAyC;AACrC,iBAAKC,kBAAL;AACA;AACH;;AACD,cAAI,KAAK3E,gBAAL,IAAyB,KAA7B,EAAoC;AAChC,iBAAK4E,iBAAL;AACA;AACH;;AACD;AAAA;AAAA,oCAAStC,IAAT,CAAc;AAAA;AAAA,wCAAWb,QAAzB;AACH;AAED;;;AACOmD,QAAAA,iBAAiB,GAAS;AAC7B,cAAIC,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAarE,eADH;AAEhBsC,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAW5C,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;AAED;;;AACOE,QAAAA,aAAa,GAAS;AACzB,cAAIF,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAauC,WADH;AAEhBtE,YAAAA,GAAG,EAAE;AAFW,WAApB;AAIA;AAAA;AAAA,wCAAW5C,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;AAED;;;AACOF,QAAAA,kBAAkB,GAAS;AAC9B,cAAIE,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAajE,gBADH;AAEhBkC,YAAAA,GAAG,EAAE;AAFW,WAApB;AAIA;AAAA;AAAA,wCAAW5C,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;;AAEMI,QAAAA,qBAAqB,CAACC,OAAD,EAA6B;AACrD,cAAIL,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa/D,mBADH;AAEhBgC,YAAAA,GAAG,EAAE;AACD0B,cAAAA,CAAC,EAAE8C,OAAO,CAACC,UADV;AAED9C,cAAAA,CAAC,EAAE6C,OAAO,CAACE,UAFV;AAGDC,cAAAA,MAAM,EAAEH,OAAO,CAACI;AAHf;AAFW,WAApB;AAQA;AAAA;AAAA,wCAAWxH,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B,EAAwCK,OAAxC;AACH;;AAEMK,QAAAA,WAAW,CAACnD,CAAD,EAAYC,CAAZ,EAA6B;AAC3C,cAAIwC,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa7D,gBADH;AAEhB8B,YAAAA,GAAG,EAAE;AACD0B,cAAAA,CAAC,EAAEA,CADF;AAEDC,cAAAA,CAAC,EAAEA;AAFF;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWvE,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;;AAEMW,QAAAA,KAAK,CAACpD,CAAD,EAAYC,CAAZ,EAAuBH,IAAvB,EAA2C;AACnD,cAAI2C,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa3D,eADH;AAEhB4B,YAAAA,GAAG,EAAE;AACD0B,cAAAA,CAAC,EAAEA,CADF;AAEDC,cAAAA,CAAC,EAAEA,CAFF;AAGDH,cAAAA,IAAI,EAAEA;AAHL;AAFW,WAApB;AAQA;AAAA;AAAA,wCAAWpE,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;;AAEMY,QAAAA,OAAO,CAACrD,CAAD,EAAYC,CAAZ,EAA6B;AACvC,cAAIwC,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAazD,iBADH;AAEhB0B,YAAAA,GAAG,EAAE;AACD0B,cAAAA,CAAC,EAAEA,CADF;AAEDC,cAAAA,CAAC,EAAEA;AAFF;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWvE,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;;AAEMa,QAAAA,QAAQ,CAACtD,CAAD,EAAYC,CAAZ,EAA6B;AACxC,cAAIwC,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAakD,kBADH;AAEhBjF,YAAAA,GAAG,EAAE;AACD0B,cAAAA,CAAC,EAAEA,CADF;AAEDC,cAAAA,CAAC,EAAEA;AAFF;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWvE,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;;AAGMe,QAAAA,UAAU,CAACxD,CAAD,EAAYC,CAAZ,EAA6B;AAC1C,cAAIwC,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAaoD,eADH;AAEhBnF,YAAAA,GAAG,EAAE;AACD0B,cAAAA,CAAC,EAAEA,CADF;AAEDC,cAAAA,CAAC,EAAEA;AAFF;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWvE,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;;AAEMrD,QAAAA,UAAU,GAAS;AACtB,cAAIqD,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAarD,eADH;AAEhBsB,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAW5C,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH,SA9Y2B,CAgZ5B;;;AACOiB,QAAAA,QAAQ,CAAC5D,IAAD,EAAcE,CAAd,EAAyBC,CAAzB,EAAoCI,IAAI,GAAG,EAA3C,EAAqD;AAChE,cAAIoC,QAAa,GAAG;AAChBpC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAanD,aADH;AAEhBoB,YAAAA,GAAG,EAAE;AACDwB,cAAAA,IAAI,EAACA,IADJ;AAEDE,cAAAA,CAAC,EAACA,CAFD;AAGDC,cAAAA,CAAC,EAACA,CAHD;AAIDI,cAAAA,IAAI,EAACA;AAJJ;AAFW,WAApB;AASA;AAAA;AAAA,wCAAW3E,WAAX,GAAyBgH,IAAzB,CAA8BD,QAA9B;AACH;AAED;AACJ;AACA;;;AACYpF,QAAAA,eAAe,CAACsG,QAAD,EAA8C;AACjE,gBAAMC,MAAM,GAAG,KAAKnG,UAAL,CAAgBL,aAAhB,EAAf;;AACA,cAAIuG,QAAQ,IAAI,OAAOA,QAAP,KAAoB,UAApC,EAAgD;AAC5CA,YAAAA,QAAQ,CAACC,MAAD,CAAR;AACH;AACJ;;AAta2B,O;;sBAAX7I,U", "sourcesContent": ["import { _decorator } from 'cc';\nimport { ServerConfig } from \"../config/ServerConfig\";\n// 移除循环依赖：不直接导入ArmyCommand\n// import ArmyCommand from \"../general/ArmyCommand\";\nimport GeneralCommand from \"../general/GeneralCommand\";\nimport { NetManager } from \"../network/socket/NetManager\";\nimport DateUtil from \"../utils/DateUtil\";\nimport MapBuildProxy, { MapBuildData } from \"./MapBuildProxy\";\nimport MapCityProxy, { MapCityData } from \"./MapCityProxy\";\nimport MapProxy, { MapAreaData } from \"./MapProxy\";\nimport MapUtil from \"./MapUtil\";\n// 移除循环依赖：不直接导入MapUICommand\n// import MapUICommand from \"./ui/MapUICommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\nexport default class MapCommand {\n    //单例\n    protected static _instance: MapCommand;\n    public static getInstance(): MapCommand {\n        if (this._instance == null) {\n            this._instance = new MapCommand();\n        }\n        return this._instance;\n    }\n\n    public static destory(): boolean {\n        if (this._instance) {\n            this._instance.onDestory();\n            this._instance = null;\n            return true;\n        }\n        return false;\n    }\n\n    //数据model\n    protected _proxy: MapProxy = new MapProxy();\n    protected _cityProxy: MapCityProxy = new MapCityProxy();\n    protected _buildProxy: MapBuildProxy = new MapBuildProxy();\n    protected _isQryMyProperty: boolean = false;\n\n    constructor() {\n        EventMgr.on(ServerConfig.role_myProperty, this.onRoleMyProperty, this);\n        EventMgr.on(ServerConfig.roleBuild_push, this.onRoleBuildStatePush, this);\n        EventMgr.on(ServerConfig.nationMap_config, this.onNationMapConfig, this);\n        EventMgr.on(ServerConfig.nationMap_scanBlock, this.onNationMapScanBlock, this);\n        EventMgr.on(ServerConfig.nationMap_giveUp, this.onNationMapGiveUp, this);\n        EventMgr.on(ServerConfig.nationMap_build, this.onNationMapBuild, this);\n        EventMgr.on(ServerConfig.nationMap_upBuild, this.onNationMapUpBuild, this);\n        EventMgr.on(ServerConfig.roleCity_push, this.onRoleCityPush, this);\n        EventMgr.on(ServerConfig.role_posTagList, this.onPosTagList, this);\n        EventMgr.on(ServerConfig.role_opPosTag, this.onOpPosTag, this);\n\n        // 添加事件监听，用于解决循环依赖\n        EventMgr.on(LogicEvent.getMyMainCity, this.onGetMyMainCity, this);\n    }\n\n    public onDestory(): void {\n        EventMgr.targetOff(this);\n    }\n\n    public initData(): void {\n        this._proxy.initData();\n        this._cityProxy.initData();\n        this._buildProxy.initData();\n    }\n\n    public clearData(): void {\n        this._proxy.clearData();\n        this._cityProxy.clearData();\n        this._buildProxy.clearData();\n        this._isQryMyProperty = false;\n    }\n\n    public get proxy(): MapProxy {\n        return this._proxy;\n    }\n\n    public get cityProxy(): MapCityProxy {\n        return this._cityProxy;\n    }\n\n    public get buildProxy(): MapBuildProxy {\n        return this._buildProxy;\n    }\n\n    protected onRoleMyProperty(data: any): void {\n        console.log(\"onRoleMyProperty\", data);\n        if (data.code == 0) {\n            this._isQryMyProperty = true;\n            MapUICommand.getInstance().updateMyProperty(data);\n            GeneralCommand.getInstance().updateMyProperty(data.msg.generals);\n            ArmyCommand.getInstance().updateMyProperty(data.msg.armys);\n            this._cityProxy.initMyCitys(data.msg.citys);\n            this._buildProxy.initMyBuilds(data.msg.mr_builds);\n            this._cityProxy.myId = this._cityProxy.getMyPlayerId();\n            this._buildProxy.myId = this._cityProxy.getMyPlayerId();\n            this._cityProxy.myUnionId = this._cityProxy.getMyMainCity().unionId;\n            this._cityProxy.myParentId = this._cityProxy.getMyMainCity().parentId;\n            this._buildProxy.myUnionId = this._cityProxy.getMyMainCity().unionId;\n            this._buildProxy.myParentId = this._cityProxy.getMyMainCity().parentId;\n            MapCommand.getInstance().posTagList();\n            \n            this.enterMap();\n        }\n    }\n\n    protected onRoleBuildStatePush(data: any): void {\n        console.log(\"onRoleBuildStatePush\", data);\n        if (data.code == 0) {\n            this._buildProxy.updateBuild(data.msg);\n        }\n    }\n\n    protected onNationMapConfig(data: any): void {\n        console.log(\"onNationMapConfig\", data);\n        if (data.code == 0) {\n            this._proxy.setNationMapConfig(data.msg.Confs);\n            this.enterMap();\n        }\n    }\n\n    protected onNationMapScanBlock(data: any, otherData: any): void {\n        console.log(\"onNationMapScan\", data, otherData);\n        if (data.code == 0) {\n            this._cityProxy.setMapScanBlock(data.msg, otherData.id);\n            this._buildProxy.setMapScanBlock(data.msg, otherData.id);\n        }\n    }\n\n    protected onNationMapGiveUp(data: any, otherData: any): void {\n        console.log(\"onNationMapGiveUp\", data, otherData);\n    }\n\n    protected onNationMapBuild(data: any, otherData: any): void {\n        console.log(\"onNationMapBuild\", data, otherData);\n    }\n\n    protected onNationMapUpBuild(data: any, otherData: any): void {\n        console.log(\"onNationMapUpBuild\", data, otherData);\n    }\n\n    protected onPosTagList(data: any, otherData: any): void {\n        console.log(\"onPosTagList\", data, otherData);\n        if(data.code == 0){\n            this._proxy.updateMapPosTags(data.msg.pos_tags);\n        }\n    }\n\n    protected onOpPosTag(data: any, otherData: any): void {\n        console.log(\"onOpPosTag\", data, otherData);\n        if(data.code == 0){\n            if(data.msg.type == 0){\n                this._proxy.removeMapPosTag(data.msg.x, data.msg.y);\n                EventMgr.emit(LogicEvent.updateTag);\n            }else if(data.msg.type == 1){\n                this._proxy.addMapPosTag(data.msg.x, data.msg.y, data.msg.name);\n                EventMgr.emit(LogicEvent.updateTag);\n            }\n        }\n    }\n    \n\n    protected onRoleCityPush(data: any): void {\n        console.log(\"onRoleCityPush:\", data)\n        this._buildProxy.updateSub(data.msg.rid, data.msg.union_id, data.msg.parent_id);\n        this._cityProxy.updateCity(data.msg);\n        EventMgr.emit(LogicEvent.unionChange, data.msg.rid, data.msg.union_id, data.msg.parent_id);\n       \n    }\n\n    public isBuildSub(id: number): boolean {\n        let buiildData: MapBuildData = this.buildProxy.getBuild(id);\n        if (buiildData) {\n            if (buiildData.rid == this.buildProxy.myId){\n                return true;\n            }\n\n            if (buiildData.unionId > 0 && buiildData.unionId == this.buildProxy.myUnionId){\n                return true\n            }\n\n            if (buiildData.parentId > 0 && buiildData.parentId == this.buildProxy.myUnionId){\n                return true\n            }\n        }\n        return false\n    }\n\n    public isBuildWarFree(id: number): boolean {\n        let buiildData: MapBuildData = this.buildProxy.getBuild(id);\n        if(buiildData){\n            return buiildData.isWarFree();\n        }else{\n            return false;\n        }\n    }\n\n    \n    public isCitySub(id: number): boolean {\n        let cityData: MapCityData = this.cityProxy.getCity(id);\n        if (cityData) {\n            if (cityData.rid == this.cityProxy.myId){\n                return true\n            }\n\n            if (cityData.unionId > 0 && cityData.unionId == this.cityProxy.myUnionId){\n                return true\n            }\n\n            if (cityData.parentId > 0 && cityData.parentId == this.cityProxy.myUnionId){\n                return true\n            }\n        }\n        return false\n    }\n\n    public isCityWarFree(id: number): boolean {\n        let cityData: MapCityData = this.cityProxy.getCity(id);\n        if (cityData && cityData.parentId > 0) {\n            var diff = DateUtil.getServerTime() - cityData.occupyTime;\n            if(diff < MapCommand.getInstance().proxy.getWarFree()){\n                return true;\n            }\n        }\n        return false\n    }\n\n\n    /**是否是可行军的位置*/\n    public isCanMoveCell(x: number, y: number): boolean {\n        let id: number = MapUtil.getIdByCellPoint(x, y);\n        if (this.isBuildSub(id)){\n            return true\n        }\n\n        if (this.isCitySub(id)){\n            return true\n        }\n\n        return false\n    }\n\n    public isCanOccupyCell(x: number, y: number): boolean {\n        var radius = 0;\n        let id: number = MapUtil.getIdByCellPoint(x, y);\n        let cityData: MapCityData = this.cityProxy.getCity(id);\n        if (cityData) {\n            if(this.isCityWarFree(id)){\n                return false;\n            }\n            radius = cityData.getCellRadius();\n        }\n\n        let buildData: MapBuildData = this.buildProxy.getBuild(id);\n        if (buildData) {\n            if(this.isBuildWarFree(id)){\n                return false;\n            }\n\n            // console.log(\"buildData 11111:\", buildData);\n            radius = buildData.getCellRadius();\n        }\n\n        //查找半径10\n        for (let tx = x-10; tx <= x+10; tx++) {\n            for (let ty = y-10; ty <= y+10; ty++) {\n\n                let id: number = MapUtil.getIdByCellPoint(tx, ty);\n                let cityData: MapCityData = this.cityProxy.getCity(id);\n                if (cityData) {\n                    var absX = Math.abs(x-tx);\n                    var absY = Math.abs(y-ty);\n                    if (absX <= radius+cityData.getCellRadius()+1 && absY <= radius+cityData.getCellRadius()+1){\n                        var ok = this.isCitySub(id)\n                        if(ok){\n                            return true;\n                        }\n                    }\n                }\n        \n                let buildData: MapBuildData = this.buildProxy.getBuild(id);\n                if (buildData) {\n                    var absX = Math.abs(x-tx);\n                    var absY = Math.abs(y-ty);\n                    // console.log(\"MapBuildData:\", absX, absY, radius+buildData.getCellRadius()+1, buildData);\n                    if (absX <= radius+buildData.getCellRadius()+1 && absY <= radius+buildData.getCellRadius()+1){\n                        var ok = this.isBuildSub(id)\n                        if(ok){\n                            return true;\n                        }\n                    }\n                }\n            }\n        }\n        return false;\n    }\n\n    public enterMap(): void {\n        if (this._proxy.hasResConfig() == false) {\n            this.qryNationMapConfig();\n            return;\n        }\n        if (this._isQryMyProperty == false) {\n            this.qryRoleMyProperty();\n            return;\n        }\n        EventMgr.emit(LogicEvent.enterMap);\n    }\n\n    /**请求角色全量信息*/\n    public qryRoleMyProperty(): void {\n        let sendData: any = {\n            name: ServerConfig.role_myProperty,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    /**请求自己的城池信息*/\n    public qryRoleMyCity(): void {\n        let sendData: any = {\n            name: ServerConfig.role_myCity,\n            msg: {}\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    /**请求地图基础配置*/\n    public qryNationMapConfig(): void {\n        let sendData: any = {\n            name: ServerConfig.nationMap_config,\n            msg: {}\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public qryNationMapScanBlock(qryData: MapAreaData): void {\n        let sendData: any = {\n            name: ServerConfig.nationMap_scanBlock,\n            msg: {\n                x: qryData.startCellX,\n                y: qryData.startCellY,\n                length: qryData.len\n            }\n        };\n        NetManager.getInstance().send(sendData, qryData);\n    }\n\n    public giveUpBuild(x: number, y: number): void {\n        let sendData: any = {\n            name: ServerConfig.nationMap_giveUp,\n            msg: {\n                x: x,\n                y: y\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public build(x: number, y: number, type: number): void {\n        let sendData: any = {\n            name: ServerConfig.nationMap_build,\n            msg: {\n                x: x,\n                y: y,\n                type: type,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public upBuild(x: number, y: number): void {\n        let sendData: any = {\n            name: ServerConfig.nationMap_upBuild,\n            msg: {\n                x: x,\n                y: y,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public delBuild(x: number, y: number): void {\n        let sendData: any = {\n            name: ServerConfig.nationMap_delBuild,\n            msg: {\n                x: x,\n                y: y,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    \n    public upPosition(x: number, y: number): void {\n        let sendData: any = {\n            name: ServerConfig.role_upPosition,\n            msg: {\n                x: x,\n                y: y\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public posTagList(): void {\n        let sendData: any = {\n            name: ServerConfig.role_posTagList,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    //1添加、0移除\n    public opPosTag(type:number, x: number, y: number, name = \"\"): void {\n        let sendData: any = {\n            name: ServerConfig.role_opPosTag,\n            msg: {\n                type:type,\n                x:x,\n                y:y,\n                name:name,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    /**\n     * 处理获取主城事件，用于解决循环依赖\n     */\n    private onGetMyMainCity(callback: (city: MapCityData) => void): void {\n        const myCity = this._cityProxy.getMyMainCity();\n        if (callback && typeof callback === 'function') {\n            callback(myCity);\n        }\n    }\n\n}\n"]}