{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts"], "names": ["_decorator", "Component", "Label", "Node", "MapUICommand", "CityAdditionType", "ccclass", "property", "FacilityAdditionItemLogic", "setData", "data", "cfg", "index", "additionType", "additions", "additionCfg", "getInstance", "proxy", "getFacilityAdditionCfgByType", "labelName", "string", "des", "level", "upLevels", "length", "upNode", "active", "maxNode", "v", "values", "Durable", "labeMax", "value", "replace", "labelOld", "labelNew"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAGhCC,MAAAA,Y;;AACiDC,MAAAA,gB,iBAAAA,gB;;;;;;;OAHlD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;yBAMTQ,yB,WADpBF,OAAO,CAAC,2BAAD,C,UAEHC,QAAQ,CAACL,KAAD,C,UAERK,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACL,KAAD,C,UAERK,QAAQ,CAACL,KAAD,C,UAERK,QAAQ,CAACL,KAAD,C,oCAZb,MACqBM,yBADrB,SACuDP,SADvD,CACiE;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AActDQ,QAAAA,OAAO,CAACC,IAAD,EAAgBC,GAAhB,EAAoCC,KAApC,EAAwD;AAClE,cAAIC,YAAmB,GAAGF,GAAG,CAACG,SAAJ,CAAcF,KAAd,CAA1B;AACA,cAAIG,WAAgC,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,4BAAjC,CAA8DL,YAA9D,CAAvC;AACA,eAAKM,SAAL,CAAeC,MAAf,GAAwBL,WAAW,CAACM,GAApC;;AACA,cAAIX,IAAI,CAACY,KAAL,IAAcX,GAAG,CAACY,QAAJ,CAAaC,MAA/B,EAAuC;AACnC;AACA,iBAAKC,MAAL,CAAYC,MAAZ,GAAqB,KAArB;AACA,iBAAKC,OAAL,CAAaD,MAAb,GAAsB,IAAtB;AAEA,gBAAIE,CAAC,GAAGjB,GAAG,CAACY,QAAJ,CAAab,IAAI,CAACY,KAAL,GAAa,CAA1B,EAA6BO,MAA7B,CAAoCjB,KAApC,CAAR;;AACA,gBAAGC,YAAY,IAAI;AAAA;AAAA,sDAAiBiB,OAApC,EAA4C;AACxCF,cAAAA,CAAC,GAAGA,CAAC,GAAC,GAAN;AACH;;AACD,iBAAKG,OAAL,CAAaX,MAAb,GAAsBL,WAAW,CAACiB,KAAZ,CAAkBC,OAAlB,CAA0B,KAA1B,EAAkCL,CAAC,GAAE,EAArC,CAAtB;AACH,WAVD,MAUO;AACH,iBAAKH,MAAL,CAAYC,MAAZ,GAAqB,IAArB;AACA,iBAAKC,OAAL,CAAaD,MAAb,GAAsB,KAAtB;;AACA,gBAAIhB,IAAI,CAACY,KAAL,IAAc,CAAlB,EAAqB;AACjB;AACA,mBAAKY,QAAL,CAAcd,MAAd,GAAuB,KAAvB;AACH,aAHD,MAGO;AAEH,kBAAIQ,CAAC,GAAGjB,GAAG,CAACY,QAAJ,CAAab,IAAI,CAACY,KAAL,GAAa,CAA1B,EAA6BO,MAA7B,CAAoCjB,KAApC,CAAR;;AACA,kBAAGC,YAAY,IAAI;AAAA;AAAA,wDAAiBiB,OAApC,EAA4C;AACxCF,gBAAAA,CAAC,GAAGA,CAAC,GAAC,GAAN;AACH;;AAED,mBAAKM,QAAL,CAAcd,MAAd,GAAuBL,WAAW,CAACiB,KAAZ,CAAkBC,OAAlB,CAA0B,KAA1B,EAAiCL,CAAC,GAAG,EAArC,CAAvB;AACH;;AAED,gBAAIA,CAAC,GAAGjB,GAAG,CAACY,QAAJ,CAAab,IAAI,CAACY,KAAlB,EAAyBO,MAAzB,CAAgCjB,KAAhC,CAAR;;AACA,gBAAGC,YAAY,IAAI;AAAA;AAAA,sDAAiBiB,OAApC,EAA4C;AACxCF,cAAAA,CAAC,GAAGA,CAAC,GAAC,GAAN;AACH;;AAED,iBAAKO,QAAL,CAAcf,MAAd,GAAuBL,WAAW,CAACiB,KAAZ,CAAkBC,OAAlB,CAA0B,KAA1B,EAAiCL,CAAC,GAAG,EAArC,CAAvB;AACH;AACJ;;AAnD4D,O;;;;;iBAE1C,I;;;;;;;iBAEJ,I;;;;;;;iBAEC,I;;;;;;;iBAEE,I;;;;;;;iBAEA,I;;;;;;;iBAED,I", "sourcesContent": ["import { _decorator, Component, Label, Node } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport MapUICommand from \"./MapUICommand\";\nimport { Facility, FacilityAdditionCfg, FacilityConfig, CityAdditionType } from \"./MapUIProxy\";\n\n@ccclass('FacilityAdditionItemLogic')\nexport default class FacilityAdditionItemLogic extends Component {\n    @property(Label)\n    labelName: Label = null;\n    @property(Node)\n    upNode: Node = null;\n    @property(Node)\n    maxNode: Node = null;\n    @property(Label)\n    labelOld: Label = null;\n    @property(Label)\n    labelNew: Label = null;\n    @property(Label)\n    labeMax: Label = null;\n\n    public setData(data:Facility, cfg:FacilityConfig, index:number): void {\n        let additionType:number = cfg.additions[index];\n        let additionCfg: FacilityAdditionCfg = MapUICommand.getInstance().proxy.getFacilityAdditionCfgByType(additionType);\n        this.labelName.string = additionCfg.des;\n        if (data.level >= cfg.upLevels.length) {\n            //达到最大等级\n            this.upNode.active = false;\n            this.maxNode.active = true;\n\n            var v = cfg.upLevels[data.level - 1].values[index];\n            if(additionType == CityAdditionType.Durable){\n                v = v/100\n            }\n            this.labeMax.string = additionCfg.value.replace(\"%n%\",  v+ \"\");\n        } else {\n            this.upNode.active = true;\n            this.maxNode.active = false;\n            if (data.level == 0) {\n                //代表未升级过\n                this.labelOld.string = \"---\";\n            } else {\n\n                var v = cfg.upLevels[data.level - 1].values[index];\n                if(additionType == CityAdditionType.Durable){\n                    v = v/100\n                }\n\n                this.labelOld.string = additionCfg.value.replace(\"%n%\", v + \"\");\n            }\n\n            var v = cfg.upLevels[data.level].values[index];\n            if(additionType == CityAdditionType.Durable){\n                v = v/100\n            }\n\n            this.labelNew.string = additionCfg.value.replace(\"%n%\", v + \"\");\n        }\n    }\n}\n"]}