{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts"], "names": ["_decorator", "Component", "Label", "AudioManager", "UnionCommand", "ccclass", "property", "UnionApplyItemLogic", "updateItem", "data", "_applyData", "name<PERSON><PERSON><PERSON>", "string", "nick_name", "verify", "event", "decide", "instance", "playClick", "getInstance", "unionVerify", "id", "Number"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AACvBC,MAAAA,Y,iBAAAA,Y;;AAEFC,MAAAA,Y;;;;;;;OADD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;yBAKTO,mB,WADpBF,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAACJ,KAAD,C,oCAFb,MACqBK,mBADrB,SACiDN,SADjD,CAC2D;AAAA;AAAA;;AAAA;;AAAA,8CAG1B,IAH0B;AAAA;;AAK7CO,QAAAA,UAAU,CAACC,IAAD,EAAiB;AACjC,eAAKC,UAAL,GAAkBD,IAAlB;AACA,eAAKE,SAAL,CAAeC,MAAf,GAAwB,KAAKF,UAAL,CAAgBG,SAAxC;AACH;;AACSC,QAAAA,MAAM,CAACC,KAAD,EAAWC,MAAa,GAAG,CAA3B,EAAkC;AAC9C;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,WAA3B,CAAuC,KAAKV,UAAL,CAAgBW,EAAvD,EAA0DC,MAAM,CAACN,MAAD,CAAhE;AACH;;AAZsD,O;;;;;iBAE7B,I", "sourcesContent": ["import { _decorator, Component, Label } from 'cc';\nimport { AudioManager } from '../common/AudioManager';\nconst { ccclass, property } = _decorator;\nimport UnionCommand from \"./UnionCommand\";\nimport { Apply } from \"./UnionProxy\";\n\n@ccclass('UnionApplyItemLogic')\nexport default class UnionApplyItemLogic extends Component {\n    @property(Label)\n    nameLabel: Label | null = null;\n    protected _applyData:Apply = null;\n\n    protected updateItem(data:Apply):void{\n        this._applyData = data;\n        this.nameLabel.string = this._applyData.nick_name;\n    }\n    protected verify(event:any,decide:number = 0):void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionVerify(this._applyData.id,Number(decide));\n    }\n}\n\n"]}