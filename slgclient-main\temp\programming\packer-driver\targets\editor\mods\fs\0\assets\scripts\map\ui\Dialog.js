System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Label, Button, AudioManager, DialogType, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _temp, _crd, ccclass, property, Dialog;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfAudioManager(extras) {
    _reporterNs.report("AudioManager", "../../common/AudioManager", _context.meta, extras);
  }

  _export("DialogType", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Button = _cc.Button;
    }, function (_unresolved_2) {
      AudioManager = _unresolved_2.AudioManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "28245ZSwXxFM7DmgOAcdnNr", "Dialog", undefined);

      ({
        ccclass,
        property
      } = _decorator);

      _export("DialogType", DialogType = class DialogType {});

      _defineProperty(DialogType, "Default", 0);

      _defineProperty(DialogType, "OnlyCancel", 1);

      _defineProperty(DialogType, "OnlyConfirm", 2);

      _export("default", Dialog = (_dec = ccclass('Dialog'), _dec2 = property(Label), _dec3 = property(Button), _dec4 = property(Button), _dec(_class = (_class2 = (_temp = class Dialog extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "text", _descriptor, this);

          _initializerDefineProperty(this, "confirmBtn", _descriptor2, this);

          _initializerDefineProperty(this, "cancelBtn", _descriptor3, this);

          _defineProperty(this, "closeCB", null);

          _defineProperty(this, "confirmCB", null);

          _defineProperty(this, "cancelCB", null);
        }

        close() {
          if (this.closeCB) {
            this.closeCB();
          }

          this.node.active = false;
        }

        show(text, type) {
          this.text.string = text;

          if (type == DialogType.Default) {
            this.confirmBtn.node.active = true;
            this.cancelBtn.node.active = true;
          } else if (type == DialogType.OnlyCancel) {
            this.confirmBtn.node.active = false;
            this.cancelBtn.node.active = true;
          } else if (type == DialogType.OnlyConfirm) {
            this.confirmBtn.node.active = true;
            this.cancelBtn.node.active = false;
          }
        }

        setClose(cb) {
          this.closeCB = cb;
        }

        setConfirmCB(cb) {
          this.confirmCB = cb;
        }

        setCancelCB(cb) {
          this.cancelCB = cb;
        }

        clickConfirm() {
          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick();

          if (this.confirmCB) {
            this.confirmCB();
          }

          this.close();
        }

        clickCancel() {
          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick();

          if (this.cancelCB) {
            this.cancelCB();
          }

          this.close();
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "text", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "confirmBtn", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "cancelBtn", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=Dialog.js.map