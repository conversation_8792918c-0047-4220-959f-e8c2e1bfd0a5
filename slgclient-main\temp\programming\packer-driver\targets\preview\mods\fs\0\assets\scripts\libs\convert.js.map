{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts"], "names": ["convert", "stringToByte", "str", "bytes", "Array", "len", "c", "length", "i", "charCodeAt", "push", "byteToString", "arr", "_arr", "one", "toString", "v", "match", "bytesLength", "store", "slice", "st", "String", "fromCharCode", "parseInt"], "mappings": ";;;iBAEaA,O;;;;;;;;;;;;;yBAAAA,O,GAAN,MAAMA,OAAN,CAAc;AACjBC,QAAAA,YAAY,CAACC,GAAD,EAAM;AACd,cAAIC,KAAK,GAAG,IAAIC,KAAJ,EAAZ;AACA,cAAIC,GAAJ,EAASC,CAAT;AACAD,UAAAA,GAAG,GAAGH,GAAG,CAACK,MAAV;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,GAApB,EAAyBG,CAAC,EAA1B,EAA8B;AAC1BF,YAAAA,CAAC,GAAGJ,GAAG,CAACO,UAAJ,CAAeD,CAAf,CAAJ;;AACA,gBAAIF,CAAC,IAAI,QAAL,IAAiBA,CAAC,IAAI,QAA1B,EAAoC;AAChCH,cAAAA,KAAK,CAACO,IAAN,CAAaJ,CAAC,IAAI,EAAN,GAAY,IAAb,GAAqB,IAAhC;AACAH,cAAAA,KAAK,CAACO,IAAN,CAAaJ,CAAC,IAAI,EAAN,GAAY,IAAb,GAAqB,IAAhC;AACAH,cAAAA,KAAK,CAACO,IAAN,CAAaJ,CAAC,IAAI,CAAN,GAAW,IAAZ,GAAoB,IAA/B;AACAH,cAAAA,KAAK,CAACO,IAAN,CAAYJ,CAAC,GAAG,IAAL,GAAa,IAAxB;AACH,aALD,MAKO,IAAIA,CAAC,IAAI,QAAL,IAAiBA,CAAC,IAAI,QAA1B,EAAoC;AACvCH,cAAAA,KAAK,CAACO,IAAN,CAAaJ,CAAC,IAAI,EAAN,GAAY,IAAb,GAAqB,IAAhC;AACAH,cAAAA,KAAK,CAACO,IAAN,CAAaJ,CAAC,IAAI,CAAN,GAAW,IAAZ,GAAoB,IAA/B;AACAH,cAAAA,KAAK,CAACO,IAAN,CAAYJ,CAAC,GAAG,IAAL,GAAa,IAAxB;AACH,aAJM,MAIA,IAAIA,CAAC,IAAI,QAAL,IAAiBA,CAAC,IAAI,QAA1B,EAAoC;AACvCH,cAAAA,KAAK,CAACO,IAAN,CAAaJ,CAAC,IAAI,CAAN,GAAW,IAAZ,GAAoB,IAA/B;AACAH,cAAAA,KAAK,CAACO,IAAN,CAAYJ,CAAC,GAAG,IAAL,GAAa,IAAxB;AACH,aAHM,MAGA;AACHH,cAAAA,KAAK,CAACO,IAAN,CAAWJ,CAAC,GAAG,IAAf;AACH;AACJ;;AACD,iBAAOH,KAAP;AACH;;AAEDQ,QAAAA,YAAY,CAACC,GAAD,EAAM;AACd,cAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;AACzB,mBAAOA,GAAP;AACH;;AACD,cAAIV,GAAG,GAAG,EAAV;AAAA,cAAcW,IAAI,GAAGD,GAArB;;AACA,eAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGK,IAAI,CAACN,MAAzB,EAAiCC,CAAC,EAAlC,EAAsC;AAClC,gBAAIM,GAAG,GAAGD,IAAI,CAACL,CAAD,CAAJ,CAAQO,QAAR,CAAiB,CAAjB,CAAV;AAAA,gBAA+BC,CAAC,GAAGF,GAAG,CAACG,KAAJ,CAAU,WAAV,CAAnC;;AACA,gBAAID,CAAC,IAAIF,GAAG,CAACP,MAAJ,IAAc,CAAvB,EAA0B;AACtB,kBAAIW,WAAW,GAAGF,CAAC,CAAC,CAAD,CAAD,CAAKT,MAAvB;;AACA,kBAAIY,KAAK,GAAGN,IAAI,CAACL,CAAD,CAAJ,CAAQO,QAAR,CAAiB,CAAjB,EAAoBK,KAApB,CAA0B,IAAIF,WAA9B,CAAZ;;AACA,mBAAK,IAAIG,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGH,WAAtB,EAAmCG,EAAE,EAArC,EAAyC;AACrCF,gBAAAA,KAAK,IAAIN,IAAI,CAACQ,EAAE,GAAGb,CAAN,CAAJ,CAAaO,QAAb,CAAsB,CAAtB,EAAyBK,KAAzB,CAA+B,CAA/B,CAAT;AACH;;AACDlB,cAAAA,GAAG,IAAIoB,MAAM,CAACC,YAAP,CAAoBC,QAAQ,CAACL,KAAD,EAAQ,CAAR,CAA5B,CAAP;AACAX,cAAAA,CAAC,IAAIU,WAAW,GAAG,CAAnB;AACH,aARD,MAQO;AACHhB,cAAAA,GAAG,IAAIoB,MAAM,CAACC,YAAP,CAAoBV,IAAI,CAACL,CAAD,CAAxB,CAAP;AACH;AACJ;;AACD,iBAAON,GAAP;AACH;;AA9CgB,O", "sourcesContent": ["\n\nexport class convert {\n    stringToByte(str) {\n        var bytes = new Array();\n        var len, c;\n        len = str.length;\n        for (var i = 0; i < len; i++) {\n            c = str.charCodeAt(i);\n            if (c >= 0x010000 && c <= 0x10FFFF) {\n                bytes.push(((c >> 18) & 0x07) | 0xF0);\n                bytes.push(((c >> 12) & 0x3F) | 0x80);\n                bytes.push(((c >> 6) & 0x3F) | 0x80);\n                bytes.push((c & 0x3F) | 0x80);\n            } else if (c >= 0x000800 && c <= 0x00FFFF) {\n                bytes.push(((c >> 12) & 0x0F) | 0xE0);\n                bytes.push(((c >> 6) & 0x3F) | 0x80);\n                bytes.push((c & 0x3F) | 0x80);\n            } else if (c >= 0x000080 && c <= 0x0007FF) {\n                bytes.push(((c >> 6) & 0x1F) | 0xC0);\n                bytes.push((c & 0x3F) | 0x80);\n            } else {\n                bytes.push(c & 0xFF);\n            }\n        }\n        return bytes;\n    }\n\n    byteToString(arr) {\n        if (typeof arr === 'string') {\n            return arr;\n        }\n        var str = '', _arr = arr;\n        for (var i = 0; i < _arr.length; i++) {\n            var one = _arr[i].toString(2), v = one.match(/^1+?(?=0)/);\n            if (v && one.length == 8) {\n                var bytesLength = v[0].length;\n                var store = _arr[i].toString(2).slice(7 - bytesLength);\n                for (var st = 1; st < bytesLength; st++) {\n                    store += _arr[st + i].toString(2).slice(2);\n                }\n                str += String.fromCharCode(parseInt(store, 2));\n                i += bytesLength - 1;\n            } else {\n                str += String.fromCharCode(_arr[i]);\n            }\n        }\n        return str;\n    }\n}"]}