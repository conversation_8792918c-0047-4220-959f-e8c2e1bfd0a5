{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts"], "names": ["_decorator", "Component", "EditBox", "LocalCache", "LoginCommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "on", "loginComplete", "onLoginComplete", "data", "getLoginValidation", "console", "log", "editName", "string", "username", "editPass", "password", "onDestroy", "targetOff", "node", "active", "onClickRegister", "instance", "playClick", "emit", "showToast", "getInstance", "register", "onClickLogin", "accountLogin", "onClickClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;;AAGvBC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBASTU,U,WADpBF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACP,OAAD,C,UAGRO,QAAQ,CAACP,OAAD,C,oCANb,MACqBQ,UADrB,SACwCT,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAQpCU,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,aAAvB,EAAsC,KAAKC,eAA3C,EAA4D,IAA5D;AAEA,cAAIC,IAAI,GAAG;AAAA;AAAA,wCAAWC,kBAAX,EAAX;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAgCH,IAAhC;;AACA,cAAGA,IAAH,EAAQ;AACJ,iBAAKI,QAAL,CAAcC,MAAd,GAAuBL,IAAI,CAACM,QAA5B;AACA,iBAAKC,QAAL,CAAcF,MAAd,GAAuBL,IAAI,CAACQ,QAA5B;AACH;AACJ;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESX,QAAAA,eAAe,GAAQ;AAC7B,eAAKY,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAESC,QAAAA,eAAe,GAAS;AAC9B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AAEA,cAAG,CAAC,KAAKX,QAAL,CAAcC,MAAf,IAAyB,CAAC,KAAKE,QAAL,CAAcF,MAA3C,EAAkD;AAC9C;AAAA;AAAA,sCAASW,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,QAApC;AACA;AACH;;AAED;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,QAA3B,CAAoC,KAAKf,QAAL,CAAcC,MAAlD,EAA0D,KAAKE,QAAL,CAAcF,MAAxE;AACH;;AAESe,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaN,QAAb,CAAsBC,SAAtB;;AAEA,cAAG,CAAC,KAAKX,QAAL,CAAcC,MAAf,IAAyB,CAAC,KAAKE,QAAL,CAAcF,MAA3C,EAAkD;AAC9C;AAAA;AAAA,sCAASW,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,QAApC;AACA;AACH;;AAED;AAAA;AAAA,4CAAaC,WAAb,GAA2BG,YAA3B,CAAwC,KAAKjB,QAAL,CAAcC,MAAtD,EAA8D,KAAKE,QAAL,CAAcF,MAA5E;AACH;;AAESiB,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaR,QAAb,CAAsBC,SAAtB;AACA,eAAKJ,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AApD6C,O;;;;;iBAG3B,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, EditBox, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { LocalCache } from \"../utils/LocalCache\";\nimport LoginCommand from \"./LoginCommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('LoginLogic')\nexport default class LoginLogic extends Component {\n\n    @property(EditBox)\n    editName:EditBox = null;\n\n    @property(EditBox)\n    editPass:Label = null;\n\n    protected onLoad(): void {\n        EventMgr.on(LogicEvent.loginComplete, this.onLoginComplete, this);\n\n        var data = LocalCache.getLoginValidation();\n        console.log(\"LoginLogic  data:\",data)\n        if(data){\n            this.editName.string = data.username;\n            this.editPass.string = data.password;\n        }\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n    }\n\n    protected onLoginComplete():void {\n        this.node.active = false;\n    }\n\n    protected onClickRegister(): void {\n        AudioManager.instance.playClick();\n\n        if(!this.editName.string || !this.editPass.string){\n            EventMgr.emit(LogicEvent.showToast, \"账号密码有误\");\n            return;\n        }\n\n        LoginCommand.getInstance().register(this.editName.string, this.editPass.string);\n    }\n\n    protected onClickLogin(): void {\n        AudioManager.instance.playClick();\n\n        if(!this.editName.string || !this.editPass.string){\n            EventMgr.emit(LogicEvent.showToast, \"账号密码有误\");\n            return;\n        }\n\n        LoginCommand.getInstance().accountLogin(this.editName.string, this.editPass.string)\n    }\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n    }\n}\n"]}