# 半成品SLG系统改造开发任务清单

## 项目概述

基于现有半成品SLG项目，进行五大系统的全面改造升级，包括竖屏适配、启动界面重设计、登录系统重构、角色系统优化和地图系统扩展。

## 总体开发计划

**预计总工期**: 45-60个工作日
**团队配置**: 2名前端开发 + 1名后端开发 + 1名UI设计师
**技术栈**: Go + Cocos Creator + TypeScript + MySQL

---

## 一、屏幕方向适配系统 (优先级: 🔥高)

**目标**: 将游戏从横屏改为竖屏适配，设计分辨率1080×1920像素

应

### 1.2 主场景布局调整
- **工作内容**: 调整MainScene中的UI布局，适配竖屏显示
- **具体任务**:
  - 重新布局登录界面UI组件
  - 调整角色创建界面布局
  - 优化按钮和输入框的位置和大小
- **负责人**: 前端开发
- **工期**: 2天
- **依赖**: 1.1完成

### 1.3 地图界面布局调整
- **工作内容**: 调整MapScene和MapUIScene的UI布局
- **具体任务**:
  - 重新设计地图操作面板布局
  - 调整建筑信息面板位置
  - 优化军队操作界面
  - 适配竖屏下的地图显示区域
- **负责人**: 前端开发
- **工期**: 3天
- **依赖**: 1.1完成

### 1.4 UI组件适配优化
- **工作内容**: 优化所有UI组件的尺寸和位置
- **具体任务**:
  - 调整所有弹窗的尺寸和位置
  - 优化列表组件的显示效果
  - 适配不同屏幕比例的设备
  - 调整字体大小和间距
- **负责人**: 前端开发
- **工期**: 2天
- **依赖**: 1.2, 1.3完成

### 1.5 适配测试与优化
- **工作内容**: 在不同尺寸的设备上测试竖屏适配效果
- **具体任务**:
  - 在多种设备上测试显示效果
  - 修复适配问题
  - 优化性能表现
- **负责人**: 前端开发
- **工期**: 1天
- **依赖**: 1.4完成

**小计工期**: 8.5天

---

## 二、启动界面系统重设计 (优先级: 🔥高)

**目标**: 重新设计游戏启动流程和主界面

### 2.1 主界面UI设计与实现
- **工作内容**: 设计并实现新的主界面
- **具体任务**:
  - 设计主界面背景图和UI元素
  - 实现"一统天下"和"解甲归田"按钮
  - 添加游戏标题和装饰元素
- **负责人**: UI设计师 + 前端开发
- **工期**: 3天
- **依赖**: 1.1完成

### 2.2 退出确认对话框实现
- **工作内容**: 实现退出确认对话框
- **具体任务**:
  - 设计退出确认对话框UI
  - 实现"再玩一会"和"先行离开"按钮逻辑
  - 添加对话框动画效果
- **负责人**: 前端开发
- **工期**: 1天
- **依赖**: 2.1完成

### 2.3 背景音乐系统实现
- **工作内容**: 实现背景音乐系统
- **具体任务**:
  - 实现音频管理器
  - APK版本自动播放逻辑
  - H5版本用户交互后播放逻辑
  - 音量控制和循环播放
- **负责人**: 前端开发
- **工期**: 2天
- **依赖**: 2.5完成

### 2.4 启动流程重构
- **工作内容**: 重构游戏启动流程
- **具体任务**:
  - 移除原有的直接登录/注册窗口
  - 集成新的主界面到启动流程
  - 调整场景切换逻辑
- **负责人**: 前端开发
- **工期**: 1天
- **依赖**: 2.1, 2.2完成

### 2.5 音频资源准备
- **工作内容**: 准备游戏背景音乐资源
- **具体任务**:
  - 选择或制作背景音乐
  - 优化音频文件大小和格式
  - 配置音频资源加载
- **负责人**: UI设计师
- **工期**: 1天
- **依赖**: 无

**小计工期**: 8天

---

## 三、登录注册系统重构 (优先级: 🔥高)

**目标**: 将用户名密码登录改为手机号验证码登录

### 3.1 手机号输入组件实现
- **工作内容**: 实现手机号输入组件
- **具体任务**:
  - 创建手机号输入框，限制只能输入数字
  - 实现11位长度限制
  - 添加实时格式验证
  - 优化输入体验（自动格式化显示）
- **负责人**: 前端开发
- **工期**: 1天
- **依赖**: 1.2完成

### 3.2 手机号注册状态检查
- **工作内容**: 实现手机号注册状态实时检查
- **具体任务**:
  - 实现手机号输入完成后的状态检查
  - 显示欢迎回来信息（绿色文字）
  - 显示新用户提示信息（绿色文字）
  - 优化检查接口的调用频率
- **负责人**: 前端开发 + 后端开发
- **工期**: 1.5天
- **依赖**: 3.1, 3.7完成

### 3.3 验证码输入组件实现
- **工作内容**: 实现验证码输入组件
- **具体任务**:
  - 创建6位数字验证码输入框
  - 替换原有的密码输入框
  - 实现自动聚焦和跳转
  - 添加输入完成检测
- **负责人**: 前端开发
- **工期**: 1天
- **依赖**: 3.1完成

### 3.4 获取验证码按钮实现
- **工作内容**: 实现获取验证码按钮
- **具体任务**:
  - 实现按钮状态控制（默认不可点击）
  - 手机号11位后激活按钮
  - 实现60秒倒计时功能
  - 倒计时期间按钮不可点击
- **负责人**: 前端开发
- **工期**: 1天
- **依赖**: 3.1完成

### 3.5 临时验证码机制实现
- **工作内容**: 实现临时验证码机制
- **具体任务**:
  - 在验证码输入框下方显示测试提示
  - 实现通用验证码888888的验证逻辑
  - 添加测试环境标识
- **负责人**: 前端开发 + 后端开发
- **工期**: 0.5天
- **依赖**: 3.3, 3.7完成

### 3.6 登录按钮状态控制
- **工作内容**: 实现登录按钮的状态控制
- **具体任务**:
  - 默认登录按钮不可点击
  - 手机号11位且验证码6位时激活
  - 添加按钮状态视觉反馈
- **负责人**: 前端开发
- **工期**: 0.5天
- **依赖**: 3.1, 3.3完成

### 3.7 后端登录接口修改
- **工作内容**: 修改后端登录接口
- **具体任务**:
  - 修改登录接口支持手机号+验证码
  - 实现临时验证码888888的验证逻辑
  - 添加手机号注册状态检查接口
  - 保持向后兼容性
- **负责人**: 后端开发
- **工期**: 2天
- **依赖**: 3.8完成

### 3.8 数据库表结构调整
- **工作内容**: 调整用户表结构
- **具体任务**:
  - 在tb_user_info表添加phone字段
  - 添加phone字段的唯一索引
  - 修改相关查询逻辑
  - 数据迁移脚本编写
- **负责人**: 后端开发
- **工期**: 1天
- **依赖**: 无

**小计工期**: 8.5天

---

## 四、角色系统优化 (优先级: 🟡中)

**目标**: 完善角色创建和进入游戏的流程

### 4.1 国家选择界面设计
- **工作内容**: 设计并实现8个国家选择界面
- **具体任务**:
  - 设计8个国家的旗帜和标识
  - 编写各国特色描述文案
  - 实现国家选择UI界面
  - 添加国家选择动画效果
- **负责人**: UI设计师 + 前端开发
- **工期**: 3天
- **依赖**: 1.2完成

### 4.2 角色名输入与验证
- **工作内容**: 实现角色名输入框和验证
- **具体任务**:
  - 创建角色名输入框
  - 实现随机角色名生成按钮
  - 实现全服角色名唯一性检查
  - 添加角色名格式验证
- **负责人**: 前端开发 + 后端开发
- **工期**: 2天
- **依赖**: 4.6完成

### 4.3 推荐国家系统实现
- **工作内容**: 实现推荐国家系统
- **具体任务**:
  - 实现推荐国家标识显示
  - 实现500战国币奖励机制
  - 添加推荐理由说明
- **负责人**: 前端开发 + 后端开发
- **工期**: 1.5天
- **依赖**: 4.6完成

### 4.4 已有角色处理逻辑
- **工作内容**: 实现已有角色的处理逻辑
- **具体任务**:
  - 登录后检查是否有角色
  - 有角色直接进入游戏大地图
  - 自动定位到角色主城中央
  - 优化进入游戏的加载流程
- **负责人**: 前端开发 + 后端开发
- **工期**: 2天
- **依赖**: 4.6完成

### 4.5 创角完成流程优化
- **工作内容**: 优化创角完成流程
- **具体任务**:
  - 创建成功后直接进入游戏
  - 优化新手引导流程
  - 添加创角成功动画
- **负责人**: 前端开发
- **工期**: 1天
- **依赖**: 4.1, 4.2完成

### 4.6 后端国家系统实现
- **工作内容**: 后端实现国家系统
- **具体任务**:
  - 实现国家信息管理
  - 实现玩家数量统计
  - 实现推荐国家算法
  - 添加国家相关接口
- **负责人**: 后端开发
- **工期**: 2天
- **依赖**: 4.7完成

### 4.7 数据库表结构扩展
- **工作内容**: 扩展角色表结构
- **具体任务**:
  - 在tb_role_1表添加country字段
  - 创建国家信息表
  - 添加相关索引和约束
  - 编写数据迁移脚本
- **负责人**: 后端开发
- **工期**: 1天
- **依赖**: 无

**小计工期**: 12.5天

---

## 五、地图系统扩展 (优先级: 🟡中)

**目标**: 扩大地图规模并优化显示效果

### 5.1 地图尺寸配置修改
- **工作内容**: 修改地图配置文件
- **具体任务**:
  - 修改MapUtil.ts中的地图尺寸配置
  - 修改服务端地图尺寸常量
  - 更新相关配置文件
- **负责人**: 前端开发 + 后端开发
- **工期**: 0.5天
- **依赖**: 无

### 5.2 瓦片尺寸调整
- **工作内容**: 调整瓦片尺寸
- **具体任务**:
  - 修改Tiled地图文件的瓦片尺寸
  - 调整相关贴图资源
  - 更新坐标转换算法
- **负责人**: 前端开发
- **工期**: 2天
- **依赖**: 5.1完成

### 5.3 地图数据存储优化
- **工作内容**: 优化地图数据存储结构
- **具体任务**:
  - 优化数据库表结构和索引
  - 实现数据分片存储
  - 优化查询性能
  - 添加数据缓存机制
- **负责人**: 后端开发
- **工期**: 3天
- **依赖**: 5.1完成

### 5.4 分区加载机制优化
- **工作内容**: 优化地图分区加载机制
- **具体任务**:
  - 调整分区大小算法
  - 优化内存管理
  - 改进加载策略
  - 添加预加载机制
- **负责人**: 前端开发
- **工期**: 3天
- **依赖**: 5.2完成

### 5.5 地形系统设计
- **工作内容**: 设计并实现地形系统
- **具体任务**:
  - 设计地形类型和属性
  - 实现地形生成算法
  - 添加地形对游戏的影响
- **负责人**: 后端开发 + 前端开发
- **工期**: 4天
- **依赖**: 5.3完成

### 5.6 资源点系统优化
- **工作内容**: 优化资源点生成和刷新机制
- **具体任务**:
  - 调整资源点密度和分布
  - 优化刷新算法
  - 平衡资源产出
- **负责人**: 后端开发
- **工期**: 2天
- **依赖**: 5.5完成

### 5.7 系统城市和要塞分布
- **工作内容**: 设计系统城市和要塞分布
- **具体任务**:
  - 设计城市和要塞的位置
  - 平衡各区域的重要性
  - 实现自动生成算法
- **负责人**: 后端开发
- **工期**: 2天
- **依赖**: 5.5完成

### 5.8 国家边界划分系统
- **工作内容**: 实现8个国家的边界划分
- **具体任务**:
  - 设计国家领土划分方案
  - 实现边界检测算法
  - 添加中立区和争夺区
  - 实现边界可视化
- **负责人**: 后端开发 + 前端开发
- **工期**: 3天
- **依赖**: 4.6完成

### 5.9 新手保护区域设计
- **工作内容**: 设计并实现新手保护区域
- **具体任务**:
  - 设计新手保护机制
  - 实现保护区域标识
  - 添加保护期限管理
- **负责人**: 后端开发
- **工期**: 2天
- **依赖**: 5.8完成

### 5.10 地图性能测试与优化
- **工作内容**: 进行大地图的性能测试
- **具体任务**:
  - 测试加载性能
  - 优化内存占用
  - 测试网络传输性能
  - 修复性能问题
- **负责人**: 前端开发 + 后端开发
- **工期**: 3天
- **依赖**: 5.4, 5.8完成

**小计工期**: 24.5天

---

## 总体任务依赖关系

### 关键路径分析
1. **第一阶段** (1-10天): 屏幕适配 + 启动界面 + 登录系统基础
2. **第二阶段** (11-25天): 角色系统 + 地图系统基础
3. **第三阶段** (26-40天): 地图系统高级功能
4. **第四阶段** (41-50天): 整体测试和优化

### 并行开发建议
- 屏幕适配和启动界面可以并行开发
- 登录系统的前后端可以并行开发
- 地图系统的不同模块可以并行开发

### 风险控制
- 地图系统扩展是最大的技术风险点
- 建议优先完成核心功能，高级功能可以后续迭代
- 性能测试应该贯穿整个开发过程

## 需要进一步讨论的技术细节

### 高优先级讨论项
1. **地图分区策略**: 1000×1000地图的具体分区方案
2. **国家边界设计**: 8个国家的具体领土划分
3. **性能优化方案**: 大地图的具体优化策略
4. **数据库分片**: 是否需要实现数据库分片

### 中优先级讨论项
1. **新手引导流程**: 详细的新手引导设计
2. **推荐国家算法**: 具体的推荐逻辑
3. **音频资源选择**: 背景音乐的具体选择
4. **UI设计风格**: 整体UI风格的统一

### 低优先级讨论项
1. **特殊区域设计**: 中立区和争夺区的具体玩法
2. **地形影响机制**: 地形对游戏的具体影响
3. **资源平衡性**: 资源产出的平衡调整
4. **扩展功能**: 后续可能的功能扩展

---

**文档版本**: v1.0
**创建日期**: 2024年12月
**最后更新**: 2024年12月
