{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts"], "names": ["_decorator", "Component", "Node", "Label", "LoginCommand", "DateUtil", "MapUICommand", "EventMgr", "GeneralItemLogic", "AudioManager", "LogicEvent", "ccclass", "property", "WarReportItemLogic", "onLoad", "winNode", "active", "loseNode", "updateItem", "data", "_curData", "isRead", "getInstance", "proxy", "id", "readBg", "setTeams", "ackNode", "beg_attack_general", "defNode", "beg_defense_general", "roleData", "getRoleData", "isMeWin", "attack_rid", "leftLabel", "string", "rid", "<PERSON><PERSON><PERSON><PERSON>", "defense_rid", "time<PERSON><PERSON><PERSON>", "converTimeStr", "ctime", "posLabel", "x", "y", "result", "node", "generals", "i", "length", "item", "com", "getComponent", "general", "setWarReportData", "onClickItem", "instance", "playClick", "warRead", "emit", "clickWarReport", "onClickPos", "closeReport", "scrollToMap"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AAG/BC,MAAAA,Y;;AAEAC,MAAAA,Q;;AACAC,MAAAA,Y;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,gB;;AACEC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAVH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAaTa,kB,WADpBF,OAAO,CAAC,oBAAD,C,UAKHC,QAAQ,CAACV,IAAD,C,UAGRU,QAAQ,CAAC,CAACV,IAAD,CAAD,C,UAGRU,QAAQ,CAAC,CAACV,IAAD,CAAD,C,UAGRU,QAAQ,CAACV,IAAD,C,UAGRU,QAAQ,CAACV,IAAD,C,UAGRU,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ,CAACT,KAAD,C,WAGRS,QAAQ,CAACT,KAAD,C,oCA7Bb,MACqBU,kBADrB,SACgDZ,SADhD,CAC0D;AAAA;AAAA;;AAAA,4CAEzB,IAFyB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AA+B5Ca,QAAAA,MAAM,GAAO;AACnB,eAAKC,OAAL,CAAaC,MAAb,GAAsB,KAAKC,QAAL,CAAcD,MAAd,GAAuB,KAA7C;AACH;;AAESE,QAAAA,UAAU,CAACC,IAAD,EAAe;AAC/B,eAAKC,QAAL,GAAgBD,IAAhB;AAEA,cAAIE,MAAM,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCF,MAAjC,CAAwC,KAAKD,QAAL,CAAcI,EAAtD,CAAb;AACA,eAAKC,MAAL,CAAYT,MAAZ,GAAqBK,MAArB;AAEA,eAAKK,QAAL,CAAc,KAAKC,OAAnB,EAA2B,KAAKP,QAAL,CAAcQ,kBAAzC;AACA,eAAKF,QAAL,CAAc,KAAKG,OAAnB,EAA2B,KAAKT,QAAL,CAAcU,mBAAzC;AAEA,cAAIC,QAAa,GAAG;AAAA;AAAA,4CAAaT,WAAb,GAA2BC,KAA3B,CAAiCS,WAAjC,EAApB;AACA,eAAKC,OAAL,CAAa,KAAKb,QAAL,CAAcc,UAA3B;AAEA,eAAKC,SAAL,CAAeC,MAAf,GAAwBL,QAAQ,CAACM,GAAT,IAAgB,KAAKjB,QAAL,CAAcc,UAA9B,GAAyC,GAAzC,GAA6C,GAArE;AACA,eAAKI,UAAL,CAAgBF,MAAhB,GAAyBL,QAAQ,CAACM,GAAT,IAAgB,KAAKjB,QAAL,CAAcmB,WAA9B,GAA0C,GAA1C,GAA8C,GAAvE;AAEA,eAAKC,SAAL,CAAeJ,MAAf,GAAwB;AAAA;AAAA,oCAASK,aAAT,CAAuB,KAAKrB,QAAL,CAAcsB,KAArC,EAA4C,qBAA5C,CAAxB;AAEA,eAAKC,QAAL,CAAcP,MAAd,GAAuB,MAAM,KAAKhB,QAAL,CAAcwB,CAApB,GAAwB,GAAxB,GAA8B,KAAKxB,QAAL,CAAcyB,CAA5C,GAAgD,GAAvE;AACH;;AAESZ,QAAAA,OAAO,CAACI,GAAU,GAAG,CAAd,EAAqB;AAClC,cAAIN,QAAa,GAAG;AAAA;AAAA,4CAAaT,WAAb,GAA2BC,KAA3B,CAAiCS,WAAjC,EAApB;AACA,eAAKjB,OAAL,CAAaC,MAAb,GAAsB,KAAKC,QAAL,CAAcD,MAAd,GAAuB,KAA7C;;AAEA,cAAGe,QAAQ,CAACM,GAAT,IAAgBA,GAAnB,EAAuB;AACnB,gBAAG,KAAKjB,QAAL,CAAc0B,MAAd,IAAwB,CAA3B,EAA6B;AACzB,mBAAK7B,QAAL,CAAcD,MAAd,GAAuB,IAAvB;AACH,aAFD,MAEM,IAAG,KAAKI,QAAL,CAAc0B,MAAd,IAAwB,CAA3B,EAA6B,CAElC,CAFK,MAED;AACD,mBAAK/B,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACH;AACJ,WARD,MAQK;AACD,gBAAG,KAAKI,QAAL,CAAc0B,MAAd,IAAwB,CAA3B,EAA6B;AACzB,mBAAK/B,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACH,aAFD,MAEM,IAAG,KAAKI,QAAL,CAAc0B,MAAd,IAAwB,CAA3B,EAA6B,CAElC,CAFK,MAED;AACD,mBAAK7B,QAAL,CAAcD,MAAd,GAAuB,IAAvB;AACH;AACJ;AAEJ;;AAESU,QAAAA,QAAQ,CAACqB,IAAD,EAAaC,QAAb,EAA4B;AAC1C,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGF,IAAI,CAACG,MAAxB,EAAgCD,CAAC,EAAjC,EAAoC;AAChC,gBAAIE,IAAS,GAAGJ,IAAI,CAACE,CAAD,CAApB;AACA,gBAAIG,GAAG,GAAGD,IAAI,CAACE,YAAL;AAAA;AAAA,qDAAV;AACA,gBAAIC,OAAO,GAAGN,QAAQ,CAACC,CAAD,CAAtB;;AACA,gBAAGK,OAAH,EAAW;AACPH,cAAAA,IAAI,CAACnC,MAAL,GAAc,IAAd;;AACA,kBAAGoC,GAAH,EAAO;AACHA,gBAAAA,GAAG,CAACG,gBAAJ,CAAqBD,OAArB;AACH;AAEJ,aAND,MAMK;AACDH,cAAAA,IAAI,CAACnC,MAAL,GAAc,KAAd;AACH;AAEJ;AACJ;;AAESwC,QAAAA,WAAW,GAAO;AACxB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AAEA,cAAIrC,MAAM,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCF,MAAjC,CAAwC,KAAKD,QAAL,CAAcI,EAAtD,CAAb;;AACA,cAAG,CAACH,MAAJ,EAAW;AACP;AAAA;AAAA,8CAAaC,WAAb,GAA2BqC,OAA3B,CAAmC,KAAKvC,QAAL,CAAcI,EAAjD;AACH;;AAED;AAAA;AAAA,oCAASoC,IAAT,CAAc;AAAA;AAAA,wCAAWC,cAAzB,EAAyC,KAAKzC,QAA9C;AAEH;;AAES0C,QAAAA,UAAU,GAAE;AAClB;AAAA;AAAA,4CAAaL,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASE,IAAT,CAAc;AAAA;AAAA,wCAAWG,WAAzB;AACA;AAAA;AAAA,oCAASH,IAAT,CAAc;AAAA;AAAA,wCAAWI,WAAzB,EAAsC,KAAK5C,QAAL,CAAcwB,CAApD,EAAuD,KAAKxB,QAAL,CAAcyB,CAArE;AACH;;AAjHqD,O;;;;;iBAKxC,I;;;;;;;iBAGG,E;;;;;;;iBAGA,E;;;;;;;iBAGF,I;;;;;;;iBAGC,I;;;;;;;iBAGG,I;;;;;;;iBAGA,I;;;;;;;iBAGC,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, Node, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport LoginCommand from \"../../login/LoginCommand\";\nimport { Role } from \"../../login/LoginProxy\";\nimport DateUtil from \"../../utils/DateUtil\";\nimport Map<PERSON>Command from \"./MapUICommand\";\nimport { WarReport } from \"./MapUIProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport GeneralItemLogic from './GeneralItemLogic';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('WarReportItemLogic')\nexport default class WarReportItemLogic extends Component {\n\n    private _curData:WarReport = null;\n\n    @property(Node)\n    readBg:Node = null;\n\n    @property([Node])\n    ackNode:Node[] = [];\n\n    @property([Node])\n    defNode:Node[] = [];\n\n    @property(Node)\n    winNode:Node = null;\n\n    @property(Node)\n    loseNode:Node = null;\n\n    @property(Label)\n    timeLabel: Label = null;\n\n    @property(Label)\n    leftLabel: Label = null;\n\n    @property(Label)\n    rightLabel: Label = null;\n\n    @property(Label)\n    posLabel: Label = null;\n\n    protected onLoad():void{\n        this.winNode.active = this.loseNode.active = false;\n    }\n\n    protected updateItem(data:any):void{\n        this._curData = data;\n\n        var isRead = MapUICommand.getInstance().proxy.isRead(this._curData.id);\n        this.readBg.active = isRead;\n       \n        this.setTeams(this.ackNode,this._curData.beg_attack_general);\n        this.setTeams(this.defNode,this._curData.beg_defense_general);\n\n        var roleData:Role = LoginCommand.getInstance().proxy.getRoleData();\n        this.isMeWin(this._curData.attack_rid)\n        \n        this.leftLabel.string = roleData.rid == this._curData.attack_rid?\"我\":\"敌\";\n        this.rightLabel.string = roleData.rid == this._curData.defense_rid?\"我\":\"敌\"\n\n        this.timeLabel.string = DateUtil.converTimeStr(this._curData.ctime, \"YYYY-MM-DD hh:mm:ss\");\n\n        this.posLabel.string = \"(\" + this._curData.x + \",\" + this._curData.y + \")\";\n    }\n\n    protected isMeWin(rid:number = 0):void{\n        var roleData:Role = LoginCommand.getInstance().proxy.getRoleData();\n        this.winNode.active = this.loseNode.active = false;\n        \n        if(roleData.rid == rid){\n            if(this._curData.result == 0){\n                this.loseNode.active = true;\n            }else if(this._curData.result == 1){\n\n            }else{\n                this.winNode.active = true;\n            }\n        }else{\n            if(this._curData.result == 0){\n                this.winNode.active = true;\n            }else if(this._curData.result == 1){\n\n            }else{\n                this.loseNode.active = true;\n            }\n        }\n\n    }\n\n    protected setTeams(node:Node[],generals:any[]){\n        for(var i = 0; i < node.length ;i++){\n            let item:Node = node[i];\n            let com = item.getComponent(GeneralItemLogic);\n            var general = generals[i];\n            if(general){\n                item.active = true;\n                if(com){\n                    com.setWarReportData(general);\n                }\n\n            }else{\n                item.active = false;\n            }\n\n        }\n    }\n\n    protected onClickItem():void{\n        AudioManager.instance.playClick();\n\n        var isRead = MapUICommand.getInstance().proxy.isRead(this._curData.id);\n        if(!isRead){\n            MapUICommand.getInstance().warRead(this._curData.id);\n        }\n\n        EventMgr.emit(LogicEvent.clickWarReport, this._curData);\n       \n    }\n\n    protected onClickPos(){\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.closeReport);\n        EventMgr.emit(LogicEvent.scrollToMap, this._curData.x, this._curData.y);\n    }\n}\n"]}