{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts"], "names": ["_decorator", "Component", "Label", "Prefab", "Node", "Layout", "instantiate", "AudioManager", "GeneralCommand", "GeneralData", "GeneralItemLogic", "GeneralItemType", "ccclass", "property", "GeneralAddPrLogic", "onLoad", "_generalNode", "generalItemPrefab", "parent", "generalItemParent", "_nameObj", "force", "strategy", "defense", "speed", "destroy", "_addPrArr", "setData", "cfgData", "curData", "console", "log", "_canUsePr", "_currData", "_cfgData", "nameLab", "string", "name", "com", "getComponent", "updateItem", "GeneralNoThing", "_addPrObj", "force_added", "strategy_added", "defense_added", "speed_added", "destroy_added", "_curAll", "Math", "abs", "hasPrPoint", "usePrPoint", "updateView", "children", "srollLayout", "node", "i", "key", "getChildByName", "getPrStr", "level", "_step", "pr<PERSON><PERSON><PERSON>", "addPr", "active", "plus", "target", "index", "num", "isCanePlus", "reduce", "isCaneReduce", "getAllUse", "all", "onClickAddPr", "instance", "playClick", "getInstance", "addPrGeneral", "id"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACpDC,MAAAA,Y,iBAAAA,Y;;AAGFC,MAAAA,c;;AACiBC,MAAAA,W,iBAAAA,W;;AACjBC,MAAAA,gB;AAAoBC,MAAAA,e,iBAAAA,e;;;;;;;OAJrB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;yBAOTc,iB,WADpBF,OAAO,CAAC,mBAAD,C,UAGHC,QAAQ,CAACX,KAAD,C,UAGRW,QAAQ,CAACV,MAAD,C,UAGRU,QAAQ,CAACT,IAAD,C,UAGRS,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACX,KAAD,C,UAIRW,QAAQ,CAACT,IAAD,C,UAeRS,QAAQ,CAAC,CAACT,IAAD,CAAD,C,oCAlCb,MACqBU,iBADrB,SACgDb,SADhD,CAC0D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,6CAsBtB,IAtBsB;;AAAA,4CAuBrB,IAvBqB;;AAAA,gDAyB1B,IAzB0B;;AAAA,4CA0B/B,EA1B+B;;AAAA,6CA2B9B,EA3B8B;;AAAA,6CA4BzB,EA5ByB;;AAAA,6CA6B3B,CAAC,CA7B0B;;AAAA,yCA8B/B,GA9B+B;;AAAA,2CA+B3B,CA/B2B;;AAAA;AAAA;;AAoC5Cc,QAAAA,MAAM,GAAO;AACnB,eAAKC,YAAL,GAAoBV,WAAW,CAAC,KAAKW,iBAAN,CAA/B;AACA,eAAKD,YAAL,CAAkBE,MAAlB,GAA2B,KAAKC,iBAAhC;AAEA,eAAKC,QAAL,GAAgB;AACZC,YAAAA,KAAK,EAAC,IADM;AAEZC,YAAAA,QAAQ,EAAC,IAFG;AAGZC,YAAAA,OAAO,EAAC,IAHI;AAIZC,YAAAA,KAAK,EAAC,IAJM;AAKZC,YAAAA,OAAO,EAAC;AALI,WAAhB;AAQA,eAAKC,SAAL,GAAiB,CAAC,OAAD,EAAS,UAAT,EAAoB,SAApB,EAA8B,OAA9B,EAAsC,SAAtC,CAAjB;AACH;;AAGMC,QAAAA,OAAO,CAACC,OAAD,EAAaC,OAAb,EAA8B;AACxCC,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAuBF,OAAvB;AACA,eAAKG,SAAL,GAAgB,CAAC,CAAjB;AACA,eAAKC,SAAL,GAAiBJ,OAAjB;AACA,eAAKK,QAAL,GAAgBN,OAAhB;AACA,eAAKO,OAAL,CAAaC,MAAb,GAAsB,KAAKF,QAAL,CAAcG,IAApC;;AAEA,cAAIC,GAAG,GAAG,KAAKtB,YAAL,CAAkBuB,YAAlB;AAAA;AAAA,mDAAV;;AACA,cAAGD,GAAH,EAAO;AACHA,YAAAA,GAAG,CAACE,UAAJ,CAAe,KAAKP,SAApB,EAA+B;AAAA;AAAA,oDAAgBQ,cAA/C;AACH;;AAED,eAAKC,SAAL,GAAiB;AACbrB,YAAAA,KAAK,EAAC,KAAKY,SAAL,CAAeU,WADR;AAEbrB,YAAAA,QAAQ,EAAC,KAAKW,SAAL,CAAeW,cAFX;AAGbrB,YAAAA,OAAO,EAAC,KAAKU,SAAL,CAAeY,aAHV;AAIbrB,YAAAA,KAAK,EAAC,KAAKS,SAAL,CAAea,WAJR;AAKbrB,YAAAA,OAAO,EAAC,KAAKQ,SAAL,CAAec;AALV,WAAjB;AAQA,eAAKC,OAAL,GAAeC,IAAI,CAACC,GAAL,CAAS,KAAKjB,SAAL,CAAekB,UAAf,GAA4B,KAAKlB,SAAL,CAAemB,UAApD,CAAf;AACA,eAAKC,UAAL;AAGH;;AAIUA,QAAAA,UAAU,GAAO;AACxB,cAAIC,QAAQ,GAAG,KAAKC,WAAL,CAAiBC,IAAjB,CAAsBF,QAArC;AACA,cAAIG,CAAC,GAAG,CAAR;;AACA,eAAI,IAAIC,GAAR,IAAe,KAAKtC,QAApB,EAA6B;AACzBkC,YAAAA,QAAQ,CAACG,CAAD,CAAR,CAAYE,cAAZ,CAA2B,WAA3B,EAAwCpB,YAAxC,CAAqDrC,KAArD,EAA4DkC,MAA5D,GAAqE,KAAKhB,QAAL,CAAcsC,GAAd,IAAoB,GAApB,GACrE;AAAA;AAAA,4CAAYE,QAAZ,CAAqB,KAAK1B,QAAL,CAAcwB,GAAd,CAArB,EAAwC,KAAKhB,SAAL,CAAegB,GAAf,CAAxC,EAA4D,KAAKzB,SAAL,CAAe4B,KAA3E,EAAiF,KAAK3B,QAAL,CAAcwB,GAAG,GAAC,OAAlB,CAAjF,CADA;AAGA,gBAAIF,IAAU,GAAGF,QAAQ,CAACG,CAAD,CAAR,CAAYE,cAAZ,CAA2B,YAA3B,EAAyCA,cAAzC,CAAwD,cAAxD,EAAwEpB,YAAxE,CAAqFrC,KAArF,CAAjB;AACAsD,YAAAA,IAAI,CAACpB,MAAL,GAAc,KAAKM,SAAL,CAAegB,GAAf,IAAoB,KAAKI,KAAzB,GAAgC,EAA9C;AACAL,YAAAA,CAAC;AACJ;;AAID,cAAG,KAAKzB,SAAL,IAAkB,CAAC,CAAtB,EAAwB;AACpB,iBAAKA,SAAL,GAAiBiB,IAAI,CAACC,GAAL,CAAS,KAAKjB,SAAL,CAAekB,UAAf,GAA4B,KAAKlB,SAAL,CAAemB,UAApD,CAAjB;AACH;;AACD,eAAKW,OAAL,CAAa3B,MAAb,GAAsB,WAAW,KAAKJ,SAAL,GAAe,KAAK8B,KAA/B,GAAuC,GAAvC,GAA6C,KAAK7B,SAAL,CAAekB,UAAf,GAA0B,KAAKW,KAAlG;AACA,eAAKE,KAAL,CAAWC,MAAX,GAAoB,KAAKhC,SAAL,CAAekB,UAAf,GAA4B,CAA5B,GAA8B,IAA9B,GAAmC,KAAvD;AACH;;AAESe,QAAAA,IAAI,CAACC,MAAD,EAAYC,KAAZ,EAAkC;AAAA,cAAtBA,KAAsB;AAAtBA,YAAAA,KAAsB,GAAP,CAAO;AAAA;;AAC5C,cAAIC,GAAU,GAAG,KAAK3B,SAAL,CAAe,KAAKhB,SAAL,CAAe0C,KAAf,CAAf,CAAjB;;AACA,cAAG,CAAC,KAAKE,UAAL,EAAD,IAAsBD,GAAG,IAAI,KAAKpC,SAAL,CAAekB,UAA/C,EAA0D;AACtD;AACH;;AAEDkB,UAAAA,GAAG,GAAGA,GAAG,GAAG,KAAKP,KAAjB;AACAO,UAAAA,GAAG,GAAGA,GAAG,GAAG,KAAKpC,SAAL,CAAekB,UAArB,GAAgC,KAAKlB,SAAL,CAAekB,UAA/C,GAA0DkB,GAAhE;AACA,eAAK3B,SAAL,CAAe,KAAKhB,SAAL,CAAe0C,KAAf,CAAf,IAAwCC,GAAxC;AACA,eAAKrC,SAAL,IAAkB,KAAK8B,KAAvB;AACA,eAAKT,UAAL;AACH;;AAGSkB,QAAAA,MAAM,CAACJ,MAAD,EAAYC,KAAZ,EAAkC;AAAA,cAAtBA,KAAsB;AAAtBA,YAAAA,KAAsB,GAAP,CAAO;AAAA;;AAC9C,cAAIC,GAAU,GAAG,KAAK3B,SAAL,CAAe,KAAKhB,SAAL,CAAe0C,KAAf,CAAf,CAAjB;;AACA,cAAG,CAAC,KAAKI,YAAL,EAAD,IAAwBH,GAAG,IAAI,CAAlC,EAAoC;AAChC;AACH;;AAEDA,UAAAA,GAAG,GAAGA,GAAG,GAAG,KAAKP,KAAjB;AACAO,UAAAA,GAAG,GAAGA,GAAG,GAAG,CAAN,GAAQ,CAAR,GAAUA,GAAhB;AACA,eAAK3B,SAAL,CAAe,KAAKhB,SAAL,CAAe0C,KAAf,CAAf,IAAwCC,GAAxC;AACA,eAAKrC,SAAL,IAAkB,KAAK8B,KAAvB;AAEA,eAAKT,UAAL;AACH;;AAGOoB,QAAAA,SAAS,GAAS;AACtB,cAAIJ,GAAU,GAAG,CAAjB;;AACA,eAAI,IAAIX,GAAR,IAAe,KAAKhB,SAApB,EAA8B;AAC1B2B,YAAAA,GAAG,IAAG,KAAK3B,SAAL,CAAegB,GAAf,CAAN;AACH;;AACD,iBAAOW,GAAP;AACH;;AAGOC,QAAAA,UAAU,GAAU;AACxB,cAAII,GAAU,GAAG,KAAKD,SAAL,EAAjB;;AACA,cAAGC,GAAG,GAAG,KAAKZ,KAAX,GAAmB,KAAK7B,SAAL,CAAekB,UAArC,EAAgD;AAC5C,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAIOqB,QAAAA,YAAY,GAAU;AAC1B,cAAIE,GAAU,GAAG,KAAKD,SAAL,EAAjB;;AACA,cAAGC,GAAG,GAAG,KAAKZ,KAAX,GAAmB,CAAtB,EAAwB;AACpB,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAGSa,QAAAA,YAAY,GAAO;AACzB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,gDAAeC,WAAf,GAA6BC,YAA7B,CACI,KAAK9C,SAAL,CAAe+C,EADnB,EAEI,KAAKtC,SAAL,CAAerB,KAFnB,EAGI,KAAKqB,SAAL,CAAepB,QAHnB,EAII,KAAKoB,SAAL,CAAenB,OAJnB,EAKI,KAAKmB,SAAL,CAAelB,KALnB,EAMI,KAAKkB,SAAL,CAAejB,OANnB;AAOH;;AAvKqD,O;;;;;iBAGrC,I;;;;;;;iBAGW,I;;;;;;;iBAGF,I;;;;;;;iBAGL,I;;;;;;;iBAGJ,I;;;;;;;iBAIH,I;;;;;;;iBAeI,E", "sourcesContent": ["\nimport { _decorator, Component, Label, Prefab, Node, Layout, instantiate } from 'cc';\nimport { AudioManager } from '../../common/AudioManager';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport { GeneralConfig, GeneralData } from \"../../general/GeneralProxy\";\nimport GeneralItemLogic, { GeneralItemType } from \"./GeneralItemLogic\";\n\n@ccclass('GeneralAddPrLogic')\nexport default class GeneralAddPrLogic  extends Component {\n\n    @property(Label)\n    nameLab: Label = null;\n\n    @property(Prefab)\n    generalItemPrefab: Prefab = null;\n\n    @property(Node)\n    generalItemParent: Node = null;\n\n    @property(Layout)\n    srollLayout:Layout = null;\n\n    @property(Label)\n    prLabel: Label = null;\n\n\n    @property(Node)\n    addPr: Node = null;\n\n\n    private _currData:GeneralData = null;\n    private _cfgData:GeneralConfig = null;\n\n    private _generalNode:Node = null;\n    private _nameObj:any = {};\n    private _addPrObj:any = {};\n    private _addPrArr:string[] = [];\n    private _canUsePr:number = -1;\n    private _step:number = 100;\n    protected _curAll:number = 0;\n\n    @property([Node])\n    prItems: Node[] = [];\n\n    protected onLoad():void{\n        this._generalNode = instantiate(this.generalItemPrefab);\n        this._generalNode.parent = this.generalItemParent;\n\n        this._nameObj = {\n            force:\"武力\",\n            strategy:\"战略\",\n            defense:\"防御\",\n            speed:\"速度\",\n            destroy:\"破坏\",\n        };\n\n        this._addPrArr = [\"force\",\"strategy\",\"defense\",\"speed\",\"destroy\"]\n    }\n\n\n    public setData(cfgData:any,curData:any):void{\n        console.log(\"curData:\",curData)\n        this._canUsePr =-1;\n        this._currData = curData;\n        this._cfgData = cfgData;\n        this.nameLab.string = this._cfgData.name;\n        \n        var com = this._generalNode.getComponent(GeneralItemLogic);\n        if(com){\n            com.updateItem(this._currData, GeneralItemType.GeneralNoThing);\n        }\n\n        this._addPrObj = {\n            force:this._currData.force_added,\n            strategy:this._currData.strategy_added,\n            defense:this._currData.defense_added,\n            speed:this._currData.speed_added,\n            destroy:this._currData.destroy_added,\n        };\n\n        this._curAll = Math.abs(this._currData.hasPrPoint - this._currData.usePrPoint);\n        this.updateView();\n\n\n    }\n\n\n\n    protected  updateView():void{\n        var children = this.srollLayout.node.children;\n        var i = 0;\n        for(var key in this._nameObj){\n            children[i].getChildByName(\"New Label\").getComponent(Label).string = this._nameObj[key] +\":\" + \n            GeneralData.getPrStr(this._cfgData[key],this._addPrObj[key],this._currData.level,this._cfgData[key+\"_grow\"]);\n\n            var node:Label = children[i].getChildByName(\"New Sprite\").getChildByName(\"change Label\").getComponent(Label);\n            node.string = this._addPrObj[key]/this._step +''\n            i++;\n        }\n        \n        \n\n        if(this._canUsePr == -1){\n            this._canUsePr = Math.abs(this._currData.hasPrPoint - this._currData.usePrPoint);\n        }\n        this.prLabel.string = \"可用属性点:\" + this._canUsePr/this._step + \"/\" + this._currData.hasPrPoint/this._step;\n        this.addPr.active = this._currData.hasPrPoint > 0?true:false;\n    }\n\n    protected plus(target:any,index:number = 0):void{\n        var num:number = this._addPrObj[this._addPrArr[index]]\n        if(!this.isCanePlus() || num >= this._currData.hasPrPoint){\n            return\n        }\n\n        num = num + this._step;\n        num = num > this._currData.hasPrPoint?this._currData.hasPrPoint:num;\n        this._addPrObj[this._addPrArr[index]] = num;\n        this._canUsePr -= this._step\n        this.updateView();\n    }\n\n\n    protected reduce(target:any,index:number = 0):void{\n        var num:number = this._addPrObj[this._addPrArr[index]]\n        if(!this.isCaneReduce() || num == 0){\n            return\n        }\n        \n        num = num - this._step;\n        num = num < 0?0:num;\n        this._addPrObj[this._addPrArr[index]] = num;\n        this._canUsePr += this._step\n       \n        this.updateView();\n    }\n\n\n    private getAllUse():number{\n        var num:number = 0;\n        for(var key in this._addPrObj){\n            num +=this._addPrObj[key];\n        }\n        return num\n    }\n\n\n    private isCanePlus():boolean{\n        var all:number = this.getAllUse();\n        if(all + this._step > this._currData.hasPrPoint){\n            return false;\n        }\n        return true;\n    }\n\n\n\n    private isCaneReduce():boolean{\n        var all:number = this.getAllUse();\n        if(all - this._step < 0){\n            return false;\n        }\n        return true;\n    }\n\n\n    protected onClickAddPr():void{\n        AudioManager.instance.playClick();\n        GeneralCommand.getInstance().addPrGeneral(\n            this._currData.id, \n            this._addPrObj.force, \n            this._addPrObj.strategy, \n            this._addPrObj.defense, \n            this._addPrObj.speed, \n            this._addPrObj.destroy);\n    }\n\n}\n"]}