{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts"], "names": ["_decorator", "Component", "Label", "UnionCommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "UnionMemItemLogic", "onLoad", "updateItem", "data", "_menberData", "titleLabel", "string", "titleDes", "name<PERSON><PERSON><PERSON>", "name", "posLabel", "x", "y", "click", "instance", "playClick", "emit", "clickUnionMemberItem", "kick", "getInstance", "unionKick", "rid", "appoint", "abdicate", "jump", "closeUnion", "scrollToMap"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AAIzBC,MAAAA,Y;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAPH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAUTS,iB,WADpBF,OAAO,CAAC,mBAAD,C,UAGHC,QAAQ,CAACN,KAAD,C,UAGRM,QAAQ,CAACN,KAAD,C,UAGRM,QAAQ,CAACN,KAAD,C,oCATb,MACqBO,iBADrB,SAC+CR,SAD/C,CACyD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,+CAWtB,IAXsB;AAAA;;AAa3CS,QAAAA,MAAM,GAAO,CACtB;;AAESC,QAAAA,UAAU,CAACC,IAAD,EAAkB;AAClC,eAAKC,WAAL,GAAmBD,IAAnB;AACA,eAAKE,UAAL,CAAgBC,MAAhB,GAAyB,MAAM,KAAKF,WAAL,CAAiBG,QAAvB,GAAkC,GAA3D;AACA,eAAKC,SAAL,CAAeF,MAAf,GAAwB,KAAKF,WAAL,CAAiBK,IAAzC;AACA,eAAKC,QAAL,CAAcJ,MAAd,GAAuB,SAAS,KAAKF,WAAL,CAAiBO,CAA1B,GAA8B,GAA9B,GAAoC,KAAKP,WAAL,CAAiBQ,CAArD,GAAuD,GAA9E;AACH;;AAESC,QAAAA,KAAK,GAAO;AAClB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,oBAAzB,EAA+C,KAAKb,WAApD;AACH;;AAESc,QAAAA,IAAI,GAAO;AACjB;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaI,WAAb,GAA2BC,SAA3B,CAAqC,KAAKhB,WAAL,CAAiBiB,GAAtD;AACH;;AAGSC,QAAAA,OAAO,GAAO;AACpB;AAAA;AAAA,4CAAaR,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaI,WAAb,GAA2BC,SAA3B,CAAqC,KAAKhB,WAAL,CAAiBiB,GAAtD;AACH;;AAESE,QAAAA,QAAQ,GAAO;AACrB;AAAA;AAAA,4CAAaT,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaI,WAAb,GAA2BC,SAA3B,CAAqC,KAAKhB,WAAL,CAAiBiB,GAAtD;AACH;;AAESG,QAAAA,IAAI,GAAO;AACjB;AAAA;AAAA,4CAAaV,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWS,UAAzB;AACA;AAAA;AAAA,oCAAST,IAAT,CAAc;AAAA;AAAA,wCAAWU,WAAzB,EAAsC,KAAKtB,WAAL,CAAiBO,CAAvD,EAA0D,KAAKP,WAAL,CAAiBQ,CAA3E;AACH;;AAhDoD,O;;;;;iBAGlC,I;;;;;;;iBAGC,I;;;;;;;iBAGF,I", "sourcesContent": ["// // Learn TypeScript:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html\n// // Learn Attribute:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html\n// // Learn life-cycle callbacks:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html\n\nimport { _decorator, Component, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\n\nimport UnionCommand from \"./UnionCommand\";\nimport { Member } from \"./UnionProxy\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('UnionMemItemLogic')\nexport default class UnionMemItemLogic extends Component {\n\n    @property(Label)\n    nameLabel: Label = null;\n\n    @property(Label)\n    titleLabel: Label = null;\n\n    @property(Label)\n    posLabel: Label = null;\n\n    protected _menberData:Member = null;\n\n    protected onLoad():void{\n    }\n\n    protected updateItem(data:Member):void{\n        this._menberData = data;\n        this.titleLabel.string = \"(\" + this._menberData.titleDes + \")\";\n        this.nameLabel.string = this._menberData.name;\n        this.posLabel.string = \"坐标:(\" + this._menberData.x + \",\" + this._menberData.y+\")\";\n    }\n\n    protected click():void{\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.clickUnionMemberItem, this._menberData);\n    }\n\n    protected kick():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionKick(this._menberData.rid);\n    }\n\n    \n    protected appoint():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionKick(this._menberData.rid);\n    }\n\n    protected abdicate():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionKick(this._menberData.rid);\n    }\n    \n    protected jump():void{\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.closeUnion);\n        EventMgr.emit(LogicEvent.scrollToMap, this._menberData.x, this._menberData.y);\n    }\n\n}\n"]}