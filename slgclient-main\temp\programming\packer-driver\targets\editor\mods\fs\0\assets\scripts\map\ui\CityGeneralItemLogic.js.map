{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts"], "names": ["_decorator", "Component", "Node", "Label", "Sprite", "ProgressBar", "ArmyCommand", "GeneralCommand", "GeneralCampType", "DateUtil", "GeneralHeadLogic", "MapUICommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "CityGeneralItemLogic", "onLoad", "schedule", "updateCon", "onEnable", "conBg", "active", "onDestroy", "_data", "onClickDown", "instance", "playClick", "getInstance", "generalDispose", "_cityId", "id", "order", "onClickItem", "cfg", "proxy", "getGeneralCfg", "cfgId", "emit", "openGeneralDes", "addNode", "general<PERSON>rr", "getAllGenerals", "openGeneralChoose", "index", "cityArmyData", "getArmyList", "general", "arr", "i", "length", "concat", "generals", "j", "getMyGeneral", "getGeneralIds", "updateItem", "labelTitle", "string", "_isUnlock", "infoNode", "lockNode", "btnDown", "desName", "getFacilityCfgByType", "name", "labelTip", "_order", "headIcon", "getComponent", "setHeadId", "labelLv", "level", "labelName", "labelSoldierCnt", "_soldierCnt", "_totalSoldierCnt", "progressBar", "progress", "labelCost", "cost", "config", "camp", "Han", "labelCamp", "<PERSON><PERSON>", "<PERSON>", "Shu", "<PERSON>", "isAfterServerTime", "_conTime", "labelConTime", "labelConCnt", "leftTimeStr", "_conCnt", "setData", "cityId", "data", "soldierCnt", "totalSoldierCnt", "conCnt", "conTime", "isUnlock"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAG9CC,MAAAA,W;;AAEAC,MAAAA,c;;AACEC,MAAAA,e,iBAAAA,e;;AACFC,MAAAA,Q;;AACAC,MAAAA,gB;;AACAC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;OAXH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;yBAcTiB,oB,WADpBF,OAAO,CAAC,sBAAD,C,UAEHC,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACb,KAAD,C,UAERa,QAAQ,CAACZ,MAAD,C,UAERY,QAAQ,CAACb,KAAD,C,UAERa,QAAQ,CAACb,KAAD,C,WAERa,QAAQ,CAACb,KAAD,C,WAERa,QAAQ,CAACb,KAAD,C,WAERa,QAAQ,CAACb,KAAD,C,WAERa,QAAQ,CAACb,KAAD,C,WAERa,QAAQ,CAACb,KAAD,C,WAERa,QAAQ,CAACb,KAAD,C,WAERa,QAAQ,CAACb,KAAD,C,WAERa,QAAQ,CAACX,WAAD,C,WAERW,QAAQ,CAACd,IAAD,C,oCAlCb,MACqBe,oBADrB,SACkDhB,SADlD,CAC4D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAoCjC,CApCiC;;AAAA,0CAqC7B,CArC6B;;AAAA,2CAsC5B,CAtC4B;;AAAA,yCAuCzB,IAvCyB;;AAAA,+CAwCxB,CAxCwB;;AAAA,oDAyCnB,CAzCmB;;AAAA,2CA0C5B,CA1C4B;;AAAA,4CA2C3B,CA3C2B;;AAAA,6CA4CzB,KA5CyB;AAAA;;AA8C9CiB,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL,CAAc,KAAKC,SAAnB,EAA8B,GAA9B;AACH;;AAESC,QAAAA,QAAQ,GAAQ;AACtB,eAAKC,KAAL,CAAWC,MAAX,GAAoB,KAApB;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKC,KAAL,GAAa,IAAb;AACH;;AAESC,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,0CAAYC,WAAZ,GAA0BC,cAA1B,CAAyC,KAAKC,OAA9C,EAAuD,KAAKN,KAAL,CAAWO,EAAlE,EAAsE,KAAKP,KAAL,CAAWQ,KAAjF,EAAwF,CAAC,CAAzF,EAA4F,IAA5F;AACH;;AAESC,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaP,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKH,KAAT,EAAgB;AACZ;AACA,gBAAIU,GAAkB,GAAG;AAAA;AAAA,kDAAeN,WAAf,GAA6BO,KAA7B,CAAmCC,aAAnC,CAAiD,KAAKZ,KAAL,CAAWa,KAA5D,CAAzB;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,cAAzB,EAAyCL,GAAzC,EAA8C,KAAKV,KAAnD;AACH,WAJD,MAIO,IAAI,KAAKgB,OAAL,CAAalB,MAAjB,EAAyB;AAC5B;AACA,gBAAImB,UAAoB,GAAG,KAAKC,cAAL,EAA3B;AACA;AAAA;AAAA,sCAASJ,IAAT,CAAc;AAAA;AAAA,0CAAWK,iBAAzB,EAA4CF,UAA5C,EAAwD,KAAKG,KAA7D;AACH;AACJ;;AAESF,QAAAA,cAAc,GAAa;AACjC,cAAIG,YAAwB,GAAG;AAAA;AAAA,0CAAYjB,WAAZ,GAA0BO,KAA1B,CAAgCW,WAAhC,CAA4C,KAAKhB,OAAjD,CAA/B;AACA,cAAIiB,OAAoB,GAAG,IAA3B;AACA,cAAIC,GAAG,GAAG,EAAV;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,YAAY,CAACK,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1C,gBAAIJ,YAAY,CAACI,CAAD,CAAhB,EAAqB;AACjBD,cAAAA,GAAG,GAAGA,GAAG,CAACG,MAAJ,CAAWN,YAAY,CAACI,CAAD,CAAZ,CAAgBG,QAA3B,CAAN;;AACA,mBAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGR,YAAY,CAACI,CAAD,CAAZ,CAAgBG,QAAhB,CAAyBF,MAArD,EAA6DG,CAAC,EAA9D,EAAkE;AAC9D,oBAAIR,YAAY,CAACI,CAAD,CAAZ,CAAgBG,QAAhB,CAAyBC,CAAzB,IAA8B,CAAlC,EAAqC;AACjCN,kBAAAA,OAAO,GAAG;AAAA;AAAA,wDAAenB,WAAf,GAA6BO,KAA7B,CAAmCmB,YAAnC,CAAgDT,YAAY,CAACI,CAAD,CAAZ,CAAgBG,QAAhB,CAAyBC,CAAzB,CAAhD,CAAV;;AACA,sBAAIN,OAAJ,EAAa;AACTC,oBAAAA,GAAG,GAAGA,GAAG,CAACG,MAAJ,CAAW;AAAA;AAAA,0DAAevB,WAAf,GAA6BO,KAA7B,CAAmCoB,aAAnC,CAAiDR,OAAO,CAACV,KAAzD,CAAX,CAAN;AACH;AACJ;AACJ;AACJ;AACJ;;AACD,iBAAOW,GAAP;AACH;;AAESQ,QAAAA,UAAU,GAAS;AACzB,cAAI,KAAKZ,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAKa,UAAL,CAAgBC,MAAhB,GAAyB,IAAzB;AACH,WAFD,MAEO;AACH,iBAAKD,UAAL,CAAgBC,MAAhB,GAAyB,IAAzB;AACH;;AACD,cAAI,KAAKC,SAAL,IAAkB,KAAtB,EAA6B;AACzB;AACA,iBAAKC,QAAL,CAActC,MAAd,GAAuB,KAAvB;AACA,iBAAKkB,OAAL,CAAalB,MAAb,GAAsB,KAAtB;AACA,iBAAKuC,QAAL,CAAcvC,MAAd,GAAuB,IAAvB;AACA,iBAAKwC,OAAL,CAAaxC,MAAb,GAAsB,KAAtB;AACA,gBAAIyC,OAAe,GAAG;AAAA;AAAA,8CAAanC,WAAb,GAA2BO,KAA3B,CAAiC6B,oBAAjC,CAAsD,EAAtD,EAA0DC,IAAhF;AACA,iBAAKC,QAAL,CAAcR,MAAd,GAAuBK,OAAO,GAAG,KAAV,GAAkB,KAAKI,MAAvB,GAAgC,IAAvD;AACA,iBAAK9C,KAAL,CAAWC,MAAX,GAAoB,KAApB;AACH,WATD,MASO,IAAI,KAAKE,KAAL,IAAc,IAAlB,EAAwB;AAC3B;AACA,iBAAKoC,QAAL,CAActC,MAAd,GAAuB,KAAvB;AACA,iBAAKkB,OAAL,CAAalB,MAAb,GAAsB,IAAtB;AACA,iBAAKuC,QAAL,CAAcvC,MAAd,GAAuB,KAAvB;AACA,iBAAKwC,OAAL,CAAaxC,MAAb,GAAsB,KAAtB;AACA,iBAAKD,KAAL,CAAWC,MAAX,GAAoB,KAApB;AAEH,WARM,MAQA;AACH;AACA,iBAAKsC,QAAL,CAActC,MAAd,GAAuB,IAAvB;AACA,iBAAKkB,OAAL,CAAalB,MAAb,GAAsB,KAAtB;AACA,iBAAKuC,QAAL,CAAcvC,MAAd,GAAuB,KAAvB;AACA,iBAAKwC,OAAL,CAAaxC,MAAb,GAAsB,IAAtB;AAEA,iBAAKH,SAAL;AAEA,gBAAIe,GAAkB,GAAG;AAAA;AAAA,kDAAeN,WAAf,GAA6BO,KAA7B,CAAmCC,aAAnC,CAAiD,KAAKZ,KAAL,CAAWa,KAA5D,CAAzB;AACA,iBAAK+B,QAAL,CAAcC,YAAd;AAAA;AAAA,sDAA6CC,SAA7C,CAAuD,KAAK9C,KAAL,CAAWa,KAAlE;AACA,iBAAKkC,OAAL,CAAab,MAAb,GAAsB,KAAKlC,KAAL,CAAWgD,KAAX,GAAmB,EAAzC;AACA,iBAAKC,SAAL,CAAef,MAAf,GAAwBxB,GAAG,CAAC+B,IAA5B;AACA,iBAAKS,eAAL,CAAqBhB,MAArB,GAA8B,KAAKiB,WAAL,GAAmB,GAAnB,GAAyB,KAAKC,gBAA5D;AACA,iBAAKC,WAAL,CAAiBC,QAAjB,GAA4B,KAAKH,WAAL,GAAmB,KAAKC,gBAApD;AACA,iBAAKG,SAAL,CAAerB,MAAf,GAAwB,UAAUxB,GAAG,CAAC8C,IAAtC;;AACA,oBAAQ,KAAKxD,KAAL,CAAWyD,MAAX,CAAkBC,IAA1B;AACI,mBAAK;AAAA;AAAA,sDAAgBC,GAArB;AACI,qBAAKC,SAAL,CAAe1B,MAAf,GAAwB,GAAxB;AACA;;AACJ,mBAAK;AAAA;AAAA,sDAAgB2B,GAArB;AACI,qBAAKD,SAAL,CAAe1B,MAAf,GAAwB,GAAxB;AACA;;AACJ,mBAAK;AAAA;AAAA,sDAAgB4B,GAArB;AACI,qBAAKF,SAAL,CAAe1B,MAAf,GAAwB,GAAxB;AACA;;AACJ,mBAAK;AAAA;AAAA,sDAAgB6B,GAArB;AACI,qBAAKH,SAAL,CAAe1B,MAAf,GAAwB,GAAxB;AACA;;AACJ,mBAAK;AAAA;AAAA,sDAAgB8B,EAArB;AACI,qBAAKJ,SAAL,CAAe1B,MAAf,GAAwB,GAAxB;AACA;;AACJ;AACI,qBAAK0B,SAAL,CAAe1B,MAAf,GAAwB,GAAxB;AACA;AAlBR;AAqBH;AACJ;;AAESvC,QAAAA,SAAS,GAAE;AACjB,cAAI;AAAA;AAAA,oCAASsE,iBAAT,CAA2B,KAAKC,QAAL,GAAc,IAAzC,CAAJ,EAAmD;AAC/C,iBAAKrE,KAAL,CAAWC,MAAX,GAAoB,KAApB;AACA,iBAAKqE,YAAL,CAAkBjC,MAAlB,GAA2B,EAA3B;AACA,iBAAKkC,WAAL,CAAiBlC,MAAjB,GAA0B,EAA1B;AACH,WAJD,MAIK;AACD,iBAAKrC,KAAL,CAAWC,MAAX,GAAoB,IAApB;AACA,iBAAKqE,YAAL,CAAkBjC,MAAlB,GAA2B;AAAA;AAAA,sCAASmC,WAAT,CAAqB,KAAKH,QAAL,GAAc,IAAnC,CAA3B;AACA,iBAAKE,WAAL,CAAiBlC,MAAjB,GAA0B,MAAM,KAAKoC,OAArC;AACH;AACJ;;AAEMC,QAAAA,OAAO,CAACC,MAAD,EAAiBhE,KAAjB,EAAgCiE,IAAhC,EACVC,UADU,EACUC,eADV,EACmCC,MADnC,EAEVC,OAFU,EAEMC,QAFN,EAE+B;AACzC,eAAKxE,OAAL,GAAekE,MAAf;AACA,eAAK7B,MAAL,GAAcnC,KAAd;AACA,eAAKR,KAAL,GAAayE,IAAb;AACA,eAAKtB,WAAL,GAAmBuB,UAAnB;AACA,eAAKtB,gBAAL,GAAwBuB,eAAxB;AACA,eAAKL,OAAL,GAAeM,MAAf;AACA,eAAKV,QAAL,GAAgBW,OAAhB;AACA,eAAK1C,SAAL,GAAiB2C,QAAjB;AACA,eAAK9C,UAAL;AACH;;AAvLuD,O;;;;;iBAEvC,I;;;;;;;iBAED,I;;;;;;;iBAEC,I;;;;;;;iBAED,I;;;;;;;iBAEI,I;;;;;;;iBAED,I;;;;;;;iBAEF,I;;;;;;;iBAEE,I;;;;;;;iBAEA,I;;;;;;;iBAEM,I;;;;;;;iBAEN,I;;;;;;;iBAEA,I;;;;;;;iBAED,I;;;;;;;iBAEI,I;;;;;;;iBAED,I;;;;;;;iBAEM,I;;;;;;;iBAEb,I", "sourcesContent": ["import { _decorator, Component, Node, Label, Sprite, ProgressBar } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport { ArmyData } from \"../../general/ArmyProxy\";\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport { GeneralCampType, GeneralConfig, GeneralData } from \"../../general/GeneralProxy\";\nimport DateUtil from \"../../utils/DateUtil\";\nimport GeneralHeadLogic from \"./GeneralHeadLogic\";\nimport MapUICommand from \"./MapUICommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('CityGeneralItemLogic')\nexport default class CityGeneralItemLogic extends Component {\n    @property(Node)\n    infoNode: Node = null;\n    @property(Node)\n    addNode: Node = null;\n    @property(Node)\n    lockNode: Node = null;\n    @property(Node)\n    btnDown: Node = null;\n    @property(Label)\n    labelTitle: Label = null;\n    @property(Sprite)\n    headIcon: Sprite = null;\n    @property(Label)\n    labelLv: Label = null;\n    @property(Label)\n    labelName: Label = null;\n    @property(Label)\n    labelArms: Label = null;\n    @property(Label)\n    labelSoldierCnt: Label = null;\n    @property(Label)\n    labelCost: Label = null;\n    @property(Label)\n    labelCamp: Label = null;\n    @property(Label)\n    labelTip: Label = null;\n    @property(Label)\n    labelConTime: Label = null;\n    @property(Label)\n    labelConCnt: Label = null;\n    @property(ProgressBar)\n    progressBar: ProgressBar = null;\n    @property(Node)\n    conBg: Node = null;\n\n    public index: number = 0;\n    protected _order: number = 0;\n    protected _cityId: number = 0;\n    protected _data: GeneralData = null;\n    protected _soldierCnt: number = 0;\n    protected _totalSoldierCnt: number = 0;\n    protected _conCnt: number = 0;\n    protected _conTime: number = 0;\n    protected _isUnlock: boolean = false;\n\n    protected onLoad(): void {\n        this.schedule(this.updateCon, 1.0);\n    }\n\n    protected onEnable(): void{\n        this.conBg.active = false;\n    }\n\n    protected onDestroy(): void {\n        this._data = null;\n    }\n\n    protected onClickDown(): void {\n        AudioManager.instance.playClick();\n        ArmyCommand.getInstance().generalDispose(this._cityId, this._data.id, this._data.order, -1, null);\n    }\n\n    protected onClickItem(): void {\n        AudioManager.instance.playClick();\n        if (this._data) {\n            //点击展示武将信息\n            let cfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(this._data.cfgId);\n            EventMgr.emit(LogicEvent.openGeneralDes, cfg, this._data);\n        } else if (this.addNode.active) {\n            //上阵武将\n            var generalArr: number[] = this.getAllGenerals();\n            EventMgr.emit(LogicEvent.openGeneralChoose, generalArr, this.index);\n        }\n    }\n\n    protected getAllGenerals(): number[] {\n        let cityArmyData: ArmyData[] = ArmyCommand.getInstance().proxy.getArmyList(this._cityId);\n        let general: GeneralData = null;\n        var arr = [];\n        for (var i = 0; i < cityArmyData.length; i++) {\n            if (cityArmyData[i]) {\n                arr = arr.concat(cityArmyData[i].generals);\n                for (let j: number = 0; j < cityArmyData[i].generals.length; j++) {\n                    if (cityArmyData[i].generals[j] > 0) {\n                        general = GeneralCommand.getInstance().proxy.getMyGeneral(cityArmyData[i].generals[j]);\n                        if (general) {\n                            arr = arr.concat(GeneralCommand.getInstance().proxy.getGeneralIds(general.cfgId));\n                        }\n                    }\n                }\n            }\n        }\n        return arr;\n    }\n\n    protected updateItem(): void {\n        if (this.index == 0) {\n            this.labelTitle.string = \"主将\"\n        } else {\n            this.labelTitle.string = \"副将\"\n        }\n        if (this._isUnlock == false) {\n            //未解锁\n            this.infoNode.active = false;\n            this.addNode.active = false;\n            this.lockNode.active = true;\n            this.btnDown.active = false;\n            let desName: string = MapUICommand.getInstance().proxy.getFacilityCfgByType(14).name;\n            this.labelTip.string = desName + \" 等级\" + this._order + \"开启\";\n            this.conBg.active = false;\n        } else if (this._data == null) {\n            //未配置武将\n            this.infoNode.active = false;\n            this.addNode.active = true;\n            this.lockNode.active = false;\n            this.btnDown.active = false;\n            this.conBg.active = false;\n            \n        } else {\n            //展示武将信息\n            this.infoNode.active = true;\n            this.addNode.active = false;\n            this.lockNode.active = false;\n            this.btnDown.active = true;\n            \n            this.updateCon();\n\n            let cfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(this._data.cfgId);\n            this.headIcon.getComponent(GeneralHeadLogic).setHeadId(this._data.cfgId);\n            this.labelLv.string = this._data.level + \"\";\n            this.labelName.string = cfg.name;\n            this.labelSoldierCnt.string = this._soldierCnt + \"/\" + this._totalSoldierCnt;\n            this.progressBar.progress = this._soldierCnt / this._totalSoldierCnt;\n            this.labelCost.string = \"Cost \" + cfg.cost;\n            switch (this._data.config.camp) {\n                case GeneralCampType.Han:\n                    this.labelCamp.string = \"汉\";\n                    break;\n                case GeneralCampType.Qun:\n                    this.labelCamp.string = \"群\";\n                    break;\n                case GeneralCampType.Wei:\n                    this.labelCamp.string = \"魏\";\n                    break;\n                case GeneralCampType.Shu:\n                    this.labelCamp.string = \"蜀\";\n                    break;\n                case GeneralCampType.Wu:\n                    this.labelCamp.string = \"吴\";\n                    break;\n                default:\n                    this.labelCamp.string = \"无\";\n                    break;\n            }\n\n        }\n    }\n\n    protected updateCon(){\n        if (DateUtil.isAfterServerTime(this._conTime*1000)){\n            this.conBg.active = false;\n            this.labelConTime.string = \"\";\n            this.labelConCnt.string = \"\";\n        }else{\n            this.conBg.active = true;\n            this.labelConTime.string = DateUtil.leftTimeStr(this._conTime*1000);\n            this.labelConCnt.string = \"+\" + this._conCnt;\n        }\n    }\n \n    public setData(cityId: number, order: number, data: GeneralData, \n        soldierCnt: number, totalSoldierCnt: number, conCnt:number, \n        conTime:number, isUnlock: boolean): void {\n        this._cityId = cityId;\n        this._order = order;\n        this._data = data;\n        this._soldierCnt = soldierCnt;\n        this._totalSoldierCnt = totalSoldierCnt;\n        this._conCnt = conCnt;\n        this._conTime = conTime;\n        this._isUnlock = isUnlock;\n        this.updateItem();\n    }\n}\n"]}