{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts"], "names": ["_decorator", "Component", "Toggle", "ScrollView", "Prefab", "instantiate", "ArmyCommand", "MapCommand", "RightArmyItemLogic", "RightCityItemLogic", "RightTagItemLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "RightInfoNodeLogic", "onLoad", "on", "updateArmyList", "onUpdateArmyList", "updateArmy", "onUpdateArmy", "updateTag", "onUpdateTag", "armyScrollView", "node", "active", "cityScrollView", "tagsScrollView", "initArmys", "initCitys", "initTags", "onDestroy", "targetOff", "_armys", "length", "cityId", "getInstance", "cityProxy", "getMyMainCity", "datas", "proxy", "getArmyList", "content", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "log", "i", "item", "armyItemPrefabs", "parent", "getComponent", "order", "setArmyData", "citys", "getMyCitys", "cityItemPrefabs", "tags", "getPosTags", "tag", "tagItemPrefabs", "setData", "data", "onClockToggle", "toggle", "instance", "playClick", "index", "toggles", "indexOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,W,OAAAA,W;;AAI3DC,MAAAA,W;;AACAC,MAAAA,U;;AACAC,MAAAA,kB;;AAEAC,MAAAA,kB;;AACAC,MAAAA,iB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAXH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;yBAcTgB,kB,WADpBF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAAC,CAACb,MAAD,CAAD,C,UAERa,QAAQ,CAACZ,UAAD,C,UAERY,QAAQ,CAACZ,UAAD,C,UAERY,QAAQ,CAACZ,UAAD,C,UAGRY,QAAQ,CAACX,MAAD,C,UAERW,QAAQ,CAACX,MAAD,C,UAGRW,QAAQ,CAACX,MAAD,C,oCAhBb,MACqBY,kBADrB,SACgDf,SADhD,CAC0D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,0CAkB3B,EAlB2B;AAAA;;AAqB5CgB,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,cAAvB,EAAuC,KAAKC,gBAA5C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,UAAvB,EAAmC,KAAKC,YAAxC,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,SAAvB,EAAkC,KAAKC,WAAvC,EAAoD,IAApD;AAEA,eAAKC,cAAL,CAAoBC,IAApB,CAAyBC,MAAzB,GAAkC,IAAlC;AACA,eAAKC,cAAL,CAAoBF,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACA,eAAKE,cAAL,CAAoBH,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACA,eAAKG,SAAL;AACA,eAAKC,SAAL;AACA,eAAKC,QAAL;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,eAAKC,MAAL,CAAYC,MAAZ,GAAqB,CAArB;AACA,eAAKD,MAAL,GAAc,IAAd;AACH;;AAESL,QAAAA,SAAS,GAAS;AACxB,cAAIO,MAAc,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,GAAmDH,MAAxE;AACA,cAAII,KAAiB,GAAG;AAAA;AAAA,0CAAYH,WAAZ,GAA0BI,KAA1B,CAAgCC,WAAhC,CAA4CN,MAA5C,CAAxB;AACA,eAAKZ,cAAL,CAAoBmB,OAApB,CAA4BC,iBAA5B;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBN,KAArB;;AACA,cAAIA,KAAJ,EAAW;AACP,iBAAKN,MAAL,CAAYC,MAAZ,GAAqBK,KAAK,CAACL,MAA3B;;AACA,iBAAK,IAAIY,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGP,KAAK,CAACL,MAAlC,EAA0CY,CAAC,EAA3C,EAA+C;AAC3C,kBAAIC,IAAU,GAAG5C,WAAW,CAAC,KAAK6C,eAAN,CAA5B;AACAD,cAAAA,IAAI,CAACE,MAAL,GAAc,KAAK1B,cAAL,CAAoBmB,OAAlC;AACA,mBAAKT,MAAL,CAAYa,CAAZ,IAAiBC,IAAjB;AACAA,cAAAA,IAAI,CAACG,YAAL;AAAA;AAAA,4DAAsCC,KAAtC,GAA8CL,CAAC,GAAG,CAAlD;AACAC,cAAAA,IAAI,CAACG,YAAL;AAAA;AAAA,4DAAsCE,WAAtC,CAAkDb,KAAK,CAACO,CAAD,CAAvD;AACH;AACJ;AACJ;;AAESjB,QAAAA,SAAS,GAAQ;AACvB,cAAIwB,KAAoB,GAAG;AAAA;AAAA,wCAAWjB,WAAX,GAAyBC,SAAzB,CAAmCiB,UAAnC,EAA3B;AACA,eAAK5B,cAAL,CAAoBgB,OAApB,CAA4BC,iBAA5B;;AACA,cAAIU,KAAK,IAAIA,KAAK,CAACnB,MAAN,GAAe,CAA5B,EAA+B;AAC3B,iBAAK,IAAIY,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGO,KAAK,CAACnB,MAAlC,EAA0CY,CAAC,EAA3C,EAA+C;AAC3C,kBAAIC,IAAU,GAAG5C,WAAW,CAAC,KAAKoD,eAAN,CAA5B;AACAR,cAAAA,IAAI,CAACE,MAAL,GAAc,KAAKvB,cAAL,CAAoBgB,OAAlC;AACAK,cAAAA,IAAI,CAACG,YAAL;AAAA;AAAA,4DAAsCE,WAAtC,CAAkDC,KAAK,CAACP,CAAD,CAAvD;AACH;AACJ;AACJ;;AAEShB,QAAAA,QAAQ,GAAS;AACvB,cAAI0B,IAAI,GAAG;AAAA;AAAA,wCAAWpB,WAAX,GAAyBI,KAAzB,CAA+BiB,UAA/B,EAAX;AACA,eAAK9B,cAAL,CAAoBe,OAApB,CAA4BC,iBAA5B;;AACA,eAAK,IAAIG,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGU,IAAI,CAACtB,MAAjC,EAAyCY,CAAC,EAA1C,EAA8C;AAC1C,gBAAIY,GAAG,GAAGF,IAAI,CAACV,CAAD,CAAd;AAGA,gBAAIC,IAAU,GAAG5C,WAAW,CAAC,KAAKwD,cAAN,CAA5B;AACAZ,YAAAA,IAAI,CAACE,MAAL,GAAc,KAAKtB,cAAL,CAAoBe,OAAlC;AACAK,YAAAA,IAAI,CAACG,YAAL;AAAA;AAAA,wDAAqCU,OAArC,CAA6CF,GAA7C;AACH;AACJ;;AAESxC,QAAAA,gBAAgB,CAACqB,KAAD,EAA0B;AAChD,eAAKX,SAAL;AACH;;AAESR,QAAAA,YAAY,CAACyC,IAAD,EAAuB;AACzC,cAAI;AAAA;AAAA,wCAAWzB,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,GAAmDH,MAAnD,IAA6D0B,IAAI,CAAC1B,MAAtE,EAA8E;AAC1E,iBAAKF,MAAL,CAAY4B,IAAI,CAACV,KAAL,GAAa,CAAzB,EAA4BD,YAA5B;AAAA;AAAA,0DAA6DE,WAA7D,CAAyES,IAAzE;AACH;AACJ;;AAESvC,QAAAA,WAAW,GAAQ;AACzB,eAAKQ,QAAL;AACH;;AAEDgC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,cAAIC,KAAa,GAAG,KAAKC,OAAL,CAAaC,OAAb,CAAqBL,MAArB,CAApB;;AACA,cAAIG,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAK3C,cAAL,CAAoBC,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACA,iBAAKC,cAAL,CAAoBF,IAApB,CAAyBC,MAAzB,GAAkC,IAAlC;AACA,iBAAKE,cAAL,CAAoBH,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACH,WAJD,MAIO,IAAGyC,KAAK,IAAI,CAAZ,EAAc;AACjB,iBAAK3C,cAAL,CAAoBC,IAApB,CAAyBC,MAAzB,GAAkC,IAAlC;AACA,iBAAKC,cAAL,CAAoBF,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACA,iBAAKE,cAAL,CAAoBH,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACH,WAJM,MAIF;AACD,iBAAKF,cAAL,CAAoBC,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACA,iBAAKC,cAAL,CAAoBF,IAApB,CAAyBC,MAAzB,GAAkC,KAAlC;AACA,iBAAKE,cAAL,CAAoBH,IAApB,CAAyBC,MAAzB,GAAkC,IAAlC;AACH;AACJ;;AAhHqD,O;;;;;iBAElC,E;;;;;;;iBAES,I;;;;;;;iBAEA,I;;;;;;;iBAEA,I;;;;;;;iBAGH,I;;;;;;;iBAEA,I;;;;;;;iBAGD,I", "sourcesContent": ["import { _decorator, Component, Toggle, ScrollView, Prefab, Node, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { ArmyData } from \"../../general/ArmyProxy\";\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport MapCommand from \"../MapCommand\";\nimport RightArmyItemLogic from \"./RightArmyItemLogic\";\nimport { MapCityData } from \"../MapCityProxy\";\nimport RightCityItemLogic from \"./RightCityItemLogic\";\nimport RightTagItemLogic from \"./RightTagItemLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('RightInfoNodeLogic')\nexport default class RightInfoNodeLogic extends Component {\n    @property([Toggle])\n    toggles: Toggle[] = [];\n    @property(ScrollView)\n    armyScrollView: ScrollView = null;\n    @property(ScrollView)\n    cityScrollView: ScrollView = null;\n    @property(ScrollView)\n    tagsScrollView: ScrollView = null;\n\n    @property(Prefab)\n    armyItemPrefabs: Prefab = null;\n    @property(Prefab)\n    cityItemPrefabs: Prefab = null;\n\n    @property(Prefab)\n    tagItemPrefabs: Prefab = null;\n\n    protected _armys: Node[] = [];\n\n\n    protected onLoad(): void {\n        EventMgr.on(LogicEvent.updateArmyList, this.onUpdateArmyList, this);\n        EventMgr.on(LogicEvent.updateArmy, this.onUpdateArmy, this);\n        EventMgr.on(LogicEvent.updateTag, this.onUpdateTag, this);\n\n        this.armyScrollView.node.active = true;\n        this.cityScrollView.node.active = false;\n        this.tagsScrollView.node.active = false;\n        this.initArmys();\n        this.initCitys();\n        this.initTags();\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._armys.length = 0;\n        this._armys = null;\n    }\n\n    protected initArmys(): void {\n        let cityId: number = MapCommand.getInstance().cityProxy.getMyMainCity().cityId;\n        let datas: ArmyData[] = ArmyCommand.getInstance().proxy.getArmyList(cityId);\n        this.armyScrollView.content.removeAllChildren();\n        console.log(\"datas\", datas);\n        if (datas) {\n            this._armys.length = datas.length;\n            for (let i: number = 0; i < datas.length; i++) {\n                let item: Node = instantiate(this.armyItemPrefabs);\n                item.parent = this.armyScrollView.content;\n                this._armys[i] = item;\n                item.getComponent(RightArmyItemLogic).order = i + 1;\n                item.getComponent(RightArmyItemLogic).setArmyData(datas[i]);\n            }\n        }\n    }\n\n    protected initCitys():void {\n        let citys: MapCityData[] = MapCommand.getInstance().cityProxy.getMyCitys();\n        this.cityScrollView.content.removeAllChildren();\n        if (citys && citys.length > 0) {\n            for (let i: number = 0; i < citys.length; i++) {\n                let item: Node = instantiate(this.cityItemPrefabs);\n                item.parent = this.cityScrollView.content;\n                item.getComponent(RightCityItemLogic).setArmyData(citys[i]);\n            }\n        }\n    }\n\n    protected initTags(): void {\n        let tags = MapCommand.getInstance().proxy.getPosTags();\n        this.tagsScrollView.content.removeAllChildren();\n        for (let i: number = 0; i < tags.length; i++) {\n            var tag = tags[i];\n\n            \n            let item: Node = instantiate(this.tagItemPrefabs);\n            item.parent = this.tagsScrollView.content;\n            item.getComponent(RightTagItemLogic).setData(tag);\n        }\n    }\n\n    protected onUpdateArmyList(datas: ArmyData[]): void {\n        this.initArmys();\n    }\n\n    protected onUpdateArmy(data: ArmyData): void {\n        if (MapCommand.getInstance().cityProxy.getMyMainCity().cityId == data.cityId) {\n            this._armys[data.order - 1].getComponent(RightArmyItemLogic).setArmyData(data);\n        }\n    }\n\n    protected onUpdateTag():void {\n        this.initTags();\n    }\n\n    onClockToggle(toggle: Toggle): void {\n        AudioManager.instance.playClick();\n        let index: number = this.toggles.indexOf(toggle);\n        if (index == 1) {\n            this.armyScrollView.node.active = false;\n            this.cityScrollView.node.active = true;\n            this.tagsScrollView.node.active = false;\n        } else if(index == 0){\n            this.armyScrollView.node.active = true;\n            this.cityScrollView.node.active = false;\n            this.tagsScrollView.node.active = false;\n        }else{\n            this.armyScrollView.node.active = false;\n            this.cityScrollView.node.active = false;\n            this.tagsScrollView.node.active = true;\n        }\n    }\n}\n"]}