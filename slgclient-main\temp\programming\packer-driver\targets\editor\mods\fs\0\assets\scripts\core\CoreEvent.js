System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, CoreEvent, _crd;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  _export("CoreEvent", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "57ff19Ak7REbIrnguUQpxPg", "CoreEvent", undefined);

      _export("CoreEvent", CoreEvent = class CoreEvent {});

      _defineProperty(CoreEvent, "loadProgress", "load_progress");

      _defineProperty(CoreEvent, "loadComplete", "load_complete");

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=CoreEvent.js.map