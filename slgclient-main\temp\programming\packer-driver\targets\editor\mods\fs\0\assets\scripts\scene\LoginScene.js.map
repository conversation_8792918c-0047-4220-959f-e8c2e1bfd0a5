{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts"], "names": ["_decorator", "Component", "Prefab", "instantiate", "AudioManager", "LogicEvent", "LoginCommand", "NetEvent", "EventMgr", "ccclass", "property", "LoginScene", "onLoad", "openLogin", "on", "createRole", "onCreate", "enterServerComplete", "enterServer", "onDestroy", "targetOff", "_loginNode", "_serverListNode", "loginPrefab", "parent", "node", "active", "_createNode", "createPrefab", "console", "log", "emit", "ServerRequesting", "onClickEnter", "instance", "playClick", "loginData", "getInstance", "proxy", "getLoginData", "role_enterServer", "getSession"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,W,OAAAA,W;;AACrCC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AAGFC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;OAJH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;yBAOTW,U,WADpBF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACR,MAAD,C,oCANb,MACqBS,UADrB,SACwCV,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,8CAQjB,IARiB;;AAAA,+CAShB,IATgB;;AAAA,mDAUZ,IAVY;;AAAA,8CAYjB,IAZiB;AAAA;;AAcpCW,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,UAAvB,EAAmC,KAAKC,QAAxC,EAAkD,IAAlD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,mBAAvB,EAA4C,KAAKC,WAAjD,EAA8D,IAA9D;AAEH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,eAAKC,UAAL,GAAkB,IAAlB;AACA,eAAKC,eAAL,GAAuB,IAAvB;AACH;;AAEST,QAAAA,SAAS,GAAS;AAExB,cAAI,KAAKQ,UAAL,IAAmB,IAAvB,EAA6B;AACzB,iBAAKA,UAAL,GAAkBlB,WAAW,CAAC,KAAKoB,WAAN,CAA7B;AACA,iBAAKF,UAAL,CAAgBG,MAAhB,GAAyB,KAAKC,IAA9B;AACH,WAHD,MAGO;AACH,iBAAKJ,UAAL,CAAgBK,MAAhB,GAAyB,IAAzB;AACH;AACJ;;AAESV,QAAAA,QAAQ,GAAS;AAEvB,cAAI,KAAKW,WAAL,IAAoB,IAAxB,EAA8B;AAC1B,iBAAKA,WAAL,GAAmBxB,WAAW,CAAC,KAAKyB,YAAN,CAA9B;AACA,iBAAKD,WAAL,CAAiBH,MAAjB,GAA0B,KAAKC,IAA/B;AACH,WAHD,MAGO;AACH,iBAAKE,WAAL,CAAiBD,MAAjB,GAA0B,IAA1B;AACH;AACJ;;AAGSR,QAAAA,WAAW,GAAO;AACxBW,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,oCAASC,gBAAvB,EAAyC,IAAzC;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,cAAIC,SAAS,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,YAAjC,EAAhB;;AACA,cAAIH,SAAS,IAAI,IAAjB,EAAuB;AACnB,iBAAKvB,SAAL;AACA;AACH,WAP0B,CAQ3B;;;AACA;AAAA;AAAA,4CAAawB,WAAb,GAA2BG,gBAA3B,CAA4C;AAAA;AAAA,4CAAaH,WAAb,GAA2BC,KAA3B,CAAiCG,UAAjC,EAA5C;AACH;;AA/D6C,O;;;;;iBAExB,I;;;;;;;iBAEC,I;;;;;;;iBAEI,I", "sourcesContent": ["import { _decorator, Component, Prefab, Node, instantiate } from 'cc';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\nconst { ccclass, property } = _decorator;\n\nimport LoginCommand from \"../login/LoginCommand\";\nimport { NetEvent } from \"../network/socket/NetInterface\";\nimport { EventMgr } from '../utils/EventMgr';\n\n@ccclass('LoginScene')\nexport default class LoginScene extends Component {\n    @property(Prefab)\n    loginPrefab: Prefab = null;\n    @property(Prefab)\n    createPrefab: Prefab = null;\n    @property(Prefab)\n    serverListPrefab: Prefab = null;\n\n    protected _loginNode: Node = null;\n    protected _createNode: Node = null;\n    protected _serverListNode: Node = null;\n\n    protected _enterNode: Node = null;\n\n    protected onLoad(): void {\n        this.openLogin();\n        EventMgr.on(LogicEvent.createRole, this.onCreate, this);\n        EventMgr.on(LogicEvent.enterServerComplete, this.enterServer, this);\n        \n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._loginNode = null;\n        this._serverListNode = null;\n    }\n\n    protected openLogin(): void {\n\n        if (this._loginNode == null) {\n            this._loginNode = instantiate(this.loginPrefab);\n            this._loginNode.parent = this.node;\n        } else {\n            this._loginNode.active = true;\n        }\n    }\n\n    protected onCreate(): void {\n \n        if (this._createNode == null) {\n            this._createNode = instantiate(this.createPrefab);\n            this._createNode.parent = this.node;\n        } else {\n            this._createNode.active = true;\n        }\n    }\n\n\n    protected enterServer():void{\n        console.log(\"enterServer\");\n        EventMgr.emit(NetEvent.ServerRequesting, true);\n    }\n\n    protected onClickEnter(): void {\n        //未登录 就弹登录界面\n        AudioManager.instance.playClick();\n        var loginData = LoginCommand.getInstance().proxy.getLoginData();\n        if (loginData == null) {\n            this.openLogin();\n            return;\n        }\n        //登录完成进入服务器\n        LoginCommand.getInstance().role_enterServer(LoginCommand.getInstance().proxy.getSession());\n    }\n}\n"]}