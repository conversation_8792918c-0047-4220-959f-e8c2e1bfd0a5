---
type: "always_apply"
description: "半成品SLG项目开发规则"
---
# 半成品SLG项目开发强制规则（Augment Rules）
<!-- 每次 Augment 生成 / 修改代码前，都需要告诉用户使用的Claude版本号，并且必须完整阅读并遵守以下条款 -->

1. 统一技术栈 & 版本锁定
   - 服务端：Go 1.24.5 + WebSocket + HTTP
   - 客户端：Cocos Creator 3.4.0 + TypeScript + Built-in 管线
   - 数据层：MySQL 8.4.5 + XORM ORM框架
   - 地图系统：Tiled地图编辑器 + 等距视角瓦片地图
   - Node.js ≥ 18（用于 Creator 构建）
   任何升级须全员评审并在项目文档中更新版本号。

2. 目录与命名
   - 全英文、camelCase（前端）/ snake_case（后端）；禁止中文、空格、特殊符号。
   - 客户端根目录：`slgclient-main/`
   - 服务端根目录：`slgserver-main/`
   - 构建输出统一在 `build/<platform>/`（如 `build/web-mobile/`）。

3. 功能 / 工具复用
   - 新增代码前先全局搜索 `assets/scripts/**` 和 `server/**` 是否已有同功能。
   - 优先扩展现有Command模式架构；重复功能直接复用，严禁擅自新建临时目录。
   - 解决报错时，要分析报错原因，在现有架构框架下解决问题，不要采用规避方法。

4. 注释规范
   - 关键逻辑、地图坐标转换、网络协议必须用中文注释。
   - 函数头部 `/** 中文用途 */`，复杂算法行尾 `// 中文说明`。
   - 每次回答时，报上使用的Claude版本号。

5. 配置 & 常量化
   - 所有平台差异（屏幕适配、构建参数、服务器地址）集中到：
     - 客户端：`assets/scripts/config/GameConfig.ts`
     - 服务端：`data/conf/env.ini` + `data/conf/basic.json`。
   - 禁止在代码里硬编码魔数，使用配置文件管理。

6. 构建与发布
   - 支持平台：Android APK / H5-Web / 微信小游戏（可选）。
   - 使用Cocos Creator内置构建系统，构建配置保存在项目设置中。
   - 构建产物输出到 `build/<platform>/`，保留最近 3 次构建。

7. 资源管理
   - 武将头像统一放 `assets/resources/generalpic/`，命名格式：`{cfgId}.png`。
   - 地图资源统一放 `assets/resources/world/`，包含 `.tmx`、`.tsx`、`.png`、`.plist` 文件。
   - 配置文件统一放 `assets/resources/config/`，与服务端配置保持同步。

8. 网络协议规范
   - 服务端协议格式：WebSocket + JSON，消息结构 `{name, seq, msg}`。
   - 客户端网络层统一使用 `assets/scripts/network/NetManager.ts`，禁止直接创建WebSocket。
   - 协议定义文档保存在服务端 `server/` 目录下，变动即更新。

9. 地图系统规范
   - 地图坐标系统：等距坐标，支持格子坐标与像素坐标转换。
   - 地图尺寸：1000×1000格子，瓦片大小：160×80像素。
   - 分区加载：使用 `MapUtil.ts` 统一管理，禁止直接操作地图数据。

10. 数据库操作规范
    - 服务端统一使用XORM进行数据库操作，禁止直接拼接SQL。
    - 表名格式：`tb_{模块}_{服务器ID}`，如 `tb_role_1`。
    - 数据库脚本统一放在 `data/conf/db.sql`，结构变更即更新。

11. 配置文件管理
    - 服务端配置：`data/conf/` 目录下，支持JSON和INI格式。
    - 客户端配置：`assets/resources/config/` 目录下，与服务端保持同步。
    - 配置热加载：服务端支持配置文件热加载，客户端重启后生效。

12. 代码提交规范
    - 提交前确认只修改必要文件，避免大面积重构。
    - 在我验收没有问题后，即开发新的任务后，提交并推送git仓库
    - 提交信息格式：`[模块] 功能描述`，如 `[地图] 修复坐标转换bug`。
    - 关键功能变更需要在项目文档中记录。
