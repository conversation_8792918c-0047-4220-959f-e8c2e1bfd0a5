System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Node, Button, Prefab, instantiate, AudioManager, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _temp, _crd, ccclass, property, MainMenuController;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfAudioManager(extras) {
    _reporterNs.report("AudioManager", "../common/AudioManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Button = _cc.Button;
      Prefab = _cc.Prefab;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      AudioManager = _unresolved_2.AudioManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f4e592dY39LQKN9beMqf/bo", "MainMenuController", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 主菜单控制器
       * 控制主菜单的两个按钮：登录游戏、离开游戏
       */

      _export("MainMenuController", MainMenuController = (_dec = ccclass('MainMenuController'), _dec2 = property(Button), _dec3 = property(Button), _dec4 = property(Prefab), _dec5 = property(Prefab), _dec6 = property(Node), _dec(_class = (_class2 = (_temp = class MainMenuController extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "loginButton", _descriptor, this);

          _initializerDefineProperty(this, "exitButton", _descriptor2, this);

          _initializerDefineProperty(this, "loginDialogPrefab", _descriptor3, this);

          _initializerDefineProperty(this, "exitDialogPrefab", _descriptor4, this);

          _initializerDefineProperty(this, "dialogContainer", _descriptor5, this);

          _defineProperty(this, "currentDialog", null);
        }

        onLoad() {
          console.log('[MainMenuController] 主菜单控制器加载');
          this.setupEventListeners();
        }

        start() {
          console.log('[MainMenuController] 主菜单控制器启动');
        }
        /**
         * 设置事件监听器
         */


        setupEventListeners() {
          if (this.loginButton) {
            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
            console.log('[MainMenuController] 登录按钮事件监听器已设置');
          }

          if (this.exitButton) {
            this.exitButton.node.on(Button.EventType.CLICK, this.onExitButtonClick, this);
            console.log('[MainMenuController] 离开按钮事件监听器已设置');
          }
        }
        /**
         * 登录游戏按钮点击事件
         */


        onLoginButtonClick() {
          console.log('[MainMenuController] 登录游戏按钮被点击'); // 播放点击音效

          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick(); // 显示登录对话框

          this.showLoginDialog();
        }
        /**
         * 离开游戏按钮点击事件
         */


        onExitButtonClick() {
          console.log('[MainMenuController] 离开游戏按钮被点击'); // 播放点击音效

          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick(); // 显示离开游戏对话框

          this.showExitDialog();
        }
        /**
         * 显示登录对话框
         */


        showLoginDialog() {
          if (!this.loginDialogPrefab) {
            console.error('[MainMenuController] 登录对话框预制体未设置');
            return;
          } // 关闭当前对话框


          this.closeCurrentDialog(); // 创建登录对话框

          this.currentDialog = instantiate(this.loginDialogPrefab);
          this.currentDialog.setParent(this.dialogContainer);
          this.currentDialog.setPosition(0, 0, 0);
          console.log('[MainMenuController] 登录对话框已显示');
        }
        /**
         * 显示离开游戏对话框
         */


        showExitDialog() {
          if (!this.exitDialogPrefab) {
            console.error('[MainMenuController] 离开游戏对话框预制体未设置');
            return;
          } // 关闭当前对话框


          this.closeCurrentDialog(); // 创建离开游戏对话框

          this.currentDialog = instantiate(this.exitDialogPrefab);
          this.currentDialog.setParent(this.dialogContainer);
          this.currentDialog.setPosition(0, 0, 0);
          console.log('[MainMenuController] 离开游戏对话框已显示');
        }
        /**
         * 关闭当前对话框
         */


        closeCurrentDialog() {
          if (this.currentDialog) {
            this.currentDialog.destroy();
            this.currentDialog = null;
            console.log('[MainMenuController] 当前对话框已关闭');
          }
        }

        onDestroy() {
          // 清理事件监听器
          if (this.loginButton) {
            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);
          }

          if (this.exitButton) {
            this.exitButton.node.off(Button.EventType.CLICK, this.onExitButtonClick, this);
          }

          console.log('[MainMenuController] 主菜单控制器销毁');
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "loginButton", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "exitButton", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "loginDialogPrefab", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "exitDialogPrefab", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "dialogContainer", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=MainMenuController.js.map