System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Node, Button, Label, EditBox, Canvas, UITransform, Color, Sprite, screen, LoginCommand, _dec, _class, _temp, _crd, ccclass, property, SimpleLoginUI;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfLoginCommand(extras) {
    _reporterNs.report("LoginCommand", "./LoginCommand", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Button = _cc.Button;
      Label = _cc.Label;
      EditBox = _cc.EditBox;
      Canvas = _cc.Canvas;
      UITransform = _cc.UITransform;
      Color = _cc.Color;
      Sprite = _cc.Sprite;
      screen = _cc.screen;
    }, function (_unresolved_2) {
      LoginCommand = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9ca5eoyfSNLh78yj6dD3kiI", "SimpleLoginUI", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 简单登录UI
       * 基于测试成功的UI结构创建的实际登录界面
       */

      _export("SimpleLoginUI", SimpleLoginUI = (_dec = ccclass('SimpleLoginUI'), _dec(_class = (_temp = class SimpleLoginUI extends Component {
        constructor(...args) {
          super(...args);

          _defineProperty(this, "usernameInput", null);

          _defineProperty(this, "passwordInput", null);

          _defineProperty(this, "loginButton", null);

          _defineProperty(this, "statusLabel", null);
        }

        onLoad() {
          console.log('[SimpleLoginUI] 开始创建简单登录界面'); // 创建登录UI

          this.createLoginUI();
          console.log('[SimpleLoginUI] 简单登录界面创建完成');
        }
        /**
         * 创建登录UI
         */


        createLoginUI() {
          // 确保当前节点有Canvas组件
          let canvas = this.getComponent(Canvas);

          if (!canvas) {
            canvas = this.addComponent(Canvas);
          } // 设置Canvas（使用成功的配置）


          canvas.alignCanvasWithScreen = false;
          this.node.setPosition(0, 0, 0); // 设置UITransform

          const uiTransform = this.getComponent(UITransform);

          if (uiTransform) {
            const windowSize = screen.windowSize;
            uiTransform.setContentSize(windowSize.width, windowSize.height);
            uiTransform.setAnchorPoint(0.5, 0.5);
          } // 创建背景


          this.createBackground(); // 创建标题

          this.createTitle(); // 创建用户名输入框

          this.createUsernameInput(); // 创建密码输入框

          this.createPasswordInput(); // 创建登录按钮

          this.createLoginButton(); // 创建状态标签

          this.createStatusLabel();
        }
        /**
         * 创建背景
         */


        createBackground() {
          const bgNode = new Node('Background');
          bgNode.setParent(this.node);
          const bgTransform = bgNode.addComponent(UITransform);
          const windowSize = screen.windowSize;
          bgTransform.setContentSize(windowSize.width, windowSize.height);
          bgTransform.setAnchorPoint(0.5, 0.5);
          const bgSprite = bgNode.addComponent(Sprite);
          bgSprite.color = new Color(30, 30, 50, 255); // 深蓝色背景

          bgNode.setPosition(0, 0, 0);
        }
        /**
         * 创建标题
         */


        createTitle() {
          const titleNode = new Node('Title');
          titleNode.setParent(this.node);
          const titleTransform = titleNode.addComponent(UITransform);
          titleTransform.setContentSize(400, 60);
          titleTransform.setAnchorPoint(0.5, 0.5);
          titleNode.setPosition(0, 150, 0);
          const titleLabel = titleNode.addComponent(Label);
          titleLabel.string = 'SLG游戏登录';
          titleLabel.fontSize = 36;
          titleLabel.color = new Color(255, 255, 255, 255);
        }
        /**
         * 创建用户名输入框
         */


        createUsernameInput() {
          // 标签
          const labelNode = new Node('UsernameLabel');
          labelNode.setParent(this.node);
          const labelTransform = labelNode.addComponent(UITransform);
          labelTransform.setContentSize(300, 30);
          labelTransform.setAnchorPoint(0.5, 0.5);
          labelNode.setPosition(0, 80, 0);
          const label = labelNode.addComponent(Label);
          label.string = '用户名：';
          label.fontSize = 20;
          label.color = new Color(255, 255, 255, 255); // 创建一个简单的EditBox节点

          const inputNode = new Node('UsernameInput');
          inputNode.setParent(this.node); // 设置变换组件

          const inputTransform = inputNode.addComponent(UITransform);
          inputTransform.setContentSize(300, 50);
          inputTransform.setAnchorPoint(0.5, 0.5);
          inputNode.setPosition(0, 40, 0); // 添加背景精灵

          const bgSprite = inputNode.addComponent(Sprite); // 使用纯色背景

          bgSprite.color = new Color(255, 255, 255, 255); // 添加EditBox组件

          this.usernameInput = inputNode.addComponent(EditBox);
          this.usernameInput.string = '';
          this.usernameInput.placeholder = '请输入用户名';
          this.usernameInput.maxLength = 20;
          this.usernameInput.fontSize = 20;
          this.usernameInput.fontColor = new Color(0, 0, 0, 255);
          this.usernameInput.placeholderFontColor = new Color(128, 128, 128, 255);
          this.usernameInput.inputMode = EditBox.InputMode.SINGLE_LINE;
          this.usernameInput.returnType = EditBox.KeyboardReturnType.DONE; // 设置背景图像类型

          this.usernameInput.backgroundImage = bgSprite; // 添加调试日志

          console.log('[SimpleLoginUI] 用户名输入框创建完成');
          console.log('[SimpleLoginUI] EditBox节点位置:', inputNode.position.x, inputNode.position.y);
          console.log('[SimpleLoginUI] EditBox尺寸:', inputTransform.width, inputTransform.height);
        }
        /**
         * 创建密码输入框
         */


        createPasswordInput() {
          // 标签
          const labelNode = new Node('PasswordLabel');
          labelNode.setParent(this.node);
          const labelTransform = labelNode.addComponent(UITransform);
          labelTransform.setContentSize(300, 30);
          labelTransform.setAnchorPoint(0.5, 0.5);
          labelNode.setPosition(0, -20, 0);
          const label = labelNode.addComponent(Label);
          label.string = '密码：';
          label.fontSize = 20;
          label.color = new Color(255, 255, 255, 255); // 创建一个简单的EditBox节点

          const inputNode = new Node('PasswordInput');
          inputNode.setParent(this.node); // 设置变换组件

          const inputTransform = inputNode.addComponent(UITransform);
          inputTransform.setContentSize(300, 50);
          inputTransform.setAnchorPoint(0.5, 0.5);
          inputNode.setPosition(0, -60, 0); // 添加背景精灵

          const bgSprite = inputNode.addComponent(Sprite); // 使用纯色背景

          bgSprite.color = new Color(255, 255, 255, 255); // 添加EditBox组件

          this.passwordInput = inputNode.addComponent(EditBox);
          this.passwordInput.string = '';
          this.passwordInput.placeholder = '请输入密码';
          this.passwordInput.maxLength = 20;
          this.passwordInput.fontSize = 20;
          this.passwordInput.fontColor = new Color(0, 0, 0, 255);
          this.passwordInput.placeholderFontColor = new Color(128, 128, 128, 255);
          this.passwordInput.inputFlag = EditBox.InputFlag.PASSWORD; // 密码模式

          this.passwordInput.inputMode = EditBox.InputMode.SINGLE_LINE;
          this.passwordInput.returnType = EditBox.KeyboardReturnType.DONE; // 设置背景图像类型

          this.passwordInput.backgroundImage = bgSprite; // 添加调试日志

          console.log('[SimpleLoginUI] 密码输入框创建完成');
          console.log('[SimpleLoginUI] EditBox节点位置:', inputNode.position.x, inputNode.position.y);
          console.log('[SimpleLoginUI] EditBox尺寸:', inputTransform.width, inputTransform.height);
        }
        /**
         * 创建登录按钮
         */


        createLoginButton() {
          const buttonNode = new Node('LoginButton');
          buttonNode.setParent(this.node);
          const buttonTransform = buttonNode.addComponent(UITransform);
          buttonTransform.setContentSize(200, 60);
          buttonTransform.setAnchorPoint(0.5, 0.5);
          buttonNode.setPosition(0, -140, 0); // 按钮背景

          const buttonSprite = buttonNode.addComponent(Sprite);
          buttonSprite.color = new Color(0, 150, 255, 255); // 按钮组件

          this.loginButton = buttonNode.addComponent(Button);
          this.loginButton.transition = Button.Transition.COLOR;
          this.loginButton.normalColor = new Color(0, 150, 255, 255);
          this.loginButton.pressedColor = new Color(0, 100, 200, 255);
          this.loginButton.hoverColor = new Color(50, 180, 255, 255);
          this.loginButton.disabledColor = new Color(100, 100, 100, 255); // 按钮文字

          const labelNode = new Node('Label');
          labelNode.setParent(buttonNode);
          const labelTransform = labelNode.addComponent(UITransform);
          labelTransform.setContentSize(200, 60);
          labelTransform.setAnchorPoint(0.5, 0.5);
          labelNode.setPosition(0, 0, 0);
          const label = labelNode.addComponent(Label);
          label.string = '登录';
          label.fontSize = 24;
          label.color = new Color(255, 255, 255, 255); // 绑定点击事件

          this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
        }
        /**
         * 创建状态标签
         */


        createStatusLabel() {
          const labelNode = new Node('StatusLabel');
          labelNode.setParent(this.node);
          const labelTransform = labelNode.addComponent(UITransform);
          labelTransform.setContentSize(400, 50);
          labelTransform.setAnchorPoint(0.5, 0.5);
          labelNode.setPosition(0, -220, 0);
          this.statusLabel = labelNode.addComponent(Label);
          this.statusLabel.string = '请输入用户名和密码';
          this.statusLabel.fontSize = 16;
          this.statusLabel.color = new Color(200, 200, 200, 255);
        }
        /**
         * 登录按钮点击事件
         */


        onLoginButtonClick() {
          const username = this.usernameInput.string.trim();
          const password = this.passwordInput.string.trim();
          console.log(`[SimpleLoginUI] 登录按钮被点击，用户名: ${username}`);

          if (!username) {
            this.showStatus('请输入用户名', new Color(255, 100, 100, 255));
            return;
          }

          if (!password) {
            this.showStatus('请输入密码', new Color(255, 100, 100, 255));
            return;
          } // 显示登录中状态


          this.showStatus('正在登录...', new Color(255, 255, 0, 255));
          this.loginButton.interactable = false; // 调用登录逻辑

          this.performLogin(username, password);
        }
        /**
         * 执行登录
         */


        performLogin(username, password) {
          console.log(`[SimpleLoginUI] 执行登录: ${username}`);

          try {
            // 调用实际的登录逻辑
            const loginCommand = (_crd && LoginCommand === void 0 ? (_reportPossibleCrUseOfLoginCommand({
              error: Error()
            }), LoginCommand) : LoginCommand).getInstance();
            loginCommand.accountLogin(username, password); // 监听登录结果

            this.scheduleOnce(() => {
              // 这里可以根据实际的登录状态来判断
              // 暂时使用简单的测试逻辑
              if (username === 'test' && password === 'test') {
                this.showStatus('登录成功！', new Color(0, 255, 0, 255));
                console.log('[SimpleLoginUI] 登录成功，准备进入游戏');
              } else {
                this.showStatus('正在连接服务器...', new Color(255, 255, 0, 255)); // 实际项目中这里会通过事件监听登录结果

                this.scheduleOnce(() => {
                  this.showStatus('连接服务器失败，请检查网络', new Color(255, 100, 100, 255));
                  this.loginButton.interactable = true;
                }, 3.0);
              }
            }, 1.0);
          } catch (error) {
            console.error('[SimpleLoginUI] 登录出错:', error);
            this.showStatus('登录出错，请重试', new Color(255, 100, 100, 255));
            this.loginButton.interactable = true;
          }
        }
        /**
         * 显示状态信息
         */


        showStatus(message, color) {
          if (this.statusLabel) {
            this.statusLabel.string = message;
            this.statusLabel.color = color;
          }

          console.log(`[SimpleLoginUI] 状态: ${message}`);
        }

      }, _temp)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=SimpleLoginUI.js.map