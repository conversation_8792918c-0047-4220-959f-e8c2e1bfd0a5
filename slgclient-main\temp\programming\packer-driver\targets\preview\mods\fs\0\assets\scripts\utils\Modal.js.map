{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts"], "names": ["_decorator", "Component", "Node", "ccclass", "property", "Modal", "start", "onEnable", "mask", "active", "on", "EventType", "TOUCH_START", "stopPropagation", "TOUCH_END", "onDisable", "off", "event", "propagationStopped"], "mappings": ";;;;;;;;;;;;;;;;AAESA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;;;;;;OAC1B;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBJ,U;;yBAGPK,K,WADpBF,OAAO,CAAC,OAAD,C,UAGHC,QAAQ,CAACF,IAAD,C,oCAHb,MACqBG,KADrB,SACmCJ,SADnC,CAC6C;AAAA;AAAA;;AAAA;AAAA;;AAMzCK,QAAAA,KAAK,GAAI,CAER;;AAESC,QAAAA,QAAQ,GAAQ;AACtB,eAAKC,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACA,eAAKD,IAAL,CAAUE,EAAV,CAAaR,IAAI,CAACS,SAAL,CAAeC,WAA5B,EAAyC,KAAKC,eAA9C,EAA+D,IAA/D,EAAqE,IAArE;AACA,eAAKL,IAAL,CAAUE,EAAV,CAAaR,IAAI,CAACS,SAAL,CAAeG,SAA5B,EAAuC,KAAKD,eAA5C,EAA6D,IAA7D,EAAmE,IAAnE;AACH;;AAESE,QAAAA,SAAS,GAAO;AACtB,eAAKP,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA,eAAKD,IAAL,CAAUQ,GAAV,CAAcd,IAAI,CAACS,SAAL,CAAeC,WAA7B,EAA0C,KAAKC,eAA/C,EAAgE,IAAhE,EAAsE,IAAtE;AACA,eAAKL,IAAL,CAAUQ,GAAV,CAAcd,IAAI,CAACS,SAAL,CAAeG,SAA7B,EAAwC,KAAKD,eAA7C,EAA8D,IAA9D,EAAoE,IAApE;AACH;;AAESA,QAAAA,eAAe,CAACI,KAAD,EAAoB;AACzCA,UAAAA,KAAK,CAACC,kBAAN,GAA2B,IAA3B;AACH;;AAxBwC,O;;;;;iBAG5B,I", "sourcesContent": ["\n\nimport { _decorator, Component, Node, Event } from 'cc';\nconst {ccclass, property} = _decorator;\n\n@ccclass('Modal')\nexport default class Modal extends Component {\n\n    @property(Node)\n    mask: Node = null;\n\n    \n    start () {\n\n    }\n\n    protected onEnable() :void{\n        this.mask.active = true;\n        this.mask.on(Node.EventType.TOUCH_START, this.stopPropagation, this, true);\n        this.mask.on(Node.EventType.TOUCH_END, this.stopPropagation, this, true);\n    } \n\n    protected onDisable():void{\n        this.mask.active = false;\n        this.mask.off(Node.EventType.TOUCH_START, this.stopPropagation, this, true);\n        this.mask.off(Node.EventType.TOUCH_END, this.stopPropagation, this, true);\n    }\n\n    protected stopPropagation(event: Event):void {\n        event.propagationStopped = true;\n    }\n}\n"]}