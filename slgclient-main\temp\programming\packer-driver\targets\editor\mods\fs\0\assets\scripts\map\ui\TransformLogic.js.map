{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts"], "names": ["_decorator", "Component", "Layout", "Node", "Label", "Slide<PERSON>", "Toggle", "LoginCommand", "MapCommand", "MapUICommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "TransformLogic", "onLoad", "_nameObj", "wood", "iron", "stone", "grain", "_keyArr", "on", "upateMyRoleRes", "initView", "getRate", "cityId", "getInstance", "cityProxy", "getMyMainCity", "_addition", "proxy", "getMyCityAddition", "rate", "getTransformRate", "taxRate", "updateView", "updateBtn", "roleRes", "getRoleResData", "i", "children_from", "fromLayout", "node", "children", "key", "getChildByName", "getComponent", "string", "children_to", "toLayout", "rateLabel", "trSlider", "progress", "trNode", "active", "_curFromIndex", "_curToIndex", "updateLable", "from_index", "getFromSelectIndex", "to_index", "getToSelectIndex", "tr<PERSON><PERSON><PERSON>", "from_key", "_fromChange", "Math", "round", "_to<PERSON>hange", "length", "isChecked", "fromToggleHandle", "event", "console", "log", "toToggleHandle", "slide<PERSON><PERSON>le", "onDestroy", "targetOff", "onClickClose", "instance", "playClick", "onTransForm", "from", "to", "interiorTransform"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;;AAEtDC,MAAAA,Y;;AACAC,MAAAA,U;;AACAC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;yBASTe,c,WADpBF,OAAO,CAAC,gBAAD,C,UAIHC,QAAQ,CAACZ,MAAD,C,UAIRY,QAAQ,CAACZ,MAAD,C,UAIRY,QAAQ,CAACX,IAAD,C,UAIRW,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAIRU,QAAQ,CAACT,MAAD,C,oCAvBb,MACqBU,cADrB,SAC4Cd,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,4CAyBxB,EAzBwB;;AAAA,2CA0BrB,EA1BqB;;AAAA,iDA2BjB,CAAC,CA3BgB;;AAAA,+CA4BnB,CAAC,CA5BkB;;AAAA,+CA6BnB,CA7BmB;;AAAA,6CA8BrB,CA9BqB;AAAA;;AAgCxCe,QAAAA,MAAM,GAAO;AAEnB,eAAKC,QAAL,GAAgB;AACZC,YAAAA,IAAI,EAAE,KADM;AAEZC,YAAAA,IAAI,EAAE,KAFM;AAGZC,YAAAA,KAAK,EAAE,KAHK;AAIZC,YAAAA,KAAK,EAAE;AAJK,WAAhB;AAOA,eAAKC,OAAL,GAAe,CAAC,MAAD,EAAQ,MAAR,EAAe,OAAf,EAAuB,OAAvB,CAAf;AAEA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,cAAvB,EAAuC,KAAKC,QAA5C,EAAsD,IAAtD;AACH;;AAEOC,QAAAA,OAAO,GAAW;AACtB,cAAIC,MAAM,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,GAAmDH,MAAhE;;AACA,cAAII,SAAS,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BI,KAA3B,CAAiCC,iBAAjC,CAAmDN,MAAnD,CAAhB;;AACA,cAAIO,IAAI,GAAG;AAAA;AAAA,4CAAaN,WAAb,GAA2BI,KAA3B,CAAiCG,gBAAjC,KAAsDJ,SAAS,CAACK,OAA3E;;AACA,iBAAOF,IAAP;AACH;;AAEMT,QAAAA,QAAQ,GAAO;AAClB,eAAKY,UAAL;AACA,eAAKC,SAAL;AAEH;;AAESD,QAAAA,UAAU,GAAO;AACvB,cAAIE,OAAO,GAAG;AAAA;AAAA,4CAAaX,WAAb,GAA2BI,KAA3B,CAAiCQ,cAAjC,EAAd;AACA,cAAIC,CAAC,GAAG,CAAR;AACA,cAAIC,aAAa,GAAG,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBC,QAAzC;;AACA,eAAK,IAAIC,GAAT,IAAgB,KAAK7B,QAArB,EAA+B;AAC3ByB,YAAAA,aAAa,CAACD,CAAD,CAAb,CAAiBM,cAAjB,CAAgC,WAAhC,EAA6CC,YAA7C,CAA0D5C,KAA1D,EAAiE6C,MAAjE,GAA0E,KAAKhC,QAAL,CAAc6B,GAAd,IAAqBP,OAAO,CAACO,GAAD,CAAtG;AACAL,YAAAA,CAAC;AACJ;;AACDA,UAAAA,CAAC,GAAG,CAAJ;AACA,cAAIS,WAAW,GAAG,KAAKC,QAAL,CAAcP,IAAd,CAAmBC,QAArC;;AACA,eAAK,IAAIC,GAAT,IAAgB,KAAK7B,QAArB,EAA+B;AAC3BiC,YAAAA,WAAW,CAACT,CAAD,CAAX,CAAeM,cAAf,CAA8B,WAA9B,EAA2CC,YAA3C,CAAwD5C,KAAxD,EAA+D6C,MAA/D,GAAwE,KAAKhC,QAAL,CAAc6B,GAAd,IAAqBP,OAAO,CAACO,GAAD,CAApG;AACAL,YAAAA,CAAC;AACJ;;AAED,cAAIP,IAAI,GAAG,KAAKR,OAAL,EAAX;AACA,eAAK0B,SAAL,CAAeH,MAAf,GAAwB,SAAUf,IAAI,GAAC,GAAvC;AAEH;;AAESI,QAAAA,SAAS,GAAO;AACtB,eAAKe,QAAL,CAAcC,QAAd,GAAyB,GAAzB;AACA,eAAKC,MAAL,CAAYC,MAAZ,GAAqB,KAAKC,aAAL,IAAsB,KAAKC,WAA3B,GAAuC,KAAvC,GAA6C,IAAlE;AACA,eAAKC,WAAL;AACH;;AAGSA,QAAAA,WAAW,GAAO;AACxB,cAAIC,UAAU,GAAG,KAAKC,kBAAL,EAAjB;AACA,cAAIC,QAAQ,GAAG,KAAKC,gBAAL,EAAf;;AACA,cAAIH,UAAU,GAAG,CAAb,IAAkBE,QAAQ,GAAG,CAAjC,EAAmC;AAC/B,iBAAKE,OAAL,CAAaf,MAAb,GAAsB,EAAtB;AACH,WAFD,MAEK;AACD,gBAAIV,OAAO,GAAG;AAAA;AAAA,8CAAaX,WAAb,GAA2BI,KAA3B,CAAiCQ,cAAjC,EAAd;AACA,gBAAIyB,QAAQ,GAAG,KAAK3C,OAAL,CAAasC,UAAb,CAAf;AACA,iBAAKM,WAAL,GAAmBC,IAAI,CAACC,KAAL,CAAW7B,OAAO,CAAC0B,QAAD,CAAP,GAAoB,KAAKZ,QAAL,CAAcC,QAA7C,CAAnB;AAEA,gBAAIpB,IAAI,GAAG,KAAKR,OAAL,EAAX;AACA,iBAAK2C,SAAL,GAAiBF,IAAI,CAACC,KAAL,CAAW,KAAKF,WAAL,GAAmBhC,IAAnB,GAA0B,GAArC,CAAjB;AACA,iBAAK8B,OAAL,CAAaf,MAAb,GAAsB,KAAKiB,WAAL,GAAoB,GAApB,GAA0B,KAAKG,SAArD;AACH;AACJ;;AAESR,QAAAA,kBAAkB,GAAS;AACjC,cAAInB,aAAa,GAAG,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBC,QAAzC;;AACA,eAAI,IAAIJ,CAAC,GAAG,CAAZ,EAAcA,CAAC,GAAGC,aAAa,CAAC4B,MAAhC,EAAuC7B,CAAC,EAAxC,EAA2C;AACvC,gBAAGC,aAAa,CAACD,CAAD,CAAb,CAAiBO,YAAjB,CAA8B1C,MAA9B,EAAsCiE,SAAzC,EAAmD;AAC/C,qBAAO9B,CAAP;AACH;AACJ;;AAED,iBAAO,CAAC,CAAR;AACH;;AAGSsB,QAAAA,gBAAgB,GAAS;AAC/B,cAAIb,WAAW,GAAG,KAAKC,QAAL,CAAcP,IAAd,CAAmBC,QAArC;;AACA,eAAI,IAAIJ,CAAC,GAAG,CAAZ,EAAcA,CAAC,GAAGS,WAAW,CAACoB,MAA9B,EAAqC7B,CAAC,EAAtC,EAAyC;AACrC,gBAAGS,WAAW,CAACT,CAAD,CAAX,CAAeO,YAAf,CAA4B1C,MAA5B,EAAoCiE,SAAvC,EAAiD;AAC7C,qBAAO9B,CAAP;AACH;AACJ;;AAED,iBAAO,CAAC,CAAR;AACH;;AAGS+B,QAAAA,gBAAgB,CAACC,KAAD,EAAgB;AACtCC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAgC,KAAKd,kBAAL,EAAhC;AACA,eAAKJ,aAAL,GAAqB,KAAKI,kBAAL,EAArB;AACA,eAAKvB,SAAL;AACH;;AAESsC,QAAAA,cAAc,CAACH,KAAD,EAAgB;AACpCC,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA8B,KAAKZ,gBAAL,EAA9B;AACA,eAAKL,WAAL,GAAmB,KAAKK,gBAAL,EAAnB;AACA,eAAKzB,SAAL;AACH;;AAGSuC,QAAAA,WAAW,GAAO;AACxB,eAAKlB,WAAL;AACH;;AAESmB,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B,eAAKpC,IAAL,CAAUY,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAayB,QAAb,CAAsBC,SAAtB;AACH;;AAESC,QAAAA,WAAW,GAAO;AACxB;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,SAAtB;AACA,cAAIE,IAAa,GAAG,CAAC,CAAD,EAAG,CAAH,EAAK,CAAL,EAAO,CAAP,CAApB;AACA,cAAIC,EAAW,GAAG,CAAC,CAAD,EAAG,CAAH,EAAK,CAAL,EAAO,CAAP,CAAlB;AAEA,cAAIzB,UAAU,GAAG,KAAKC,kBAAL,EAAjB;AACA,cAAIC,QAAQ,GAAG,KAAKC,gBAAL,EAAf;;AAEA,cAAGH,UAAU,GAAG,CAAb,IAAkBE,QAAQ,GAAG,CAAhC,EAAkC;AAC9B;AACH;;AAEDsB,UAAAA,IAAI,CAACxB,UAAD,CAAJ,GAAmB,KAAKM,WAAxB;AACAmB,UAAAA,EAAE,CAACvB,QAAD,CAAF,GAAe,KAAKO,SAApB;AAEA;AAAA;AAAA,4CAAazC,WAAb,GAA2B0D,iBAA3B,CAA6CF,IAA7C,EAAkDC,EAAlD;AACH;;AAxKiD,O;;;;;iBAI9B,I;;;;;;;iBAIF,I;;;;;;;iBAIJ,I;;;;;;;iBAIE,I;;;;;;;iBAGE,I;;;;;;;iBAIA,I", "sourcesContent": ["\nimport { _decorator, Component, Layout, Node, Label, Slider, Toggle } from 'cc';\nconst { ccclass, property } = _decorator;\nimport LoginCommand from \"../../login/LoginCommand\";\nimport MapCommand from \"../MapCommand\";\nimport MapUICommand from \"./MapUICommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('TransformLogic')\nexport default class TransformLogic extends Component {\n\n\n    @property(Layout)\n    fromLayout:Layout = null;\n\n\n    @property(Layout)\n    toLayout:Layout = null;\n\n\n    @property(Node)\n    trNode:Node = null;\n\n\n    @property(Label)\n    trLabel:Label = null;\n\n    @property(Label)\n    rateLabel:Label = null;\n\n    \n    @property(Slider)\n    trSlider:Slider = null;\n\n    protected _nameObj: any = {};\n    protected _keyArr:string[] = []\n    protected _curFromIndex:number = -1;\n    protected _curToIndex:number = -1;\n    protected _fromChange:number = 0;\n    protected _toChange:number = 0;\n\n    protected onLoad():void{\n        \n        this._nameObj = {\n            wood: \"木材x\",\n            iron: \"金属x\",\n            stone: \"石材x\",\n            grain: \"谷物x\",\n        };\n\n        this._keyArr = [\"wood\",\"iron\",\"stone\",\"grain\"]\n\n        EventMgr.on(LogicEvent.upateMyRoleRes, this.initView, this);\n    }\n\n    private getRate() :number {\n        var cityId = MapCommand.getInstance().cityProxy.getMyMainCity().cityId;\n        var _addition = MapUICommand.getInstance().proxy.getMyCityAddition(cityId);\n        var rate = MapUICommand.getInstance().proxy.getTransformRate() + _addition.taxRate;\n        return rate\n    }\n\n    public initView():void{\n        this.updateView();\n        this.updateBtn();\n        \n    }\n\n    protected updateView():void{\n        var roleRes = LoginCommand.getInstance().proxy.getRoleResData();\n        var i = 0;\n        let children_from = this.fromLayout.node.children;\n        for (var key in this._nameObj) {\n            children_from[i].getChildByName(\"New Label\").getComponent(Label).string = this._nameObj[key] + roleRes[key];\n            i++;\n        }\n        i = 0;\n        let children_to = this.toLayout.node.children;\n        for (var key in this._nameObj) {\n            children_to[i].getChildByName(\"New Label\").getComponent(Label).string = this._nameObj[key] + roleRes[key];\n            i++;\n        }\n\n        var rate = this.getRate()\n        this.rateLabel.string = \"1 / \" + (rate/100)\n\n    }\n\n    protected updateBtn():void{\n        this.trSlider.progress = 0.0;\n        this.trNode.active = this._curFromIndex == this._curToIndex?false:true;\n        this.updateLable();\n    }\n\n\n    protected updateLable():void{\n        var from_index = this.getFromSelectIndex();\n        var to_index = this.getToSelectIndex();\n        if (from_index < 0 || to_index < 0){\n            this.trLabel.string = \"\"\n        }else{\n            var roleRes = LoginCommand.getInstance().proxy.getRoleResData();\n            var from_key = this._keyArr[from_index];\n            this._fromChange = Math.round(roleRes[from_key] * this.trSlider.progress)\n            \n            var rate = this.getRate()\n            this._toChange = Math.round(this._fromChange * rate / 100)\n            this.trLabel.string = this._fromChange  + \"/\" + this._toChange\n        }\n    }\n\n    protected getFromSelectIndex():number{\n        let children_from = this.fromLayout.node.children;\n        for(var i = 0;i < children_from.length;i++){\n            if(children_from[i].getComponent(Toggle).isChecked){\n                return i;\n            }\n        }\n\n        return -1;\n    }\n\n\n    protected getToSelectIndex():number{\n        let children_to = this.toLayout.node.children;\n        for(var i = 0;i < children_to.length;i++){\n            if(children_to[i].getComponent(Toggle).isChecked){\n                return i;\n            }\n        }\n\n        return -1;\n    }\n\n\n    protected fromToggleHandle(event:any):void{\n        console.log(\"fromToggleHandle:\",this.getFromSelectIndex())\n        this._curFromIndex = this.getFromSelectIndex();\n        this.updateBtn();\n    }\n\n    protected toToggleHandle(event:any):void{\n        console.log(\"toToggleHandle:\",this.getToSelectIndex())\n        this._curToIndex = this.getToSelectIndex()\n        this.updateBtn();\n    }\n\n\n    protected slideHandle():void{\n        this.updateLable();\n    }\n\n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n    protected onTransForm():void{\n        AudioManager.instance.playClick();\n        let from:number[] = [0,0,0,0];\n        let to:number[] = [0,0,0,0];\n\n        var from_index = this.getFromSelectIndex();\n        var to_index = this.getToSelectIndex();\n\n        if(from_index < 0 || to_index < 0){\n            return\n        }\n\n        from[from_index] = this._fromChange;\n        to[to_index] = this._toChange;\n\n        MapUICommand.getInstance().interiorTransform(from,to);\n    }\n\n}\n"]}