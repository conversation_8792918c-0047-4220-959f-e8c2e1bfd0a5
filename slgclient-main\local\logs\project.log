2025-08-01 07:24:19-log: [Package] menu@1.0.0 enable
2025-08-01 07:24:19-log: [Package] profile@1.0.0 enable
2025-08-01 07:24:19-log: [Package] project@1.0.1 enable
2025-08-01 07:24:19-log: [Package] messages@1.0.0 enable
2025-08-01 07:24:19-log: [Package] program@1.0.0 enable
2025-08-01 07:24:19-log: [Package] device@1.0.1 enable
2025-08-01 07:24:19-log: [Package] ui-kit@1.0.1 enable
2025-08-01 07:24:19-log: [Package] tester@1.0.0 enable
2025-08-01 07:24:19-log: [Package] preferences@1.0.0 enable
2025-08-01 07:24:20-log: Setup mods mgr.: 281.646ms
2025-08-01 07:24:20-log: [Package] engine@1.0.5 enable
2025-08-01 07:24:22-log: [Package] programming@1.0.0 enable
2025-08-01 07:24:22-log: [Package] engine-extends@1.0.0 enable
2025-08-01 07:24:22-log: [Package] asset-db@1.0.0 enable
2025-08-01 07:24:22-log: [Package] console@1.0.0 enable
2025-08-01 07:24:22-log: [Package] scene@1.0.0 enable
2025-08-01 07:24:22-log: [Package] utils@1.0.0 enable
2025-08-01 07:24:22-log: [Package] assets@1.0.0 enable
2025-08-01 07:24:22-log: [Package] inspector@1.0.0 enable
2025-08-01 07:24:22-log: [Package] hierarchy@1.0.0 enable
2025-08-01 07:24:23-log: [Package] preview@1.0.1 enable
2025-08-01 07:24:23-log: [Package] animator@1.0.0 enable
2025-08-01 07:24:24-log: [Package] builder@1.3.0 enable
2025-08-01 07:24:24-log: [Package] node-library@1.0.0 enable
2025-08-01 07:24:24-log: [Package] shortcuts@1.0.1 enable
2025-08-01 07:24:24-log: [Package] package-asset@1.0.0 enable
2025-08-01 07:24:24-log: [Package] reference-image@1.0.0 enable
2025-08-01 07:24:24-log: [Package] animation-graph@1.0.0 enable
2025-08-01 07:24:24-log: [Package] channel-upload-tools@1.0.0 enable
2025-08-01 07:24:24-log: [Package] runtime-dev-tools@1.0.0 enable
2025-08-01 07:24:24-log: [Package] lightmap@1.0.4 enable
2025-08-01 07:24:24-log: [Package] alipay-mini-game@1.0.0 enable
2025-08-01 07:24:24-log: [Package] android@1.0.0 enable
2025-08-01 07:24:24-log: [Package] baidu-mini-game@1.0.0 enable
2025-08-01 07:24:25-log: [Package] bytedance-mini-game@1.0.1 enable
2025-08-01 07:24:25-log: [Package] huawei-agc@1.0.0 enable
2025-08-01 07:24:25-log: [Package] ios@1.0.0 enable
2025-08-01 07:24:25-log: [Package] ios-app-clip@1.0.0 enable
2025-08-01 07:24:25-log: [Package] mac@1.0.0 enable
2025-08-01 07:24:25-log: [Package] native@1.0.0 enable
2025-08-01 07:24:25-log: [Package] ohos@1.0.0 enable
2025-08-01 07:24:25-log: [Package] web-desktop@1.0.0 enable
2025-08-01 07:24:25-log: [Package] web-mobile@1.0.0 enable
2025-08-01 07:24:25-log: [Package] wechatgame@1.0.1 enable
2025-08-01 07:24:25-log: [Package] windows@1.0.0 enable
2025-08-01 07:24:25-log: [Package] xiaomi-quick-game@1.0.0 enable
2025-08-01 07:24:25-log: [Package] cocos-play@1.0.0 enable
2025-08-01 07:24:25-log: [Package] huawei-quick-game@1.0.0 enable
2025-08-01 07:24:25-log: [Package] link-sure@1.0.0 enable
2025-08-01 07:24:25-log: [Package] oppo-mini-game@1.0.0 enable
2025-08-01 07:24:25-log: [Package] qtt@1.0.0 enable
2025-08-01 07:24:25-log: [Package] vivo-mini-game@1.0.0 enable
2025-08-01 07:24:25-log: [Package] cocos-service@3.0.2 enable
2025-08-01 07:24:25-log: [Package] importer@1.0.0 enable
2025-08-01 07:24:26-log: [Package] extension@3.0.13 enable
2025-08-01 07:24:26-log: [Im-plugin] Check im-plugin version.
2025-08-01 07:24:26-log: [Package] im-plugin@3.0.8 enable
2025-08-01 07:24:26-warn: Browserslist: caniuse-lite is outdated. Please run:
npx browserslist@latest --update-db

Why you should do it regularly:
https://github.com/browserslist/browserslist#browsers-data-updating
2025-08-01 07:24:32-log: [Scene] begin init
2025-08-01 07:24:32-log: [Scene] require engine end
2025-08-01 07:24:32-log: [Scene] Serialize end
2025-08-01 07:24:32-log: [Scene] configureStartup end
2025-08-01 07:24:32-log: [Scene] Cocos Creator v3.4.0
2025-08-01 07:24:38-log: [Im-plugin] No online info of im-plugin.
2025-08-01 07:24:38-info: [Scene] Forward render pipeline initialized.
2025-08-01 07:24:39-log: [Scene] openEngine end
2025-08-01 07:24:39-log: [Scene] configureEngine end
2025-08-01 07:24:39-info: [Scene] DragonBones: 5.6.300
Website: http://dragonbones.com/
Source and Demo: https://github.com/DragonBones/
2025-08-01 07:37:50-error: [Preview]Uncaught TypeError: Cannot read properties of null (reading 'getComponentInChildren') in http://localhost:7456/scripting/x/mods/fs/0/assets/scripts/login/LoginDialogController.js
