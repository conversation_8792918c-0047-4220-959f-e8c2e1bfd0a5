{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts"], "names": ["_decorator", "Component", "Prefab", "ToggleContainer", "instantiate", "AudioManager", "LogicEvent", "EventMgr", "ccclass", "property", "GeneralInfoLogic", "onLoad", "on", "updateOneGenerals", "updateOnce", "des", "generalDesPrefab", "parent", "node", "active", "comp", "generalComposePrefab", "addd", "generalAddPrefab", "_nodeList", "curData", "setData", "_cfgData", "onDestroy", "targetOff", "onClickClose", "instance", "playClick", "cfgData", "_currData", "setIndex", "_curIndex", "index", "allVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toggleItems", "isChecked", "logicNameArr", "com", "getComponent", "i", "length", "selectHandle", "event", "other"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,e,OAAAA,e;AAAuBC,MAAAA,W,OAAAA,W;;AACtDC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGTU,gB,WADpBF,OAAO,CAAC,kBAAD,C,UAIHC,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,UAIRO,QAAQ,CAACP,MAAD,C,UAIRO,QAAQ,CAACN,eAAD,C,oCAfb,MACqBO,gBADrB,SAC+CT,SAD/C,CACyD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,6CAkB7B,IAlB6B;;AAAA,4CAmB9B,IAnB8B;;AAAA,6CAqB1B,CArB0B;;AAAA,6CAsB1B,EAtB0B;AAAA;;AAwB3CU,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,iBAAvB,EAA0C,KAAKC,UAA/C,EAA2D,IAA3D;AAEA,cAAIC,GAAG,GAAGX,WAAW,CAAC,KAAKY,gBAAN,CAArB;AACAD,UAAAA,GAAG,CAACE,MAAJ,GAAa,KAAKC,IAAlB;AACAH,UAAAA,GAAG,CAACI,MAAJ,GAAa,KAAb;AAGA,cAAIC,IAAI,GAAGhB,WAAW,CAAC,KAAKiB,oBAAN,CAAtB;AACAD,UAAAA,IAAI,CAACH,MAAL,GAAc,KAAKC,IAAnB;AACAE,UAAAA,IAAI,CAACD,MAAL,GAAc,KAAd;AAGA,cAAIG,IAAI,GAAGlB,WAAW,CAAC,KAAKmB,gBAAN,CAAtB;AACAD,UAAAA,IAAI,CAACL,MAAL,GAAc,KAAKC,IAAnB;AACAI,UAAAA,IAAI,CAACH,MAAL,GAAc,KAAd;AAEA,eAAKK,SAAL,CAAe,CAAf,IAAoBT,GAApB;AACA,eAAKS,SAAL,CAAe,CAAf,IAAoBJ,IAApB;AACA,eAAKI,SAAL,CAAe,CAAf,IAAoBF,IAApB;AACH;;AAESR,QAAAA,UAAU,CAACW,OAAD,EAAkB;AAClC,eAAKC,OAAL,CAAa,KAAKC,QAAlB,EAA2BF,OAA3B;AACH;;AAGSG,QAAAA,SAAS,GAAO;AACtB,eAAKJ,SAAL,GAAiB,EAAjB;AACA;AAAA;AAAA,oCAASK,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,eAAKd,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAIMO,QAAAA,OAAO,CAACO,OAAD,EAAaR,OAAb,EAA8B;AACxC,eAAKS,SAAL,GAAiBT,OAAjB;AACA,eAAKE,QAAL,GAAgBM,OAAhB;AACA,eAAKE,QAAL,CAAc,KAAKC,SAAnB;AACH;;AAESD,QAAAA,QAAQ,CAACE,KAAY,GAAG,CAAhB,EAAuB;AACrC,eAAKD,SAAL,GAAiBC,KAAjB;AACA,eAAKC,UAAL;AACA,eAAKd,SAAL,CAAea,KAAf,EAAsBlB,MAAtB,GAA+B,IAA/B;AACA,eAAKoB,sBAAL,CAA4BC,WAA5B,CAAwCH,KAAxC,EAA+CI,SAA/C,GAA2D,IAA3D;AAEA,cAAIC,YAAqB,GAAG,CAAC,iBAAD,EAAmB,qBAAnB,EAAyC,mBAAzC,CAA5B;;AACA,cAAIC,GAAG,GAAG,KAAKnB,SAAL,CAAea,KAAf,EAAsBO,YAAtB,CAAmCF,YAAY,CAACL,KAAD,CAA/C,CAAV;;AACA,cAAGM,GAAH,EAAO;AACHA,YAAAA,GAAG,CAACjB,OAAJ,CAAY,KAAKC,QAAjB,EAA2B,KAAKO,SAAhC;AACH;AACJ;;AAGSI,QAAAA,UAAU,GAAO;AACvB,eAAI,IAAIO,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAG,KAAKrB,SAAL,CAAesB,MAAlC,EAA0CD,CAAC,EAA3C,EAA8C;AAC1C,iBAAKrB,SAAL,CAAeqB,CAAf,EAAkB1B,MAAlB,GAA2B,KAA3B;AACH;AACJ;;AAES4B,QAAAA,YAAY,CAACC,KAAD,EAAWC,KAAX,EAA0B;AAC5C;AACA;AAAA;AAAA,4CAAalB,QAAb,CAAsBC,SAAtB;AACA,eAAKG,QAAL,CAAcc,KAAd;AACH;;AA7FoD,O;;;;;iBAI1B,I;;;;;;;iBAGI,I;;;;;;;iBAIJ,I;;;;;;;iBAIe,I", "sourcesContent": ["\nimport { _decorator, Component, Prefab, ToggleContainer, Node, instantiate } from 'cc';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\nimport { EventMgr } from '../../utils/EventMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('GeneralInfoLogic')\nexport default class GeneralInfoLogic  extends Component {\n\n\n    @property(Prefab)\n    generalDesPrefab: Prefab = null;\n\n    @property(Prefab)\n    generalComposePrefab: Prefab = null;\n\n\n    @property(Prefab)\n    generalAddPrefab: Prefab = null;\n\n\n    @property(ToggleContainer)\n    generalToggleContainer: ToggleContainer = null;\n\n    \n    private _currData:any = null;\n    private _cfgData:any = null;\n\n    private _curIndex:number = 0;\n    private _nodeList:Node[] = [];\n\n    protected onLoad():void{\n        EventMgr.on(LogicEvent.updateOneGenerals, this.updateOnce, this); \n\n        var des = instantiate(this.generalDesPrefab);\n        des.parent = this.node;\n        des.active = false;\n\n\n        var comp = instantiate(this.generalComposePrefab);\n        comp.parent = this.node;\n        comp.active = false;\n\n\n        var addd = instantiate(this.generalAddPrefab);\n        addd.parent = this.node;\n        addd.active = false;\n\n        this._nodeList[0] = des;\n        this._nodeList[1] = comp;\n        this._nodeList[2] = addd;\n    }\n\n    protected updateOnce(curData:any):void{\n        this.setData(this._cfgData,curData)\n    }\n   \n\n    protected onDestroy():void{\n        this._nodeList = []\n        EventMgr.targetOff(this);\n    }\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n    }\n\n\n\n    public setData(cfgData:any,curData:any):void{\n        this._currData = curData;\n        this._cfgData = cfgData;\n        this.setIndex(this._curIndex);\n    }\n\n    protected setIndex(index:number = 0):void{\n        this._curIndex = index;\n        this.allVisible();\n        this._nodeList[index].active = true;\n        this.generalToggleContainer.toggleItems[index].isChecked = true;\n\n        let logicNameArr:string[] = [\"GeneralDesLogic\",\"GeneralComposeLogic\",\"GeneralAddPrLogic\"]\n        let com = this._nodeList[index].getComponent(logicNameArr[index]);\n        if(com){\n            com.setData(this._cfgData, this._currData);\n        }\n    }\n\n\n    protected allVisible():void{\n        for(var i = 0; i < this._nodeList.length; i++){\n            this._nodeList[i].active = false;\n        }\n    }\n\n    protected selectHandle(event:any,other:any):void{\n        // console.log(\"event:\",event,other)\n        AudioManager.instance.playClick();\n        this.setIndex(other)\n    }\n\n\n}\n"]}