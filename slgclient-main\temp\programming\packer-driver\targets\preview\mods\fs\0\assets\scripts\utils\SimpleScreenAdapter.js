System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Canvas, view, sys, director, screen, ResolutionPolicy, GameConfig, _dec, _class, _class2, _temp, _crd, ccclass, property, SimpleScreenAdapter;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../config/GameConfig", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Canvas = _cc.Canvas;
      view = _cc.view;
      sys = _cc.sys;
      director = _cc.director;
      screen = _cc.screen;
      ResolutionPolicy = _cc.ResolutionPolicy;
    }, function (_unresolved_2) {
      GameConfig = _unresolved_2.GameConfig;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e5a91fa6q5GR5/sTceefFyM", "SimpleScreenAdapter", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 简单屏幕适配器
       * 专门解决点击偏移问题的轻量级方案
       */

      _export("SimpleScreenAdapter", SimpleScreenAdapter = (_dec = ccclass('SimpleScreenAdapter'), _dec(_class = (_temp = _class2 = class SimpleScreenAdapter extends Component {
        static get instance() {
          return SimpleScreenAdapter._instance;
        }

        onLoad() {
          SimpleScreenAdapter._instance = this;
          console.log('[SimpleScreenAdapter] 简单屏幕适配器初始化');
        }

        onDestroy() {
          if (SimpleScreenAdapter._instance === this) {
            SimpleScreenAdapter._instance = null;
          }
        }
        /**
         * 初始化屏幕适配
         */


        init() {
          console.log('[SimpleScreenAdapter] 开始初始化屏幕适配...'); // 获取屏幕信息

          var windowSize = screen.windowSize;
          var screenWidth = windowSize.width;
          var screenHeight = windowSize.height;
          var screenRatio = screenWidth / screenHeight;
          console.log("[SimpleScreenAdapter] \u5C4F\u5E55\u5C3A\u5BF8: " + screenWidth + "x" + screenHeight);
          console.log("[SimpleScreenAdapter] \u5C4F\u5E55\u6BD4\u4F8B: " + screenRatio.toFixed(3)); // 使用SHOW_ALL策略，确保内容完整显示且不变形

          var designWidth = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.baseWidth;
          var designHeight = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.baseHeight;
          view.setDesignResolutionSize(designWidth, designHeight, ResolutionPolicy.SHOW_ALL);
          console.log("[SimpleScreenAdapter] \u8BBE\u8BA1\u5206\u8FA8\u7387: " + designWidth + "x" + designHeight + " (SHOW_ALL)"); // 修复所有Canvas设置

          this.fixAllCanvas(); // 监听场景切换

          director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this); // 监听屏幕尺寸变化

          this.setupResizeListener(); // 打印最终信息

          this.scheduleOnce(() => {
            this.printFinalInfo();
          }, 0.1);
          console.log('[SimpleScreenAdapter] 屏幕适配初始化完成');
        }
        /**
         * 场景启动后的回调
         */


        onSceneLaunched(scene) {
          this.scheduleOnce(() => {
            this.fixAllCanvas();
          }, 0);
        }
        /**
         * 修复所有Canvas设置
         */


        fixAllCanvas() {
          var scene = director.getScene();
          if (!scene) return;
          var canvasComponents = scene.getComponentsInChildren(Canvas);
          canvasComponents.forEach((canvas, index) => {
            console.log("[SimpleScreenAdapter] \u4FEE\u590DCanvas[" + index + "]"); // 关键设置：禁用自动对齐屏幕

            canvas.alignCanvasWithScreen = false; // 确保Canvas位置为原点

            canvas.node.setPosition(0, 0, 0);
            console.log("[SimpleScreenAdapter] Canvas[" + index + "] \u4FEE\u590D\u5B8C\u6210: alignCanvasWithScreen=false, position=(0,0,0)");
          });
        }
        /**
         * 设置屏幕尺寸变化监听
         */


        setupResizeListener() {
          // 监听窗口尺寸变化
          window.addEventListener('resize', () => {
            this.scheduleOnce(() => {
              console.log('[SimpleScreenAdapter] 检测到窗口尺寸变化，重新适配...');
              this.handleResize();
            }, 0.1);
          }); // 监听屏幕方向变化（移动设备）

          if (sys.isMobile) {
            window.addEventListener('orientationchange', () => {
              this.scheduleOnce(() => {
                console.log('[SimpleScreenAdapter] 检测到屏幕方向变化，重新适配...');
                this.handleResize();
              }, 0.2);
            });
          }
        }
        /**
         * 处理屏幕尺寸变化
         */


        handleResize() {
          // 重新初始化适配
          this.init();
        }
        /**
         * 打印最终适配信息
         */


        printFinalInfo() {
          if (!(_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.enableDebugInfo) return;
          var windowSize = screen.windowSize;
          var visibleSize = view.getVisibleSize();
          var visibleOrigin = view.getVisibleOrigin();
          var designSize = view.getDesignResolutionSize();
          console.log('=== 简单屏幕适配最终信息 ===');
          console.log("\u7A97\u53E3\u5C3A\u5BF8: " + windowSize.width + "x" + windowSize.height);
          console.log("\u8BBE\u8BA1\u5206\u8FA8\u7387: " + designSize.width + "x" + designSize.height);
          console.log("\u53EF\u89C6\u533A\u57DF: " + visibleSize.width.toFixed(1) + "x" + visibleSize.height.toFixed(1));
          console.log("\u53EF\u89C6\u539F\u70B9: (" + visibleOrigin.x.toFixed(1) + ", " + visibleOrigin.y.toFixed(1) + ")"); // 计算缩放比例

          var scaleX = visibleSize.width / designSize.width;
          var scaleY = visibleSize.height / designSize.height;
          console.log("\u7F29\u653E\u6BD4\u4F8B: X=" + scaleX.toFixed(3) + ", Y=" + scaleY.toFixed(3)); // 检查是否有异常

          if (Math.abs(visibleOrigin.x) > 1 || Math.abs(visibleOrigin.y) > 1) {
            console.warn("\u26A0\uFE0F \u53EF\u89C6\u539F\u70B9\u504F\u79FB\u5F02\u5E38: (" + visibleOrigin.x.toFixed(1) + ", " + visibleOrigin.y.toFixed(1) + ")");
          }

          if (scaleX < 0.5 || scaleY < 0.5 || scaleX > 3 || scaleY > 3) {
            console.warn("\u26A0\uFE0F \u7F29\u653E\u6BD4\u4F8B\u5F02\u5E38: X=" + scaleX.toFixed(3) + ", Y=" + scaleY.toFixed(3));
          }

          console.log('============================');
        }
        /**
         * 获取当前适配信息
         */


        getAdaptInfo() {
          var windowSize = screen.windowSize;
          var visibleSize = view.getVisibleSize();
          var visibleOrigin = view.getVisibleOrigin();
          var designSize = view.getDesignResolutionSize();
          return {
            windowSize,
            designSize,
            visibleSize,
            visibleOrigin,
            scaleX: visibleSize.width / designSize.width,
            scaleY: visibleSize.height / designSize.height
          };
        }
        /**
         * 手动触发适配信息打印
         */


        printDebugInfo() {
          this.printFinalInfo();
        }

      }, _defineProperty(_class2, "_instance", null), _temp)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=SimpleScreenAdapter.js.map