{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts"], "names": ["_decorator", "Component", "Node", "EditBox", "<PERSON><PERSON>", "Label", "Color", "Toggle", "AudioManager", "EventMgr", "LogicEvent", "ccclass", "property", "LoginDialogController", "onLoad", "console", "log", "initializeDialog", "setupEventListeners", "start", "phoneStatusLabel", "node", "active", "codeStatusLabel", "phoneInput", "max<PERSON><PERSON><PERSON>", "codeInput", "getCodeButton", "on", "EventType", "CLICK", "onGetCodeButtonClick", "loginButton", "onLoginButtonClick", "maskNode", "TOUCH_END", "onMaskClick", "onPhoneInputChanged", "onCodeInputChanged", "phone", "string", "numericPhone", "replace", "code", "numericCode", "instance", "playClick", "trim", "validatePhone", "startCodeCountdown", "emit", "showToast", "validateCode", "validateAgreement", "performLogin", "destroy", "showPhoneStatus", "length", "test", "showCodeStatus", "agreementToggle", "isChecked", "scheduleOnce", "message", "color", "codeCountdown", "updateCodeButtonText", "countdownTimer", "setInterval", "stopCodeCountdown", "clearInterval", "label", "getComponentInChildren", "interactable", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAC5DC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;AAE9B;AACA;AACA;AACA;;uCAEaa,qB,WADZF,OAAO,CAAC,uBAAD,C,UAGHC,QAAQ,CAACT,OAAD,C,UAGRS,QAAQ,CAACT,OAAD,C,UAGRS,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACV,IAAD,C,UAGRU,QAAQ,CAACL,MAAD,C,UAGRK,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,oCAxBb,MACaQ,qBADb,SAC2CZ,SAD3C,CACqD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,iDA0BjB,CA1BiB;;AAAA,kDA2BhB,CA3BgB;AAAA;;AA6BjDa,QAAAA,MAAM,GAAG;AACLC,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACA,eAAKC,gBAAL;AACA,eAAKC,mBAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJJ,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,gBAAgB,GAAS;AAC7B;AACA,cAAI,KAAKG,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBC,IAAtB,CAA2BC,MAA3B,GAAoC,KAApC;AACH;;AACD,cAAI,KAAKC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBF,IAArB,CAA0BC,MAA1B,GAAmC,KAAnC;AACH,WAP4B,CAS7B;;;AACA,cAAI,KAAKE,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBC,SAAhB,GAA4B,EAA5B;AACH;;AACD,cAAI,KAAKC,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeD,SAAf,GAA2B,CAA3B;AACH;AACJ;AAED;AACJ;AACA;;;AACYP,QAAAA,mBAAmB,GAAS;AAChC,cAAI,KAAKS,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBN,IAAnB,CAAwBO,EAAxB,CAA2BxB,MAAM,CAACyB,SAAP,CAAiBC,KAA5C,EAAmD,KAAKC,oBAAxD,EAA8E,IAA9E;AACH;;AAED,cAAI,KAAKC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBX,IAAjB,CAAsBO,EAAtB,CAAyBxB,MAAM,CAACyB,SAAP,CAAiBC,KAA1C,EAAiD,KAAKG,kBAAtD,EAA0E,IAA1E;AACH,WAP+B,CAShC;;;AACA,cAAI,KAAKC,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAcN,EAAd,CAAiB1B,IAAI,CAAC2B,SAAL,CAAeM,SAAhC,EAA2C,KAAKC,WAAhD,EAA6D,IAA7D;AACH;;AAED,cAAI,KAAKZ,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBH,IAAhB,CAAqBO,EAArB,CAAwB,cAAxB,EAAwC,KAAKS,mBAA7C,EAAkE,IAAlE;AACH;;AAED,cAAI,KAAKX,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeL,IAAf,CAAoBO,EAApB,CAAuB,cAAvB,EAAuC,KAAKU,kBAA5C,EAAgE,IAAhE;AACH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,mBAAmB,GAAS;AAChC,cAAME,KAAK,GAAG,KAAKf,UAAL,CAAgBgB,MAA9B,CADgC,CAGhC;;AACA,cAAMC,YAAY,GAAGF,KAAK,CAACG,OAAN,CAAc,SAAd,EAAyB,EAAzB,CAArB;;AACA,cAAID,YAAY,KAAKF,KAArB,EAA4B;AACxB,iBAAKf,UAAL,CAAgBgB,MAAhB,GAAyBC,YAAzB;AACH,WAP+B,CAShC;;;AACA,cAAI,KAAKrB,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBC,IAAtB,CAA2BC,MAA3B,GAAoC,KAApC;AACH;AACJ;AAED;AACJ;AACA;;;AACYgB,QAAAA,kBAAkB,GAAS;AAC/B,cAAMK,IAAI,GAAG,KAAKjB,SAAL,CAAec,MAA5B,CAD+B,CAG/B;;AACA,cAAMI,WAAW,GAAGD,IAAI,CAACD,OAAL,CAAa,SAAb,EAAwB,EAAxB,CAApB;;AACA,cAAIE,WAAW,KAAKD,IAApB,EAA0B;AACtB,iBAAKjB,SAAL,CAAec,MAAf,GAAwBI,WAAxB;AACH,WAP8B,CAS/B;;;AACA,cAAI,KAAKrB,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBF,IAArB,CAA0BC,MAA1B,GAAmC,KAAnC;AACH;AACJ;AAED;AACJ;AACA;;;AACYS,QAAAA,oBAAoB,GAAS;AACjChB,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AAEA;AAAA;AAAA,4CAAa6B,QAAb,CAAsBC,SAAtB;AAEA,cAAMP,KAAK,GAAG,KAAKf,UAAL,CAAgBgB,MAAhB,CAAuBO,IAAvB,EAAd,CALiC,CAOjC;;AACA,cAAI,CAAC,KAAKC,aAAL,CAAmBT,KAAnB,CAAL,EAAgC;AAC5B;AACH,WAVgC,CAYjC;;;AACA,eAAKU,kBAAL,GAbiC,CAejC;;AACAlC,UAAAA,OAAO,CAACC,GAAR,uDAA4CuB,KAA5C;AACA;AAAA;AAAA,oCAASW,IAAT,CAAc;AAAA;AAAA,wCAAWC,SAAzB,kDAA+CZ,KAA/C;AACH;AAED;AACJ;AACA;;;AACYN,QAAAA,kBAAkB,GAAS;AAC/BlB,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ;AAEA;AAAA;AAAA,4CAAa6B,QAAb,CAAsBC,SAAtB;AAEA,cAAMP,KAAK,GAAG,KAAKf,UAAL,CAAgBgB,MAAhB,CAAuBO,IAAvB,EAAd;AACA,cAAMJ,IAAI,GAAG,KAAKjB,SAAL,CAAec,MAAf,CAAsBO,IAAtB,EAAb,CAN+B,CAQ/B;;AACA,cAAI,CAAC,KAAKC,aAAL,CAAmBT,KAAnB,CAAD,IAA8B,CAAC,KAAKa,YAAL,CAAkBT,IAAlB,CAA/B,IAA0D,CAAC,KAAKU,iBAAL,EAA/D,EAAyF;AACrF;AACH,WAX8B,CAa/B;;;AACA,eAAKC,YAAL,CAAkBf,KAAlB,EAAyBI,IAAzB;AACH;AAED;AACJ;AACA;;;AACYP,QAAAA,WAAW,GAAS;AACxBrB,UAAAA,OAAO,CAACC,GAAR,CAAY,wCAAZ;AAEA;AAAA;AAAA,4CAAa6B,QAAb,CAAsBC,SAAtB;AACA,eAAKzB,IAAL,CAAUkC,OAAV;AACH;AAED;AACJ;AACA;;;AACYP,QAAAA,aAAa,CAACT,KAAD,EAAyB;AAC1C,cAAI,CAACA,KAAL,EAAY;AACR,iBAAKiB,eAAL,CAAqB,QAArB,EAA+B,IAAIlD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA/B;AACA,mBAAO,KAAP;AACH;;AAED,cAAIiC,KAAK,CAACkB,MAAN,KAAiB,EAArB,EAAyB;AACrB,iBAAKD,eAAL,CAAqB,aAArB,EAAoC,IAAIlD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAApC;AACA,mBAAO,KAAP;AACH;;AAED,cAAI,CAAC,gBAAgBoD,IAAhB,CAAqBnB,KAArB,CAAL,EAAkC;AAC9B,iBAAKiB,eAAL,CAAqB,aAArB,EAAoC,IAAIlD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAApC;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACY8C,QAAAA,YAAY,CAACT,IAAD,EAAwB;AACxC,cAAI,CAACA,IAAL,EAAW;AACP,iBAAKgB,cAAL,CAAoB,QAApB,EAA8B,IAAIrD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA9B;AACA,mBAAO,KAAP;AACH;;AAED,cAAIqC,IAAI,CAACc,MAAL,KAAgB,CAApB,EAAuB;AACnB,iBAAKE,cAAL,CAAoB,YAApB,EAAkC,IAAIrD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAlC;AACA,mBAAO,KAAP;AACH;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACY+C,QAAAA,iBAAiB,GAAY;AACjC,cAAI,CAAC,KAAKO,eAAN,IAAyB,CAAC,KAAKA,eAAL,CAAqBC,SAAnD,EAA8D;AAC1D;AAAA;AAAA,sCAASX,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,UAApC;AACA,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYG,QAAAA,YAAY,CAACf,KAAD,EAAgBI,IAAhB,EAAoC;AACpD5B,UAAAA,OAAO,CAACC,GAAR,2EAAiDuB,KAAjD,6BAA+DI,IAA/D,EADoD,CAGpD;AACA;;AACA;AAAA;AAAA,oCAASO,IAAT,CAAc;AAAA;AAAA,wCAAWC,SAAzB,EAAoC,OAApC,EALoD,CAOpD;;AACA,eAAKW,YAAL,CAAkB,MAAM;AACpB,iBAAKzC,IAAL,CAAUkC,OAAV;AACH,WAFD,EAEG,CAFH;AAGH;AAED;AACJ;AACA;;;AACYC,QAAAA,eAAe,CAACO,OAAD,EAAkBC,KAAlB,EAAsC;AACzD,cAAI,KAAK5C,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBoB,MAAtB,GAA+BuB,OAA/B;AACA,iBAAK3C,gBAAL,CAAsB4C,KAAtB,GAA8BA,KAA9B;AACA,iBAAK5C,gBAAL,CAAsBC,IAAtB,CAA2BC,MAA3B,GAAoC,IAApC;AACH;AACJ;AAED;AACJ;AACA;;;AACYqC,QAAAA,cAAc,CAACI,OAAD,EAAkBC,KAAlB,EAAsC;AACxD,cAAI,KAAKzC,eAAT,EAA0B;AACtB,iBAAKA,eAAL,CAAqBiB,MAArB,GAA8BuB,OAA9B;AACA,iBAAKxC,eAAL,CAAqByC,KAArB,GAA6BA,KAA7B;AACA,iBAAKzC,eAAL,CAAqBF,IAArB,CAA0BC,MAA1B,GAAmC,IAAnC;AACH;AACJ;AAED;AACJ;AACA;;;AACY2B,QAAAA,kBAAkB,GAAS;AAC/B,eAAKgB,aAAL,GAAqB,EAArB;AACA,eAAKC,oBAAL;AAEA,eAAKC,cAAL,GAAsBC,WAAW,CAAC,MAAM;AACpC,iBAAKH,aAAL;AACA,iBAAKC,oBAAL;;AAEA,gBAAI,KAAKD,aAAL,IAAsB,CAA1B,EAA6B;AACzB,mBAAKI,iBAAL;AACH;AACJ,WAPgC,EAO9B,IAP8B,CAAjC;AAQH;AAED;AACJ;AACA;;;AACYA,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,KAAKF,cAAT,EAAyB;AACrBG,YAAAA,aAAa,CAAC,KAAKH,cAAN,CAAb;AACA,iBAAKA,cAAL,GAAsB,CAAtB;AACH;;AACD,eAAKF,aAAL,GAAqB,CAArB;AACA,eAAKC,oBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,oBAAoB,GAAS;AACjC,cAAI,CAAC,KAAKvC,aAAV,EAAyB;AAEzB,cAAM4C,KAAK,GAAG,KAAK5C,aAAL,CAAmBN,IAAnB,CAAwBmD,sBAAxB,CAA+CnE,KAA/C,CAAd;;AACA,cAAIkE,KAAJ,EAAW;AACP,gBAAI,KAAKN,aAAL,GAAqB,CAAzB,EAA4B;AACxBM,cAAAA,KAAK,CAAC/B,MAAN,GAAkB,KAAKyB,aAAvB;AACA,mBAAKtC,aAAL,CAAmB8C,YAAnB,GAAkC,KAAlC;AACH,aAHD,MAGO;AACHF,cAAAA,KAAK,CAAC/B,MAAN,GAAe,OAAf;AACA,mBAAKb,aAAL,CAAmB8C,YAAnB,GAAkC,IAAlC;AACH;AACJ;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR;AACA,eAAKL,iBAAL,GAFQ,CAIR;;AACA,cAAI,KAAK1C,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBN,IAAnB,CAAwBsD,GAAxB,CAA4BvE,MAAM,CAACyB,SAAP,CAAiBC,KAA7C,EAAoD,KAAKC,oBAAzD,EAA+E,IAA/E;AACH;;AACD,cAAI,KAAKC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBX,IAAjB,CAAsBsD,GAAtB,CAA0BvE,MAAM,CAACyB,SAAP,CAAiBC,KAA3C,EAAkD,KAAKG,kBAAvD,EAA2E,IAA3E;AACH;;AACD,cAAI,KAAKC,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAcyC,GAAd,CAAkBzE,IAAI,CAAC2B,SAAL,CAAeM,SAAjC,EAA4C,KAAKC,WAAjD,EAA8D,IAA9D;AACH;;AACD,cAAI,KAAKZ,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBH,IAAhB,CAAqBsD,GAArB,CAAyB,cAAzB,EAAyC,KAAKtC,mBAA9C,EAAmE,IAAnE;AACH;;AACD,cAAI,KAAKX,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeL,IAAf,CAAoBsD,GAApB,CAAwB,cAAxB,EAAwC,KAAKrC,kBAA7C,EAAiE,IAAjE;AACH;;AAEDvB,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACH;;AA5UgD,O;;;;;iBAG3B,I;;;;;;;iBAGD,I;;;;;;;iBAGG,I;;;;;;;iBAGF,I;;;;;;;iBAGL,I;;;;;;;iBAGS,I;;;;;;;iBAGA,I;;;;;;;iBAGD,I", "sourcesContent": ["import { _decorator, Component, Node, EditBox, Button, Label, Color, Toggle } from 'cc';\nimport { AudioManager } from '../common/AudioManager';\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 登录对话框控制器\n * 处理手机号登录、验证码获取等功能\n */\n@ccclass('LoginDialogController')\nexport class LoginDialogController extends Component {\n    \n    @property(EditBox)\n    phoneInput: EditBox = null!;\n    \n    @property(EditBox)\n    codeInput: EditBox = null!;\n    \n    @property(Button)\n    getCodeButton: Button = null!;\n    \n    @property(Button)\n    loginButton: Button = null!;\n    \n    @property(Node)\n    maskNode: Node = null!;\n    \n    @property(Toggle)\n    agreementToggle: Toggle = null!;\n    \n    @property(Label)\n    phoneStatusLabel: Label = null!;\n    \n    @property(Label)\n    codeStatusLabel: Label = null!;\n    \n    private codeCountdown: number = 0;\n    private countdownTimer: number = 0;\n\n    onLoad() {\n        console.log('[LoginDialogController] 登录对话框控制器加载');\n        this.initializeDialog();\n        this.setupEventListeners();\n    }\n\n    start() {\n        console.log('[LoginDialogController] 登录对话框控制器启动');\n    }\n\n    /**\n     * 初始化对话框\n     */\n    private initializeDialog(): void {\n        // 隐藏提示标签\n        if (this.phoneStatusLabel) {\n            this.phoneStatusLabel.node.active = false;\n        }\n        if (this.codeStatusLabel) {\n            this.codeStatusLabel.node.active = false;\n        }\n        \n        // 设置输入框限制\n        if (this.phoneInput) {\n            this.phoneInput.maxLength = 11;\n        }\n        if (this.codeInput) {\n            this.codeInput.maxLength = 6;\n        }\n    }\n\n    /**\n     * 设置事件监听器\n     */\n    private setupEventListeners(): void {\n        if (this.getCodeButton) {\n            this.getCodeButton.node.on(Button.EventType.CLICK, this.onGetCodeButtonClick, this);\n        }\n\n        if (this.loginButton) {\n            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);\n        }\n\n        // 设置遮罩点击事件（点击对话框外区域关闭）\n        if (this.maskNode) {\n            this.maskNode.on(Node.EventType.TOUCH_END, this.onMaskClick, this);\n        }\n\n        if (this.phoneInput) {\n            this.phoneInput.node.on('text-changed', this.onPhoneInputChanged, this);\n        }\n\n        if (this.codeInput) {\n            this.codeInput.node.on('text-changed', this.onCodeInputChanged, this);\n        }\n    }\n\n    /**\n     * 手机号输入变化事件\n     */\n    private onPhoneInputChanged(): void {\n        const phone = this.phoneInput.string;\n        \n        // 只允许输入数字\n        const numericPhone = phone.replace(/[^0-9]/g, '');\n        if (numericPhone !== phone) {\n            this.phoneInput.string = numericPhone;\n        }\n        \n        // 隐藏手机号状态提示\n        if (this.phoneStatusLabel) {\n            this.phoneStatusLabel.node.active = false;\n        }\n    }\n\n    /**\n     * 验证码输入变化事件\n     */\n    private onCodeInputChanged(): void {\n        const code = this.codeInput.string;\n        \n        // 只允许输入数字\n        const numericCode = code.replace(/[^0-9]/g, '');\n        if (numericCode !== code) {\n            this.codeInput.string = numericCode;\n        }\n        \n        // 隐藏验证码状态提示\n        if (this.codeStatusLabel) {\n            this.codeStatusLabel.node.active = false;\n        }\n    }\n\n    /**\n     * 获取验证码按钮点击事件\n     */\n    private onGetCodeButtonClick(): void {\n        console.log('[LoginDialogController] 获取验证码按钮被点击');\n        \n        AudioManager.instance.playClick();\n        \n        const phone = this.phoneInput.string.trim();\n        \n        // 验证手机号\n        if (!this.validatePhone(phone)) {\n            return;\n        }\n        \n        // 开始倒计时\n        this.startCodeCountdown();\n        \n        // 这里应该调用获取验证码的API\n        console.log(`[LoginDialogController] 向手机号 ${phone} 发送验证码`);\n        EventMgr.emit(LogicEvent.showToast, `验证码已发送到 ${phone}`);\n    }\n\n    /**\n     * 登录按钮点击事件\n     */\n    private onLoginButtonClick(): void {\n        console.log('[LoginDialogController] 登录按钮被点击');\n        \n        AudioManager.instance.playClick();\n        \n        const phone = this.phoneInput.string.trim();\n        const code = this.codeInput.string.trim();\n        \n        // 验证输入\n        if (!this.validatePhone(phone) || !this.validateCode(code) || !this.validateAgreement()) {\n            return;\n        }\n        \n        // 执行登录\n        this.performLogin(phone, code);\n    }\n\n    /**\n     * 遮罩点击事件（点击对话框外区域关闭）\n     */\n    private onMaskClick(): void {\n        console.log('[LoginDialogController] 点击对话框外区域，关闭对话框');\n\n        AudioManager.instance.playClick();\n        this.node.destroy();\n    }\n\n    /**\n     * 验证手机号\n     */\n    private validatePhone(phone: string): boolean {\n        if (!phone) {\n            this.showPhoneStatus('请输入手机号', new Color(255, 100, 100, 255));\n            return false;\n        }\n        \n        if (phone.length !== 11) {\n            this.showPhoneStatus('手机号必须是11位数字', new Color(255, 100, 100, 255));\n            return false;\n        }\n        \n        if (!/^1[3-9]\\d{9}$/.test(phone)) {\n            this.showPhoneStatus('请输入正确的手机号格式', new Color(255, 100, 100, 255));\n            return false;\n        }\n        \n        return true;\n    }\n\n    /**\n     * 验证验证码\n     */\n    private validateCode(code: string): boolean {\n        if (!code) {\n            this.showCodeStatus('请输入验证码', new Color(255, 100, 100, 255));\n            return false;\n        }\n        \n        if (code.length !== 6) {\n            this.showCodeStatus('验证码必须是6位数字', new Color(255, 100, 100, 255));\n            return false;\n        }\n        \n        return true;\n    }\n\n    /**\n     * 验证协议勾选\n     */\n    private validateAgreement(): boolean {\n        if (!this.agreementToggle || !this.agreementToggle.isChecked) {\n            EventMgr.emit(LogicEvent.showToast, '请先同意用户协议');\n            return false;\n        }\n        return true;\n    }\n\n    /**\n     * 执行登录\n     */\n    private performLogin(phone: string, code: string): void {\n        console.log(`[LoginDialogController] 开始登录: 手机号=${phone}, 验证码=${code}`);\n        \n        // 这里应该调用登录API\n        // 模拟登录成功\n        EventMgr.emit(LogicEvent.showToast, '登录成功！');\n        \n        // 关闭对话框\n        this.scheduleOnce(() => {\n            this.node.destroy();\n        }, 1);\n    }\n\n    /**\n     * 显示手机号状态\n     */\n    private showPhoneStatus(message: string, color: Color): void {\n        if (this.phoneStatusLabel) {\n            this.phoneStatusLabel.string = message;\n            this.phoneStatusLabel.color = color;\n            this.phoneStatusLabel.node.active = true;\n        }\n    }\n\n    /**\n     * 显示验证码状态\n     */\n    private showCodeStatus(message: string, color: Color): void {\n        if (this.codeStatusLabel) {\n            this.codeStatusLabel.string = message;\n            this.codeStatusLabel.color = color;\n            this.codeStatusLabel.node.active = true;\n        }\n    }\n\n    /**\n     * 开始验证码倒计时\n     */\n    private startCodeCountdown(): void {\n        this.codeCountdown = 60;\n        this.updateCodeButtonText();\n        \n        this.countdownTimer = setInterval(() => {\n            this.codeCountdown--;\n            this.updateCodeButtonText();\n            \n            if (this.codeCountdown <= 0) {\n                this.stopCodeCountdown();\n            }\n        }, 1000);\n    }\n\n    /**\n     * 停止验证码倒计时\n     */\n    private stopCodeCountdown(): void {\n        if (this.countdownTimer) {\n            clearInterval(this.countdownTimer);\n            this.countdownTimer = 0;\n        }\n        this.codeCountdown = 0;\n        this.updateCodeButtonText();\n    }\n\n    /**\n     * 更新获取验证码按钮文本\n     */\n    private updateCodeButtonText(): void {\n        if (!this.getCodeButton) return;\n        \n        const label = this.getCodeButton.node.getComponentInChildren(Label);\n        if (label) {\n            if (this.codeCountdown > 0) {\n                label.string = `${this.codeCountdown}秒后重试`;\n                this.getCodeButton.interactable = false;\n            } else {\n                label.string = '获取验证码';\n                this.getCodeButton.interactable = true;\n            }\n        }\n    }\n\n    onDestroy() {\n        // 清理倒计时\n        this.stopCodeCountdown();\n\n        // 清理事件监听器\n        if (this.getCodeButton) {\n            this.getCodeButton.node.off(Button.EventType.CLICK, this.onGetCodeButtonClick, this);\n        }\n        if (this.loginButton) {\n            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);\n        }\n        if (this.maskNode) {\n            this.maskNode.off(Node.EventType.TOUCH_END, this.onMaskClick, this);\n        }\n        if (this.phoneInput) {\n            this.phoneInput.node.off('text-changed', this.onPhoneInputChanged, this);\n        }\n        if (this.codeInput) {\n            this.codeInput.node.off('text-changed', this.onCodeInputChanged, this);\n        }\n\n        console.log('[LoginDialogController] 登录对话框控制器销毁');\n    }\n}\n"]}