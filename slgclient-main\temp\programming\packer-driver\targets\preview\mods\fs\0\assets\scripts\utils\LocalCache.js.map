{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts"], "names": ["LocalCache", "sys", "set<PERSON>ersonMemory", "keyStr", "Value", "undefined", "json<PERSON><PERSON><PERSON>", "getList<PERSON>or<PERSON>son", "jsonstring", "JSON", "stringify", "localStorage", "setItem", "userList<PERSON>ey", "get<PERSON>ersonMemory", "defaultValue", "jsondata", "getItem", "Number", "jsonArray", "parse", "getUuid", "setUuid", "uuid", "setLoginValidation", "data", "getLoginValidation", "getMusic", "setMusic", "state", "getSound", "setSound"], "mappings": ";;;sBAEaA,U;;;;;;;;;AAFJC,MAAAA,G,OAAAA,G;;;;;;;4BAEID,U,GAAN,MAAMA,UAAN,CAAgB;AAIU,eAAfE,eAAe,CAACC,MAAD,EAASC,KAAT,EAAqB;AAC9C;AAEA,cAAID,MAAM,KAAKE,SAAX,IAAwBF,MAAM,KAAK,IAAnC,IAA2CA,MAAM,KAAK,EAA1D,EAA8D;AAC1D;AACH;;AAED,cAAIC,KAAK,KAAKC,SAAV,IAAuBD,KAAK,KAAK,IAAjC,IAAyCA,KAAK,KAAK,EAAvD,EAA2D;AACvDA,YAAAA,KAAK,GAAG,KAAR;AACH;;AAED,cAAIE,WAAW,GAAGN,UAAU,CAACO,cAAX,EAAlB;;AACA,cAAID,WAAW,KAAKD,SAAhB,IAA6BC,WAAW,KAAK,IAA7C,IAAqDA,WAAW,KAAK,EAAzE,EAA6E;AACzEA,YAAAA,WAAW,GAAG,EAAd;AACH;;AACDA,UAAAA,WAAW,CAACH,MAAD,CAAX,GAAsBC,KAAtB;AAEA,cAAII,UAAU,GAAGC,IAAI,CAACC,SAAL,CAAeJ,WAAf,CAAjB;AACAL,UAAAA,GAAG,CAACU,YAAJ,CAAiBC,OAAjB,CAAyBZ,UAAU,CAACa,WAApC,EAAiDL,UAAjD;AACH;;AAK4B,eAAfM,eAAe,CAACX,MAAD,EAASY,YAAT,EAA2B;AACpD;AAEA;AACA,cAAIZ,MAAM,KAAKE,SAAX,IAAwBF,MAAM,KAAK,IAAnC,IAA2CA,MAAM,KAAK,EAA1D,EAA8D;AAC1D;AACH,WANmD,CAQpD;;;AACA,cAAIG,WAAW,GAAGN,UAAU,CAACO,cAAX,EAAlB;;AACA,cAAID,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAKD,SAAxC,IAAqDC,WAAW,KAAK,EAAzE,EAA6E;AACzEA,YAAAA,WAAW,GAAG,EAAd;AACH,WAZmD,CAepD;;;AACA,cAAIA,WAAW,CAACH,MAAD,CAAX,KAAwB,IAAxB,IAAgCG,WAAW,CAACH,MAAD,CAAX,KAAwBE,SAAxD,IAAqEC,WAAW,CAACH,MAAD,CAAX,KAAwB,EAAjG,EAAqG;AACjG,mBAAOG,WAAW,CAACH,MAAD,CAAlB;AACH,WAFD,MAEM;AACN;AACI;AACA,kBAAIY,YAAY,KAAKV,SAAjB,IAA8BU,YAAY,KAAK,IAA/C,IAAuDA,YAAY,KAAK,EAA5E,EAAgF;AAC5E,uBAAO,KAAP;AACH,eAFD,MAEO;AACH;AACAT,gBAAAA,WAAW,CAACH,MAAD,CAAX,GAAsBY,YAAtB;AACA,oBAAIP,UAAU,GAAGC,IAAI,CAACC,SAAL,CAAeJ,WAAf,CAAjB;AACAL,gBAAAA,GAAG,CAACU,YAAJ,CAAiBC,OAAjB,CAAyBZ,UAAU,CAACa,WAApC,EAAiDL,UAAjD;AACA,uBAAOF,WAAW,CAACH,MAAD,CAAlB;AACH;AACJ;AAEJ;;AAG2B,eAAdI,cAAc,GAAO;AAC/B,cAAIS,QAAQ,GAAGf,GAAG,CAACU,YAAJ,CAAiBM,OAAjB,CAAyBjB,UAAU,CAACa,WAApC,CAAf;AACA,cAAI,KAAKK,MAAM,CAACF,QAAD,CAAX,IAAyBA,QAAQ,IAAIX,SAAzC,EACI;AAEJ,cAAIc,SAAS,GAAGV,IAAI,CAACW,KAAL,CAAWJ,QAAX,CAAhB;AACA,iBAAOG,SAAP;AACH;;AAGoB,eAAPE,OAAO,GAAM;AACvB,iBAAOrB,UAAU,CAACc,eAAX,CAA2B,YAA3B,EAAyC,EAAzC,CAAP;AACH;;AAEoB,eAAPQ,OAAO,CAACC,IAAD,EAAY;AAC7BvB,UAAAA,UAAU,CAACE,eAAX,CAA2B,YAA3B,EAAyCqB,IAAzC;AACH;;AAG+B,eAAlBC,kBAAkB,CAACC,IAAD,EAAe;AAC3CzB,UAAAA,UAAU,CAACE,eAAX,CAA2B,iBAA3B,EAA8CuB,IAA9C;AACH;;AAE+B,eAAlBC,kBAAkB,GAAM;AAClC,iBAAO1B,UAAU,CAACc,eAAX,CAA2B,iBAA3B,EAA8C,EAA9C,CAAP;AACH;;AAEqB,eAARa,QAAQ,GAAG;AACrB,iBAAO3B,UAAU,CAACc,eAAX,CAA2B,OAA3B,EAAoC,KAApC,CAAP;AACH;;AAEqB,eAARc,QAAQ,CAACC,KAAD,EAAgB;AAClC,iBAAO7B,UAAU,CAACE,eAAX,CAA2B,OAA3B,EAAoC2B,KAApC,CAAP;AACH;;AAEqB,eAARC,QAAQ,GAAG;AACrB,iBAAO9B,UAAU,CAACc,eAAX,CAA2B,OAA3B,EAAoC,KAApC,CAAP;AACH;;AAEqB,eAARiB,QAAQ,CAACF,KAAD,EAAgB;AAClC,iBAAO7B,UAAU,CAACE,eAAX,CAA2B,OAA3B,EAAoC2B,KAApC,CAAP;AACH;;AAxGkB,O;;sBAAV7B,U,iBACmB,a", "sourcesContent": ["import { sys } from \"cc\";\n\nexport class LocalCache{\n    public static userListKey = \"userListKey\";\n\n\n    public static setPersonMemory(keyStr, Value):void {\n        //log(\"setPersonMemory:\" + keyStr + \", \" + Value);\n    \n        if (keyStr === undefined || keyStr === null || keyStr === \"\") {\n            return;\n        }\n    \n        if (Value === undefined || Value === null || Value === \"\") {\n            Value = false;\n        }\n    \n        var jsonContent = LocalCache.getListForJson();\n        if (jsonContent === undefined || jsonContent === null || jsonContent === \"\") {\n            jsonContent = {};\n        }\n        jsonContent[keyStr] = Value;\n    \n        var jsonstring = JSON.stringify(jsonContent);\n        sys.localStorage.setItem(LocalCache.userListKey, jsonstring);\n    };\n\n\n\n    \n    public static getPersonMemory(keyStr, defaultValue):any {\n        //log(\"getPersonMemory:\" + keyStr + \", \" + defaultValue);\n    \n        //key不存在就gg了\n        if (keyStr === undefined || keyStr === null || keyStr === \"\") {\n            return;\n        }\n    \n        //获取本地已经保存的\n        var jsonContent = LocalCache.getListForJson();\n        if (jsonContent === null || jsonContent === undefined || jsonContent === \"\") {\n            jsonContent = {};\n        }\n    \n        \n        //如果本身值存在就返回本身\n        if (jsonContent[keyStr] !== null && jsonContent[keyStr] !== undefined && jsonContent[keyStr] !== \"\") {\n            return jsonContent[keyStr];\n        } else//如果本身不存在就判断默认是否存在\n        {\n            //默认也不存在 返回false\n            if (defaultValue === undefined || defaultValue === null || defaultValue === \"\") {\n                return false;\n            } else {\n                //默认存在  设置默认保存并且返回默认值\n                jsonContent[keyStr] = defaultValue;\n                var jsonstring = JSON.stringify(jsonContent);\n                sys.localStorage.setItem(LocalCache.userListKey, jsonstring);\n                return jsonContent[keyStr];\n            }\n        }\n    \n    }\n\n\n    public static getListForJson():any {\n        var jsondata = sys.localStorage.getItem(LocalCache.userListKey);\n        if (0 == Number(jsondata) || jsondata == undefined)\n            return;\n    \n        var jsonArray = JSON.parse(jsondata);\n        return jsonArray;\n    };\n\n\n    public static getUuid():any{\n        return LocalCache.getPersonMemory(\"deviceuuid\", \"\");\n    }\n    \n    public static setUuid(uuid):void {\n        LocalCache.setPersonMemory(\"deviceuuid\", uuid);\n    };\n\n\n    public static setLoginValidation(data:any):void{\n        LocalCache.setPersonMemory(\"loginvalidation\", data);\n    }\n\n    public static getLoginValidation():any{\n        return LocalCache.getPersonMemory(\"loginvalidation\", \"\");\n    }\n\n    public static getMusic() {\n        return LocalCache.getPersonMemory(\"music\", false);\n    }\n\n    public static setMusic(state:Boolean) {\n        return LocalCache.setPersonMemory(\"music\", state);\n    }\n\n    public static getSound() {\n        return LocalCache.getPersonMemory(\"sound\", false);\n    }\n\n    public static setSound(state:Boolean) {\n        return LocalCache.setPersonMemory(\"sound\", state);\n    }\n\n\n\n}\n"]}