[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 20}, {"__id__": 28}, {"__id__": 36}, {"__id__": 44}, {"__id__": 52}, {"__id__": 68}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "19be7a7f-2bd0-482b-9c25-6263042f4c49@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50dUXaTtlEPp9g3sJuBv25"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50kGifLUdJRKZPT/wdJmkv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_contentSize": {"__type__": "cc.Size", "width": 352, "height": 480}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dPmeSB/NGP53/OgDIsjrd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9scOqYOtDmLRF8jDtyIkm"}, {"__type__": "cc.Node", "_name": "pic", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}, {"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f740eff0-9e94-41ff-bd71-d4ba8f783573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91iPN3QM9HzIaYl/qmjGBt"}, {"__type__": "e558bByICxFd5ynB+Xun+9b", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 14}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9904vQy0ZHMYruzvF4Scz/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 16}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f78hHfx35NvLqc7vgQST/R"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 18}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 480}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eKH9+4wJODKnDyCJ8bAHX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24kmxca35Ka5nfP8325IJK"}, {"__type__": "cc.Node", "_name": "head_wrap", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 21}, {"__id__": 23}, {"__id__": 25}], "_prefab": {"__id__": 27}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 22}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "169d74af-302b-40fc-bd71-c1fc931cb38f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a42+dv8ldJj6JCpyUvhGQ3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 24}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cVj8PgE5INqd811fNgWyo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 26}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 480}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0c14leuMpDlrNUctFaV3m9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d0QRqQD0RHsa54KpXWc4fg"}, {"__type__": "cc.Node", "_name": "camp<PERSON>abel", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}, {"__id__": 33}], "_prefab": {"__id__": 35}, "_lpos": {"__type__": "cc.Vec3", "x": -145, "y": 205, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 30}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 155, "g": 101, "b": 65, "a": 255}, "_string": "蜀", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90Vo0XRV9Dl6IX6Wtv3Muj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 32}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b48UwpZL1IqpqJROhoZ8o1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 34}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0JEfDB1pEvp9ul1m2Q/U6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b6ZHVSlihCArPGoT7eVGt7"}, {"__type__": "cc.Node", "_name": "name", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 37}, {"__id__": 39}, {"__id__": 41}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": -145, "y": 90, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 38}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "张飞", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 45, "_overflow": 3, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baF08yoxRPMqFDokPZdkH8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 40}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96Ogsr43VKY4m8jjjamGke"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 101.69999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3elvxpPnRDq4/xnAMEzTYv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6bPSGg+pRFq6AFbZU5VNuR"}, {"__type__": "cc.Node", "_name": "cost", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 45}, {"__id__": 47}, {"__id__": 49}], "_prefab": {"__id__": 51}, "_lpos": {"__type__": "cc.Vec3", "x": 159.466, "y": -198.42, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 46}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 45, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99CjdnjX9EV6wl6jv3hz7D"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 48}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22Nof7YD5G+LdsxJAIIcf3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 50}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 56.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bxZ4qCBNHFqk35nz4urSs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dfgXLS5pMbrPYzltS4ekb"}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 53}], "_active": true, "_components": [{"__id__": 61}, {"__id__": 63}, {"__id__": 65}], "_prefab": {"__id__": 67}, "_lpos": {"__type__": "cc.Vec3", "x": -3.31, "y": -215.203, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "armLab", "_objFlags": 0, "_parent": {"__id__": 52}, "_children": [], "_active": true, "_components": [{"__id__": 54}, {"__id__": 56}, {"__id__": 58}], "_prefab": {"__id__": 60}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 55}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "骑兵", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5A4As2q1AJow8hZ6dcLqG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 57}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fvIucr5pEtaQlJLEJq/aH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 59}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26IY4WvxdALJnfAh8dkegO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9bf6HVPwhBSIEpOquXcm6I"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 62}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 44, "g": 30, "b": 30, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5+V67yC5N9aPWvoC8T7Ms"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 64}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91I4U5rjNLyYGVXFQtpnj+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 66}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bNben5thEv6ZK4z5yT+f3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91snsgKXxPRr6RYegtsKuu"}, {"__type__": "cc.Node", "_name": "starLayout", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 69}, {"__id__": 77}, {"__id__": 85}, {"__id__": 93}, {"__id__": 101}], "_active": true, "_components": [{"__id__": 109}, {"__id__": 111}, {"__id__": 113}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": -39.827, "y": 205.618, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_star", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 70}, {"__id__": 72}, {"__id__": 74}], "_prefab": {"__id__": 76}, "_lpos": {"__type__": "cc.Vec3", "x": 173, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 71}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "064508c8-04a6-40a6-9ae1-115fc8e94dcc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25IQUVWtFOb5NdUaX7hWYk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 73}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddrv55hGBJlY+68OTaPtbf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 75}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "095Ud+TPlMo7kWG4Jxp0s+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07m8Ku6WZE17H6+8w3KiB3"}, {"__type__": "cc.Node", "_name": "img_star", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 78}, {"__id__": 80}, {"__id__": 82}], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": 134, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 79}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "064508c8-04a6-40a6-9ae1-115fc8e94dcc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fciDx73WZEwY37Qn8KoWJ+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 81}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "faos1bhKBAV7a3BilGwpPh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "__prefab": {"__id__": 83}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16IihTv4NFEJrEV1MQDFyi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "08mL4CAHlPAqXi5f6RurV7"}, {"__type__": "cc.Node", "_name": "img_star", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 86}, {"__id__": 88}, {"__id__": 90}], "_prefab": {"__id__": 92}, "_lpos": {"__type__": "cc.Vec3", "x": 95, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 87}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "064508c8-04a6-40a6-9ae1-115fc8e94dcc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04vO+aTUpPmJfOc8uHNUBO"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 89}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6ebTkwaNBIKErpbf4T8L6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 91}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe1bjQWtRF8Kmph3WnFCW0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9azF/7dfZCuZjeNt2bnFJL"}, {"__type__": "cc.Node", "_name": "img_star", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 94}, {"__id__": 96}, {"__id__": 98}], "_prefab": {"__id__": 100}, "_lpos": {"__type__": "cc.Vec3", "x": 56, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 95}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "064508c8-04a6-40a6-9ae1-115fc8e94dcc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1630r5Vd5Ck4ouQt2qKhfr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 97}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "da2TgNEeRFe7GiPSVYkGBf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 99}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e85lUO2bJIfp/DbBpxlBz1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67vH51nVBP2LXl0NAiD79V"}, {"__type__": "cc.Node", "_name": "img_star", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 102}, {"__id__": 104}, {"__id__": 106}], "_prefab": {"__id__": 108}, "_lpos": {"__type__": "cc.Vec3", "x": 17, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 103}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "064508c8-04a6-40a6-9ae1-115fc8e94dcc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27gzJraVVKfIXBBxS5AfPn"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 105}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ac8+ADkw5Fe5Iq1pYqAbjj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 107}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dd1/QFj09IGZZ/oIjShA8O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3e1Ifqh59EPZfcdeJZaY3H"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 110}, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 1, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "379X903xtFO7k8LPtnQXZB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 112}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dY3txGp5B2odewS/TN3q/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 114}, "_contentSize": {"__type__": "cc.Size", "width": 190, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7PejOUztD8ZG8iz+Dg2a0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5YM11k0RIDrxlSbbDRpLD"}, {"__type__": "796ac8y0RhCLL4O+UJVy1OH", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 117}, "nameLabel": {"__id__": 37}, "spritePic": {"__id__": 11}, "costLabel": {"__id__": 45}, "starLayout": {"__id__": 109}, "campLabel": {"__id__": 29}, "armLabel": {"__id__": 54}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5322mqU/5KL7DYZnH7/3eR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 119}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffXOyT2KxB66tRdPZTSIBr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 121}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 480}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2XFkQCEBARoU0tKiRRhzu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffQBLqTVdDn4DCJuDmGew5"}]