{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts"], "names": ["_decorator", "Component", "Node", "Vec3", "tween", "ccclass", "property", "CloudAni", "onEnable", "console", "log", "aniTime", "leftNode", "setPosition", "rightNode", "lt", "to", "position", "start", "rt", "scheduleOnce", "node", "active"], "mappings": ";;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAyBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;;;;;;OACzD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;0BAIjBO,Q,WADZF,OAAO,CAAC,UAAD,C,UAGHC,QAAQ,CAACJ,IAAD,C,UAGRI,QAAQ,CAACJ,IAAD,C,oCANb,MACaK,QADb,SAC8BN,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAQpCO,QAAAA,QAAQ,GAAE;AACNC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACA,cAAIC,OAAO,GAAG,GAAd;AACA,eAAKC,QAAL,CAAcC,WAAd,CAA0B,IAAIV,IAAJ,CAAS,CAAC,EAAV,EAAc,CAAd,EAAiB,CAAjB,CAA1B;AACA,eAAKW,SAAL,CAAeD,WAAf,CAA2B,IAAIV,IAAJ,CAAS,GAAT,EAAc,CAAd,EAAiB,CAAjB,CAA3B;AAEA,cAAIY,EAAE,GAAGX,KAAK,CAAC,KAAKQ,QAAN,CAAL,CAAqBI,EAArB,CAAwBL,OAAxB,EAAiC;AAAEM,YAAAA,QAAQ,EAAE,IAAId,IAAJ,CAAS,CAAC,GAAV,EAAe,CAAf,EAAkB,CAAlB;AAAZ,WAAjC,CAAT;AACAY,UAAAA,EAAE,CAACG,KAAH;AAEA,cAAIC,EAAE,GAAGf,KAAK,CAAC,KAAKU,SAAN,CAAL,CAAsBE,EAAtB,CAAyBL,OAAzB,EAAkC;AAAEM,YAAAA,QAAQ,EAAE,IAAId,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkB,CAAlB;AAAZ,WAAlC,CAAT;AACAgB,UAAAA,EAAE,CAACD,KAAH;AAEA,eAAKE,YAAL,CAAkB,MAAI;AAClB,iBAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH,WAFD,EAEGX,OAFH;AAGH;;AAvBmC,O;;;;;iBAGnB,I;;;;;;;iBAGC,I", "sourcesContent": ["\nimport { _decorator, Component, Node, UITransform, Vec2, Vec3, tween } from 'cc';\nconst { ccclass, property } = _decorator;\n\n\n@ccclass('CloudAni')\nexport class CloudAni extends Component {\n    \n    @property(Node)\n    leftNode: Node = null;\n\n    @property(Node)\n    rightNode: Node = null;\n\n    onEnable(){\n        console.log(\"CloudAni onEnable\");\n        let aniTime = 0.5;\n        this.leftNode.setPosition(new Vec3(-60, 0, 0));\n        this.rightNode.setPosition(new Vec3(600, 0, 0));\n\n        let lt = tween(this.leftNode).to(aniTime, { position: new Vec3(-700, 0, 0) });\n        lt.start();\n\n        let rt = tween(this.rightNode).to(aniTime, { position: new Vec3(1300, 0, 0) });\n        rt.start();\n\n        this.scheduleOnce(()=>{\n            this.node.active = false;\n        }, aniTime);\n    }\n\n}\n\n\n"]}