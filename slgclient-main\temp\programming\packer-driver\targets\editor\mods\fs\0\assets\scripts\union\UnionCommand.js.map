{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"], "names": ["UnionCommand", "NetManager", "UnionProxy", "ServerConfig", "MapCommand", "EventMgr", "LogicEvent", "getInstance", "_instance", "destory", "onDestory", "constructor", "on", "union_create", "onUnionCreate", "union_join", "onUnionJoin", "union_list", "onUnionList", "union_member", "onUnionMember", "union_dismiss", "onUnionDisMiss", "union_applyList", "onUnionApply", "union_verify", "onUnionVerify", "union_exit", "union_kick", "onUnionKick", "union_appoint", "onUnionAppoint", "union_abdicate", "onUnionAbdicate", "union_modNotice", "onUnionNotice", "union_info", "onUnionInfo", "union_log", "onUnionLog", "union_apply_push", "onUnionApplyPush", "targetOff", "clearData", "_proxy", "proxy", "data", "otherData", "console", "log", "code", "emit", "createUnionSuccess", "unionList", "updateUnionList", "msg", "list", "updateMemberList", "id", "Members", "updateUnionMember", "dismissUnionSuccess", "updateApplyList", "applys", "updateUnionApply", "kickUnionSuccess", "verifyUnionSuccess", "unionAppoint", "unionAbdicate", "updateNotice", "text", "unionNotice", "l", "push", "info", "unionInfo", "unionLog", "logs", "city", "cityProxy", "getMyMainCity", "unionData", "getUnion", "unionId", "<PERSON><PERSON><PERSON><PERSON>", "rid", "updateApply", "unionCreate", "name", "sendData", "send", "unionJoin", "unionMember", "unionApplyList", "unionDismiss", "unionVerify", "decide", "unionExit", "unionKick", "title", "modNotice", "appoint"], "mappings": ";;;sGASqBA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARZC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,U;;AACEC,MAAAA,Y,iBAAAA,Y;;AAEFC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;yBAEYN,Y,GAAN,MAAMA,YAAN,CAAmB;AAC9B;AAEyB,eAAXO,WAAW,GAAiB;AACtC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIR,YAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKQ,SAAZ;AACH,SAR6B,CAW9B;;;AAGqB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SArB6B,CAuB9B;;;AAEAG,QAAAA,WAAW,GAAG;AAAA,0CAbiB;AAAA;AAAA,yCAajB;;AACV;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,YAAzB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,4CAAaK,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASN,EAAT,CAAY;AAAA;AAAA,4CAAaO,YAAzB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASR,EAAT,CAAY;AAAA;AAAA,4CAAaS,aAAzB,EAAwC,KAAKC,cAA7C,EAA6D,IAA7D;AACA;AAAA;AAAA,oCAASV,EAAT,CAAY;AAAA;AAAA,4CAAaW,eAAzB,EAA0C,KAAKC,YAA/C,EAA6D,IAA7D;AACA;AAAA;AAAA,oCAASZ,EAAT,CAAY;AAAA;AAAA,4CAAaa,YAAzB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASd,EAAT,CAAY;AAAA;AAAA,4CAAae,UAAzB,EAAqC,KAAKL,cAA1C,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASV,EAAT,CAAY;AAAA;AAAA,4CAAagB,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASjB,EAAT,CAAY;AAAA;AAAA,4CAAakB,aAAzB,EAAwC,KAAKC,cAA7C,EAA6D,IAA7D;AACA;AAAA;AAAA,oCAASnB,EAAT,CAAY;AAAA;AAAA,4CAAaoB,cAAzB,EAAyC,KAAKC,eAA9C,EAA+D,IAA/D;AACA;AAAA;AAAA,oCAASrB,EAAT,CAAY;AAAA;AAAA,4CAAasB,eAAzB,EAA0C,KAAKC,aAA/C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASvB,EAAT,CAAY;AAAA;AAAA,4CAAawB,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASzB,EAAT,CAAY;AAAA;AAAA,4CAAa0B,SAAzB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,oCAAS3B,EAAT,CAAY;AAAA;AAAA,4CAAa4B,gBAAzB,EAA2C,KAAKC,gBAAhD,EAAkE,IAAlE;AACH;;AAEM/B,QAAAA,SAAS,GAAS;AACrB;AAAA;AAAA,oCAASgC,SAAT,CAAmB,IAAnB;AACH;;AAEMC,QAAAA,SAAS,GAAS;AACrB,eAAKC,MAAL,CAAYD,SAAZ;AACH;;AAEe,YAALE,KAAK,GAAe;AAC3B,iBAAO,KAAKD,MAAZ;AACH;;AAGS9B,QAAAA,aAAa,CAACgC,IAAD,EAAYC,SAAZ,EAAkC;AACrDC,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BH,IAA7B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,kBAAzB;AACA,iBAAKC,SAAL;AACH;AACJ;;AAGSrC,QAAAA,WAAW,CAAC8B,IAAD,EAAYC,SAAZ,EAAkC;AACnDC,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BH,IAA3B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB,CACnB;AACJ;;AAGShC,QAAAA,WAAW,CAAC4B,IAAD,EAAYC,SAAZ,EAAkC;AACnDC,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BH,IAA3B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKN,MAAL,CAAYU,eAAZ,CAA4BR,IAAI,CAACS,GAAL,CAASC,IAArC;;AACA;AAAA;AAAA,sCAASL,IAAT,CAAc;AAAA;AAAA,0CAAWG,eAAzB,EAAyCR,IAAI,CAACS,GAAL,CAASC,IAAlD;AACH;AAEJ;;AAGSpC,QAAAA,aAAa,CAAC0B,IAAD,EAAYC,SAAZ,EAAkC;AACrDC,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BH,IAA7B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKN,MAAL,CAAYa,gBAAZ,CAA6BX,IAAI,CAACS,GAAL,CAASG,EAAtC,EAAyCZ,IAAI,CAACS,GAAL,CAASI,OAAlD;;AACA;AAAA;AAAA,sCAASR,IAAT,CAAc;AAAA;AAAA,0CAAWS,iBAAzB,EAA2Cd,IAAI,CAACS,GAAL,CAASI,OAApD;AACH;AAEJ;;AAGSrC,QAAAA,cAAc,CAACwB,IAAD,EAAYC,SAAZ,EAAkC;AACtDC,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BH,IAA9B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKG,SAAL;AACA;AAAA;AAAA,sCAASF,IAAT,CAAc;AAAA;AAAA,0CAAWU,mBAAzB;AACH;AACJ;;AAGSrC,QAAAA,YAAY,CAACsB,IAAD,EAAYC,SAAZ,EAAkC;AACpDC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BH,IAA5B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKN,MAAL,CAAYkB,eAAZ,CAA4BhB,IAAI,CAACS,GAAL,CAASG,EAArC,EAAyCZ,IAAI,CAACS,GAAL,CAASQ,MAAlD;;AACA;AAAA;AAAA,sCAASZ,IAAT,CAAc;AAAA;AAAA,0CAAWa,gBAAzB,EAA2ClB,IAAI,CAACS,GAAL,CAASQ,MAApD;AACH;AACJ;;AAGSrC,QAAAA,aAAa,CAACoB,IAAD,EAAYC,SAAZ,EAAkC;AACrDC,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BH,IAA7B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWc,gBAAzB;AACA;AAAA;AAAA,sCAASd,IAAT,CAAc;AAAA;AAAA,0CAAWe,kBAAzB;AACH;AACJ;;AAGSrC,QAAAA,WAAW,CAACiB,IAAD,EAAYC,SAAZ,EAAkC;AACnDC,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BH,IAA3B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWc,gBAAzB;AACH;AACJ;;AAESlC,QAAAA,cAAc,CAACe,IAAD,EAAYC,SAAZ,EAAkC;AACtDC,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BH,IAA9B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWgB,YAAzB,EAAuCrB,IAAI,CAACS,GAA5C;AACH;AACJ;;AAEStB,QAAAA,eAAe,CAACa,IAAD,EAAYC,SAAZ,EAAkC;AACvDC,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BH,IAA/B;;AACA,cAAIA,IAAI,CAACI,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWiB,aAAzB,EAAwCtB,IAAI,CAACS,GAA7C;AACH;AACJ;;AAGSpB,QAAAA,aAAa,CAACW,IAAD,EAAYC,SAAZ,EAAkC;AACrDC,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BH,IAA7B;;AACA,cAAGA,IAAI,CAACI,IAAL,IAAa,CAAhB,EAAkB;AACd,iBAAKN,MAAL,CAAYyB,YAAZ,CAAyBvB,IAAI,CAACS,GAAL,CAASG,EAAlC,EAAsCZ,IAAI,CAACS,GAAL,CAASe,IAA/C;;AACA;AAAA;AAAA,sCAASnB,IAAT,CAAc;AAAA;AAAA,0CAAWoB,WAAzB,EAAsCzB,IAAI,CAACS,GAA3C;AACH;AAEJ;;AAESlB,QAAAA,WAAW,CAACS,IAAD,EAAYC,SAAZ,EAAkC;AACnDC,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BH,IAA3B;;AACA,cAAGA,IAAI,CAACI,IAAL,IAAa,CAAhB,EAAkB;AACd,gBAAIsB,CAAC,GAAG,EAAR;AACAA,YAAAA,CAAC,CAACC,IAAF,CAAO3B,IAAI,CAACS,GAAL,CAASmB,IAAhB;;AACA,iBAAK9B,MAAL,CAAYU,eAAZ,CAA4BkB,CAA5B;;AAEA;AAAA;AAAA,sCAASrB,IAAT,CAAc;AAAA;AAAA,0CAAWwB,SAAzB,EAAoC7B,IAAI,CAACS,GAAzC;AACH;AACJ;;AAEShB,QAAAA,UAAU,CAACO,IAAD,EAAYC,SAAZ,EAAkC;AAClDC,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BH,IAA1B;;AACA,cAAGA,IAAI,CAACI,IAAL,IAAa,CAAhB,EAAkB;AACd;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWyB,QAAzB,EAAmC9B,IAAI,CAACS,GAAL,CAASsB,IAA5C;AACH;AACJ;;AAESpC,QAAAA,gBAAgB,CAACK,IAAD,EAAYC,SAAZ,EAAkC;AACxDC,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCH,IAAhC;AACA,cAAIgC,IAAgB,GAAG;AAAA;AAAA,wCAAWvE,WAAX,GAAyBwE,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIC,SAAe,GAAGjF,YAAY,CAACO,WAAb,GAA2BsC,KAA3B,CAAiCqC,QAAjC,CAA0CJ,IAAI,CAACK,OAA/C,CAAtB;;AACA,cAAIF,SAAS,IAAIA,SAAS,CAACG,OAAV,CAAkBN,IAAI,CAACO,GAAvB,CAAjB,EAA6C;AACzC,iBAAKzC,MAAL,CAAY0C,WAAZ,CAAwBR,IAAI,CAACK,OAA7B,EAAsCrC,IAAI,CAACS,GAA3C;;AACA;AAAA;AAAA,sCAASJ,IAAT,CAAc;AAAA;AAAA,0CAAWa,gBAAzB,EAA2ClB,IAAI,CAACS,GAAhD;AACH;AACJ;;AAGMgC,QAAAA,WAAW,CAACC,IAAD,EAAkB;AAChC,cAAIC,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa3E,YADH;AAEhB0C,YAAAA,GAAG,EAAE;AACDiC,cAAAA,IAAI,EAAEA;AADL;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAWjF,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAGME,QAAAA,SAAS,CAACjC,EAAS,GAAG,CAAb,EAAoB;AAChC,cAAI+B,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAazE,UADH;AAEhBwC,YAAAA,GAAG,EAAE;AACDG,cAAAA,EAAE,EAAEA;AADH;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAWnD,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAGMpC,QAAAA,SAAS,GAAO;AACnB,cAAIoC,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAavE,UADH;AAEhBsC,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWhD,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAEMd,QAAAA,SAAS,CAACjB,EAAS,GAAG,CAAb,EAAoB;AAChC,cAAI+B,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAapD,UADH;AAEhBmB,YAAAA,GAAG,EAAE;AACDG,cAAAA,EAAE,EAACA;AADF;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAWnD,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAGMG,QAAAA,WAAW,CAAClC,EAAS,GAAG,CAAb,EAAoB;AAClC,cAAI+B,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAarE,YADH;AAEhBoC,YAAAA,GAAG,EAAE;AACDG,cAAAA,EAAE,EAAEA;AADH;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAWnD,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAGMI,QAAAA,cAAc,CAACnC,EAAS,GAAG,CAAb,EAAoB;AACrC,cAAI+B,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAajE,eADH;AAEhBgC,YAAAA,GAAG,EAAE;AACDG,cAAAA,EAAE,EAAEA;AADH;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAWnD,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAIMK,QAAAA,YAAY,GAAO;AACtB,cAAIL,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAanE,aADH;AAEhBkC,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWhD,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAGMM,QAAAA,WAAW,CAACrC,EAAS,GAAG,CAAb,EAAesC,MAAa,GAAG,CAA/B,EAAsC;AACpD,cAAIP,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa/D,YADH;AAEhB8B,YAAAA,GAAG,EAAE;AACDG,cAAAA,EAAE,EAACA,EADF;AAEDsC,cAAAA,MAAM,EAACA;AAFN;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWzF,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAGMQ,QAAAA,SAAS,GAAO;AACnB,cAAIR,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa7D,UADH;AAEhB4B,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWhD,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAGMS,QAAAA,SAAS,CAACb,GAAU,GAAG,CAAd,EAAqB;AACjC,cAAII,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa5D,UADH;AAEhB2B,YAAAA,GAAG,EAAE;AACD8B,cAAAA,GAAG,EAACA;AADH;AAFW,WAApB;AAMArC,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ;AACA;AAAA;AAAA,wCAAW1C,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAEMtB,QAAAA,YAAY,CAACkB,GAAU,GAAG,CAAd,EAAiBc,KAAK,GAAC,CAAvB,EAA8B;AAC7C,cAAIV,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa1D,aADH;AAEhByB,YAAAA,GAAG,EAAE;AACD8B,cAAAA,GAAG,EAACA,GADH;AAEDc,cAAAA,KAAK,EAACA;AAFL;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAW5F,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAEMrB,QAAAA,aAAa,CAACiB,GAAU,GAAG,CAAd,EAAqB;AACrC,cAAII,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAaxD,cADH;AAEhBuB,YAAAA,GAAG,EAAE;AACD8B,cAAAA,GAAG,EAACA;AADH;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAW9E,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAEMW,QAAAA,SAAS,CAAC9B,IAAD,EAAkB;AAC9B,cAAImB,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAatD,eADH;AAEhBqB,YAAAA,GAAG,EAAE;AACDe,cAAAA,IAAI,EAACA;AADJ;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAW/D,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAEMY,QAAAA,OAAO,CAAChB,GAAU,GAAG,CAAd,EAAqB;AAC/B,cAAII,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa5D,UADH;AAEhB2B,YAAAA,GAAG,EAAE;AACD8B,cAAAA,GAAG,EAACA;AADH;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAW9E,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AAEMb,QAAAA,QAAQ,GAAO;AAClB,cAAIa,QAAa,GAAG;AAChBD,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAalD,SADH;AAEhBiB,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWhD,WAAX,GAAyBmF,IAAzB,CAA8BD,QAA9B;AACH;;AA/U6B,O;;sBAAbzF,Y", "sourcesContent": ["import { _decorator } from 'cc';\nimport { NetManager } from \"../network/socket/NetManager\";\nimport UnionProxy, { Union } from \"./UnionProxy\";\nimport { ServerConfig } from \"../config/ServerConfig\";\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\nexport default class UnionCommand {\n    //单例\n    protected static _instance: UnionCommand;\n    public static getInstance(): UnionCommand {\n        if (this._instance == null) {\n            this._instance = new UnionCommand();\n        }\n        return this._instance;\n    }\n\n\n    //数据model\n    protected _proxy: UnionProxy = new UnionProxy();\n\n    public static destory(): boolean {\n        if (this._instance) {\n            this._instance.onDestory();\n            this._instance = null;\n            return true;\n        }\n        return false;\n    }\n\n    //数据model\n\n    constructor() {\n        EventMgr.on(ServerConfig.union_create, this.onUnionCreate, this);\n        EventMgr.on(ServerConfig.union_join, this.onUnionJoin, this);\n        EventMgr.on(ServerConfig.union_list, this.onUnionList, this);\n        EventMgr.on(ServerConfig.union_member, this.onUnionMember, this);\n        EventMgr.on(ServerConfig.union_dismiss, this.onUnionDisMiss, this);\n        EventMgr.on(ServerConfig.union_applyList, this.onUnionApply, this);\n        EventMgr.on(ServerConfig.union_verify, this.onUnionVerify, this);\n        EventMgr.on(ServerConfig.union_exit, this.onUnionDisMiss, this);\n        EventMgr.on(ServerConfig.union_kick, this.onUnionKick, this);\n        EventMgr.on(ServerConfig.union_appoint, this.onUnionAppoint, this);\n        EventMgr.on(ServerConfig.union_abdicate, this.onUnionAbdicate, this);\n        EventMgr.on(ServerConfig.union_modNotice, this.onUnionNotice, this)\n        EventMgr.on(ServerConfig.union_info, this.onUnionInfo, this);\n        EventMgr.on(ServerConfig.union_log, this.onUnionLog, this);\n        EventMgr.on(ServerConfig.union_apply_push, this.onUnionApplyPush, this);\n    }\n\n    public onDestory(): void {\n        EventMgr.targetOff(this);\n    }\n\n    public clearData(): void {\n        this._proxy.clearData();\n    }\n\n    public get proxy(): UnionProxy {\n        return this._proxy;\n    }\n\n\n    protected onUnionCreate(data: any, otherData: any): void {\n        console.log(\"onUnionCreate\", data);\n        if (data.code == 0) {\n            EventMgr.emit(LogicEvent.createUnionSuccess);\n            this.unionList();\n        }\n    }\n\n\n    protected onUnionJoin(data: any, otherData: any): void {\n        console.log(\"onUnionJoin\", data);\n        if (data.code == 0) {\n        }\n    }\n\n\n    protected onUnionList(data: any, otherData: any): void {\n        console.log(\"onUnionList\", data);\n        if (data.code == 0) {\n            this._proxy.updateUnionList(data.msg.list);\n            EventMgr.emit(LogicEvent.updateUnionList,data.msg.list);\n        }\n\n    }\n\n\n    protected onUnionMember(data: any, otherData: any): void {\n        console.log(\"onUnionMember\", data);\n        if (data.code == 0) {\n            this._proxy.updateMemberList(data.msg.id,data.msg.Members);\n            EventMgr.emit(LogicEvent.updateUnionMember,data.msg.Members);\n        }\n        \n    }\n\n\n    protected onUnionDisMiss(data: any, otherData: any): void {\n        console.log(\"onUnionDisMiss\", data);\n        if (data.code == 0) {\n            this.unionList();\n            EventMgr.emit(LogicEvent.dismissUnionSuccess);\n        }\n    }\n\n\n    protected onUnionApply(data: any, otherData: any): void {\n        console.log(\"onUnionApply\", data);\n        if (data.code == 0) {\n            this._proxy.updateApplyList(data.msg.id, data.msg.applys);\n            EventMgr.emit(LogicEvent.updateUnionApply, data.msg.applys);\n        }\n    }\n\n\n    protected onUnionVerify(data: any, otherData: any): void {\n        console.log(\"onUnionVerify\", data);\n        if (data.code == 0) {\n            EventMgr.emit(LogicEvent.kickUnionSuccess);\n            EventMgr.emit(LogicEvent.verifyUnionSuccess);\n        }\n    }\n\n\n    protected onUnionKick(data: any, otherData: any): void {\n        console.log(\"onUnionKick\", data);\n        if (data.code == 0) {\n            EventMgr.emit(LogicEvent.kickUnionSuccess);\n        }\n    }\n\n    protected onUnionAppoint(data: any, otherData: any): void {\n        console.log(\"onUnionAppoint\", data);\n        if (data.code == 0) {\n            EventMgr.emit(LogicEvent.unionAppoint, data.msg);\n        }\n    }\n\n    protected onUnionAbdicate(data: any, otherData: any): void {\n        console.log(\"onUnionAbdicate\", data);\n        if (data.code == 0) {\n            EventMgr.emit(LogicEvent.unionAbdicate, data.msg);\n        }\n    }\n\n\n    protected onUnionNotice(data: any, otherData: any): void {\n        console.log(\"onUnionNotice\", data);\n        if(data.code == 0){\n            this._proxy.updateNotice(data.msg.id, data.msg.text)\n            EventMgr.emit(LogicEvent.unionNotice, data.msg);\n        }\n        \n    }\n\n    protected onUnionInfo(data: any, otherData: any): void {\n        console.log(\"onUnionInfo\", data);\n        if(data.code == 0){\n            let l = []\n            l.push(data.msg.info)\n            this._proxy.updateUnionList(l);\n\n            EventMgr.emit(LogicEvent.unionInfo, data.msg);\n        }\n    }\n\n    protected onUnionLog(data: any, otherData: any): void {\n        console.log(\"onUnionLog\", data);\n        if(data.code == 0){\n            EventMgr.emit(LogicEvent.unionLog, data.msg.logs);\n        }\n    }\n    \n    protected onUnionApplyPush(data: any, otherData: any): void {\n        console.log(\"onUnionApplyPush\", data);\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let unionData:Union = UnionCommand.getInstance().proxy.getUnion(city.unionId);\n        if (unionData && unionData.isMajor(city.rid)){\n            this._proxy.updateApply(city.unionId, data.msg);\n            EventMgr.emit(LogicEvent.updateUnionApply, data.msg);\n        }\n    }\n\n\n    public unionCreate(name:string):void{\n        let sendData: any = {\n            name: ServerConfig.union_create,\n            msg: {\n                name: name,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    public unionJoin(id:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.union_join,\n            msg: {\n                id: id,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    public unionList():void{\n        let sendData: any = {\n            name: ServerConfig.union_list,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public unionInfo(id:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.union_info,\n            msg: {\n                id:id\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    public unionMember(id:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.union_member,\n            msg: {\n                id: id,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    public unionApplyList(id:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.union_applyList,\n            msg: {\n                id: id,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n\n    public unionDismiss():void{\n        let sendData: any = {\n            name: ServerConfig.union_dismiss,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    public unionVerify(id:number = 0,decide:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.union_verify,\n            msg: {\n                id:id,\n                decide:decide\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    \n    public unionExit():void{\n        let sendData: any = {\n            name: ServerConfig.union_exit,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    public unionKick(rid:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.union_kick,\n            msg: {\n                rid:rid,\n            }\n        };\n        console.log(\"unionKick\");\n        NetManager.getInstance().send(sendData);\n    }\n\n    public unionAppoint(rid:number = 0, title=1):void{\n        let sendData: any = {\n            name: ServerConfig.union_appoint,\n            msg: {\n                rid:rid,\n                title:title\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public unionAbdicate(rid:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.union_abdicate,\n            msg: {\n                rid:rid\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public modNotice(text:string):void{\n        let sendData: any = {\n            name: ServerConfig.union_modNotice,\n            msg: {\n                text:text,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public appoint(rid:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.union_kick,\n            msg: {\n                rid:rid,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public unionLog():void{\n        let sendData: any = {\n            name: ServerConfig.union_log,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n}\n"]}