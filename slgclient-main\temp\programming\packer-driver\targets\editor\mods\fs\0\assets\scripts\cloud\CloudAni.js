System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _decorator, Component, Node, Vec3, tween, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _temp, _crd, ccclass, property, CloudAni;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Vec3 = _cc.Vec3;
      tween = _cc.tween;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2312cRD1zBLHpuuTwSVOMB4", "CloudAni", undefined);

      ({
        ccclass,
        property
      } = _decorator);

      _export("CloudAni", CloudAni = (_dec = ccclass('CloudAni'), _dec2 = property(Node), _dec3 = property(Node), _dec(_class = (_class2 = (_temp = class CloudAni extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "leftNode", _descriptor, this);

          _initializerDefineProperty(this, "rightNode", _descriptor2, this);
        }

        onEnable() {
          console.log("CloudAni onEnable");
          let aniTime = 0.5;
          this.leftNode.setPosition(new Vec3(-60, 0, 0));
          this.rightNode.setPosition(new Vec3(600, 0, 0));
          let lt = tween(this.leftNode).to(aniTime, {
            position: new Vec3(-700, 0, 0)
          });
          lt.start();
          let rt = tween(this.rightNode).to(aniTime, {
            position: new Vec3(1300, 0, 0)
          });
          rt.start();
          this.scheduleOnce(() => {
            this.node.active = false;
          }, aniTime);
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "leftNode", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "rightNode", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=CloudAni.js.map