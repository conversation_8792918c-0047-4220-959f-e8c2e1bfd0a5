{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts"], "names": ["_decorator", "Component", "Prefab", "Node", "Layout", "Label", "instantiate", "Vec2", "LoginCommand", "ArmySelectNodeLogic", "CityArmySettingLogic", "FacilityListLogic", "MapUICommand", "Dialog", "DialogType", "UnionCommand", "MapCommand", "FortressAbout", "CityAboutLogic", "GeneralList<PERSON><PERSON><PERSON>", "TransformLogic", "Tools", "GeneralInfoLogic", "WarReportLogic", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SkillInfoLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "MapUILogic", "onLoad", "_resArray", "push", "key", "name", "_yieldArray", "on", "openCityAbout", "closeCityAbout", "openFortressAbout", "openFacility", "openArmySetting", "upateMyRoleRes", "updateRoleRes", "openGeneralDes", "openGeneralChoose", "openArmySelectUi", "onOpenArmySelectUI", "openDrawResult", "openDrawR", "robLoginUI", "interiorCollect", "onCollection", "openGeneralConvert", "onOpenGeneralConvert", "openGeneralRoster", "onOpenGeneralRoster", "openGeneral", "openSkill", "onOpenSkill", "closeSkill", "onCloseSkill", "openSkillInfo", "onOpenSkillInfo", "beforeScrollToMap", "showTip", "updateRole", "unionId", "getInstance", "cityProxy", "myUnionId", "unionApplyList", "emit", "enterLogin", "text", "close", "_dialogNode", "dialog", "parent", "contentNode", "active", "setSiblingIndex", "topLayer", "getComponent", "show", "OnlyConfirm", "setClose", "onDestroy", "clearAllNode", "proxy", "clearData", "targetOff", "console", "log", "onBack", "instance", "playClick", "account_logout", "_facilityNode", "_generalNode", "_cityAboutNode", "_fortressAboutNode", "_armySelectNode", "_armySettingNode", "_drawNode", "_drawResultNode", "_generalDesNode", "children", "length", "data", "facilityPrefab", "setData", "cityId", "order", "armySettingPrefab", "onClickGeneral", "type", "position", "generalPrefab", "cmd", "x", "y", "armySelectPrefab", "cfgData", "curData", "generalDesPrefab", "cityAboutPrefab", "widgetNode", "scrollToMap", "fortressAboutPrefab", "openWarReport", "_warReportNode", "warReportPrefab", "updateView", "srollLayout", "node", "roleRes", "getRoleResData", "i", "getChildByName", "string", "numberToShow", "index", "obj", "label", "openDraw", "drawPrefab", "drawResultrefab", "openUnion", "_unionNode", "unionPrefab", "openChat", "_chatNode", "chatPrefab", "openTr", "_transFormNode", "transFormPrefab", "initView", "_generalConvertNode", "generalConvertPrefab", "_generalRosterNode", "generalRosterPrefab", "onClickSkillBtn", "general", "skillPos", "_skillNode", "skillPrefab", "cfg", "_skillInfoNode", "skillInfoPrefab", "msg", "gold", "roleData", "getRoleData", "name<PERSON><PERSON><PERSON>", "nick<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "rid", "onClickCollection", "_collectNode", "collectPrefab", "onClickSetting", "_settingNode", "settingPrefab", "oldx", "oldy", "newPoint", "oldPoint", "dis", "squaredDistance", "_cloudAniNode", "cloudAniPrefab"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAA0BC,MAAAA,I,OAAAA,I;;AAGhFC,MAAAA,Y;;AACAC,MAAAA,mB;;AACAC,MAAAA,oB;;AACAC,MAAAA,iB;;AACAC,MAAAA,Y;;AACAC,MAAAA,M;AAAUC,MAAAA,U,iBAAAA,U;;AACVC,MAAAA,Y;;AACAC,MAAAA,U;;AACAC,MAAAA,a;;AACAC,MAAAA,c;;AACAC,MAAAA,gB;;AACAC,MAAAA,c;;AACEC,MAAAA,K,kBAAAA,K;;AACFC,MAAAA,gB;;AACAC,MAAAA,c;;AACAC,MAAAA,U;;AAEAC,MAAAA,U;;AAEAC,MAAAA,c;;AACEC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,Y,kBAAAA,Y;;AAEAC,MAAAA,U,kBAAAA,U;;;;;;;OAzBH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwB/B,U;;yBA8BTgC,U,WADpBF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAAC5B,IAAD,C,UAGR4B,QAAQ,CAAC7B,MAAD,C,UAIR6B,QAAQ,CAAC7B,MAAD,C,UAIR6B,QAAQ,CAAC7B,MAAD,C,UAIR6B,QAAQ,CAAC7B,MAAD,C,UAIR6B,QAAQ,CAAC7B,MAAD,C,UAIR6B,QAAQ,CAAC7B,MAAD,C,UAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAGR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAKR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAIR6B,QAAQ,CAAC7B,MAAD,C,WAKR6B,QAAQ,CAAC7B,MAAD,C,WAKR6B,QAAQ,CAAC5B,IAAD,C,WAGR4B,QAAQ,CAAC3B,MAAD,C,WAGR2B,QAAQ,CAAC1B,KAAD,C,WAGR0B,QAAQ,CAAC1B,KAAD,C,oCArGb,MACqB2B,UADrB,SACwC/B,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,iDAOd,IAPc;;AAAA;;AAAA,oDAWX,IAXW;;AAAA;;AAAA,+CAehB,IAfgB;;AAAA;;AAAA,gDAmBf,IAnBe;;AAAA;;AAAA,mDAuBZ,IAvBY;;AAAA;;AAAA,kDA2Bb,IA3Ba;;AAAA;;AAAA,sDA+BT,IA/BS;;AAAA;;AAAA,kDAmCb,IAnCa;;AAAA;;AAAA,mDAsCZ,IAtCY;;AAAA;;AAAA,6CA0ClB,IA1CkB;;AAAA;;AAAA,mDA8CZ,IA9CY;;AAAA;;AAAA,8CAkDjB,IAlDiB;;AAAA;;AAAA,6CAsDlB,IAtDkB;;AAAA;;AAAA,gDA0Df,IA1De;;AAAA;;AAAA,kDA+Db,IA/Da;;AAAA;;AAAA,uDAmER,IAnEQ;;AAAA;;AAAA,sDAuET,IAvES;;AAAA;;AAAA,8CA2EjB,IA3EiB;;AAAA;;AAAA,kDA+Eb,IA/Ea;;AAAA;;AAAA,gDAmFf,IAnFe;;AAAA;;AAAA,iDAwFd,IAxFc;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,6CAuGnB,EAvGmB;;AAAA,+CAwGjB,EAxGiB;AAAA;;AA0GpCgC,QAAAA,MAAM,GAAS;AAErB,eAAKC,SAAL,CAAeC,IAAf,CAAoB;AAACC,YAAAA,GAAG,EAAC,OAAL;AAAcC,YAAAA,IAAI,EAAC;AAAnB,WAApB;;AACA,eAAKH,SAAL,CAAeC,IAAf,CAAoB;AAACC,YAAAA,GAAG,EAAC,MAAL;AAAaC,YAAAA,IAAI,EAAC;AAAlB,WAApB;;AACA,eAAKH,SAAL,CAAeC,IAAf,CAAoB;AAACC,YAAAA,GAAG,EAAC,MAAL;AAAaC,YAAAA,IAAI,EAAC;AAAlB,WAApB;;AACA,eAAKH,SAAL,CAAeC,IAAf,CAAoB;AAACC,YAAAA,GAAG,EAAC,OAAL;AAAcC,YAAAA,IAAI,EAAC;AAAnB,WAApB;;AACA,eAAKH,SAAL,CAAeC,IAAf,CAAoB;AAACC,YAAAA,GAAG,EAAC,MAAL;AAAaC,YAAAA,IAAI,EAAC;AAAlB,WAApB;;AAEA,eAAKC,WAAL,CAAiBH,IAAjB,CAAsB;AAACC,YAAAA,GAAG,EAAC,YAAL;AAAmBC,YAAAA,IAAI,EAAC;AAAxB,WAAtB;;AACA,eAAKC,WAAL,CAAiBH,IAAjB,CAAsB;AAACC,YAAAA,GAAG,EAAC,YAAL;AAAmBC,YAAAA,IAAI,EAAC;AAAxB,WAAtB;;AACA,eAAKC,WAAL,CAAiBH,IAAjB,CAAsB;AAACC,YAAAA,GAAG,EAAC,aAAL;AAAoBC,YAAAA,IAAI,EAAC;AAAzB,WAAtB;;AACA,eAAKC,WAAL,CAAiBH,IAAjB,CAAsB;AAACC,YAAAA,GAAG,EAAC,aAAL;AAAoBC,YAAAA,IAAI,EAAC;AAAzB,WAAtB;;AAGA;AAAA;AAAA,oCAASE,EAAT,CAAY;AAAA;AAAA,wCAAWC,aAAvB,EAAsC,KAAKA,aAA3C,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASD,EAAT,CAAY;AAAA;AAAA,wCAAWE,cAAvB,EAAuC,KAAKA,cAA5C,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,iBAAvB,EAA0C,KAAKA,iBAA/C,EAAkE,IAAlE;AACA;AAAA;AAAA,oCAASH,EAAT,CAAY;AAAA;AAAA,wCAAWI,YAAvB,EAAqC,KAAKA,YAA1C,EAAwD,IAAxD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,eAAvB,EAAwC,KAAKA,eAA7C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASL,EAAT,CAAY;AAAA;AAAA,wCAAWM,cAAvB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASP,EAAT,CAAY;AAAA;AAAA,wCAAWQ,cAAvB,EAAuC,KAAKA,cAA5C,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAASR,EAAT,CAAY;AAAA;AAAA,wCAAWS,iBAAvB,EAA0C,KAAKA,iBAA/C,EAAkE,IAAlE;AACA;AAAA;AAAA,oCAAST,EAAT,CAAY;AAAA;AAAA,wCAAWU,gBAAvB,EAAyC,KAAKC,kBAA9C,EAAkE,IAAlE;AACA;AAAA;AAAA,oCAASX,EAAT,CAAY;AAAA;AAAA,wCAAWY,cAAvB,EAAuC,KAAKC,SAA5C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASb,EAAT,CAAY;AAAA;AAAA,wCAAWc,UAAvB,EAAmC,KAAKA,UAAxC,EAAoD,IAApD;AACA;AAAA;AAAA,oCAASd,EAAT,CAAY;AAAA;AAAA,wCAAWe,eAAvB,EAAwC,KAAKC,YAA7C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAAShB,EAAT,CAAY;AAAA;AAAA,wCAAWiB,kBAAvB,EAA2C,KAAKC,oBAAhD,EAAsE,IAAtE;AACA;AAAA;AAAA,oCAASlB,EAAT,CAAY;AAAA;AAAA,wCAAWmB,iBAAvB,EAA0C,KAAKC,mBAA/C,EAAoE,IAApE;AACA;AAAA;AAAA,oCAASpB,EAAT,CAAY;AAAA;AAAA,wCAAWqB,WAAvB,EAAoC,KAAKA,WAAzC,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASrB,EAAT,CAAY;AAAA;AAAA,wCAAWsB,SAAvB,EAAkC,KAAKC,WAAvC,EAAoD,IAApD;AACA;AAAA;AAAA,oCAASvB,EAAT,CAAY;AAAA;AAAA,wCAAWwB,UAAvB,EAAmC,KAAKC,YAAxC,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASzB,EAAT,CAAY;AAAA;AAAA,wCAAW0B,aAAvB,EAAsC,KAAKC,eAA3C,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAAS3B,EAAT,CAAY;AAAA;AAAA,wCAAW4B,iBAAvB,EAA0C,KAAKA,iBAA/C,EAAkE,IAAlE;AACA;AAAA;AAAA,oCAAS5B,EAAT,CAAY;AAAA;AAAA,wCAAW6B,OAAvB,EAAgC,KAAKA,OAArC,EAA8C,IAA9C;AAIA,eAAKtB,aAAL;AACA,eAAKuB,UAAL;AACA,cAAIC,OAAO,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,SAAjD;;AACA,cAAIH,OAAO,GAAG,CAAd,EAAiB;AACb;AAAA;AAAA,8CAAaC,WAAb,GAA2BG,cAA3B,CAA0CJ,OAA1C;AACH;AACJ;;AAESjB,QAAAA,UAAU,GAAS;AACzB,eAAKe,OAAL,CAAa,WAAb,EAAyB,YAAY;AACjC;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,0CAAWC,UAAzB;AACH,WAFD;AAGH;;AAESR,QAAAA,OAAO,CAACS,IAAD,EAAcC,KAAd,EAAmC;AAChD,cAAI,KAAKC,WAAL,IAAoB,IAAxB,EAA6B;AACzB,iBAAKA,WAAL,GAAmBzE,WAAW,CAAC,KAAK0E,MAAN,CAA9B;AACA,iBAAKD,WAAL,CAAiBE,MAAjB,GAA0B,KAAKC,WAA/B;AACH,WAHD,MAGK;AACD,iBAAKH,WAAL,CAAiBI,MAAjB,GAA0B,IAA1B;AACH;;AACD,eAAKJ,WAAL,CAAiBK,eAAjB,CAAiC,KAAKC,QAAL,EAAjC;;AACA,eAAKN,WAAL,CAAiBO,YAAjB;AAAA;AAAA,gCAAsCC,IAAtC,CAA2CV,IAA3C,EAAiD;AAAA;AAAA,wCAAWW,WAA5D;;AACA,eAAKT,WAAL,CAAiBO,YAAjB;AAAA;AAAA,gCAAsCG,QAAtC,CAA+CX,KAA/C;AACH;;AAGSY,QAAAA,SAAS,GAAS;AACxB,eAAKC,YAAL;AACA;AAAA;AAAA,4CAAapB,WAAb,GAA2BqB,KAA3B,CAAiCC,SAAjC;AACA;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AAEAC,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAa5B,WAAb,GAA2B6B,cAA3B;AACH;;AAGST,QAAAA,YAAY,GAAS;AAC3B,eAAKU,aAAL,GAAqB,IAArB;AACA,eAAKC,YAAL,GAAoB,IAApB;AACA,eAAKC,cAAL,GAAsB,IAAtB;AACA,eAAKC,kBAAL,GAA0B,IAA1B;AACA,eAAKC,eAAL,GAAuB,IAAvB;AACA,eAAKC,gBAAL,GAAwB,IAAxB;AACA,eAAKC,SAAL,GAAiB,IAAjB;AACA,eAAKC,eAAL,GAAuB,IAAvB;AACA,eAAKC,eAAL,GAAuB,IAAvB;AACA,eAAK9B,WAAL,GAAmB,IAAnB;AACH;;AAIMM,QAAAA,QAAQ,GAAU;AACrB,iBAAO,KAAKH,WAAL,CAAiB4B,QAAjB,CAA0BC,MAA1B,GAAiC,CAAxC;AACH;AACD;AACJ;AACA;;;AACcpE,QAAAA,YAAY,CAACqE,IAAD,EAAkB;AACpC,cAAI,KAAKX,aAAL,IAAsB,IAA1B,EAAgC;AAC5B,iBAAKA,aAAL,GAAqB/F,WAAW,CAAC,KAAK2G,cAAN,CAAhC;AACA,iBAAKZ,aAAL,CAAmBpB,MAAnB,GAA4B,KAAKC,WAAjC;AACH,WAHD,MAGO;AACH,iBAAKmB,aAAL,CAAmBlB,MAAnB,GAA4B,IAA5B;AACH;;AACD,eAAKkB,aAAL,CAAmBjB,eAAnB,CAAmC,KAAKC,QAAL,EAAnC;;AACA,eAAKgB,aAAL,CAAmBf,YAAnB;AAAA;AAAA,sDAAmD4B,OAAnD,CAA2DF,IAA3D;AACH;;AAESpE,QAAAA,eAAe,CAACuE,MAAD,EAAiBC,KAAjB,EAAsC;AAC3D,cAAI,KAAKV,gBAAL,IAAyB,IAA7B,EAAmC;AAC/B,iBAAKA,gBAAL,GAAwBpG,WAAW,CAAC,KAAK+G,iBAAN,CAAnC;AACA,iBAAKX,gBAAL,CAAsBzB,MAAtB,GAA+B,KAAKC,WAApC;AACH,WAHD,MAGO;AACH,iBAAKwB,gBAAL,CAAsBvB,MAAtB,GAA+B,IAA/B;AACH;;AACD,eAAKuB,gBAAL,CAAsBtB,eAAtB,CAAsC,KAAKC,QAAL,EAAtC;;AACA,eAAKqB,gBAAL,CAAsBpB,YAAtB;AAAA;AAAA,4DAAyD4B,OAAzD,CAAiEC,MAAjE,EAAyEC,KAAzE;AACH;AACD;AACJ;AACA;;;AAEcE,QAAAA,cAAc,GAAE;AACtB;AAAA;AAAA,4CAAapB,QAAb,CAAsBC,SAAtB;AACA,eAAKvC,WAAL,CAAiB,EAAjB;AACH;;AAESA,QAAAA,WAAW,CAACoD,IAAD,EAAiBO,IAAY,GAAG,CAAhC,EAAmCC,QAAgB,GAAG,CAAtD,EAA+D;AAChF,cAAI,KAAKlB,YAAL,IAAqB,IAAzB,EAA+B;AAC3B,iBAAKA,YAAL,GAAoBhG,WAAW,CAAC,KAAKmH,aAAN,CAA/B;AACA,iBAAKnB,YAAL,CAAkBrB,MAAlB,GAA2B,KAAKC,WAAhC;AACH,WAHD,MAGO;AACH,iBAAKoB,YAAL,CAAkBnB,MAAlB,GAA2B,IAA3B;AACH;;AACD,eAAKmB,YAAL,CAAkBlB,eAAlB,CAAkC,KAAKC,QAAL,EAAlC;;AACA,eAAKiB,YAAL,CAAkBhB,YAAlB;AAAA;AAAA,oDAAiD4B,OAAjD,CAAyDF,IAAzD,EAA+DO,IAA/D,EAAqEC,QAArE;AACH;AAGD;AACJ;AACA;AACA;AACA;;;AACcxE,QAAAA,iBAAiB,CAACgE,IAAD,EAAiBQ,QAAgB,GAAG,CAApC,EAA6C;AACpE,eAAK5D,WAAL,CAAiBoD,IAAjB,EAAuB,CAAvB,EAA0BQ,QAA1B;AACH;AAED;;;AACUtE,QAAAA,kBAAkB,CAACwE,GAAD,EAAcC,CAAd,EAAyBC,CAAzB,EAA0C;AAClE,cAAI,KAAKnB,eAAL,IAAwB,IAA5B,EAAkC;AAC9B,iBAAKA,eAAL,GAAuBnG,WAAW,CAAC,KAAKuH,gBAAN,CAAlC;AACA,iBAAKpB,eAAL,CAAqBxB,MAArB,GAA8B,KAAKC,WAAnC;AACH,WAHD,MAGO;AACH,iBAAKuB,eAAL,CAAqBtB,MAArB,GAA8B,IAA9B;AACH;;AACD,eAAKsB,eAAL,CAAqBrB,eAArB,CAAqC,KAAKC,QAAL,EAArC;;AACA,eAAKoB,eAAL,CAAqBnB,YAArB;AAAA;AAAA,0DAAuD4B,OAAvD,CAA+DQ,GAA/D,EAAoEC,CAApE,EAAuEC,CAAvE;AACH;AAGD;AACJ;AACA;;;AACc7E,QAAAA,cAAc,CAAC+E,OAAD,EAAeC,OAAf,EAAmC;AACvD,cAAI,KAAKlB,eAAL,IAAwB,IAA5B,EAAkC;AAC9B,iBAAKA,eAAL,GAAuBvG,WAAW,CAAC,KAAK0H,gBAAN,CAAlC;AACA,iBAAKnB,eAAL,CAAqB5B,MAArB,GAA8B,KAAKC,WAAnC;AACH,WAHD,MAGO;AACH,iBAAK2B,eAAL,CAAqB1B,MAArB,GAA8B,IAA9B;AACH;;AACD,eAAK0B,eAAL,CAAqBzB,eAArB,CAAqC,KAAKC,QAAL,EAArC;;AACA,eAAKwB,eAAL,CAAqBvB,YAArB;AAAA;AAAA,oDAAoD4B,OAApD,CAA4DY,OAA5D,EAAqEC,OAArE;AACH;AAGD;AACJ;AACA;;;AACcvF,QAAAA,aAAa,CAACwE,IAAD,EAAkB;AAErC,cAAI,KAAKT,cAAL,IAAuB,IAA3B,EAAiC;AAC7B,iBAAKA,cAAL,GAAsBjG,WAAW,CAAC,KAAK2H,eAAN,CAAjC;AACA,iBAAK1B,cAAL,CAAoBtB,MAApB,GAA6B,KAAKC,WAAlC;AACH,WAHD,MAGO;AACH,iBAAKqB,cAAL,CAAoBpB,MAApB,GAA6B,IAA7B;AACH;;AAED,eAAKoB,cAAL,CAAoBnB,eAApB,CAAoC,KAAKC,QAAL,EAApC;;AACA,eAAK6C,UAAL,CAAgB/C,MAAhB,GAAyB,KAAzB;AACA;AAAA;AAAA,oCAASR,IAAT,CAAc;AAAA;AAAA,wCAAWwD,WAAzB,EAAsCnB,IAAI,CAACW,CAA3C,EAA8CX,IAAI,CAACY,CAAnD;;AACA,eAAKrB,cAAL,CAAoBjB,YAApB;AAAA;AAAA,gDAAiD4B,OAAjD,CAAyDF,IAAzD;AACH;;AAESvE,QAAAA,cAAc,GAAS;AAC7B,eAAKyF,UAAL,CAAgB/C,MAAhB,GAAyB,IAAzB;AACH;;AAESzC,QAAAA,iBAAiB,CAACsE,IAAD,EAAkB;AACzC,cAAI,KAAKR,kBAAL,IAA2B,IAA/B,EAAqC;AACjC,iBAAKA,kBAAL,GAA0BlG,WAAW,CAAC,KAAK8H,mBAAN,CAArC;AACA,iBAAK5B,kBAAL,CAAwBvB,MAAxB,GAAiC,KAAKC,WAAtC;AACH,WAHD,MAGO;AACH,iBAAKsB,kBAAL,CAAwBrB,MAAxB,GAAiC,IAAjC;AACH;;AACD,eAAKqB,kBAAL,CAAwBpB,eAAxB,CAAwC,KAAKC,QAAL,EAAxC;;AACA,eAAKmB,kBAAL,CAAwBlB,YAAxB;AAAA;AAAA,8CAAoD4B,OAApD,CAA4DF,IAA5D;AACH;AAID;AACJ;AACA;;;AACcqB,QAAAA,aAAa,GAAS;AAC5B;AAAA;AAAA,4CAAanC,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKmC,cAAL,IAAuB,IAA3B,EAAiC;AAC7B,iBAAKA,cAAL,GAAsBhI,WAAW,CAAC,KAAKiI,eAAN,CAAjC;AACA,iBAAKD,cAAL,CAAoBrD,MAApB,GAA6B,KAAKC,WAAlC;AACH,WAHD,MAGO;AACH,iBAAKoD,cAAL,CAAoBnD,MAApB,GAA6B,IAA7B;AACH;;AACD,eAAKmD,cAAL,CAAoBlD,eAApB,CAAoC,KAAKC,QAAL,EAApC;;AACA,eAAKiD,cAAL,CAAoBhD,YAApB;AAAA;AAAA,gDAAiDkD,UAAjD;AACH;AAED;AACJ;AACA;;;AACc1F,QAAAA,aAAa,GAAS;AAC5B,cAAIgE,QAAQ,GAAG,KAAK2B,WAAL,CAAiBC,IAAjB,CAAsB5B,QAArC;AACA,cAAI6B,OAAO,GAAG;AAAA;AAAA,4CAAapE,WAAb,GAA2BqB,KAA3B,CAAiCgD,cAAjC,EAAd;AAEA,cAAIC,CAAC,GAAG,CAAR;AACA/B,UAAAA,QAAQ,CAAC+B,CAAD,CAAR,CAAYC,cAAZ,CAA2B,WAA3B,EAAwCxD,YAAxC,CAAqDjF,KAArD,EAA4D0I,MAA5D,GAAqE,QAAQ;AAAA;AAAA,8BAAMC,YAAN,CAAmBL,OAAO,CAAC,QAAD,CAA1B,CAA7E;AACAE,UAAAA,CAAC,IAAE,CAAH;;AAGA,eAAK,IAAII,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAK/G,SAAL,CAAe6E,MAA3C,EAAmDkC,KAAK,EAAxD,EAA4D;AACxD,kBAAMC,GAAG,GAAG,KAAKhH,SAAL,CAAe+G,KAAf,CAAZ;AACA,gBAAIE,KAAK,GAAGrC,QAAQ,CAAC+B,CAAD,CAAR,CAAYC,cAAZ,CAA2B,WAA3B,EAAwCxD,YAAxC,CAAqDjF,KAArD,CAAZ;;AAEA,gBAAG6I,GAAG,CAAC9G,GAAJ,IAAW,MAAd,EAAqB;AACjB+G,cAAAA,KAAK,CAACJ,MAAN,GAAeG,GAAG,CAAC7G,IAAJ,GAAW;AAAA;AAAA,kCAAM2G,YAAN,CAAmBL,OAAO,CAACO,GAAG,CAAC9G,GAAL,CAA1B,CAA1B;AACH,aAFD,MAEK;AACD+G,cAAAA,KAAK,CAACJ,MAAN,GAAeG,GAAG,CAAC7G,IAAJ,GAAW;AAAA;AAAA,kCAAM2G,YAAN,CAAmBL,OAAO,CAACO,GAAG,CAAC9G,GAAL,CAA1B,CAAX,GAAkD,GAAlD,GAAwD;AAAA;AAAA,kCAAM4G,YAAN,CAAmBL,OAAO,CAAC,gBAAD,CAA1B,CAAvE;AACH;;AAEDE,YAAAA,CAAC,IAAE,CAAH;AACH;;AAED,eAAK,IAAII,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAK3G,WAAL,CAAiByE,MAA7C,EAAqDkC,KAAK,EAA1D,EAA8D;AAC1D,kBAAMC,GAAG,GAAG,KAAK5G,WAAL,CAAiB2G,KAAjB,CAAZ;AACA,gBAAIE,KAAK,GAAGrC,QAAQ,CAAC+B,CAAD,CAAR,CAAYC,cAAZ,CAA2B,WAA3B,EAAwCxD,YAAxC,CAAqDjF,KAArD,CAAZ;AACA8I,YAAAA,KAAK,CAACJ,MAAN,GAAeG,GAAG,CAAC7G,IAAJ,GAAW;AAAA;AAAA,gCAAM2G,YAAN,CAAmBL,OAAO,CAACO,GAAG,CAAC9G,GAAL,CAA1B,CAA1B;AACAyG,YAAAA,CAAC,IAAE,CAAH;AACH;AAEJ;AAID;AACJ;AACA;;;AACcO,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,4CAAalD,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKQ,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiBrG,WAAW,CAAC,KAAK+I,UAAN,CAA5B;AACA,iBAAK1C,SAAL,CAAe1B,MAAf,GAAwB,KAAKC,WAA7B;AACH,WAHD,MAGO;AACH,iBAAKyB,SAAL,CAAexB,MAAf,GAAwB,IAAxB;AACH;;AACD,eAAKwB,SAAL,CAAevB,eAAf,CAA+B,KAAKC,QAAL,EAA/B;AACH;AAKD;AACJ;AACA;AACA;;;AACcjC,QAAAA,SAAS,CAAC4D,IAAD,EAAkB;AACjC,cAAI,KAAKJ,eAAL,IAAwB,IAA5B,EAAkC;AAC9B,iBAAKA,eAAL,GAAuBtG,WAAW,CAAC,KAAKgJ,eAAN,CAAlC;AACA,iBAAK1C,eAAL,CAAqB3B,MAArB,GAA8B,KAAKC,WAAnC;AACH,WAHD,MAGO;AACH,iBAAK0B,eAAL,CAAqBzB,MAArB,GAA8B,IAA9B;AACH;;AACD,eAAKyB,eAAL,CAAqBxB,eAArB,CAAqC,KAAKC,QAAL,EAArC;;AACA,eAAKuB,eAAL,CAAqBtB,YAArB;AAAA;AAAA,wCAA8C4B,OAA9C,CAAsDF,IAAtD;AACH;;AAISuC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAarD,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKqD,UAAL,IAAmB,IAAvB,EAA6B;AACzB,iBAAKA,UAAL,GAAkBlJ,WAAW,CAAC,KAAKmJ,WAAN,CAA7B;AACA,iBAAKD,UAAL,CAAgBvE,MAAhB,GAAyB,KAAKC,WAA9B;AACH,WAHD,MAGO;AACH,iBAAKsE,UAAL,CAAgBrE,MAAhB,GAAyB,IAAzB;AACH;;AACD,eAAKqE,UAAL,CAAgBpE,eAAhB,CAAgC,KAAKC,QAAL,EAAhC;AACH;;AAGSqE,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,4CAAaxD,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKwD,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiBrJ,WAAW,CAAC,KAAKsJ,UAAN,CAA5B;AACA,iBAAKD,SAAL,CAAe1E,MAAf,GAAwB,KAAKC,WAA7B;AACH,WAHD,MAGO;AACH,iBAAKyE,SAAL,CAAexE,MAAf,GAAwB,IAAxB;AACH;;AACD,eAAKwE,SAAL,CAAevE,eAAf,CAA+B,KAAKC,QAAL,EAA/B;AACH;;AAISwE,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,4CAAa3D,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAK2D,cAAL,IAAuB,IAA3B,EAAiC;AAC7B,iBAAKA,cAAL,GAAsBxJ,WAAW,CAAC,KAAKyJ,eAAN,CAAjC;AACA,iBAAKD,cAAL,CAAoB7E,MAApB,GAA6B,KAAKC,WAAlC;AACH,WAHD,MAGO;AACH,iBAAK4E,cAAL,CAAoB3E,MAApB,GAA6B,IAA7B;AACH;;AACD,eAAK2E,cAAL,CAAoB1E,eAApB,CAAoC,KAAKC,QAAL,EAApC;;AACA,eAAKyE,cAAL,CAAoBxE,YAApB;AAAA;AAAA,gDAAiD0E,QAAjD;AACH;;AAESvG,QAAAA,oBAAoB,GAAS;AACnC;AAAA;AAAA,4CAAayC,QAAb,CAAsBC,SAAtB;AACAJ,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ;;AACA,cAAI,KAAKiE,mBAAL,IAA4B,IAAhC,EAAsC;AAClC,iBAAKA,mBAAL,GAA2B3J,WAAW,CAAC,KAAK4J,oBAAN,CAAtC;AACA,iBAAKD,mBAAL,CAAyBhF,MAAzB,GAAkC,KAAKC,WAAvC;AAEH,WAJD,MAIO;AACH,iBAAK+E,mBAAL,CAAyB9E,MAAzB,GAAkC,IAAlC;AACH;;AAED,eAAK8E,mBAAL,CAAyB7E,eAAzB,CAAyC,KAAKC,QAAL,EAAzC;AAEH;;AAES1B,QAAAA,mBAAmB,GAAS;AAClC;AAAA;AAAA,4CAAauC,QAAb,CAAsBC,SAAtB;AACAJ,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ;;AACA,cAAI,KAAKmE,kBAAL,IAA2B,IAA/B,EAAqC;AACjC,iBAAKA,kBAAL,GAA0B7J,WAAW,CAAC,KAAK8J,mBAAN,CAArC;AACA,iBAAKD,kBAAL,CAAwBlF,MAAxB,GAAiC,KAAKC,WAAtC;AACH,WAHD,MAGO;AACH,iBAAKiF,kBAAL,CAAwBhF,MAAxB,GAAiC,IAAjC;AACH;;AACD,eAAKgF,kBAAL,CAAwB/E,eAAxB,CAAwC,KAAKC,QAAL,EAAxC;AAEH;;AAEDgF,QAAAA,eAAe,GAAQ;AACnB;AAAA;AAAA,4CAAanE,QAAb,CAAsBC,SAAtB;AACA,eAAKrC,WAAL,CAAiB,CAAjB;AACH;;AAESA,QAAAA,WAAW,CAACyD,IAAW,GAAC,CAAb,EAAgB+C,OAAmB,GAAG,IAAtC,EAA4CC,QAAe,GAAC,CAAC,CAA7D,EAAsE;AACvFxE,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BuB,IAA3B,EAAiC+C,OAAjC,EAA0CC,QAA1C;;AACA,cAAI,KAAKC,UAAL,IAAmB,IAAvB,EAA6B;AACzB,iBAAKA,UAAL,GAAkBlK,WAAW,CAAC,KAAKmK,WAAN,CAA7B;AACA,iBAAKD,UAAL,CAAgBvF,MAAhB,GAAyB,KAAKC,WAA9B;AACH,WAHD,MAGO;AACH,iBAAKsF,UAAL,CAAgBrF,MAAhB,GAAyB,IAAzB;AACH;;AACD,eAAKqF,UAAL,CAAgBpF,eAAhB,CAAgC,KAAKC,QAAL,EAAhC;;AACA,eAAKmF,UAAL,CAAgBlF,YAAhB;AAAA;AAAA,wCAAyC4B,OAAzC,CAAiDK,IAAjD,EAAuD+C,OAAvD,EAAgEC,QAAhE;AACH;;AAESvG,QAAAA,YAAY,GAAE;AACpB;AAAA;AAAA,4CAAakC,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKqE,UAAT,EAAqB;AAClB,iBAAKA,UAAL,CAAgBrF,MAAhB,GAAyB,KAAzB;AACF;AACJ;;AAESjB,QAAAA,eAAe,CAACwG,GAAD,EAAYnD,IAAW,GAAC,CAAxB,EAA2B+C,OAAmB,GAAG,IAAjD,EAAuDC,QAAe,GAAC,CAAC,CAAxE,EAA0E;AAC/FxE,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B0E,GAA/B,EAAoCnD,IAApC,EAA0C+C,OAA1C,EAAmDC,QAAnD;AACA;AAAA;AAAA,4CAAarE,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKwE,cAAL,IAAuB,IAA3B,EAAiC;AAC7B,iBAAKA,cAAL,GAAsBrK,WAAW,CAAC,KAAKsK,eAAN,CAAjC;AACA,iBAAKD,cAAL,CAAoB1F,MAApB,GAA6B,KAAKC,WAAlC;AACH,WAHD,MAGO;AACH,iBAAKyF,cAAL,CAAoBxF,MAApB,GAA6B,IAA7B;AACH;;AACD,eAAKwF,cAAL,CAAoBvF,eAApB,CAAoC,KAAKC,QAAL,EAApC;;AACA,eAAKsF,cAAL,CAAoBrF,YAApB;AAAA;AAAA,gDAAiD4B,OAAjD,CAAyDwD,GAAzD,EAA8DnD,IAA9D,EAAoE+C,OAApE,EAA6EC,QAA7E;AACH,SAxf6C,CA2f9C;;;AACUhH,QAAAA,YAAY,CAACsH,GAAD,EAAc;AAChC,eAAKzG,OAAL,CAAa,WAASyG,GAAG,CAACC,IAAb,GAAkB,KAA/B,EAAsC,IAAtC;AACH;;AAESzG,QAAAA,UAAU,GAAS;AACzB,cAAI0G,QAAQ,GAAG;AAAA;AAAA,4CAAaxG,WAAb,GAA2BqB,KAA3B,CAAiCoF,WAAjC,EAAf;AACA,eAAKC,SAAL,CAAelC,MAAf,GAAwB,SAASgC,QAAQ,CAACG,QAA1C;AACA,eAAKC,QAAL,CAAcpC,MAAd,GAAuB,WAAWgC,QAAQ,CAACK,GAApB,GAA0B,EAAjD;AACH;;AAESC,QAAAA,iBAAiB,GAAQ;AAC/B;AAAA;AAAA,4CAAanF,QAAb,CAAsBC,SAAtB;;AAEA,cAAG,KAAKmF,YAAL,IAAqB,IAAxB,EAA6B;AACzB,iBAAKA,YAAL,GAAoBhL,WAAW,CAAC,KAAKiL,aAAN,CAA/B;AACA,iBAAKD,YAAL,CAAkBrG,MAAlB,GAA2B,KAAKC,WAAhC;AACH;;AACD,eAAKoG,YAAL,CAAkBnG,MAAlB,GAA2B,IAA3B;;AACA,eAAKmG,YAAL,CAAkBlG,eAAlB,CAAkC,KAAKC,QAAL,EAAlC;AAEH;;AAESmG,QAAAA,cAAc,GAAQ;AAC5B;AAAA;AAAA,4CAAatF,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKsF,YAAL,IAAqB,IAAxB,EAA6B;AACzB,iBAAKA,YAAL,GAAoBnL,WAAW,CAAC,KAAKoL,aAAN,CAA/B;AACA,iBAAKD,YAAL,CAAkBxG,MAAlB,GAA2B,KAAKC,WAAhC;AACH;;AACD,eAAKuG,YAAL,CAAkBtG,MAAlB,GAA2B,IAA3B;;AACA,eAAKsG,YAAL,CAAkBrG,eAAlB,CAAkC,KAAKC,QAAL,EAAlC;AAEH;;AAESlB,QAAAA,iBAAiB,CAACwD,CAAD,EAAWC,CAAX,EAAqB+D,IAArB,EAAkCC,IAAlC,EAAoD;AAC3E,cAAIC,QAAQ,GAAG,IAAItL,IAAJ,CAASoH,CAAT,EAAYC,CAAZ,CAAf;AACA,cAAIkE,QAAQ,GAAG,IAAIvL,IAAJ,CAASoL,IAAT,EAAeC,IAAf,CAAf;AACA,cAAIG,GAAG,GAAGxL,IAAI,CAACyL,eAAL,CAAqBH,QAArB,EAA+BC,QAA/B,CAAV;AACA/F,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkC2B,CAAlC,EAAqCC,CAArC,EAAwC+D,IAAxC,EAA8CC,IAA9C,EAAoDG,GAApD;;AAEA,cAAGA,GAAG,GAAG,MAAT,EAAgB;AACZ;AACH;;AAED,cAAG,KAAKE,aAAL,IAAsB,IAAzB,EAA8B;AAC1B,iBAAKA,aAAL,GAAqB3L,WAAW,CAAC,KAAK4L,cAAN,CAAhC;AACA,iBAAKD,aAAL,CAAmBhH,MAAnB,GAA4B,KAAKC,WAAjC;AACH;;AACD,eAAK+G,aAAL,CAAmB9G,MAAnB,GAA4B,IAA5B;;AACA,eAAK8G,aAAL,CAAmB7G,eAAnB,CAAmC,KAAKC,QAAL,EAAnC;AAEH;;AA9iB6C,O;;;;;iBAG3B,I;;;;;;;iBAGM,I;;;;;;;iBAIG,I;;;;;;;iBAIX,I;;;;;;;iBAIO,I;;;;;;;iBAIG,I;;;;;;;iBAID,I;;;;;;;iBAII,I;;;;;;;iBAIJ,I;;;;;;;iBAGC,I;;;;;;;iBAIN,I;;;;;;;iBAIK,I;;;;;;;iBAIJ,I;;;;;;;iBAID,I;;;;;;;iBAIG,I;;;;;;;iBAKE,I;;;;;;;iBAIK,I;;;;;;;iBAID,I;;;;;;;iBAIR,I;;;;;;;iBAII,I;;;;;;;iBAIF,I;;;;;;;iBAKC,I;;;;;;;iBAKN,I;;;;;;;iBAGG,I;;;;;;;iBAGH,I;;;;;;;iBAGD,I", "sourcesContent": ["import { _decorator, Component, Prefab, Node, Layout, Label, instantiate, UITransform, Vec2 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport LoginCommand from \"../../login/LoginCommand\";\nimport ArmySelectNodeLogic from \"./ArmySelectNodeLogic\";\nimport CityArmySettingLogic from \"./CityArmySettingLogic\";\nimport FacilityListLogic from \"./FacilityListLogic\";\nimport MapUICommand from \"./MapUICommand\";\nimport Dialog, { DialogType } from \"./Dialog\";\nimport UnionCommand from \"../../union/UnionCommand\";\nimport MapCommand from \"../MapCommand\";\nimport FortressAbout from \"./FortressAbout\";\nimport CityAboutLogic from \"./CityAboutLogic\";\nimport GeneralListLogic from \"./GeneralListLogic\";\nimport TransformLogic from \"./TransformLogic\";\nimport { Tools } from \"../../utils/Tools\";\nimport GeneralInfoLogic from \"./GeneralInfoLogic\";\nimport WarReportLogic from \"./WarReportLogic\";\nimport DrawRLogic from \"./DrawRLogic\";\nimport { GeneralData } from \"../../general/GeneralProxy\";\nimport SkillLogic from \"./SkillLogic\";\nimport { SkillConf } from \"../../config/skill/Skill\";\nimport SkillInfoLogic from \"./SkillInfoLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { Skill } from '../../skill/SkillProxy';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n\n\n@ccclass('MapUILogic')\nexport default class MapUILogic extends Component {\n\n    @property(Node)\n    contentNode:Node = null;\n\n    @property(Prefab)\n    facilityPrefab: Prefab = null;\n    protected _facilityNode: Node = null;\n\n    @property(Prefab)\n    armySettingPrefab: Prefab = null;\n    protected _armySettingNode: Node = null;\n\n    @property(Prefab)\n    dialog: Prefab = null;\n    protected _dialogNode: Node = null;\n\n    @property(Prefab)\n    generalPrefab: Prefab = null;\n    protected _generalNode: Node = null;\n\n    @property(Prefab)\n    generalDesPrefab: Prefab = null;\n    protected _generalDesNode: Node = null;\n\n    @property(Prefab)\n    cityAboutPrefab: Prefab = null;\n    protected _cityAboutNode: Node = null;\n\n    @property(Prefab)\n    fortressAboutPrefab: Prefab = null;\n    protected _fortressAboutNode: Node = null;\n\n    @property(Prefab)\n    warReportPrefab: Prefab = null;\n    protected _warReportNode: Node = null;\n    @property(Prefab)\n    armySelectPrefab: Prefab = null;\n    protected _armySelectNode: Node = null;\n\n    @property(Prefab)\n    drawPrefab: Prefab = null;\n    protected _drawNode: Node = null;\n\n    @property(Prefab)\n    drawResultrefab: Prefab = null;\n    protected _drawResultNode: Node = null;\n    \n    @property(Prefab)\n    unionPrefab: Prefab = null;\n    protected _unionNode: Node = null;\n\n    @property(Prefab)\n    chatPrefab: Prefab = null;\n    protected _chatNode: Node = null;\n\n    @property(Prefab)\n    collectPrefab: Prefab = null;\n    protected _collectNode: Node = null;\n\n\n    @property(Prefab)\n    transFormPrefab: Prefab = null;\n    protected _transFormNode: Node = null;\n\n    @property(Prefab)\n    generalConvertPrefab: Prefab = null;\n    protected _generalConvertNode: Node = null;\n\n    @property(Prefab)\n    generalRosterPrefab: Prefab = null;\n    protected _generalRosterNode: Node = null;\n\n    @property(Prefab)\n    skillPrefab: Prefab = null;\n    protected _skillNode: Node = null;\n\n    @property(Prefab)\n    skillInfoPrefab: Prefab = null;\n    protected _skillInfoNode: Node = null;\n\n    @property(Prefab)\n    settingPrefab: Prefab = null;\n    protected _settingNode: Node = null;\n\n    \n    @property(Prefab)\n    cloudAniPrefab: Prefab = null;\n    protected _cloudAniNode: Node = null;\n\n\n    @property(Node)\n    widgetNode: Node = null;\n\n    @property(Layout)\n    srollLayout: Layout = null;\n\n    @property(Label)\n    nameLabel: Label = null;\n\n    @property(Label)\n    ridLabel: Label = null;\n\n    protected _resArray: any = [];\n    protected _yieldArray: any = [];\n\n    protected onLoad(): void {\n\n        this._resArray.push({key:\"grain\", name:\"谷:\"});\n        this._resArray.push({key:\"wood\", name:\"木:\"});\n        this._resArray.push({key:\"iron\", name:\"铁:\"});\n        this._resArray.push({key:\"stone\", name:\"石:\"});\n        this._resArray.push({key:\"gold\", name:\"钱:\"});\n\n        this._yieldArray.push({key:\"wood_yield\", name:\"木+\"});\n        this._yieldArray.push({key:\"iron_yield\", name:\"铁+\"});\n        this._yieldArray.push({key:\"stone_yield\", name:\"石+\"});\n        this._yieldArray.push({key:\"grain_yield\", name:\"谷+\"});\n\n\n        EventMgr.on(LogicEvent.openCityAbout, this.openCityAbout, this);\n        EventMgr.on(LogicEvent.closeCityAbout, this.closeCityAbout, this);\n        EventMgr.on(LogicEvent.openFortressAbout, this.openFortressAbout, this);\n        EventMgr.on(LogicEvent.openFacility, this.openFacility, this);\n        EventMgr.on(LogicEvent.openArmySetting, this.openArmySetting, this);\n        EventMgr.on(LogicEvent.upateMyRoleRes, this.updateRoleRes, this);\n        EventMgr.on(LogicEvent.openGeneralDes, this.openGeneralDes, this);\n        EventMgr.on(LogicEvent.openGeneralChoose, this.openGeneralChoose, this);\n        EventMgr.on(LogicEvent.openArmySelectUi, this.onOpenArmySelectUI, this);\n        EventMgr.on(LogicEvent.openDrawResult, this.openDrawR, this);\n        EventMgr.on(LogicEvent.robLoginUI, this.robLoginUI, this);\n        EventMgr.on(LogicEvent.interiorCollect, this.onCollection, this);\n        EventMgr.on(LogicEvent.openGeneralConvert, this.onOpenGeneralConvert, this);\n        EventMgr.on(LogicEvent.openGeneralRoster, this.onOpenGeneralRoster, this);\n        EventMgr.on(LogicEvent.openGeneral, this.openGeneral, this);\n        EventMgr.on(LogicEvent.openSkill, this.onOpenSkill, this);\n        EventMgr.on(LogicEvent.closeSkill, this.onCloseSkill, this);\n        EventMgr.on(LogicEvent.openSkillInfo, this.onOpenSkillInfo, this);\n        EventMgr.on(LogicEvent.beforeScrollToMap, this.beforeScrollToMap, this);\n        EventMgr.on(LogicEvent.showTip, this.showTip, this);\n        \n        \n\n        this.updateRoleRes();\n        this.updateRole();\n        let unionId = MapCommand.getInstance().cityProxy.myUnionId;\n        if (unionId > 0) {\n            UnionCommand.getInstance().unionApplyList(unionId);\n        }\n    }\n\n    protected robLoginUI(): void {\n        this.showTip(\"账号在其他地方登录\",function () {\n            EventMgr.emit(LogicEvent.enterLogin);\n        });\n    }\n\n    protected showTip(text:string, close:Function):void {\n        if (this._dialogNode == null){\n            this._dialogNode = instantiate(this.dialog)\n            this._dialogNode.parent = this.contentNode;\n        }else{\n            this._dialogNode.active = true;\n        }\n        this._dialogNode.setSiblingIndex(this.topLayer());\n        this._dialogNode.getComponent(Dialog).show(text, DialogType.OnlyConfirm);\n        this._dialogNode.getComponent(Dialog).setClose(close)\n    }\n\n\n    protected onDestroy(): void {\n        this.clearAllNode();\n        MapUICommand.getInstance().proxy.clearData();\n        EventMgr.targetOff(this);\n\n        console.log(\"MapUILogic onDestroy\")\n    }\n\n    protected onBack(): void {\n        AudioManager.instance.playClick();\n        LoginCommand.getInstance().account_logout();\n    }\n\n\n    protected clearAllNode(): void {\n        this._facilityNode = null;\n        this._generalNode = null;\n        this._cityAboutNode = null;\n        this._fortressAboutNode = null;\n        this._armySelectNode = null;\n        this._armySettingNode = null;\n        this._drawNode = null;\n        this._drawResultNode = null;\n        this._generalDesNode = null;\n        this._dialogNode = null\n    }\n\n\n\n    public topLayer():number {\n        return this.contentNode.children.length+1;\n    }\n    /**\n     * 设施\n     */\n    protected openFacility(data: any): void {\n        if (this._facilityNode == null) {\n            this._facilityNode = instantiate(this.facilityPrefab);\n            this._facilityNode.parent = this.contentNode;\n        } else {\n            this._facilityNode.active = true;\n        }\n        this._facilityNode.setSiblingIndex(this.topLayer());\n        this._facilityNode.getComponent(FacilityListLogic).setData(data);\n    }\n\n    protected openArmySetting(cityId: number, order: number): void {\n        if (this._armySettingNode == null) {\n            this._armySettingNode = instantiate(this.armySettingPrefab);\n            this._armySettingNode.parent = this.contentNode;\n        } else {\n            this._armySettingNode.active = true;\n        }\n        this._armySettingNode.setSiblingIndex(this.topLayer());\n        this._armySettingNode.getComponent(CityArmySettingLogic).setData(cityId, order);\n    }\n    /**\n     * 武将\n     */\n    \n    protected onClickGeneral(){\n        AudioManager.instance.playClick();\n        this.openGeneral([]);\n    } \n    \n    protected openGeneral(data: number[], type: number = 0, position: number = 0): void {\n        if (this._generalNode == null) {\n            this._generalNode = instantiate(this.generalPrefab);\n            this._generalNode.parent = this.contentNode;\n        } else {\n            this._generalNode.active = true;\n        }\n        this._generalNode.setSiblingIndex(this.topLayer());\n        this._generalNode.getComponent(GeneralListLogic).setData(data, type, position);\n    }\n\n\n    /**\n     * 武将选择\n     * @param data \n     * @param zIndex \n     */\n    protected openGeneralChoose(data: number[], position: number = 0): void {\n        this.openGeneral(data, 1, position);\n    }\n\n    /**打开军队选择界面*/\n    protected onOpenArmySelectUI(cmd: number, x: number, y: number): void {\n        if (this._armySelectNode == null) {\n            this._armySelectNode = instantiate(this.armySelectPrefab);\n            this._armySelectNode.parent = this.contentNode;\n        } else {\n            this._armySelectNode.active = true;\n        }\n        this._armySelectNode.setSiblingIndex(this.topLayer());\n        this._armySelectNode.getComponent(ArmySelectNodeLogic).setData(cmd, x, y);\n    }\n\n\n    /**\n     * 武将详情\n     */\n    protected openGeneralDes(cfgData: any, curData: any): void {\n        if (this._generalDesNode == null) {\n            this._generalDesNode = instantiate(this.generalDesPrefab);\n            this._generalDesNode.parent = this.contentNode;\n        } else {\n            this._generalDesNode.active = true;\n        }\n        this._generalDesNode.setSiblingIndex(this.topLayer());\n        this._generalDesNode.getComponent(GeneralInfoLogic).setData(cfgData, curData);\n    }\n\n\n    /**\n     * 城市\n     */\n    protected openCityAbout(data: any): void {\n   \n        if (this._cityAboutNode == null) {\n            this._cityAboutNode = instantiate(this.cityAboutPrefab);\n            this._cityAboutNode.parent = this.contentNode;\n        } else {\n            this._cityAboutNode.active = true;\n        }\n\n        this._cityAboutNode.setSiblingIndex(this.topLayer());\n        this.widgetNode.active = false;\n        EventMgr.emit(LogicEvent.scrollToMap, data.x, data.y);\n        this._cityAboutNode.getComponent(CityAboutLogic).setData(data);\n    }\n\n    protected closeCityAbout(): void {\n        this.widgetNode.active = true;\n    }\n    \n    protected openFortressAbout(data: any): void {\n        if (this._fortressAboutNode == null) {\n            this._fortressAboutNode = instantiate(this.fortressAboutPrefab);\n            this._fortressAboutNode.parent = this.contentNode;\n        } else {\n            this._fortressAboutNode.active = true;\n        }\n        this._fortressAboutNode.setSiblingIndex(this.topLayer());\n        this._fortressAboutNode.getComponent(FortressAbout).setData(data);\n    }\n\n    \n\n    /**\n     * 战报\n     */\n    protected openWarReport(): void {\n        AudioManager.instance.playClick();\n        if (this._warReportNode == null) {\n            this._warReportNode = instantiate(this.warReportPrefab);\n            this._warReportNode.parent = this.contentNode;\n        } else {\n            this._warReportNode.active = true;\n        }\n        this._warReportNode.setSiblingIndex(this.topLayer());\n        this._warReportNode.getComponent(WarReportLogic).updateView();\n    }\n\n    /**\n     * 角色信息\n     */\n    protected updateRoleRes(): void {\n        var children = this.srollLayout.node.children;\n        var roleRes = LoginCommand.getInstance().proxy.getRoleResData();\n\n        var i = 0;\n        children[i].getChildByName(\"New Label\").getComponent(Label).string = \"令牌:\" + Tools.numberToShow(roleRes[\"decree\"]);\n        i+=1;\n        \n\n        for (let index = 0; index < this._resArray.length; index++) {\n            const obj = this._resArray[index];\n            var label = children[i].getChildByName(\"New Label\").getComponent(Label)\n\n            if(obj.key == \"gold\"){\n                label.string = obj.name + Tools.numberToShow(roleRes[obj.key]);\n            }else{\n                label.string = obj.name + Tools.numberToShow(roleRes[obj.key]) + \"/\" + Tools.numberToShow(roleRes[\"depot_capacity\"]);\n            }\n            \n            i+=1;\n        }\n\n        for (let index = 0; index < this._yieldArray.length; index++) {\n            const obj = this._yieldArray[index];\n            var label = children[i].getChildByName(\"New Label\").getComponent(Label)\n            label.string = obj.name + Tools.numberToShow(roleRes[obj.key]);\n            i+=1;\n        }\n\n    }\n\n\n\n    /**\n     * 抽卡\n     */\n    protected openDraw(): void {\n        AudioManager.instance.playClick();\n        if (this._drawNode == null) {\n            this._drawNode = instantiate(this.drawPrefab);\n            this._drawNode.parent = this.contentNode;\n        } else {\n            this._drawNode.active = true;\n        }\n        this._drawNode.setSiblingIndex(this.topLayer());\n    }\n\n\n\n\n    /**\n     * 抽卡结果\n     * @param data \n     */\n    protected openDrawR(data: any): void {\n        if (this._drawResultNode == null) {\n            this._drawResultNode = instantiate(this.drawResultrefab);\n            this._drawResultNode.parent = this.contentNode;\n        } else {\n            this._drawResultNode.active = true;\n        }\n        this._drawResultNode.setSiblingIndex(this.topLayer());\n        this._drawResultNode.getComponent(DrawRLogic).setData(data);\n    }\n\n\n\n    protected openUnion(): void {\n        AudioManager.instance.playClick();\n        if (this._unionNode == null) {\n            this._unionNode = instantiate(this.unionPrefab);\n            this._unionNode.parent = this.contentNode;\n        } else {\n            this._unionNode.active = true;\n        }\n        this._unionNode.setSiblingIndex(this.topLayer());\n    }\n\n\n    protected openChat(): void {\n        AudioManager.instance.playClick();\n        if (this._chatNode == null) {\n            this._chatNode = instantiate(this.chatPrefab);\n            this._chatNode.parent = this.contentNode;\n        } else {\n            this._chatNode.active = true;\n        }\n        this._chatNode.setSiblingIndex(this.topLayer());\n    }\n\n\n\n    protected openTr(): void {\n        AudioManager.instance.playClick();\n        if (this._transFormNode == null) {\n            this._transFormNode = instantiate(this.transFormPrefab);\n            this._transFormNode.parent = this.contentNode;\n        } else {\n            this._transFormNode.active = true;\n        }\n        this._transFormNode.setSiblingIndex(this.topLayer());\n        this._transFormNode.getComponent(TransformLogic).initView();\n    }\n\n    protected onOpenGeneralConvert(): void {\n        AudioManager.instance.playClick();\n        console.log(\"onOpenGeneralConvert\");\n        if (this._generalConvertNode == null) {\n            this._generalConvertNode = instantiate(this.generalConvertPrefab);\n            this._generalConvertNode.parent = this.contentNode;\n            \n        } else {\n            this._generalConvertNode.active = true;\n        }\n\n        this._generalConvertNode.setSiblingIndex(this.topLayer());\n\n    }\n\n    protected onOpenGeneralRoster(): void {\n        AudioManager.instance.playClick();\n        console.log(\"onOpenGeneralRoster\");\n        if (this._generalRosterNode == null) {\n            this._generalRosterNode = instantiate(this.generalRosterPrefab);\n            this._generalRosterNode.parent = this.contentNode;\n        } else {\n            this._generalRosterNode.active = true;\n        }\n        this._generalRosterNode.setSiblingIndex(this.topLayer());\n\n    }\n    \n    onClickSkillBtn(): void{\n        AudioManager.instance.playClick();\n        this.onOpenSkill(0);\n    }\n\n    protected onOpenSkill(type:number=0, general:GeneralData = null, skillPos:number=-1): void {\n        console.log(\"onOpenSkill\", type, general, skillPos);\n        if (this._skillNode == null) {\n            this._skillNode = instantiate(this.skillPrefab);\n            this._skillNode.parent = this.contentNode;\n        } else {\n            this._skillNode.active = true;\n        }\n        this._skillNode.setSiblingIndex(this.topLayer());\n        this._skillNode.getComponent(SkillLogic).setData(type, general, skillPos);\n    }\n\n    protected onCloseSkill(){\n        AudioManager.instance.playClick();\n        if (this._skillNode) {\n           this._skillNode.active = false;\n        } \n    }\n    \n    protected onOpenSkillInfo(cfg:Skill, type:number=0, general:GeneralData = null, skillPos:number=-1){\n        console.log(\"onOpenSkillInfo\", cfg, type, general, skillPos);\n        AudioManager.instance.playClick();\n        if (this._skillInfoNode == null) {\n            this._skillInfoNode = instantiate(this.skillInfoPrefab);\n            this._skillInfoNode.parent = this.contentNode;\n        } else {\n            this._skillInfoNode.active = true;\n        }\n        this._skillInfoNode.setSiblingIndex(this.topLayer());\n        this._skillInfoNode.getComponent(SkillInfoLogic).setData(cfg, type, general, skillPos);\n    }\n\n\n    //征收\n    protected onCollection(msg:any):void{\n        this.showTip(\"成功征收到 \"+msg.gold+\" 金币\", null);\n    }\n\n    protected updateRole(): void {\n        var roleData = LoginCommand.getInstance().proxy.getRoleData();\n        this.nameLabel.string = \"昵称: \" + roleData.nickName;\n        this.ridLabel.string = \"角色ID: \" + roleData.rid + \"\";\n    }\n\n    protected onClickCollection():void {\n        AudioManager.instance.playClick();\n      \n        if(this._collectNode == null){\n            this._collectNode = instantiate(this.collectPrefab);\n            this._collectNode.parent = this.contentNode;\n        }\n        this._collectNode.active = true;\n        this._collectNode.setSiblingIndex(this.topLayer());\n\n    }\n\n    protected onClickSetting():void {\n        AudioManager.instance.playClick();\n        if(this._settingNode == null){\n            this._settingNode = instantiate(this.settingPrefab);\n            this._settingNode.parent = this.contentNode;\n        }\n        this._settingNode.active = true;\n        this._settingNode.setSiblingIndex(this.topLayer());\n\n    }\n\n    protected beforeScrollToMap(x:number, y:number, oldx:number, oldy:number):void {\n        let newPoint = new Vec2(x, y);\n        let oldPoint = new Vec2(oldx, oldy);\n        let dis = Vec2.squaredDistance(newPoint, oldPoint);\n        console.log(\"beforeScrollToMap:\", x, y, oldx, oldy, dis);\n\n        if(dis < 360000){\n            return;\n        }\n\n        if(this._cloudAniNode == null){\n            this._cloudAniNode = instantiate(this.cloudAniPrefab);\n            this._cloudAniNode.parent = this.contentNode;\n        }\n        this._cloudAniNode.active = true;\n        this._cloudAniNode.setSiblingIndex(this.topLayer());\n\n    }\n\n    \n\n}\n"]}