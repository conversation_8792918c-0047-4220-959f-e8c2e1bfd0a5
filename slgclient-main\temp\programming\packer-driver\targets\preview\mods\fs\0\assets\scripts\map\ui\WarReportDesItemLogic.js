System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, RichText, Label, UITransform, Node, AudioManager, LogicEvent, SkillEffectType, GeneralCommand, SkillCommand, EventMgr, GeneralDataX, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _temp, _crd, ccclass, property, WarReportDesItemLogic;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfAudioManager(extras) {
    _reporterNs.report("AudioManager", "../../common/AudioManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLogicEvent(extras) {
    _reporterNs.report("LogicEvent", "../../common/LogicEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkillEffectType(extras) {
    _reporterNs.report("SkillEffectType", "../../config/skill/Skill", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGeneralCommand(extras) {
    _reporterNs.report("GeneralCommand", "../../general/GeneralCommand", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGeneralConfig(extras) {
    _reporterNs.report("GeneralConfig", "../../general/GeneralProxy", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGeneralData(extras) {
    _reporterNs.report("GeneralData", "../../general/GeneralProxy", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkillCommand(extras) {
    _reporterNs.report("SkillCommand", "../../skill/SkillCommand", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../utils/EventMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWarReport(extras) {
    _reporterNs.report("WarReport", "./MapUIProxy", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWarReportRound(extras) {
    _reporterNs.report("WarReportRound", "./MapUIProxy", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWarReportSkill(extras) {
    _reporterNs.report("WarReportSkill", "./MapUIProxy", _context.meta, extras);
  }

  _export("GeneralDataX", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      RichText = _cc.RichText;
      Label = _cc.Label;
      UITransform = _cc.UITransform;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      AudioManager = _unresolved_2.AudioManager;
    }, function (_unresolved_3) {
      LogicEvent = _unresolved_3.LogicEvent;
    }, function (_unresolved_4) {
      SkillEffectType = _unresolved_4.SkillEffectType;
    }, function (_unresolved_5) {
      GeneralCommand = _unresolved_5.default;
    }, function (_unresolved_6) {
      SkillCommand = _unresolved_6.default;
    }, function (_unresolved_7) {
      EventMgr = _unresolved_7.EventMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "15d17h2x1RGrrMVW/ctqz8x", "WarReportDesItemLogic", undefined);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GeneralDataX", GeneralDataX = class GeneralDataX {
        constructor() {
          _defineProperty(this, "gdata", void 0);

          _defineProperty(this, "gcfg", void 0);

          _defineProperty(this, "isAttack", void 0);
        }

      });

      _export("default", WarReportDesItemLogic = (_dec = ccclass('WarReportDesItemLogic'), _dec2 = property(RichText), _dec3 = property(Label), _dec4 = property(Label), _dec5 = property(Node), _dec(_class = (_class2 = (_temp = class WarReportDesItemLogic extends Component {
        constructor() {
          super(...arguments);

          _defineProperty(this, "_reportRound", null);

          _initializerDefineProperty(this, "warLab", _descriptor, this);

          _initializerDefineProperty(this, "roundsLabel", _descriptor2, this);

          _initializerDefineProperty(this, "endLab", _descriptor3, this);

          _initializerDefineProperty(this, "cNode", _descriptor4, this);

          _defineProperty(this, "warReport", null);

          _defineProperty(this, "attColor", "<color=#ff0000>");

          _defineProperty(this, "denColor", "<color=#00ff00>");

          _defineProperty(this, "skillColor", "<color=#FD6500>");

          _defineProperty(this, "lossColor", "<color=#F2C420>");

          _defineProperty(this, "endColor", "</color>");

          _defineProperty(this, "attstr", "攻");

          _defineProperty(this, "denStr", "防");
        }

        setData(data, warReport, isEnd) {
          this._reportRound = data;
          this.warReport = warReport;
          this.endLab.node.active = false;
          this.warLab.string = "";
          this.roundsLabel.string = "第" + this._reportRound.round + "轮/" + this._reportRound.turn + "回合"; //技能

          var str = this.skillString(data.attackBefore);
          this.warLab.string = str; //伤害

          if (this._reportRound.attack && this._reportRound.defense) {
            this.warLab.string += "\n";
            var att_cfg = (_crd && GeneralCommand === void 0 ? (_reportPossibleCrUseOfGeneralCommand({
              error: Error()
            }), GeneralCommand) : GeneralCommand).getInstance().proxy.getGeneralCfg(this._reportRound.attack.cfgId);
            var def_cfg = (_crd && GeneralCommand === void 0 ? (_reportPossibleCrUseOfGeneralCommand({
              error: Error()
            }), GeneralCommand) : GeneralCommand).getInstance().proxy.getGeneralCfg(this._reportRound.defense.cfgId);

            if (data.isAttack) {
              var aName = this.nameString(true, att_cfg, this._reportRound.attack);
              var bName = this.nameString(false, def_cfg, this._reportRound.defense);
              this.warLab.string += this.attColor + aName + this.endColor + " 对 " + this.denColor + bName + this.endColor + " 发起攻击，" + this.denColor + bName + this.endColor + " 损失 " + this.lossColor + this._reportRound.defenseLoss + this.endColor + " 士兵";
            } else {
              var _bName = this.nameString(true, att_cfg, this._reportRound.attack);

              var _aName = this.nameString(false, def_cfg, this._reportRound.defense);

              this.warLab.string += this.denColor + _aName + this.endColor + " 对 " + this.attColor + _bName + this.endColor + " 发起攻击，" + this.attColor + _bName + this.endColor + " 损失 " + this.lossColor + this._reportRound.defenseLoss + this.endColor + " 士兵";
            }
          }

          if (data.attackAfter.length > 0) {
            this.warLab.string += "\n";

            var _str = this.skillString(data.attackAfter);

            this.warLab.string += _str;
          }

          if (data.defenseAfter.length > 0) {
            this.warLab.string += "\n";

            var _str2 = this.skillString(data.defenseAfter);

            this.warLab.string += _str2;
          }

          this.cNode.getComponent(UITransform).height = this.warLab.getComponent(UITransform).height;

          if (isEnd) {
            this.endLab.node.active = true;
            this.endLab.string = "";

            if (this.warReport.result == 0) {
              var _str3 = "我方主将兵力被消耗殆尽，战斗失败";
              this.endLab.string = _str3;
            } else if (this.warReport.result == 1) {
              var _str4 = "战斗不分胜负，打平";
              this.endLab.string = _str4;
            } else if (this.warReport.result == 2) {
              var _str5 = "对方主将兵力被消耗殆尽，";

              if (1 == this.warReport.occupy) {
                _str5 += "我方占领了(" + this.warReport.x + "," + this.warReport.y + ")领地";
                this.endLab.string = _str5;
              } else {
                var destroy = this.warReport.destroy_durable / 100;
                _str5 += "对(" + this.warReport.x + "," + this.warReport.y + ")领地造成" + Math.ceil(destroy) + "破坏";
                this.endLab.string = _str5;
              }
            }

            this.cNode.getComponent(UITransform).height = this.warLab.getComponent(UITransform).height + this.endLab.getComponent(UITransform).height + 20;
          }

          this.node.getComponent(UITransform).height = this.cNode.getComponent(UITransform).height + 40;
        }

        getGeneralX(id) {
          var gx = new GeneralDataX(); // console.log("getGeneralX:", this.warReport);

          var attgs = this.warReport.beg_attack_general;

          for (var i = 0; i < attgs.length; i++) {
            var g = attgs[i];

            if (g.id == id) {
              gx.gdata = g;
              gx.isAttack = true;
              gx.gcfg = (_crd && GeneralCommand === void 0 ? (_reportPossibleCrUseOfGeneralCommand({
                error: Error()
              }), GeneralCommand) : GeneralCommand).getInstance().proxy.getGeneralCfg(gx.gdata.cfgId);
              return gx;
            }
          }

          var dengs = this.warReport.beg_defense_general;

          for (var _i = 0; _i < dengs.length; _i++) {
            var _g = dengs[_i];

            if (_g.id == id) {
              gx.gdata = _g;
              gx.isAttack = false;
              gx.gcfg = (_crd && GeneralCommand === void 0 ? (_reportPossibleCrUseOfGeneralCommand({
                error: Error()
              }), GeneralCommand) : GeneralCommand).getInstance().proxy.getGeneralCfg(gx.gdata.cfgId);
              return gx;
            }
          }
        }

        skillString(skills) {
          var str = "";

          for (var i = 0; i < skills.length; i++) {
            var b = skills[i];
            var gx1 = this.getGeneralX(b.fromId);
            var skillCfg = (_crd && SkillCommand === void 0 ? (_reportPossibleCrUseOfSkillCommand({
              error: Error()
            }), SkillCommand) : SkillCommand).getInstance().proxy.getSkillCfg(b.cfgId);

            if (gx1.isAttack) {
              str += this.attColor + this.nameString(true, gx1.gcfg, gx1.gdata) + this.endColor;
            } else {
              str += this.denColor + this.nameString(false, gx1.gcfg, gx1.gdata) + this.endColor;
            }

            str += " 使用技能 ";
            str += this.skillColor + skillCfg.name + "(lv" + b.lv + ") " + this.endColor;
            str += "作用于 ";

            for (var j = 0; j < b.toId.length; j++) {
              var to = b.toId[j];
              var gx2 = this.getGeneralX(to);

              if (gx2.isAttack) {
                str += this.attColor + this.nameString(true, gx2.gcfg, gx2.gdata);
              } else {
                str += this.denColor + this.nameString(false, gx2.gcfg, gx2.gdata);
              }

              if (j < b.toId.length - 1) {
                str += ",";
                str += this.endColor;
              } else {
                str += this.endColor;
                str += " 身上";
              }
            }

            str += this.skillColor;
            var estr = this.effectString(b);
            str += estr;
            str += this.endColor;
            str += this.killString(b);
          }

          return str;
        }

        effectString(skill) {
          var str = "";

          for (var i = 0; i < skill.includeEffect.length; i++) {
            var ie = skill.includeEffect[i];
            var ev = skill.effectValue[i];
            var er = skill.effectRound[i];

            if (ie == (_crd && SkillEffectType === void 0 ? (_reportPossibleCrUseOfSkillEffectType({
              error: Error()
            }), SkillEffectType) : SkillEffectType).Defense) {
              str += "防御提升" + ev;
            } else if (ie == (_crd && SkillEffectType === void 0 ? (_reportPossibleCrUseOfSkillEffectType({
              error: Error()
            }), SkillEffectType) : SkillEffectType).Force) {
              str += "武力提升" + ev;
            } else if (ie == (_crd && SkillEffectType === void 0 ? (_reportPossibleCrUseOfSkillEffectType({
              error: Error()
            }), SkillEffectType) : SkillEffectType).Strategy) {
              str += "谋略提升" + ev;
            } else if (ie == (_crd && SkillEffectType === void 0 ? (_reportPossibleCrUseOfSkillEffectType({
              error: Error()
            }), SkillEffectType) : SkillEffectType).Speed) {
              str += "速度提升" + ev;
            } else if (ie == (_crd && SkillEffectType === void 0 ? (_reportPossibleCrUseOfSkillEffectType({
              error: Error()
            }), SkillEffectType) : SkillEffectType).Destroy) {
              str += "破坏提升" + ev;
            }

            if (er > 0) {
              str += "持续" + er + "回合";
            }
          }

          return str;
        }

        killString(skill) {
          if (!skill.kill) {
            return "";
          }

          var str = "造成";

          for (var i = 0; i < skill.kill.length; i++) {
            var kill = skill.kill[i];
            var to = skill.toId[i];
            var g = this.getGeneralX(to);

            if (g.isAttack) {
              str += this.attColor + " " + this.nameString(true, g.gcfg, g.gdata) + " " + this.endColor + "损失" + kill + "士兵";
            } else {
              str += this.denColor + " " + this.nameString(false, g.gcfg, g.gdata) + " " + this.endColor + "损失" + kill + "士兵";
            }
          }

          return str;
        }

        nameString(isAttack, cfg, general) {
          if (isAttack) {
            var position = -1;

            for (var index = 0; index < this.warReport.beg_attack_general.length; index++) {
              var g = this.warReport.beg_attack_general[index];

              if (g.id == general.id) {
                position = index;
                break;
              }
            }

            return this.attstr + cfg.name + "(" + this.positionString(position) + ")";
          } else {
            var _position = -1;

            for (var _index = 0; _index < this.warReport.beg_attack_general.length; _index++) {
              var _g2 = this.warReport.beg_defense_general[_index];

              if (_g2.id == general.id) {
                _position = _index;
                break;
              }
            }

            return this.denStr + cfg.name + "(" + this.positionString(_position) + ")";
          }
        }

        positionString(position) {
          if (position == 0) {
            return "主将";
          } else {
            return "副将";
          }
        }

        clickPos() {
          console.log("clickPos");
          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick();
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
            error: Error()
          }), LogicEvent) : LogicEvent).closeReport);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && LogicEvent === void 0 ? (_reportPossibleCrUseOfLogicEvent({
            error: Error()
          }), LogicEvent) : LogicEvent).scrollToMap, this.warReport.x, this.warReport.y);
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "warLab", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "roundsLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "endLab", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "cNode", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=WarReportDesItemLogic.js.map