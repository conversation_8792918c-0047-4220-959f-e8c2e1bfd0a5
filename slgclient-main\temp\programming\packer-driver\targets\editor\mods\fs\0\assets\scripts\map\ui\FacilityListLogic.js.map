{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "Label", "FacilityDesLogic", "FacilityItemLogic", "MapUICommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "FacilityListLogic", "Map", "onLoad", "initView", "on", "updateMyFacilities", "updateView", "updateMyFacility", "updateFacility", "selectFacilityItem", "onSelectItem", "upateMyRoleRes", "onUpdateMyRoleRes", "onDestroy", "targetOff", "_itemLogics", "clear", "children", "scrollView", "content", "i", "length", "subChildren", "j", "item", "name", "indexOf", "type", "parseInt", "substring", "comp", "addComponent", "labelRate", "getChildByName", "getComponent", "labelName", "labelTime", "lockNode", "string", "set", "dataList", "getInstance", "proxy", "getMyFacilitys", "_curCityId", "size", "for<PERSON>ach", "data", "has", "logic", "get", "cfg", "getFacilityCfgByType", "isUnlock", "isFacilityUnlock", "setData", "_curSelectType", "setCurSelectType", "updateDesView", "cityId", "conditions", "getMyFacilityByType", "node", "onClickClose", "active", "instance", "playClick"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,K,OAAAA,K;;AAG3CC,MAAAA,gB;;AACAC,MAAAA,iB;;AACAC,MAAAA,Y;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OARH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAWTY,iB,WADpBF,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAACT,UAAD,C,oCAFb,MACqBU,iBADrB,SAC+CX,SAD/C,CACyD;AAAA;AAAA;;AAAA;;AAAA,8CAItB,CAJsB;;AAAA,kDAKlB,CAAC,CALiB;;AAAA,+CAMG,IAAIY,GAAJ,EANH;AAAA;;AAQ3CC,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,kBAAvB,EAA2C,KAAKC,UAAhD,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,gBAAvB,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,kBAAvB,EAA2C,KAAKC,YAAhD,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASN,EAAT,CAAY;AAAA;AAAA,wCAAWO,cAAvB,EAAuC,KAAKC,iBAA5C,EAA+D,IAA/D;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;;AACA,eAAKC,WAAL,CAAiBC,KAAjB;;AACA,eAAKD,WAAL,GAAmB,IAAnB;AACH;;AAESZ,QAAAA,QAAQ,GAAS;AACvB,cAAIc,QAAgB,GAAG,KAAKC,UAAL,CAAgBC,OAAhB,CAAwBF,QAA/C;;AACA,eAAK,IAAIG,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGH,QAAQ,CAACI,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,gBAAIE,WAAmB,GAAGL,QAAQ,CAACG,CAAD,CAAR,CAAYH,QAAtC;;AACA,iBAAK,IAAIM,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGD,WAAW,CAACD,MAAxC,EAAgDE,CAAC,EAAjD,EAAqD;AACjD,kBAAIC,IAAU,GAAGF,WAAW,CAACC,CAAD,CAA5B;;AACA,kBAAIC,IAAI,CAACC,IAAL,CAAUC,OAAV,CAAkB,kBAAlB,KAAyC,CAA7C,EAAgD;AAC5C,oBAAIC,IAAY,GAAGC,QAAQ,CAACJ,IAAI,CAACC,IAAL,CAAUI,SAAV,CAAoB,EAApB,CAAD,CAA3B;AACA,oBAAIC,IAAuB,GAAGN,IAAI,CAACO,YAAL;AAAA;AAAA,2DAA9B;AACAD,gBAAAA,IAAI,CAACE,SAAL,GAAiBR,IAAI,CAACS,cAAL,CAAoB,WAApB,EAAiCC,YAAjC,CAA8C3C,KAA9C,CAAjB;AACAuC,gBAAAA,IAAI,CAACK,SAAL,GAAiBX,IAAI,CAACS,cAAL,CAAoB,WAApB,EAAiCC,YAAjC,CAA8C3C,KAA9C,CAAjB;AACAuC,gBAAAA,IAAI,CAACM,SAAL,GAAiBZ,IAAI,CAACS,cAAL,CAAoB,WAApB,EAAiCC,YAAjC,CAA8C3C,KAA9C,CAAjB;AACAuC,gBAAAA,IAAI,CAACO,QAAL,GAAgBb,IAAI,CAACS,cAAL,CAAoB,UAApB,CAAhB;AACAH,gBAAAA,IAAI,CAACM,SAAL,CAAeE,MAAf,GAAwB,EAAxB;AACAR,gBAAAA,IAAI,CAACH,IAAL,GAAYA,IAAZ;;AACA,qBAAKZ,WAAL,CAAiBwB,GAAjB,CAAqBZ,IAArB,EAA2BG,IAA3B;AACH;AACJ;AACJ;AACJ;;AAESxB,QAAAA,UAAU,GAAS;AACzB,cAAIkC,QAA+B,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,cAAjC,CAAgD,KAAKC,UAArD,CAAtC;;AACA,cAAIJ,QAAQ,IAAIA,QAAQ,CAACK,IAAT,GAAgB,CAAhC,EAAmC;AAC/BL,YAAAA,QAAQ,CAACM,OAAT,CAAiB,CAACC,IAAD,EAAiBpB,IAAjB,KAAkC;AAC/C,kBAAI,KAAKZ,WAAL,CAAiBiC,GAAjB,CAAqBrB,IAArB,CAAJ,EAAgC;AAC5B;AACA,oBAAIsB,KAAwB,GAAG,KAAKlC,WAAL,CAAiBmC,GAAjB,CAAqBvB,IAArB,CAA/B;;AACA,oBAAIwB,GAAmB,GAAG;AAAA;AAAA,kDAAaV,WAAb,GAA2BC,KAA3B,CAAiCU,oBAAjC,CAAsDzB,IAAtD,CAA1B;AACA,oBAAI0B,QAAiB,GAAG;AAAA;AAAA,kDAAaZ,WAAb,GAA2BC,KAA3B,CAAiCY,gBAAjC,CAAkD,KAAKV,UAAvD,EAAmEjB,IAAnE,CAAxB;AACAsB,gBAAAA,KAAK,CAACM,OAAN,CAAc,KAAKX,UAAnB,EAA+BG,IAA/B,EAAqCI,GAArC,EAA0CE,QAA1C;AACH;AACJ,aARD;;AASA,gBAAI,KAAKG,cAAL,IAAuB,CAAC,CAA5B,EAA+B;AAC3B,mBAAKC,gBAAL,CAAsB,CAAtB,EAD2B,CACF;AAC5B;AACJ;;AAED,eAAKC,aAAL;AACH;;AAESlD,QAAAA,cAAc,CAACmD,MAAD,EAAiBZ,IAAjB,EAAuC;AAC3D,cAAI,KAAKH,UAAL,IAAmBe,MAAvB,EAA+B;AAC3B,gBAAI,KAAK5C,WAAL,CAAiBiC,GAAjB,CAAqBD,IAAI,CAACpB,IAA1B,CAAJ,EAAqC;AACjC;AACA,kBAAIsB,KAAwB,GAAG,KAAKlC,WAAL,CAAiBmC,GAAjB,CAAqBH,IAAI,CAACpB,IAA1B,CAA/B;;AACA,kBAAIwB,GAAmB,GAAG;AAAA;AAAA,gDAAaV,WAAb,GAA2BC,KAA3B,CAAiCU,oBAAjC,CAAsDL,IAAI,CAACpB,IAA3D,CAA1B;AACA,kBAAI0B,QAAiB,GAAG;AAAA;AAAA,gDAAaZ,WAAb,GAA2BC,KAA3B,CAAiCY,gBAAjC,CAAkD,KAAKV,UAAvD,EAAmEG,IAAI,CAACpB,IAAxE,CAAxB;AACAsB,cAAAA,KAAK,CAACM,OAAN,CAAc,KAAKX,UAAnB,EAA+BG,IAA/B,EAAqCI,GAArC,EAA0CE,QAA1C;AACH;;AACD,iBAAKtC,WAAL,CAAiB+B,OAAjB,CAAyB,CAACG,KAAD,EAA2BtB,IAA3B,KAA4C;AACjE,kBAAIwB,GAAmB,GAAG;AAAA;AAAA,gDAAaV,WAAb,GAA2BC,KAA3B,CAAiCU,oBAAjC,CAAsDH,KAAK,CAACF,IAAN,CAAWpB,IAAjE,CAA1B;;AACA,mBAAK,IAAIP,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG+B,GAAG,CAACS,UAAJ,CAAevC,MAA3C,EAAmDD,CAAC,EAApD,EAAwD;AACpD,oBAAI+B,GAAG,CAACS,UAAJ,CAAexC,CAAf,EAAkBO,IAAlB,IAA0BoB,IAAI,CAACpB,IAAnC,EAAyC;AACrC;AACA,sBAAIoB,IAAc,GAAG;AAAA;AAAA,oDAAaN,WAAb,GAA2BC,KAA3B,CAAiCmB,mBAAjC,CAAqD,KAAKjB,UAA1D,EAAsEK,KAAK,CAACF,IAAN,CAAWpB,IAAjF,CAArB;AACA,sBAAI0B,QAAiB,GAAG;AAAA;AAAA,oDAAaZ,WAAb,GAA2BC,KAA3B,CAAiCY,gBAAjC,CAAkD,KAAKV,UAAvD,EAAmEK,KAAK,CAACF,IAAN,CAAWpB,IAA9E,CAAxB;AACAsB,kBAAAA,KAAK,CAACM,OAAN,CAAc,KAAKX,UAAnB,EAA+BG,IAA/B,EAAqCI,GAArC,EAA0CE,QAA1C;AACA;AACH;AACJ;AACJ,aAXD;AAYH;;AACD,eAAKK,aAAL;AACH;;AAES9C,QAAAA,iBAAiB,GAAS;AAChC,eAAK8C,aAAL;AACH;;AAEShD,QAAAA,YAAY,CAACiD,MAAD,EAAiBhC,IAAjB,EAAqC;AACvD,cAAI,KAAKiB,UAAL,IAAmBe,MAAvB,EAA+B;AAC3B,iBAAKF,gBAAL,CAAsB9B,IAAtB;AACH;AACJ;;AAES+B,QAAAA,aAAa,GAAS;AAC5B,cAAIX,IAAc,GAAG;AAAA;AAAA,4CAAaN,WAAb,GAA2BC,KAA3B,CAAiCmB,mBAAjC,CAAqD,KAAKjB,UAA1D,EAAsE,KAAKY,cAA3E,CAArB;AACA,cAAIL,GAAmB,GAAG;AAAA;AAAA,4CAAaV,WAAb,GAA2BC,KAA3B,CAAiCU,oBAAjC,CAAsD,KAAKI,cAA3D,CAA1B;AACA,eAAKM,IAAL,CAAU5B,YAAV;AAAA;AAAA,oDAAyCqB,OAAzC,CAAiD,KAAKX,UAAtD,EAAkEG,IAAlE,EAAwEI,GAAxE;AACH;;AAESY,QAAAA,YAAY,GAAS;AAC3B,eAAKD,IAAL,CAAUE,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACH;;AAEMT,QAAAA,gBAAgB,CAAC9B,IAAD,EAAqB;AACxC,cAAI,KAAK6B,cAAL,IAAuB7B,IAA3B,EAAiC;AAC7B,iBAAK6B,cAAL,GAAsB7B,IAAtB;AACA,iBAAK+B,aAAL;AACH;AACJ;;AAEMH,QAAAA,OAAO,CAACR,IAAD,EAAkB;AAC5B,eAAKH,UAAL,GAAkBG,IAAI,CAACY,MAAvB;AACA,eAAKrD,UAAL;AACH;;AAvHoD,O;;;;;iBAE5B,I", "sourcesContent": ["import { _decorator, Component, ScrollView, Node, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport FacilityDesLogic from \"./FacilityDesLogic\";\nimport FacilityItemLogic from \"./FacilityItemLogic\";\nimport MapUICommand from \"./MapUICommand\";\nimport { Facility, FacilityConfig } from \"./MapUIProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('FacilityListLogic')\nexport default class FacilityListLogic extends Component {\n    @property(ScrollView)\n    scrollView: ScrollView = null;\n\n    protected _curCityId: number = 0;\n    protected _curSelectType: number = -1;\n    protected _itemLogics: Map<number, FacilityItemLogic> = new Map<number, FacilityItemLogic>();\n\n    protected onLoad(): void {\n        this.initView();\n        EventMgr.on(LogicEvent.updateMyFacilities, this.updateView, this);\n        EventMgr.on(LogicEvent.updateMyFacility, this.updateFacility, this);\n        EventMgr.on(LogicEvent.selectFacilityItem, this.onSelectItem, this);\n        EventMgr.on(LogicEvent.upateMyRoleRes, this.onUpdateMyRoleRes, this);\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._itemLogics.clear();\n        this._itemLogics = null;\n    }\n\n    protected initView(): void {\n        let children: Node[] = this.scrollView.content.children;\n        for (let i: number = 0; i < children.length; i++) {\n            let subChildren: Node[] = children[i].children;\n            for (let j: number = 0; j < subChildren.length; j++) {\n                let item: Node = subChildren[j];\n                if (item.name.indexOf(\"CityFacilityItem\") == 0) {\n                    let type: number = parseInt(item.name.substring(16));\n                    let comp: FacilityItemLogic = item.addComponent(FacilityItemLogic);\n                    comp.labelRate = item.getChildByName(\"labelRate\").getComponent(Label);\n                    comp.labelName = item.getChildByName(\"labelName\").getComponent(Label);\n                    comp.labelTime = item.getChildByName(\"labelTime\").getComponent(Label);\n                    comp.lockNode = item.getChildByName(\"lockNode\");\n                    comp.labelTime.string = \"\";\n                    comp.type = type;\n                    this._itemLogics.set(type, comp);\n                }\n            }\n        }\n    }\n\n    protected updateView(): void {\n        let dataList: Map<number, Facility> = MapUICommand.getInstance().proxy.getMyFacilitys(this._curCityId);\n        if (dataList && dataList.size > 0) {\n            dataList.forEach((data: Facility, type: number) => {\n                if (this._itemLogics.has(type)) {\n                    //有数据就更新\n                    let logic: FacilityItemLogic = this._itemLogics.get(type);\n                    let cfg: FacilityConfig = MapUICommand.getInstance().proxy.getFacilityCfgByType(type);\n                    let isUnlock: boolean = MapUICommand.getInstance().proxy.isFacilityUnlock(this._curCityId, type);\n                    logic.setData(this._curCityId, data, cfg, isUnlock);\n                }\n            });\n            if (this._curSelectType == -1) {\n                this.setCurSelectType(0);//默认选中主城\n            }\n        }\n\n        this.updateDesView();\n    }\n\n    protected updateFacility(cityId: number, data: Facility): void {\n        if (this._curCityId == cityId) {\n            if (this._itemLogics.has(data.type)) {\n                //有数据就更新\n                let logic: FacilityItemLogic = this._itemLogics.get(data.type);\n                let cfg: FacilityConfig = MapUICommand.getInstance().proxy.getFacilityCfgByType(data.type);\n                let isUnlock: boolean = MapUICommand.getInstance().proxy.isFacilityUnlock(this._curCityId, data.type);\n                logic.setData(this._curCityId, data, cfg, isUnlock);\n            }\n            this._itemLogics.forEach((logic: FacilityItemLogic, type: number) => {\n                let cfg: FacilityConfig = MapUICommand.getInstance().proxy.getFacilityCfgByType(logic.data.type);\n                for (let i: number = 0; i < cfg.conditions.length; i++) {\n                    if (cfg.conditions[i].type == data.type) {\n                        //涉及到了解锁条件\n                        let data: Facility = MapUICommand.getInstance().proxy.getMyFacilityByType(this._curCityId, logic.data.type);\n                        let isUnlock: boolean = MapUICommand.getInstance().proxy.isFacilityUnlock(this._curCityId, logic.data.type);\n                        logic.setData(this._curCityId, data, cfg, isUnlock);\n                        break;\n                    }\n                }\n            })\n        }\n        this.updateDesView();\n    }\n\n    protected onUpdateMyRoleRes(): void {\n        this.updateDesView();\n    }\n\n    protected onSelectItem(cityId: number, type: number): void {\n        if (this._curCityId == cityId) {\n            this.setCurSelectType(type);\n        }\n    }\n\n    protected updateDesView(): void {\n        let data: Facility = MapUICommand.getInstance().proxy.getMyFacilityByType(this._curCityId, this._curSelectType);\n        let cfg: FacilityConfig = MapUICommand.getInstance().proxy.getFacilityCfgByType(this._curSelectType);\n        this.node.getComponent(FacilityDesLogic).setData(this._curCityId, data, cfg);\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n    public setCurSelectType(type: number): void {\n        if (this._curSelectType != type) {\n            this._curSelectType = type;\n            this.updateDesView();\n        }\n    }\n\n    public setData(data: any): void {\n        this._curCityId = data.cityId;\n        this.updateView();\n    }\n}\n"]}