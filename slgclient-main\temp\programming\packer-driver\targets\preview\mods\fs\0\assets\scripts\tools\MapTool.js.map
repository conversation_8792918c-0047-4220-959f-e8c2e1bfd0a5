{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts"], "names": ["_decorator", "Component", "TiledMap", "EditBox", "Label", "MapResType", "JSB", "ccclass", "property", "MapTool", "onLoad", "console", "log", "tiledMap", "getRandomResData", "randomType", "Math", "floor", "random", "randomValue", "randomLevel", "SYS_FORTRESS", "onClickMake", "_mapSize", "getMapSize", "_mapGroundIds", "<PERSON><PERSON><PERSON><PERSON>", "getTiles", "city_positionIds", "data", "w", "h", "list", "_resList", "i", "length", "num", "push", "editBox", "string", "tipsLab", "path", "jsb", "fileUtils", "isDirectoryExist", "width", "height", "writeStringToFile", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;;AAG1CC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,G,UAAAA,G;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAMTS,O,WADpBF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAACN,QAAD,C,UAGRM,QAAQ,CAACL,OAAD,C,UAGRK,QAAQ,CAACJ,KAAD,C,oCATb,MACqBK,OADrB,SACqCR,SADrC,CAC+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,4CAWhB,IAXgB;;AAAA,iDAYP,IAZO;;AAAA,4CAaf,IAbe;AAAA;;AAejCS,QAAAA,MAAM,GAAS;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwB,KAAKC,QAA7B;AACH;;AAESC,QAAAA,gBAAgB,GAAa;AACnC,cAAIC,UAAkB,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,CAA3B,IAAgC,EAAzD;AACA,cAAIC,WAAmB,GAAGH,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,GAA3B,CAA1B;AACA,cAAIE,WAAmB,GAAG,CAA1B;;AACA,cAAID,WAAW,GAAG,EAAlB,EAAsB;AAClBC,YAAAA,WAAW,GAAG,CAAd;AACH,WAFD,MAEO,IAAID,WAAW,IAAI,EAAf,IAAqBA,WAAW,GAAG,EAAvC,EAA2C;AAC9CC,YAAAA,WAAW,GAAG,CAAd;AACH,WAFM,MAEA,IAAID,WAAW,IAAI,EAAf,IAAqBA,WAAW,GAAG,EAAvC,EAA2C;AAC9CC,YAAAA,WAAW,GAAG,CAAd;AACH,WAFM,MAEA,IAAID,WAAW,IAAI,EAAf,IAAqBA,WAAW,GAAG,EAAvC,EAA2C;AAC9CC,YAAAA,WAAW,GAAG,CAAd;AACH,WAFM,MAEA,IAAID,WAAW,IAAI,EAAf,IAAqBA,WAAW,GAAG,EAAvC,EAA2C;AAC9CC,YAAAA,WAAW,GAAG,CAAd;AACH,WAFM,MAED,IAAID,WAAW,IAAI,EAAf,IAAqBA,WAAW,GAAG,EAAvC,EAA2C;AAC7CC,YAAAA,WAAW,GAAG,CAAd;AACH,WAFK,MAEA,IAAID,WAAW,IAAI,EAAf,IAAqBA,WAAW,GAAG,EAAvC,EAA2C;AAC7CC,YAAAA,WAAW,GAAG,CAAd;AACH,WAFK,MAEA,IAAID,WAAW,IAAI,EAAf,IAAqBA,WAAW,GAAG,EAAvC,EAA2C;AAC7CC,YAAAA,WAAW,GAAG,CAAd;AACH,WAFK,MAEA;AACFL,YAAAA,UAAU,GAAG;AAAA;AAAA,0CAAWM,YAAxB;AACAD,YAAAA,WAAW,GAAG,CAAd;AACH;;AACD,iBAAO,CAACL,UAAD,EAAaK,WAAb,CAAP;AACH;;AAESE,QAAAA,WAAW,GAAS;AAC1B,eAAKC,QAAL,GAAgB,KAAKV,QAAL,CAAcW,UAAd,EAAhB;AACA,eAAKC,aAAL,GAAqB,KAAKZ,QAAL,CAAca,QAAd,CAAuB,UAAvB,EAAmCC,QAAnC,EAArB;AACA,cAAIC,gBAAgB,GAAG,KAAKf,QAAL,CAAca,QAAd,CAAuB,eAAvB,EAAwCC,QAAxC,EAAvB;AAGA,cAAIE,IAA2C,GAAG;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE,CAAX;AAAcC,YAAAA,IAAI,EAAE;AAApB,WAAlD;AACA,eAAKC,QAAL,GAAgB,EAAhB;;AACA,eAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKT,aAAL,CAAmBU,MAA/C,EAAuDD,CAAC,EAAxD,EAA4D;AACxD,gBAAIN,gBAAgB,CAACM,CAAD,CAAhB,GAAsB,CAA1B,EAA6B;AACzB,kBAAIE,GAAG,GAAGpB,IAAI,CAACC,KAAL,CAAYD,IAAI,CAACE,MAAL,KAAc,EAAf,GAAmB,CAA9B,CAAV;;AACA,mBAAKe,QAAL,CAAcI,IAAd,CAAmB,CAAC,EAAD,EAAKD,GAAL,CAAnB;AACH,aAHD,MAGO,IAAG,KAAKX,aAAL,CAAmBS,CAAnB,IAAwB,CAA3B,EAA8B;AACjC,mBAAKD,QAAL,CAAcI,IAAd,CAAmB,CAAC,CAAD,EAAI,CAAJ,CAAnB;AACH,aAFM,MAGF;AACD,mBAAKJ,QAAL,CAAcI,IAAd,CAAmB,KAAKvB,gBAAL,EAAnB;AACH;AACJ;;AAED,cAAI,KAAKwB,OAAL,CAAaC,MAAb,IAAuB,EAA3B,EAA8B;AAC1B,iBAAKC,OAAL,CAAaD,MAAb,GAAsB,WAAtB;AACA;AACH;;AAED,cAAI,CAACjC,GAAL,EAAU;AACN,iBAAKkC,OAAL,CAAaD,MAAb,GAAsB,mBAAtB;AACA;AACH;;AAED,cAAIE,IAAI,GAAG,KAAKH,OAAL,CAAaC,MAAxB;;AACA,cAAGG,GAAG,CAACC,SAAJ,CAAcC,gBAAd,CAA+BH,IAA/B,KAAwC,KAA3C,EAAiD;AAC7C,iBAAKD,OAAL,CAAaD,MAAb,GAAsB,OAAtB;AACA;AACH;;AAGDV,UAAAA,IAAI,CAACC,CAAL,GAAS,KAAKP,QAAL,CAAcsB,KAAvB;AACAhB,UAAAA,IAAI,CAACE,CAAL,GAAS,KAAKR,QAAL,CAAcuB,MAAvB;AACAjB,UAAAA,IAAI,CAACG,IAAL,GAAY,KAAKC,QAAjB;AACAS,UAAAA,GAAG,CAACC,SAAJ,CAAcI,iBAAd,CAAgCC,IAAI,CAACC,SAAL,CAAepB,IAAf,CAAhC,EAAsDY,IAAI,GAAG,gBAA7D;AAGA,eAAKD,OAAL,CAAaD,MAAb,GAAsB,MAAtB;AACH;;AA1F0C,O;;;;;iBAGtB,I;;;;;;;iBAGF,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, TiledMap, EditBox, Label, Size } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { MapResType } from \"../map/MapProxy\";\nimport { JSB } from 'cc/env';\n\n@ccclass('MapTool')\nexport default class MapTool extends Component {\n\n    @property(TiledMap)\n    tiledMap: TiledMap = null;\n\n    @property(EditBox)\n    editBox: EditBox = null;\n\n    @property(Label)\n    tipsLab: Label = null;\n\n    protected _mapSize: Size = null;\n    protected _mapGroundIds: number[] = null;\n    protected _resList: any[] = null;\n\n    protected onLoad(): void {\n        console.log(\"tiledMap\", this.tiledMap);\n    }\n\n    protected getRandomResData(): number[] {\n        let randomType: number = Math.floor(Math.random() * 4) + 52;\n        let randomValue: number = Math.floor(Math.random() * 100);\n        let randomLevel: number = 1;\n        if (randomValue < 20) {\n            randomLevel = 1;\n        } else if (randomValue >= 20 && randomValue < 40) {\n            randomLevel = 2;\n        } else if (randomValue >= 40 && randomValue < 55) {\n            randomLevel = 3;\n        } else if (randomValue >= 55 && randomValue < 65) {\n            randomLevel = 4;\n        } else if (randomValue >= 65 && randomValue < 75) {\n            randomLevel = 5;\n        }else if (randomValue >= 75 && randomValue < 85) {\n            randomLevel = 6;\n        }else if (randomValue >= 85 && randomValue < 94) {\n            randomLevel = 7;\n        }else if (randomValue >= 94 && randomValue < 99) {\n            randomLevel = 8;\n        }else {\n            randomType = MapResType.SYS_FORTRESS;\n            randomLevel = 5;\n        }\n        return [randomType, randomLevel];\n    }\n\n    protected onClickMake(): void {\n        this._mapSize = this.tiledMap.getMapSize();\n        this._mapGroundIds = this.tiledMap.getLayer(\"obstruct\").getTiles();\n        var city_positionIds = this.tiledMap.getLayer(\"city_position\").getTiles();\n\n\n        let data: { w: number, h: number, list: any[] } = { w: 0, h: 0, list: null };\n        this._resList = [];\n        for (let i: number = 0; i < this._mapGroundIds.length; i++) {\n            if (city_positionIds[i] > 0) {\n                var num = Math.floor((Math.random()*10)+1);\n                this._resList.push([51, num]);\n            } else if(this._mapGroundIds[i] > 0) {\n                this._resList.push([0, 0]);\n            }\n            else {\n                this._resList.push(this.getRandomResData());\n            }\n        }\n\n        if (this.editBox.string == \"\"){\n            this.tipsLab.string = \"请输入生成输出目录\";\n            return\n        }\n\n        if (!JSB) {\n            this.tipsLab.string = \"请使用 Windows 模拟器运行\";\n            return\n        }\n\n        var path = this.editBox.string;\n        if(jsb.fileUtils.isDirectoryExist(path) == false){\n            this.tipsLab.string = \"目录不存在\";\n            return\n        }\n        \n\n        data.w = this._mapSize.width;\n        data.h = this._mapSize.height;\n        data.list = this._resList;\n        jsb.fileUtils.writeStringToFile(JSON.stringify(data), path + \"/mapRes_0.json\");\n       \n\n        this.tipsLab.string = \"生成成功\";\n    }\n}\n"]}