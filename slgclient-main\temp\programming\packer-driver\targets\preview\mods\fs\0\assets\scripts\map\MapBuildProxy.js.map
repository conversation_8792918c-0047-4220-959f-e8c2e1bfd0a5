{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts"], "names": ["MapBuildData", "MapBuildProxy", "DateUtil", "MapCommand", "MapResType", "MapUtil", "EventMgr", "LogicEvent", "equalsServerData", "data", "rid", "name", "nick<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "type", "level", "opLevel", "op_level", "curDurable", "cur_durable", "maxDurable", "defender", "unionId", "union_id", "parentId", "parent_id", "unionName", "union_name", "occupyTime", "occupy_time", "giveUpTime", "giveUp_time", "endTime", "end_time", "createBuildData", "id", "buildData", "build", "x", "y", "max_durable", "getCellRadius", "isSysCity", "SYS_CITY", "isSysFortress", "SYS_FORTRESS", "isWarFree", "diff", "getServerTime", "getInstance", "proxy", "getWarFree", "isResBuild", "WOOD", "FORTRESS", "isInGiveUp", "leftTime", "isBuilding", "isUping", "isDestroying", "Map", "initData", "_mapBuilds", "length", "mapCellCount", "_lastBuildCellIds", "clear", "updateMyBuildIds", "clearData", "initMyBuilds", "builds", "_myBuilds", "i", "getIdByCellPoint", "push", "updateBuild", "removeBuild", "emit", "myId", "deleteBuild", "removeMyBuild", "index", "splice", "setMapScanBlock", "scanDatas", "areaId", "rBuilds", "mr_builds", "lastBuildCellIds", "has", "get", "buildCellIds", "addBuildCellIds", "updateBuildCellIds", "removeBuildCellIds", "areaIndex", "getAreaIdByCellPoint", "console", "log", "cellId", "indexOf", "set", "updateBuilds", "getBuild", "getMyBuildList", "updateSub", "myUnionId", "myParentId"], "mappings": ";;;+FASaA,Y,EA6HQC,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArIdC,MAAAA,Q;;AACAC,MAAAA,U;;AACCC,MAAAA,U,iBAAAA,U;;AACDC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;AAET;8BACaP,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,sCACT,CADS;;AAAA,uCAER,CAFQ;;AAAA,4CAGH,EAHG;;AAAA,wCAIP,EAJO;;AAAA,qCAKV,CALU;;AAAA,qCAMV,CANU;;AAAA,wCAOP,CAPO;;AAAA,yCAQN,CARM;;AAAA,2CASJ,CATI;;AAAA,8CAUD,CAVC;;AAAA,8CAWD,CAXC;;AAAA,4CAYH,CAZG;;AAAA,2CAaJ,CAbI;;AAAA,4CAcH,CAdG;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAoBfQ,QAAAA,gBAAgB,CAACC,IAAD,EAAY;AAC/B,cAAI,KAAKC,GAAL,IAAYD,IAAI,CAACC,GAAjB,IACG,KAAKC,IAAL,IAAaF,IAAI,CAACE,IADrB,IAEG,KAAKC,QAAL,IAAiBH,IAAI,CAACI,KAFzB,IAGG,KAAKC,IAAL,IAAaL,IAAI,CAACK,IAHrB,IAIG,KAAKC,KAAL,IAAcN,IAAI,CAACM,KAJtB,IAKG,KAAKC,OAAL,IAAgBP,IAAI,CAACQ,QALxB,IAMG,KAAKC,UAAL,IAAmBT,IAAI,CAACU,WAN3B,IAOG,KAAKC,UAAL,IAAmBX,IAAI,CAACW,UAP3B,IAQG,KAAKC,QAAL,IAAiBZ,IAAI,CAACY,QARzB,IASG,KAAKC,OAAL,IAAgBb,IAAI,CAACc,QATxB,IAUG,KAAKC,QAAL,IAAiBf,IAAI,CAACgB,SAVzB,IAWG,KAAKC,SAAL,IAAkBjB,IAAI,CAACkB,UAX1B,IAYG,KAAKC,UAAL,IAAmBnB,IAAI,CAACoB,WAZ3B,IAaG,KAAKC,UAAL,IAAmBrB,IAAI,CAACsB,WAb3B,IAcG,KAAKC,OAAL,IAAgBvB,IAAI,CAACwB,QAd5B,EAcsC;AAClC,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAE4B,eAAfC,eAAe,CAACzB,IAAD,EAAY0B,EAAZ,EAA4BC,SAA5B,EAA0E;AAAA,cAA9DD,EAA8D;AAA9DA,YAAAA,EAA8D,GAAjD,CAAiD;AAAA;;AAAA,cAA9CC,SAA8C;AAA9CA,YAAAA,SAA8C,GAApB,IAAoB;AAAA;;AACnG,cAAIC,KAAmB,GAAGD,SAA1B;;AACA,cAAIA,SAAS,IAAI,IAAjB,EAAuB;AACnBC,YAAAA,KAAK,GAAG,IAAIrC,YAAJ,EAAR;AACH;;AACDqC,UAAAA,KAAK,CAACF,EAAN,GAAWA,EAAX;AACAE,UAAAA,KAAK,CAAC3B,GAAN,GAAYD,IAAI,CAACC,GAAjB;AACA2B,UAAAA,KAAK,CAACzB,QAAN,GAAiBH,IAAI,CAACI,KAAtB;AACAwB,UAAAA,KAAK,CAAC1B,IAAN,GAAaF,IAAI,CAACE,IAAlB;AACA0B,UAAAA,KAAK,CAACC,CAAN,GAAU7B,IAAI,CAAC6B,CAAf;AACAD,UAAAA,KAAK,CAACE,CAAN,GAAU9B,IAAI,CAAC8B,CAAf;AACAF,UAAAA,KAAK,CAACvB,IAAN,GAAaL,IAAI,CAACK,IAAlB;AACAuB,UAAAA,KAAK,CAACtB,KAAN,GAAcN,IAAI,CAACM,KAAnB;AACAsB,UAAAA,KAAK,CAACrB,OAAN,GAAgBP,IAAI,CAACQ,QAArB;AACAoB,UAAAA,KAAK,CAACnB,UAAN,GAAmBT,IAAI,CAACU,WAAxB;AACAkB,UAAAA,KAAK,CAACjB,UAAN,GAAmBX,IAAI,CAAC+B,WAAxB;AACAH,UAAAA,KAAK,CAAChB,QAAN,GAAiBZ,IAAI,CAACY,QAAtB;AACAgB,UAAAA,KAAK,CAACf,OAAN,GAAgBb,IAAI,CAACc,QAArB;AACAc,UAAAA,KAAK,CAACb,QAAN,GAAiBf,IAAI,CAACgB,SAAtB;AACAY,UAAAA,KAAK,CAACX,SAAN,GAAkBjB,IAAI,CAACkB,UAAvB;AACAU,UAAAA,KAAK,CAACT,UAAN,GAAmBnB,IAAI,CAACoB,WAAxB;AACAQ,UAAAA,KAAK,CAACP,UAAN,GAAmBrB,IAAI,CAACsB,WAAxB;AACAM,UAAAA,KAAK,CAACL,OAAN,GAAgBvB,IAAI,CAACwB,QAArB;AACA,iBAAOI,KAAP;AACH;;AAEMI,QAAAA,aAAa,GAAW;AAC3B,cAAI,KAAKC,SAAL,EAAJ,EAAsB;AAClB,gBAAI,KAAK3B,KAAL,IAAc,CAAlB,EAAoB;AAChB,qBAAO,CAAP;AACH,aAFD,MAEM,IAAI,KAAKA,KAAL,IAAc,CAAlB,EAAoB;AACtB,qBAAO,CAAP;AACH,aAFK,MAEA;AACF,qBAAO,CAAP;AACH;AACJ,WARD,MAQM;AACF,mBAAO,CAAP;AACH;AACJ;;AAEM2B,QAAAA,SAAS,GAAU;AACtB,iBAAO,KAAK5B,IAAL,IAAa;AAAA;AAAA,wCAAW6B,QAA/B;AACH;;AAEMC,QAAAA,aAAa,GAAU;AAC1B,iBAAO,KAAK9B,IAAL,IAAa;AAAA;AAAA,wCAAW+B,YAA/B;AACH;;AAEMC,QAAAA,SAAS,GAAY;AACxB,cAAIC,IAAI,GAAG;AAAA;AAAA,oCAASC,aAAT,KAA2B,KAAKpB,UAA3C;;AACA,cAAGmB,IAAI,GAAG;AAAA;AAAA,wCAAWE,WAAX,GAAyBC,KAAzB,CAA+BC,UAA/B,EAAV,EAAsD;AAClD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEMC,QAAAA,UAAU,GAAW;AACxB,iBAAO,KAAKtC,IAAL,IAAW;AAAA;AAAA,wCAAWuC,IAAtB,IAA8B,KAAKvC,IAAL,GAAY;AAAA;AAAA,wCAAWwC,QAA5D;AACH;;AAEMC,QAAAA,UAAU,GAAY;AACzB,cAAIR,IAAI,GAAG;AAAA;AAAA,oCAASS,QAAT,CAAkB,KAAK1B,UAAvB,CAAX;AACA,iBAAOiB,IAAI,GAAG,CAAd;AACH,SAxGqB,CA0GtB;;;AACOU,QAAAA,UAAU,GAAY;AACzB,cAAIV,IAAI,GAAG;AAAA;AAAA,oCAASS,QAAT,CAAkB,KAAKxB,OAAvB,CAAX;AACA,iBAAOe,IAAI,GAAG,CAAP,IAAY,KAAKhC,KAAL,IAAc,CAAjC;AACH,SA9GqB,CAgHtB;;;AACO2C,QAAAA,OAAO,GAAY;AACtB,cAAIX,IAAI,GAAG;AAAA;AAAA,oCAASS,QAAT,CAAkB,KAAKxB,OAAvB,CAAX;AACA,iBAAOe,IAAI,GAAG,CAAP,IAAY,KAAKhC,KAAL,GAAa,CAAzB,IAA8B,KAAKC,OAAL,GAAe,CAApD;AACH,SApHqB,CAsHtB;;;AACO2C,QAAAA,YAAY,GAAY;AAC3B,cAAIZ,IAAI,GAAG;AAAA;AAAA,oCAASS,QAAT,CAAkB,KAAKxB,OAAvB,CAAX;AACA,iBAAOe,IAAI,GAAG,CAAP,IAAY,KAAK/B,OAAL,IAAgB,CAAnC;AACH;;AA1HqB,O;;yBA6HLf,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,8CACQ,EADR;;AAAA,6CAEO,EAFP;;AAAA,qDAGsB,IAAI2D,GAAJ,EAHtB;;AAAA,wCAIT,CAJS;;AAAA,6CAKJ,CALI;;AAAA,8CAMH,CANG;AAAA;;AAO/B;AACOC,QAAAA,QAAQ,GAAS;AACpB,eAAKC,UAAL,CAAgBC,MAAhB,GAAyB;AAAA;AAAA,kCAAQC,YAAjC;;AACA,eAAKC,iBAAL,CAAuBC,KAAvB;;AACA,eAAKC,gBAAL,GAHoB,CAGI;AAC3B;;AAEMC,QAAAA,SAAS,GAAS;AACrB,eAAKN,UAAL,CAAgBC,MAAhB,GAAyB,CAAzB;;AACA,eAAKE,iBAAL,CAAuBC,KAAvB;AACH;AAED;;;AACOG,QAAAA,YAAY,CAACC,MAAD,EAAsB;AACrC,eAAKC,SAAL,CAAeR,MAAf,GAAwB,CAAxB;;AACA,eAAK,IAAIS,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGF,MAAM,CAACP,MAAnC,EAA2CS,CAAC,EAA5C,EAAgD;AAC5C,gBAAIrC,EAAU,GAAG;AAAA;AAAA,oCAAQsC,gBAAR,CAAyBH,MAAM,CAACE,CAAD,CAAN,CAAUlC,CAAnC,EAAsCgC,MAAM,CAACE,CAAD,CAAN,CAAUjC,CAAhD,CAAjB;AACA,gBAAIF,KAAmB,GAAGrC,YAAY,CAACkC,eAAb,CAA6BoC,MAAM,CAACE,CAAD,CAAnC,EAAwCrC,EAAxC,CAA1B;;AACA,iBAAKoC,SAAL,CAAeG,IAAf,CAAoBrC,KAApB;AACH;AACJ;AAED;;;AACO8B,QAAAA,gBAAgB,GAAS;AAC5B,eAAK,IAAIK,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKD,SAAL,CAAeR,MAA3C,EAAmDS,CAAC,EAApD,EAAwD;AACpD,gBAAIrC,EAAU,GAAG;AAAA;AAAA,oCAAQsC,gBAAR,CAAyB,KAAKF,SAAL,CAAeC,CAAf,EAAkBlC,CAA3C,EAA8C,KAAKiC,SAAL,CAAeC,CAAf,EAAkBjC,CAAhE,CAAjB;AACA,iBAAKgC,SAAL,CAAeC,CAAf,EAAkBrC,EAAlB,GAAuBA,EAAvB;AACA,iBAAK2B,UAAL,CAAgB3B,EAAhB,IAAsB,KAAKoC,SAAL,CAAeC,CAAf,CAAtB;AACH;AACJ;AAED;;;AACOG,QAAAA,WAAW,CAACtC,KAAD,EAAmB;AACjC,cAAIA,KAAK,CAAC3B,GAAN,IAAa,CAAjB,EAAoB;AAChB;AACA,gBAAG2B,KAAK,CAACvB,IAAN,GAAa;AAAA;AAAA,0CAAW6B,QAA3B,EAAoC;AAChC,mBAAKiC,WAAL,CAAiBvC,KAAK,CAACC,CAAvB,EAA0BD,KAAK,CAACE,CAAhC;AACA;AACH;AAEJ;;AACD,cAAIJ,EAAU,GAAG;AAAA;AAAA,kCAAQsC,gBAAR,CAAyBpC,KAAK,CAACC,CAA/B,EAAkCD,KAAK,CAACE,CAAxC,CAAjB;AACA,cAAIH,SAAuB,GAAG,IAA9B;;AACA,cAAI,KAAK0B,UAAL,CAAgB3B,EAAhB,KAAuB,IAA3B,EAAiC;AAC7B;AACAC,YAAAA,SAAS,GAAGpC,YAAY,CAACkC,eAAb,CAA6BG,KAA7B,EAAoCF,EAApC,CAAZ;;AACA,iBAAKoC,SAAL,CAAeG,IAAf,CAAoBrC,KAApB;;AACA,iBAAKyB,UAAL,CAAgB3B,EAAhB,IAAsBC,SAAtB;AACH,WALD,MAKO;AACHA,YAAAA,SAAS,GAAGpC,YAAY,CAACkC,eAAb,CAA6BG,KAA7B,EAAoCF,EAApC,EAAwC,KAAK2B,UAAL,CAAgB3B,EAAhB,CAAxC,CAAZ;AACH;;AACD;AAAA;AAAA,oCAAS0C,IAAT,CAAc;AAAA;AAAA,wCAAWF,WAAzB,EAAsCvC,SAAtC;;AACA,cAAIA,SAAS,CAAC1B,GAAV,IAAiB,KAAKoE,IAA1B,EAAgC,CAC5B;AACH;AACJ;;AAEMF,QAAAA,WAAW,CAACtC,CAAD,EAAYC,CAAZ,EAA6B;AAC3C,cAAIJ,EAAU,GAAG;AAAA;AAAA,kCAAQsC,gBAAR,CAAyBnC,CAAzB,EAA4BC,CAA5B,CAAjB;AACA,eAAKuB,UAAL,CAAgB3B,EAAhB,IAAsB,IAAtB;AACA;AAAA;AAAA,oCAAS0C,IAAT,CAAc;AAAA;AAAA,wCAAWE,WAAzB,EAAsC5C,EAAtC,EAA0CG,CAA1C,EAA6CC,CAA7C;AACA,eAAKyC,aAAL,CAAmB1C,CAAnB,EAAsBC,CAAtB;AACH;;AAEMyC,QAAAA,aAAa,CAAC1C,CAAD,EAAYC,CAAZ,EAA2B;AAC3C,cAAI0C,KAAa,GAAG,CAAC,CAArB;;AACA,eAAK,IAAIT,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKD,SAAL,CAAeR,MAA3C,EAAmDS,CAAC,EAApD,EAAwD;AACpD,gBAAI,KAAKD,SAAL,CAAeC,CAAf,EAAkBlC,CAAlB,IAAuBA,CAAvB,IACG,KAAKiC,SAAL,CAAeC,CAAf,EAAkBjC,CAAlB,IAAuBA,CAD9B,EACiC;AACzB0C,cAAAA,KAAK,GAAGT,CAAR;AACA;AACP;AACJ;;AACD,cAAIS,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb,iBAAKV,SAAL,CAAeW,MAAf,CAAsBD,KAAtB,EAA6B,CAA7B;AACH;AACJ;;AAEME,QAAAA,eAAe,CAACC,SAAD,EAAiBC,MAAjB,EAA2C;AAAA,cAA1BA,MAA0B;AAA1BA,YAAAA,MAA0B,GAAT,CAAS;AAAA;;AAC7D,cAAIC,OAAc,GAAGF,SAAS,CAACG,SAA/B;;AACA,cAAID,OAAO,CAACvB,MAAR,GAAiB,CAArB,EAAwB;AACpB,gBAAIyB,gBAA0B,GAAG,IAAjC;;AACA,gBAAI,KAAKvB,iBAAL,CAAuBwB,GAAvB,CAA2BJ,MAA3B,CAAJ,EAAwC;AACpCG,cAAAA,gBAAgB,GAAG,KAAKvB,iBAAL,CAAuByB,GAAvB,CAA2BL,MAA3B,CAAnB;AACH;;AACD,gBAAIM,YAAsB,GAAG,EAA7B;AACA,gBAAIC,eAAyB,GAAG,EAAhC;AACA,gBAAIC,kBAA4B,GAAG,EAAnC;AACA,gBAAIC,kBAA4B,GAAG,EAAnC;;AACA,iBAAK,IAAItB,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGc,OAAO,CAACvB,MAApC,EAA4CS,CAAC,EAA7C,EAAiD;AAC7C,kBAAIuB,SAAiB,GAAG;AAAA;AAAA,sCAAQC,oBAAR,CAA6BV,OAAO,CAACd,CAAD,CAAP,CAAWlC,CAAxC,EAA2CgD,OAAO,CAACd,CAAD,CAAP,CAAWjC,CAAtD,CAAxB;;AACA,kBAAIwD,SAAS,IAAIV,MAAjB,EAAyB;AACrB;AACAY,gBAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACA;AACH;;AACD,kBAAIC,MAAc,GAAG;AAAA;AAAA,sCAAQ1B,gBAAR,CAAyBa,OAAO,CAACd,CAAD,CAAP,CAAWlC,CAApC,EAAuCgD,OAAO,CAACd,CAAD,CAAP,CAAWjC,CAAlD,CAArB;AACAoD,cAAAA,YAAY,CAACjB,IAAb,CAAkByB,MAAlB;;AACA,kBAAIX,gBAAJ,EAAsB;AAClB,oBAAIP,KAAa,GAAGO,gBAAgB,CAACY,OAAjB,CAAyBD,MAAzB,CAApB;;AACA,oBAAIlB,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACA,sBAAI,KAAKnB,UAAL,CAAgBqC,MAAhB,EAAwB3F,gBAAxB,CAAyC8E,OAAO,CAACd,CAAD,CAAhD,KAAwD,KAA5D,EAAmE;AAC/D;AACA,yBAAKV,UAAL,CAAgBqC,MAAhB,IAA0BnG,YAAY,CAACkC,eAAb,CAA6BoD,OAAO,CAACd,CAAD,CAApC,EAAyC2B,MAAzC,EAAiD,KAAKrC,UAAL,CAAgBqC,MAAhB,CAAjD,CAA1B;AACAN,oBAAAA,kBAAkB,CAACnB,IAAnB,CAAwByB,MAAxB;AACH,mBAJD,MAIK;AACDF,oBAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ;AACH;;AACDV,kBAAAA,gBAAgB,CAACN,MAAjB,CAAwBD,KAAxB,EAA+B,CAA/B,EATa,CASqB;;AAClC;AACH;AAEJ,eAxB4C,CAyB7C;;;AACA,mBAAKnB,UAAL,CAAgBqC,MAAhB,IAA0BnG,YAAY,CAACkC,eAAb,CAA6BoD,OAAO,CAACd,CAAD,CAApC,EAAyC2B,MAAzC,CAA1B;AACAP,cAAAA,eAAe,CAAClB,IAAhB,CAAqByB,MAArB;AACH;;AACD,gBAAIX,gBAAgB,IAAIA,gBAAgB,CAACzB,MAAjB,GAA0B,CAAlD,EAAqD;AACjD;AACA+B,cAAAA,kBAAkB,GAAGN,gBAArB;;AACA,mBAAK,IAAIhB,EAAS,GAAG,CAArB,EAAwBA,EAAC,GAAGsB,kBAAkB,CAAC/B,MAA/C,EAAuDS,EAAC,EAAxD,EAA4D;AACxD,qBAAKV,UAAL,CAAgBgC,kBAAkB,CAACtB,EAAD,CAAlC,IAAyC,IAAzC;AACH;AACJ;;AACD,iBAAKP,iBAAL,CAAuBoC,GAAvB,CAA2BhB,MAA3B,EAAmCM,YAAnC;;AACA,gBAAIC,eAAe,CAAC7B,MAAhB,GAAyB,CAAzB,IAA8B+B,kBAAkB,CAAC/B,MAAnB,GAA4B,CAA1D,IAA+D8B,kBAAkB,CAAC9B,MAAnB,GAA4B,CAA/F,EAAkG;AAC9FkC,cAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6Bb,MAA7B,EAAqCO,eAArC,EAAsDE,kBAAtD,EAA0ED,kBAA1E;AACA;AAAA;AAAA,wCAAShB,IAAT,CAAc;AAAA;AAAA,4CAAWyB,YAAzB,EAAuCjB,MAAvC,EAA+CO,eAA/C,EAAgEE,kBAAhE,EAAoFD,kBAApF;AACH;AACJ;AACJ;;AAEMU,QAAAA,QAAQ,CAACpE,EAAD,EAA2B;AACtC,iBAAO,KAAK2B,UAAL,CAAgB3B,EAAhB,CAAP;AACH;;AAEMqE,QAAAA,cAAc,GAAkB;AACnC,iBAAO,KAAKjC,SAAZ;AACH;;AAEMkC,QAAAA,SAAS,CAAC/F,GAAD,EAAcY,OAAd,EAA+BE,QAA/B,EAAuD;AACnE,cAAId,GAAG,IAAI,KAAKoE,IAAhB,EAAqB;AACjB,iBAAK4B,SAAL,GAAiBpF,OAAjB;AACA,iBAAKqF,UAAL,GAAkBnF,QAAlB;AACH;AACJ;;AAzJ8B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport DateUtil from \"../utils/DateUtil\";\nimport MapCommand from \"./MapCommand\";\nimport {MapResType } from \"./MapProxy\";\nimport MapUtil from \"./MapUtil\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\n/**地图建筑和占领数据*/\nexport class MapBuildData {\n    id: number = 0;\n    rid: number = 0;\n    nickName: string = \"\";\n    name: string = \"\";\n    x: number = 0;\n    y: number = 0;\n    type: number = 0;\n    level: number = 0;\n    opLevel: number = 0;\n    curDurable: number = 0;\n    maxDurable: number = 0;\n    defender: number = 0;\n    unionId: number = 0;\n    parentId: number = 0;\n    unionName: string;\n    occupyTime: number;\n    giveUpTime: number;\n    endTime: number;\n\n    public equalsServerData(data: any) {\n        if (this.rid == data.rid\n            && this.name == data.name\n            && this.nickName == data.RNick\n            && this.type == data.type\n            && this.level == data.level\n            && this.opLevel == data.op_level\n            && this.curDurable == data.cur_durable\n            && this.maxDurable == data.maxDurable\n            && this.defender == data.defender\n            && this.unionId == data.union_id\n            && this.parentId == data.parent_id\n            && this.unionName == data.union_name\n            && this.occupyTime == data.occupy_time\n            && this.giveUpTime == data.giveUp_time\n            && this.endTime == data.end_time) {\n            return true;\n        }\n        return false;\n    }\n\n    public static createBuildData(data: any, id: number = 0, buildData: MapBuildData = null): MapBuildData {\n        let build: MapBuildData = buildData;\n        if (buildData == null) {\n            build = new MapBuildData();\n        }\n        build.id = id;\n        build.rid = data.rid;\n        build.nickName = data.RNick;\n        build.name = data.name;\n        build.x = data.x;\n        build.y = data.y;\n        build.type = data.type;\n        build.level = data.level;\n        build.opLevel = data.op_level;\n        build.curDurable = data.cur_durable;\n        build.maxDurable = data.max_durable;\n        build.defender = data.defender;\n        build.unionId = data.union_id;\n        build.parentId = data.parent_id;\n        build.unionName = data.union_name;\n        build.occupyTime = data.occupy_time;\n        build.giveUpTime = data.giveUp_time;\n        build.endTime = data.end_time;\n        return build;\n    }\n\n    public getCellRadius() :number {\n        if (this.isSysCity()) {\n            if (this.level >= 8){\n                return 3\n            }else if (this.level >= 5){\n                return 2\n            }else {\n                return 1\n            }\n        }else {\n            return 0\n        }\n    }\n\n    public isSysCity():boolean{\n        return this.type == MapResType.SYS_CITY;\n    }\n\n    public isSysFortress():boolean{\n        return this.type == MapResType.SYS_FORTRESS;\n    }\n\n    public isWarFree(): boolean {\n        var diff = DateUtil.getServerTime() - this.occupyTime;\n        if(diff < MapCommand.getInstance().proxy.getWarFree()){\n            return true;\n        }\n        return false\n    }\n\n    public isResBuild(): boolean{\n        return this.type>=MapResType.WOOD && this.type < MapResType.FORTRESS\n    }\n\n    public isInGiveUp(): boolean {\n        var diff = DateUtil.leftTime(this.giveUpTime);\n        return diff > 0\n    }\n\n    //正在建设中\n    public isBuilding(): boolean {\n        var diff = DateUtil.leftTime(this.endTime);\n        return diff > 0 && this.level == 0\n    }\n\n    //正在升级中\n    public isUping(): boolean {\n        var diff = DateUtil.leftTime(this.endTime);\n        return diff > 0 && this.level > 0 && this.opLevel > 0\n    }\n\n    //正在拆除中\n    public isDestroying(): boolean {\n        var diff = DateUtil.leftTime(this.endTime);\n        return diff > 0 && this.opLevel == 0\n    }\n}\n\nexport default class MapBuildProxy {\n    protected _mapBuilds: MapBuildData[] = [];\n    protected _myBuilds: MapBuildData[] = [];\n    protected _lastBuildCellIds: Map<number, number[]> = new Map<number, number[]>();\n    public myId: number = 0;\n    public myUnionId: number = 0;\n    public myParentId: number = 0;\n    // 初始化数据\n    public initData(): void {\n        this._mapBuilds.length = MapUtil.mapCellCount;\n        this._lastBuildCellIds.clear();\n        this.updateMyBuildIds();//建筑信息比加载更前 所以id需要根据加载的地图做更新\n    }\n\n    public clearData(): void {\n        this._mapBuilds.length = 0;\n        this._lastBuildCellIds.clear();\n    }\n\n    /**我的建筑信息*/\n    public initMyBuilds(builds: any[]): void {\n        this._myBuilds.length = 0;\n        for (let i: number = 0; i < builds.length; i++) {\n            let id: number = MapUtil.getIdByCellPoint(builds[i].x, builds[i].y);\n            let build: MapBuildData = MapBuildData.createBuildData(builds[i], id);\n            this._myBuilds.push(build);\n        }\n    }\n\n    /**更新建筑id*/\n    public updateMyBuildIds(): void {\n        for (let i: number = 0; i < this._myBuilds.length; i++) {\n            let id: number = MapUtil.getIdByCellPoint(this._myBuilds[i].x, this._myBuilds[i].y);\n            this._myBuilds[i].id = id;\n            this._mapBuilds[id] = this._myBuilds[i];\n        }\n    }\n\n    /**更新建筑*/\n    public updateBuild(build: any): void {\n        if (build.rid == 0) {\n            //代表是放弃领地\n            if(build.type > MapResType.SYS_CITY){\n                this.removeBuild(build.x, build.y);\n                return;\n            }\n            \n        }\n        let id: number = MapUtil.getIdByCellPoint(build.x, build.y);\n        let buildData: MapBuildData = null;\n        if (this._mapBuilds[id] == null) {\n            //代表是新增\n            buildData = MapBuildData.createBuildData(build, id);\n            this._myBuilds.push(build);\n            this._mapBuilds[id] = buildData;\n        } else {\n            buildData = MapBuildData.createBuildData(build, id, this._mapBuilds[id]);\n        }\n        EventMgr.emit(LogicEvent.updateBuild, buildData);\n        if (buildData.rid == this.myId) {\n            //代表是自己的领地\n        }\n    }\n\n    public removeBuild(x: number, y: number): void {\n        let id: number = MapUtil.getIdByCellPoint(x, y);\n        this._mapBuilds[id] = null;\n        EventMgr.emit(LogicEvent.deleteBuild, id, x, y);\n        this.removeMyBuild(x, y);\n    }\n\n    public removeMyBuild(x: number, y:number):void {\n        let index: number = -1;\n        for (let i: number = 0; i < this._myBuilds.length; i++) {\n            if (this._myBuilds[i].x == x \n                && this._myBuilds[i].y == y) {\n                    index = i;\n                    break;\n            }\n        }\n        if (index != -1) {\n            this._myBuilds.splice(index, 1);\n        }\n    }\n\n    public setMapScanBlock(scanDatas: any, areaId: number = 0): void {\n        let rBuilds: any[] = scanDatas.mr_builds;\n        if (rBuilds.length > 0) {\n            let lastBuildCellIds: number[] = null;\n            if (this._lastBuildCellIds.has(areaId)) {\n                lastBuildCellIds = this._lastBuildCellIds.get(areaId);\n            }\n            let buildCellIds: number[] = [];\n            let addBuildCellIds: number[] = [];\n            let updateBuildCellIds: number[] = [];\n            let removeBuildCellIds: number[] = [];\n            for (let i: number = 0; i < rBuilds.length; i++) {\n                let areaIndex: number = MapUtil.getAreaIdByCellPoint(rBuilds[i].x, rBuilds[i].y);\n                if (areaIndex != areaId) {\n                    //代表服务端给过来的数据不在当前区域\n                    console.log(\"代表服务端给过来的数据不在当前区域\");\n                    continue;\n                }\n                let cellId: number = MapUtil.getIdByCellPoint(rBuilds[i].x, rBuilds[i].y);\n                buildCellIds.push(cellId);\n                if (lastBuildCellIds) {\n                    let index: number = lastBuildCellIds.indexOf(cellId);\n                    if (index != -1) {\n                        //存在就列表中 就代表是已存在的数据\n                        if (this._mapBuilds[cellId].equalsServerData(rBuilds[i]) == false) {\n                            //代表数据不一样需要刷新\n                            this._mapBuilds[cellId] = MapBuildData.createBuildData(rBuilds[i], cellId, this._mapBuilds[cellId]);\n                            updateBuildCellIds.push(cellId);\n                        }else{\n                            console.log(\"equalsServerData true\");\n                        }\n                        lastBuildCellIds.splice(index, 1);//移除重复数据\n                        continue;\n                    }\n\n                }\n                //其他情况就是新数据了\n                this._mapBuilds[cellId] = MapBuildData.createBuildData(rBuilds[i], cellId);\n                addBuildCellIds.push(cellId);\n            }\n            if (lastBuildCellIds && lastBuildCellIds.length > 0) {\n                //代表有需要删除的数据\n                removeBuildCellIds = lastBuildCellIds;\n                for (let i: number = 0; i < removeBuildCellIds.length; i++) {\n                    this._mapBuilds[removeBuildCellIds[i]] = null;\n                }\n            }\n            this._lastBuildCellIds.set(areaId, buildCellIds);\n            if (addBuildCellIds.length > 0 || removeBuildCellIds.length > 0 || updateBuildCellIds.length > 0) {\n                console.log(\"update_builds\", areaId, addBuildCellIds, removeBuildCellIds, updateBuildCellIds);\n                EventMgr.emit(LogicEvent.updateBuilds, areaId, addBuildCellIds, removeBuildCellIds, updateBuildCellIds);\n            }\n        }\n    }\n\n    public getBuild(id: number): MapBuildData {\n        return this._mapBuilds[id];\n    }\n\n    public getMyBuildList():MapBuildData[] {\n        return this._myBuilds;\n    }\n\n    public updateSub(rid: number, unionId: number, parentId: number): void {\n        if (rid == this.myId){\n            this.myUnionId = unionId;\n            this.myParentId = parentId;\n        }\n    }\n}\n"]}