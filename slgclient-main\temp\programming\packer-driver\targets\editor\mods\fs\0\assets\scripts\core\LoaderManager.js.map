{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts"], "names": ["LoadData", "LoadCompleteData", "LoaderManager", "<PERSON><PERSON>", "resources", "EventMgr", "CoreEvent", "LoadDataType", "constructor", "path", "loadType", "FILE", "fileType", "getInstance", "_instance", "destory", "onDestory", "_loadDataList", "length", "loadNext", "_curIndex", "onComplete", "data", "DIR", "loadDir", "finish", "total", "onProgress", "error", "assets", "_completePaths", "push", "_completeAssets", "load", "asset", "percent", "subPercent", "totalPercent", "Number", "toFixed", "emit", "loadProgress", "_target", "_progressCallback", "call", "loadComplete", "_completeCallback", "clearData", "_isLoading", "startLoad", "target", "startLoadList", "dataList"], "mappings": ";;;qEASaA,Q,EAYAC,gB,EAKQC,a;;;;;;;;;;;;;;;;;;;;;;;;AA1BAC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;;AACnBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;iBAEGC,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;SAAAA,Y,4BAAAA,Y;;0BAKCP,Q,GAAN,MAAMA,QAAN,CAAe;AAKlBQ,QAAAA,WAAW,CAACC,IAAY,GAAG,EAAhB,EAAoBC,QAAsB,GAAGH,YAAY,CAACI,IAA1D,EAAgEC,QAAsB,GAAGT,KAAzF,EAAgG;AAAA,wCAJ5F,EAI4F;;AAAA,4CAHlFI,YAAY,CAACI,IAGqE;;AAAA,4CAFlFR,KAEkF;;AACvG,eAAKM,IAAL,GAAYA,IAAZ;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACA,eAAKE,QAAL,GAAgBA,QAAhB;AACH;;AATiB,O;;kCAYTX,gB,GAAN,MAAMA,gBAAN,CAAuB;AAAA;AAAA,wCACX,EADW;;AAAA;AAAA;;AAAA,O;;yBAKTC,a,GAAN,MAAMA,aAAN,CAAoB;AAC/B;AAEyB,eAAXW,WAAW,GAAkB;AACvC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIZ,aAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKY,SAAZ;AACH;;AAEoB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAWDN,QAAAA,WAAW,GAAG;AAAA,8CATkB,KASlB;;AAAA,6CARgB,CAAC,CAQjB;;AAAA,iDAPwB,EAOxB;;AAAA,kDANuB,EAMvB;;AAAA,mDALqB,EAKrB;;AAAA,qDAJ0B,IAI1B;;AAAA,qDAH0B,IAG1B;;AAAA,2CAFW,IAEX;AAEb;;AAEMQ,QAAAA,SAAS,GAAS;AACrB,eAAKC,aAAL,CAAmBC,MAAnB,GAA4B,CAA5B;AACH;;AAESC,QAAAA,QAAQ,GAAS;AACvB,cAAI,KAAKC,SAAL,IAAkB,KAAKH,aAAL,CAAmBC,MAAzC,EAAiD;AAC7C,iBAAKG,UAAL;AACA;AACH;;AACD,cAAIC,IAAc,GAAG,KAAKL,aAAL,CAAmB,KAAKG,SAAxB,CAArB;;AACA,cAAIE,IAAI,CAACZ,QAAL,IAAiBH,YAAY,CAACgB,GAAlC,EAAuC;AACnC;AACAnB,YAAAA,SAAS,CAACoB,OAAV,CAAkBF,IAAI,CAACb,IAAvB,EAA6Ba,IAAI,CAACV,QAAlC,EACI,CAACa,MAAD,EAAiBC,KAAjB,KAAmC;AAC/B,mBAAKC,UAAL,CAAgBF,MAAhB,EAAwBC,KAAxB;AACH,aAHL,EAII,CAACE,KAAD,EAAeC,MAAf,KAAiC;AAC7B,kBAAID,KAAK,IAAI,IAAb,EAAmB;AACf,qBAAKE,cAAL,CAAoBC,IAApB,CAAyBT,IAAI,CAACb,IAA9B;;AACA,qBAAKuB,eAAL,CAAqBD,IAArB,CAA0BF,MAA1B;;AACA,qBAAKT,SAAL;AACA,qBAAKD,QAAL;AACH,eALD,MAKO;AACH,qBAAKE,UAAL,CAAgBO,KAAhB;AACH;AACJ,aAbL;AAcH,WAhBD,MAgBO;AACH;AACAxB,YAAAA,SAAS,CAAC6B,IAAV,CAAeX,IAAI,CAACb,IAApB,EAA0Ba,IAAI,CAACV,QAA/B,EACI,CAACa,MAAD,EAAiBC,KAAjB,KAAmC;AAC/B,mBAAKC,UAAL,CAAgBF,MAAhB,EAAwBC,KAAxB;AACH,aAHL,EAII,CAACE,KAAD,EAAeM,KAAf,KAA8B;AAC1B,kBAAIN,KAAK,IAAI,IAAb,EAAmB;AACf,qBAAKE,cAAL,CAAoBC,IAApB,CAAyBT,IAAI,CAACb,IAA9B;;AACA,qBAAKuB,eAAL,CAAqBD,IAArB,CAA0BG,KAA1B;;AACA,qBAAKd,SAAL;AACA,qBAAKD,QAAL;AACH,eALD,MAKO;AACH,qBAAKE,UAAL,CAAgBO,KAAhB;AACH;AACJ,aAbL;AAcH;AACJ;;AAESD,QAAAA,UAAU,CAACF,MAAD,EAAiBC,KAAjB,EAAsC;AAEtD,cAAIS,OAAe,GAAG,IAAI,KAAKlB,aAAL,CAAmBC,MAA7C;AACA,cAAIkB,UAAiB,GAAIX,MAAM,GAAGC,KAAV,GAAmBS,OAA3C;AACA,cAAIE,YAAmB,GAAGC,MAAM,CAAC,CAACF,UAAU,GAAGD,OAAO,GAAG,KAAKf,SAA7B,EAAwCmB,OAAxC,CAAgD,CAAhD,CAAD,CAAhC;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,sCAAUC,YAAxB,EAAsCJ,YAAtC;;AAEA,cAAI,KAAKK,OAAL,IAAgB,KAAKC,iBAAzB,EAA4C;AACxC,iBAAKA,iBAAL,CAAuBC,IAAvB,CAA4B,KAAKF,OAAjC,EAA0CL,YAA1C;AACH;AAEJ;;AAEShB,QAAAA,UAAU,CAACO,KAAY,GAAG,IAAhB,EAA4B;AAE5C;AAAA;AAAA,oCAASY,IAAT,CAAc;AAAA;AAAA,sCAAUK,YAAxB;;AACA,cAAI,KAAKH,OAAL,IAAgB,KAAKI,iBAAzB,EAA4C;AACxC,iBAAKA,iBAAL,CAAuBF,IAAvB,CAA4B,KAAKF,OAAjC,EAA0Cd,KAA1C,EAAiD,KAAKE,cAAtD,EAAsE,KAAKE,eAA3E;AACH;;AACD,eAAKe,SAAL;AACH;;AAESA,QAAAA,SAAS,GAAS;AACxB,eAAKC,UAAL,GAAkB,KAAlB;AACA,eAAK/B,aAAL,CAAmBC,MAAnB,GAA4B,CAA5B;AACA,eAAKyB,iBAAL,GAAyB,IAAzB;AACA,eAAKG,iBAAL,GAAyB,IAAzB;AACA,eAAKJ,OAAL,GAAe,IAAf;AACA,eAAKV,eAAL,CAAqBd,MAArB,GAA8B,CAA9B;AACA,eAAKY,cAAL,CAAoBZ,MAApB,GAA6B,CAA7B;AACH;;AAEM+B,QAAAA,SAAS,CAAC3B,IAAD,EAAiBmB,YAAjB,EAA0DI,YAA1D,EAA6HK,MAAW,GAAG,IAA3I,EAAuJ;AACnK,eAAKC,aAAL,CAAmB,CAAC7B,IAAD,CAAnB,EAA2BmB,YAA3B,EAAyCI,YAAzC;AACH;;AAEMM,QAAAA,aAAa,CAACC,QAAD,EAAuBX,YAAvB,EAAgEI,YAAhE,EAAmIK,MAAW,GAAG,IAAjJ,EAA6J;AAC7K,cAAI,KAAKF,UAAT,EAAqB;AACjB;AACH;;AACD,eAAKD,SAAL;AACA,eAAKC,UAAL,GAAkB,IAAlB;AACA,eAAK/B,aAAL,GAAqBmC,QAArB;AACA,eAAKT,iBAAL,GAAyBF,YAAzB;AACA,eAAKK,iBAAL,GAAyBD,YAAzB;AACA,eAAKH,OAAL,GAAeQ,MAAf;AACA,eAAK9B,SAAL,GAAiB,CAAjB;AACA,eAAKD,QAAL;AACH;;AA7H8B,O;;sBAAdjB,a", "sourcesContent": ["import { _decorator, Asset, resources } from 'cc';\nimport { EventMgr } from '../utils/EventMgr';\nimport { CoreEvent } from './coreEvent';\n\nexport enum LoadDataType {\n    DIR,\n    FILE\n}\n\nexport class LoadData {\n    path: string = \"\";\n    loadType: LoadDataType = LoadDataType.FILE;\n    fileType: typeof Asset = Asset; \n\n    constructor(path: string = \"\", loadType: LoadDataType = LoadDataType.FILE, fileType: typeof Asset = Asset) {\n        this.path = path;\n        this.loadType = loadType;\n        this.fileType = fileType;\n    }\n}\n\nexport class LoadCompleteData {\n    path: string = \"\";\n    data: any;\n}\n\nexport default class LoaderManager {\n    //单例\n    protected static _instance: LoaderManager;\n    public static getInstance(): LoaderManager {\n        if (this._instance == null) {\n            this._instance = new LoaderManager();\n        }\n        return this._instance;\n    }\n\n    public static destory(): boolean {\n        if (this._instance) {\n            this._instance.onDestory();\n            this._instance = null;\n            return true;\n        }\n        return false;\n    }\n\n    protected _isLoading: boolean = false;\n    protected _curIndex: number = -1;\n    protected _loadDataList: LoadData[] = [];\n    protected _completePaths: string[] = [];\n    protected _completeAssets: any[] = [];\n    protected _progressCallback: Function = null;\n    protected _completeCallback: Function = null;\n    protected _target: any = null;\n\n    constructor() {\n\n    }\n\n    public onDestory(): void {\n        this._loadDataList.length = 0;\n    }\n\n    protected loadNext(): void {\n        if (this._curIndex >= this._loadDataList.length) {\n            this.onComplete();\n            return;\n        }\n        let data: LoadData = this._loadDataList[this._curIndex];\n        if (data.loadType == LoadDataType.DIR) {\n            //加载目录\n            resources.loadDir(data.path, data.fileType, \n                (finish: number, total: number) => {\n                    this.onProgress(finish, total);\n                },\n                (error: Error, assets: any[]) => {\n                    if (error == null) {\n                        this._completePaths.push(data.path);\n                        this._completeAssets.push(assets);\n                        this._curIndex++;\n                        this.loadNext();\n                    } else {\n                        this.onComplete(error);\n                    }\n                });\n        } else {\n            //加载文件\n            resources.load(data.path, data.fileType, \n                (finish: number, total: number) => {\n                    this.onProgress(finish, total);\n                },\n                (error: Error, asset: any) => {\n                    if (error == null) {\n                        this._completePaths.push(data.path);\n                        this._completeAssets.push(asset);\n                        this._curIndex++;\n                        this.loadNext();\n                    } else {\n                        this.onComplete(error);\n                    }\n                });\n        }\n    }\n\n    protected onProgress(finish: number, total: number): void {\n        \n        let percent: number = 1 / this._loadDataList.length;\n        let subPercent:number = (finish / total) * percent;\n        let totalPercent:number = Number((subPercent + percent * this._curIndex).toFixed(2));\n        EventMgr.emit(CoreEvent.loadProgress, totalPercent);\n        \n        if (this._target && this._progressCallback) {\n            this._progressCallback.call(this._target, totalPercent);\n        }\n        \n    }\n\n    protected onComplete(error: Error = null): void {\n        \n        EventMgr.emit(CoreEvent.loadComplete);\n        if (this._target && this._completeCallback) {\n            this._completeCallback.call(this._target, error, this._completePaths, this._completeAssets);\n        }\n        this.clearData();\n    }\n\n    protected clearData(): void {\n        this._isLoading = false;\n        this._loadDataList.length = 0;\n        this._progressCallback = null;\n        this._completeCallback = null;\n        this._target = null;\n        this._completeAssets.length = 0;\n        this._completePaths.length = 0;\n    }\n\n    public startLoad(data: LoadData, loadProgress: (percent: number) => void, loadComplete: (error:Error, paths:string[], datas: any[]) => void, target: any = null): void {\n        this.startLoadList([data], loadProgress, loadComplete);\n    }\n\n    public startLoadList(dataList: LoadData[], loadProgress: (percent: number) => void, loadComplete: (error:Error, paths:string[], datas: any[]) => void, target: any = null): void {\n        if (this._isLoading) {\n            return;\n        }\n        this.clearData();\n        this._isLoading = true;\n        this._loadDataList = dataList;\n        this._progressCallback = loadProgress;\n        this._completeCallback = loadComplete;\n        this._target = target;\n        this._curIndex = 0;\n        this.loadNext();\n    }\n}\n"]}