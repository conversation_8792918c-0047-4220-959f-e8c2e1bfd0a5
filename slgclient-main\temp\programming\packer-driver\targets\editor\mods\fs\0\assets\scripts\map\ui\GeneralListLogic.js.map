{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "Label", "GeneralCommand", "MapUICommand", "EventMgr", "ListLogic", "AudioManager", "LogicEvent", "ccclass", "property", "GeneralList<PERSON><PERSON><PERSON>", "onEnable", "on", "updateMyGenerals", "initGeneralCfg", "general<PERSON><PERSON><PERSON>", "chosedGeneral", "onClickClose", "onDisable", "targetOff", "instance", "playClick", "node", "active", "onClickConvert", "emit", "openGeneralConvert", "onTuJianConvert", "openGeneralRoster", "basic", "getInstance", "proxy", "getBasicGeneral", "cnt", "getMyActiveGeneralCnt", "cntLab", "string", "limit", "list", "getUseGenerals", "listTemp", "concat", "for<PERSON>ach", "item", "type", "_type", "position", "_position", "i", "length", "_cunGeneral", "indexOf", "id", "splice", "comp", "scrollView", "getComponent", "setData", "data", "qryMyGenerals"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AAGrCC,MAAAA,c;;AACAC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;AACEC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAPH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAUTY,gB,WADpBF,OAAO,CAAC,kBAAD,C,UAGHC,QAAQ,CAACT,UAAD,C,UAGRS,QAAQ,CAACR,KAAD,C,oCANb,MACqBS,gBADrB,SAC8CX,SAD9C,CACwD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,+CAQrB,EARqB;;AAAA,yCAS7B,CAT6B;;AAAA,6CAUzB,CAVyB;AAAA;;AAY1CY,QAAAA,QAAQ,GAAO;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,gBAAvB,EAAyC,KAAKC,cAA9C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,cAAvB,EAAuC,KAAKD,cAA5C,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWI,aAAvB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACH;;AAGSC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESF,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaG,QAAb,CAAsBC,SAAtB;AACA,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAESC,QAAAA,cAAc,GAAS;AAC7B;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASI,IAAT,CAAc;AAAA;AAAA,wCAAWC,kBAAzB;AACA,eAAKJ,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAESI,QAAAA,eAAe,GAAS;AAC9B;AAAA;AAAA,4CAAaP,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASI,IAAT,CAAc;AAAA;AAAA,wCAAWG,iBAAzB;AACA,eAAKN,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAEST,QAAAA,cAAc,GAAO;AAE3B,cAAIe,KAAK,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,eAAjC,EAAZ;AACA,cAAIC,GAAG,GAAG;AAAA;AAAA,gDAAeH,WAAf,GAA6BC,KAA7B,CAAmCG,qBAAnC,EAAV;AACA,eAAKC,MAAL,CAAYC,MAAZ,GAAqB,MAAMH,GAAN,GAAY,GAAZ,GAAkBJ,KAAK,CAACQ,KAAxB,GAAgC,GAArD;AAEA,cAAIC,IAAU,GAAG;AAAA;AAAA,gDAAeR,WAAf,GAA6BC,KAA7B,CAAmCQ,cAAnC,EAAjB;AACA,cAAIC,QAAQ,GAAGF,IAAI,CAACG,MAAL,EAAf;AAGAD,UAAAA,QAAQ,CAACE,OAAT,CAAiBC,IAAI,IAAI;AACrBA,YAAAA,IAAI,CAACC,IAAL,GAAY,KAAKC,KAAjB;AACAF,YAAAA,IAAI,CAACG,QAAL,GAAgB,KAAKC,SAArB;AACH,WAHD;;AAMA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGR,QAAQ,CAACS,MAA5B,EAAoCD,CAAC,EAArC,EAAwC;AACpC,gBAAG,KAAKE,WAAL,CAAiBC,OAAjB,CAAyBX,QAAQ,CAACQ,CAAD,CAAR,CAAYI,EAArC,KAA4C,CAA/C,EAAkD;AAC9CZ,cAAAA,QAAQ,CAACa,MAAT,CAAgBL,CAAhB,EAAkB,CAAlB;AACAA,cAAAA,CAAC;AACJ;AACJ;;AAED,cAAIM,IAAI,GAAG,KAAKC,UAAL,CAAgBjC,IAAhB,CAAqBkC,YAArB;AAAA;AAAA,qCAAX;AACAF,UAAAA,IAAI,CAACG,OAAL,CAAajB,QAAb;AACH;;AAIMiB,QAAAA,OAAO,CAACC,IAAD,EAAed,IAAW,GAAG,CAA7B,EAA+BE,QAAe,GAAG,CAAjD,EAAwD;AAClE,eAAKI,WAAL,GAAmB,EAAnB;;AACA,cAAGQ,IAAI,IAAIA,IAAI,CAACT,MAAL,GAAc,CAAzB,EAA2B;AACvB,iBAAKC,WAAL,GAAmBQ,IAAnB;AACH;;AAED,eAAKb,KAAL,GAAaD,IAAb;AACA,eAAKG,SAAL,GAAiBD,QAAjB;AAEA,eAAKhC,cAAL;AACA;AAAA;AAAA,gDAAegB,WAAf,GAA6B6B,aAA7B;AACH;;AAhFmD,O;;;;;iBAG5B,I;;;;;;;iBAGT,I", "sourcesContent": ["\nimport { _decorator, Component, ScrollView, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport MapUICommand from \"./MapUICommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport ListLogic from '../../utils/ListLogic';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('GeneralListLogic')\nexport default class GeneralListLogic extends Component {\n\n    @property(ScrollView)\n    scrollView:ScrollView = null;\n\n    @property(Label)\n    cntLab:Label = null;\n\n    private _cunGeneral:number[] = [];\n    private _type:number = 0;\n    private _position:number = 0;\n\n    protected onEnable():void{\n        EventMgr.on(LogicEvent.updateMyGenerals, this.initGeneralCfg, this);\n        EventMgr.on(LogicEvent.generalConvert, this.initGeneralCfg, this);\n        EventMgr.on(LogicEvent.chosedGeneral, this.onClickClose, this);\n    }\n\n\n    protected onDisable():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n    }\n\n    protected onClickConvert(): void {\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.openGeneralConvert);\n        this.node.active = false;\n    }\n\n    protected onTuJianConvert(): void {\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.openGeneralRoster);\n        this.node.active = false;\n    }\n\n    protected initGeneralCfg():void{\n\n        var basic = MapUICommand.getInstance().proxy.getBasicGeneral();\n        var cnt = GeneralCommand.getInstance().proxy.getMyActiveGeneralCnt();\n        this.cntLab.string = \"(\" + cnt + \"/\" + basic.limit + \")\";\n\n        let list:any[] = GeneralCommand.getInstance().proxy.getUseGenerals();\n        let listTemp = list.concat();\n\n\n        listTemp.forEach(item => {\n            item.type = this._type;\n            item.position = this._position;\n        })\n\n\n        for(var i = 0; i < listTemp.length ;i++){\n            if(this._cunGeneral.indexOf(listTemp[i].id) >= 0 ){\n                listTemp.splice(i,1);\n                i--;\n            }\n        }\n\n        var comp = this.scrollView.node.getComponent(ListLogic);\n        comp.setData(listTemp);\n    }\n\n\n\n    public setData(data:number[],type:number = 0,position:number = 0):void{\n        this._cunGeneral = [];\n        if(data && data.length > 0){\n            this._cunGeneral = data;\n        }\n        \n        this._type = type;\n        this._position = position;\n\n        this.initGeneralCfg();\n        GeneralCommand.getInstance().qryMyGenerals();\n    }\n\n}\n"]}