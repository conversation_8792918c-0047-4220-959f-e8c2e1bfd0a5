{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts"], "names": ["_decorator", "Vec2", "Vec3", "ResBuildLogic", "MapBaseLayerLogic", "MapUtil", "EventMgr", "LogicEvent", "ccclass", "property", "MapResBuildLogic", "onLoad", "on", "updateBuilds", "onUpdateBuilds", "updateBuild", "onUpdateBuild", "deleteBuild", "onDeleteBuild", "onDestroy", "targetOff", "areaIndex", "addIds", "removeIds", "updateIds", "_itemMap", "has", "i", "length", "addItem", "_cmd", "buildProxy", "getBuild", "removeItem", "updateItem", "data", "getAreaIdByCellPoint", "x", "y", "id", "setItemData", "item", "buildData", "position", "mapCellToPixelPoint", "setPosition", "getComponent", "setBuildData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG1BC,MAAAA,a;;AACAC,MAAAA,iB;;AAEAC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAPH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAUTU,gB,WADpBF,OAAO,CAAC,kBAAD,C,gBAAR,MACqBE,gBADrB;AAAA;AAAA,kDACgE;AAElDC,QAAAA,MAAM,GAAS;AACrB,gBAAMA,MAAN;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,YAAvB,EAAqC,KAAKC,cAA1C,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AAEH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,gBAAMD,SAAN;AACH;;AAESL,QAAAA,cAAc,CAACO,SAAD,EAAoBC,MAApB,EAAsCC,SAAtC,EAA2DC,SAA3D,EAAsF;AAE1G,cAAI,KAAKC,QAAL,CAAcC,GAAd,CAAkBL,SAAlB,CAAJ,EAAkC;AAC9B,iBAAK,IAAIM,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGL,MAAM,CAACM,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;AAC5C,mBAAKE,OAAL,CAAaR,SAAb,EAAwB,KAAKS,IAAL,CAAUC,UAAV,CAAqBC,QAArB,CAA8BV,MAAM,CAACK,CAAD,CAApC,CAAxB;AACH;;AACD,iBAAK,IAAIA,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGJ,SAAS,CAACK,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,mBAAKM,UAAL,CAAgBZ,SAAhB,EAA2BE,SAAS,CAACI,CAAD,CAApC;AACH;;AACD,iBAAK,IAAIA,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGH,SAAS,CAACI,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,mBAAKO,UAAL,CAAgBb,SAAhB,EAA2B,KAAKS,IAAL,CAAUC,UAAV,CAAqBC,QAArB,CAA8BR,SAAS,CAACG,CAAD,CAAvC,CAA3B;AACH;AACJ;AACJ;;AAESX,QAAAA,aAAa,CAACmB,IAAD,EAA2B;AAE9C,cAAId,SAAiB,GAAG;AAAA;AAAA,kCAAQe,oBAAR,CAA6BD,IAAI,CAACE,CAAlC,EAAqCF,IAAI,CAACG,CAA1C,CAAxB;AACA,eAAKT,OAAL,CAAaR,SAAb,EAAwBc,IAAxB;AACH;;AAESjB,QAAAA,aAAa,CAACqB,EAAD,EAAaF,CAAb,EAAwBC,CAAxB,EAAyC;AAC5D,cAAIjB,SAAiB,GAAG;AAAA;AAAA,kCAAQe,oBAAR,CAA6BC,CAA7B,EAAgCC,CAAhC,CAAxB;AACA,eAAKL,UAAL,CAAgBZ,SAAhB,EAA2BkB,EAA3B;AACH;;AAEMC,QAAAA,WAAW,CAACC,IAAD,EAAaN,IAAb,EAA8B;AAC5C,cAAIO,SAAuB,GAAGP,IAA9B;AACA,cAAIQ,QAAc,GAAG;AAAA;AAAA,kCAAQC,mBAAR,CAA4B,IAAI3C,IAAJ,CAASyC,SAAS,CAACL,CAAnB,EAAsBK,SAAS,CAACJ,CAAhC,CAA5B,CAArB;AACAG,UAAAA,IAAI,CAACI,WAAL,CAAiB,IAAI3C,IAAJ,CAASyC,QAAQ,CAACN,CAAlB,EAAqBM,QAAQ,CAACL,CAA9B,EAAiC,CAAjC,CAAjB;AACAG,UAAAA,IAAI,CAACK,YAAL;AAAA;AAAA,8CAAiCC,YAAjC,CAA8CL,SAA9C;AACH;;AA9C2D,O", "sourcesContent": ["import { _decorator, Node, Vec2, Vec3 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport ResBuildLogic from \"./entries/ResBuildLogic\";\nimport MapBaseLayerLogic from \"./MapBaseLayerLogic\";\nimport { MapBuildData } from \"./MapBuildProxy\";\nimport MapUtil from \"./MapUtil\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('MapResBuildLogic')\nexport default class MapResBuildLogic extends MapBaseLayerLogic {\n\n    protected onLoad(): void {\n        super.onLoad();\n        EventMgr.on(LogicEvent.updateBuilds, this.onUpdateBuilds, this);\n        EventMgr.on(LogicEvent.updateBuild, this.onUpdateBuild, this);\n        EventMgr.on(LogicEvent.deleteBuild, this.onDeleteBuild, this);\n\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        super.onDestroy();\n    }\n\n    protected onUpdateBuilds(areaIndex: number, addIds: number[], removeIds: number[], updateIds: number[]): void {\n  \n        if (this._itemMap.has(areaIndex)) {\n            for (let i: number = 0; i < addIds.length; i++) {\n                this.addItem(areaIndex, this._cmd.buildProxy.getBuild(addIds[i]));\n            }\n            for (let i: number = 0; i < removeIds.length; i++) {\n                this.removeItem(areaIndex, removeIds[i]);\n            }\n            for (let i: number = 0; i < updateIds.length; i++) {\n                this.updateItem(areaIndex, this._cmd.buildProxy.getBuild(updateIds[i]));\n            }\n        }\n    }\n\n    protected onUpdateBuild(data: MapBuildData): void {\n\n        let areaIndex: number = MapUtil.getAreaIdByCellPoint(data.x, data.y);\n        this.addItem(areaIndex, data);\n    }\n\n    protected onDeleteBuild(id: number, x: number, y: number): void {\n        let areaIndex: number = MapUtil.getAreaIdByCellPoint(x, y);\n        this.removeItem(areaIndex, id);\n    }\n\n    public setItemData(item: Node, data: any): void {\n        let buildData: MapBuildData = data as MapBuildData;\n        let position: Vec2 = MapUtil.mapCellToPixelPoint(new Vec2(buildData.x, buildData.y));\n        item.setPosition(new Vec3(position.x, position.y, 0));\n        item.getComponent(ResBuildLogic).setBuildData(buildData);\n    }\n}\n"]}