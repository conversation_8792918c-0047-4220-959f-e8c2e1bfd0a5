{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"], "names": ["ArmyCommand", "ServerConfig", "NetManager", "ArmyProxy", "ArmyCmd", "GeneralCommand", "GeneralData", "EventMgr", "LogicEvent", "getInstance", "_instance", "destory", "onDestory", "constructor", "on", "army_myList", "onQryArmyList", "army_myOne", "onQryArmyOne", "army_dispose", "onGeneralDispose", "army_conscript", "onGeneralConscript", "army_assign", "onGeneralAssignArmy", "army_push", "onGeneralArmyStatePush", "nationMap_scanBlock", "onNationMapScanBlock", "setInterval", "emit", "getMyMainCity", "myCity", "armyList", "proxy", "getArmyList", "cityId", "i", "length", "army", "isGenConEnd", "console", "log", "qryArmyOne", "order", "targetOff", "clearData", "_proxy", "data", "otherData", "code", "armyDatas", "updateArmys", "msg", "armys", "updateArmyList", "armyData", "updateArmy", "LoginCommand", "saveEnterData", "upateMyRoleRes", "updateMyProperty", "datas", "getArmyPhysicalPower", "min<PERSON><PERSON>er", "generals", "general", "getMyGeneral", "physical_power", "getArmyGenerals", "list", "push", "getArmyPhysicalPowerByGenerals", "getArmyCurSoldierCnt", "cnt", "soldiers", "getArmyTotalSoldierCntByGenerals", "levelCfg", "getGeneralLevelCfg", "level", "getArmySpeed", "empyt", "speed", "cfg", "getGeneralCfg", "cfgId", "Math", "min", "getPrValue", "speed_grow", "speed_added", "getArmy<PERSON><PERSON>roy", "destroy", "destroy_grow", "destroy_added", "getArmyCamp", "lastCamp", "config", "camp", "getArmyStateDes", "stateStr", "state", "cmd", "Return", "Idle", "Reclaim", "Conscript", "Garrison", "qryArmyList", "sendData", "name", "send", "generalDispose", "generalId", "position", "generalConscript", "armyId", "cnts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y"], "mappings": ";;;+<PERSON><PERSON><PERSON><PERSON><PERSON>,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAbZC,MAAAA,Y,iBAAAA,Y;;AAKAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,S;AAAaC,MAAAA,O,iBAAAA,O;;AACbC,MAAAA,c;;AACqCC,MAAAA,W,iBAAAA,W;;AACnCC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;yBAGYR,W,GAAN,MAAMA,WAAN,CAAkB;AAC7B;AAEyB,eAAXS,WAAW,GAAgB;AACrC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIV,WAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKU,SAAZ;AACH;;AAEoB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SAjB4B,CAmB7B;;;AAGAG,QAAAA,WAAW,GAAG;AAAA,0CAFgB;AAAA;AAAA,uCAEhB;;AACV;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,WAAzB,EAAsC,KAAKC,aAA3C,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,UAAzB,EAAqC,KAAKC,YAA1C,EAAwD,IAAxD;AAEA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,4CAAaK,YAAzB,EAAuC,KAAKC,gBAA5C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASN,EAAT,CAAY;AAAA;AAAA,4CAAaO,cAAzB,EAAyC,KAAKC,kBAA9C,EAAkE,IAAlE;AACA;AAAA;AAAA,oCAASR,EAAT,CAAY;AAAA;AAAA,4CAAaS,WAAzB,EAAsC,KAAKC,mBAA3C,EAAgE,IAAhE;AACA;AAAA;AAAA,oCAASV,EAAT,CAAY;AAAA;AAAA,4CAAaW,SAAzB,EAAoC,KAAKC,sBAAzC,EAAiE,IAAjE;AACA;AAAA;AAAA,oCAASZ,EAAT,CAAY;AAAA;AAAA,4CAAaa,mBAAzB,EAA8C,KAAKC,oBAAnD,EAAyE,IAAzE,EARU,CAUV;;AACAC,UAAAA,WAAW,CAAC,MAAM;AACd;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,aAAzB,EAAyCC,MAAD,IAAyB;AAC7D,kBAAIA,MAAM,IAAI,IAAd,EAAmB;AACf,oBAAIC,QAAoB,GAAG,KAAKC,KAAL,CAAWC,WAAX,CAAuBH,MAAM,CAACI,MAA9B,CAA3B;;AACA,oBAAIH,QAAQ,IAAI,IAAhB,EAAqB;AACjB;AACH;;AAED,qBAAK,IAAII,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGJ,QAAQ,CAACK,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,sBAAIE,IAAI,GAAGN,QAAQ,CAACI,CAAD,CAAnB;;AACA,sBAAIE,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAACC,WAAL,EAApB,EAAuC;AACnCC,oBAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACA,yBAAKC,UAAL,CAAgBJ,IAAI,CAACH,MAArB,EAA6BG,IAAI,CAACK,KAAlC;AACH;AACJ;AACJ;AACJ,aAfD;AAgBF,WAlBS,EAkBP,IAlBO,CAAX;AAmBH;;AAEMhC,QAAAA,SAAS,GAAS;AACrB;AAAA;AAAA,oCAASiC,SAAT,CAAmB,IAAnB;AACH;;AAEMC,QAAAA,SAAS,GAAS;AACrB,eAAKC,MAAL,CAAYD,SAAZ;AACH;;AAEe,YAALZ,KAAK,GAAc;AAC1B,iBAAO,KAAKa,MAAZ;AACH;AAED;;;AACU/B,QAAAA,aAAa,CAACgC,IAAD,EAAYC,SAAZ,EAAkC;AACrDR,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BM,IAA7B;;AACA,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAIC,SAAqB,GAAG,KAAKJ,MAAL,CAAYK,WAAZ,CAAwBJ,IAAI,CAACK,GAAL,CAASjB,MAAjC,EAAyCY,IAAI,CAACK,GAAL,CAASC,KAAlD,CAA5B;;AACA;AAAA;AAAA,sCAASxB,IAAT,CAAc;AAAA;AAAA,0CAAWyB,cAAzB,EAAyCJ,SAAzC;AACH;AACJ;;AAESjC,QAAAA,YAAY,CAAC8B,IAAD,EAAYC,SAAZ,EAAkC;AACpDR,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BM,IAA5B;;AACA,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAIM,QAAQ,GAAG,KAAKT,MAAL,CAAYU,UAAZ,CAAuBT,IAAI,CAACK,GAAL,CAASd,IAAT,CAAcH,MAArC,EAA6CY,IAAI,CAACK,GAAL,CAASd,IAAtD,CAAf;;AACA,gBAAIY,SAAqB,GAAG,KAAKJ,MAAL,CAAYZ,WAAZ,CAAwBa,IAAI,CAACK,GAAL,CAASd,IAAT,CAAcH,MAAtC,CAA5B;;AACA;AAAA;AAAA,sCAASN,IAAT,CAAc;AAAA;AAAA,0CAAWyB,cAAzB,EAAyCJ,SAAzC;AACA;AAAA;AAAA,sCAASrB,IAAT,CAAc;AAAA;AAAA,0CAAW2B,UAAzB,EAAqCD,QAArC;AACH;AACJ;AAID;;;AACUpC,QAAAA,gBAAgB,CAAC4B,IAAD,EAAYC,SAAZ,EAAkC;AACxDR,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCM,IAAhC;;AACA,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAIM,QAAkB,GAAG,KAAKT,MAAL,CAAYU,UAAZ,CAAuBT,IAAI,CAACK,GAAL,CAASd,IAAT,CAAcH,MAArC,EAA6CY,IAAI,CAACK,GAAL,CAASd,IAAtD,CAAzB;;AACAE,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBc,QAAxB;AACA;AAAA;AAAA,sCAAS1B,IAAT,CAAc;AAAA;AAAA,0CAAW2B,UAAzB,EAAqCD,QAArC;AACH;AACJ;AAED;;;AACUlC,QAAAA,kBAAkB,CAAC0B,IAAD,EAAYC,SAAZ,EAAkC;AAC1DR,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCM,IAAlC;;AACA,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChBQ,YAAAA,YAAY,CAACjD,WAAb,GAA2ByB,KAA3B,CAAiCyB,aAAjC,CAA+CX,IAAI,CAACK,GAApD;AACA;AAAA;AAAA,sCAASvB,IAAT,CAAc;AAAA;AAAA,0CAAW8B,cAAzB;;AAEA,gBAAIJ,QAAkB,GAAG,KAAKT,MAAL,CAAYU,UAAZ,CAAuBT,IAAI,CAACK,GAAL,CAASd,IAAT,CAAcH,MAArC,EAA6CY,IAAI,CAACK,GAAL,CAASd,IAAtD,CAAzB;;AACA;AAAA;AAAA,sCAAST,IAAT,CAAc;AAAA;AAAA,0CAAW2B,UAAzB,EAAqCD,QAArC;AAEH;AACJ;AAED;;;AACUhC,QAAAA,mBAAmB,CAACwB,IAAD,EAAYC,SAAZ,EAAkC;AAC3DR,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCM,IAAnC;;AACA,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAIM,QAAkB,GAAG,KAAKT,MAAL,CAAYU,UAAZ,CAAuBT,IAAI,CAACK,GAAL,CAASd,IAAT,CAAcH,MAArC,EAA6CY,IAAI,CAACK,GAAL,CAASd,IAAtD,CAAzB;;AACA;AAAA;AAAA,sCAAST,IAAT,CAAc;AAAA;AAAA,0CAAW2B,UAAzB,EAAqCD,QAArC;AACH;AACJ;AAED;;;AACU9B,QAAAA,sBAAsB,CAACsB,IAAD,EAAkB;AAC9CP,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCM,IAAlC;;AACA,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAIM,QAAkB,GAAG,KAAKT,MAAL,CAAYU,UAAZ,CAAuBT,IAAI,CAACK,GAAL,CAASjB,MAAhC,EAAwCY,IAAI,CAACK,GAA7C,CAAzB;;AACA;AAAA;AAAA,sCAASvB,IAAT,CAAc;AAAA;AAAA,0CAAW2B,UAAzB,EAAqCD,QAArC;AACH;AACJ;;AAES5B,QAAAA,oBAAoB,CAACoB,IAAD,EAAkB;AAC5C,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAK,IAAIb,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGW,IAAI,CAACK,GAAL,CAASC,KAAT,CAAehB,MAA3C,EAAmDD,CAAC,EAApD,EAAwD;AACpD,kBAAImB,QAAkB,GAAG,KAAKT,MAAL,CAAYU,UAAZ,CAAuBT,IAAI,CAACK,GAAL,CAASC,KAAT,CAAejB,CAAf,EAAkBD,MAAzC,EAAiDY,IAAI,CAACK,GAAL,CAASC,KAAT,CAAejB,CAAf,CAAjD,CAAzB;;AACA;AAAA;AAAA,wCAASP,IAAT,CAAc;AAAA;AAAA,4CAAW2B,UAAzB,EAAqCD,QAArC;AACH;AACJ;AACJ;AAED;;;AACOK,QAAAA,gBAAgB,CAACC,KAAD,EAAqB;AACxC,cAAIA,KAAK,CAACxB,MAAN,GAAe,CAAnB,EAAsB;AAClB,gBAAIa,SAAqB,GAAG,KAAKJ,MAAL,CAAYK,WAAZ,CAAwBU,KAAK,CAAC,CAAD,CAAL,CAAS1B,MAAjC,EAAyC0B,KAAzC,CAA5B;;AACA;AAAA;AAAA,sCAAShC,IAAT,CAAc;AAAA;AAAA,0CAAWyB,cAAzB,EAAyCJ,SAAzC;AACH;AACJ;AAED;;;AACOY,QAAAA,oBAAoB,CAACP,QAAD,EAA6B;AACpD,cAAIQ,QAAgB,GAAG,GAAvB;;AACA,eAAK,IAAI3B,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGmB,QAAQ,CAACS,QAAT,CAAkB3B,MAA9C,EAAsDD,CAAC,EAAvD,EAA2D;AACvD,gBAAI6B,OAAoB,GAAG;AAAA;AAAA,kDAAezD,WAAf,GAA6ByB,KAA7B,CAAmCiC,YAAnC,CAAgDX,QAAQ,CAACS,QAAT,CAAkB5B,CAAlB,CAAhD,CAA3B;;AACA,gBAAI6B,OAAO,IAAIF,QAAQ,GAAGE,OAAO,CAACE,cAAlC,EAAkD;AAC9CJ,cAAAA,QAAQ,GAAGE,OAAO,CAACE,cAAnB;AACH;AACJ;;AACD,iBAAOJ,QAAP;AACH;AAED;;;AACOK,QAAAA,eAAe,CAACb,QAAD,EAAoC;AACtD,cAAIc,IAAmB,GAAG,EAA1B;;AACA,eAAK,IAAIjC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGmB,QAAQ,CAACS,QAAT,CAAkB3B,MAA9C,EAAsDD,CAAC,EAAvD,EAA2D;AACvD,gBAAI6B,OAAoB,GAAG;AAAA;AAAA,kDAAezD,WAAf,GAA6ByB,KAA7B,CAAmCiC,YAAnC,CAAgDX,QAAQ,CAACS,QAAT,CAAkB5B,CAAlB,CAAhD,CAA3B;AACAiC,YAAAA,IAAI,CAACC,IAAL,CAAUL,OAAV;AACH;;AACD,iBAAOI,IAAP;AACH;AAED;;;AACOE,QAAAA,8BAA8B,CAACP,QAAD,EAAkC;AACnE,cAAID,QAAgB,GAAG,GAAvB;;AACA,eAAK,IAAI3B,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG4B,QAAQ,CAAC3B,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,gBAAI4B,QAAQ,CAAC5B,CAAD,CAAR,IAAe2B,QAAQ,GAAGC,QAAQ,CAAC5B,CAAD,CAAR,CAAY+B,cAA1C,EAA0D;AACtDJ,cAAAA,QAAQ,GAAGC,QAAQ,CAAC5B,CAAD,CAAR,CAAY+B,cAAvB;AACH;AACJ;;AACD,iBAAOJ,QAAP;AACH;AAED;;;AACOS,QAAAA,oBAAoB,CAACjB,QAAD,EAA6B;AACpD,cAAIkB,GAAW,GAAG,CAAlB;;AACA,eAAK,IAAIrC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGmB,QAAQ,CAACmB,QAAT,CAAkBrC,MAA9C,EAAsDD,CAAC,EAAvD,EAA2D;AACvDqC,YAAAA,GAAG,IAAIlB,QAAQ,CAACmB,QAAT,CAAkBtC,CAAlB,CAAP;AACH;;AACD,iBAAOqC,GAAP;AACH;AAED;;;AACOE,QAAAA,gCAAgC,CAACX,QAAD,EAAkC;AACrE,cAAIS,GAAW,GAAG,CAAlB;AACA,cAAIG,QAA4B,GAAG,IAAnC;;AACA,eAAK,IAAIxC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG4B,QAAQ,CAAC3B,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,gBAAI4B,QAAQ,CAAC5B,CAAD,CAAZ,EAAiB;AACbwC,cAAAA,QAAQ,GAAG;AAAA;AAAA,oDAAepE,WAAf,GAA6ByB,KAA7B,CAAmC4C,kBAAnC,CAAsDb,QAAQ,CAAC5B,CAAD,CAAR,CAAY0C,KAAlE,CAAX;AACAL,cAAAA,GAAG,IAAIG,QAAQ,CAACF,QAAhB;AACH;AACJ;;AACD,iBAAOD,GAAP;AACH;AAED;;;AACOM,QAAAA,YAAY,CAACf,QAAD,EAAkC;AACjD,cAAIgB,KAAc,GAAG,IAArB;AACA,cAAIC,KAAa,GAAG,OAApB;AACA,cAAIC,GAAkB,GAAG,IAAzB;;AACA,eAAK,IAAI9C,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG4B,QAAQ,CAAC3B,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,gBAAI4B,QAAQ,CAAC5B,CAAD,CAAZ,EAAiB;AACb8C,cAAAA,GAAG,GAAG;AAAA;AAAA,oDAAe1E,WAAf,GAA6ByB,KAA7B,CAAmCkD,aAAnC,CAAiDnB,QAAQ,CAAC5B,CAAD,CAAR,CAAYgD,KAA7D,CAAN;AACAH,cAAAA,KAAK,GAAGI,IAAI,CAACC,GAAL,CAASL,KAAT,EAAgB;AAAA;AAAA,8CAAYM,UAAZ,CAAuBL,GAAG,CAACD,KAA3B,EAAkCC,GAAG,CAACM,UAAJ,GAAexB,QAAQ,CAAC5B,CAAD,CAAR,CAAY0C,KAA7D,EAAoEd,QAAQ,CAAC5B,CAAD,CAAR,CAAYqD,WAAhF,CAAhB,CAAR;AACAT,cAAAA,KAAK,GAAG,KAAR;AACH;AACJ;;AACD,cAAIA,KAAJ,EAAU;AACN,mBAAO,CAAP;AACH,WAFD,MAEK;AACD,mBAAOC,KAAP;AACH;AACJ;;AAEMS,QAAAA,cAAc,CAAC1B,QAAD,EAAkC;AACnD,cAAIgB,KAAc,GAAG,IAArB;AACA,cAAItE,OAAe,GAAG,CAAtB;AACA,cAAIwE,GAAkB,GAAG,IAAzB;;AACA,eAAK,IAAI9C,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG4B,QAAQ,CAAC3B,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,gBAAI4B,QAAQ,CAAC5B,CAAD,CAAZ,EAAiB;AACb8C,cAAAA,GAAG,GAAG;AAAA;AAAA,oDAAe1E,WAAf,GAA6ByB,KAA7B,CAAmCkD,aAAnC,CAAiDnB,QAAQ,CAAC5B,CAAD,CAAR,CAAYgD,KAA7D,CAAN;AACA1E,cAAAA,OAAO,IAAI;AAAA;AAAA,8CAAY6E,UAAZ,CAAuBL,GAAG,CAACS,OAA3B,EAAoCT,GAAG,CAACU,YAAJ,GAAiB5B,QAAQ,CAAC5B,CAAD,CAAR,CAAY0C,KAAjE,EAAwEd,QAAQ,CAAC5B,CAAD,CAAR,CAAYyD,aAApF,CAAX;AACAb,cAAAA,KAAK,GAAG,KAAR;AACH;AACJ;;AACD,cAAIA,KAAJ,EAAU;AACN,mBAAO,CAAP;AACH,WAFD,MAEK;AACD,mBAAOtE,OAAP;AACH;AACJ;AAED;;;AACOoF,QAAAA,WAAW,CAAC9B,QAAD,EAAkC;AAChD,cAAIS,GAAW,GAAG,CAAlB;AACA,cAAIsB,QAAgB,GAAG,CAAvB;;AACA,eAAK,IAAI3D,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG4B,QAAQ,CAAC3B,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,gBAAI4B,QAAQ,CAAC5B,CAAD,CAAR,KAAgB4B,QAAQ,CAAC5B,CAAD,CAAR,CAAY4D,MAAZ,CAAmBC,IAAnB,IAA2BF,QAA3B,IAAuCA,QAAQ,IAAI,CAAnE,CAAJ,EAA2E;AACvEA,cAAAA,QAAQ,GAAG/B,QAAQ,CAAC5B,CAAD,CAAR,CAAY4D,MAAZ,CAAmBC,IAA9B;AACAxB,cAAAA,GAAG;AACN,aAHD,MAGO;AACH;AACH;AACJ;;AACD,cAAIA,GAAG,IAAI,CAAX,EAAc;AACV,mBAAOsB,QAAP;AACH;;AACD,iBAAO,CAAP;AACH;;AAEMG,QAAAA,eAAe,CAAC3C,QAAD,EAA6B;AAC/C,cAAI4C,QAAgB,GAAG,EAAvB;;AACA,cAAI5C,QAAQ,CAAC6C,KAAT,GAAiB,CAArB,EAAwB;AACpB,gBAAI7C,QAAQ,CAAC8C,GAAT,IAAgB;AAAA;AAAA,oCAAQC,MAA5B,EAAoC;AAChCH,cAAAA,QAAQ,GAAG,MAAX;AACH,aAFD,MAEO;AACHA,cAAAA,QAAQ,GAAG,MAAX;AACH;AACJ,WAND,MAMO;AACH,gBAAI5C,QAAQ,CAAC8C,GAAT,IAAgB;AAAA;AAAA,oCAAQE,IAA5B,EAAkC;AAC9BJ,cAAAA,QAAQ,GAAG,MAAX;AACH,aAFD,MAEO,IAAI5C,QAAQ,CAAC8C,GAAT,IAAgB;AAAA;AAAA,oCAAQG,OAA5B,EAAqC;AACxCL,cAAAA,QAAQ,GAAG,MAAX;AACH,aAFM,MAEA,IAAI5C,QAAQ,CAAC8C,GAAT,IAAgB;AAAA;AAAA,oCAAQI,SAA5B,EAAuC;AAC1CN,cAAAA,QAAQ,GAAG,MAAX;AACH,aAFM,MAEA,IAAI5C,QAAQ,CAAC8C,GAAT,IAAgB;AAAA;AAAA,oCAAQK,QAA5B,EAAsC;AACzCP,cAAAA,QAAQ,GAAG,MAAX;AACH,aAFM,MAED;AACFA,cAAAA,QAAQ,GAAG,MAAX;AACH;AACJ;;AACD,iBAAOA,QAAP;AACH;AAED;;;AACOQ,QAAAA,WAAW,CAACxE,MAAD,EAAuB;AACrC,cAAIyE,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa/F,WADH;AAEhBsC,YAAAA,GAAG,EAAE;AACDjB,cAAAA,MAAM,EAAEA;AADP;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAW3B,WAAX,GAAyBsG,IAAzB,CAA8BF,QAA9B;AACH;;AAEMlE,QAAAA,UAAU,CAACP,MAAD,EAAiBQ,KAAjB,EAAsC;AACnD,cAAIiE,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa7F,UADH;AAEhBoC,YAAAA,GAAG,EAAE;AACDjB,cAAAA,MAAM,EAAEA,MADP;AAEDQ,cAAAA,KAAK,EAACA;AAFL;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWnC,WAAX,GAAyBsG,IAAzB,CAA8BF,QAA9B;AACH;AAED;;;AACOG,QAAAA,cAAc,CAAC5E,MAAc,GAAG,CAAlB,EAAqB6E,SAAiB,GAAG,CAAzC,EAA4CrE,KAAa,GAAG,CAA5D,EAA+DsE,QAAgB,GAAG,CAAlF,EAAqFjE,SAArF,EAA2G;AAC5H,cAAI4D,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa3F,YADH;AAEhBkC,YAAAA,GAAG,EAAE;AACDjB,cAAAA,MAAM,EAAEA,MADP;AAED6E,cAAAA,SAAS,EAAEA,SAFV;AAGDrE,cAAAA,KAAK,EAAEA,KAHN;AAIDsE,cAAAA,QAAQ,EAAEA;AAJT;AAFW,WAApB;AASA;AAAA;AAAA,wCAAWzG,WAAX,GAAyBsG,IAAzB,CAA8BF,QAA9B,EAAwC5D,SAAxC;AACH;AAED;;;AACOkE,QAAAA,gBAAgB,CAACC,MAAc,GAAG,CAAlB,EAAqBC,IAAc,GAAG,EAAtC,EAA0CpE,SAA1C,EAAgE;AACnF,cAAI4D,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAazF,cADH;AAEhBgC,YAAAA,GAAG,EAAE;AACD+D,cAAAA,MAAM,EAAEA,MADP;AAEDC,cAAAA,IAAI,EAAEA;AAFL;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAW5G,WAAX,GAAyBsG,IAAzB,CAA8BF,QAA9B,EAAwC5D,SAAxC;AACH;AAED;;;AACOqE,QAAAA,iBAAiB,CAACF,MAAc,GAAG,CAAlB,EAAqBd,GAAW,GAAG,CAAnC,EAAsCiB,CAAS,GAAG,CAAlD,EAAqDC,CAAS,GAAG,CAAjE,EAAoEvE,SAAc,GAAG,IAArF,EAAiG;AACrH,cAAI4D,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAavF,WADH;AAEhB8B,YAAAA,GAAG,EAAE;AACD+D,cAAAA,MAAM,EAAEA,MADP;AAEDd,cAAAA,GAAG,EAAEA,GAFJ;AAGDiB,cAAAA,CAAC,EAAEA,CAHF;AAIDC,cAAAA,CAAC,EAAEA;AAJF;AAFW,WAApB;AASA;AAAA;AAAA,wCAAW/G,WAAX,GAAyBsG,IAAzB,CAA8BF,QAA9B,EAAwC5D,SAAxC;AACH;;AAnV4B,O;;sBAAZjD,W", "sourcesContent": ["\nimport { ServerConfig } from \"../config/ServerConfig\";\n// 移除循环依赖：不直接导入LoginCommand和MapCommand\n// import LoginCommand from \"../login/LoginCommand\";\nimport { MapCityData } from \"../map/MapCityProxy\";\n// import MapCommand from \"../map/MapCommand\";\nimport { NetManager } from \"../network/socket/NetManager\";\nimport ArmyProxy, { ArmyCmd, ArmyData } from \"./ArmyProxy\";\nimport GeneralCommand from \"./GeneralCommand\";\nimport { GenaralLevelConfig, GeneralConfig, GeneralData } from \"./GeneralProxy\";\nimport { EventMgr } from \"../utils/EventMgr\";\nimport { LogicEvent } from \"../common/LogicEvent\";\n\n\nexport default class ArmyCommand {\n    //单例\n    protected static _instance: ArmyCommand;\n    public static getInstance(): ArmyCommand {\n        if (this._instance == null) {\n            this._instance = new ArmyCommand();\n        }\n        return this._instance;\n    }\n\n    public static destory(): boolean {\n        if (this._instance) {\n            this._instance.onDestory();\n            this._instance = null;\n            return true;\n        }\n        return false;\n    }\n\n    //数据model\n    protected _proxy: ArmyProxy = new ArmyProxy();\n\n    constructor() {\n        EventMgr.on(ServerConfig.army_myList, this.onQryArmyList, this);\n        EventMgr.on(ServerConfig.army_myOne, this.onQryArmyOne, this);\n\n        EventMgr.on(ServerConfig.army_dispose, this.onGeneralDispose, this);\n        EventMgr.on(ServerConfig.army_conscript, this.onGeneralConscript, this);\n        EventMgr.on(ServerConfig.army_assign, this.onGeneralAssignArmy, this);\n        EventMgr.on(ServerConfig.army_push, this.onGeneralArmyStatePush, this);\n        EventMgr.on(ServerConfig.nationMap_scanBlock, this.onNationMapScanBlock, this);\n\n        //定时检测自己的军队是否有武将已经征兵完，如果是请求刷新\n        setInterval(() => {\n            // 通过事件系统获取主城信息，避免循环依赖\n            EventMgr.emit(LogicEvent.getMyMainCity, (myCity: MapCityData) => {\n                if (myCity != null){\n                    let armyList: ArmyData[] = this.proxy.getArmyList(myCity.cityId);\n                    if (armyList == null){\n                        return\n                    }\n\n                    for (let i: number = 0; i < armyList.length; i++) {\n                        var army = armyList[i];\n                        if (army != null && army.isGenConEnd()){\n                            console.log(\"有武将征兵完了\");\n                            this.qryArmyOne(army.cityId, army.order);\n                        }\n                    }\n                }\n            });\n         }, 1000);\n    }\n\n    public onDestory(): void {\n        EventMgr.targetOff(this);\n    }\n\n    public clearData(): void {\n        this._proxy.clearData();\n    }\n\n    public get proxy(): ArmyProxy {\n        return this._proxy;\n    }\n\n    /**军队列表回调*/\n    protected onQryArmyList(data: any, otherData: any): void {\n        console.log(\"onQryArmyList\", data);\n        if (data.code == 0) {\n            let armyDatas: ArmyData[] = this._proxy.updateArmys(data.msg.cityId, data.msg.armys);\n            EventMgr.emit(LogicEvent.updateArmyList, armyDatas);\n        }\n    }\n\n    protected onQryArmyOne(data: any, otherData: any): void {\n        console.log(\"onQryArmyOne\", data);\n        if (data.code == 0) {\n            let armyData = this._proxy.updateArmy(data.msg.army.cityId, data.msg.army);\n            let armyDatas: ArmyData[] = this._proxy.getArmyList(data.msg.army.cityId);\n            EventMgr.emit(LogicEvent.updateArmyList, armyDatas);\n            EventMgr.emit(LogicEvent.updateArmy, armyData);\n        }\n    }\n\n    \n\n    /**配置将领回调*/\n    protected onGeneralDispose(data: any, otherData: any): void {\n        console.log(\"onGeneralDispose\", data);\n        if (data.code == 0) {\n            let armyData: ArmyData = this._proxy.updateArmy(data.msg.army.cityId, data.msg.army);\n            console.log(\"armyData\", armyData);\n            EventMgr.emit(LogicEvent.updateArmy, armyData);\n        }\n    }\n\n    /**征兵回调*/\n    protected onGeneralConscript(data: any, otherData: any): void {\n        console.log(\"onGeneralConscript\", data);\n        if (data.code == 0) {\n            LoginCommand.getInstance().proxy.saveEnterData(data.msg);\n            EventMgr.emit(LogicEvent.upateMyRoleRes);\n\n            let armyData: ArmyData = this._proxy.updateArmy(data.msg.army.cityId, data.msg.army);\n            EventMgr.emit(LogicEvent.updateArmy, armyData);\n\n        }\n    }\n\n    /**出征回调*/\n    protected onGeneralAssignArmy(data: any, otherData: any): void {\n        console.log(\"onGeneralAssignArmy\", data);\n        if (data.code == 0) {\n            let armyData: ArmyData = this._proxy.updateArmy(data.msg.army.cityId, data.msg.army);\n            EventMgr.emit(LogicEvent.updateArmy, armyData);\n        }\n    }\n\n    /**军队状态变更*/\n    protected onGeneralArmyStatePush(data: any): void {\n        console.log(\"onGeneralArmyState\", data);\n        if (data.code == 0) {\n            let armyData: ArmyData = this._proxy.updateArmy(data.msg.cityId, data.msg);\n            EventMgr.emit(LogicEvent.updateArmy, armyData);\n        }\n    }\n\n    protected onNationMapScanBlock(data: any): void {\n        if (data.code == 0) {\n            for (let i: number = 0; i < data.msg.armys.length; i++) {\n                let armyData: ArmyData = this._proxy.updateArmy(data.msg.armys[i].cityId, data.msg.armys[i]);\n                EventMgr.emit(LogicEvent.updateArmy, armyData);\n            }\n        }\n    }\n\n    /**我的角色属性*/\n    public updateMyProperty(datas: any[]): void {\n        if (datas.length > 0) {\n            let armyDatas: ArmyData[] = this._proxy.updateArmys(datas[0].cityId, datas);\n            EventMgr.emit(LogicEvent.updateArmyList, armyDatas);\n        }\n    }\n\n    /**获取军队当前体力*/\n    public getArmyPhysicalPower(armyData: ArmyData): number {\n        let minPower: number = 100;\n        for (let i: number = 0; i < armyData.generals.length; i++) {\n            let general: GeneralData = GeneralCommand.getInstance().proxy.getMyGeneral(armyData.generals[i]);\n            if (general && minPower > general.physical_power) {\n                minPower = general.physical_power;\n            }\n        }\n        return minPower;\n    }\n\n    /**获取军队将领列表*/\n    public getArmyGenerals(armyData: ArmyData): GeneralData[] {\n        let list: GeneralData[] = [];\n        for (let i: number = 0; i < armyData.generals.length; i++) {\n            let general: GeneralData = GeneralCommand.getInstance().proxy.getMyGeneral(armyData.generals[i]);\n            list.push(general);\n        }\n        return list;\n    }\n\n    /**根据将领列表获取军队体力*/\n    public getArmyPhysicalPowerByGenerals(generals: GeneralData[]): number {\n        let minPower: number = 100;\n        for (let i: number = 0; i < generals.length; i++) {\n            if (generals[i] && minPower > generals[i].physical_power) {\n                minPower = generals[i].physical_power;\n            }\n        }\n        return minPower;\n    }\n\n    /**获取军队当前士兵数*/\n    public getArmyCurSoldierCnt(armyData: ArmyData): number {\n        let cnt: number = 0;\n        for (let i: number = 0; i < armyData.soldiers.length; i++) {\n            cnt += armyData.soldiers[i];\n        }\n        return cnt;\n    }\n\n    /**根据将领列表获取军队总士兵数*/\n    public getArmyTotalSoldierCntByGenerals(generals: GeneralData[]): number {\n        let cnt: number = 0;\n        let levelCfg: GenaralLevelConfig = null;\n        for (let i: number = 0; i < generals.length; i++) {\n            if (generals[i]) {\n                levelCfg = GeneralCommand.getInstance().proxy.getGeneralLevelCfg(generals[i].level);\n                cnt += levelCfg.soldiers;\n            }\n        }\n        return cnt;\n    }\n\n    /**获取速度**/\n    public getArmySpeed(generals: GeneralData[]): number {\n        let empyt: boolean = true;\n        let speed: number = 1000000;\n        let cfg: GeneralConfig = null;\n        for (let i: number = 0; i < generals.length; i++) {\n            if (generals[i]) {\n                cfg = GeneralCommand.getInstance().proxy.getGeneralCfg(generals[i].cfgId);\n                speed = Math.min(speed, GeneralData.getPrValue(cfg.speed, cfg.speed_grow*generals[i].level, generals[i].speed_added));\n                empyt = false;\n            }\n        }\n        if (empyt){\n            return 0;\n        }else{\n            return speed;\n        }\n    }\n\n    public getArmyDestroy(generals: GeneralData[]): number {\n        let empyt: boolean = true;\n        let destory: number = 0;\n        let cfg: GeneralConfig = null;\n        for (let i: number = 0; i < generals.length; i++) {\n            if (generals[i]) {\n                cfg = GeneralCommand.getInstance().proxy.getGeneralCfg(generals[i].cfgId);\n                destory += GeneralData.getPrValue(cfg.destroy, cfg.destroy_grow*generals[i].level, generals[i].destroy_added);\n                empyt = false;\n            }\n        }\n        if (empyt){\n            return 0;\n        }else{\n            return destory;\n        }\n    }\n\n    /**根据将领列表获取军队阵营*/\n    public getArmyCamp(generals: GeneralData[]): number {\n        let cnt: number = 0;\n        let lastCamp: number = 0;\n        for (let i: number = 0; i < generals.length; i++) {\n            if (generals[i] && (generals[i].config.camp == lastCamp || lastCamp == 0)) {\n                lastCamp = generals[i].config.camp;\n                cnt++;\n            } else {\n                break;\n            }\n        }\n        if (cnt >= 3) {\n            return lastCamp;\n        }\n        return 0;\n    }\n\n    public getArmyStateDes(armyData: ArmyData): string {\n        let stateStr: string = \"\";\n        if (armyData.state > 0) {\n            if (armyData.cmd == ArmyCmd.Return) {\n                stateStr = \"[撤退]\";\n            } else {\n                stateStr = \"[行军]\";\n            }\n        } else {\n            if (armyData.cmd == ArmyCmd.Idle) {\n                stateStr = \"[待命]\";\n            } else if (armyData.cmd == ArmyCmd.Reclaim) {\n                stateStr = \"[屯田]\";\n            } else if (armyData.cmd == ArmyCmd.Conscript) {\n                stateStr = \"[征兵]\";\n            } else if (armyData.cmd == ArmyCmd.Garrison) {\n                stateStr = \"[驻守]\";\n            }else {\n                stateStr = \"[停留]\";\n            }\n        }\n        return stateStr;\n    }\n\n    /**请求自己的军队信息*/\n    public qryArmyList(cityId: number): void {\n        let sendData: any = {\n            name: ServerConfig.army_myList,\n            msg: {\n                cityId: cityId\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public qryArmyOne(cityId: number, order: number): void {\n        let sendData: any = {\n            name: ServerConfig.army_myOne,\n            msg: {\n                cityId: cityId,\n                order:order,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    /**给军队配置将领*/\n    public generalDispose(cityId: number = 0, generalId: number = 0, order: number = 0, position: number = 0, otherData: any): void {\n        let sendData: any = {\n            name: ServerConfig.army_dispose,\n            msg: {\n                cityId: cityId,\n                generalId: generalId,\n                order: order,\n                position: position,\n            }\n        };\n        NetManager.getInstance().send(sendData, otherData);\n    }\n\n    /**给军队征兵*/\n    public generalConscript(armyId: number = 0, cnts: number[] = [], otherData: any): void {\n        let sendData: any = {\n            name: ServerConfig.army_conscript,\n            msg: {\n                armyId: armyId,\n                cnts: cnts,\n            }\n        };\n        NetManager.getInstance().send(sendData, otherData);\n    }\n\n    /**出兵*/\n    public generalAssignArmy(armyId: number = 0, cmd: number = 0, x: number = 0, y: Number = 0, otherData: any = null): void {\n        let sendData: any = {\n            name: ServerConfig.army_assign,\n            msg: {\n                armyId: armyId,\n                cmd: cmd,\n                x: x,\n                y: y,\n            }\n        };\n        NetManager.getInstance().send(sendData, otherData);\n    }\n}\n"]}