System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Canvas, view, sys, director, screen, ResolutionPolicy, GameConfig, _dec, _class, _class2, _temp, _crd, ccclass, property, FixedScreenAdapter;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../config/GameConfig", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Canvas = _cc.Canvas;
      view = _cc.view;
      sys = _cc.sys;
      director = _cc.director;
      screen = _cc.screen;
      ResolutionPolicy = _cc.ResolutionPolicy;
    }, function (_unresolved_2) {
      GameConfig = _unresolved_2.GameConfig;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "df9eaLhfGZALpafNxxQgDGN", "FixedScreenAdapter", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 固定屏幕适配器
       * 专门解决点击偏移问题的最简单方案
       * 使用固定分辨率 + 强制Canvas设置
       */

      _export("FixedScreenAdapter", FixedScreenAdapter = (_dec = ccclass('FixedScreenAdapter'), _dec(_class = (_temp = _class2 = class FixedScreenAdapter extends Component {
        static get instance() {
          return FixedScreenAdapter._instance;
        }

        onLoad() {
          FixedScreenAdapter._instance = this;
          console.log('[FixedScreenAdapter] 固定屏幕适配器初始化');
        }

        onDestroy() {
          if (FixedScreenAdapter._instance === this) {
            FixedScreenAdapter._instance = null;
          }
        }
        /**
         * 初始化屏幕适配
         */


        init() {
          console.log('[FixedScreenAdapter] 开始初始化屏幕适配...'); // 获取屏幕信息

          const windowSize = screen.windowSize;
          console.log(`[FixedScreenAdapter] 窗口尺寸: ${windowSize.width}x${windowSize.height}`); // 使用固定的设计分辨率，不进行任何缩放适配

          const designWidth = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.baseWidth;
          const designHeight = (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.baseHeight; // 关键：使用EXACT_FIT策略，强制匹配屏幕尺寸

          view.setDesignResolutionSize(windowSize.width, windowSize.height, ResolutionPolicy.EXACT_FIT);
          console.log(`[FixedScreenAdapter] 设计分辨率设置为窗口尺寸: ${windowSize.width}x${windowSize.height} (EXACT_FIT)`); // 立即修复所有Canvas

          this.fixAllCanvas(); // 监听场景切换

          director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this); // 监听屏幕尺寸变化

          this.setupResizeListener(); // 延迟打印最终信息

          this.scheduleOnce(() => {
            this.printFinalInfo();
          }, 0.2);
          console.log('[FixedScreenAdapter] 屏幕适配初始化完成');
        }
        /**
         * 场景启动后的回调
         */


        onSceneLaunched(scene) {
          this.scheduleOnce(() => {
            console.log('[FixedScreenAdapter] 新场景启动，修复Canvas设置');
            this.fixAllCanvas();
          }, 0);
        }
        /**
         * 修复所有Canvas设置
         */


        fixAllCanvas() {
          const scene = director.getScene();

          if (!scene) {
            console.warn('[FixedScreenAdapter] 无法获取当前场景');
            return;
          }

          const canvasComponents = scene.getComponentsInChildren(Canvas);
          console.log(`[FixedScreenAdapter] 找到 ${canvasComponents.length} 个Canvas组件`);
          canvasComponents.forEach((canvas, index) => {
            console.log(`[FixedScreenAdapter] 修复Canvas[${index}]:`); // 最关键的设置：禁用自动对齐屏幕

            const oldAlign = canvas.alignCanvasWithScreen;
            canvas.alignCanvasWithScreen = false;
            console.log(`  - alignCanvasWithScreen: ${oldAlign} → false`); // 确保Canvas位置为原点

            const oldPos = canvas.node.position.clone();
            canvas.node.setPosition(0, 0, 0);
            console.log(`  - position: (${oldPos.x}, ${oldPos.y}, ${oldPos.z}) → (0, 0, 0)`); // 获取UITransform并设置尺寸

            const uiTransform = canvas.getComponent('UITransform');

            if (uiTransform) {
              const windowSize = screen.windowSize;
              const oldSize = {
                width: uiTransform.width,
                height: uiTransform.height
              };
              uiTransform.setContentSize(windowSize.width, windowSize.height);
              uiTransform.setAnchorPoint(0.5, 0.5);
              console.log(`  - 尺寸: ${oldSize.width}x${oldSize.height} → ${windowSize.width}x${windowSize.height}`);
              console.log(`  - 锚点: (0.5, 0.5)`);
            }

            console.log(`[FixedScreenAdapter] Canvas[${index}] 修复完成`);
          });
        }
        /**
         * 设置屏幕尺寸变化监听
         */


        setupResizeListener() {
          let resizeTimer = null;

          const handleResize = () => {
            if (resizeTimer) {
              clearTimeout(resizeTimer);
            }

            resizeTimer = setTimeout(() => {
              console.log('[FixedScreenAdapter] 检测到窗口尺寸变化，重新适配...');
              this.init(); // 重新初始化
            }, 100);
          }; // 监听窗口尺寸变化


          window.addEventListener('resize', handleResize); // 监听屏幕方向变化（移动设备）

          if (sys.isMobile) {
            window.addEventListener('orientationchange', () => {
              setTimeout(() => {
                console.log('[FixedScreenAdapter] 检测到屏幕方向变化，重新适配...');
                this.init();
              }, 200);
            });
          }

          console.log('[FixedScreenAdapter] 屏幕尺寸变化监听已设置');
        }
        /**
         * 打印最终适配信息
         */


        printFinalInfo() {
          if (!(_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.enableDebugInfo) return;
          const windowSize = screen.windowSize;
          const visibleSize = view.getVisibleSize();
          const visibleOrigin = view.getVisibleOrigin();
          const designSize = view.getDesignResolutionSize();
          console.log('=== 固定屏幕适配最终信息 ===');
          console.log(`窗口尺寸: ${windowSize.width}x${windowSize.height}`);
          console.log(`设计分辨率: ${designSize.width}x${designSize.height}`);
          console.log(`可视区域: ${visibleSize.width.toFixed(1)}x${visibleSize.height.toFixed(1)}`);
          console.log(`可视原点: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`); // 计算缩放比例

          const scaleX = visibleSize.width / designSize.width;
          const scaleY = visibleSize.height / designSize.height;
          console.log(`缩放比例: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`); // 理想状态检查

          const isIdeal = Math.abs(scaleX - 1.0) < 0.01 && Math.abs(scaleY - 1.0) < 0.01 && Math.abs(visibleOrigin.x) < 1 && Math.abs(visibleOrigin.y) < 1;

          if (isIdeal) {
            console.log('✅ 适配状态理想，点击应该正常工作');
          } else {
            console.warn('⚠️ 适配状态异常，可能存在点击偏移');

            if (Math.abs(visibleOrigin.x) > 1 || Math.abs(visibleOrigin.y) > 1) {
              console.warn(`  - 可视原点偏移: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);
            }

            if (Math.abs(scaleX - 1.0) > 0.01 || Math.abs(scaleY - 1.0) > 0.01) {
              console.warn(`  - 缩放比例异常: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);
            }
          }

          console.log('============================');
        }
        /**
         * 获取当前适配信息
         */


        getAdaptInfo() {
          const windowSize = screen.windowSize;
          const visibleSize = view.getVisibleSize();
          const visibleOrigin = view.getVisibleOrigin();
          const designSize = view.getDesignResolutionSize();
          return {
            windowSize,
            designSize,
            visibleSize,
            visibleOrigin,
            scaleX: visibleSize.width / designSize.width,
            scaleY: visibleSize.height / designSize.height
          };
        }
        /**
         * 手动触发适配信息打印
         */


        printDebugInfo() {
          this.printFinalInfo();
        }
        /**
         * 强制重新适配
         */


        forceReadapt() {
          console.log('[FixedScreenAdapter] 强制重新适配');
          this.init();
        }

      }, _defineProperty(_class2, "_instance", null), _temp)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=FixedScreenAdapter.js.map