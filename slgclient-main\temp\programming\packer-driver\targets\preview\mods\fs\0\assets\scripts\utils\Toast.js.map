{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts"], "names": ["_decorator", "Component", "Label", "ccclass", "property", "Toast", "onRemove", "node", "active", "setText", "text", "unschedule", "schedule", "msgLabel", "string"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;;;;;;OAC1B;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;yBAGTK,K,WADpBF,OAAO,CAAC,OAAD,C,UAEHC,QAAQ,CAACF,KAAD,C,oCAFb,MACqBG,KADrB,SACmCJ,SADnC,CAC6C;AAAA;AAAA;;AAAA;AAAA;;AAG/BK,QAAAA,QAAQ,GAAO;AACrB,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AACMC,QAAAA,OAAO,CAACC,IAAD,EAAkB;AAC5B,eAAKC,UAAL,CAAgB,KAAKL,QAArB;AACA,eAAKM,QAAL,CAAc,KAAKN,QAAnB,EAA6B,CAA7B;AACA,eAAKO,QAAL,CAAcC,MAAd,GAAuBJ,IAAvB;AACH;;AAVwC,O;;;;;iBAEhB,I", "sourcesContent": ["import { _decorator, Component, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Toast')\nexport default class Toast extends Component {\n    @property(Label)\n    msgLabel: Label | null = null;\n    protected onRemove():void{\n        this.node.active = false;\n    }\n    public setText(text:string):void{\n        this.unschedule(this.onRemove);\n        this.schedule(this.onRemove, 2);\n        this.msgLabel.string = text;\n    }\n}\n"]}