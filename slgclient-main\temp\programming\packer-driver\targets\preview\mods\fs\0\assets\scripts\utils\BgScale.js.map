{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts"], "names": ["_decorator", "Component", "Enum", "sys", "UITransform", "Widget", "view", "ccclass", "property", "BGScaleType", "BGAlignmentType", "BgScale", "type", "onLoad", "realW", "node", "getComponent", "width", "realH", "height", "setMyFrameSize", "<PERSON><PERSON><PERSON><PERSON>", "_resizeCallback", "setMyFrameSizeAgain", "bind", "window", "addEventListener", "document", "onDestroy", "removeEventListener", "wsize", "getVisibleSize", "scale1", "scale2", "max_scale", "Math", "max", "scaleX", "scaleY", "scaleType", "SCALE_BY_WIDTH", "SCALE_BY_HEIGHT", "SCALE_ONLY_WIDTH", "SCALE_ONLY_HEIGHT", "widget", "addComponent", "alignmentType", "BOTTOM", "isAlignHorizontalCenter", "isAlignBottom", "bottom", "TOP", "isAlignTop", "top", "LEFT", "isAlignVerticalCenter", "isAlignLeft", "left", "RIGHT", "isAlignRight", "right", "CENTER", "scheduleOnce", "changeOrientation", "flag", "FULL_SCREEN"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,G,OAAAA,G;AAAeC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;;;;;;;OACpE;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBR,U;;iBAEhBS,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;SAAAA,W,2BAAAA,W;;iBAQAC,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;SAAAA,e,+BAAAA,e;;yBASSC,O,WAChBH,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEV,IAAI,CAACQ,eAAD;AAAX,OAAD,C,UAERF,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEV,IAAI,CAACO,WAAD;AAAX,OAAD,C,EAJZF,O,8BAAD,MACqBI,OADrB,SACqCV,SADrC,CAC+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA,yCAMpB,CANoB;;AAAA,yCAOpB,CAPoB;;AAAA,mDAQb,IARa;AAAA;;AAUjCY,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,GAAa,KAAKC,IAAL,CAAUC,YAAV,CAAuBZ,WAAvB,EAAoCa,KAAjD;AACA,eAAKC,KAAL,GAAa,KAAKH,IAAL,CAAUC,YAAV,CAAuBZ,WAAvB,EAAoCe,MAAjD;AACA,eAAKC,cAAL;;AACA,cAAIjB,GAAG,CAACkB,SAAR,EAAmB;AACf,iBAAKC,eAAL,GAAuB,KAAKC,mBAAL,CAAyBC,IAAzB,CAA8B,IAA9B,CAAvB;AACAC,YAAAA,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKJ,eAAvC;AACAG,YAAAA,MAAM,CAACC,gBAAP,CAAwB,mBAAxB,EAA6C,KAAKJ,eAAlD;AACAK,YAAAA,QAAQ,CAACD,gBAAT,CAA0B,cAA1B,EAA0C,KAAKJ,eAA/C;AACAK,YAAAA,QAAQ,CAACD,gBAAT,CAA0B,aAA1B,EAAyC,KAAKJ,eAA9C;AACH;AACJ;;AAESM,QAAAA,SAAS,GAAQ;AACvB,cAAIzB,GAAG,CAACkB,SAAR,EAAmB;AACfI,YAAAA,MAAM,CAACI,mBAAP,CAA2B,QAA3B,EAAqC,KAAKP,eAA1C;AACAG,YAAAA,MAAM,CAACI,mBAAP,CAA2B,mBAA3B,EAAgD,KAAKP,eAArD;AACAK,YAAAA,QAAQ,CAACE,mBAAT,CAA6B,cAA7B,EAA6C,KAAKP,eAAlD;AACAK,YAAAA,QAAQ,CAACE,mBAAT,CAA6B,aAA7B,EAA4C,KAAKP,eAAjD;AACA,iBAAKA,eAAL,GAAuB,IAAvB;AACH;AACJ;;AAESF,QAAAA,cAAc,GAAQ;AAE5B,cAAI,CAAC,KAAKL,IAAV,EAAgB;AACZ;AACH;;AACD,cAAIe,KAAK,GAAG,IAAZ;AACAA,UAAAA,KAAK,GAAGxB,IAAI,CAACyB,cAAL,EAAR;AAEA,cAAIC,MAAM,GAAGF,KAAK,CAACb,KAAN,GAAc,KAAKH,KAAhC;AACA,cAAImB,MAAM,GAAGH,KAAK,CAACX,MAAN,GAAe,KAAKD,KAAjC;AACA,cAAIgB,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASJ,MAAT,EAAiBC,MAAjB,CAAhB;AACA,cAAII,MAAJ,EAAYC,MAAZ;;AACA,cAAI,KAAKC,SAAL,IAAkB9B,WAAW,CAAC+B,cAAlC,EAAkD;AAC9CH,YAAAA,MAAM,GAAGC,MAAM,GAAGN,MAAlB;AACH,WAFD,MAEO,IAAI,KAAKO,SAAL,IAAkB9B,WAAW,CAACgC,eAAlC,EAAmD;AACtDJ,YAAAA,MAAM,GAAGC,MAAM,GAAGL,MAAlB;AACH,WAFM,MAEA,IAAI,KAAKM,SAAL,IAAkB9B,WAAW,CAACiC,gBAAlC,EAAoD;AACvDL,YAAAA,MAAM,GAAGL,MAAT;AACAM,YAAAA,MAAM,GAAG,CAAT;AACH,WAHM,MAGA,IAAI,KAAKC,SAAL,IAAkB9B,WAAW,CAACkC,iBAAlC,EAAqD;AACxDN,YAAAA,MAAM,GAAG,CAAT;AACAC,YAAAA,MAAM,GAAGL,MAAT;AACH,WAHM,MAGA,IAAI9B,GAAG,CAACkB,SAAR,EAAmB;AACtB;AACAgB,YAAAA,MAAM,GAAGC,MAAM,GAAGJ,SAAlB,CAFsB,CAGtB;AACH,WAJM,MAIA;AACHG,YAAAA,MAAM,GAAGC,MAAM,GAAGJ,SAAlB;AACH;;AAED,eAAKnB,IAAL,CAAUC,YAAV,CAAuBZ,WAAvB,EAAoCa,KAApC,GAA4C,KAAKH,KAAL,GAAauB,MAAzD;AACA,eAAKtB,IAAL,CAAUC,YAAV,CAAuBZ,WAAvB,EAAoCe,MAApC,GAA6C,KAAKD,KAAL,GAAaoB,MAA1D;AAEA,cAAIM,MAAM,GAAG,KAAK7B,IAAL,CAAUC,YAAV,CAAuBX,MAAvB,CAAb;;AACA,cAAIuC,MAAM,IAAI,IAAd,EAAoB;AAChBA,YAAAA,MAAM,GAAG,KAAK7B,IAAL,CAAU8B,YAAV,CAAuBxC,MAAvB,CAAT;AACH;;AAED,cAAI,KAAKyC,aAAL,IAAsBpC,eAAe,CAACqC,MAA1C,EAAkD;AAC9CH,YAAAA,MAAM,CAACI,uBAAP,GAAiC,IAAjC;AACAJ,YAAAA,MAAM,CAACK,aAAP,GAAuB,IAAvB;AACAL,YAAAA,MAAM,CAACM,MAAP,GAAgB,CAAhB;AACH,WAJD,MAIO,IAAI,KAAKJ,aAAL,IAAsBpC,eAAe,CAACyC,GAA1C,EAA+C;AAClDP,YAAAA,MAAM,CAACI,uBAAP,GAAiC,IAAjC;AACAJ,YAAAA,MAAM,CAACQ,UAAP,GAAoB,IAApB;AACAR,YAAAA,MAAM,CAACS,GAAP,GAAa,CAAb;AACH,WAJM,MAIA,IAAI,KAAKP,aAAL,IAAsBpC,eAAe,CAAC4C,IAA1C,EAAgD;AACnDV,YAAAA,MAAM,CAACW,qBAAP,GAA+B,IAA/B;AACAX,YAAAA,MAAM,CAACY,WAAP,GAAqB,IAArB;AACAZ,YAAAA,MAAM,CAACa,IAAP,GAAc,CAAd;AACH,WAJM,MAIA,IAAI,KAAKX,aAAL,IAAsBpC,eAAe,CAACgD,KAA1C,EAAiD;AACpDd,YAAAA,MAAM,CAACW,qBAAP,GAA+B,IAA/B;AACAX,YAAAA,MAAM,CAACe,YAAP,GAAsB,IAAtB;AACAf,YAAAA,MAAM,CAACgB,KAAP,GAAe,CAAf;AACH,WAJM,MAIA,IAAI,KAAKd,aAAL,IAAsBpC,eAAe,CAACmD,MAA1C,EAAkD;AACrDjB,YAAAA,MAAM,CAACI,uBAAP,GAAiC,IAAjC;AACAJ,YAAAA,MAAM,CAACW,qBAAP,GAA+B,IAA/B;AACH;AACJ;;AAEShC,QAAAA,mBAAmB,GAAQ;AACjC,eAAKuC,YAAL,CAAkB,YAAY;AAC1B,iBAAK1C,cAAL;AACH,WAFiB,CAEhBI,IAFgB,CAEX,IAFW,CAAlB,EAEc,IAFd;AAGH;;AAESuC,QAAAA,iBAAiB,CAACC,IAAD,EAAoB;AAC3C,eAAK5C,cAAL;AACH;;AArG0C,O;;;;;iBAEXV,eAAe,CAACmD,M;;;;;;;iBAExBpD,WAAW,CAACwD,W", "sourcesContent": ["import { _decorator, Component, En<PERSON>, sys, director, UITransform, Widget, view, Vec3 } from 'cc';\nconst {ccclass, property} = _decorator;\n\nexport enum BGScaleType {\n    FULL_SCREEN,                    \n    SCALE_BY_WIDTH,                 \n    SCALE_BY_HEIGHT,\n    SCALE_ONLY_WIDTH,  \n    SCALE_ONLY_HEIGHT,                     \n}\n\nexport enum BGAlignmentType {\n    TOP,                    \n    BOTTOM,                 \n    CENTER,\n    LEFT,  \n    RIGHT,                     \n}\n\n@ccclass\nexport default class BgScale extends Component {\n    @property({type: Enum(BGAlignmentType)})\n    alignmentType:BGAlignmentType = BGAlignmentType.CENTER;\n    @property({type: Enum(BGScaleType)})\n    scaleType:BGScaleType = BGScaleType.FULL_SCREEN;\n\n    private realW:number = 0;\n    private realH:number = 0;\n    private _resizeCallback:any = null;\n\n    protected onLoad ():void {\n        this.realW = this.node.getComponent(UITransform).width;\n        this.realH = this.node.getComponent(UITransform).height;\n        this.setMyFrameSize();\n        if (sys.isBrowser) {\n            this._resizeCallback = this.setMyFrameSizeAgain.bind(this);\n            window.addEventListener('resize', this._resizeCallback);\n            window.addEventListener('orientationchange', this._resizeCallback);\n            document.addEventListener('rotateScreen', this._resizeCallback);\n            document.addEventListener('resetScreen', this._resizeCallback);\n        }\n    }\n\n    protected onDestroy():void {\n        if (sys.isBrowser) {\n            window.removeEventListener('resize', this._resizeCallback);\n            window.removeEventListener('orientationchange', this._resizeCallback);\n            document.removeEventListener('rotateScreen', this._resizeCallback);\n            document.removeEventListener('resetScreen', this._resizeCallback);\n            this._resizeCallback = null;\n        }\n    }\n\n    protected setMyFrameSize():void {\n       \n        if (!this.node) {\n            return;\n        }\n        var wsize = null;\n        wsize = view.getVisibleSize();\n        \n        var scale1 = wsize.width / this.realW;\n        var scale2 = wsize.height / this.realH;\n        var max_scale = Math.max(scale1, scale2);\n        var scaleX, scaleY;\n        if (this.scaleType == BGScaleType.SCALE_BY_WIDTH) {\n            scaleX = scaleY = scale1;\n        } else if (this.scaleType == BGScaleType.SCALE_BY_HEIGHT) {\n            scaleX = scaleY = scale2;\n        } else if (this.scaleType == BGScaleType.SCALE_ONLY_WIDTH) {\n            scaleX = scale1;\n            scaleY = 1;\n        } else if (this.scaleType == BGScaleType.SCALE_ONLY_HEIGHT) {\n            scaleX = 1;\n            scaleY = scale2;\n        } else if (sys.isBrowser) {\n            //横向浏览器 只缩放宽度\n            scaleX = scaleY = max_scale;\n            // scaleY = 1;\n        } else {\n            scaleX = scaleY = max_scale;\n        }\n\n        this.node.getComponent(UITransform).width = this.realW * scaleX;\n        this.node.getComponent(UITransform).height = this.realH * scaleY;\n\n        var widget = this.node.getComponent(Widget);\n        if (widget == null) {\n            widget = this.node.addComponent(Widget);\n        }\n  \n        if (this.alignmentType == BGAlignmentType.BOTTOM) {\n            widget.isAlignHorizontalCenter = true;\n            widget.isAlignBottom = true;\n            widget.bottom = 0;\n        } else if (this.alignmentType == BGAlignmentType.TOP) {\n            widget.isAlignHorizontalCenter = true;\n            widget.isAlignTop = true;\n            widget.top = 0;\n        } else if (this.alignmentType == BGAlignmentType.LEFT) {\n            widget.isAlignVerticalCenter = true;\n            widget.isAlignLeft = true;\n            widget.left = 0;\n        } else if (this.alignmentType == BGAlignmentType.RIGHT) {\n            widget.isAlignVerticalCenter = true;\n            widget.isAlignRight = true;\n            widget.right = 0;\n        } else if (this.alignmentType == BGAlignmentType.CENTER) {\n            widget.isAlignHorizontalCenter = true;\n            widget.isAlignVerticalCenter = true;\n        }\n    }\n\n    protected setMyFrameSizeAgain():void {\n        this.scheduleOnce(function () {\n            this.setMyFrameSize();\n        }.bind(this), 0.05);\n    }\n\n    protected changeOrientation(flag:boolean):void {\n        this.setMyFrameSize();\n    }\n}\n"]}