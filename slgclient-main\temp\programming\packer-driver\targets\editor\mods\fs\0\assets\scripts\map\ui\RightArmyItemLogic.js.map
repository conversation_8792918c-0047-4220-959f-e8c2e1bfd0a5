{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts"], "names": ["_decorator", "Component", "Label", "Node", "Sprite", "UITransform", "ArmyCmd", "GeneralCommand", "ArmyCommand", "MapCommand", "DateUtil", "GeneralHeadLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "RightArmyItemLogic", "onLoad", "on", "updateGeneral", "onUpdateGeneral", "node", "getComponent", "height", "bottomNode", "active", "onDestroy", "targetOff", "_data", "update", "state", "cmd", "Reclaim", "nowTime", "getServerTime", "time", "Math", "max", "endTime", "getInstance", "proxy", "getCommonCfg", "reclamation_time", "labelPos", "string", "converSecondStr", "onClickTop", "instance", "playClick", "onClickBack", "cityData", "cityProxy", "getMyMainCity", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "Return", "x", "y", "onClickSetting", "getMyCityById", "cityId", "emit", "openArmySetting", "order", "updateGeneralByData", "stateStr", "getArmyStateDes", "teamName", "_firstGeneral", "cfg", "getGeneralCfg", "cfgId", "name", "headIcon", "setHeadId", "labelStrength", "physical_power", "physical_power_limit", "labelInfo", "updateItem", "generals", "getMyGeneral", "labelSoldierCnt", "soldiers", "Idle", "btnSetting", "fromX", "fromY", "btnBack", "Conscript", "setArmyData", "data"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAG5CC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,c;;AACAC,MAAAA,W;;AAGAC,MAAAA,U;;AACAC,MAAAA,Q;;AACAC,MAAAA,gB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;OAZH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;yBAeTiB,kB,WADpBF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACb,IAAD,C,UAERa,QAAQ,CAACZ,MAAD,C,UAERY,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACb,IAAD,C,WAERa,QAAQ,CAACb,IAAD,C,oCAlBb,MACqBc,kBADrB,SACgDhB,SADhD,CAC0D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAoB/B,CApB+B;;AAAA,yCAqB1B,IArB0B;;AAAA,iDAsBf,IAtBe;;AAAA,kDAuBnB,CAvBmB;AAAA;;AAyB5CiB,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,aAAvB,EAAsC,KAAKC,eAA3C,EAA4D,IAA5D;AACA,eAAKC,IAAL,CAAUC,YAAV,CAAuBlB,WAAvB,EAAoCmB,MAApC,IAA8C,KAAKC,UAAL,CAAgBF,YAAhB,CAA6BlB,WAA7B,EAA0CmB,MAAxF;AACA,eAAKC,UAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,eAAKC,KAAL,GAAa,IAAb;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAI,KAAKD,KAAL,KAAe,KAAKA,KAAL,CAAWE,KAAX,GAAmB,CAAnB,IAAwB,KAAKF,KAAL,CAAWG,GAAX,IAAkB;AAAA;AAAA,kCAAQC,OAAjE,CAAJ,EAA+E;AAC3E,gBAAIC,OAAe,GAAG;AAAA;AAAA,sCAASC,aAAT,EAAtB;AACA,gBAAIC,IAAY,GAAG,CAAnB;;AACA,gBAAI,KAAKP,KAAL,CAAWE,KAAX,GAAmB,CAAvB,EAA0B;AACtB;AACAK,cAAAA,IAAI,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAKT,KAAL,CAAWU,OAAX,GAAqBL,OAAjC,CAAP;AACH,aAHD,MAGO;AACH;AACAE,cAAAA,IAAI,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY;AAAA;AAAA,oDAAeE,WAAf,GAA6BC,KAA7B,CAAmCC,YAAnC,GAAkDC,gBAAlD,GAAqE,IAArE,IAA6ET,OAAO,GAAG,KAAKL,KAAL,CAAWU,OAAlG,CAAZ,CAAP,CAFG,CAGH;AACA;AACA;AACA;AACA;AACH;;AACD,iBAAKK,QAAL,CAAcC,MAAd,GAAuB;AAAA;AAAA,sCAASC,eAAT,CAAyBV,IAAzB,CAAvB;AACH;AACJ;;AAESf,QAAAA,eAAe,GAAS,CAEjC;;AAES0B,QAAAA,UAAU,GAAS;AACzB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,eAAKxB,UAAL,CAAgBC,MAAhB,GAAyB,CAAC,KAAKD,UAAL,CAAgBC,MAA1C;;AACA,cAAG,KAAKD,UAAL,CAAgBC,MAAnB,EAA0B;AACtB,iBAAKJ,IAAL,CAAUC,YAAV,CAAuBlB,WAAvB,EAAoCmB,MAApC,IAA8C,KAAKC,UAAL,CAAgBF,YAAhB,CAA6BlB,WAA7B,EAA0CmB,MAAxF;AACH,WAFD,MAEK;AACD,iBAAKF,IAAL,CAAUC,YAAV,CAAuBlB,WAAvB,EAAoCmB,MAApC,IAA8C,KAAKC,UAAL,CAAgBF,YAAhB,CAA6BlB,WAA7B,EAA0CmB,MAAxF;AACH;AACJ;;AAES0B,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKpB,KAAT,EAAgB;AACZ,gBAAIsB,QAAqB,GAAG;AAAA;AAAA,0CAAWX,WAAX,GAAyBY,SAAzB,CAAmCC,aAAnC,EAA5B;AACA;AAAA;AAAA,4CAAYb,WAAZ,GAA0Bc,iBAA1B,CAA4C,KAAKzB,KAAL,CAAW0B,EAAvD,EAA2D;AAAA;AAAA,oCAAQC,MAAnE,EAA2EL,QAAQ,CAACM,CAApF,EAAuFN,QAAQ,CAACO,CAAhG,EAAmG,IAAnG;AACH;AACJ;;AAESC,QAAAA,cAAc,GAAS;AAC7B;AAAA;AAAA,4CAAaX,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKpB,KAAT,EAAgB;AACZ,gBAAIsB,QAAqB,GAAG;AAAA;AAAA,0CAAWX,WAAX,GAAyBY,SAAzB,CAAmCQ,aAAnC,CAAiD,KAAK/B,KAAL,CAAWgC,MAA5D,CAA5B;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,eAAzB,EAA0C,KAAKlC,KAAL,CAAWgC,MAArD,EAA6D,KAAKG,KAAlE;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAS;AAClC,cAAIC,QAAgB,GAAG;AAAA;AAAA,0CAAY1B,WAAZ,GAA0B2B,eAA1B,CAA0C,KAAKtC,KAA/C,CAAvB;AACA,cAAIuC,QAAQ,GAAG,EAAf;;AACA,cAAI,KAAKC,aAAT,EAAwB;AACpB,gBAAIC,GAAkB,GAAG;AAAA;AAAA,kDAAe9B,WAAf,GAA6BC,KAA7B,CAAmC8B,aAAnC,CAAiD,KAAKF,aAAL,CAAmBG,KAApE,CAAzB;AACAJ,YAAAA,QAAQ,GAAGE,GAAG,CAACG,IAAf;AACA,iBAAKC,QAAL,CAAcnD,YAAd;AAAA;AAAA,sDAA6CoD,SAA7C,CAAuD,KAAKN,aAAL,CAAmBG,KAA1E;AACA,iBAAKI,aAAL,CAAmB/B,MAAnB,GAA4B,QAAQ,KAAKwB,aAAL,CAAmBQ,cAA3B,GAA4C,GAA5C,GAAkDP,GAAG,CAACQ,oBAAlF;AACH;;AACD,eAAKC,SAAL,CAAelC,MAAf,GAAwBqB,QAAQ,GAAG,GAAX,GAAiBE,QAAjB,GAA4B,GAApD;AAEH;;AAESY,QAAAA,UAAU,GAAS;AACzB,cAAI,KAAKnD,KAAL,IAAc,KAAKA,KAAL,CAAWoD,QAAX,CAAoB,CAApB,KAA0B,CAA5C,EAA+C;AAC3C;AACA,iBAAK3D,IAAL,CAAUI,MAAV,GAAmB,IAAnB;AACA,iBAAK2C,aAAL,GAAqB;AAAA;AAAA,kDAAe7B,WAAf,GAA6BC,KAA7B,CAAmCyC,YAAnC,CAAgD,KAAKrD,KAAL,CAAWoD,QAAX,CAAoB,CAApB,CAAhD,CAArB;AACA,iBAAKhB,mBAAL;AAEA,iBAAKrB,QAAL,CAAcC,MAAd,GAAuB,MAAM,KAAKhB,KAAL,CAAW4B,CAAjB,GAAqB,IAArB,GAA4B,KAAK5B,KAAL,CAAW6B,CAAvC,GAA2C,GAAlE;AAEA,iBAAKyB,eAAL,CAAqBtC,MAArB,GAA8B,SAAS,KAAKhB,KAAL,CAAWuD,QAAX,CAAoB,CAApB,IAAyB,KAAKvD,KAAL,CAAWuD,QAAX,CAAoB,CAApB,CAAzB,GAAkD,KAAKvD,KAAL,CAAWuD,QAAX,CAAoB,CAApB,CAA3D,CAA9B;;AAEA,gBAAI,KAAKvD,KAAL,CAAWG,GAAX,IAAkB;AAAA;AAAA,oCAAQqD,IAA9B,EAAoC;AAEhC,mBAAKC,UAAL,CAAgB5D,MAAhB,GAAyB,IAAzB;AACA,kBAAIyB,QAAqB,GAAG;AAAA;AAAA,4CAAWX,WAAX,GAAyBY,SAAzB,CAAmCQ,aAAnC,CAAiD,KAAK/B,KAAL,CAAWgC,MAA5D,CAA5B;;AACA,kBAAIV,QAAQ,IAAIA,QAAQ,CAACM,CAAT,IAAc,KAAK5B,KAAL,CAAW0D,KAArC,IAA8CpC,QAAQ,CAACO,CAAT,IAAc,KAAK7B,KAAL,CAAW2D,KAA3E,EAAiF;AAC7E;AACA,qBAAKC,OAAL,CAAa/D,MAAb,GAAsB,KAAtB;AACH,eAHD,MAGK;AACD;AACA,qBAAK+D,OAAL,CAAa/D,MAAb,GAAsB,IAAtB;AACH;AAEJ,aAZD,MAYO,IAAI,KAAKG,KAAL,CAAWG,GAAX,IAAkB;AAAA;AAAA,oCAAQ0D,SAA9B,EAAwC;AAC3C,mBAAKJ,UAAL,CAAgB5D,MAAhB,GAAyB,KAAzB;AACA,mBAAK+D,OAAL,CAAa/D,MAAb,GAAsB,KAAtB;AACH,aAHM,MAGA,IAAI,KAAKG,KAAL,CAAWE,KAAX,IAAoB,CAApB,IAAyB,KAAKF,KAAL,CAAWG,GAAX,IAAkB;AAAA;AAAA,oCAAQC,OAAvD,EAAgE;AACnE;AACA,mBAAKqD,UAAL,CAAgB5D,MAAhB,GAAyB,KAAzB;AACA,mBAAK+D,OAAL,CAAa/D,MAAb,GAAsB,IAAtB;AACH,aAJM,MAIA;AACH,mBAAK4D,UAAL,CAAgB5D,MAAhB,GAAyB,KAAzB;AACA,mBAAK+D,OAAL,CAAa/D,MAAb,GAAsB,KAAtB;AACH;AACJ,WAjCD,MAiCO;AACH,iBAAK2C,aAAL,GAAqB,IAArB;AACA,iBAAK/C,IAAL,CAAUI,MAAV,GAAmB,KAAnB;AACH;AACJ;;AAEMiE,QAAAA,WAAW,CAACC,IAAD,EAAuB;AACrC,eAAK/D,KAAL,GAAa+D,IAAb;AACA,eAAKZ,UAAL;AACH;;AA9IqD,O;;;;;iBAEnC,I;;;;;;;iBAED,I;;;;;;;iBAEC,I;;;;;;;iBAEA,I;;;;;;;iBAEM,I;;;;;;;iBAEF,I;;;;;;;iBAEF,I;;;;;;;iBAEL,I;;;;;;;iBAEG,I", "sourcesContent": ["import { _decorator, Component, Label, Node, Sprite, UITransform } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { ArmyCmd, ArmyData } from \"../../general/ArmyProxy\";\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport { GeneralConfig, GeneralData } from \"../../general/GeneralProxy\";\nimport { MapCityData } from \"../MapCityProxy\";\nimport MapCommand from \"../MapCommand\";\nimport DateUtil from \"../../utils/DateUtil\";\nimport GeneralHeadLogic from \"./GeneralHeadLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('RightArmyItemLogic')\nexport default class RightArmyItemLogic extends Component {\n    @property(Label)\n    labelInfo: Label = null;\n    @property(Label)\n    labelPos: Label = null;\n    @property(Node)\n    bottomNode: Node = null;\n    @property(Sprite)\n    headIcon: Sprite = null;\n    @property(Label)\n    labelSoldierCnt: Label = null;\n    @property(Label)\n    labelStrength: Label = null;\n    @property(Label)\n    labelMorale: Label = null;\n    @property(Node)\n    btnBack: Node = null;\n    @property(Node)\n    btnSetting: Node = null;\n\n    public order: number = 0;\n    protected _data: ArmyData = null;\n    protected _firstGeneral: GeneralData = null;\n    protected _qryReturnTime: number = 0;\n\n    protected onLoad(): void {\n        EventMgr.on(LogicEvent.updateGeneral, this.onUpdateGeneral, this);\n        this.node.getComponent(UITransform).height -= this.bottomNode.getComponent(UITransform).height;\n        this.bottomNode.active = false;\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._data = null;\n    }\n\n    protected update(): void {\n        if (this._data && (this._data.state > 0 || this._data.cmd == ArmyCmd.Reclaim)) {\n            let nowTime: number = DateUtil.getServerTime();\n            let time: number = 0;\n            if (this._data.state > 0) {\n                //行军或者撤退中\n                time = Math.max(0, this._data.endTime - nowTime);\n            } else {\n                //屯田中\n                time = Math.max(0, GeneralCommand.getInstance().proxy.getCommonCfg().reclamation_time * 1000 - (nowTime - this._data.endTime));\n                // if (time == 0 && nowTime - this._qryReturnTime > 2000) {\n                //     //屯田结束 主动请求撤退\n                //     this._qryReturnTime = nowTime;\n                //     this.onClickBack();\n                // }\n            }\n            this.labelPos.string = DateUtil.converSecondStr(time);\n        }\n    }\n\n    protected onUpdateGeneral(): void {\n\n    }\n\n    protected onClickTop(): void {\n        AudioManager.instance.playClick();\n        this.bottomNode.active = !this.bottomNode.active;\n        if(this.bottomNode.active){\n            this.node.getComponent(UITransform).height += this.bottomNode.getComponent(UITransform).height;\n        }else{\n            this.node.getComponent(UITransform).height -= this.bottomNode.getComponent(UITransform).height;\n        }\n    }\n\n    protected onClickBack(): void {\n        AudioManager.instance.playClick();\n        if (this._data) {\n            let cityData: MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n            ArmyCommand.getInstance().generalAssignArmy(this._data.id, ArmyCmd.Return, cityData.x, cityData.y, null);\n        }\n    }\n\n    protected onClickSetting(): void {\n        AudioManager.instance.playClick();\n        if (this._data) {\n            let cityData: MapCityData = MapCommand.getInstance().cityProxy.getMyCityById(this._data.cityId);\n            EventMgr.emit(LogicEvent.openArmySetting, this._data.cityId, this.order);\n        }\n    }\n\n    protected updateGeneralByData(): void {\n        let stateStr: string = ArmyCommand.getInstance().getArmyStateDes(this._data);\n        var teamName = \"\";\n        if (this._firstGeneral) {\n            let cfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(this._firstGeneral.cfgId);\n            teamName = cfg.name;\n            this.headIcon.getComponent(GeneralHeadLogic).setHeadId(this._firstGeneral.cfgId);\n            this.labelStrength.string = \"体力 \" + this._firstGeneral.physical_power + \"/\" + cfg.physical_power_limit;\n        }\n        this.labelInfo.string = stateStr + \" \" + teamName + \"队\";\n\n    }\n\n    protected updateItem(): void {\n        if (this._data && this._data.generals[0] != 0) {\n            // console.log(\"updateItem\", this._data);\n            this.node.active = true;\n            this._firstGeneral = GeneralCommand.getInstance().proxy.getMyGeneral(this._data.generals[0]);\n            this.updateGeneralByData();\n\n            this.labelPos.string = \"(\" + this._data.x + \", \" + this._data.y + \")\";\n\n            this.labelSoldierCnt.string = \"骑兵 \" + (this._data.soldiers[0] + this._data.soldiers[1] + this._data.soldiers[2]);\n\n            if (this._data.cmd == ArmyCmd.Idle) {\n                \n                this.btnSetting.active = true;\n                let cityData: MapCityData = MapCommand.getInstance().cityProxy.getMyCityById(this._data.cityId);\n                if (cityData && cityData.x == this._data.fromX && cityData.y == this._data.fromY){\n                    //代表在城池里面\n                    this.btnBack.active = false;\n                }else{\n                    //代表在城外据点待命\n                    this.btnBack.active = true;\n                }\n\n            } else if (this._data.cmd == ArmyCmd.Conscript){\n                this.btnSetting.active = false;\n                this.btnBack.active = false;\n            } else if (this._data.state == 0 && this._data.cmd != ArmyCmd.Reclaim) {\n                //停留的时候才能配置队伍和撤退\n                this.btnSetting.active = false;\n                this.btnBack.active = true;\n            } else {\n                this.btnSetting.active = false;\n                this.btnBack.active = false;\n            }\n        } else {\n            this._firstGeneral = null;\n            this.node.active = false;\n        }\n    }\n\n    public setArmyData(data: ArmyData): void {\n        this._data = data;\n        this.updateItem();\n    }\n}\n"]}