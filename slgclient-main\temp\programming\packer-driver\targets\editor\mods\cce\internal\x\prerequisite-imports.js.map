{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4E,uCAA5E,EAAmK,uCAAnK,EAA4P,uCAA5P,EAAiV,uCAAjV,EAAsa,uCAAta,EAA2f,uCAA3f,EAAqlB,uCAArlB,EAA0qB,uCAA1qB,EAAiwB,uCAAjwB,EAA21B,wCAA31B,EAAm7B,wCAAn7B,EAAygC,wCAAzgC,EAA4lC,wCAA5lC,EAAorC,wCAAprC,EAA4wC,wCAA5wC,EAAs2C,wCAAt2C,EAA+7C,wCAA/7C,EAAohD,wCAAphD,EAA6mD,wCAA7mD,EAAusD,wCAAvsD,EAA+xD,wCAA/xD,EAA43D,wCAA53D,EAAu9D,wCAAv9D,EAA2iE,wCAA3iE,EAA8nE,wCAA9nE,EAAutE,wCAAvtE,EAA6yE,wCAA7yE,EAAm4E,wCAAn4E,EAA89E,wCAA99E,EAAmjF,wCAAnjF,EAA8oF,wCAA9oF,EAAyuF,wCAAzuF,EAAi0F,wCAAj0F,EAAk6F,wCAAl6F,EAA2/F,wCAA3/F,EAA6lG,wCAA7lG,EAA6rG,wCAA7rG,EAAoxG,wCAApxG,EAA22G,wCAA32G,EAA08G,wCAA18G,EAAoiH,wCAApiH,EAA2nH,wCAA3nH,EAAutH,wCAAvtH,EAA+yH,wCAA/yH,EAA04H,wCAA14H,EAAs+H,wCAAt+H,EAA6jI,wCAA7jI,EAAopI,wCAAppI,EAA8uI,wCAA9uI,EAAm0I,wCAAn0I,EAAm6I,wCAAn6I,EAAs/I,wCAAt/I,EAAykJ,wCAAzkJ,EAAoqJ,wCAApqJ,EAA0vJ,wCAA1vJ,EAAo1J,wCAAp1J,EAA46J,wCAA56J,EAA8/J,wCAA9/J,EAA0lK,wCAA1lK,EAA0rK,wCAA1rK,EAA2xK,wCAA3xK,EAAu3K,wCAAv3K,EAA49K,wCAA59K,EAA4jL,wCAA5jL,EAAupL,wCAAvpL,EAAsvL,wCAAtvL,EAAu1L,wCAAv1L,EAAw7L,wCAAx7L,EAAohM,wCAAphM,EAAmnM,wCAAnnM,EAAqtM,wCAArtM,EAAuzM,wCAAvzM,EAAi5M,wCAAj5M,EAAq+M,wCAAr+M,EAA4jN,wCAA5jN,EAAopN,wCAAppN,EAA2vN,wCAA3vN,EAAy1N,wCAAz1N,EAAw7N,wCAAx7N,EAAuhO,wCAAvhO,EAAknO,wCAAlnO,EAAitO,wCAAjtO,EAAkzO,wCAAlzO,EAAm5O,wCAAn5O,EAAg/O,wCAAh/O,EAA8kP,wCAA9kP,EAA4qP,wCAA5qP,EAA0wP,wCAA1wP,EAAw2P,wCAAx2P,EAA48P,wCAA58P,EAA4iQ,wCAA5iQ,EAAsoQ,wCAAtoQ,EAA8tQ,wCAA9tQ,EAAszQ,wCAAtzQ,EAAs5Q,wCAAt5Q,EAAs/Q,wCAAt/Q,EAAslR,wCAAtlR,EAAqrR,wCAArrR,EAA0wR,wCAA1wR,EAAs2R,yCAAt2R,EAAk8R,yCAAl8R,EAA8hS,yCAA9hS,EAAsnS,yCAAtnS,EAAitS,yCAAjtS,EAA6yS,yCAA7yS,EAA24S,yCAA34S,EAAu+S,yCAAv+S,EAA0kT,yCAA1kT,EAAyqT,yCAAzqT,EAAywT,yCAAzwT,EAAq2T,yCAAr2T,EAAm8T,yCAAn8T,EAAkiU,yCAAliU,EAAooU,yCAApoU,EAAouU,yCAApuU,EAAi0U,yCAAj0U,EAA+5U,yCAA/5U,EAA4/U,yCAA5/U,EAAmlV,yCAAnlV,EAAwqV,yCAAxqV,EAAiwV,yCAAjwV,EAAw1V,yCAAx1V,EAAm7V,yCAAn7V,EAA2gW,yCAA3gW,EAA+lW,yCAA/lW,EAA+rW,yCAA/rW,EAA2xW,yCAA3xW,EAAo3W,yCAAp3W,EAAi9W,yCAAj9W,EAA4iX,yCAA5iX,EAAwoX,yCAAxoX,EAAsuX,yCAAtuX,EAAg0X,yCAAh0X,EAAu5X,yCAAv5X,EAAk/X,yCAAl/X,EAAglY,yCAAhlY,EAAmrY,yCAAnrY,EAAgxY,yCAAhxY,EAAu2Y,yCAAv2Y,EAA27Y,yCAA37Y,EAAqhZ,yCAArhZ,EAA6mZ,yCAA7mZ,EAAysZ,yCAAzsZ,EAA8xZ,yCAA9xZ,EAAm3Z,yCAAn3Z,EAAk9Z,yCAAl9Z,EAAwia,yCAAxia,EAA+na,yCAA/na,EAAita,yCAAjta,EAA+ya,yCAA/ya,EAAy4a,yCAAz4a,EAAy+a,yCAAz+a,EAA2jb,yCAA3jb,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts\"), () => import(\"file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}