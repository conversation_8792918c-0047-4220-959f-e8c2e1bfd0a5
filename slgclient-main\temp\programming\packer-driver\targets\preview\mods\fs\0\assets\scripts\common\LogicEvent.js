System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, LogicEvent, _crd;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  _export("LogicEvent", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3443bMPBwFCFYWbvTL1+b8i", "LogicEvent", undefined);

      _export("LogicEvent", LogicEvent = class LogicEvent {});

      _defineProperty(LogicEvent, "robLoginUI", "robLoginUI");

      _defineProperty(LogicEvent, "enterMap", "enter_map");

      _defineProperty(LogicEvent, "enterLogin", "enter_login");

      _defineProperty(LogicEvent, "showToast", "show_toast");

      _defineProperty(LogicEvent, "showWaiting", "show_waiting");

      _defineProperty(LogicEvent, "hideWaiting", "hide_waiting");

      _defineProperty(LogicEvent, "showTip", "show_tip");

      _defineProperty(LogicEvent, "createRole", "createRole");

      _defineProperty(LogicEvent, "enterServerComplete", "enterServerComplete");

      _defineProperty(LogicEvent, "loginComplete", "login_complete");

      _defineProperty(LogicEvent, "getMyMainCity", "get_my_main_city");

      _defineProperty(LogicEvent, "touchMap", "touch_map");

      _defineProperty(LogicEvent, "moveMap", "move_map");

      _defineProperty(LogicEvent, "beforeScrollToMap", "before_scroll_to_map");

      _defineProperty(LogicEvent, "mapEenterChange", "map_center_change");

      _defineProperty(LogicEvent, "scrollToMap", "scroll_to_map");

      _defineProperty(LogicEvent, "mapShowAreaChange", "map_show_area_change");

      _defineProperty(LogicEvent, "updateArmy", "update_army");

      _defineProperty(LogicEvent, "updateArmyList", "update_army_list");

      _defineProperty(LogicEvent, "updateTag", "update_tag");

      _defineProperty(LogicEvent, "updateBuilds", "update_builds");

      _defineProperty(LogicEvent, "updateBuild", "update_build");

      _defineProperty(LogicEvent, "deleteBuild", "delete_build");

      _defineProperty(LogicEvent, "updateCitys", "update_citys");

      _defineProperty(LogicEvent, "updateCity", "update_city");

      _defineProperty(LogicEvent, "updateChatHistory", "update_chat_history");

      _defineProperty(LogicEvent, "unionChange", "union_change");

      _defineProperty(LogicEvent, "openCityAbout", "open_city_about");

      _defineProperty(LogicEvent, "closeCityAbout", "close_city_about");

      _defineProperty(LogicEvent, "openFortressAbout", "open_fortress_about");

      _defineProperty(LogicEvent, "openFacility", "open_facility");

      _defineProperty(LogicEvent, "openArmySetting", "open_army_setting");

      _defineProperty(LogicEvent, "upateMyRoleRes", "upate_my_roleRes");

      _defineProperty(LogicEvent, "openGeneralDes", "open_general_des");

      _defineProperty(LogicEvent, "openGeneralChoose", "open_general_choose");

      _defineProperty(LogicEvent, "openArmySelectUi", "open_army_select_ui");

      _defineProperty(LogicEvent, "openDrawResult", "open_draw_result");

      _defineProperty(LogicEvent, "interiorCollect", "interior_collect");

      _defineProperty(LogicEvent, "interiorOpenCollect", "interior_openCollect");

      _defineProperty(LogicEvent, "openGeneralConvert", "open_general_convert");

      _defineProperty(LogicEvent, "openGeneralRoster", "open_general_roster");

      _defineProperty(LogicEvent, "openGeneral", "open_general");

      _defineProperty(LogicEvent, "openSkill", "open_skill");

      _defineProperty(LogicEvent, "closeSkill", "close_skill");

      _defineProperty(LogicEvent, "openSkillInfo", "open_skillInfo");

      _defineProperty(LogicEvent, "closeArmyAelectUi", "close_army_select_ui");

      _defineProperty(LogicEvent, "updateCityAddition", "update_city_addition");

      _defineProperty(LogicEvent, "updateMyFacility", "update_my_facility");

      _defineProperty(LogicEvent, "updateMyFacilities", "update_my_facilities");

      _defineProperty(LogicEvent, "selectFacilityItem", "select_facility_item");

      _defineProperty(LogicEvent, "openGeneralSelect", "open_general_select");

      _defineProperty(LogicEvent, "openArmyConscript", "open_army_conscript");

      _defineProperty(LogicEvent, "chosedGeneral", "chosed_general");

      _defineProperty(LogicEvent, "generalConvert", "general_convert");

      _defineProperty(LogicEvent, "updateMyGenerals", "update_my_generals");

      _defineProperty(LogicEvent, "updateOneGenerals", "update_one_generals");

      _defineProperty(LogicEvent, "updateGeneral", "update_general");

      _defineProperty(LogicEvent, "skillListInfo", "skill_list_info");

      _defineProperty(LogicEvent, "closeUnion", "close_union");

      _defineProperty(LogicEvent, "openMyUnion", "open_my_union");

      _defineProperty(LogicEvent, "dismissUnionSuccess", "dismiss_union_success");

      _defineProperty(LogicEvent, "createUnionSuccess", "create_union_success");

      _defineProperty(LogicEvent, "updateUnionApply", "update_union_apply");

      _defineProperty(LogicEvent, "unionInfo", "union_info");

      _defineProperty(LogicEvent, "unionNotice", "union_notice");

      _defineProperty(LogicEvent, "unionLog", "union_log");

      _defineProperty(LogicEvent, "upateWarReport", "upate_war_report");

      _defineProperty(LogicEvent, "clickWarReport", "click_war_report");

      _defineProperty(LogicEvent, "closeReport", "close_report");

      _defineProperty(LogicEvent, "verifyUnionSuccess", "verify_union_success");

      _defineProperty(LogicEvent, "kickUnionSuccess", "kick_union_success");

      _defineProperty(LogicEvent, "unionAppoint", "union_appoint");

      _defineProperty(LogicEvent, "updateUnionMember", "update_union_member");

      _defineProperty(LogicEvent, "unionAbdicate", "union_abdicate");

      _defineProperty(LogicEvent, "clickUnionMemberItem", "clickUnionMemberItem");

      _defineProperty(LogicEvent, "updateUnionList", "update_union_list");

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=LogicEvent.js.map