{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts"], "names": ["_decorator", "Component", "tween", "UIOpacity", "ccclass", "property", "PanelOut", "onEnable", "console", "log", "node", "getComponent", "opacity", "lt", "to", "easing", "start"], "mappings": ";;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,S,OAAAA,S;;;;;;;OACvC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;0BAIjBM,Q,WADZF,OAAO,CAAC,UAAD,C,gBAAR,MACaE,QADb,SAC8BL,SAD9B,CACwC;AACpCM,QAAAA,QAAQ,GAAE;AACNC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACA,eAAKC,IAAL,CAAUC,YAAV,CAAuBR,SAAvB,EAAkCS,OAAlC,GAA4C,GAA5C;AACA,cAAIC,EAAE,GAAGX,KAAK,CAAC,KAAKQ,IAAL,CAAUC,YAAV,CAAuBR,SAAvB,CAAD,CAAL,CAAyCW,EAAzC,CAA4C,GAA5C,EAAiD;AAAEF,YAAAA,OAAO,EAAE;AAAX,WAAjD,EAAmE;AAAEG,YAAAA,MAAM,EAAE;AAAV,WAAnE,CAAT;AACAF,UAAAA,EAAE,CAACG,KAAH;AACH;;AANmC,O", "sourcesContent": ["\nimport { _decorator, Component, tween, Vec3, UIOpacity } from 'cc';\nconst { ccclass, property } = _decorator;\n\n\n@ccclass('PanelOut')\nexport class PanelOut extends Component {\n    onEnable(){\n        console.log(\"PanelOut onEnable\");\n        this.node.getComponent(UIOpacity).opacity = 125;\n        let lt = tween(this.node.getComponent(UIOpacity)).to(0.2, { opacity: 255 }, { easing: 'sineOut' })\n        lt.start();\n    }\n}\n\n"]}