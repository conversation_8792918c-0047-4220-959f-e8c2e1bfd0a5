{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"], "names": ["GeneralCommand", "ServerConfig", "NetManager", "GeneralProxy", "EventMgr", "LogicEvent", "getInstance", "_instance", "destory", "onDestory", "constructor", "on", "general_myGenerals", "onMyGenerals", "general_push", "onGeneralPush", "general_drawGeneral", "onDrawGenerals", "general_composeGeneral", "onComposeGeneral", "general_addPrGeneral", "onAddPrGeneral", "general_convert", "onGeneralConvert", "general_upSkill", "onUpSkill", "general_downSkill", "onDownSkill", "general_lvSkill", "onLvSkill", "targetOff", "clearData", "_proxy", "proxy", "data", "console", "log", "code", "updateMyGenerals", "msg", "generals", "emit", "updateGeneral", "openDrawResult", "hideWaiting", "updateOneGenerals", "length", "general", "removeMyGenerals", "gIds", "general<PERSON><PERSON><PERSON>", "updateMyProperty", "datas", "qryMyGenerals", "sendData", "name", "send", "drawGenerals", "drawTimes", "composeGeneral", "compId", "addPrGeneral", "force_add", "strategy_add", "defense_add", "speed_add", "destroy_add", "forceAdd", "strategyAdd", "defenseAdd", "speedAdd", "destroyAdd", "convert", "upSkill", "gId", "cfgId", "pos", "Number", "downSkill", "lvSkill"], "mappings": ";;;4FAMqBA,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANZC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;yBAEYL,c,GAAN,MAAMA,cAAN,CAAqB;AAChC;AAEyB,eAAXM,WAAW,GAAmB;AACxC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIP,cAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKO,SAAZ;AACH;;AAEoB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SAjB+B,CAmBhC;;;AAGAG,QAAAA,WAAW,GAAG;AAAA,0CAFmB;AAAA;AAAA,6CAEnB;;AACV;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,kBAAzB,EAA6C,KAAKC,YAAlD,EAAgE,IAAhE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,YAAzB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,4CAAaK,mBAAzB,EAA8C,KAAKC,cAAnD,EAAmE,IAAnE;AACA;AAAA;AAAA,oCAASN,EAAT,CAAY;AAAA;AAAA,4CAAaO,sBAAzB,EAAiD,KAAKC,gBAAtD,EAAwE,IAAxE;AACA;AAAA;AAAA,oCAASR,EAAT,CAAY;AAAA;AAAA,4CAAaS,oBAAzB,EAA+C,KAAKC,cAApD,EAAoE,IAApE;AACA;AAAA;AAAA,oCAASV,EAAT,CAAY;AAAA;AAAA,4CAAaW,eAAzB,EAA0C,KAAKC,gBAA/C,EAAkE,IAAlE;AACA;AAAA;AAAA,oCAASZ,EAAT,CAAY;AAAA;AAAA,4CAAaa,eAAzB,EAA0C,KAAKC,SAA/C,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASd,EAAT,CAAY;AAAA;AAAA,4CAAae,iBAAzB,EAA4C,KAAKC,WAAjD,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAAShB,EAAT,CAAY;AAAA;AAAA,4CAAaiB,eAAzB,EAA0C,KAAKC,SAA/C,EAA0D,IAA1D;AAGH;;AAEMpB,QAAAA,SAAS,GAAS;AACrB;AAAA;AAAA,oCAASqB,SAAT,CAAmB,IAAnB;AACH;;AAEMC,QAAAA,SAAS,GAAS;AACrB,eAAKC,MAAL,CAAYD,SAAZ;AACH;;AAEe,YAALE,KAAK,GAAiB;AAC7B,iBAAO,KAAKD,MAAZ;AACH;AAED;;;AACUnB,QAAAA,YAAY,CAACqB,IAAD,EAAkB;AACpCC,UAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyCF,IAAzC;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKL,MAAL,CAAYM,gBAAZ,CAA6BJ,IAAI,CAACK,GAAL,CAASC,QAAtC;;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWH,gBAAzB;AACH;AACJ;;AAESvB,QAAAA,aAAa,CAACmB,IAAD,EAAkB;AACrCC,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BF,IAA9B;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKL,MAAL,CAAYU,aAAZ,CAA0BR,IAAI,CAACK,GAA/B;;AACA;AAAA;AAAA,sCAASE,IAAT,CAAc;AAAA;AAAA,0CAAWC,aAAzB;AACH;AACJ;;AAESzB,QAAAA,cAAc,CAACiB,IAAD,EAAkB;AACtCC,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BF,IAA9B;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKL,MAAL,CAAYM,gBAAZ,CAA6BJ,IAAI,CAACK,GAAL,CAASC,QAAtC;;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWH,gBAAzB;AACA;AAAA;AAAA,sCAASG,IAAT,CAAc;AAAA;AAAA,0CAAWE,cAAzB,EAAyCT,IAAI,CAACK,GAAL,CAASC,QAAlD;AACH;;AACD;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWG,WAAzB;AACH;;AAESzB,QAAAA,gBAAgB,CAACe,IAAD,EAAe;AACrCC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCF,IAAjC;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKL,MAAL,CAAYM,gBAAZ,CAA6BJ,IAAI,CAACK,GAAL,CAASC,QAAtC;;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWH,gBAAzB;AACA;AAAA;AAAA,sCAASG,IAAT,CAAc;AAAA;AAAA,0CAAWI,iBAAzB,EAA4CX,IAAI,CAACK,GAAL,CAASC,QAAT,CAAkBN,IAAI,CAACK,GAAL,CAASC,QAAT,CAAkBM,MAAlB,GAA2B,CAA7C,CAA5C;AACH;AACJ;;AAISzB,QAAAA,cAAc,CAACa,IAAD,EAAe;AACnCC,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BF,IAA/B;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKL,MAAL,CAAYU,aAAZ,CAA0BR,IAAI,CAACK,GAAL,CAASQ,OAAnC;;AACA;AAAA;AAAA,sCAASN,IAAT,CAAc;AAAA;AAAA,0CAAWI,iBAAzB,EAA2CX,IAAI,CAACK,GAAL,CAASQ,OAApD;AACH;AACJ;;AAESxB,QAAAA,gBAAgB,CAACW,IAAD,EAAe;AACrCC,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCF,IAAjC;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKL,MAAL,CAAYgB,gBAAZ,CAA6Bd,IAAI,CAACK,GAAL,CAASU,IAAtC;;AACA;AAAA;AAAA,sCAASR,IAAT,CAAc;AAAA;AAAA,0CAAWS,cAAzB,EAAyChB,IAAI,CAACK,GAA9C;AACH;AACJ;;AAESd,QAAAA,SAAS,CAACS,IAAD,EAAe;AAC9BC,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BF,IAA1B;AAEH;;AAGSP,QAAAA,WAAW,CAACO,IAAD,EAAe;AAChCC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BF,IAA5B;AAEH;;AAESL,QAAAA,SAAS,CAACK,IAAD,EAAe;AAC9BC,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BF,IAA1B;AACH;AAGD;;;AACOiB,QAAAA,gBAAgB,CAACC,KAAD,EAAqB;AACxC,eAAKpB,MAAL,CAAYM,gBAAZ,CAA6Bc,KAA7B;;AACA;AAAA;AAAA,oCAASX,IAAT,CAAc;AAAA;AAAA,wCAAWH,gBAAzB;AACH;;AAEMe,QAAAA,aAAa,GAAS;AACzB,cAAIC,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa3C,kBADH;AAEhB2B,YAAAA,GAAG,EAAE;AAFW,WAApB;AAKA;AAAA;AAAA,wCAAWjC,WAAX,GAAyBkD,IAAzB,CAA8BF,QAA9B;AACH;AAGD;AACJ;AACA;AACA;;;AACWG,QAAAA,YAAY,CAACC,SAAD,EAA6B;AAAA,cAA5BA,SAA4B;AAA5BA,YAAAA,SAA4B,GAAT,CAAS;AAAA;;AAC5C,cAAIJ,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAavC,mBADH;AAEhBuB,YAAAA,GAAG,EAAE;AACDmB,cAAAA,SAAS,EAACA;AADT;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAWpD,WAAX,GAAyBkD,IAAzB,CAA8BF,QAA9B;AACH;AAID;AACJ;AACA;AACA;AACA;;;AACWK,QAAAA,cAAc,CAACC,MAAD,EAAmBX,IAAnB,EAA6C;AAAA,cAA5CW,MAA4C;AAA5CA,YAAAA,MAA4C,GAA5B,CAA4B;AAAA;;AAAA,cAA1BX,IAA0B;AAA1BA,YAAAA,IAA0B,GAAV,EAAU;AAAA;;AAC9D,cAAIK,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAarC,sBADH;AAEhBqB,YAAAA,GAAG,EAAE;AACDqB,cAAAA,MAAM,EAACA,MADN;AAEDX,cAAAA,IAAI,EAACA;AAFJ;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAW3C,WAAX,GAAyBkD,IAAzB,CAA8BF,QAA9B;AACH;;AAGMO,QAAAA,YAAY,CAACD,MAAD,EAAmBE,SAAnB,EAAoCC,YAApC,EAAwDC,WAAxD,EAA2EC,SAA3E,EAA4FC,WAA5F,EAAsH;AAAA,cAArHN,MAAqH;AAArHA,YAAAA,MAAqH,GAArG,CAAqG;AAAA;;AACrI,cAAIN,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAanC,oBADH;AAEhBmB,YAAAA,GAAG,EAAE;AACDqB,cAAAA,MAAM,EAACA,MADN;AAEDO,cAAAA,QAAQ,EAACL,SAFR;AAGDM,cAAAA,WAAW,EAACL,YAHX;AAIDM,cAAAA,UAAU,EAACL,WAJV;AAKDM,cAAAA,QAAQ,EAACL,SALR;AAMDM,cAAAA,UAAU,EAACL;AANV;AAFW,WAApB;AAWA;AAAA;AAAA,wCAAW5D,WAAX,GAAyBkD,IAAzB,CAA8BF,QAA9B;AACH;;AAEMkB,QAAAA,OAAO,CAACvB,IAAD,EAAsB;AAChC,cAAIK,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAajC,eADH;AAEhBiB,YAAAA,GAAG,EAAE;AACDU,cAAAA,IAAI,EAACA;AADJ;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAW3C,WAAX,GAAyBkD,IAAzB,CAA8BF,QAA9B;AACH;;AAEMmB,QAAAA,OAAO,CAACC,GAAD,EAAaC,KAAb,EAA2BC,GAA3B,EAA6C;AACvD,cAAItB,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa/B,eADH;AAEhBe,YAAAA,GAAG,EAAE;AACDmC,cAAAA,GAAG,EAACA,GADH;AAEDC,cAAAA,KAAK,EAACA,KAFL;AAGDC,cAAAA,GAAG,EAACC,MAAM,CAACD,GAAD;AAHT;AAFW,WAApB;AASAzC,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BkB,QAA7B;AACA;AAAA;AAAA,wCAAWhD,WAAX,GAAyBkD,IAAzB,CAA8BF,QAA9B;AACH;;AAEMwB,QAAAA,SAAS,CAACJ,GAAD,EAAaC,KAAb,EAA2BC,GAA3B,EAA6C;AACzD,cAAItB,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa7B,iBADH;AAEhBa,YAAAA,GAAG,EAAE;AACDmC,cAAAA,GAAG,EAACA,GADH;AAEDC,cAAAA,KAAK,EAACA,KAFL;AAGDC,cAAAA,GAAG,EAACC,MAAM,CAACD,GAAD;AAHT;AAFW,WAApB;AAQA;AAAA;AAAA,wCAAWtE,WAAX,GAAyBkD,IAAzB,CAA8BF,QAA9B;AACH;;AAGMyB,QAAAA,OAAO,CAACL,GAAD,EAAaE,GAAb,EAAyB;AACnC,cAAItB,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAa3B,eADH;AAEhBW,YAAAA,GAAG,EAAE;AACDmC,cAAAA,GAAG,EAACA,GADH;AAEDE,cAAAA,GAAG,EAACC,MAAM,CAACD,GAAD;AAFT;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWtE,WAAX,GAAyBkD,IAAzB,CAA8BF,QAA9B;AACH;;AApO+B,O;;sBAAftD,c", "sourcesContent": ["import { ServerConfig } from \"../config/ServerConfig\";\nimport { NetManager } from \"../network/socket/NetManager\";\nimport GeneralProxy from \"./GeneralProxy\";\nimport { EventMgr } from \"../utils/EventMgr\";\nimport { LogicEvent } from \"../common/LogicEvent\";\n\nexport default class GeneralCommand {\n    //单例\n    protected static _instance: GeneralCommand;\n    public static getInstance(): GeneralCommand {\n        if (this._instance == null) {\n            this._instance = new GeneralCommand();\n        }\n        return this._instance;\n    }\n\n    public static destory(): boolean {\n        if (this._instance) {\n            this._instance.onDestory();\n            this._instance = null;\n            return true;\n        }\n        return false;\n    }\n\n    //数据model\n    protected _proxy: GeneralProxy = new GeneralProxy();\n\n    constructor() {\n        EventMgr.on(ServerConfig.general_myGenerals, this.onMyGenerals, this);\n        EventMgr.on(ServerConfig.general_push, this.onGeneralPush, this);\n        EventMgr.on(ServerConfig.general_drawGeneral, this.onDrawGenerals, this);\n        EventMgr.on(ServerConfig.general_composeGeneral, this.onComposeGeneral, this);\n        EventMgr.on(ServerConfig.general_addPrGeneral, this.onAddPrGeneral, this);\n        EventMgr.on(ServerConfig.general_convert, this.onGeneralConvert , this);\n        EventMgr.on(ServerConfig.general_upSkill, this.onUpSkill, this);\n        EventMgr.on(ServerConfig.general_downSkill, this.onDownSkill, this);\n        EventMgr.on(ServerConfig.general_lvSkill, this.onLvSkill, this);\n        \n\n    }\n\n    public onDestory(): void {\n        EventMgr.targetOff(this);\n    }\n\n    public clearData(): void {\n        this._proxy.clearData();\n    }\n\n    public get proxy(): GeneralProxy {\n        return this._proxy;\n    }\n\n    /**我的将领列表*/\n    protected onMyGenerals(data: any): void {\n        console.log(\"onMyGeneralsonMyGenerals \", data);\n        if (data.code == 0) {\n            this._proxy.updateMyGenerals(data.msg.generals);\n            EventMgr.emit(LogicEvent.updateMyGenerals);\n        }\n    }\n\n    protected onGeneralPush(data: any): void {\n        console.log(\"onGeneralPush \", data);\n        if (data.code == 0) {\n            this._proxy.updateGeneral(data.msg);\n            EventMgr.emit(LogicEvent.updateGeneral);\n        }\n    }\n\n    protected onDrawGenerals(data: any): void {\n        console.log(\"onDrawGenerals\", data);\n        if (data.code == 0) {\n            this._proxy.updateMyGenerals(data.msg.generals);\n            EventMgr.emit(LogicEvent.updateMyGenerals);\n            EventMgr.emit(LogicEvent.openDrawResult, data.msg.generals);\n        }\n        EventMgr.emit(LogicEvent.hideWaiting);\n    }\n\n    protected onComposeGeneral(data:any):void{\n        console.log(\"onComposeGeneral \", data);\n        if (data.code == 0) {\n            this._proxy.updateMyGenerals(data.msg.generals);\n            EventMgr.emit(LogicEvent.updateMyGenerals);\n            EventMgr.emit(LogicEvent.updateOneGenerals, data.msg.generals[data.msg.generals.length - 1]);\n        }\n    }\n\n\n\n    protected onAddPrGeneral(data:any):void{\n        console.log(\"onAddPrGeneral \", data);\n        if (data.code == 0) {\n            this._proxy.updateGeneral(data.msg.general);\n            EventMgr.emit(LogicEvent.updateOneGenerals,data.msg.general);\n        }\n    }\n\n    protected onGeneralConvert(data:any):void{\n        console.log(\"onGeneralConvert \", data);\n        if (data.code == 0) {\n            this._proxy.removeMyGenerals(data.msg.gIds);\n            EventMgr.emit(LogicEvent.generalConvert, data.msg);\n        }\n    }\n\n    protected onUpSkill(data:any):void{\n        console.log(\"onUpSkill \", data);\n        \n    }\n\n    \n    protected onDownSkill(data:any):void{\n        console.log(\"onDownSkill \", data);\n       \n    }\n\n    protected onLvSkill(data:any):void{\n        console.log(\"onLvSkill \", data);\n    }\n    \n\n    /**我的角色属性*/\n    public updateMyProperty(datas: any[]): void {\n        this._proxy.updateMyGenerals(datas);\n        EventMgr.emit(LogicEvent.updateMyGenerals);\n    }\n\n    public qryMyGenerals(): void {\n        let sendData: any = {\n            name: ServerConfig.general_myGenerals,\n            msg: {\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    /**\n     * 抽卡\n     * @param drawTimes \n     */\n    public drawGenerals(drawTimes:number = 1): void {\n        let sendData: any = {\n            name: ServerConfig.general_drawGeneral,\n            msg: {\n                drawTimes:drawTimes\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n\n    /**\n     * \n     * @param compId \n     * @param gIds \n     */\n    public composeGeneral(compId:number = 1,gIds:number[] = []): void {\n        let sendData: any = {\n            name: ServerConfig.general_composeGeneral,\n            msg: {\n                compId:compId,\n                gIds:gIds\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n\n    public addPrGeneral(compId:number = 1,force_add:number,strategy_add:number,defense_add:number,speed_add:number,destroy_add:number): void {\n        let sendData: any = {\n            name: ServerConfig.general_addPrGeneral,\n            msg: {\n                compId:compId,\n                forceAdd:force_add,\n                strategyAdd:strategy_add,\n                defenseAdd:defense_add,\n                speedAdd:speed_add,\n                destroyAdd:destroy_add\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public convert(gIds:number[]): void {\n        let sendData: any = {\n            name: ServerConfig.general_convert,\n            msg: {\n                gIds:gIds\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public upSkill(gId:number, cfgId:number, pos:number): void {\n        let sendData: any = {\n            name: ServerConfig.general_upSkill,\n            msg: {\n                gId:gId,\n                cfgId:cfgId,\n                pos:Number(pos)\n            }\n        };\n\n        console.log(\"send upSkill:\", sendData);\n        NetManager.getInstance().send(sendData);\n    }\n\n    public downSkill(gId:number, cfgId:number, pos:number): void {\n        let sendData: any = {\n            name: ServerConfig.general_downSkill,\n            msg: {\n                gId:gId,\n                cfgId:cfgId,\n                pos:Number(pos)\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n \n    public lvSkill(gId:number, pos:number) {\n        let sendData: any = {\n            name: ServerConfig.general_lvSkill,\n            msg: {\n                gId:gId,\n                pos:Number(pos)\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n}\n"]}