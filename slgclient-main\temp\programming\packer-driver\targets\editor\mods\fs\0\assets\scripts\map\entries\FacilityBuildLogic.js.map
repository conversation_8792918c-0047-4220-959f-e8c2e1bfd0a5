{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts"], "names": ["_decorator", "Component", "Sprite", "Label", "SpriteAtlas", "DateUtil", "MapCommand", "MapResType", "MapUtil", "ccclass", "property", "FacilityBuildLogic", "onLoad", "_cmd", "getInstance", "onEnable", "nameLab", "string", "tipsLab", "spr", "spriteFrame", "onDisable", "_data", "unscheduleAllCallbacks", "setBuildData", "data", "updateUI", "type", "FORTRESS", "buildAtlas", "getSpriteFrame", "resData", "proxy", "getResData", "id", "resCfg", "getResConfig", "level", "nick<PERSON><PERSON>", "name", "isBuilding", "isUping", "isDestroying", "startCountDownTime", "console", "log", "stopCountDownTime", "schedule", "countDownTime", "leftTimeStr", "endTime", "areaPoint", "getAreaPointByCellPoint", "x", "y", "areaId", "getIdByAreaPoint", "areaData", "getMapAreaData", "qryNationMapScanBlock", "unschedule"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;;AAGxCC,MAAAA,Q;;AAEAC,MAAAA,U;;AACyCC,MAAAA,U,iBAAAA,U;;AACzCC,MAAAA,O;;;;;;;OAND;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;yBASTW,kB,WADpBF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACP,KAAD,C,UAERO,QAAQ,CAACP,KAAD,C,UAERO,QAAQ,CAACN,WAAD,C,oCARb,MACqBO,kBADrB,SACgDV,SADhD,CAC0D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAStB,IATsB;;AAAA,wCAUzB,IAVyB;AAAA;;AAW5CW,QAAAA,MAAM,GAAS;AACrB,eAAKC,IAAL,GAAa;AAAA;AAAA,wCAAWC,WAAX,EAAb;AACH;;AACSC,QAAAA,QAAQ,GAAQ;AACtB,eAAKC,OAAL,CAAaC,MAAb,GAAsB,EAAtB;AACA,eAAKC,OAAL,CAAaD,MAAb,GAAsB,EAAtB;AACA,eAAKE,GAAL,CAASC,WAAT,GAAuB,IAAvB;AAEH;;AACSC,QAAAA,SAAS,GAAS;AACxB,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKC,sBAAL;AACH;;AACOC,QAAAA,YAAY,CAACC,IAAD,EAA2B;AAC3C,eAAKH,KAAL,GAAaG,IAAb;AACA,eAAKC,QAAL;AACF;;AACKA,QAAAA,QAAQ,GAAS;AAEpB,cAAI,KAAKJ,KAAT,EAAgB;AAChB,gBAAI,KAAKA,KAAL,CAAWK,IAAX,IAAmB;AAAA;AAAA,0CAAWC,QAAlC,EAA2C;AAC3C,mBAAKT,GAAL,CAASC,WAAT,GAAuB,KAAKS,UAAL,CAAgBC,cAAhB,CAA+B,eAA/B,CAAvB;AAEA,kBAAIC,OAAmB,GAAG;AAAA;AAAA,4CAAWjB,WAAX,GAAyBkB,KAAzB,CAA+BC,UAA/B,CAA0C,KAAKX,KAAL,CAAWY,EAArD,CAA1B;AACA,kBAAIC,MAAoB,GAAG;AAAA;AAAA,4CAAWrB,WAAX,GAAyBkB,KAAzB,CAA+BI,YAA/B,CAA4CL,OAAO,CAACJ,IAApD,EAA0DI,OAAO,CAACM,KAAlE,CAA3B;;AAEA,kBAAI,KAAKf,KAAL,CAAWgB,QAAX,IAAuB,IAA3B,EAAgC;AAChC,qBAAKtB,OAAL,CAAaC,MAAb,GAAsB,KAAKK,KAAL,CAAWgB,QAAX,GAAsB,GAAtB,GAA4B,KAAKhB,KAAL,CAAWiB,IAA7D;AACC,eAFD,MAEK;AACL,qBAAKvB,OAAL,CAAaC,MAAb,GAAsBkB,MAAM,CAACI,IAA7B;AACC;;AAED,kBAAI,KAAKjB,KAAL,CAAWkB,UAAX,MAA2B,KAAKlB,KAAL,CAAWmB,OAAX,EAA3B,IAAmD,KAAKnB,KAAL,CAAWoB,YAAX,EAAvD,EAAiF;AACjF,qBAAKC,kBAAL;AACC,eAFD,MAGI;AACJ,qBAAKzB,OAAL,CAAaD,MAAb,GAAsB,EAAtB;AACC;AACA,aAlBD,MAkBK;AACL,mBAAKE,GAAL,CAASC,WAAT,GAAuB,IAAvB;AACA,mBAAKJ,OAAL,CAAaC,MAAb,GAAsB,EAAtB;AACC;AACA;AACJ;;AACM0B,QAAAA,kBAAkB,GAAE;AACvBC,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACA,eAAKC,iBAAL;AACA,eAAKC,QAAL,CAAc,KAAKC,aAAnB,EAAkC,GAAlC;AACA,eAAKA,aAAL;AACH;;AACMA,QAAAA,aAAa,GAAG;AACnB,cAAI,KAAK1B,KAAL,CAAWkB,UAAX,EAAJ,EAA4B;AAC5B,iBAAKtB,OAAL,CAAaD,MAAb,GAAsB,WAAW;AAAA;AAAA,sCAASgC,WAAT,CAAqB,KAAK3B,KAAL,CAAW4B,OAAhC,CAAjC;AACC,WAFD,MAEO,IAAG,KAAK5B,KAAL,CAAWmB,OAAX,EAAH,EAAwB;AAC/B,iBAAKvB,OAAL,CAAaD,MAAb,GAAsB,WAAW;AAAA;AAAA,sCAASgC,WAAT,CAAqB,KAAK3B,KAAL,CAAW4B,OAAhC,CAAjC;AACC,WAFM,MAEA,IAAG,KAAK5B,KAAL,CAAWoB,YAAX,EAAH,EAA6B;AACpC,iBAAKxB,OAAL,CAAaD,MAAb,GAAsB,WAAW;AAAA;AAAA,sCAASgC,WAAT,CAAqB,KAAK3B,KAAL,CAAW4B,OAAhC,CAAjC;AACC,WAFM,MAED;AACN,iBAAKhC,OAAL,CAAaD,MAAb,GAAsB,EAAtB;AACA,iBAAK6B,iBAAL;AACAF,YAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ;AAEA,gBAAIM,SAAe,GAAG;AAAA;AAAA,oCAAQC,uBAAR,CAAgC,KAAK9B,KAAL,CAAW+B,CAA3C,EAA8C,KAAK/B,KAAL,CAAWgC,CAAzD,CAAtB;AACA,gBAAIC,MAAc,GAAG;AAAA;AAAA,oCAAQC,gBAAR,CAAyBL,SAAS,CAACE,CAAnC,EAAsCF,SAAS,CAACG,CAAhD,CAArB;;AACA,gBAAIG,QAAqB,GAAG,KAAK5C,IAAL,CAAUmB,KAAV,CAAgB0B,cAAhB,CAA+BH,MAA/B,CAA5B;;AACA,iBAAK1C,IAAL,CAAU8C,qBAAV,CAAgCF,QAAhC;AACC;AAEJ;;AACMX,QAAAA,iBAAiB,GAAG;AACvB,eAAKc,UAAL,CAAgB,KAAKZ,aAArB;AACH;;AAlFqD,O;;;;;iBAEjC,I;;;;;;;iBAEG,I;;;;;;;iBAEA,I;;;;;;;iBAES,I", "sourcesContent": ["import { _decorator, Component, Sprite, Label, SpriteAtlas, Vec2 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport DateUtil from \"../../utils/DateUtil\";\nimport { MapBuildData } from \"../MapBuildProxy\";\nimport MapCommand from \"../MapCommand\";\nimport { MapAreaData, MapResConfig, MapResData, MapResType } from \"../MapProxy\";\nimport MapUtil from \"../MapUtil\";\n\n@ccclass('FacilityBuildLogic')\nexport default class FacilityBuildLogic extends Component {\n    @property(Sprite)\n    spr: Sprite | null = null;\n    @property(Label)\n    nameLab: Label | null = null;\n    @property(Label)\n    tipsLab: Label | null = null;\n    @property(SpriteAtlas)\n    buildAtlas: SpriteAtlas | null = null;\n    protected _data: MapBuildData = null;\n    protected _cmd: MapCommand = null;\n    protected onLoad(): void {\n        this._cmd =  MapCommand.getInstance();\n    }\n    protected onEnable():void {\n        this.nameLab.string = \"\";\n        this.tipsLab.string = \"\";\n        this.spr.spriteFrame = null;\n\n    }\n    protected onDisable(): void {\n        this._data = null;\n        this.unscheduleAllCallbacks();\n    }\n     public setBuildData(data: MapBuildData): void {\n        this._data = data;\n        this.updateUI();\n     }\n    public updateUI(): void {\n\n        if (this._data) {\n        if (this._data.type == MapResType.FORTRESS){\n        this.spr.spriteFrame = this.buildAtlas.getSpriteFrame(\"component_119\");\n\n        let resData: MapResData = MapCommand.getInstance().proxy.getResData(this._data.id);\n        let resCfg: MapResConfig = MapCommand.getInstance().proxy.getResConfig(resData.type, resData.level);\n\n        if (this._data.nickName != null){\n        this.nameLab.string = this._data.nickName + \":\" + this._data.name;\n        }else{\n        this.nameLab.string = resCfg.name;\n        }\n\n        if (this._data.isBuilding() || this._data.isUping() || this._data.isDestroying()){\n        this.startCountDownTime();\n        }\n        else{\n        this.tipsLab.string = \"\";\n        }\n        }else{\n        this.spr.spriteFrame = null;\n        this.nameLab.string = \"\";\n        }\n        }\n    }\n    public startCountDownTime(){\n        console.log(\"startCountDownTime\");\n        this.stopCountDownTime();\n        this.schedule(this.countDownTime, 1.0);\n        this.countDownTime();\n    }\n    public countDownTime() {\n        if (this._data.isBuilding()){\n        this.tipsLab.string = \"建设中...\" + DateUtil.leftTimeStr(this._data.endTime);\n        } else if(this._data.isUping()){\n        this.tipsLab.string = \"升级中...\" + DateUtil.leftTimeStr(this._data.endTime);\n        } else if(this._data.isDestroying()){\n        this.tipsLab.string = \"拆除中...\" + DateUtil.leftTimeStr(this._data.endTime);\n        } else{\n        this.tipsLab.string = \"\";\n        this.stopCountDownTime();\n        console.log(\"qryNationMapScanBlock\");\n\n        let areaPoint: Vec2 = MapUtil.getAreaPointByCellPoint(this._data.x, this._data.y);\n        let areaId: number = MapUtil.getIdByAreaPoint(areaPoint.x, areaPoint.y);\n        let areaData: MapAreaData = this._cmd.proxy.getMapAreaData(areaId);\n        this._cmd.qryNationMapScanBlock(areaData);\n        }\n\n    }\n    public stopCountDownTime() {\n        this.unschedule(this.countDownTime);\n    }\n}\n\n"]}