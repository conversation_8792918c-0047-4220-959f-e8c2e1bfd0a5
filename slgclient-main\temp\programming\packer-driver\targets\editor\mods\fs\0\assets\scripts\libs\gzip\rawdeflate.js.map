{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts"], "names": ["DeflateCT", "fc", "dl", "DeflateTreeDesc", "dyn_tree", "static_tree", "extra_bits", "extra_base", "elems", "max_length", "max_code", "DeflateConfiguration", "a", "b", "c", "d", "good_length", "max_lazy", "nice_length", "max_chain", "Def<PERSON><PERSON><PERSON><PERSON>", "next", "len", "ptr", "off", "deflate_start", "level", "i", "DEFAULT_LEVEL", "compr_level", "initflag", "eofile", "outbuf", "free_queue", "qhead", "qtail", "window", "d_buf", "l_buf", "prev", "dyn_ltree", "HEAP_SIZE", "dyn_dtree", "D_CODES", "static_ltree", "L_CODES", "static_dtree", "bl_tree", "BL_CODES", "l_desc", "d_desc", "bl_desc", "bl_count", "heap", "depth", "length_code", "dist_code", "base_length", "base_dist", "flag_buf", "deflate_end", "reuse_queue", "p", "new_queue", "head1", "WSIZE", "head2", "val", "put_byte", "outoff", "outcnt", "OUTBUFSIZ", "qoutbuf", "put_short", "w", "INSERT_STRING", "ins_h", "H_SHIFT", "strstart", "MIN_MATCH", "HASH_MASK", "hash_head", "WMASK", "SEND_CODE", "tree", "send_bits", "D_CODE", "dist", "SMALLER", "n", "m", "read_buff", "buff", "offset", "deflate_pos", "deflate_data", "length", "lm_init", "j", "HASH_SIZE", "max_lazy_match", "configuration_table", "good_match", "FULL_SEARCH", "nice_match", "max_chain_length", "block_start", "<PERSON><PERSON><PERSON>", "MIN_LOOKAHEAD", "fill_window", "longest_match", "cur_match", "chain_length", "scanp", "matchp", "best_len", "prev_length", "limit", "MAX_DIST", "NIL", "strendp", "MAX_MATCH", "scan_end1", "scan_end", "broke", "match_start", "more", "window_size", "deflate_fast", "flush", "match_length", "ct_tally", "flush_block", "deflate_better", "prev_match", "TOO_FAR", "match_available", "init_deflate", "bi_buf", "bi_valid", "ct_init", "complete", "deflate_internal", "buff_size", "qcopy", "bits", "code", "extra_lbits", "LITERALS", "MAX_BITS", "extra_dbits", "extra_blbits", "MAX_BL_BITS", "LENGTH_CODES", "gen_codes", "bi_reverse", "init_block", "END_BLOCK", "opt_len", "static_len", "last_lit", "last_dist", "last_flags", "flags", "flag_bit", "pqdownheap", "k", "v", "heap_len", "gen_bitlen", "desc", "extra", "base", "stree", "h", "xbits", "f", "overflow", "heap_max", "next_code", "build_tree", "node", "xnew", "SMALLEST", "scan_tree", "prevlen", "curlen", "nextlen", "count", "max_count", "min_count", "REP_3_6", "REPZ_3_10", "REPZ_11_138", "send_tree", "build_bl_tree", "max_blindex", "bl_order", "send_all_trees", "lcodes", "dcodes", "blcodes", "rank", "eof", "opt_lenb", "static_lenb", "stored_len", "STORED_BLOCK", "bi_windup", "STATIC_TREES", "compress_block", "DYN_TREES", "lc", "out_length", "in_length", "dcode", "parseInt", "LIT_BUFSIZE", "DIST_BUFSIZE", "ltree", "dtree", "lx", "dx", "fx", "flag", "value", "Buf_size", "res", "q", "deflate", "arr", "INBUFSIZ", "BITS", "HASH_BITS", "console", "error"], "mappings": ";;;;;AAyIA;AAEA,WAASA,SAAT,GAAqB;AACpB,SAAKC,EAAL,GAAU,CAAV,CADoB,CACP;;AACb,SAAKC,EAAL,GAAU,CAAV,CAFoB,CAEP;AACb;;AAED,WAASC,eAAT,GAA2B;AAC1B,SAAKC,QAAL,GAAgB,IAAhB,CAD0B,CACJ;;AACtB,SAAKC,WAAL,GAAmB,IAAnB,CAF0B,CAED;;AACzB,SAAKC,UAAL,GAAkB,IAAlB,CAH0B,CAGF;;AACxB,SAAKC,UAAL,GAAkB,CAAlB,CAJ0B,CAIL;;AACrB,SAAKC,KAAL,GAAa,CAAb,CAL0B,CAKV;;AAChB,SAAKC,UAAL,GAAkB,CAAlB,CAN0B,CAML;;AACrB,SAAKC,QAAL,GAAgB,CAAhB,CAP0B,CAOP;AACnB;AAED;AACA;AACA;AACA;AACA;;;AACA,WAASC,oBAAT,CAA8BC,CAA9B,EAAiCC,CAAjC,EAAoCC,CAApC,EAAuCC,CAAvC,EAA0C;AACzC,SAAKC,WAAL,GAAmBJ,CAAnB,CADyC,CACnB;;AACtB,SAAKK,QAAL,GAAgBJ,CAAhB,CAFyC,CAEtB;;AACnB,SAAKK,WAAL,GAAmBJ,CAAnB,CAHyC,CAGnB;;AACtB,SAAKK,SAAL,GAAiBJ,CAAjB;AACA;;AAED,WAASK,aAAT,GAAyB;AACxB,SAAKC,IAAL,GAAY,IAAZ;AACA,SAAKC,GAAL,GAAW,CAAX;AACA,SAAKC,GAAL,GAAW,EAAX,CAHwB,CAGT;;AACf,SAAKC,GAAL,GAAW,CAAX;AACA;AAED;;;AAmBA;AAEA,WAASC,aAAT,CAAuBC,KAAvB,EAA8B;AAC7B,QAAIC,CAAJ;;AAEA,QAAI,CAACD,KAAL,EAAY;AACXA,MAAAA,KAAK,GAAGE,aAAR;AACA,KAFD,MAEO,IAAIF,KAAK,GAAG,CAAZ,EAAe;AACrBA,MAAAA,KAAK,GAAG,CAAR;AACA,KAFM,MAEA,IAAIA,KAAK,GAAG,CAAZ,EAAe;AACrBA,MAAAA,KAAK,GAAG,CAAR;AACA;;AAEDG,IAAAA,WAAW,GAAGH,KAAd;AACAI,IAAAA,QAAQ,GAAG,KAAX;AACAC,IAAAA,MAAM,GAAG,KAAT;;AACA,QAAIC,MAAM,KAAK,IAAf,EAAqB;AACpB;AACA;;AAEDC,IAAAA,UAAU,GAAGC,KAAK,GAAGC,KAAK,GAAG,IAA7B;AACAH,IAAAA,MAAM,GAAG,EAAT,CAnB6B,CAmBhB;;AACbI,IAAAA,MAAM,GAAG,EAAT,CApB6B,CAoBhB;;AACbC,IAAAA,KAAK,GAAG,EAAR,CArB6B,CAqBjB;;AACZC,IAAAA,KAAK,GAAG,EAAR,CAtB6B,CAsBjB;;AACZC,IAAAA,IAAI,GAAG,EAAP,CAvB6B,CAuBlB;;AAEXC,IAAAA,SAAS,GAAG,EAAZ;;AACA,SAAKb,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGc,SAAhB,EAA2Bd,CAAC,EAA5B,EAAgC;AAC/Ba,MAAAA,SAAS,CAACb,CAAD,CAAT,GAAe,IAAI3B,SAAJ,EAAf;AACA;;AACD0C,IAAAA,SAAS,GAAG,EAAZ;;AACA,SAAKf,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,IAAIgB,OAAJ,GAAc,CAA9B,EAAiChB,CAAC,EAAlC,EAAsC;AACrCe,MAAAA,SAAS,CAACf,CAAD,CAAT,GAAe,IAAI3B,SAAJ,EAAf;AACA;;AACD4C,IAAAA,YAAY,GAAG,EAAf;;AACA,SAAKjB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGkB,OAAO,GAAG,CAA1B,EAA6BlB,CAAC,EAA9B,EAAkC;AACjCiB,MAAAA,YAAY,CAACjB,CAAD,CAAZ,GAAkB,IAAI3B,SAAJ,EAAlB;AACA;;AACD8C,IAAAA,YAAY,GAAG,EAAf;;AACA,SAAKnB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGgB,OAAhB,EAAyBhB,CAAC,EAA1B,EAA8B;AAC7BmB,MAAAA,YAAY,CAACnB,CAAD,CAAZ,GAAkB,IAAI3B,SAAJ,EAAlB;AACA;;AACD+C,IAAAA,OAAO,GAAG,EAAV;;AACA,SAAKpB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,IAAIqB,QAAJ,GAAe,CAA/B,EAAkCrB,CAAC,EAAnC,EAAuC;AACtCoB,MAAAA,OAAO,CAACpB,CAAD,CAAP,GAAa,IAAI3B,SAAJ,EAAb;AACA;;AACDiD,IAAAA,MAAM,GAAG,IAAI9C,eAAJ,EAAT;AACA+C,IAAAA,MAAM,GAAG,IAAI/C,eAAJ,EAAT;AACAgD,IAAAA,OAAO,GAAG,IAAIhD,eAAJ,EAAV;AACAiD,IAAAA,QAAQ,GAAG,EAAX,CAhD6B,CAgDd;;AACfC,IAAAA,IAAI,GAAG,EAAP,CAjD6B,CAiDlB;;AACXC,IAAAA,KAAK,GAAG,EAAR,CAlD6B,CAkDjB;;AACZC,IAAAA,WAAW,GAAG,EAAd,CAnD6B,CAmDX;;AAClBC,IAAAA,SAAS,GAAG,EAAZ,CApD6B,CAoDb;;AAChBC,IAAAA,WAAW,GAAG,EAAd,CArD6B,CAqDX;;AAClBC,IAAAA,SAAS,GAAG,EAAZ,CAtD6B,CAsDb;;AAChBC,IAAAA,QAAQ,GAAG,EAAX,CAvD6B,CAuDd;AACf;;AAED,WAASC,WAAT,GAAuB;AACtB3B,IAAAA,UAAU,GAAGC,KAAK,GAAGC,KAAK,GAAG,IAA7B;AACAH,IAAAA,MAAM,GAAG,IAAT;AACAI,IAAAA,MAAM,GAAG,IAAT;AACAC,IAAAA,KAAK,GAAG,IAAR;AACAC,IAAAA,KAAK,GAAG,IAAR;AACAC,IAAAA,IAAI,GAAG,IAAP;AACAC,IAAAA,SAAS,GAAG,IAAZ;AACAE,IAAAA,SAAS,GAAG,IAAZ;AACAE,IAAAA,YAAY,GAAG,IAAf;AACAE,IAAAA,YAAY,GAAG,IAAf;AACAC,IAAAA,OAAO,GAAG,IAAV;AACAE,IAAAA,MAAM,GAAG,IAAT;AACAC,IAAAA,MAAM,GAAG,IAAT;AACAC,IAAAA,OAAO,GAAG,IAAV;AACAC,IAAAA,QAAQ,GAAG,IAAX;AACAC,IAAAA,IAAI,GAAG,IAAP;AACAC,IAAAA,KAAK,GAAG,IAAR;AACAC,IAAAA,WAAW,GAAG,IAAd;AACAC,IAAAA,SAAS,GAAG,IAAZ;AACAC,IAAAA,WAAW,GAAG,IAAd;AACAC,IAAAA,SAAS,GAAG,IAAZ;AACAC,IAAAA,QAAQ,GAAG,IAAX;AACA;;AAED,WAASE,WAAT,CAAqBC,CAArB,EAAwB;AACvBA,IAAAA,CAAC,CAACzC,IAAF,GAASY,UAAT;AACAA,IAAAA,UAAU,GAAG6B,CAAb;AACA;;AAED,WAASC,SAAT,GAAqB;AACpB,QAAID,CAAJ;;AAEA,QAAI7B,UAAU,KAAK,IAAnB,EAAyB;AACxB6B,MAAAA,CAAC,GAAG7B,UAAJ;AACAA,MAAAA,UAAU,GAAGA,UAAU,CAACZ,IAAxB;AACA,KAHD,MAGO;AACNyC,MAAAA,CAAC,GAAG,IAAI1C,aAAJ,EAAJ;AACA;;AACD0C,IAAAA,CAAC,CAACzC,IAAF,GAAS,IAAT;AACAyC,IAAAA,CAAC,CAACxC,GAAF,GAAQwC,CAAC,CAACtC,GAAF,GAAQ,CAAhB;AAEA,WAAOsC,CAAP;AACA;;AAED,WAASE,KAAT,CAAerC,CAAf,EAAkB;AACjB,WAAOY,IAAI,CAAC0B,KAAK,GAAGtC,CAAT,CAAX;AACA;;AAED,WAASuC,KAAT,CAAevC,CAAf,EAAkBwC,GAAlB,EAAuB;AACtB,WAAQ5B,IAAI,CAAC0B,KAAK,GAAGtC,CAAT,CAAJ,GAAkBwC,GAA1B;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,WAASC,QAAT,CAAkBtD,CAAlB,EAAqB;AACpBkB,IAAAA,MAAM,CAACqC,MAAM,GAAGC,MAAM,EAAhB,CAAN,GAA4BxD,CAA5B;;AACA,QAAIuD,MAAM,GAAGC,MAAT,KAAoBC,SAAxB,EAAmC;AAClCC,MAAAA,OAAO;AACP;AACD;AAED;;;AACA,WAASC,SAAT,CAAmBC,CAAnB,EAAsB;AACrBA,IAAAA,CAAC,IAAI,MAAL;;AACA,QAAIL,MAAM,GAAGC,MAAT,GAAkBC,SAAS,GAAG,CAAlC,EAAqC;AACpCvC,MAAAA,MAAM,CAACqC,MAAM,GAAGC,MAAM,EAAhB,CAAN,GAA6BI,CAAC,GAAG,IAAjC;AACA1C,MAAAA,MAAM,CAACqC,MAAM,GAAGC,MAAM,EAAhB,CAAN,GAA6BI,CAAC,KAAK,CAAnC;AACA,KAHD,MAGO;AACNN,MAAAA,QAAQ,CAACM,CAAC,GAAG,IAAL,CAAR;AACAN,MAAAA,QAAQ,CAACM,CAAC,KAAK,CAAP,CAAR;AACA;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASC,aAAT,GAAyB;AACxBC,IAAAA,KAAK,GAAG,CAAEA,KAAK,IAAIC,OAAV,GAAsBzC,MAAM,CAAC0C,QAAQ,GAAGC,SAAX,GAAuB,CAAxB,CAAN,GAAmC,IAA1D,IAAmEC,SAA3E;AACAC,IAAAA,SAAS,GAAGjB,KAAK,CAACY,KAAD,CAAjB;AACArC,IAAAA,IAAI,CAACuC,QAAQ,GAAGI,KAAZ,CAAJ,GAAyBD,SAAzB;AACAf,IAAAA,KAAK,CAACU,KAAD,EAAQE,QAAR,CAAL;AACA;AAED;;;AACA,WAASK,SAAT,CAAmBrE,CAAnB,EAAsBsE,IAAtB,EAA4B;AAC3BC,IAAAA,SAAS,CAACD,IAAI,CAACtE,CAAD,CAAJ,CAAQb,EAAT,EAAamF,IAAI,CAACtE,CAAD,CAAJ,CAAQZ,EAArB,CAAT;AACA;AAED;AACA;AACA;AACA;;;AACA,WAASoF,MAAT,CAAgBC,IAAhB,EAAsB;AACrB,WAAO,CAACA,IAAI,GAAG,GAAP,GAAa/B,SAAS,CAAC+B,IAAD,CAAtB,GAA+B/B,SAAS,CAAC,OAAO+B,IAAI,IAAI,CAAf,CAAD,CAAzC,IAAgE,IAAvE;AACA;AAED;AACA;AACA;AACA;;;AACA,WAASC,OAAT,CAAiBJ,IAAjB,EAAuBK,CAAvB,EAA0BC,CAA1B,EAA6B;AAC5B,WAAON,IAAI,CAACK,CAAD,CAAJ,CAAQxF,EAAR,GAAamF,IAAI,CAACM,CAAD,CAAJ,CAAQzF,EAArB,IAA4BmF,IAAI,CAACK,CAAD,CAAJ,CAAQxF,EAAR,KAAemF,IAAI,CAACM,CAAD,CAAJ,CAAQzF,EAAvB,IAA6BqD,KAAK,CAACmC,CAAD,CAAL,IAAYnC,KAAK,CAACoC,CAAD,CAAjF;AACA;AAED;AACA;AACA;;;AACA,WAASC,SAAT,CAAmBC,IAAnB,EAAyBC,MAAzB,EAAiCJ,CAAjC,EAAoC;AACnC,QAAI9D,CAAJ;;AACA,SAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG8D,CAAJ,IAASK,WAAW,GAAGC,YAAY,CAACC,MAAhD,EAAwDrE,CAAC,EAAzD,EAA6D;AAC5DiE,MAAAA,IAAI,CAACC,MAAM,GAAGlE,CAAV,CAAJ,GAAmBoE,YAAY,CAACD,WAAW,EAAZ,CAAZ,GAA8B,IAAjD;AACA;;AACD,WAAOnE,CAAP;AACA;AAED;AACA;AACA;;;AACA,WAASsE,OAAT,GAAmB;AAClB,QAAIC,CAAJ,CADkB,CAGlB;;AACA,SAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGC,SAAhB,EAA2BD,CAAC,EAA5B,EAAgC;AAC/B;AACA3D,MAAAA,IAAI,CAAC0B,KAAK,GAAGiC,CAAT,CAAJ,GAAkB,CAAlB;AACA,KAPiB,CAQlB;AAEA;;;AACAE,IAAAA,cAAc,GAAGC,mBAAmB,CAACxE,WAAD,CAAnB,CAAiCZ,QAAlD;AACAqF,IAAAA,UAAU,GAAGD,mBAAmB,CAACxE,WAAD,CAAnB,CAAiCb,WAA9C;;AACA,QAAI,CAACuF,WAAL,EAAkB;AACjBC,MAAAA,UAAU,GAAGH,mBAAmB,CAACxE,WAAD,CAAnB,CAAiCX,WAA9C;AACA;;AACDuF,IAAAA,gBAAgB,GAAGJ,mBAAmB,CAACxE,WAAD,CAAnB,CAAiCV,SAApD;AAEA2D,IAAAA,QAAQ,GAAG,CAAX;AACA4B,IAAAA,WAAW,GAAG,CAAd;AAEAC,IAAAA,SAAS,GAAGhB,SAAS,CAACvD,MAAD,EAAS,CAAT,EAAY,IAAI6B,KAAhB,CAArB;;AACA,QAAI0C,SAAS,IAAI,CAAjB,EAAoB;AACnB5E,MAAAA,MAAM,GAAG,IAAT;AACA4E,MAAAA,SAAS,GAAG,CAAZ;AACA;AACA;;AACD5E,IAAAA,MAAM,GAAG,KAAT,CA3BkB,CA4BlB;AACA;;AACA,WAAO4E,SAAS,GAAGC,aAAZ,IAA6B,CAAC7E,MAArC,EAA6C;AAC5C8E,MAAAA,WAAW;AACX,KAhCiB,CAkClB;AACA;;;AACAjC,IAAAA,KAAK,GAAG,CAAR;;AACA,SAAKsB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGnB,SAAS,GAAG,CAA5B,EAA+BmB,CAAC,EAAhC,EAAoC;AACnC;AACAtB,MAAAA,KAAK,GAAG,CAAEA,KAAK,IAAIC,OAAV,GAAsBzC,MAAM,CAAC8D,CAAD,CAAN,GAAY,IAAnC,IAA4ClB,SAApD;AACA;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAAS8B,aAAT,CAAuBC,SAAvB,EAAkC;AACjC,QAAIC,YAAY,GAAGP,gBAAnB,CADiC,CACI;;AACrC,QAAIQ,KAAK,GAAGnC,QAAZ,CAFiC,CAEX;;AACtB,QAAIoC,MAAJ,CAHiC,CAGrB;;AACZ,QAAI5F,GAAJ,CAJiC,CAIxB;;AACT,QAAI6F,QAAQ,GAAGC,WAAf,CALiC,CAKL;AAE5B;AACA;;AACA,QAAIC,KAAK,GAAIvC,QAAQ,GAAGwC,QAAX,GAAsBxC,QAAQ,GAAGwC,QAAjC,GAA4CC,GAAzD;AAEA,QAAIC,OAAO,GAAG1C,QAAQ,GAAG2C,SAAzB;AACA,QAAIC,SAAS,GAAGtF,MAAM,CAAC6E,KAAK,GAAGE,QAAR,GAAmB,CAApB,CAAtB;AACA,QAAIQ,QAAQ,GAAGvF,MAAM,CAAC6E,KAAK,GAAGE,QAAT,CAArB;AAEA,QAAIxF,CAAJ,EAAOiG,KAAP,CAfiC,CAiBjC;;AACA,QAAIR,WAAW,IAAId,UAAnB,EAA+B;AAC9BU,MAAAA,YAAY,KAAK,CAAjB;AACA,KApBgC,CAsBjC;;;AAEA,OAAG;AACF;AACAE,MAAAA,MAAM,GAAGH,SAAT,CAFE,CAIF;AACA;;AACA,UAAI3E,MAAM,CAAC8E,MAAM,GAAGC,QAAV,CAAN,KAA8BQ,QAA9B,IACFvF,MAAM,CAAC8E,MAAM,GAAGC,QAAT,GAAoB,CAArB,CAAN,KAAkCO,SADhC,IAEFtF,MAAM,CAAC8E,MAAD,CAAN,KAAmB9E,MAAM,CAAC6E,KAAD,CAFvB,IAGF7E,MAAM,CAAC,EAAE8E,MAAH,CAAN,KAAqB9E,MAAM,CAAC6E,KAAK,GAAG,CAAT,CAH7B,EAG0C;AACzC;AACA,OAXC,CAaF;AACA;AACA;AACA;AACA;;;AACAA,MAAAA,KAAK,IAAI,CAAT;AACAC,MAAAA,MAAM,GAnBJ,CAqBF;AACA;;AACA,aAAOD,KAAK,GAAGO,OAAf,EAAwB;AACvBI,QAAAA,KAAK,GAAG,KAAR;;AACA,aAAKjG,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,CAAhB,EAAmBA,CAAC,IAAI,CAAxB,EAA2B;AAC1BsF,UAAAA,KAAK,IAAI,CAAT;AACAC,UAAAA,MAAM,IAAI,CAAV;;AACA,cAAI9E,MAAM,CAAC6E,KAAD,CAAN,KAAkB7E,MAAM,CAAC8E,MAAD,CAA5B,EAAsC;AACrCU,YAAAA,KAAK,GAAG,IAAR;AACA;AACA;AACD;;AAED,YAAIA,KAAJ,EAAW;AACV;AACA;AACD;;AAEDtG,MAAAA,GAAG,GAAGmG,SAAS,IAAID,OAAO,GAAGP,KAAd,CAAf;AACAA,MAAAA,KAAK,GAAGO,OAAO,GAAGC,SAAlB;;AAEA,UAAInG,GAAG,GAAG6F,QAAV,EAAoB;AACnBU,QAAAA,WAAW,GAAGd,SAAd;AACAI,QAAAA,QAAQ,GAAG7F,GAAX;;AACA,YAAIiF,WAAJ,EAAiB;AAChB,cAAIjF,GAAG,IAAImG,SAAX,EAAsB;AACrB;AACA;AACD,SAJD,MAIO;AACN,cAAInG,GAAG,IAAIkF,UAAX,EAAuB;AACtB;AACA;AACD;;AAEDkB,QAAAA,SAAS,GAAGtF,MAAM,CAAC6E,KAAK,GAAGE,QAAR,GAAmB,CAApB,CAAlB;AACAQ,QAAAA,QAAQ,GAAGvF,MAAM,CAAC6E,KAAK,GAAGE,QAAT,CAAjB;AACA;AACD,KA1DD,QA0DS,CAACJ,SAAS,GAAGxE,IAAI,CAACwE,SAAS,GAAG7B,KAAb,CAAjB,IAAwCmC,KAAxC,IAAiD,EAAEL,YAAF,KAAmB,CA1D7E;;AA4DA,WAAOG,QAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASN,WAAT,GAAuB;AACtB,QAAIpB,CAAJ,EAAOC,CAAP,CADsB,CAGtB;;AACA,QAAIoC,IAAI,GAAGC,WAAW,GAAGpB,SAAd,GAA0B7B,QAArC,CAJsB,CAMtB;AACA;;AACA,QAAIgD,IAAI,KAAK,CAAC,CAAd,EAAiB;AAChB;AACA;AACAA,MAAAA,IAAI;AACJ,KAJD,MAIO,IAAIhD,QAAQ,IAAIb,KAAK,GAAGqD,QAAxB,EAAkC;AACxC;AACA;AACA;AAEA;AACA,WAAK7B,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGxB,KAAhB,EAAuBwB,CAAC,EAAxB,EAA4B;AAC3BrD,QAAAA,MAAM,CAACqD,CAAD,CAAN,GAAYrD,MAAM,CAACqD,CAAC,GAAGxB,KAAL,CAAlB;AACA;;AAED4D,MAAAA,WAAW,IAAI5D,KAAf;AACAa,MAAAA,QAAQ,IAAOb,KAAf;AAAsB;;AACtByC,MAAAA,WAAW,IAAIzC,KAAf;;AAEA,WAAKwB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGU,SAAhB,EAA2BV,CAAC,EAA5B,EAAgC;AAC/BC,QAAAA,CAAC,GAAG1B,KAAK,CAACyB,CAAD,CAAT;AACAvB,QAAAA,KAAK,CAACuB,CAAD,EAAIC,CAAC,IAAIzB,KAAL,GAAayB,CAAC,GAAGzB,KAAjB,GAAyBsD,GAA7B,CAAL;AACA;;AACD,WAAK9B,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGxB,KAAhB,EAAuBwB,CAAC,EAAxB,EAA4B;AAC5B;AACA;AACCC,QAAAA,CAAC,GAAGnD,IAAI,CAACkD,CAAD,CAAR;AACAlD,QAAAA,IAAI,CAACkD,CAAD,CAAJ,GAAWC,CAAC,IAAIzB,KAAL,GAAayB,CAAC,GAAGzB,KAAjB,GAAyBsD,GAApC;AACA;;AACDO,MAAAA,IAAI,IAAI7D,KAAR;AACA,KArCqB,CAsCtB;;;AACA,QAAI,CAAClC,MAAL,EAAa;AACZ0D,MAAAA,CAAC,GAAGE,SAAS,CAACvD,MAAD,EAAS0C,QAAQ,GAAG6B,SAApB,EAA+BmB,IAA/B,CAAb;;AACA,UAAIrC,CAAC,IAAI,CAAT,EAAY;AACX1D,QAAAA,MAAM,GAAG,IAAT;AACA,OAFD,MAEO;AACN4E,QAAAA,SAAS,IAAIlB,CAAb;AACA;AACD;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASuC,YAAT,GAAwB;AACvB,WAAOrB,SAAS,KAAK,CAAd,IAAmBzE,KAAK,KAAK,IAApC,EAA0C;AACzC,UAAI+F,KAAJ,CADyC,CAC9B;AAEX;AACA;;AACAtD,MAAAA,aAAa,GAL4B,CAOzC;AACA;;AACA,UAAIM,SAAS,KAAKsC,GAAd,IAAqBzC,QAAQ,GAAGG,SAAX,IAAwBqC,QAAjD,EAA2D;AAC1D;AACA;AACA;AACAY,QAAAA,YAAY,GAAGpB,aAAa,CAAC7B,SAAD,CAA5B,CAJ0D,CAK1D;;AACA,YAAIiD,YAAY,GAAGvB,SAAnB,EAA8B;AAC7BuB,UAAAA,YAAY,GAAGvB,SAAf;AACA;AACD;;AACD,UAAIuB,YAAY,IAAInD,SAApB,EAA+B;AAC9B;AAEAkD,QAAAA,KAAK,GAAGE,QAAQ,CAACrD,QAAQ,GAAG+C,WAAZ,EAAyBK,YAAY,GAAGnD,SAAxC,CAAhB;AACA4B,QAAAA,SAAS,IAAIuB,YAAb,CAJ8B,CAM9B;AACA;;AACA,YAAIA,YAAY,IAAI9B,cAApB,EAAoC;AACnC8B,UAAAA,YAAY,GADuB,CACnB;;AAChB,aAAG;AACFpD,YAAAA,QAAQ;AACRH,YAAAA,aAAa,GAFX,CAGF;AACA;AACA;AACA;AACA,WAPD,QAOS,EAAEuD,YAAF,KAAmB,CAP5B;;AAQApD,UAAAA,QAAQ;AACR,SAXD,MAWO;AACNA,UAAAA,QAAQ,IAAIoD,YAAZ;AACAA,UAAAA,YAAY,GAAG,CAAf;AACAtD,UAAAA,KAAK,GAAGxC,MAAM,CAAC0C,QAAD,CAAN,GAAmB,IAA3B,CAHM,CAIN;;AACAF,UAAAA,KAAK,GAAG,CAAEA,KAAK,IAAIC,OAAV,GAAsBzC,MAAM,CAAC0C,QAAQ,GAAG,CAAZ,CAAN,GAAuB,IAA9C,IAAuDE,SAA/D,CALM,CAOP;AACA;AACA;AAEC;AACD,OA/BD,MA+BO;AACN;AACAiD,QAAAA,KAAK,GAAGE,QAAQ,CAAC,CAAD,EAAI/F,MAAM,CAAC0C,QAAD,CAAN,GAAmB,IAAvB,CAAhB;AACA6B,QAAAA,SAAS;AACT7B,QAAAA,QAAQ;AACR;;AACD,UAAImD,KAAJ,EAAW;AACVG,QAAAA,WAAW,CAAC,CAAD,CAAX;AACA1B,QAAAA,WAAW,GAAG5B,QAAd;AACA,OA3DwC,CA6DzC;AACA;AACA;AACA;;;AACA,aAAO6B,SAAS,GAAGC,aAAZ,IAA6B,CAAC7E,MAArC,EAA6C;AAC5C8E,QAAAA,WAAW;AACX;AACD;AACD;;AAED,WAASwB,cAAT,GAA0B;AACzB;AACA,WAAO1B,SAAS,KAAK,CAAd,IAAmBzE,KAAK,KAAK,IAApC,EAA0C;AACzC;AACA;AACAyC,MAAAA,aAAa,GAH4B,CAKzC;;AACAyC,MAAAA,WAAW,GAAGc,YAAd;AACAI,MAAAA,UAAU,GAAGT,WAAb;AACAK,MAAAA,YAAY,GAAGnD,SAAS,GAAG,CAA3B;;AAEA,UAAIE,SAAS,KAAKsC,GAAd,IAAqBH,WAAW,GAAGhB,cAAnC,IAAqDtB,QAAQ,GAAGG,SAAX,IAAwBqC,QAAjF,EAA2F;AAC1F;AACA;AACA;AACAY,QAAAA,YAAY,GAAGpB,aAAa,CAAC7B,SAAD,CAA5B,CAJ0F,CAK1F;;AACA,YAAIiD,YAAY,GAAGvB,SAAnB,EAA8B;AAC7BuB,UAAAA,YAAY,GAAGvB,SAAf;AACA,SARyF,CAU1F;;;AACA,YAAIuB,YAAY,KAAKnD,SAAjB,IAA8BD,QAAQ,GAAG+C,WAAX,GAAyBU,OAA3D,EAAoE;AACnE;AACA;AACAL,UAAAA,YAAY;AACZ;AACD,OA1BwC,CA2BzC;AACA;;;AACA,UAAId,WAAW,IAAIrC,SAAf,IAA4BmD,YAAY,IAAId,WAAhD,EAA6D;AAC5D,YAAIa,KAAJ,CAD4D,CACjD;AAEX;;AACAA,QAAAA,KAAK,GAAGE,QAAQ,CAACrD,QAAQ,GAAG,CAAX,GAAewD,UAAhB,EAA4BlB,WAAW,GAAGrC,SAA1C,CAAhB,CAJ4D,CAM5D;AACA;;AACA4B,QAAAA,SAAS,IAAIS,WAAW,GAAG,CAA3B;AACAA,QAAAA,WAAW,IAAI,CAAf;;AACA,WAAG;AACFtC,UAAAA,QAAQ;AACRH,UAAAA,aAAa,GAFX,CAGF;AACA;AACA;AACA;AACA,SAPD,QAOS,EAAEyC,WAAF,KAAkB,CAP3B;;AAQAoB,QAAAA,eAAe,GAAG,KAAlB;AACAN,QAAAA,YAAY,GAAGnD,SAAS,GAAG,CAA3B;AACAD,QAAAA,QAAQ;;AACR,YAAImD,KAAJ,EAAW;AACVG,UAAAA,WAAW,CAAC,CAAD,CAAX;AACA1B,UAAAA,WAAW,GAAG5B,QAAd;AACA;AACD,OAzBD,MAyBO,IAAI0D,eAAJ,EAAqB;AAC3B;AACA;AACA;AACA,YAAIL,QAAQ,CAAC,CAAD,EAAI/F,MAAM,CAAC0C,QAAQ,GAAG,CAAZ,CAAN,GAAuB,IAA3B,CAAZ,EAA8C;AAC7CsD,UAAAA,WAAW,CAAC,CAAD,CAAX;AACA1B,UAAAA,WAAW,GAAG5B,QAAd;AACA;;AACDA,QAAAA,QAAQ;AACR6B,QAAAA,SAAS;AACT,OAVM,MAUA;AACN;AACA;AACA6B,QAAAA,eAAe,GAAG,IAAlB;AACA1D,QAAAA,QAAQ;AACR6B,QAAAA,SAAS;AACT,OAtEwC,CAwEzC;AACA;AACA;AACA;;;AACA,aAAOA,SAAS,GAAGC,aAAZ,IAA6B,CAAC7E,MAArC,EAA6C;AAC5C8E,QAAAA,WAAW;AACX;AACD;AACD;;AAED,WAAS4B,YAAT,GAAwB;AACvB,QAAI1G,MAAJ,EAAY;AACX;AACA;;AACD2G,IAAAA,MAAM,GAAG,CAAT;AACAC,IAAAA,QAAQ,GAAG,CAAX;AACAC,IAAAA,OAAO;AACP3C,IAAAA,OAAO;AAEP/D,IAAAA,KAAK,GAAG,IAAR;AACAoC,IAAAA,MAAM,GAAG,CAAT;AACAD,IAAAA,MAAM,GAAG,CAAT;;AAEA,QAAIxC,WAAW,IAAI,CAAnB,EAAsB;AACrBuF,MAAAA,WAAW,GAAGrC,SAAS,GAAG,CAA1B;AACAmD,MAAAA,YAAY,GAAG,CAAf;AACA,KAHD,MAGO;AACNA,MAAAA,YAAY,GAAGnD,SAAS,GAAG,CAA3B;AACAyD,MAAAA,eAAe,GAAG,KAAlB;AACA;;AAEDK,IAAAA,QAAQ,GAAG,KAAX;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,WAASC,gBAAT,CAA0BlD,IAA1B,EAAgCpE,GAAhC,EAAqCuH,SAArC,EAAgD;AAC/C,QAAItD,CAAJ;;AAEA,QAAI,CAAC3D,QAAL,EAAe;AACd2G,MAAAA,YAAY;AACZ3G,MAAAA,QAAQ,GAAG,IAAX;;AACA,UAAI6E,SAAS,KAAK,CAAlB,EAAqB;AAAE;AACtBkC,QAAAA,QAAQ,GAAG,IAAX;AACA,eAAO,CAAP;AACA;AACD;;AAEDpD,IAAAA,CAAC,GAAGuD,KAAK,CAACpD,IAAD,EAAOpE,GAAP,EAAYuH,SAAZ,CAAT;;AACA,QAAItD,CAAC,KAAKsD,SAAV,EAAqB;AACpB,aAAOA,SAAP;AACA;;AAED,QAAIF,QAAJ,EAAc;AACb,aAAOpD,CAAP;AACA;;AAED,QAAI5D,WAAW,IAAI,CAAnB,EAAsB;AACrB;AACAmG,MAAAA,YAAY;AACZ,KAHD,MAGO;AACNK,MAAAA,cAAc;AACd;;AAED,QAAI1B,SAAS,KAAK,CAAlB,EAAqB;AACpB,UAAI6B,eAAJ,EAAqB;AACpBL,QAAAA,QAAQ,CAAC,CAAD,EAAI/F,MAAM,CAAC0C,QAAQ,GAAG,CAAZ,CAAN,GAAuB,IAA3B,CAAR;AACA;;AACDsD,MAAAA,WAAW,CAAC,CAAD,CAAX;AACAS,MAAAA,QAAQ,GAAG,IAAX;AACA;;AAED,WAAOpD,CAAC,GAAGuD,KAAK,CAACpD,IAAD,EAAOH,CAAC,GAAGjE,GAAX,EAAgBuH,SAAS,GAAGtD,CAA5B,CAAhB;AACA;;AAED,WAASuD,KAAT,CAAepD,IAAf,EAAqBpE,GAArB,EAA0BuH,SAA1B,EAAqC;AACpC,QAAItD,CAAJ,EAAO9D,CAAP,EAAUuE,CAAV;AAEAT,IAAAA,CAAC,GAAG,CAAJ;;AACA,WAAOvD,KAAK,KAAK,IAAV,IAAkBuD,CAAC,GAAGsD,SAA7B,EAAwC;AACvCpH,MAAAA,CAAC,GAAGoH,SAAS,GAAGtD,CAAhB;;AACA,UAAI9D,CAAC,GAAGO,KAAK,CAACZ,GAAd,EAAmB;AAClBK,QAAAA,CAAC,GAAGO,KAAK,CAACZ,GAAV;AACA,OAJsC,CAKvC;;;AACA,WAAK4E,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGvE,CAAhB,EAAmBuE,CAAC,EAApB,EAAwB;AACvBN,QAAAA,IAAI,CAACpE,GAAG,GAAGiE,CAAN,GAAUS,CAAX,CAAJ,GAAoBhE,KAAK,CAACX,GAAN,CAAUW,KAAK,CAACV,GAAN,GAAY0E,CAAtB,CAApB;AACA;;AAEDhE,MAAAA,KAAK,CAACV,GAAN,IAAaG,CAAb;AACAO,MAAAA,KAAK,CAACZ,GAAN,IAAaK,CAAb;AACA8D,MAAAA,CAAC,IAAI9D,CAAL;;AACA,UAAIO,KAAK,CAACZ,GAAN,KAAc,CAAlB,EAAqB;AACpB,YAAIwC,CAAJ;AACAA,QAAAA,CAAC,GAAG5B,KAAJ;AACAA,QAAAA,KAAK,GAAGA,KAAK,CAACb,IAAd;AACAwC,QAAAA,WAAW,CAACC,CAAD,CAAX;AACA;AACD;;AAED,QAAI2B,CAAC,KAAKsD,SAAV,EAAqB;AACpB,aAAOtD,CAAP;AACA;;AAED,QAAIpB,MAAM,GAAGC,MAAb,EAAqB;AACpB3C,MAAAA,CAAC,GAAGoH,SAAS,GAAGtD,CAAhB;;AACA,UAAI9D,CAAC,GAAG2C,MAAM,GAAGD,MAAjB,EAAyB;AACxB1C,QAAAA,CAAC,GAAG2C,MAAM,GAAGD,MAAb;AACA,OAJmB,CAKpB;;;AACA,WAAK6B,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGvE,CAAhB,EAAmBuE,CAAC,EAApB,EAAwB;AACvBN,QAAAA,IAAI,CAACpE,GAAG,GAAGiE,CAAN,GAAUS,CAAX,CAAJ,GAAoBlE,MAAM,CAACqC,MAAM,GAAG6B,CAAV,CAA1B;AACA;;AACD7B,MAAAA,MAAM,IAAI1C,CAAV;AACA8D,MAAAA,CAAC,IAAI9D,CAAL;;AACA,UAAI2C,MAAM,KAAKD,MAAf,EAAuB;AACtBC,QAAAA,MAAM,GAAGD,MAAM,GAAG,CAAlB;AACA;AACD;;AACD,WAAOoB,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,WAASmD,OAAT,GAAmB;AAClB,QAAInD,CAAJ,CADkB,CACX;;AACP,QAAIwD,IAAJ,CAFkB,CAER;;AACV,QAAIjD,MAAJ,CAHkB,CAGN;;AACZ,QAAIkD,IAAJ,CAJkB,CAIR;;AACV,QAAI3D,IAAJ,CALkB,CAKR;;AAEV,QAAIzC,YAAY,CAAC,CAAD,CAAZ,CAAgB5C,EAAhB,KAAuB,CAA3B,EAA8B;AAC7B,aAD6B,CACrB;AACR;;AAED+C,IAAAA,MAAM,CAAC7C,QAAP,GAAkBoC,SAAlB;AACAS,IAAAA,MAAM,CAAC5C,WAAP,GAAqBuC,YAArB;AACAK,IAAAA,MAAM,CAAC3C,UAAP,GAAoB6I,WAApB;AACAlG,IAAAA,MAAM,CAAC1C,UAAP,GAAoB6I,QAAQ,GAAG,CAA/B;AACAnG,IAAAA,MAAM,CAACzC,KAAP,GAAeqC,OAAf;AACAI,IAAAA,MAAM,CAACxC,UAAP,GAAoB4I,QAApB;AACApG,IAAAA,MAAM,CAACvC,QAAP,GAAkB,CAAlB;AAEAwC,IAAAA,MAAM,CAAC9C,QAAP,GAAkBsC,SAAlB;AACAQ,IAAAA,MAAM,CAAC7C,WAAP,GAAqByC,YAArB;AACAI,IAAAA,MAAM,CAAC5C,UAAP,GAAoBgJ,WAApB;AACApG,IAAAA,MAAM,CAAC3C,UAAP,GAAoB,CAApB;AACA2C,IAAAA,MAAM,CAAC1C,KAAP,GAAemC,OAAf;AACAO,IAAAA,MAAM,CAACzC,UAAP,GAAoB4I,QAApB;AACAnG,IAAAA,MAAM,CAACxC,QAAP,GAAkB,CAAlB;AAEAyC,IAAAA,OAAO,CAAC/C,QAAR,GAAmB2C,OAAnB;AACAI,IAAAA,OAAO,CAAC9C,WAAR,GAAsB,IAAtB;AACA8C,IAAAA,OAAO,CAAC7C,UAAR,GAAqBiJ,YAArB;AACApG,IAAAA,OAAO,CAAC5C,UAAR,GAAqB,CAArB;AACA4C,IAAAA,OAAO,CAAC3C,KAAR,GAAgBwC,QAAhB;AACAG,IAAAA,OAAO,CAAC1C,UAAR,GAAqB+I,WAArB;AACArG,IAAAA,OAAO,CAACzC,QAAR,GAAmB,CAAnB,CAjCkB,CAmClB;;AACAsF,IAAAA,MAAM,GAAG,CAAT;;AACA,SAAKkD,IAAI,GAAG,CAAZ,EAAeA,IAAI,GAAGO,YAAY,GAAG,CAArC,EAAwCP,IAAI,EAA5C,EAAgD;AAC/CzF,MAAAA,WAAW,CAACyF,IAAD,CAAX,GAAoBlD,MAApB;;AACA,WAAKP,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAI,KAAK0D,WAAW,CAACD,IAAD,CAAjC,EAA0CzD,CAAC,EAA3C,EAA+C;AAC9ClC,QAAAA,WAAW,CAACyC,MAAM,EAAP,CAAX,GAAwBkD,IAAxB;AACA;AACD,KA1CiB,CA2ClB;AAEA;AACA;AACA;;;AACA3F,IAAAA,WAAW,CAACyC,MAAM,GAAG,CAAV,CAAX,GAA0BkD,IAA1B,CAhDkB,CAkDlB;;AACA3D,IAAAA,IAAI,GAAG,CAAP;;AACA,SAAK2D,IAAI,GAAG,CAAZ,EAAeA,IAAI,GAAG,EAAtB,EAA0BA,IAAI,EAA9B,EAAkC;AACjCxF,MAAAA,SAAS,CAACwF,IAAD,CAAT,GAAkB3D,IAAlB;;AACA,WAAKE,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAI,KAAK6D,WAAW,CAACJ,IAAD,CAAjC,EAA0CzD,CAAC,EAA3C,EAA+C;AAC9CjC,QAAAA,SAAS,CAAC+B,IAAI,EAAL,CAAT,GAAoB2D,IAApB;AACA;AACD,KAzDiB,CA0DlB;AACA;;;AACA,SAAK3D,IAAI,KAAK,CAAd,EAAiB2D,IAAI,GAAGvG,OAAxB,EAAiCuG,IAAI,EAArC,EAAyC;AACxCxF,MAAAA,SAAS,CAACwF,IAAD,CAAT,GAAkB3D,IAAI,IAAI,CAA1B;;AACA,WAAKE,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAI,KAAM6D,WAAW,CAACJ,IAAD,CAAX,GAAoB,CAA3C,EAAgDzD,CAAC,EAAjD,EAAqD;AACpDjC,QAAAA,SAAS,CAAC,MAAM+B,IAAI,EAAX,CAAT,GAA0B2D,IAA1B;AACA;AACD,KAjEiB,CAkElB;AAEA;;;AACA,SAAKD,IAAI,GAAG,CAAZ,EAAeA,IAAI,IAAII,QAAvB,EAAiCJ,IAAI,EAArC,EAAyC;AACxC7F,MAAAA,QAAQ,CAAC6F,IAAD,CAAR,GAAiB,CAAjB;AACA;;AACDxD,IAAAA,CAAC,GAAG,CAAJ;;AACA,WAAOA,CAAC,IAAI,GAAZ,EAAiB;AAChB7C,MAAAA,YAAY,CAAC6C,CAAC,EAAF,CAAZ,CAAkBvF,EAAlB,GAAuB,CAAvB;AACAkD,MAAAA,QAAQ,CAAC,CAAD,CAAR;AACA;;AACD,WAAOqC,CAAC,IAAI,GAAZ,EAAiB;AAChB7C,MAAAA,YAAY,CAAC6C,CAAC,EAAF,CAAZ,CAAkBvF,EAAlB,GAAuB,CAAvB;AACAkD,MAAAA,QAAQ,CAAC,CAAD,CAAR;AACA;;AACD,WAAOqC,CAAC,IAAI,GAAZ,EAAiB;AAChB7C,MAAAA,YAAY,CAAC6C,CAAC,EAAF,CAAZ,CAAkBvF,EAAlB,GAAuB,CAAvB;AACAkD,MAAAA,QAAQ,CAAC,CAAD,CAAR;AACA;;AACD,WAAOqC,CAAC,IAAI,GAAZ,EAAiB;AAChB7C,MAAAA,YAAY,CAAC6C,CAAC,EAAF,CAAZ,CAAkBvF,EAAlB,GAAuB,CAAvB;AACAkD,MAAAA,QAAQ,CAAC,CAAD,CAAR;AACA,KAxFiB,CAyFlB;AACA;AACA;;;AACAsG,IAAAA,SAAS,CAAC9G,YAAD,EAAeC,OAAO,GAAG,CAAzB,CAAT,CA5FkB,CA8FlB;;AACA,SAAK4C,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG9C,OAAhB,EAAyB8C,CAAC,EAA1B,EAA8B;AAC7B3C,MAAAA,YAAY,CAAC2C,CAAD,CAAZ,CAAgBvF,EAAhB,GAAqB,CAArB;AACA4C,MAAAA,YAAY,CAAC2C,CAAD,CAAZ,CAAgBxF,EAAhB,GAAqB0J,UAAU,CAAClE,CAAD,EAAI,CAAJ,CAA/B;AACA,KAlGiB,CAoGlB;;;AACAmE,IAAAA,UAAU;AACV;AAED;AACA;AACA;;;AACA,WAASA,UAAT,GAAsB;AACrB,QAAInE,CAAJ,CADqB,CACd;AAEP;;AACA,SAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG5C,OAAhB,EAA0B4C,CAAC,EAA3B,EAA+B;AAC9BjD,MAAAA,SAAS,CAACiD,CAAD,CAAT,CAAaxF,EAAb,GAAkB,CAAlB;AACA;;AACD,SAAKwF,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG9C,OAAhB,EAA0B8C,CAAC,EAA3B,EAA+B;AAC9B/C,MAAAA,SAAS,CAAC+C,CAAD,CAAT,CAAaxF,EAAb,GAAkB,CAAlB;AACA;;AACD,SAAKwF,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGzC,QAAhB,EAA0ByC,CAAC,EAA3B,EAA+B;AAC9B1C,MAAAA,OAAO,CAAC0C,CAAD,CAAP,CAAWxF,EAAX,GAAgB,CAAhB;AACA;;AAEDuC,IAAAA,SAAS,CAACqH,SAAD,CAAT,CAAqB5J,EAArB,GAA0B,CAA1B;AACA6J,IAAAA,OAAO,GAAGC,UAAU,GAAG,CAAvB;AACAC,IAAAA,QAAQ,GAAGC,SAAS,GAAGC,UAAU,GAAG,CAApC;AACAC,IAAAA,KAAK,GAAG,CAAR;AACAC,IAAAA,QAAQ,GAAG,CAAX;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASC,UAAT,CAAoBjF,IAApB,EAA0BkF,CAA1B,EAA6B;AAC5B,QAAIC,CAAC,GAAGlH,IAAI,CAACiH,CAAD,CAAZ;AAAA,QACCpE,CAAC,GAAGoE,CAAC,IAAI,CADV,CAD4B,CAEf;;AAEb,WAAOpE,CAAC,IAAIsE,QAAZ,EAAsB;AACrB;AACA,UAAItE,CAAC,GAAGsE,QAAJ,IAAgBhF,OAAO,CAACJ,IAAD,EAAO/B,IAAI,CAAC6C,CAAC,GAAG,CAAL,CAAX,EAAoB7C,IAAI,CAAC6C,CAAD,CAAxB,CAA3B,EAAyD;AACxDA,QAAAA,CAAC;AACD,OAJoB,CAMrB;;;AACA,UAAIV,OAAO,CAACJ,IAAD,EAAOmF,CAAP,EAAUlH,IAAI,CAAC6C,CAAD,CAAd,CAAX,EAA+B;AAC9B;AACA,OAToB,CAWrB;;;AACA7C,MAAAA,IAAI,CAACiH,CAAD,CAAJ,GAAUjH,IAAI,CAAC6C,CAAD,CAAd;AACAoE,MAAAA,CAAC,GAAGpE,CAAJ,CAbqB,CAerB;;AACAA,MAAAA,CAAC,KAAK,CAAN;AACA;;AACD7C,IAAAA,IAAI,CAACiH,CAAD,CAAJ,GAAUC,CAAV;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASE,UAAT,CAAoBC,IAApB,EAA0B;AAAE;AAC3B,QAAItF,IAAI,GAAGsF,IAAI,CAACtK,QAAhB;AACA,QAAIuK,KAAK,GAAGD,IAAI,CAACpK,UAAjB;AACA,QAAIsK,IAAI,GAAGF,IAAI,CAACnK,UAAhB;AACA,QAAIG,QAAQ,GAAGgK,IAAI,CAAChK,QAApB;AACA,QAAID,UAAU,GAAGiK,IAAI,CAACjK,UAAtB;AACA,QAAIoK,KAAK,GAAGH,IAAI,CAACrK,WAAjB;AACA,QAAIyK,CAAJ,CAPyB,CAOlB;;AACP,QAAIrF,CAAJ,EAAOC,CAAP,CARyB,CAQf;;AACV,QAAIuD,IAAJ,CATyB,CASf;;AACV,QAAI8B,KAAJ,CAVyB,CAUd;;AACX,QAAIC,CAAJ,CAXyB,CAWlB;;AACP,QAAIC,QAAQ,GAAG,CAAf,CAZyB,CAYP;;AAElB,SAAKhC,IAAI,GAAG,CAAZ,EAAeA,IAAI,IAAII,QAAvB,EAAiCJ,IAAI,EAArC,EAAyC;AACxC7F,MAAAA,QAAQ,CAAC6F,IAAD,CAAR,GAAiB,CAAjB;AACA,KAhBwB,CAkBzB;AACA;;;AACA7D,IAAAA,IAAI,CAAC/B,IAAI,CAAC6H,QAAD,CAAL,CAAJ,CAAqBhL,EAArB,GAA0B,CAA1B,CApByB,CAoBI;;AAE7B,SAAK4K,CAAC,GAAGI,QAAQ,GAAG,CAApB,EAAuBJ,CAAC,GAAGrI,SAA3B,EAAsCqI,CAAC,EAAvC,EAA2C;AAC1CrF,MAAAA,CAAC,GAAGpC,IAAI,CAACyH,CAAD,CAAR;AACA7B,MAAAA,IAAI,GAAG7D,IAAI,CAACA,IAAI,CAACK,CAAD,CAAJ,CAAQvF,EAAT,CAAJ,CAAiBA,EAAjB,GAAsB,CAA7B;;AACA,UAAI+I,IAAI,GAAGxI,UAAX,EAAuB;AACtBwI,QAAAA,IAAI,GAAGxI,UAAP;AACAwK,QAAAA,QAAQ;AACR;;AACD7F,MAAAA,IAAI,CAACK,CAAD,CAAJ,CAAQvF,EAAR,GAAa+I,IAAb,CAP0C,CAQ1C;;AAEA,UAAIxD,CAAC,GAAG/E,QAAR,EAAkB;AACjB,iBADiB,CACP;AACV;;AAED0C,MAAAA,QAAQ,CAAC6F,IAAD,CAAR;AACA8B,MAAAA,KAAK,GAAG,CAAR;;AACA,UAAItF,CAAC,IAAImF,IAAT,EAAe;AACdG,QAAAA,KAAK,GAAGJ,KAAK,CAAClF,CAAC,GAAGmF,IAAL,CAAb;AACA;;AACDI,MAAAA,CAAC,GAAG5F,IAAI,CAACK,CAAD,CAAJ,CAAQxF,EAAZ;AACA6J,MAAAA,OAAO,IAAIkB,CAAC,IAAI/B,IAAI,GAAG8B,KAAX,CAAZ;;AACA,UAAIF,KAAK,KAAK,IAAd,EAAoB;AACnBd,QAAAA,UAAU,IAAIiB,CAAC,IAAIH,KAAK,CAACpF,CAAD,CAAL,CAASvF,EAAT,GAAc6K,KAAlB,CAAf;AACA;AACD;;AACD,QAAIE,QAAQ,KAAK,CAAjB,EAAoB;AACnB;AACA,KAjDwB,CAmDzB;AAEA;;;AACA,OAAG;AACFhC,MAAAA,IAAI,GAAGxI,UAAU,GAAG,CAApB;;AACA,aAAO2C,QAAQ,CAAC6F,IAAD,CAAR,KAAmB,CAA1B,EAA6B;AAC5BA,QAAAA,IAAI;AACJ;;AACD7F,MAAAA,QAAQ,CAAC6F,IAAD,CAAR,GALE,CAKgB;;AAClB7F,MAAAA,QAAQ,CAAC6F,IAAI,GAAG,CAAR,CAAR,IAAsB,CAAtB,CANE,CAMuB;;AACzB7F,MAAAA,QAAQ,CAAC3C,UAAD,CAAR,GAPE,CAQF;AACA;;AACAwK,MAAAA,QAAQ,IAAI,CAAZ;AACA,KAXD,QAWSA,QAAQ,GAAG,CAXpB,EAtDyB,CAmEzB;AACA;AACA;AACA;;;AACA,SAAKhC,IAAI,GAAGxI,UAAZ,EAAwBwI,IAAI,KAAK,CAAjC,EAAoCA,IAAI,EAAxC,EAA4C;AAC3CxD,MAAAA,CAAC,GAAGrC,QAAQ,CAAC6F,IAAD,CAAZ;;AACA,aAAOxD,CAAC,KAAK,CAAb,EAAgB;AACfC,QAAAA,CAAC,GAAGrC,IAAI,CAAC,EAAEyH,CAAH,CAAR;;AACA,YAAIpF,CAAC,GAAGhF,QAAR,EAAkB;AACjB;AACA;;AACD,YAAI0E,IAAI,CAACM,CAAD,CAAJ,CAAQxF,EAAR,KAAe+I,IAAnB,EAAyB;AACxBa,UAAAA,OAAO,IAAI,CAACb,IAAI,GAAG7D,IAAI,CAACM,CAAD,CAAJ,CAAQxF,EAAhB,IAAsBkF,IAAI,CAACM,CAAD,CAAJ,CAAQzF,EAAzC;AACAmF,UAAAA,IAAI,CAACM,CAAD,CAAJ,CAAQzF,EAAR,GAAagJ,IAAb;AACA;;AACDxD,QAAAA,CAAC;AACD;AACD;AACD;AAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASiE,SAAT,CAAmBtE,IAAnB,EAAyB1E,QAAzB,EAAmC;AAClC,QAAIyK,SAAS,GAAG,EAAhB,CADkC,CACd;;AACpB,QAAIjC,IAAI,GAAG,CAAX,CAFkC,CAEpB;;AACd,QAAID,IAAJ,CAHkC,CAGxB;;AACV,QAAIxD,CAAJ,CAJkC,CAI3B;AAEP;AACA;;AACA,SAAKwD,IAAI,GAAG,CAAZ,EAAeA,IAAI,IAAII,QAAvB,EAAiCJ,IAAI,EAArC,EAAyC;AACxCC,MAAAA,IAAI,GAAKA,IAAI,GAAG9F,QAAQ,CAAC6F,IAAI,GAAG,CAAR,CAAhB,IAA+B,CAAvC;AACAkC,MAAAA,SAAS,CAAClC,IAAD,CAAT,GAAkBC,IAAlB;AACA,KAXiC,CAalC;AACA;AACA;AACA;;;AAEA,SAAKzD,CAAC,GAAG,CAAT,EAAYA,CAAC,IAAI/E,QAAjB,EAA2B+E,CAAC,EAA5B,EAAgC;AAC/B,UAAInE,GAAG,GAAG8D,IAAI,CAACK,CAAD,CAAJ,CAAQvF,EAAlB;;AACA,UAAIoB,GAAG,KAAK,CAAZ,EAAe;AACd;AACA,OAJ8B,CAK/B;;;AACA8D,MAAAA,IAAI,CAACK,CAAD,CAAJ,CAAQxF,EAAR,GAAa0J,UAAU,CAACwB,SAAS,CAAC7J,GAAD,CAAT,EAAD,EAAmBA,GAAnB,CAAvB,CAN+B,CAQ/B;AACA;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAAS8J,UAAT,CAAoBV,IAApB,EAA0B;AAAE;AAC3B,QAAItF,IAAI,GAAGsF,IAAI,CAACtK,QAAhB;AACA,QAAIyK,KAAK,GAAGH,IAAI,CAACrK,WAAjB;AACA,QAAIG,KAAK,GAAGkK,IAAI,CAAClK,KAAjB;AACA,QAAIiF,CAAJ,EAAOC,CAAP,CAJyB,CAIf;;AACV,QAAIhF,QAAQ,GAAG,CAAC,CAAhB,CALyB,CAKN;;AACnB,QAAI2K,IAAI,GAAG7K,KAAX,CANyB,CAMP;AAElB;AACA;AACA;;AACAgK,IAAAA,QAAQ,GAAG,CAAX;AACAU,IAAAA,QAAQ,GAAGzI,SAAX;;AAEA,SAAKgD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGjF,KAAhB,EAAuBiF,CAAC,EAAxB,EAA4B;AAC3B,UAAIL,IAAI,CAACK,CAAD,CAAJ,CAAQxF,EAAR,KAAe,CAAnB,EAAsB;AACrBoD,QAAAA,IAAI,CAAC,EAAEmH,QAAH,CAAJ,GAAmB9J,QAAQ,GAAG+E,CAA9B;AACAnC,QAAAA,KAAK,CAACmC,CAAD,CAAL,GAAW,CAAX;AACA,OAHD,MAGO;AACNL,QAAAA,IAAI,CAACK,CAAD,CAAJ,CAAQvF,EAAR,GAAa,CAAb;AACA;AACD,KArBwB,CAuBzB;AACA;AACA;AACA;;;AACA,WAAOsK,QAAQ,GAAG,CAAlB,EAAqB;AACpB,UAAIc,IAAI,GAAGjI,IAAI,CAAC,EAAEmH,QAAH,CAAJ,GAAoB9J,QAAQ,GAAG,CAAX,GAAe,EAAEA,QAAjB,GAA4B,CAA3D;AACA0E,MAAAA,IAAI,CAACkG,IAAD,CAAJ,CAAWrL,EAAX,GAAgB,CAAhB;AACAqD,MAAAA,KAAK,CAACgI,IAAD,CAAL,GAAc,CAAd;AACAxB,MAAAA,OAAO;;AACP,UAAIe,KAAK,KAAK,IAAd,EAAoB;AACnBd,QAAAA,UAAU,IAAIc,KAAK,CAACS,IAAD,CAAL,CAAYpL,EAA1B;AACA,OAPmB,CAQpB;;AACA;;AACDwK,IAAAA,IAAI,CAAChK,QAAL,GAAgBA,QAAhB,CArCyB,CAuCzB;AACA;;AACA,SAAK+E,CAAC,GAAG+E,QAAQ,IAAI,CAArB,EAAwB/E,CAAC,IAAI,CAA7B,EAAgCA,CAAC,EAAjC,EAAqC;AACpC4E,MAAAA,UAAU,CAACjF,IAAD,EAAOK,CAAP,CAAV;AACA,KA3CwB,CA6CzB;AACA;;;AACA,OAAG;AACFA,MAAAA,CAAC,GAAGpC,IAAI,CAACkI,QAAD,CAAR;AACAlI,MAAAA,IAAI,CAACkI,QAAD,CAAJ,GAAiBlI,IAAI,CAACmH,QAAQ,EAAT,CAArB;AACAH,MAAAA,UAAU,CAACjF,IAAD,EAAOmG,QAAP,CAAV;AAEA7F,MAAAA,CAAC,GAAGrC,IAAI,CAACkI,QAAD,CAAR,CALE,CAKkB;AAEpB;;AACAlI,MAAAA,IAAI,CAAC,EAAE6H,QAAH,CAAJ,GAAmBzF,CAAnB;AACApC,MAAAA,IAAI,CAAC,EAAE6H,QAAH,CAAJ,GAAmBxF,CAAnB,CATE,CAWF;;AACAN,MAAAA,IAAI,CAACiG,IAAD,CAAJ,CAAWpL,EAAX,GAAgBmF,IAAI,CAACK,CAAD,CAAJ,CAAQxF,EAAR,GAAamF,IAAI,CAACM,CAAD,CAAJ,CAAQzF,EAArC,CAZE,CAaF;;AACA,UAAIqD,KAAK,CAACmC,CAAD,CAAL,GAAWnC,KAAK,CAACoC,CAAD,CAAL,GAAW,CAA1B,EAA6B;AAC5BpC,QAAAA,KAAK,CAAC+H,IAAD,CAAL,GAAc/H,KAAK,CAACmC,CAAD,CAAnB;AACA,OAFD,MAEO;AACNnC,QAAAA,KAAK,CAAC+H,IAAD,CAAL,GAAc/H,KAAK,CAACoC,CAAD,CAAL,GAAW,CAAzB;AACA;;AACDN,MAAAA,IAAI,CAACK,CAAD,CAAJ,CAAQvF,EAAR,GAAakF,IAAI,CAACM,CAAD,CAAJ,CAAQxF,EAAR,GAAamL,IAA1B,CAnBE,CAqBF;;AACAhI,MAAAA,IAAI,CAACkI,QAAD,CAAJ,GAAiBF,IAAI,EAArB;AACAhB,MAAAA,UAAU,CAACjF,IAAD,EAAOmG,QAAP,CAAV;AAEA,KAzBD,QAyBSf,QAAQ,IAAI,CAzBrB;;AA2BAnH,IAAAA,IAAI,CAAC,EAAE6H,QAAH,CAAJ,GAAmB7H,IAAI,CAACkI,QAAD,CAAvB,CA1EyB,CA4EzB;AACA;;AACAd,IAAAA,UAAU,CAACC,IAAD,CAAV,CA9EyB,CAgFzB;;AACAhB,IAAAA,SAAS,CAACtE,IAAD,EAAO1E,QAAP,CAAT;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAAS8K,SAAT,CAAmBpG,IAAnB,EAAyB1E,QAAzB,EAAmC;AAClC,QAAI+E,CAAJ;AAAA,QAAO;AACNgG,IAAAA,OAAO,GAAG,CAAC,CADZ;AAAA,QACe;AACdC,IAAAA,MAFD;AAAA,QAES;AACRC,IAAAA,OAAO,GAAGvG,IAAI,CAAC,CAAD,CAAJ,CAAQlF,EAHnB;AAAA,QAGuB;AACtB0L,IAAAA,KAAK,GAAG,CAJT;AAAA,QAIY;AACXC,IAAAA,SAAS,GAAG,CALb;AAAA,QAKgB;AACfC,IAAAA,SAAS,GAAG,CANb,CADkC,CAOlB;;AAEhB,QAAIH,OAAO,KAAK,CAAhB,EAAmB;AAClBE,MAAAA,SAAS,GAAG,GAAZ;AACAC,MAAAA,SAAS,GAAG,CAAZ;AACA;;AACD1G,IAAAA,IAAI,CAAC1E,QAAQ,GAAG,CAAZ,CAAJ,CAAmBR,EAAnB,GAAwB,MAAxB,CAbkC,CAaF;;AAEhC,SAAKuF,CAAC,GAAG,CAAT,EAAYA,CAAC,IAAI/E,QAAjB,EAA2B+E,CAAC,EAA5B,EAAgC;AAC/BiG,MAAAA,MAAM,GAAGC,OAAT;AACAA,MAAAA,OAAO,GAAGvG,IAAI,CAACK,CAAC,GAAG,CAAL,CAAJ,CAAYvF,EAAtB;;AACA,UAAI,EAAE0L,KAAF,GAAUC,SAAV,IAAuBH,MAAM,KAAKC,OAAtC,EAA+C;AAC9C;AACA,OAFD,MAEO,IAAIC,KAAK,GAAGE,SAAZ,EAAuB;AAC7B/I,QAAAA,OAAO,CAAC2I,MAAD,CAAP,CAAgBzL,EAAhB,IAAsB2L,KAAtB;AACA,OAFM,MAEA,IAAIF,MAAM,KAAK,CAAf,EAAkB;AACxB,YAAIA,MAAM,KAAKD,OAAf,EAAwB;AACvB1I,UAAAA,OAAO,CAAC2I,MAAD,CAAP,CAAgBzL,EAAhB;AACA;;AACD8C,QAAAA,OAAO,CAACgJ,OAAD,CAAP,CAAiB9L,EAAjB;AACA,OALM,MAKA,IAAI2L,KAAK,IAAI,EAAb,EAAiB;AACvB7I,QAAAA,OAAO,CAACiJ,SAAD,CAAP,CAAmB/L,EAAnB;AACA,OAFM,MAEA;AACN8C,QAAAA,OAAO,CAACkJ,WAAD,CAAP,CAAqBhM,EAArB;AACA;;AACD2L,MAAAA,KAAK,GAAG,CAAR;AAAWH,MAAAA,OAAO,GAAGC,MAAV;;AACX,UAAIC,OAAO,KAAK,CAAhB,EAAmB;AAClBE,QAAAA,SAAS,GAAG,GAAZ;AACAC,QAAAA,SAAS,GAAG,CAAZ;AACA,OAHD,MAGO,IAAIJ,MAAM,KAAKC,OAAf,EAAwB;AAC9BE,QAAAA,SAAS,GAAG,CAAZ;AACAC,QAAAA,SAAS,GAAG,CAAZ;AACA,OAHM,MAGA;AACND,QAAAA,SAAS,GAAG,CAAZ;AACAC,QAAAA,SAAS,GAAG,CAAZ;AACA;AACD;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASI,SAAT,CAAmB9G,IAAnB,EAAyB1E,QAAzB,EAAmC;AAClC,QAAI+E,CAAJ,CADkC,CAC3B;;AACP,QAAIgG,OAAO,GAAG,CAAC,CAAf,CAFkC,CAEhB;;AAClB,QAAIC,MAAJ,CAHkC,CAGtB;;AACZ,QAAIC,OAAO,GAAGvG,IAAI,CAAC,CAAD,CAAJ,CAAQlF,EAAtB,CAJkC,CAIR;;AAC1B,QAAI0L,KAAK,GAAG,CAAZ,CALkC,CAKnB;;AACf,QAAIC,SAAS,GAAG,CAAhB,CANkC,CAMf;;AACnB,QAAIC,SAAS,GAAG,CAAhB,CAPkC,CAOf;AAEnB;;AACA,QAAIH,OAAO,KAAK,CAAhB,EAAmB;AAClBE,MAAAA,SAAS,GAAG,GAAZ;AACAC,MAAAA,SAAS,GAAG,CAAZ;AACA;;AAED,SAAKrG,CAAC,GAAG,CAAT,EAAYA,CAAC,IAAI/E,QAAjB,EAA2B+E,CAAC,EAA5B,EAAgC;AAC/BiG,MAAAA,MAAM,GAAGC,OAAT;AACAA,MAAAA,OAAO,GAAGvG,IAAI,CAACK,CAAC,GAAG,CAAL,CAAJ,CAAYvF,EAAtB;;AACA,UAAI,EAAE0L,KAAF,GAAUC,SAAV,IAAuBH,MAAM,KAAKC,OAAtC,EAA+C;AAC9C;AACA,OAFD,MAEO,IAAIC,KAAK,GAAGE,SAAZ,EAAuB;AAC7B,WAAG;AACF3G,UAAAA,SAAS,CAACuG,MAAD,EAAS3I,OAAT,CAAT;AACA,SAFD,QAES,EAAE6I,KAAF,KAAY,CAFrB;AAGA,OAJM,MAIA,IAAIF,MAAM,KAAK,CAAf,EAAkB;AACxB,YAAIA,MAAM,KAAKD,OAAf,EAAwB;AACvBtG,UAAAA,SAAS,CAACuG,MAAD,EAAS3I,OAAT,CAAT;AACA6I,UAAAA,KAAK;AACL,SAJuB,CAKzB;;;AACCzG,QAAAA,SAAS,CAAC4G,OAAD,EAAUhJ,OAAV,CAAT;AACAsC,QAAAA,SAAS,CAACuG,KAAK,GAAG,CAAT,EAAY,CAAZ,CAAT;AACA,OARM,MAQA,IAAIA,KAAK,IAAI,EAAb,EAAiB;AACvBzG,QAAAA,SAAS,CAAC6G,SAAD,EAAYjJ,OAAZ,CAAT;AACAsC,QAAAA,SAAS,CAACuG,KAAK,GAAG,CAAT,EAAY,CAAZ,CAAT;AACA,OAHM,MAGA;AACNzG,QAAAA,SAAS,CAAC8G,WAAD,EAAclJ,OAAd,CAAT;AACAsC,QAAAA,SAAS,CAACuG,KAAK,GAAG,EAAT,EAAa,CAAb,CAAT;AACA;;AACDA,MAAAA,KAAK,GAAG,CAAR;AACAH,MAAAA,OAAO,GAAGC,MAAV;;AACA,UAAIC,OAAO,KAAK,CAAhB,EAAmB;AAClBE,QAAAA,SAAS,GAAG,GAAZ;AACAC,QAAAA,SAAS,GAAG,CAAZ;AACA,OAHD,MAGO,IAAIJ,MAAM,KAAKC,OAAf,EAAwB;AAC9BE,QAAAA,SAAS,GAAG,CAAZ;AACAC,QAAAA,SAAS,GAAG,CAAZ;AACA,OAHM,MAGA;AACND,QAAAA,SAAS,GAAG,CAAZ;AACAC,QAAAA,SAAS,GAAG,CAAZ;AACA;AACD;AACD;AAED;AACA;AACA;AACA;;;AACA,WAASK,aAAT,GAAyB;AACxB,QAAIC,WAAJ,CADwB,CACP;AAEjB;;AACAZ,IAAAA,SAAS,CAAChJ,SAAD,EAAYS,MAAM,CAACvC,QAAnB,CAAT;AACA8K,IAAAA,SAAS,CAAC9I,SAAD,EAAYQ,MAAM,CAACxC,QAAnB,CAAT,CALwB,CAOxB;;AACA0K,IAAAA,UAAU,CAACjI,OAAD,CAAV,CARwB,CASxB;AACA;AAEA;AACA;AACA;;AACA,SAAKiJ,WAAW,GAAGpJ,QAAQ,GAAG,CAA9B,EAAiCoJ,WAAW,IAAI,CAAhD,EAAmDA,WAAW,EAA9D,EAAkE;AACjE,UAAIrJ,OAAO,CAACsJ,QAAQ,CAACD,WAAD,CAAT,CAAP,CAA+BlM,EAA/B,KAAsC,CAA1C,EAA6C;AAC5C;AACA;AACD,KAnBuB,CAoBxB;;;AACA4J,IAAAA,OAAO,IAAI,KAAKsC,WAAW,GAAG,CAAnB,IAAwB,CAAxB,GAA4B,CAA5B,GAAgC,CAA3C,CArBwB,CAsBxB;AACA;;AAEA,WAAOA,WAAP;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,WAASE,cAAT,CAAwBC,MAAxB,EAAgCC,MAAhC,EAAwCC,OAAxC,EAAiD;AAAE;AAClD,QAAIC,IAAJ,CADgD,CACtC;AAEV;AACA;AACA;;AACArH,IAAAA,SAAS,CAACkH,MAAM,GAAG,GAAV,EAAe,CAAf,CAAT,CANgD,CAMpB;;AAC5BlH,IAAAA,SAAS,CAACmH,MAAM,GAAG,CAAV,EAAe,CAAf,CAAT;AACAnH,IAAAA,SAAS,CAACoH,OAAO,GAAG,CAAX,EAAe,CAAf,CAAT,CARgD,CAQpB;;AAC5B,SAAKC,IAAI,GAAG,CAAZ,EAAeA,IAAI,GAAGD,OAAtB,EAA+BC,IAAI,EAAnC,EAAuC;AACtC;AACArH,MAAAA,SAAS,CAACtC,OAAO,CAACsJ,QAAQ,CAACK,IAAD,CAAT,CAAP,CAAwBxM,EAAzB,EAA6B,CAA7B,CAAT;AACA,KAZ+C,CAchD;;;AACAgM,IAAAA,SAAS,CAAC1J,SAAD,EAAY+J,MAAM,GAAG,CAArB,CAAT,CAfgD,CAiBhD;;AACAL,IAAAA,SAAS,CAACxJ,SAAD,EAAY8J,MAAM,GAAG,CAArB,CAAT;AACA;AAED;AACA;AACA;AACA;;;AACA,WAASpE,WAAT,CAAqBuE,GAArB,EAA0B;AAAE;AAC3B,QAAIC,QAAJ,EAAcC,WAAd,EAA2B;AAC1BT,IAAAA,WADD,EACc;AACbU,IAAAA,UAFD,EAEa;AACZnL,IAAAA,CAHD;AAKAmL,IAAAA,UAAU,GAAGhI,QAAQ,GAAG4B,WAAxB;AACA/C,IAAAA,QAAQ,CAACuG,UAAD,CAAR,GAAuBC,KAAvB,CAPyB,CAOK;AAE9B;;AACAiB,IAAAA,UAAU,CAACnI,MAAD,CAAV,CAVyB,CAWzB;AACA;;AAEAmI,IAAAA,UAAU,CAAClI,MAAD,CAAV,CAdyB,CAezB;AACA;AACA;AACA;AAEA;AACA;;AACAkJ,IAAAA,WAAW,GAAGD,aAAa,EAA3B,CAtByB,CAwBzB;;AACAS,IAAAA,QAAQ,GAAI9C,OAAO,GAAG,CAAV,GAAc,CAAf,IAAqB,CAAhC;AACA+C,IAAAA,WAAW,GAAI9C,UAAU,GAAG,CAAb,GAAiB,CAAlB,IAAwB,CAAtC,CA1ByB,CA4B1B;;AAEC,QAAI8C,WAAW,IAAID,QAAnB,EAA6B;AAC5BA,MAAAA,QAAQ,GAAGC,WAAX;AACA;;AACD,QAAIC,UAAU,GAAG,CAAb,IAAkBF,QAAlB,IAA8BlG,WAAW,IAAI,CAAjD,EAAoD;AAAE;AACrD;AACA;AACA;AACA;AACA;AACArB,MAAAA,SAAS,CAAC,CAAC0H,YAAY,IAAI,CAAjB,IAAsBJ,GAAvB,EAA4B,CAA5B,CAAT;AAA0C;;AAC1CK,MAAAA,SAAS;AAAY;;AACrBvI,MAAAA,SAAS,CAACqI,UAAD,CAAT;AACArI,MAAAA,SAAS,CAAC,CAACqI,UAAF,CAAT,CATmD,CAWnD;;AACA;AACF;AACA;AACA;AACA;AACA;;AACE,WAAKnL,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGmL,UAAhB,EAA4BnL,CAAC,EAA7B,EAAiC;AAChCyC,QAAAA,QAAQ,CAAChC,MAAM,CAACsE,WAAW,GAAG/E,CAAf,CAAP,CAAR;AACA;AACD,KArBD,MAqBO,IAAIkL,WAAW,KAAKD,QAApB,EAA8B;AACpCvH,MAAAA,SAAS,CAAC,CAAC4H,YAAY,IAAI,CAAjB,IAAsBN,GAAvB,EAA4B,CAA5B,CAAT;AACAO,MAAAA,cAAc,CAACtK,YAAD,EAAeE,YAAf,CAAd;AACA,KAHM,MAGA;AACNuC,MAAAA,SAAS,CAAC,CAAC8H,SAAS,IAAI,CAAd,IAAmBR,GAApB,EAAyB,CAAzB,CAAT;AACAL,MAAAA,cAAc,CAACrJ,MAAM,CAACvC,QAAP,GAAkB,CAAnB,EAAsBwC,MAAM,CAACxC,QAAP,GAAkB,CAAxC,EAA2C0L,WAAW,GAAG,CAAzD,CAAd;AACAc,MAAAA,cAAc,CAAC1K,SAAD,EAAYE,SAAZ,CAAd;AACA;;AAEDkH,IAAAA,UAAU;;AAEV,QAAI+C,GAAG,KAAK,CAAZ,EAAe;AACdK,MAAAA,SAAS;AACT;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAAS7E,QAAT,CAAkB5C,IAAlB,EAAwB6H,EAAxB,EAA4B;AAC3B9K,IAAAA,KAAK,CAAC0H,QAAQ,EAAT,CAAL,GAAoBoD,EAApB;;AACA,QAAI7H,IAAI,KAAK,CAAb,EAAgB;AACf;AACA/C,MAAAA,SAAS,CAAC4K,EAAD,CAAT,CAAcnN,EAAd;AACA,KAHD,MAGO;AACN;AACAsF,MAAAA,IAAI,GAFE,CAEE;AACR;;AAEA/C,MAAAA,SAAS,CAACe,WAAW,CAAC6J,EAAD,CAAX,GAAkBhE,QAAlB,GAA6B,CAA9B,CAAT,CAA0CnJ,EAA1C;AACAyC,MAAAA,SAAS,CAAC4C,MAAM,CAACC,IAAD,CAAP,CAAT,CAAwBtF,EAAxB;AAEAoC,MAAAA,KAAK,CAAC4H,SAAS,EAAV,CAAL,GAAqB1E,IAArB;AACA4E,MAAAA,KAAK,IAAIC,QAAT;AACA;;AACDA,IAAAA,QAAQ,KAAK,CAAb,CAhB2B,CAkB3B;;AACA,QAAI,CAACJ,QAAQ,GAAG,CAAZ,MAAmB,CAAvB,EAA0B;AACzBrG,MAAAA,QAAQ,CAACuG,UAAU,EAAX,CAAR,GAAyBC,KAAzB;AACAA,MAAAA,KAAK,GAAG,CAAR;AACAC,MAAAA,QAAQ,GAAG,CAAX;AACA,KAvB0B,CAwB3B;;;AACA,QAAIvI,WAAW,GAAG,CAAd,IAAmB,CAACmI,QAAQ,GAAG,KAAZ,MAAuB,CAA9C,EAAiD;AAChD;AACA,UAAIqD,UAAU,GAAGrD,QAAQ,GAAG,CAA5B;AACA,UAAIsD,SAAS,GAAGxI,QAAQ,GAAG4B,WAA3B;AACA,UAAI6G,KAAJ;;AAEA,WAAKA,KAAK,GAAG,CAAb,EAAgBA,KAAK,GAAG5K,OAAxB,EAAiC4K,KAAK,EAAtC,EAA0C;AACzCF,QAAAA,UAAU,IAAI3K,SAAS,CAAC6K,KAAD,CAAT,CAAiBtN,EAAjB,IAAuB,IAAIqJ,WAAW,CAACiE,KAAD,CAAtC,CAAd;AACA;;AACDF,MAAAA,UAAU,KAAK,CAAf,CATgD,CAUhD;;AACA,UAAIpD,SAAS,GAAGuD,QAAQ,CAACxD,QAAQ,GAAG,CAAZ,EAAe,EAAf,CAApB,IAA0CqD,UAAU,GAAGG,QAAQ,CAACF,SAAS,GAAG,CAAb,EAAgB,EAAhB,CAAnE,EAAwF;AACvF,eAAO,IAAP;AACA;AACD;;AACD,WAAQtD,QAAQ,KAAKyD,WAAW,GAAG,CAA3B,IAAgCxD,SAAS,KAAKyD,YAAtD,CAxC2B,CAyC3B;AACA;AACA;AACA;AAEA;AACD;AACA;AACA;AACA;AACA;;;AACA,WAASR,cAAT,CAAwBS,KAAxB,EAA+BC,KAA/B,EAAsC;AACrC,QAAIrI,IAAJ,CADqC,CAC3B;;AACV,QAAI6H,EAAJ,CAFqC,CAE7B;;AACR,QAAIS,EAAE,GAAG,CAAT,CAHqC,CAGzB;;AACZ,QAAIC,EAAE,GAAG,CAAT,CAJqC,CAIzB;;AACZ,QAAIC,EAAE,GAAG,CAAT,CALqC,CAKzB;;AACZ,QAAIC,IAAI,GAAG,CAAX,CANqC,CAMvB;;AACd,QAAI9E,IAAJ,CAPqC,CAO3B;;AACV,QAAIyB,KAAJ,CARqC,CAQ1B;;AAEX,QAAIX,QAAQ,KAAK,CAAjB,EAAoB;AACnB,SAAG;AACF,YAAI,CAAC6D,EAAE,GAAG,CAAN,MAAa,CAAjB,EAAoB;AACnBG,UAAAA,IAAI,GAAGrK,QAAQ,CAACoK,EAAE,EAAH,CAAf;AACA;;AACDX,QAAAA,EAAE,GAAG9K,KAAK,CAACuL,EAAE,EAAH,CAAL,GAAc,IAAnB;;AACA,YAAI,CAACG,IAAI,GAAG,CAAR,MAAe,CAAnB,EAAsB;AACrB7I,UAAAA,SAAS,CAACiI,EAAD,EAAKO,KAAL,CAAT;AAAsB;AACtB;AACA,SAHD,MAGO;AACN;AACAzE,UAAAA,IAAI,GAAG3F,WAAW,CAAC6J,EAAD,CAAlB;AACAjI,UAAAA,SAAS,CAAC+D,IAAI,GAAGE,QAAP,GAAkB,CAAnB,EAAsBuE,KAAtB,CAAT,CAHM,CAGiC;;AACvChD,UAAAA,KAAK,GAAGxB,WAAW,CAACD,IAAD,CAAnB;;AACA,cAAIyB,KAAK,KAAK,CAAd,EAAiB;AAChByC,YAAAA,EAAE,IAAI3J,WAAW,CAACyF,IAAD,CAAjB;AACA7D,YAAAA,SAAS,CAAC+H,EAAD,EAAKzC,KAAL,CAAT,CAFgB,CAEM;AACtB;;AACDpF,UAAAA,IAAI,GAAGlD,KAAK,CAACyL,EAAE,EAAH,CAAZ,CATM,CAUN;;AACA5E,UAAAA,IAAI,GAAG5D,MAAM,CAACC,IAAD,CAAb,CAXM,CAYN;;AAEAJ,UAAAA,SAAS,CAAC+D,IAAD,EAAO0E,KAAP,CAAT,CAdM,CAckB;;AACxBjD,UAAAA,KAAK,GAAGrB,WAAW,CAACJ,IAAD,CAAnB;;AACA,cAAIyB,KAAK,KAAK,CAAd,EAAiB;AAChBpF,YAAAA,IAAI,IAAI7B,SAAS,CAACwF,IAAD,CAAjB;AACA7D,YAAAA,SAAS,CAACE,IAAD,EAAOoF,KAAP,CAAT,CAFgB,CAEQ;AACxB;AACD,SA5BC,CA4BA;;;AACFqD,QAAAA,IAAI,KAAK,CAAT;AACA,OA9BD,QA8BSH,EAAE,GAAG7D,QA9Bd;AA+BA;;AAED7E,IAAAA,SAAS,CAAC0E,SAAD,EAAY8D,KAAZ,CAAT;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACmB;AACnB,WAAStI,SAAT,CAAmB4I,KAAnB,EAA0BjI,MAA1B,EAAkC;AACjC;AACA;AACA;AACA,QAAI2C,QAAQ,GAAGuF,QAAQ,GAAGlI,MAA1B,EAAkC;AACjC0C,MAAAA,MAAM,IAAKuF,KAAK,IAAItF,QAApB;AACAlE,MAAAA,SAAS,CAACiE,MAAD,CAAT;AACAA,MAAAA,MAAM,GAAIuF,KAAK,IAAKC,QAAQ,GAAGvF,QAA/B;AACAA,MAAAA,QAAQ,IAAI3C,MAAM,GAAGkI,QAArB;AACA,KALD,MAKO;AACNxF,MAAAA,MAAM,IAAIuF,KAAK,IAAItF,QAAnB;AACAA,MAAAA,QAAQ,IAAI3C,MAAZ;AACA;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAAS2D,UAAT,CAAoBT,IAApB,EAA0B5H,GAA1B,EAA+B;AAC9B,QAAI6M,GAAG,GAAG,CAAV;;AACA,OAAG;AACFA,MAAAA,GAAG,IAAIjF,IAAI,GAAG,CAAd;AACAA,MAAAA,IAAI,KAAK,CAAT;AACAiF,MAAAA,GAAG,KAAK,CAAR;AACA,KAJD,QAIS,EAAE7M,GAAF,GAAQ,CAJjB;;AAKA,WAAO6M,GAAG,IAAI,CAAd;AACA;AAED;AACA;AACA;;;AACA,WAASnB,SAAT,GAAqB;AACpB,QAAIrE,QAAQ,GAAG,CAAf,EAAkB;AACjBlE,MAAAA,SAAS,CAACiE,MAAD,CAAT;AACA,KAFD,MAEO,IAAIC,QAAQ,GAAG,CAAf,EAAkB;AACxBvE,MAAAA,QAAQ,CAACsE,MAAD,CAAR;AACA;;AACDA,IAAAA,MAAM,GAAG,CAAT;AACAC,IAAAA,QAAQ,GAAG,CAAX;AACA;;AAED,WAASnE,OAAT,GAAmB;AAClB,QAAI4J,CAAJ,EAAOzM,CAAP;;AACA,QAAI2C,MAAM,KAAK,CAAf,EAAkB;AACjB8J,MAAAA,CAAC,GAAGrK,SAAS,EAAb;;AACA,UAAI7B,KAAK,KAAK,IAAd,EAAoB;AACnBA,QAAAA,KAAK,GAAGC,KAAK,GAAGiM,CAAhB;AACA,OAFD,MAEO;AACNjM,QAAAA,KAAK,GAAGA,KAAK,CAACd,IAAN,GAAa+M,CAArB;AACA;;AACDA,MAAAA,CAAC,CAAC9M,GAAF,GAAQgD,MAAM,GAAGD,MAAjB,CAPiB,CAQjB;;AACA,WAAK1C,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGyM,CAAC,CAAC9M,GAAlB,EAAuBK,CAAC,EAAxB,EAA4B;AAC3ByM,QAAAA,CAAC,CAAC7M,GAAF,CAAMI,CAAN,IAAWK,MAAM,CAACqC,MAAM,GAAG1C,CAAV,CAAjB;AACA;;AACD2C,MAAAA,MAAM,GAAGD,MAAM,GAAG,CAAlB;AACA;AACD;;AAEM,WAASgK,OAAT,CAAiBC,GAAjB,EAAsB5M,KAAtB,EAA6B;AACnC,QAAIC,CAAJ,EAAOuE,CAAP,EAAUN,IAAV;AAEAG,IAAAA,YAAY,GAAGuI,GAAf;AACAxI,IAAAA,WAAW,GAAG,CAAd;;AACA,QAAI,OAAOpE,KAAP,KAAiB,WAArB,EAAkC;AACjCA,MAAAA,KAAK,GAAGE,aAAR;AACA;;AACDH,IAAAA,aAAa,CAACC,KAAD,CAAb;AAEAkE,IAAAA,IAAI,GAAG,EAAP;;AAEA,OAAG;AACFjE,MAAAA,CAAC,GAAGmH,gBAAgB,CAAClD,IAAD,EAAOA,IAAI,CAACI,MAAZ,EAAoB,IAApB,CAApB;AACA,KAFD,QAESrE,CAAC,GAAG,CAFb;;AAIAoE,IAAAA,YAAY,GAAG,IAAf,CAhBmC,CAgBd;;AACrB,WAAOH,IAAP;AACA;;qBAlBeyI,O;;;;;;;;;;;AAzoDhB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACIpK,MAAAA,K,GAAQ,K;AACX8I,MAAAA,Y,GAAe,C;AACfE,MAAAA,Y,GAAe,C;AACfE,MAAAA,S,GAAY,C;AAGZvL,MAAAA,a,GAAgB,C;AAChB2E,MAAAA,W,GAAc,K;AACdgI,MAAAA,Q,GAAW,K;AAEXhK,MAAAA,S,GAAY,OAAO,C;AACnBwD,MAAAA,W,GAAc,IAAI9D,K;AAClBc,MAAAA,S,GAAY,C;AACZ0C,MAAAA,S,GAAY,G;AACZ+G,MAAAA,I,GAAO,E;AAEPf,MAAAA,W,GAAc,M;AAOdgB,MAAAA,S,GAAY,E;AACZf,MAAAA,Y,GAAeD,W;AACftH,MAAAA,S,GAAY,KAAKsI,S;AACjBzJ,MAAAA,S,GAAYmB,SAAS,GAAG,C;AACxBjB,MAAAA,K,GAAQjB,KAAK,GAAG,C;AAChBsD,MAAAA,G,GAAM,C;AACNgB,MAAAA,O,GAAU,I;AACV3B,MAAAA,a,GAAgBa,SAAS,GAAG1C,SAAZ,GAAwB,C;AACxCuC,MAAAA,Q,GAAWrD,KAAK,GAAG2C,a;AACnB2E,MAAAA,Q,GAAW,C;AACXlC,MAAAA,Q,GAAW,E;AACXG,MAAAA,W,GAAc,C;AACdC,MAAAA,Y,GAAe,E;AACfL,MAAAA,Q,GAAW,G;AACXS,MAAAA,S,GAAY,G;AACZhH,MAAAA,O,GAAUuG,QAAQ,GAAG,CAAX,GAAeK,Y;AACzB9G,MAAAA,O,GAAU,E;AACVK,MAAAA,Q,GAAW,E;AACX+I,MAAAA,O,GAAU,E;AACVC,MAAAA,S,GAAY,E;AACZC,MAAAA,W,GAAc,E;AACdxJ,MAAAA,S,GAAY,IAAII,OAAJ,GAAc,C;AAC1BgC,MAAAA,O,GAAU2I,QAAQ,CAAC,CAACiB,SAAS,GAAG1J,SAAZ,GAAwB,CAAzB,IAA8BA,SAA/B,EAA0C,EAA1C,C;AAOlB/C,MAAAA,M,GAAS,I;;AAsDV,UAAIyL,WAAW,GAAGc,QAAlB,EAA4B;AAC3BG,QAAAA,OAAO,CAACC,KAAR,CAAc,8BAAd;AACA;;AACD,UAAK1K,KAAK,IAAI,CAAV,GAAgB,KAAKuK,IAAzB,EAAgC;AAC/BE,QAAAA,OAAO,CAACC,KAAR,CAAc,2BAAd;AACA;;AACD,UAAIF,SAAS,GAAGD,IAAI,GAAG,CAAvB,EAA0B;AACzBE,QAAAA,OAAO,CAACC,KAAR,CAAc,+BAAd;AACA;;AACD,UAAIF,SAAS,GAAG,CAAZ,IAAiBhH,SAAS,KAAK,GAAnC,EAAwC;AACvCiH,QAAAA,OAAO,CAACC,KAAR,CAAc,wBAAd;AACA;;AAuCGxF,MAAAA,W,GAAc,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,EAAiD,CAAjD,EAAoD,CAApD,EAAuD,CAAvD,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,CAAnE,EAAsE,CAAtE,EAAyE,CAAzE,EAA4E,CAA5E,EAA+E,CAA/E,EAAkF,CAAlF,EAAqF,CAArF,C;AACdG,MAAAA,W,GAAc,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,EAAiD,CAAjD,EAAoD,CAApD,EAAuD,CAAvD,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,EAAnE,EAAuE,EAAvE,EAA2E,EAA3E,EAA+E,EAA/E,EAAmF,EAAnF,EAAuF,EAAvF,EAA2F,EAA3F,EAA+F,EAA/F,C;AACdC,MAAAA,Y,GAAe,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,EAA2C,CAA3C,EAA8C,CAA9C,EAAiD,CAAjD,EAAoD,CAApD,EAAuD,CAAvD,C;AACf8C,MAAAA,Q,GAAW,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,EAA5B,EAAgC,CAAhC,EAAmC,EAAnC,EAAuC,CAAvC,EAA0C,EAA1C,EAA8C,CAA9C,EAAiD,EAAjD,EAAqD,CAArD,EAAwD,EAAxD,EAA4D,CAA5D,EAA+D,EAA/D,C;AACXhG,MAAAA,mB,GAAsB,CACzB,IAAI1F,oBAAJ,CAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CADyB,EAEzB,IAAIA,oBAAJ,CAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFyB,EAGzB,IAAIA,oBAAJ,CAAyB,CAAzB,EAA4B,CAA5B,EAA+B,EAA/B,EAAmC,CAAnC,CAHyB,EAIzB,IAAIA,oBAAJ,CAAyB,CAAzB,EAA4B,CAA5B,EAA+B,EAA/B,EAAmC,EAAnC,CAJyB,EAKzB,IAAIA,oBAAJ,CAAyB,CAAzB,EAA4B,CAA5B,EAA+B,EAA/B,EAAmC,EAAnC,CALyB,EAMzB,IAAIA,oBAAJ,CAAyB,CAAzB,EAA4B,EAA5B,EAAgC,EAAhC,EAAoC,EAApC,CANyB,EAOzB,IAAIA,oBAAJ,CAAyB,CAAzB,EAA4B,EAA5B,EAAgC,GAAhC,EAAqC,GAArC,CAPyB,EAQzB,IAAIA,oBAAJ,CAAyB,CAAzB,EAA4B,EAA5B,EAAgC,GAAhC,EAAqC,GAArC,CARyB,EASzB,IAAIA,oBAAJ,CAAyB,EAAzB,EAA6B,GAA7B,EAAkC,GAAlC,EAAuC,IAAvC,CATyB,EAUzB,IAAIA,oBAAJ,CAAyB,EAAzB,EAA6B,GAA7B,EAAkC,GAAlC,EAAuC,IAAvC,CAVyB,C;AAs5CtBuN,MAAAA,Q,GAAW,E", "sourcesContent": ["/*\n * $Id: rawdeflate.js,v 0.3 2009/03/01 19:05:05 dankogai Exp dankogai $\n *\n * Original:\n *   http://www.onicos.com/staff/iz/amuse/javascript/expert/deflate.txt\n */\n\n/* Copyright (C) 1999 <PERSON><PERSON><PERSON> <<EMAIL>>\n * Version: 1.0.1\n * LastModified: Dec 25 1999\n */\n\n/* Interface:\n * data = deflate(src);\n */\n\n\n/* constant parameters */\nvar WSIZE = 32768, // Sliding Window size\n\tSTORED_BLOCK = 0,\n\tSTATIC_TREES = 1,\n\tDYN_TREES = 2,\n\n/* for deflate */\n\tDEFAULT_LEVEL = 6,\n\tFULL_SEARCH = false,\n\tINBUFSIZ = 32768, // Input buffer size\n\t//INBUF_EXTRA = 64, // Extra buffer\n\tOUTBUFSIZ = 1024 * 8,\n\twindow_size = 2 * WSIZE,\n\tMIN_MATCH = 3,\n\tMAX_MATCH = 258,\n\tBITS = 16,\n// for SMALL_MEM\n\tLIT_BUFSIZE = 0x2000,\n//\t\tHASH_BITS = 13,\n//for MEDIUM_MEM\n//\tLIT_BUFSIZE = 0x4000,\n//\tHASH_BITS = 14,\n// for BIG_MEM\n//\tLIT_BUFSIZE = 0x8000,\n\tHASH_BITS = 15,\n\tDIST_BUFSIZE = LIT_BUFSIZE,\n\tHASH_SIZE = 1 << HASH_BITS,\n\tHASH_MASK = HASH_SIZE - 1,\n\tWMASK = WSIZE - 1,\n\tNIL = 0, // Tail of hash chains\n\tTOO_FAR = 4096,\n\tMIN_LOOKAHEAD = MAX_MATCH + MIN_MATCH + 1,\n\tMAX_DIST = WSIZE - MIN_LOOKAHEAD,\n\tSMALLEST = 1,\n\tMAX_BITS = 15,\n\tMAX_BL_BITS = 7,\n\tLENGTH_CODES = 29,\n\tLITERALS = 256,\n\tEND_BLOCK = 256,\n\tL_CODES = LITERALS + 1 + LENGTH_CODES,\n\tD_CODES = 30,\n\tBL_CODES = 19,\n\tREP_3_6 = 16,\n\tREPZ_3_10 = 17,\n\tREPZ_11_138 = 18,\n\tHEAP_SIZE = 2 * L_CODES + 1,\n\tH_SHIFT = parseInt((HASH_BITS + MIN_MATCH - 1) / MIN_MATCH, 10),\n\n/* variables */\n\tfree_queue,\n\tqhead,\n\tqtail,\n\tinitflag,\n\toutbuf = null,\n\toutcnt,\n\toutoff,\n\tcomplete,\n\twindow,\n\td_buf,\n\tl_buf,\n\tprev,\n\tbi_buf,\n\tbi_valid,\n\tblock_start,\n\tins_h,\n\thash_head,\n\tprev_match,\n\tmatch_available,\n\tmatch_length,\n\tprev_length,\n\tstrstart,\n\tmatch_start,\n\teofile,\n\tlookahead,\n\tmax_chain_length,\n\tmax_lazy_match,\n\tcompr_level,\n\tgood_match,\n\tnice_match,\n\tdyn_ltree,\n\tdyn_dtree,\n\tstatic_ltree,\n\tstatic_dtree,\n\tbl_tree,\n\tl_desc,\n\td_desc,\n\tbl_desc,\n\tbl_count,\n\theap,\n\theap_len,\n\theap_max,\n\tdepth,\n\tlength_code,\n\tdist_code,\n\tbase_length,\n\tbase_dist,\n\tflag_buf,\n\tlast_lit,\n\tlast_dist,\n\tlast_flags,\n\tflags,\n\tflag_bit,\n\topt_len,\n\tstatic_len,\n\tdeflate_data,\n\tdeflate_pos;\n\nif (LIT_BUFSIZE > INBUFSIZ) {\n\tconsole.error(\"error: INBUFSIZ is too small\");\n}\nif ((WSIZE << 1) > (1 << BITS)) {\n\tconsole.error(\"error: WSIZE is too large\");\n}\nif (HASH_BITS > BITS - 1) {\n\tconsole.error(\"error: HASH_BITS is too large\");\n}\nif (HASH_BITS < 8 || MAX_MATCH !== 258) {\n\tconsole.error(\"error: Code too clever\");\n}\n\n/* objects (deflate) */\n\nfunction DeflateCT() {\n\tthis.fc = 0; // frequency count or bit string\n\tthis.dl = 0; // father node in Huffman tree or length of bit string\n}\n\nfunction DeflateTreeDesc() {\n\tthis.dyn_tree = null; // the dynamic tree\n\tthis.static_tree = null; // corresponding static tree or NULL\n\tthis.extra_bits = null; // extra bits for each code or NULL\n\tthis.extra_base = 0; // base index for extra_bits\n\tthis.elems = 0; // max number of elements in the tree\n\tthis.max_length = 0; // max bit length for the codes\n\tthis.max_code = 0; // largest code with non zero frequency\n}\n\n/* Values for max_lazy_match, good_match and max_chain_length, depending on\n\t* the desired pack level (0..9). The values given below have been tuned to\n\t* exclude worst case performance for pathological files. Better values may be\n\t* found for specific files.\n\t*/\nfunction DeflateConfiguration(a, b, c, d) {\n\tthis.good_length = a; // reduce lazy search above this match length\n\tthis.max_lazy = b; // do not perform lazy search above this match length\n\tthis.nice_length = c; // quit search above this match length\n\tthis.max_chain = d;\n}\n\nfunction DeflateBuffer() {\n\tthis.next = null;\n\tthis.len = 0;\n\tthis.ptr = []; // new Array(OUTBUFSIZ); // ptr.length is never read\n\tthis.off = 0;\n}\n\n/* constant tables */\nvar extra_lbits = [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0];\nvar extra_dbits = [0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13];\nvar extra_blbits = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7];\nvar bl_order = [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15];\nvar configuration_table = [\n\tnew DeflateConfiguration(0, 0, 0, 0),\n\tnew DeflateConfiguration(4, 4, 8, 4),\n\tnew DeflateConfiguration(4, 5, 16, 8),\n\tnew DeflateConfiguration(4, 6, 32, 32),\n\tnew DeflateConfiguration(4, 4, 16, 16),\n\tnew DeflateConfiguration(8, 16, 32, 32),\n\tnew DeflateConfiguration(8, 16, 128, 128),\n\tnew DeflateConfiguration(8, 32, 128, 256),\n\tnew DeflateConfiguration(32, 128, 258, 1024),\n\tnew DeflateConfiguration(32, 258, 258, 4096)\n];\n\n\n/* routines (deflate) */\n\nfunction deflate_start(level) {\n\tvar i;\n\n\tif (!level) {\n\t\tlevel = DEFAULT_LEVEL;\n\t} else if (level < 1) {\n\t\tlevel = 1;\n\t} else if (level > 9) {\n\t\tlevel = 9;\n\t}\n\n\tcompr_level = level;\n\tinitflag = false;\n\teofile = false;\n\tif (outbuf !== null) {\n\t\treturn;\n\t}\n\n\tfree_queue = qhead = qtail = null;\n\toutbuf = []; // new Array(OUTBUFSIZ); // outbuf.length never called\n\twindow = []; // new Array(window_size); // window.length never called\n\td_buf = []; // new Array(DIST_BUFSIZE); // d_buf.length never called\n\tl_buf = []; // new Array(INBUFSIZ + INBUF_EXTRA); // l_buf.length never called\n\tprev = []; // new Array(1 << BITS); // prev.length never called\n\n\tdyn_ltree = [];\n\tfor (i = 0; i < HEAP_SIZE; i++) {\n\t\tdyn_ltree[i] = new DeflateCT();\n\t}\n\tdyn_dtree = [];\n\tfor (i = 0; i < 2 * D_CODES + 1; i++) {\n\t\tdyn_dtree[i] = new DeflateCT();\n\t}\n\tstatic_ltree = [];\n\tfor (i = 0; i < L_CODES + 2; i++) {\n\t\tstatic_ltree[i] = new DeflateCT();\n\t}\n\tstatic_dtree = [];\n\tfor (i = 0; i < D_CODES; i++) {\n\t\tstatic_dtree[i] = new DeflateCT();\n\t}\n\tbl_tree = [];\n\tfor (i = 0; i < 2 * BL_CODES + 1; i++) {\n\t\tbl_tree[i] = new DeflateCT();\n\t}\n\tl_desc = new DeflateTreeDesc();\n\td_desc = new DeflateTreeDesc();\n\tbl_desc = new DeflateTreeDesc();\n\tbl_count = []; // new Array(MAX_BITS+1); // bl_count.length never called\n\theap = []; // new Array(2*L_CODES+1); // heap.length never called\n\tdepth = []; // new Array(2*L_CODES+1); // depth.length never called\n\tlength_code = []; // new Array(MAX_MATCH-MIN_MATCH+1); // length_code.length never called\n\tdist_code = []; // new Array(512); // dist_code.length never called\n\tbase_length = []; // new Array(LENGTH_CODES); // base_length.length never called\n\tbase_dist = []; // new Array(D_CODES); // base_dist.length never called\n\tflag_buf = []; // new Array(parseInt(LIT_BUFSIZE / 8, 10)); // flag_buf.length never called\n}\n\nfunction deflate_end() {\n\tfree_queue = qhead = qtail = null;\n\toutbuf = null;\n\twindow = null;\n\td_buf = null;\n\tl_buf = null;\n\tprev = null;\n\tdyn_ltree = null;\n\tdyn_dtree = null;\n\tstatic_ltree = null;\n\tstatic_dtree = null;\n\tbl_tree = null;\n\tl_desc = null;\n\td_desc = null;\n\tbl_desc = null;\n\tbl_count = null;\n\theap = null;\n\tdepth = null;\n\tlength_code = null;\n\tdist_code = null;\n\tbase_length = null;\n\tbase_dist = null;\n\tflag_buf = null;\n}\n\nfunction reuse_queue(p) {\n\tp.next = free_queue;\n\tfree_queue = p;\n}\n\nfunction new_queue() {\n\tvar p;\n\n\tif (free_queue !== null) {\n\t\tp = free_queue;\n\t\tfree_queue = free_queue.next;\n\t} else {\n\t\tp = new DeflateBuffer();\n\t}\n\tp.next = null;\n\tp.len = p.off = 0;\n\n\treturn p;\n}\n\nfunction head1(i) {\n\treturn prev[WSIZE + i];\n}\n\nfunction head2(i, val) {\n\treturn (prev[WSIZE + i] = val);\n}\n\n/* put_byte is used for the compressed output, put_ubyte for the\n\t* uncompressed output. However unlzw() uses window for its\n\t* suffix table instead of its output buffer, so it does not use put_ubyte\n\t* (to be cleaned up).\n\t*/\nfunction put_byte(c) {\n\toutbuf[outoff + outcnt++] = c;\n\tif (outoff + outcnt === OUTBUFSIZ) {\n\t\tqoutbuf();\n\t}\n}\n\n/* Output a 16 bit value, lsb first */\nfunction put_short(w) {\n\tw &= 0xffff;\n\tif (outoff + outcnt < OUTBUFSIZ - 2) {\n\t\toutbuf[outoff + outcnt++] = (w & 0xff);\n\t\toutbuf[outoff + outcnt++] = (w >>> 8);\n\t} else {\n\t\tput_byte(w & 0xff);\n\t\tput_byte(w >>> 8);\n\t}\n}\n\n/* ==========================================================================\n\t* Insert string s in the dictionary and set match_head to the previous head\n\t* of the hash chain (the most recent string with same hash key). Return\n\t* the previous length of the hash chain.\n\t* IN  assertion: all calls to to INSERT_STRING are made with consecutive\n\t*    input characters and the first MIN_MATCH bytes of s are valid\n\t*    (except for the last MIN_MATCH-1 bytes of the input file).\n\t*/\nfunction INSERT_STRING() {\n\tins_h = ((ins_h << H_SHIFT) ^ (window[strstart + MIN_MATCH - 1] & 0xff)) & HASH_MASK;\n\thash_head = head1(ins_h);\n\tprev[strstart & WMASK] = hash_head;\n\thead2(ins_h, strstart);\n}\n\n/* Send a code of the given tree. c and tree must not have side effects */\nfunction SEND_CODE(c, tree) {\n\tsend_bits(tree[c].fc, tree[c].dl);\n}\n\n/* Mapping from a distance to a distance code. dist is the distance - 1 and\n\t* must not have side effects. dist_code[256] and dist_code[257] are never\n\t* used.\n\t*/\nfunction D_CODE(dist) {\n\treturn (dist < 256 ? dist_code[dist] : dist_code[256 + (dist >> 7)]) & 0xff;\n}\n\n/* ==========================================================================\n\t* Compares to subtrees, using the tree depth as tie breaker when\n\t* the subtrees have equal frequency. This minimizes the worst case length.\n\t*/\nfunction SMALLER(tree, n, m) {\n\treturn tree[n].fc < tree[m].fc || (tree[n].fc === tree[m].fc && depth[n] <= depth[m]);\n}\n\n/* ==========================================================================\n\t* read string data\n\t*/\nfunction read_buff(buff, offset, n) {\n\tvar i;\n\tfor (i = 0; i < n && deflate_pos < deflate_data.length; i++) {\n\t\tbuff[offset + i] = deflate_data[deflate_pos++] & 0xff;\n\t}\n\treturn i;\n}\n\n/* ==========================================================================\n\t* Initialize the \"longest match\" routines for a new file\n\t*/\nfunction lm_init() {\n\tvar j;\n\n\t// Initialize the hash table. */\n\tfor (j = 0; j < HASH_SIZE; j++) {\n\t\t// head2(j, NIL);\n\t\tprev[WSIZE + j] = 0;\n\t}\n\t// prev will be initialized on the fly */\n\n\t// Set the default configuration parameters:\n\tmax_lazy_match = configuration_table[compr_level].max_lazy;\n\tgood_match = configuration_table[compr_level].good_length;\n\tif (!FULL_SEARCH) {\n\t\tnice_match = configuration_table[compr_level].nice_length;\n\t}\n\tmax_chain_length = configuration_table[compr_level].max_chain;\n\n\tstrstart = 0;\n\tblock_start = 0;\n\n\tlookahead = read_buff(window, 0, 2 * WSIZE);\n\tif (lookahead <= 0) {\n\t\teofile = true;\n\t\tlookahead = 0;\n\t\treturn;\n\t}\n\teofile = false;\n\t// Make sure that we always have enough lookahead. This is important\n\t// if input comes from a device such as a tty.\n\twhile (lookahead < MIN_LOOKAHEAD && !eofile) {\n\t\tfill_window();\n\t}\n\n\t// If lookahead < MIN_MATCH, ins_h is garbage, but this is\n\t// not important since only literal bytes will be emitted.\n\tins_h = 0;\n\tfor (j = 0; j < MIN_MATCH - 1; j++) {\n\t\t// UPDATE_HASH(ins_h, window[j]);\n\t\tins_h = ((ins_h << H_SHIFT) ^ (window[j] & 0xff)) & HASH_MASK;\n\t}\n}\n\n/* ==========================================================================\n\t* Set match_start to the longest match starting at the given string and\n\t* return its length. Matches shorter or equal to prev_length are discarded,\n\t* in which case the result is equal to prev_length and match_start is\n\t* garbage.\n\t* IN assertions: cur_match is the head of the hash chain for the current\n\t*   string (strstart) and its distance is <= MAX_DIST, and prev_length >= 1\n\t*/\nfunction longest_match(cur_match) {\n\tvar chain_length = max_chain_length; // max hash chain length\n\tvar scanp = strstart; // current string\n\tvar matchp; // matched string\n\tvar len; // length of current match\n\tvar best_len = prev_length; // best match length so far\n\n\t// Stop when cur_match becomes <= limit. To simplify the code,\n\t// we prevent matches with the string of window index 0.\n\tvar limit = (strstart > MAX_DIST ? strstart - MAX_DIST : NIL);\n\n\tvar strendp = strstart + MAX_MATCH;\n\tvar scan_end1 = window[scanp + best_len - 1];\n\tvar scan_end = window[scanp + best_len];\n\n\tvar i, broke;\n\n\t// Do not waste too much time if we already have a good match: */\n\tif (prev_length >= good_match) {\n\t\tchain_length >>= 2;\n\t}\n\n\t// Assert(encoder->strstart <= window_size-MIN_LOOKAHEAD, \"insufficient lookahead\");\n\n\tdo {\n\t\t// Assert(cur_match < encoder->strstart, \"no future\");\n\t\tmatchp = cur_match;\n\n\t\t// Skip to next match if the match length cannot increase\n\t\t// or if the match length is less than 2:\n\t\tif (window[matchp + best_len] !== scan_end  ||\n\t\t\t\twindow[matchp + best_len - 1] !== scan_end1 ||\n\t\t\t\twindow[matchp] !== window[scanp] ||\n\t\t\t\twindow[++matchp] !== window[scanp + 1]) {\n\t\t\tcontinue;\n\t\t}\n\n\t\t// The check at best_len-1 can be removed because it will be made\n\t\t// again later. (This heuristic is not always a win.)\n\t\t// It is not necessary to compare scan[2] and match[2] since they\n\t\t// are always equal when the other bytes match, given that\n\t\t// the hash keys are equal and that HASH_BITS >= 8.\n\t\tscanp += 2;\n\t\tmatchp++;\n\n\t\t// We check for insufficient lookahead only every 8th comparison;\n\t\t// the 256th check will be made at strstart+258.\n\t\twhile (scanp < strendp) {\n\t\t\tbroke = false;\n\t\t\tfor (i = 0; i < 8; i += 1) {\n\t\t\t\tscanp += 1;\n\t\t\t\tmatchp += 1;\n\t\t\t\tif (window[scanp] !== window[matchp]) {\n\t\t\t\t\tbroke = true;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (broke) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tlen = MAX_MATCH - (strendp - scanp);\n\t\tscanp = strendp - MAX_MATCH;\n\n\t\tif (len > best_len) {\n\t\t\tmatch_start = cur_match;\n\t\t\tbest_len = len;\n\t\t\tif (FULL_SEARCH) {\n\t\t\t\tif (len >= MAX_MATCH) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (len >= nice_match) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tscan_end1 = window[scanp + best_len - 1];\n\t\t\tscan_end = window[scanp + best_len];\n\t\t}\n\t} while ((cur_match = prev[cur_match & WMASK]) > limit && --chain_length !== 0);\n\n\treturn best_len;\n}\n\n/* ==========================================================================\n\t* Fill the window when the lookahead becomes insufficient.\n\t* Updates strstart and lookahead, and sets eofile if end of input file.\n\t* IN assertion: lookahead < MIN_LOOKAHEAD && strstart + lookahead > 0\n\t* OUT assertions: at least one byte has been read, or eofile is set;\n\t*    file reads are performed for at least two bytes (required for the\n\t*    translate_eol option).\n\t*/\nfunction fill_window() {\n\tvar n, m;\n\n\t// Amount of free space at the end of the window.\n\tvar more = window_size - lookahead - strstart;\n\n\t// If the window is almost full and there is insufficient lookahead,\n\t// move the upper half to the lower one to make room in the upper half.\n\tif (more === -1) {\n\t\t// Very unlikely, but possible on 16 bit machine if strstart == 0\n\t\t// and lookahead == 1 (input done one byte at time)\n\t\tmore--;\n\t} else if (strstart >= WSIZE + MAX_DIST) {\n\t\t// By the IN assertion, the window is not empty so we can't confuse\n\t\t// more == 0 with more == 64K on a 16 bit machine.\n\t\t// Assert(window_size == (ulg)2*WSIZE, \"no sliding with BIG_MEM\");\n\n\t\t// System.arraycopy(window, WSIZE, window, 0, WSIZE);\n\t\tfor (n = 0; n < WSIZE; n++) {\n\t\t\twindow[n] = window[n + WSIZE];\n\t\t}\n\n\t\tmatch_start -= WSIZE;\n\t\tstrstart    -= WSIZE; /* we now have strstart >= MAX_DIST: */\n\t\tblock_start -= WSIZE;\n\n\t\tfor (n = 0; n < HASH_SIZE; n++) {\n\t\t\tm = head1(n);\n\t\t\thead2(n, m >= WSIZE ? m - WSIZE : NIL);\n\t\t}\n\t\tfor (n = 0; n < WSIZE; n++) {\n\t\t// If n is not on any hash chain, prev[n] is garbage but\n\t\t// its value will never be used.\n\t\t\tm = prev[n];\n\t\t\tprev[n] = (m >= WSIZE ? m - WSIZE : NIL);\n\t\t}\n\t\tmore += WSIZE;\n\t}\n\t// At this point, more >= 2\n\tif (!eofile) {\n\t\tn = read_buff(window, strstart + lookahead, more);\n\t\tif (n <= 0) {\n\t\t\teofile = true;\n\t\t} else {\n\t\t\tlookahead += n;\n\t\t}\n\t}\n}\n\n/* ==========================================================================\n\t* Processes a new input file and return its compressed length. This\n\t* function does not perform lazy evaluationof matches and inserts\n\t* new strings in the dictionary only for unmatched strings or for short\n\t* matches. It is used only for the fast compression options.\n\t*/\nfunction deflate_fast() {\n\twhile (lookahead !== 0 && qhead === null) {\n\t\tvar flush; // set if current block must be flushed\n\n\t\t// Insert the string window[strstart .. strstart+2] in the\n\t\t// dictionary, and set hash_head to the head of the hash chain:\n\t\tINSERT_STRING();\n\n\t\t// Find the longest match, discarding those <= prev_length.\n\t\t// At this point we have always match_length < MIN_MATCH\n\t\tif (hash_head !== NIL && strstart - hash_head <= MAX_DIST) {\n\t\t\t// To simplify the code, we prevent matches with the string\n\t\t\t// of window index 0 (in particular we have to avoid a match\n\t\t\t// of the string with itself at the start of the input file).\n\t\t\tmatch_length = longest_match(hash_head);\n\t\t\t// longest_match() sets match_start */\n\t\t\tif (match_length > lookahead) {\n\t\t\t\tmatch_length = lookahead;\n\t\t\t}\n\t\t}\n\t\tif (match_length >= MIN_MATCH) {\n\t\t\t// check_match(strstart, match_start, match_length);\n\n\t\t\tflush = ct_tally(strstart - match_start, match_length - MIN_MATCH);\n\t\t\tlookahead -= match_length;\n\n\t\t\t// Insert new strings in the hash table only if the match length\n\t\t\t// is not too large. This saves time but degrades compression.\n\t\t\tif (match_length <= max_lazy_match) {\n\t\t\t\tmatch_length--; // string at strstart already in hash table\n\t\t\t\tdo {\n\t\t\t\t\tstrstart++;\n\t\t\t\t\tINSERT_STRING();\n\t\t\t\t\t// strstart never exceeds WSIZE-MAX_MATCH, so there are\n\t\t\t\t\t// always MIN_MATCH bytes ahead. If lookahead < MIN_MATCH\n\t\t\t\t\t// these bytes are garbage, but it does not matter since\n\t\t\t\t\t// the next lookahead bytes will be emitted as literals.\n\t\t\t\t} while (--match_length !== 0);\n\t\t\t\tstrstart++;\n\t\t\t} else {\n\t\t\t\tstrstart += match_length;\n\t\t\t\tmatch_length = 0;\n\t\t\t\tins_h = window[strstart] & 0xff;\n\t\t\t\t// UPDATE_HASH(ins_h, window[strstart + 1]);\n\t\t\t\tins_h = ((ins_h << H_SHIFT) ^ (window[strstart + 1] & 0xff)) & HASH_MASK;\n\n\t\t\t//#if MIN_MATCH !== 3\n\t\t\t//\t\tCall UPDATE_HASH() MIN_MATCH-3 more times\n\t\t\t//#endif\n\n\t\t\t}\n\t\t} else {\n\t\t\t// No match, output a literal byte */\n\t\t\tflush = ct_tally(0, window[strstart] & 0xff);\n\t\t\tlookahead--;\n\t\t\tstrstart++;\n\t\t}\n\t\tif (flush) {\n\t\t\tflush_block(0);\n\t\t\tblock_start = strstart;\n\t\t}\n\n\t\t// Make sure that we always have enough lookahead, except\n\t\t// at the end of the input file. We need MAX_MATCH bytes\n\t\t// for the next match, plus MIN_MATCH bytes to insert the\n\t\t// string following the next match.\n\t\twhile (lookahead < MIN_LOOKAHEAD && !eofile) {\n\t\t\tfill_window();\n\t\t}\n\t}\n}\n\nfunction deflate_better() {\n\t// Process the input block. */\n\twhile (lookahead !== 0 && qhead === null) {\n\t\t// Insert the string window[strstart .. strstart+2] in the\n\t\t// dictionary, and set hash_head to the head of the hash chain:\n\t\tINSERT_STRING();\n\n\t\t// Find the longest match, discarding those <= prev_length.\n\t\tprev_length = match_length;\n\t\tprev_match = match_start;\n\t\tmatch_length = MIN_MATCH - 1;\n\n\t\tif (hash_head !== NIL && prev_length < max_lazy_match && strstart - hash_head <= MAX_DIST) {\n\t\t\t// To simplify the code, we prevent matches with the string\n\t\t\t// of window index 0 (in particular we have to avoid a match\n\t\t\t// of the string with itself at the start of the input file).\n\t\t\tmatch_length = longest_match(hash_head);\n\t\t\t// longest_match() sets match_start */\n\t\t\tif (match_length > lookahead) {\n\t\t\t\tmatch_length = lookahead;\n\t\t\t}\n\n\t\t\t// Ignore a length 3 match if it is too distant: */\n\t\t\tif (match_length === MIN_MATCH && strstart - match_start > TOO_FAR) {\n\t\t\t\t// If prev_match is also MIN_MATCH, match_start is garbage\n\t\t\t\t// but we will ignore the current match anyway.\n\t\t\t\tmatch_length--;\n\t\t\t}\n\t\t}\n\t\t// If there was a match at the previous step and the current\n\t\t// match is not better, output the previous match:\n\t\tif (prev_length >= MIN_MATCH && match_length <= prev_length) {\n\t\t\tvar flush; // set if current block must be flushed\n\n\t\t\t// check_match(strstart - 1, prev_match, prev_length);\n\t\t\tflush = ct_tally(strstart - 1 - prev_match, prev_length - MIN_MATCH);\n\n\t\t\t// Insert in hash table all strings up to the end of the match.\n\t\t\t// strstart-1 and strstart are already inserted.\n\t\t\tlookahead -= prev_length - 1;\n\t\t\tprev_length -= 2;\n\t\t\tdo {\n\t\t\t\tstrstart++;\n\t\t\t\tINSERT_STRING();\n\t\t\t\t// strstart never exceeds WSIZE-MAX_MATCH, so there are\n\t\t\t\t// always MIN_MATCH bytes ahead. If lookahead < MIN_MATCH\n\t\t\t\t// these bytes are garbage, but it does not matter since the\n\t\t\t\t// next lookahead bytes will always be emitted as literals.\n\t\t\t} while (--prev_length !== 0);\n\t\t\tmatch_available = false;\n\t\t\tmatch_length = MIN_MATCH - 1;\n\t\t\tstrstart++;\n\t\t\tif (flush) {\n\t\t\t\tflush_block(0);\n\t\t\t\tblock_start = strstart;\n\t\t\t}\n\t\t} else if (match_available) {\n\t\t\t// If there was no match at the previous position, output a\n\t\t\t// single literal. If there was a match but the current match\n\t\t\t// is longer, truncate the previous match to a single literal.\n\t\t\tif (ct_tally(0, window[strstart - 1] & 0xff)) {\n\t\t\t\tflush_block(0);\n\t\t\t\tblock_start = strstart;\n\t\t\t}\n\t\t\tstrstart++;\n\t\t\tlookahead--;\n\t\t} else {\n\t\t\t// There is no previous match to compare with, wait for\n\t\t\t// the next step to decide.\n\t\t\tmatch_available = true;\n\t\t\tstrstart++;\n\t\t\tlookahead--;\n\t\t}\n\n\t\t// Make sure that we always have enough lookahead, except\n\t\t// at the end of the input file. We need MAX_MATCH bytes\n\t\t// for the next match, plus MIN_MATCH bytes to insert the\n\t\t// string following the next match.\n\t\twhile (lookahead < MIN_LOOKAHEAD && !eofile) {\n\t\t\tfill_window();\n\t\t}\n\t}\n}\n\nfunction init_deflate() {\n\tif (eofile) {\n\t\treturn;\n\t}\n\tbi_buf = 0;\n\tbi_valid = 0;\n\tct_init();\n\tlm_init();\n\n\tqhead = null;\n\toutcnt = 0;\n\toutoff = 0;\n\n\tif (compr_level <= 3) {\n\t\tprev_length = MIN_MATCH - 1;\n\t\tmatch_length = 0;\n\t} else {\n\t\tmatch_length = MIN_MATCH - 1;\n\t\tmatch_available = false;\n\t}\n\n\tcomplete = false;\n}\n\n/* ==========================================================================\n\t* Same as above, but achieves better compression. We use a lazy\n\t* evaluation for matches: a match is finally adopted only if there is\n\t* no better match at the next window position.\n\t*/\nfunction deflate_internal(buff, off, buff_size) {\n\tvar n;\n\n\tif (!initflag) {\n\t\tinit_deflate();\n\t\tinitflag = true;\n\t\tif (lookahead === 0) { // empty\n\t\t\tcomplete = true;\n\t\t\treturn 0;\n\t\t}\n\t}\n\n\tn = qcopy(buff, off, buff_size);\n\tif (n === buff_size) {\n\t\treturn buff_size;\n\t}\n\n\tif (complete) {\n\t\treturn n;\n\t}\n\n\tif (compr_level <= 3) {\n\t\t// optimized for speed\n\t\tdeflate_fast();\n\t} else {\n\t\tdeflate_better();\n\t}\n\n\tif (lookahead === 0) {\n\t\tif (match_available) {\n\t\t\tct_tally(0, window[strstart - 1] & 0xff);\n\t\t}\n\t\tflush_block(1);\n\t\tcomplete = true;\n\t}\n\n\treturn n + qcopy(buff, n + off, buff_size - n);\n}\n\nfunction qcopy(buff, off, buff_size) {\n\tvar n, i, j;\n\n\tn = 0;\n\twhile (qhead !== null && n < buff_size) {\n\t\ti = buff_size - n;\n\t\tif (i > qhead.len) {\n\t\t\ti = qhead.len;\n\t\t}\n\t\t// System.arraycopy(qhead.ptr, qhead.off, buff, off + n, i);\n\t\tfor (j = 0; j < i; j++) {\n\t\t\tbuff[off + n + j] = qhead.ptr[qhead.off + j];\n\t\t}\n\n\t\tqhead.off += i;\n\t\tqhead.len -= i;\n\t\tn += i;\n\t\tif (qhead.len === 0) {\n\t\t\tvar p;\n\t\t\tp = qhead;\n\t\t\tqhead = qhead.next;\n\t\t\treuse_queue(p);\n\t\t}\n\t}\n\n\tif (n === buff_size) {\n\t\treturn n;\n\t}\n\n\tif (outoff < outcnt) {\n\t\ti = buff_size - n;\n\t\tif (i > outcnt - outoff) {\n\t\t\ti = outcnt - outoff;\n\t\t}\n\t\t// System.arraycopy(outbuf, outoff, buff, off + n, i);\n\t\tfor (j = 0; j < i; j++) {\n\t\t\tbuff[off + n + j] = outbuf[outoff + j];\n\t\t}\n\t\toutoff += i;\n\t\tn += i;\n\t\tif (outcnt === outoff) {\n\t\t\toutcnt = outoff = 0;\n\t\t}\n\t}\n\treturn n;\n}\n\n/* ==========================================================================\n\t* Allocate the match buffer, initialize the various tables and save the\n\t* location of the internal file attribute (ascii/binary) and method\n\t* (DEFLATE/STORE).\n\t*/\nfunction ct_init() {\n\tvar n; // iterates over tree elements\n\tvar bits; // bit counter\n\tvar length; // length value\n\tvar code; // code value\n\tvar dist; // distance index\n\n\tif (static_dtree[0].dl !== 0) {\n\t\treturn; // ct_init already called\n\t}\n\n\tl_desc.dyn_tree = dyn_ltree;\n\tl_desc.static_tree = static_ltree;\n\tl_desc.extra_bits = extra_lbits;\n\tl_desc.extra_base = LITERALS + 1;\n\tl_desc.elems = L_CODES;\n\tl_desc.max_length = MAX_BITS;\n\tl_desc.max_code = 0;\n\n\td_desc.dyn_tree = dyn_dtree;\n\td_desc.static_tree = static_dtree;\n\td_desc.extra_bits = extra_dbits;\n\td_desc.extra_base = 0;\n\td_desc.elems = D_CODES;\n\td_desc.max_length = MAX_BITS;\n\td_desc.max_code = 0;\n\n\tbl_desc.dyn_tree = bl_tree;\n\tbl_desc.static_tree = null;\n\tbl_desc.extra_bits = extra_blbits;\n\tbl_desc.extra_base = 0;\n\tbl_desc.elems = BL_CODES;\n\tbl_desc.max_length = MAX_BL_BITS;\n\tbl_desc.max_code = 0;\n\n\t// Initialize the mapping length (0..255) -> length code (0..28)\n\tlength = 0;\n\tfor (code = 0; code < LENGTH_CODES - 1; code++) {\n\t\tbase_length[code] = length;\n\t\tfor (n = 0; n < (1 << extra_lbits[code]); n++) {\n\t\t\tlength_code[length++] = code;\n\t\t}\n\t}\n\t// Assert (length === 256, \"ct_init: length !== 256\");\n\n\t// Note that the length 255 (match length 258) can be represented\n\t// in two different ways: code 284 + 5 bits or code 285, so we\n\t// overwrite length_code[255] to use the best encoding:\n\tlength_code[length - 1] = code;\n\n\t// Initialize the mapping dist (0..32K) -> dist code (0..29) */\n\tdist = 0;\n\tfor (code = 0; code < 16; code++) {\n\t\tbase_dist[code] = dist;\n\t\tfor (n = 0; n < (1 << extra_dbits[code]); n++) {\n\t\t\tdist_code[dist++] = code;\n\t\t}\n\t}\n\t// Assert (dist === 256, \"ct_init: dist !== 256\");\n\t// from now on, all distances are divided by 128\n\tfor (dist >>= 7; code < D_CODES; code++) {\n\t\tbase_dist[code] = dist << 7;\n\t\tfor (n = 0; n < (1 << (extra_dbits[code] - 7)); n++) {\n\t\t\tdist_code[256 + dist++] = code;\n\t\t}\n\t}\n\t// Assert (dist === 256, \"ct_init: 256+dist !== 512\");\n\n\t// Construct the codes of the static literal tree\n\tfor (bits = 0; bits <= MAX_BITS; bits++) {\n\t\tbl_count[bits] = 0;\n\t}\n\tn = 0;\n\twhile (n <= 143) {\n\t\tstatic_ltree[n++].dl = 8;\n\t\tbl_count[8]++;\n\t}\n\twhile (n <= 255) {\n\t\tstatic_ltree[n++].dl = 9;\n\t\tbl_count[9]++;\n\t}\n\twhile (n <= 279) {\n\t\tstatic_ltree[n++].dl = 7;\n\t\tbl_count[7]++;\n\t}\n\twhile (n <= 287) {\n\t\tstatic_ltree[n++].dl = 8;\n\t\tbl_count[8]++;\n\t}\n\t// Codes 286 and 287 do not exist, but we must include them in the\n\t// tree construction to get a canonical Huffman tree (longest code\n\t// all ones)\n\tgen_codes(static_ltree, L_CODES + 1);\n\n\t// The static distance tree is trivial: */\n\tfor (n = 0; n < D_CODES; n++) {\n\t\tstatic_dtree[n].dl = 5;\n\t\tstatic_dtree[n].fc = bi_reverse(n, 5);\n\t}\n\n\t// Initialize the first block of the first file:\n\tinit_block();\n}\n\n/* ==========================================================================\n\t* Initialize a new block.\n\t*/\nfunction init_block() {\n\tvar n; // iterates over tree elements\n\n\t// Initialize the trees.\n\tfor (n = 0; n < L_CODES;  n++) {\n\t\tdyn_ltree[n].fc = 0;\n\t}\n\tfor (n = 0; n < D_CODES;  n++) {\n\t\tdyn_dtree[n].fc = 0;\n\t}\n\tfor (n = 0; n < BL_CODES; n++) {\n\t\tbl_tree[n].fc = 0;\n\t}\n\n\tdyn_ltree[END_BLOCK].fc = 1;\n\topt_len = static_len = 0;\n\tlast_lit = last_dist = last_flags = 0;\n\tflags = 0;\n\tflag_bit = 1;\n}\n\n/* ==========================================================================\n\t* Restore the heap property by moving down the tree starting at node k,\n\t* exchanging a node with the smallest of its two sons if necessary, stopping\n\t* when the heap property is re-established (each father smaller than its\n\t* two sons).\n\t*\n\t* @param tree- tree to restore\n\t* @param k- node to move down\n\t*/\nfunction pqdownheap(tree, k) {\n\tvar v = heap[k],\n\t\tj = k << 1; // left son of k\n\n\twhile (j <= heap_len) {\n\t\t// Set j to the smallest of the two sons:\n\t\tif (j < heap_len && SMALLER(tree, heap[j + 1], heap[j])) {\n\t\t\tj++;\n\t\t}\n\n\t\t// Exit if v is smaller than both sons\n\t\tif (SMALLER(tree, v, heap[j])) {\n\t\t\tbreak;\n\t\t}\n\n\t\t// Exchange v with the smallest son\n\t\theap[k] = heap[j];\n\t\tk = j;\n\n\t\t// And continue down the tree, setting j to the left son of k\n\t\tj <<= 1;\n\t}\n\theap[k] = v;\n}\n\n/* ==========================================================================\n\t* Compute the optimal bit lengths for a tree and update the total bit length\n\t* for the current block.\n\t* IN assertion: the fields freq and dad are set, heap[heap_max] and\n\t*    above are the tree nodes sorted by increasing frequency.\n\t* OUT assertions: the field len is set to the optimal bit length, the\n\t*     array bl_count contains the frequencies for each bit length.\n\t*     The length opt_len is updated; static_len is also updated if stree is\n\t*     not null.\n\t*/\nfunction gen_bitlen(desc) { // the tree descriptor\n\tvar tree = desc.dyn_tree;\n\tvar extra = desc.extra_bits;\n\tvar base = desc.extra_base;\n\tvar max_code = desc.max_code;\n\tvar max_length = desc.max_length;\n\tvar stree = desc.static_tree;\n\tvar h; // heap index\n\tvar n, m; // iterate over the tree elements\n\tvar bits; // bit length\n\tvar xbits; // extra bits\n\tvar f; // frequency\n\tvar overflow = 0; // number of elements with bit length too large\n\n\tfor (bits = 0; bits <= MAX_BITS; bits++) {\n\t\tbl_count[bits] = 0;\n\t}\n\n\t// In a first pass, compute the optimal bit lengths (which may\n\t// overflow in the case of the bit length tree).\n\ttree[heap[heap_max]].dl = 0; // root of the heap\n\n\tfor (h = heap_max + 1; h < HEAP_SIZE; h++) {\n\t\tn = heap[h];\n\t\tbits = tree[tree[n].dl].dl + 1;\n\t\tif (bits > max_length) {\n\t\t\tbits = max_length;\n\t\t\toverflow++;\n\t\t}\n\t\ttree[n].dl = bits;\n\t\t// We overwrite tree[n].dl which is no longer needed\n\n\t\tif (n > max_code) {\n\t\t\tcontinue; // not a leaf node\n\t\t}\n\n\t\tbl_count[bits]++;\n\t\txbits = 0;\n\t\tif (n >= base) {\n\t\t\txbits = extra[n - base];\n\t\t}\n\t\tf = tree[n].fc;\n\t\topt_len += f * (bits + xbits);\n\t\tif (stree !== null) {\n\t\t\tstatic_len += f * (stree[n].dl + xbits);\n\t\t}\n\t}\n\tif (overflow === 0) {\n\t\treturn;\n\t}\n\n\t// This happens for example on obj2 and pic of the Calgary corpus\n\n\t// Find the first bit length which could increase:\n\tdo {\n\t\tbits = max_length - 1;\n\t\twhile (bl_count[bits] === 0) {\n\t\t\tbits--;\n\t\t}\n\t\tbl_count[bits]--; // move one leaf down the tree\n\t\tbl_count[bits + 1] += 2; // move one overflow item as its brother\n\t\tbl_count[max_length]--;\n\t\t// The brother of the overflow item also moves one step up,\n\t\t// but this does not affect bl_count[max_length]\n\t\toverflow -= 2;\n\t} while (overflow > 0);\n\n\t// Now recompute all bit lengths, scanning in increasing frequency.\n\t// h is still equal to HEAP_SIZE. (It is simpler to reconstruct all\n\t// lengths instead of fixing only the wrong ones. This idea is taken\n\t// from 'ar' written by Haruhiko Okumura.)\n\tfor (bits = max_length; bits !== 0; bits--) {\n\t\tn = bl_count[bits];\n\t\twhile (n !== 0) {\n\t\t\tm = heap[--h];\n\t\t\tif (m > max_code) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (tree[m].dl !== bits) {\n\t\t\t\topt_len += (bits - tree[m].dl) * tree[m].fc;\n\t\t\t\ttree[m].fc = bits;\n\t\t\t}\n\t\t\tn--;\n\t\t}\n\t}\n}\n\n\t/* ==========================================================================\n\t* Generate the codes for a given tree and bit counts (which need not be\n\t* optimal).\n\t* IN assertion: the array bl_count contains the bit length statistics for\n\t* the given tree and the field len is set for all tree elements.\n\t* OUT assertion: the field code is set for all tree elements of non\n\t*     zero code length.\n\t* @param tree- the tree to decorate\n\t* @param max_code- largest code with non-zero frequency\n\t*/\nfunction gen_codes(tree, max_code) {\n\tvar next_code = []; // new Array(MAX_BITS + 1); // next code value for each bit length\n\tvar code = 0; // running code value\n\tvar bits; // bit index\n\tvar n; // code index\n\n\t// The distribution counts are first used to generate the code values\n\t// without bit reversal.\n\tfor (bits = 1; bits <= MAX_BITS; bits++) {\n\t\tcode = ((code + bl_count[bits - 1]) << 1);\n\t\tnext_code[bits] = code;\n\t}\n\n\t// Check that the bit counts in bl_count are consistent. The last code\n\t// must be all ones.\n\t// Assert (code + encoder->bl_count[MAX_BITS]-1 === (1<<MAX_BITS)-1, \"inconsistent bit counts\");\n\t// Tracev((stderr,\"\\ngen_codes: max_code %d \", max_code));\n\n\tfor (n = 0; n <= max_code; n++) {\n\t\tvar len = tree[n].dl;\n\t\tif (len === 0) {\n\t\t\tcontinue;\n\t\t}\n\t\t// Now reverse the bits\n\t\ttree[n].fc = bi_reverse(next_code[len]++, len);\n\n\t\t// Tracec(tree !== static_ltree, (stderr,\"\\nn %3d %c l %2d c %4x (%x) \", n, (isgraph(n) ? n : ' '), len, tree[n].fc, next_code[len]-1));\n\t}\n}\n\n/* ==========================================================================\n\t* Construct one Huffman tree and assigns the code bit strings and lengths.\n\t* Update the total bit length for the current block.\n\t* IN assertion: the field freq is set for all tree elements.\n\t* OUT assertions: the fields len and code are set to the optimal bit length\n\t*     and corresponding code. The length opt_len is updated; static_len is\n\t*     also updated if stree is not null. The field max_code is set.\n\t*/\nfunction build_tree(desc) { // the tree descriptor\n\tvar tree = desc.dyn_tree;\n\tvar stree = desc.static_tree;\n\tvar elems = desc.elems;\n\tvar n, m; // iterate over heap elements\n\tvar max_code = -1; // largest code with non zero frequency\n\tvar node = elems; // next internal node of the tree\n\n\t// Construct the initial heap, with least frequent element in\n\t// heap[SMALLEST]. The sons of heap[n] are heap[2*n] and heap[2*n+1].\n\t// heap[0] is not used.\n\theap_len = 0;\n\theap_max = HEAP_SIZE;\n\n\tfor (n = 0; n < elems; n++) {\n\t\tif (tree[n].fc !== 0) {\n\t\t\theap[++heap_len] = max_code = n;\n\t\t\tdepth[n] = 0;\n\t\t} else {\n\t\t\ttree[n].dl = 0;\n\t\t}\n\t}\n\n\t// The pkzip format requires that at least one distance code exists,\n\t// and that at least one bit should be sent even if there is only one\n\t// possible code. So to avoid special checks later on we force at least\n\t// two codes of non zero frequency.\n\twhile (heap_len < 2) {\n\t\tvar xnew = heap[++heap_len] = (max_code < 2 ? ++max_code : 0);\n\t\ttree[xnew].fc = 1;\n\t\tdepth[xnew] = 0;\n\t\topt_len--;\n\t\tif (stree !== null) {\n\t\t\tstatic_len -= stree[xnew].dl;\n\t\t}\n\t\t// new is 0 or 1 so it does not have extra bits\n\t}\n\tdesc.max_code = max_code;\n\n\t// The elements heap[heap_len/2+1 .. heap_len] are leaves of the tree,\n\t// establish sub-heaps of increasing lengths:\n\tfor (n = heap_len >> 1; n >= 1; n--) {\n\t\tpqdownheap(tree, n);\n\t}\n\n\t// Construct the Huffman tree by repeatedly combining the least two\n\t// frequent nodes.\n\tdo {\n\t\tn = heap[SMALLEST];\n\t\theap[SMALLEST] = heap[heap_len--];\n\t\tpqdownheap(tree, SMALLEST);\n\n\t\tm = heap[SMALLEST]; // m = node of next least frequency\n\n\t\t// keep the nodes sorted by frequency\n\t\theap[--heap_max] = n;\n\t\theap[--heap_max] = m;\n\n\t\t// Create a new node father of n and m\n\t\ttree[node].fc = tree[n].fc + tree[m].fc;\n\t\t//\tdepth[node] = (char)(MAX(depth[n], depth[m]) + 1);\n\t\tif (depth[n] > depth[m] + 1) {\n\t\t\tdepth[node] = depth[n];\n\t\t} else {\n\t\t\tdepth[node] = depth[m] + 1;\n\t\t}\n\t\ttree[n].dl = tree[m].dl = node;\n\n\t\t// and insert the new node in the heap\n\t\theap[SMALLEST] = node++;\n\t\tpqdownheap(tree, SMALLEST);\n\n\t} while (heap_len >= 2);\n\n\theap[--heap_max] = heap[SMALLEST];\n\n\t// At this point, the fields freq and dad are set. We can now\n\t// generate the bit lengths.\n\tgen_bitlen(desc);\n\n\t// The field len is now set, we can generate the bit codes\n\tgen_codes(tree, max_code);\n}\n\n/* ==========================================================================\n\t* Scan a literal or distance tree to determine the frequencies of the codes\n\t* in the bit length tree. Updates opt_len to take into account the repeat\n\t* counts. (The contribution of the bit length codes will be added later\n\t* during the construction of bl_tree.)\n\t*\n\t* @param tree- the tree to be scanned\n\t* @param max_code- and its largest code of non zero frequency\n\t*/\nfunction scan_tree(tree, max_code) {\n\tvar n, // iterates over all tree elements\n\t\tprevlen = -1, // last emitted length\n\t\tcurlen, // length of current code\n\t\tnextlen = tree[0].dl, // length of next code\n\t\tcount = 0, // repeat count of the current code\n\t\tmax_count = 7, // max repeat count\n\t\tmin_count = 4; // min repeat count\n\n\tif (nextlen === 0) {\n\t\tmax_count = 138;\n\t\tmin_count = 3;\n\t}\n\ttree[max_code + 1].dl = 0xffff; // guard\n\n\tfor (n = 0; n <= max_code; n++) {\n\t\tcurlen = nextlen;\n\t\tnextlen = tree[n + 1].dl;\n\t\tif (++count < max_count && curlen === nextlen) {\n\t\t\tcontinue;\n\t\t} else if (count < min_count) {\n\t\t\tbl_tree[curlen].fc += count;\n\t\t} else if (curlen !== 0) {\n\t\t\tif (curlen !== prevlen) {\n\t\t\t\tbl_tree[curlen].fc++;\n\t\t\t}\n\t\t\tbl_tree[REP_3_6].fc++;\n\t\t} else if (count <= 10) {\n\t\t\tbl_tree[REPZ_3_10].fc++;\n\t\t} else {\n\t\t\tbl_tree[REPZ_11_138].fc++;\n\t\t}\n\t\tcount = 0; prevlen = curlen;\n\t\tif (nextlen === 0) {\n\t\t\tmax_count = 138;\n\t\t\tmin_count = 3;\n\t\t} else if (curlen === nextlen) {\n\t\t\tmax_count = 6;\n\t\t\tmin_count = 3;\n\t\t} else {\n\t\t\tmax_count = 7;\n\t\t\tmin_count = 4;\n\t\t}\n\t}\n}\n\n/* ==========================================================================\n\t* Send a literal or distance tree in compressed form, using the codes in\n\t* bl_tree.\n\t*\n\t* @param tree- the tree to be scanned\n\t* @param max_code- and its largest code of non zero frequency\n\t*/\nfunction send_tree(tree, max_code) {\n\tvar n; // iterates over all tree elements\n\tvar prevlen = -1; // last emitted length\n\tvar curlen; // length of current code\n\tvar nextlen = tree[0].dl; // length of next code\n\tvar count = 0; // repeat count of the current code\n\tvar max_count = 7; // max repeat count\n\tvar min_count = 4; // min repeat count\n\n\t// tree[max_code+1].dl = -1; */  /* guard already set */\n\tif (nextlen === 0) {\n\t\tmax_count = 138;\n\t\tmin_count = 3;\n\t}\n\n\tfor (n = 0; n <= max_code; n++) {\n\t\tcurlen = nextlen;\n\t\tnextlen = tree[n + 1].dl;\n\t\tif (++count < max_count && curlen === nextlen) {\n\t\t\tcontinue;\n\t\t} else if (count < min_count) {\n\t\t\tdo {\n\t\t\t\tSEND_CODE(curlen, bl_tree);\n\t\t\t} while (--count !== 0);\n\t\t} else if (curlen !== 0) {\n\t\t\tif (curlen !== prevlen) {\n\t\t\t\tSEND_CODE(curlen, bl_tree);\n\t\t\t\tcount--;\n\t\t\t}\n\t\t// Assert(count >= 3 && count <= 6, \" 3_6?\");\n\t\t\tSEND_CODE(REP_3_6, bl_tree);\n\t\t\tsend_bits(count - 3, 2);\n\t\t} else if (count <= 10) {\n\t\t\tSEND_CODE(REPZ_3_10, bl_tree);\n\t\t\tsend_bits(count - 3, 3);\n\t\t} else {\n\t\t\tSEND_CODE(REPZ_11_138, bl_tree);\n\t\t\tsend_bits(count - 11, 7);\n\t\t}\n\t\tcount = 0;\n\t\tprevlen = curlen;\n\t\tif (nextlen === 0) {\n\t\t\tmax_count = 138;\n\t\t\tmin_count = 3;\n\t\t} else if (curlen === nextlen) {\n\t\t\tmax_count = 6;\n\t\t\tmin_count = 3;\n\t\t} else {\n\t\t\tmax_count = 7;\n\t\t\tmin_count = 4;\n\t\t}\n\t}\n}\n\n/* ==========================================================================\n\t* Construct the Huffman tree for the bit lengths and return the index in\n\t* bl_order of the last bit length code to send.\n\t*/\nfunction build_bl_tree() {\n\tvar max_blindex; // index of last bit length code of non zero freq\n\n\t// Determine the bit length frequencies for literal and distance trees\n\tscan_tree(dyn_ltree, l_desc.max_code);\n\tscan_tree(dyn_dtree, d_desc.max_code);\n\n\t// Build the bit length tree:\n\tbuild_tree(bl_desc);\n\t// opt_len now includes the length of the tree representations, except\n\t// the lengths of the bit lengths codes and the 5+5+4 bits for the counts.\n\n\t// Determine the number of bit length codes to send. The pkzip format\n\t// requires that at least 4 bit length codes be sent. (appnote.txt says\n\t// 3 but the actual value used is 4.)\n\tfor (max_blindex = BL_CODES - 1; max_blindex >= 3; max_blindex--) {\n\t\tif (bl_tree[bl_order[max_blindex]].dl !== 0) {\n\t\t\tbreak;\n\t\t}\n\t}\n\t// Update opt_len to include the bit length tree and counts */\n\topt_len += 3 * (max_blindex + 1) + 5 + 5 + 4;\n\t// Tracev((stderr, \"\\ndyn trees: dyn %ld, stat %ld\",\n\t// encoder->opt_len, encoder->static_len));\n\n\treturn max_blindex;\n}\n\n/* ==========================================================================\n\t* Send the header for a block using dynamic Huffman trees: the counts, the\n\t* lengths of the bit length codes, the literal tree and the distance tree.\n\t* IN assertion: lcodes >= 257, dcodes >= 1, blcodes >= 4.\n\t*/\nfunction send_all_trees(lcodes, dcodes, blcodes) { // number of codes for each tree\n\tvar rank; // index in bl_order\n\n\t// Assert (lcodes >= 257 && dcodes >= 1 && blcodes >= 4, \"not enough codes\");\n\t// Assert (lcodes <= L_CODES && dcodes <= D_CODES && blcodes <= BL_CODES, \"too many codes\");\n\t// Tracev((stderr, \"\\nbl counts: \"));\n\tsend_bits(lcodes - 257, 5); // not +255 as stated in appnote.txt\n\tsend_bits(dcodes - 1,   5);\n\tsend_bits(blcodes - 4,  4); // not -3 as stated in appnote.txt\n\tfor (rank = 0; rank < blcodes; rank++) {\n\t\t// Tracev((stderr, \"\\nbl code %2d \", bl_order[rank]));\n\t\tsend_bits(bl_tree[bl_order[rank]].dl, 3);\n\t}\n\n\t// send the literal tree\n\tsend_tree(dyn_ltree, lcodes - 1);\n\n\t// send the distance tree\n\tsend_tree(dyn_dtree, dcodes - 1);\n}\n\n/* ==========================================================================\n\t* Determine the best encoding for the current block: dynamic trees, static\n\t* trees or store, and output the encoded block to the zip file.\n\t*/\nfunction flush_block(eof) { // true if this is the last block for a file\n\tvar opt_lenb, static_lenb, // opt_len and static_len in bytes\n\t\tmax_blindex, // index of last bit length code of non zero freq\n\t\tstored_len, // length of input block\n\t\ti;\n\n\tstored_len = strstart - block_start;\n\tflag_buf[last_flags] = flags; // Save the flags for the last 8 items\n\n\t// Construct the literal and distance trees\n\tbuild_tree(l_desc);\n\t// Tracev((stderr, \"\\nlit data: dyn %ld, stat %ld\",\n\t// encoder->opt_len, encoder->static_len));\n\n\tbuild_tree(d_desc);\n\t// Tracev((stderr, \"\\ndist data: dyn %ld, stat %ld\",\n\t// encoder->opt_len, encoder->static_len));\n\t// At this point, opt_len and static_len are the total bit lengths of\n\t// the compressed block data, excluding the tree representations.\n\n\t// Build the bit length tree for the above two trees, and get the index\n\t// in bl_order of the last bit length code to send.\n\tmax_blindex = build_bl_tree();\n\n\t// Determine the best encoding. Compute first the block length in bytes\n\topt_lenb = (opt_len + 3 + 7) >> 3;\n\tstatic_lenb = (static_len + 3 + 7) >> 3;\n\n//  Trace((stderr, \"\\nopt %lu(%lu) stat %lu(%lu) stored %lu lit %u dist %u \", opt_lenb, encoder->opt_len, static_lenb, encoder->static_len, stored_len, encoder->last_lit, encoder->last_dist));\n\n\tif (static_lenb <= opt_lenb) {\n\t\topt_lenb = static_lenb;\n\t}\n\tif (stored_len + 4 <= opt_lenb && block_start >= 0) { // 4: two words for the lengths\n\t\t// The test buf !== NULL is only necessary if LIT_BUFSIZE > WSIZE.\n\t\t// Otherwise we can't have processed more than WSIZE input bytes since\n\t\t// the last block flush, because compression would have been\n\t\t// successful. If LIT_BUFSIZE <= WSIZE, it is never too late to\n\t\t// transform a block into a stored block.\n\t\tsend_bits((STORED_BLOCK << 1) + eof, 3);  /* send block type */\n\t\tbi_windup();         /* align on byte boundary */\n\t\tput_short(stored_len);\n\t\tput_short(~stored_len);\n\n\t\t// copy block\n\t\t/*\n\t\t\tp = &window[block_start];\n\t\t\tfor (i = 0; i < stored_len; i++) {\n\t\t\t\tput_byte(p[i]);\n\t\t\t}\n\t\t*/\n\t\tfor (i = 0; i < stored_len; i++) {\n\t\t\tput_byte(window[block_start + i]);\n\t\t}\n\t} else if (static_lenb === opt_lenb) {\n\t\tsend_bits((STATIC_TREES << 1) + eof, 3);\n\t\tcompress_block(static_ltree, static_dtree);\n\t} else {\n\t\tsend_bits((DYN_TREES << 1) + eof, 3);\n\t\tsend_all_trees(l_desc.max_code + 1, d_desc.max_code + 1, max_blindex + 1);\n\t\tcompress_block(dyn_ltree, dyn_dtree);\n\t}\n\n\tinit_block();\n\n\tif (eof !== 0) {\n\t\tbi_windup();\n\t}\n}\n\n/* ==========================================================================\n\t* Save the match info and tally the frequency counts. Return true if\n\t* the current block must be flushed.\n\t*\n\t* @param dist- distance of matched string\n\t* @param lc- (match length - MIN_MATCH) or unmatched char (if dist === 0)\n\t*/\nfunction ct_tally(dist, lc) {\n\tl_buf[last_lit++] = lc;\n\tif (dist === 0) {\n\t\t// lc is the unmatched char\n\t\tdyn_ltree[lc].fc++;\n\t} else {\n\t\t// Here, lc is the match length - MIN_MATCH\n\t\tdist--; // dist = match distance - 1\n\t\t// Assert((ush)dist < (ush)MAX_DIST && (ush)lc <= (ush)(MAX_MATCH-MIN_MATCH) && (ush)D_CODE(dist) < (ush)D_CODES,  \"ct_tally: bad match\");\n\n\t\tdyn_ltree[length_code[lc] + LITERALS + 1].fc++;\n\t\tdyn_dtree[D_CODE(dist)].fc++;\n\n\t\td_buf[last_dist++] = dist;\n\t\tflags |= flag_bit;\n\t}\n\tflag_bit <<= 1;\n\n\t// Output the flags if they fill a byte\n\tif ((last_lit & 7) === 0) {\n\t\tflag_buf[last_flags++] = flags;\n\t\tflags = 0;\n\t\tflag_bit = 1;\n\t}\n\t// Try to guess if it is profitable to stop the current block here\n\tif (compr_level > 2 && (last_lit & 0xfff) === 0) {\n\t\t// Compute an upper bound for the compressed length\n\t\tvar out_length = last_lit * 8;\n\t\tvar in_length = strstart - block_start;\n\t\tvar dcode;\n\n\t\tfor (dcode = 0; dcode < D_CODES; dcode++) {\n\t\t\tout_length += dyn_dtree[dcode].fc * (5 + extra_dbits[dcode]);\n\t\t}\n\t\tout_length >>= 3;\n\t\t// Trace((stderr,\"\\nlast_lit %u, last_dist %u, in %ld, out ~%ld(%ld%%) \", encoder->last_lit, encoder->last_dist, in_length, out_length, 100L - out_length*100L/in_length));\n\t\tif (last_dist < parseInt(last_lit / 2, 10) && out_length < parseInt(in_length / 2, 10)) {\n\t\t\treturn true;\n\t\t}\n\t}\n\treturn (last_lit === LIT_BUFSIZE - 1 || last_dist === DIST_BUFSIZE);\n\t// We avoid equality with LIT_BUFSIZE because of wraparound at 64K\n\t// on 16 bit machines and because stored blocks are restricted to\n\t// 64K-1 bytes.\n}\n\n\t/* ==========================================================================\n\t* Send the block data compressed using the given Huffman trees\n\t*\n\t* @param ltree- literal tree\n\t* @param dtree- distance tree\n\t*/\nfunction compress_block(ltree, dtree) {\n\tvar dist; // distance of matched string\n\tvar lc; // match length or unmatched char (if dist === 0)\n\tvar lx = 0; // running index in l_buf\n\tvar dx = 0; // running index in d_buf\n\tvar fx = 0; // running index in flag_buf\n\tvar flag = 0; // current flags\n\tvar code; // the code to send\n\tvar extra; // number of extra bits to send\n\n\tif (last_lit !== 0) {\n\t\tdo {\n\t\t\tif ((lx & 7) === 0) {\n\t\t\t\tflag = flag_buf[fx++];\n\t\t\t}\n\t\t\tlc = l_buf[lx++] & 0xff;\n\t\t\tif ((flag & 1) === 0) {\n\t\t\t\tSEND_CODE(lc, ltree); /* send a literal byte */\n\t\t\t\t//\tTracecv(isgraph(lc), (stderr,\" '%c' \", lc));\n\t\t\t} else {\n\t\t\t\t// Here, lc is the match length - MIN_MATCH\n\t\t\t\tcode = length_code[lc];\n\t\t\t\tSEND_CODE(code + LITERALS + 1, ltree); // send the length code\n\t\t\t\textra = extra_lbits[code];\n\t\t\t\tif (extra !== 0) {\n\t\t\t\t\tlc -= base_length[code];\n\t\t\t\t\tsend_bits(lc, extra); // send the extra length bits\n\t\t\t\t}\n\t\t\t\tdist = d_buf[dx++];\n\t\t\t\t// Here, dist is the match distance - 1\n\t\t\t\tcode = D_CODE(dist);\n\t\t\t\t//\tAssert (code < D_CODES, \"bad d_code\");\n\n\t\t\t\tSEND_CODE(code, dtree); // send the distance code\n\t\t\t\textra = extra_dbits[code];\n\t\t\t\tif (extra !== 0) {\n\t\t\t\t\tdist -= base_dist[code];\n\t\t\t\t\tsend_bits(dist, extra); // send the extra distance bits\n\t\t\t\t}\n\t\t\t} // literal or match pair ?\n\t\t\tflag >>= 1;\n\t\t} while (lx < last_lit);\n\t}\n\n\tSEND_CODE(END_BLOCK, ltree);\n}\n\n/* ==========================================================================\n\t* Send a value on a given number of bits.\n\t* IN assertion: length <= 16 and value fits in length bits.\n\t*\n\t* @param value- value to send\n\t* @param length- number of bits\n\t*/\nvar Buf_size = 16; // bit size of bi_buf\nfunction send_bits(value, length) {\n\t// If not enough room in bi_buf, use (valid) bits from bi_buf and\n\t// (16 - bi_valid) bits from value, leaving (width - (16-bi_valid))\n\t// unused bits in value.\n\tif (bi_valid > Buf_size - length) {\n\t\tbi_buf |= (value << bi_valid);\n\t\tput_short(bi_buf);\n\t\tbi_buf = (value >> (Buf_size - bi_valid));\n\t\tbi_valid += length - Buf_size;\n\t} else {\n\t\tbi_buf |= value << bi_valid;\n\t\tbi_valid += length;\n\t}\n}\n\n/* ==========================================================================\n\t* Reverse the first len bits of a code, using straightforward code (a faster\n\t* method would use a table)\n\t* IN assertion: 1 <= len <= 15\n\t*\n\t* @param code- the value to invert\n\t* @param len- its bit length\n\t*/\nfunction bi_reverse(code, len) {\n\tvar res = 0;\n\tdo {\n\t\tres |= code & 1;\n\t\tcode >>= 1;\n\t\tres <<= 1;\n\t} while (--len > 0);\n\treturn res >> 1;\n}\n\n/* ==========================================================================\n\t* Write out any remaining bits in an incomplete byte.\n\t*/\nfunction bi_windup() {\n\tif (bi_valid > 8) {\n\t\tput_short(bi_buf);\n\t} else if (bi_valid > 0) {\n\t\tput_byte(bi_buf);\n\t}\n\tbi_buf = 0;\n\tbi_valid = 0;\n}\n\nfunction qoutbuf() {\n\tvar q, i;\n\tif (outcnt !== 0) {\n\t\tq = new_queue();\n\t\tif (qhead === null) {\n\t\t\tqhead = qtail = q;\n\t\t} else {\n\t\t\tqtail = qtail.next = q;\n\t\t}\n\t\tq.len = outcnt - outoff;\n\t\t// System.arraycopy(outbuf, outoff, q.ptr, 0, q.len);\n\t\tfor (i = 0; i < q.len; i++) {\n\t\t\tq.ptr[i] = outbuf[outoff + i];\n\t\t}\n\t\toutcnt = outoff = 0;\n\t}\n}\n\nexport function deflate(arr, level) {\n\tvar i, j, buff;\n\n\tdeflate_data = arr;\n\tdeflate_pos = 0;\n\tif (typeof level === \"undefined\") {\n\t\tlevel = DEFAULT_LEVEL;\n\t}\n\tdeflate_start(level);\n\n\tbuff = [];\n\n\tdo {\n\t\ti = deflate_internal(buff, buff.length, 1024);\n\t} while (i > 0);\n\n\tdeflate_data = null; // G.C.\n\treturn buff;\n}"]}