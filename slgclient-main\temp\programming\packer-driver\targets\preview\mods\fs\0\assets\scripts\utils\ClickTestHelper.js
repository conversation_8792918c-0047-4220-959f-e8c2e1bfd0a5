System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Node, UITransform, Vec3, Label, view, sys, director, screen, FixedScreenAdapter, _dec, _dec2, _class, _class2, _descriptor, _temp, _crd, ccclass, property, ClickTestHelper;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfFixedScreenAdapter(extras) {
    _reporterNs.report("FixedScreenAdapter", "./FixedScreenAdapter", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
      Vec3 = _cc.Vec3;
      Label = _cc.Label;
      view = _cc.view;
      sys = _cc.sys;
      director = _cc.director;
      screen = _cc.screen;
    }, function (_unresolved_2) {
      FixedScreenAdapter = _unresolved_2.FixedScreenAdapter;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "eb873fDuJxLyaJTKUI3ZFqK", "ClickTestHelper", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 点击测试助手
       * 用于测试和调试点击功能是否正常
       */

      _export("ClickTestHelper", ClickTestHelper = (_dec = ccclass('ClickTestHelper'), _dec2 = property(Label), _dec(_class = (_class2 = (_temp = class ClickTestHelper extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "debugLabel", _descriptor, this);

          _defineProperty(this, "_touchCount", 0);
        }

        onLoad() {
          // 监听全局触摸事件
          this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
          this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
          this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this); // 创建调试信息显示

          this.createDebugDisplay(); // 定期更新调试信息

          this.schedule(this.updateDebugInfo, 1.0);
        }

        onDestroy() {
          this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
          this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
          this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
          this.unschedule(this.updateDebugInfo);
        }
        /**
         * 创建调试信息显示
         */


        createDebugDisplay() {
          if (!this.debugLabel) {
            // 创建调试标签节点
            var debugNode = new Node('DebugLabel');
            debugNode.setParent(this.node); // 添加UITransform组件

            var uiTransform = debugNode.addComponent(UITransform);
            uiTransform.setContentSize(400, 200); // 添加Label组件

            this.debugLabel = debugNode.addComponent(Label);
            this.debugLabel.fontSize = 20;
            this.debugLabel.lineHeight = 25;
            this.debugLabel.string = '点击测试助手已启动'; // 设置位置（左上角）

            var visibleSize = view.getVisibleSize();
            debugNode.setPosition(-visibleSize.width / 2 + 200, visibleSize.height / 2 - 100, 0);
          }
        }
        /**
         * 触摸开始事件
         */


        onTouchStart(event) {
          this._touchCount++;
          var touchPos = event.getUILocation();
          var worldPos = new Vec3(touchPos.x, touchPos.y, 0); // 获取更多调试信息

          var adaptInfo = (_crd && FixedScreenAdapter === void 0 ? (_reportPossibleCrUseOfFixedScreenAdapter({
            error: Error()
          }), FixedScreenAdapter) : FixedScreenAdapter).instance ? (_crd && FixedScreenAdapter === void 0 ? (_reportPossibleCrUseOfFixedScreenAdapter({
            error: Error()
          }), FixedScreenAdapter) : FixedScreenAdapter).instance.getAdaptInfo() : null;
          console.log("[ClickTest] \u89E6\u6478\u5F00\u59CB #" + this._touchCount + ":");
          console.log("  UI\u5750\u6807: (" + touchPos.x.toFixed(1) + ", " + touchPos.y.toFixed(1) + ")");

          if (adaptInfo) {
            console.log("  \u7A97\u53E3\u5C3A\u5BF8: " + adaptInfo.windowSize.width + "x" + adaptInfo.windowSize.height);
            console.log("  \u53EF\u89C6\u533A\u57DF: " + adaptInfo.visibleSize.width.toFixed(1) + "x" + adaptInfo.visibleSize.height.toFixed(1));
            console.log("  \u53EF\u89C6\u539F\u70B9: (" + adaptInfo.visibleOrigin.x.toFixed(1) + ", " + adaptInfo.visibleOrigin.y.toFixed(1) + ")");
            console.log("  \u7F29\u653E\u6BD4\u4F8B: X=" + adaptInfo.scaleX.toFixed(3) + ", Y=" + adaptInfo.scaleY.toFixed(3));
          }

          console.log("  \u8BBE\u8BA1\u5206\u8FA8\u7387: " + adaptInfo.designSize.width + "x" + adaptInfo.designSize.height);
          console.log("  \u53EF\u89C6\u533A\u57DF: " + adaptInfo.visibleSize.width.toFixed(0) + "x" + adaptInfo.visibleSize.height.toFixed(0));
          console.log("  \u7F29\u653E\u6BD4\u4F8B: X=" + adaptInfo.scaleX.toFixed(3) + ", Y=" + adaptInfo.scaleY.toFixed(3)); // 检查点击的UI元素

          this.checkClickedElement(worldPos); // 更新调试信息

          this.updateTouchDebugInfo(touchPos, '开始');
        }
        /**
         * 触摸移动事件
         */


        onTouchMove(event) {
          var touchPos = event.getUILocation(); // 只在移动距离较大时记录

          var delta = event.getDelta();

          if (Math.abs(delta.x) > 5 || Math.abs(delta.y) > 5) {
            // 获取屏幕适配信息
            var canvas = director.root.mainWindow;
            var designSize = screen.designResolution;
            var windowSize = screen.windowSize;
            console.log("[ClickTest] \u89E6\u6478\u79FB\u52A8: UI\u5750\u6807(" + touchPos.x.toFixed(1) + ", " + touchPos.y.toFixed(1) + ")");
            console.log("[ClickTest] \u5C4F\u5E55\u9002\u914D: \u8BBE\u8BA1\u5206\u8FA8\u7387" + designSize.width + "x" + designSize.height + ", \u7A97\u53E3\u5927\u5C0F" + windowSize.width + "x" + windowSize.height);
          }
        }
        /**
         * 触摸结束事件
         */


        onTouchEnd(event) {
          var touchPos = event.getUILocation();
          console.log("[ClickTest] \u89E6\u6478\u7ED3\u675F: UI\u5750\u6807(" + touchPos.x.toFixed(1) + ", " + touchPos.y.toFixed(1) + ")"); // 更新调试信息

          this.updateTouchDebugInfo(touchPos, '结束');
        }
        /**
         * 检查点击的UI元素
         */


        checkClickedElement(worldPos) {
          // 这里可以添加具体的UI元素检测逻辑
          // 例如检查是否点击了按钮、输入框等
          console.log("[ClickTest] \u68C0\u67E5\u4E16\u754C\u5750\u6807(" + worldPos.x.toFixed(1) + ", " + worldPos.y.toFixed(1) + ")\u5904\u7684UI\u5143\u7D20");
        }
        /**
         * 更新触摸调试信息
         */


        updateTouchDebugInfo(touchPos, action) {
          if (!this.debugLabel) return;
          var adaptInfo = (_crd && FixedScreenAdapter === void 0 ? (_reportPossibleCrUseOfFixedScreenAdapter({
            error: Error()
          }), FixedScreenAdapter) : FixedScreenAdapter).instance ? (_crd && FixedScreenAdapter === void 0 ? (_reportPossibleCrUseOfFixedScreenAdapter({
            error: Error()
          }), FixedScreenAdapter) : FixedScreenAdapter).instance.getAdaptInfo() : null;
          var deviceType = sys.isMobile ? 'Mobile' : 'Desktop';
          var debugText = "\u70B9\u51FB\u6D4B\u8BD5\u52A9\u624B\n\u8BBE\u5907\u7C7B\u578B: " + deviceType + "\n\u89E6\u6478\u6B21\u6570: " + this._touchCount + "\n\u6700\u540E" + action + ": (" + touchPos.x.toFixed(1) + ", " + touchPos.y.toFixed(1) + ")\n" + (adaptInfo ? "\u7A97\u53E3\u5C3A\u5BF8: " + adaptInfo.windowSize.width + "x" + adaptInfo.windowSize.height + "\n\u53EF\u89C6\u533A\u57DF: " + adaptInfo.visibleSize.width.toFixed(0) + "x" + adaptInfo.visibleSize.height.toFixed(0) + "\n\u7F29\u653E\u6BD4\u4F8B: X=" + adaptInfo.scaleX.toFixed(3) + ", Y=" + adaptInfo.scaleY.toFixed(3) : '适配器未初始化');
          this.debugLabel.string = debugText;
        }
        /**
         * 定期更新调试信息
         */


        updateDebugInfo() {
          if (!this.debugLabel) return;
          var adaptInfo = (_crd && FixedScreenAdapter === void 0 ? (_reportPossibleCrUseOfFixedScreenAdapter({
            error: Error()
          }), FixedScreenAdapter) : FixedScreenAdapter).instance ? (_crd && FixedScreenAdapter === void 0 ? (_reportPossibleCrUseOfFixedScreenAdapter({
            error: Error()
          }), FixedScreenAdapter) : FixedScreenAdapter).instance.getAdaptInfo() : null;
          var deviceType = sys.isMobile ? 'Mobile' : 'Desktop';
          var safeAreaInfo = '';

          if (sys.isMobile) {
            var safeArea = sys.getSafeAreaRect();
            safeAreaInfo = "\n\u5B89\u5168\u533A\u57DF: " + safeArea.width.toFixed(0) + "x" + safeArea.height.toFixed(0);
          }

          var debugText = "\u70B9\u51FB\u6D4B\u8BD5\u52A9\u624B\n\u8BBE\u5907\u7C7B\u578B: " + deviceType + "\n\u89E6\u6478\u6B21\u6570: " + this._touchCount + "\n" + (adaptInfo ? "\u7A97\u53E3\u5C3A\u5BF8: " + adaptInfo.windowSize.width + "x" + adaptInfo.windowSize.height + "\n\u53EF\u89C6\u533A\u57DF: " + adaptInfo.visibleSize.width.toFixed(0) + "x" + adaptInfo.visibleSize.height.toFixed(0) + "\n\u7F29\u653E\u6BD4\u4F8B: X=" + adaptInfo.scaleX.toFixed(3) + ", Y=" + adaptInfo.scaleY.toFixed(3) : '适配器未初始化') + safeAreaInfo + "\n\u72B6\u6001: \u7B49\u5F85\u70B9\u51FB...";
          this.debugLabel.string = debugText;
        }
        /**
         * 手动触发适配信息打印
         */


        printAdaptInfo() {
          if ((_crd && FixedScreenAdapter === void 0 ? (_reportPossibleCrUseOfFixedScreenAdapter({
            error: Error()
          }), FixedScreenAdapter) : FixedScreenAdapter).instance) {
            (_crd && FixedScreenAdapter === void 0 ? (_reportPossibleCrUseOfFixedScreenAdapter({
              error: Error()
            }), FixedScreenAdapter) : FixedScreenAdapter).instance.printDebugInfo();
          }

          console.log('=== 点击测试统计 ===');
          console.log("\u603B\u89E6\u6478\u6B21\u6570: " + this._touchCount);
          console.log('==================');
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "debugLabel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ClickTestHelper.js.map