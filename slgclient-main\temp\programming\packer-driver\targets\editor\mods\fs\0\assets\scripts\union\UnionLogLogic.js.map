{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "UnionCommand", "MapCommand", "EventMgr", "LogicEvent", "ListLogic", "ccclass", "property", "UnionLogLogic", "onLoad", "on", "unionLog", "updateLog", "onDestroy", "targetOff", "data", "comp", "logView", "node", "getComponent", "setData", "getLog", "city", "getInstance", "cityProxy", "getMyMainCity", "unionData", "proxy", "getUnion", "unionId", "<PERSON><PERSON><PERSON><PERSON>", "rid", "onEnable", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAESA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;;AAGzBC,MAAAA,Y;;AAGAC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,S;;;;;;;OARD;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBT,U;;yBAWPU,a,WADpBF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACP,UAAD,C,oCAFb,MACqBQ,aADrB,SAC2CT,SAD3C,CACqD;AAAA;AAAA;;AAAA;AAAA;;AAGvCU,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,QAAvB,EAAgC,KAAKC,SAArC,EAA+C,IAA/C;AACH;;AAESC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACSF,QAAAA,SAAS,CAACG,IAAD,EAAY;AAE3B,cAAIC,IAAI,GAAG,KAAKC,OAAL,CAAaC,IAAb,CAAkBC,YAAlB;AAAA;AAAA,qCAAX;AACAH,UAAAA,IAAI,CAACI,OAAL,CAAaL,IAAI,GAACA,IAAD,GAAM,EAAvB;AACH;;AACSM,QAAAA,MAAM,GAAO;AACnB,cAAIC,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIC,SAAe,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BI,KAA3B,CAAiCC,QAAjC,CAA0CN,IAAI,CAACO,OAA/C,CAAtB;;AACA,cAAGH,SAAS,CAACI,OAAV,CAAkBR,IAAI,CAACS,GAAvB,CAAH,EAA+B;AAC3B;AAAA;AAAA,8CAAaR,WAAb,GAA2BZ,QAA3B;AACH;AACJ;;AACSqB,QAAAA,QAAQ,GAAO;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AACA,eAAKb,MAAL;AACH;;AAzBgD,O;;;;;iBAErB,I", "sourcesContent": ["\n\nimport { _decorator, Component, ScrollView } from 'cc';\nconst {ccclass, property} = _decorator;\n\nimport UnionCommand from \"./UnionCommand\";\nimport { Union } from \"./UnionProxy\";\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\nimport ListLogic from '../utils/ListLogic';\n\n@ccclass('UnionLogLogic')\nexport default class UnionLogLogic extends Component {\n    @property(ScrollView)\n    logView:ScrollView | null = null;\n    protected onLoad():void{\n        EventMgr.on(LogicEvent.unionLog,this.updateLog,this);\n    }\n    \n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n    protected updateLog(data:any[]){\n\n        var comp = this.logView.node.getComponent(ListLogic);\n        comp.setData(data?data:[]);\n    }\n    protected getLog():void{\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let unionData:Union = UnionCommand.getInstance().proxy.getUnion(city.unionId);\n        if(unionData.isMajor(city.rid)){\n            UnionCommand.getInstance().unionLog();\n        }\n    }\n    protected onEnable():void{\n        console.log(\"getLog\");\n        this.getLog()\n    }\n}\n\n"]}