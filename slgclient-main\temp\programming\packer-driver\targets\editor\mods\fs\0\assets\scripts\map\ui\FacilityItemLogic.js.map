{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts"], "names": ["_decorator", "Component", "Label", "Node", "DateUtil", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "FacilityItemLogic", "onLoad", "node", "on", "EventType", "TOUCH_END", "onTouchItem", "onDestroy", "off", "targetOff", "updateItem", "labelRate", "string", "data", "level", "cfg", "upLevels", "length", "labelName", "name", "lockNode", "active", "isUnlock", "instance", "playClick", "emit", "selectFacilityItem", "cityId", "type", "setData", "isUping", "startUpTime", "stopCountDown", "countDown", "labelTime", "converSecondStr", "upLastTime", "unscheduleAllCallbacks", "schedule"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAGhCC,MAAAA,Q;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBASTU,iB,WADpBF,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAACP,KAAD,C,UAERO,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACN,IAAD,C,oCAVb,MACqBO,iBADrB,SAC+CT,SAD/C,CACyD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,wCAY/B,CAZ+B;;AAAA,4CAa1B,KAb0B;;AAAA,0CAc7B,CAd6B;;AAAA,wCAe7B,IAf6B;;AAAA,uCAgBxB,IAhBwB;AAAA;;AAkB3CU,QAAAA,MAAM,GAAS;AACrB,eAAKC,IAAL,CAAUC,EAAV,CAAaV,IAAI,CAACW,SAAL,CAAeC,SAA5B,EAAuC,KAAKC,WAA5C,EAAyD,IAAzD;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKL,IAAL,CAAUM,GAAV,CAAcf,IAAI,CAACW,SAAL,CAAeC,SAA7B,EAAwC,KAAKC,WAA7C,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASG,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,UAAU,GAAS;AACzB,eAAKC,SAAL,CAAeC,MAAf,GAAwB,KAAKC,IAAL,CAAUC,KAAV,GAAkB,GAAlB,GAAwB,KAAKC,GAAL,CAASC,QAAT,CAAkBC,MAAlE;AACA,eAAKC,SAAL,CAAeN,MAAf,GAAwB,KAAKG,GAAL,CAASI,IAAjC;AACA,eAAKC,QAAL,CAAcC,MAAd,GAAuB,CAAC,KAAKC,QAA7B;AACH;;AAEShB,QAAAA,WAAW,GAAG;AACpB;AAAA;AAAA,4CAAaiB,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,kBAAzB,EAA6C,KAAKC,MAAlD,EAA0D,KAAKd,IAAL,CAAUe,IAApE;AACH;;AAEMC,QAAAA,OAAO,CAACF,MAAD,EAAiBd,IAAjB,EAAiCE,GAAjC,EAAqDO,QAArD,EAA6E;AACvF;AAEA,eAAKK,MAAL,GAAcA,MAAd;AACA,eAAKd,IAAL,GAAYA,IAAZ;AACA,eAAKE,GAAL,GAAWA,GAAX;AACA,eAAKO,QAAL,GAAgBA,QAAhB;;AAEA,cAAG,KAAKT,IAAL,CAAUiB,OAAV,EAAH,EAAuB;AACnB,iBAAKC,WAAL;AACH,WAFD,MAEK;AACD,iBAAKC,aAAL;AACH;;AAED,eAAKtB,UAAL;AACH;;AAESuB,QAAAA,SAAS,GAAE;AACjB,cAAI,KAAKpB,IAAL,CAAUiB,OAAV,EAAJ,EAAwB;AACpB,iBAAKI,SAAL,CAAetB,MAAf,GAAwB;AAAA;AAAA,sCAASuB,eAAT,CAAyB,KAAKtB,IAAL,CAAUuB,UAAV,EAAzB,CAAxB;AACH,WAFD,MAEK;AACD,iBAAKJ,aAAL;AACH;AACJ;;AAESA,QAAAA,aAAa,GAAE;AACrB,eAAKK,sBAAL;AACA,eAAKH,SAAL,CAAetB,MAAf,GAAwB,EAAxB;AACH;;AAESmB,QAAAA,WAAW,GAAE;AACnB,eAAKC,aAAL;AACA,eAAKM,QAAL,CAAc,KAAKL,SAAnB,EAA8B,GAA9B;AACA,eAAKA,SAAL;AACH;;AAxEoD,O;;;;;iBAElC,I;;;;;;;iBAEA,I;;;;;;;iBAGA,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, Label, Node } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport DateUtil from \"../../utils/DateUtil\";\nimport { Facility, FacilityConfig } from \"./MapUIProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('FacilityItemLogic')\nexport default class FacilityItemLogic extends Component {\n    @property(Label)\n    labelRate: Label = null;\n    @property(Label)\n    labelName: Label = null;\n\n    @property(Label)\n    labelTime: Label = null;\n\n    @property(Node)\n    lockNode: Node = null;\n\n    public type: number = 0;\n    public isUnlock: boolean = false;\n    public cityId: number = 0;\n    public data: Facility = null;\n    public cfg: FacilityConfig = null;\n\n    protected onLoad(): void {\n        this.node.on(Node.EventType.TOUCH_END, this.onTouchItem, this);\n    }\n\n    protected onDestroy(): void {\n        this.node.off(Node.EventType.TOUCH_END, this.onTouchItem, this);\n        EventMgr.targetOff(this);\n    }\n\n    protected updateItem(): void {\n        this.labelRate.string = this.data.level + \"/\" + this.cfg.upLevels.length;\n        this.labelName.string = this.cfg.name;\n        this.lockNode.active = !this.isUnlock;\n    }\n\n    protected onTouchItem() {\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.selectFacilityItem, this.cityId, this.data.type);\n    }\n\n    public setData(cityId: number, data: Facility, cfg:FacilityConfig, isUnlock:boolean): void {\n        // console.log(\"setData:\", data);\n\n        this.cityId = cityId;\n        this.data = data;\n        this.cfg = cfg;\n        this.isUnlock = isUnlock;\n        \n        if(this.data.isUping()){\n            this.startUpTime();\n        }else{\n            this.stopCountDown();\n        }\n \n        this.updateItem();\n    }\n\n    protected countDown(){\n        if (this.data.isUping()){\n            this.labelTime.string = DateUtil.converSecondStr(this.data.upLastTime());\n        }else{\n            this.stopCountDown();\n        }\n    }\n\n    protected stopCountDown(){\n        this.unscheduleAllCallbacks();\n        this.labelTime.string = \"\";\n    }\n\n    protected startUpTime(){\n        this.stopCountDown();\n        this.schedule(this.countDown, 1.0);\n        this.countDown();\n    }\n}\n"]}