{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "SkillCommand", "Skill", "EventMgr", "AudioManager", "ListLogic", "LogicEvent", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onEnable", "on", "skillListInfo", "onSkillList", "getInstance", "qrySkillList", "onDisable", "targetOff", "skills", "proxy", "skillConfs", "arr", "i", "length", "found", "cfg", "dSkill", "cfgId", "generals", "j", "skill", "push", "comp", "scrollView", "node", "getComponent", "setData", "onClickClose", "active", "instance", "playClick", "onClickItem", "data", "target", "emit", "openSkillInfo", "_type", "_general", "_skillPos", "type", "general", "skillPos"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;;AAIzBC,MAAAA,Y;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,S;;AACEC,MAAAA,U,iBAAAA,U;;;;;;;OARH;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBV,U;;yBAWPW,U,WADpBF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACR,UAAD,C,oCAHb,MACqBS,UADrB,SACwCV,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA,4CAKtB,IALsB;;AAAA,yCAM9B,CAN8B;;AAAA,6CAOzB,CAAC,CAPwB;AAAA;;AAWpCW,QAAAA,QAAQ,GAAO;AAErB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,aAAvB,EAAsC,KAAKC,WAA3C,EAAwD,IAAxD;AACA;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,YAA3B;AACH;;AAESC,QAAAA,SAAS,GAAQ;AACvB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESJ,QAAAA,WAAW,GAAE;AAEnB,cAAIK,MAAM,GAAG;AAAA;AAAA,4CAAaJ,WAAb,GAA2BK,KAA3B,CAAiCD,MAA9C;AACA,cAAIE,UAAU,GAAG;AAAA;AAAA,4CAAaN,WAAb,GAA2BK,KAA3B,CAAiCC,UAAlD;AAEA,cAAIC,GAAG,GAAG,EAAV;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,UAAU,CAACG,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AACxC,gBAAIE,KAAK,GAAG,KAAZ;AACA,gBAAIC,GAAG,GAAGL,UAAU,CAACE,CAAD,CAApB;AAEA,gBAAII,MAAM,GAAG;AAAA;AAAA,iCAAb;AACAA,YAAAA,MAAM,CAACC,KAAP,GAAeF,GAAG,CAACE,KAAnB;AACAD,YAAAA,MAAM,CAACE,QAAP,GAAkB,EAAlB;;AAEA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGX,MAAM,CAACK,MAA3B,EAAmCM,CAAC,EAApC,EAAwC;AACpC,kBAAIC,KAAK,GAAGZ,MAAM,CAACW,CAAD,CAAlB;;AACA,kBAAIC,KAAK,CAACH,KAAN,IAAeF,GAAG,CAACE,KAAvB,EAA6B;AACzBH,gBAAAA,KAAK,GAAG,IAAR;AACAH,gBAAAA,GAAG,CAACU,IAAJ,CAASD,KAAT;AACA;AACH;AACJ;;AACD,gBAAGN,KAAK,IAAI,KAAZ,EAAkB;AACdH,cAAAA,GAAG,CAACU,IAAJ,CAASL,MAAT;AACH;AACJ;;AAED,cAAIM,IAAI,GAAG,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBC,YAArB;AAAA;AAAA,qCAAX;AACAH,UAAAA,IAAI,CAACI,OAAL,CAAaf,GAAb;AACH;;AAESgB,QAAAA,YAAY,GAAS;AAC3B,eAAKH,IAAL,CAAUI,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACH;;AAESC,QAAAA,WAAW,CAACC,IAAD,EAAcC,MAAd,EAA4B;AAC7C;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASI,IAAT,CAAc;AAAA;AAAA,wCAAWC,aAAzB,EAAwCH,IAAxC,EAA8C,KAAKI,KAAnD,EAA0D,KAAKC,QAA/D,EAAyE,KAAKC,SAA9E;AACH;AAGD;;;AACOZ,QAAAA,OAAO,CAACa,IAAD,EAAcC,OAAd,EAAmCC,QAAnC,EAAqD;AAC/D,eAAKL,KAAL,GAAaG,IAAb;AACA,eAAKF,QAAL,GAAgBG,OAAhB;AACA,eAAKF,SAAL,GAAiBG,QAAjB;AACH;;AApE6C,O;;;;;iBAGrB,I", "sourcesContent": ["import { _decorator, Component, ScrollView } from 'cc';\nconst {ccclass, property} = _decorator;\n\nimport { GeneralData } from \"../../general/GeneralProxy\";\nimport SkillCommand from \"../../skill/SkillCommand\";\nimport { Skill } from \"../../skill/SkillProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport ListLogic from '../../utils/ListLogic';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('SkillLogic')\nexport default class SkillLogic extends Component {\n\n    @property(ScrollView)\n    scrollView: ScrollView = null;\n\n    _general: GeneralData = null;\n    _type: number = 0;\n    _skillPos : number = -1;\n\n\n   \n    protected onEnable():void{\n       \n        EventMgr.on(LogicEvent.skillListInfo, this.onSkillList, this);\n        SkillCommand.getInstance().qrySkillList();\n    }\n\n    protected onDisable():void {\n        EventMgr.targetOff(this)\n    }\n\n    protected onSkillList(){\n    \n        var skills = SkillCommand.getInstance().proxy.skills;\n        var skillConfs = SkillCommand.getInstance().proxy.skillConfs;\n\n        var arr = [];\n        for (let i = 0; i < skillConfs.length; i++) {\n            var found = false;\n            let cfg = skillConfs[i];\n\n            let dSkill = new Skill();\n            dSkill.cfgId = cfg.cfgId;\n            dSkill.generals = [];\n\n            for (let j = 0; j < skills.length; j++) {\n                var skill = skills[j];\n                if (skill.cfgId == cfg.cfgId){\n                    found = true;\n                    arr.push(skill);\n                    break\n                }\n            }\n            if(found == false){\n                arr.push(dSkill);\n            }\n        }\n\n        var comp = this.scrollView.node.getComponent(ListLogic);\n        comp.setData(arr);\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n    protected onClickItem(data: Skill, target): void {\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.openSkillInfo, data, this._type, this._general, this._skillPos);\n    }\n\n\n    /** type:0普通展示、type:1 学习、2:武将查看 **/\n    public setData(type:number, general:GeneralData, skillPos: number) {\n        this._type = type;\n        this._general = general;\n        this._skillPos = skillPos;\n    }\n\n}\n"]}