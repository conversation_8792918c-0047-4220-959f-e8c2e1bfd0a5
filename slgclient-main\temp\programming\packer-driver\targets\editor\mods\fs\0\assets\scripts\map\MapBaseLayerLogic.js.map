{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"], "names": ["_decorator", "Component", "Node", "Prefab", "NodePool", "instantiate", "MapCommand", "ccclass", "property", "MapBaseLayerLogic", "Map", "onLoad", "_cmd", "getInstance", "onDestroy", "_itemMap", "for<PERSON>ach", "value", "key", "clear", "_itemPool", "addItem", "areaIndex", "data", "has", "id", "getIdByData", "item", "getItem", "createItem", "parent", "parent<PERSON>ayer", "list", "get", "set", "updateItem", "realItem", "setItemData", "removeItem", "put", "delete", "size", "node", "entryPrefab", "removeArea", "addArea", "udpateShowAreas", "addIndexs", "removeIndexs", "i", "length", "initNodeByArea"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;;AAGjDC,MAAAA,U;;;;;;;OAFD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAKTS,iB,WADpBF,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAACN,IAAD,C,UAERM,QAAQ,CAACL,MAAD,C,oCAJb,MACqBM,iBADrB,SAC+CR,SAD/C,CACyD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,6CAOrB,IAAIG,QAAJ,EAPqB;;AAAA,4CAQA,IAAIM,GAAJ,EARA;AAAA;;AAU3CC,QAAAA,MAAM,GAAS;AACrB,eAAKC,IAAL,GAAY;AAAA;AAAA,wCAAWC,WAAX,EAAZ;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKF,IAAL,GAAY,IAAZ;;AACA,eAAKG,QAAL,CAAcC,OAAd,CAAsB,CAACC,KAAD,EAA2BC,GAA3B,KAA2C;AAC7DD,YAAAA,KAAK,CAACE,KAAN;AACH,WAFD;;AAGA,eAAKJ,QAAL,CAAcI,KAAd;;AACA,eAAKJ,QAAL,GAAgB,IAAhB;;AACA,eAAKK,SAAL,CAAeD,KAAf;;AACA,eAAKC,SAAL,GAAiB,IAAjB;AACH;;AAEMC,QAAAA,OAAO,CAACC,SAAD,EAAoBC,IAApB,EAAqC;AAC/C,cAAI,KAAKR,QAAL,CAAcS,GAAd,CAAkBF,SAAlB,CAAJ,EAAkC;AAC9B,gBAAIG,EAAU,GAAG,KAAKC,WAAL,CAAiBH,IAAjB,CAAjB;AACA,gBAAII,IAAU,GAAG,KAAKC,OAAL,CAAaN,SAAb,EAAwBG,EAAxB,CAAjB;;AACA,gBAAIE,IAAI,IAAI,IAAZ,EAAkB;AACdA,cAAAA,IAAI,GAAG,KAAKE,UAAL,EAAP;AACAF,cAAAA,IAAI,CAACG,MAAL,GAAc,KAAKC,WAAnB;;AACA,kBAAIC,IAAuB,GAAG,KAAKjB,QAAL,CAAckB,GAAd,CAAkBX,SAAlB,CAA9B;;AACAU,cAAAA,IAAI,CAACE,GAAL,CAAS,KAAKR,WAAL,CAAiBH,IAAjB,CAAT,EAAiCI,IAAjC;AACH;;AACD,iBAAKQ,UAAL,CAAgBb,SAAhB,EAA2BC,IAA3B,EAAiCI,IAAjC;AACA,mBAAOA,IAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAEMQ,QAAAA,UAAU,CAACb,SAAD,EAAoBC,IAApB,EAA+BI,IAAU,GAAG,IAA5C,EAAwD;AAErE,cAAI,KAAKZ,QAAL,CAAcS,GAAd,CAAkBF,SAAlB,CAAJ,EAAkC;AAC9B,gBAAIc,QAAc,GAAGT,IAArB;;AACA,gBAAIA,IAAI,IAAI,IAAZ,EAAkB;AACd,kBAAIF,EAAU,GAAG,KAAKC,WAAL,CAAiBH,IAAjB,CAAjB;AACAa,cAAAA,QAAQ,GAAG,KAAKR,OAAL,CAAaN,SAAb,EAAwBG,EAAxB,CAAX;AACH;;AACD,gBAAIW,QAAJ,EAAc;AACV,mBAAKC,WAAL,CAAiBD,QAAjB,EAA2Bb,IAA3B;AACH;AACJ;AACJ,SArDoD,CAuDrD;;;AACOc,QAAAA,WAAW,CAACV,IAAD,EAAaJ,IAAb,EAA8B,CAE/C;;AAEMe,QAAAA,UAAU,CAAChB,SAAD,EAAoBG,EAApB,EAAyC;AACtD,cAAIO,IAAuB,GAAG,KAAKjB,QAAL,CAAckB,GAAd,CAAkBX,SAAlB,CAA9B;;AACA,cAAIU,IAAI,CAACR,GAAL,CAASC,EAAT,CAAJ,EAAkB;AACd,gBAAIE,IAAU,GAAGK,IAAI,CAACC,GAAL,CAASR,EAAT,CAAjB;;AACA,iBAAKL,SAAL,CAAemB,GAAf,CAAmBZ,IAAnB;;AACAK,YAAAA,IAAI,CAACQ,MAAL,CAAYf,EAAZ;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEMG,QAAAA,OAAO,CAACN,SAAD,EAAoBG,EAApB,EAAsC;AAChD,cAAIO,IAAuB,GAAG,KAAKjB,QAAL,CAAckB,GAAd,CAAkBX,SAAlB,CAA9B;;AACA,cAAIU,IAAI,CAACR,GAAL,CAASC,EAAT,CAAJ,EAAkB;AACd,mBAAOO,IAAI,CAACC,GAAL,CAASR,EAAT,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAESI,QAAAA,UAAU,GAAS;AACzB,cAAI,KAAKT,SAAL,CAAeqB,IAAf,KAAwB,CAA5B,EAA+B;AAC3B,mBAAO,KAAKrB,SAAL,CAAea,GAAf,EAAP;AACH;;AACD,cAAIS,IAAU,GAAGrC,WAAW,CAAC,KAAKsC,WAAN,CAA5B;AACA,iBAAOD,IAAP;AACH;;AAEME,QAAAA,UAAU,CAACtB,SAAD,EAA0B;AACvC,cAAI,KAAKP,QAAL,CAAcS,GAAd,CAAkBF,SAAlB,CAAJ,EAAkC;AAC9B,gBAAIU,IAAuB,GAAG,KAAKjB,QAAL,CAAckB,GAAd,CAAkBX,SAAlB,CAA9B;;AACAU,YAAAA,IAAI,CAAChB,OAAL,CAAa,CAAC0B,IAAD,EAAaxB,GAAb,KAA6B;AACtC,mBAAKE,SAAL,CAAemB,GAAf,CAAmBG,IAAnB;AACH,aAFD;AAGAV,YAAAA,IAAI,CAACb,KAAL;;AACA,iBAAKJ,QAAL,CAAcyB,MAAd,CAAqBlB,SAArB;AACH;AACJ;;AAEMuB,QAAAA,OAAO,CAACvB,SAAD,EAA0B;AACpC,cAAI,KAAKP,QAAL,CAAcS,GAAd,CAAkBF,SAAlB,KAAgC,KAApC,EAA2C;AACvC,iBAAKP,QAAL,CAAcmB,GAAd,CAAkBZ,SAAlB,EAA6B,IAAIZ,GAAJ,EAA7B;AACH;AACJ;;AAEMoC,QAAAA,eAAe,CAACC,SAAD,EAAsBC,YAAtB,EAAoD;AAEtE,eAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGD,YAAY,CAACE,MAAzC,EAAiDD,CAAC,EAAlD,EAAsD;AAClD,iBAAKL,UAAL,CAAgBI,YAAY,CAACC,CAAD,CAA5B;AACH;;AACD,eAAK,IAAIA,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGF,SAAS,CAACG,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,iBAAKJ,OAAL,CAAaE,SAAS,CAACE,CAAD,CAAtB;AACH;AACJ;;AAEME,QAAAA,cAAc,CAAC7B,SAAD,EAA0B,CAE9C;;AAEMI,QAAAA,WAAW,CAACH,IAAD,EAAoB;AAClC,iBAAOA,IAAI,CAACE,EAAZ;AACH;;AAxHoD,O;;;;;iBAEjC,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, Component, Node, Prefab, NodePool, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport MapCommand from \"./MapCommand\";\n\n@ccclass('MapBaseLayerLogic')\nexport default class MapBaseLayerLogic extends Component {\n    @property(Node)\n    parentLayer: Node = null;\n    @property(Prefab)\n    entryPrefab: Prefab = null;\n\n    protected _cmd: MapCommand;\n    protected _itemPool: NodePool = new NodePool();\n    protected _itemMap: Map<number, Map<number, Node>> = new Map<number, Map<number, Node>>();\n\n    protected onLoad(): void {\n        this._cmd = MapCommand.getInstance();\n    }\n\n    protected onDestroy(): void {\n        this._cmd = null;\n        this._itemMap.forEach((value: Map<number, Node>, key: number) => {\n            value.clear();\n        });\n        this._itemMap.clear();\n        this._itemMap = null;\n        this._itemPool.clear();\n        this._itemPool = null;\n    }\n\n    public addItem(areaIndex: number, data: any): Node {\n        if (this._itemMap.has(areaIndex)) {\n            let id: number = this.getIdByData(data);\n            let item: Node = this.getItem(areaIndex, id);\n            if (item == null) {\n                item = this.createItem();\n                item.parent = this.parentLayer;\n                let list: Map<number, Node> = this._itemMap.get(areaIndex);\n                list.set(this.getIdByData(data), item);\n            }\n            this.updateItem(areaIndex, data, item);\n            return item;\n        }\n        return null;\n    }\n\n    public updateItem(areaIndex: number, data: any, item: Node = null): void {\n        \n        if (this._itemMap.has(areaIndex)) {\n            let realItem: Node = item;\n            if (item == null) {\n                let id: number = this.getIdByData(data);\n                realItem = this.getItem(areaIndex, id);\n            }\n            if (realItem) {\n                this.setItemData(realItem, data);\n            }\n        }\n    }\n\n    //子类重写\n    public setItemData(item: Node, data: any): void {\n\n    }\n\n    public removeItem(areaIndex: number, id: number): boolean {\n        let list: Map<number, Node> = this._itemMap.get(areaIndex);\n        if (list.has(id)) {\n            let item: Node = list.get(id);\n            this._itemPool.put(item);\n            list.delete(id);\n            return true;\n        }\n        return false;\n    }\n\n    public getItem(areaIndex: number, id: number): Node {\n        let list: Map<number, Node> = this._itemMap.get(areaIndex);\n        if (list.has(id)) {\n            return list.get(id);\n        }\n        return null;\n    }\n\n    protected createItem(): Node {\n        if (this._itemPool.size() > 0) {\n            return this._itemPool.get();\n        }\n        let node: Node = instantiate(this.entryPrefab);\n        return node;\n    }\n\n    public removeArea(areaIndex: number): void {\n        if (this._itemMap.has(areaIndex)) {\n            let list: Map<number, Node> = this._itemMap.get(areaIndex);\n            list.forEach((node: Node, key: number) => {\n                this._itemPool.put(node);\n            });\n            list.clear();\n            this._itemMap.delete(areaIndex);\n        }\n    }\n\n    public addArea(areaIndex: number): void {\n        if (this._itemMap.has(areaIndex) == false) {\n            this._itemMap.set(areaIndex, new Map<number, Node>());\n        }\n    }\n\n    public udpateShowAreas(addIndexs: number[], removeIndexs: number[]): void {\n       \n        for (let i: number = 0; i < removeIndexs.length; i++) {\n            this.removeArea(removeIndexs[i]);\n        }\n        for (let i: number = 0; i < addIndexs.length; i++) {\n            this.addArea(addIndexs[i]);\n        }\n    }\n\n    public initNodeByArea(areaIndex: number): void {\n\n    }\n\n    public getIdByData(data: any): number {\n        return data.id;\n    }\n}\n"]}