{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts"], "names": ["Conscript", "General", "Role", "City", "Build", "Union", "NpcLevel", "Npc", "Basic"], "mappings": ";;;iBAIaA,S,EAQAC,O,EAWAC,I,EAqBAC,I,EAOAC,K,EAMAC,K,EAIAC,Q,EAIAC,G,EAIAC,K;;;;;;;;;;;;;;;;;;;;;;;;;2BAjEAR,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,6CACC,CADD;;AAAA,6CAEC,CAFD;;AAAA,8CAGE,CAHF;;AAAA,8CAIE,CAJF;;AAAA,6CAKC,CALD;AAAA;;AAAA,O;;yBAQVC,O,GAAN,MAAMA,OAAN,CAAc;AAAA;AAAA,wDACW,CADX;;AAAA,uDAEU,CAFV;;AAAA,2DAGc,CAHd;;AAAA,oDAIO,CAJP;;AAAA,oDAKO,CALP;;AAAA,qDAMQ,CANR;;AAAA,4CAOD,CAPC;;AAAA,yCAQJ,CARI;AAAA,UAQoB;;;AARpB,O;;sBAWRC,I,GAAN,MAAMA,IAAN,CAAW;AAAA;AAAA,wCACF,CADE;;AAAA,wCAEF,CAFE;;AAAA,yCAGD,CAHC;;AAAA,yCAID,CAJC;;AAAA,wCAKF,CALE;;AAAA,0CAMA,CANA;;AAAA,8CAOI,CAPJ;;AAAA,8CAQI,CARJ;;AAAA,+CASK,CATL;;AAAA,+CAUK,CAVL;;AAAA,8CAWI,CAXJ;;AAAA,kDAYQ,CAZR;;AAAA,+CAaK,CAbL;;AAAA,iDAcO,CAdP;;AAAA,gDAeM,CAfN;;AAAA,uDAgBa,CAhBb;;AAAA,oDAiBU,CAjBV;;AAAA,iDAkBO,CAlBP;AAAA,UAkBgB;;;AAlBhB,O;;sBAqBLC,I,GAAN,MAAMA,IAAN,CAAW;AAAA;AAAA,wCACC,CADD;;AAAA,2CAEI,CAFJ;;AAAA,iDAGO,CAHP;;AAAA,kDAIQ,CAJR;AAAA;;AAAA,O;;uBAOLC,K,GAAN,MAAMA,KAAN,CAAY;AAAA;AAAA,4CACC,CADD;;AAAA,+CAEI,CAFJ;;AAAA,kDAGO,CAHP;AAAA,UAGU;;;AAHV,O;;uBAMNC,K,GAAN,MAAMA,KAAN,CAAY;AAAA;AAAA,gDACK,CADL;AAAA;;AAAA,O;;0BAINC,Q,GAAN,MAAMA,QAAN,CAAgB;AAAA;AAAA;AAAA;;AAAA,O;;qBAIVC,G,GAAN,MAAMA,GAAN,CAAU;AAAA;AAAA;AAAA;;AAAA,O;;uBAIJC,K,GAAN,MAAMA,KAAN,CAAY;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O", "sourcesContent": ["// /**征兵相关**/\n// /**武将相关**/\n\nimport { _decorator } from 'cc';\nexport class Conscript {\n    cost_wood: number = 0;\n    cost_iron: number = 0;\n    cost_stone: number = 0;\n    cost_grain: number = 0;\n    cost_gold: number = 0;\n}\n\nexport class General {\n\tphysical_power_limit: number = 0;       //体力上限\n\tcost_physical_power: number = 0;        //消耗体力\n\trecovery_physical_power: number = 0;    //恢复体力\n\treclamation_time: number = 0;           //屯田消耗时间，单位秒\n\treclamation_cost: number = 0;           //屯田消耗政令\n\tdraw_general_cost: number = 0;          //抽卡消耗金币\n\tpr_point: number = 0;                   //合成一个武将或者的技能点\n\tlimit: number = 0;                      //武将数量上限\n}\n\nexport class Role {\n\twood: number = 0;\n\tiron: number = 0;\n\tstone: number = 0;\n\tgrain: number = 0;\n\tgold: number = 0;\n\tdecree: number = 0;\n\twood_yield: number = 0;\n\tiron_yield: number = 0;\n\tstone_yield: number = 0;\n\tgrain_yield: number = 0;\n\tgold_yield: number = 0;\n\tdepot_capacity: number = 0;\t\t //仓库初始容量\n\tbuild_limit: number = 0;\t\t //野外建筑上限\n\trecovery_time: number = 0;\n\tdecree_limit: number = 0;        //令牌上限\n\tcollect_times_limit: number = 0; //每日征收次数上限\n\tcollect_interval: number = 0;    //征收间隔\n\tpos_tag_limit: number = 0;       //位置标签上限\n}\n\nexport class City {\n    cost: number = 0;\n    durable: number = 0;\n\trecovery_time: number = 0;\n\ttransform_rate: number = 0;\n}\n\nexport class Build {\n\twar_free: number = 0;       //免战时间，单位秒\n\tgiveUp_time: number = 0;    //建筑放弃时间\n\tfortress_limit: number = 0; //要塞上限\n}\n\nexport class Union {\n\tmember_limit: number = 0;\n}\n\nexport class NpcLevel  {\n\tsoilders: number\n}\n\nexport class Npc {\n\tlevels: NpcLevel[]\n}\n\nexport class Basic {\n    conscript: Conscript;\n    general: General;\n    role: Role;\n    city: City;\n    build: Build;\n    union: Union;\n    npc: Npc;\n}\n"]}