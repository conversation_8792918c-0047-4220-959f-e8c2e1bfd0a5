{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts"], "names": ["_decorator", "Component", "Label", "<PERSON><PERSON>", "LoginCommand", "DateUtil", "Tools", "MapUICommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "CollectLogic", "onEnable", "console", "log", "on", "interiorOpenCollect", "onOpenCollect", "interiorCollect", "onCollect", "roleRes", "getInstance", "proxy", "getRoleResData", "goldLab", "string", "numberToShow", "gold_yield", "onDisable", "targetOff", "msg", "_data", "startCountDown", "onClickClose", "node", "active", "instance", "playClick", "onClickCollect", "stopCountDown", "schedule", "countDown", "timesLab", "cur_times", "limit", "diff", "leftTime", "next_time", "cdLab", "leftTimeStr", "collectBtn", "unschedule"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAGhCC,MAAAA,Y;;AACAC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OARH;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBZ,U;;yBAWPa,Y,WADpBF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACT,MAAD,C,oCAZb,MACqBU,YADrB,SAC0CZ,SAD1C,CACoD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAcnC,IAdmC;AAAA;;AAgBtCa,QAAAA,QAAQ,GAAO;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,mBAAvB,EAA4C,KAAKC,aAAjD,EAAgE,IAAhE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,eAAvB,EAAwC,KAAKC,SAA7C,EAAwD,IAAxD;AAEA,cAAIC,OAAO,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,cAAjC,EAAd;AACA,eAAKC,OAAL,CAAaC,MAAb,GAAsB;AAAA;AAAA,8BAAMC,YAAN,CAAmBN,OAAO,CAACO,UAA3B,CAAtB;AAEA;AAAA;AAAA,4CAAaN,WAAb,GAA2BL,mBAA3B;AACH;;AAGSY,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESZ,QAAAA,aAAa,CAACa,GAAD,EAAc;AACjCjB,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BgB,GAA9B;AACA,eAAKC,KAAL,GAAaD,GAAb;AACA,eAAKE,cAAL;AACH;;AAESb,QAAAA,SAAS,CAACW,GAAD,EAAc;AAC7BjB,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BgB,GAA9B;AACA,eAAKC,KAAL,GAAaD,GAAb;AACA,eAAKE,cAAL;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACH;;AAESC,QAAAA,cAAc,GAAQ;AAC5B;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAahB,WAAb,GAA2BH,eAA3B;AACH;;AAESc,QAAAA,cAAc,GAAE;AACtB,eAAKO,aAAL;AACA,eAAKC,QAAL,CAAc,KAAKC,SAAnB,EAA8B,GAA9B;AACA,eAAKA,SAAL;AACH;;AAEMA,QAAAA,SAAS,GAAG;AACf,eAAKC,QAAL,CAAcjB,MAAd,GAAuB,KAAKM,KAAL,CAAWY,SAAX,GAAuB,GAAvB,GAA6B,KAAKZ,KAAL,CAAWa,KAA/D;AACA,cAAIC,IAAI,GAAG;AAAA;AAAA,oCAASC,QAAT,CAAkB,KAAKf,KAAL,CAAWgB,SAA7B,CAAX;;AACA,cAAIF,IAAI,GAAC,CAAT,EAAW;AACP,iBAAKG,KAAL,CAAWvB,MAAX,GAAoB;AAAA;AAAA,sCAASwB,WAAT,CAAqB,KAAKlB,KAAL,CAAWgB,SAAhC,CAApB;AACA,iBAAKG,UAAL,CAAgBhB,IAAhB,CAAqBC,MAArB,GAA8B,KAA9B;AACH,WAHD,MAGK;AACD,iBAAKa,KAAL,CAAWvB,MAAX,GAAoB,QAApB;AACA,iBAAKyB,UAAL,CAAgBhB,IAAhB,CAAqBC,MAArB,GAA8B,IAA9B;AACH;AACJ;;AAEMI,QAAAA,aAAa,GAAG;AACnB,eAAKY,UAAL,CAAgB,KAAKV,SAArB;AACH;;AA1E+C,O;;;;;iBAGjC,I;;;;;;;iBAGG,I;;;;;;;iBAGD,I;;;;;;;iBAGI,I", "sourcesContent": ["// // Learn TypeScript:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html\n// // Learn Attribute:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html\n// // Learn life-cycle callbacks:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html\n\nimport { _decorator, Component, Label, Button } from 'cc';\nconst {ccclass, property} = _decorator;\n\nimport LoginCommand from \"../../login/LoginCommand\";\nimport DateUtil from \"../../utils/DateUtil\";\nimport { Tools } from \"../../utils/Tools\";\nimport MapUICommand from \"./MapUICommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('CollectLogic')\nexport default class CollectLogic extends Component {\n\n    @property(Label)\n    cdLab: Label = null;\n\n    @property(Label)\n    timesLab: Label = null;\n\n    @property(Label)\n    goldLab: Label = null;\n\n    @property(Button)\n    collectBtn: Button = null;\n\n    _data: any = null;\n\n    protected onEnable():void{\n        console.log(\"add interior_openCollect\");\n        EventMgr.on(LogicEvent.interiorOpenCollect, this.onOpenCollect, this);\n        EventMgr.on(LogicEvent.interiorCollect, this.onCollect, this);\n\n        var roleRes = LoginCommand.getInstance().proxy.getRoleResData();\n        this.goldLab.string = Tools.numberToShow(roleRes.gold_yield);\n\n        MapUICommand.getInstance().interiorOpenCollect();\n    }\n\n\n    protected onDisable():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onOpenCollect(msg:any):void{\n        console.log(\"onOpenCollect:\", msg);\n        this._data = msg;\n        this.startCountDown();\n    }\n\n    protected onCollect(msg:any):void{\n        console.log(\"onOpenCollect:\", msg);\n        this._data = msg;\n        this.startCountDown();\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n    protected onClickCollect(): void{\n        AudioManager.instance.playClick();\n        MapUICommand.getInstance().interiorCollect();\n    }\n\n    protected startCountDown(){\n        this.stopCountDown();\n        this.schedule(this.countDown, 1.0);\n        this.countDown();\n    }\n\n    public countDown() {\n        this.timesLab.string = this._data.cur_times + \"/\" + this._data.limit;\n        var diff = DateUtil.leftTime(this._data.next_time);\n        if (diff>0){\n            this.cdLab.string = DateUtil.leftTimeStr(this._data.next_time);\n            this.collectBtn.node.active = false;\n        }else{\n            this.cdLab.string = \"目前可以征收\";\n            this.collectBtn.node.active = true;\n        }\n    }\n\n    public stopCountDown() {\n        this.unschedule(this.countDown);\n    }\n\n}\n"]}