{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts"], "names": ["_decorator", "Component", "Label", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "RightCityItemLogic", "onLoad", "onDestroy", "_data", "onClickBg", "instance", "playClick", "emit", "scrollToMap", "x", "y", "setArmyData", "data", "labelInfo", "string", "name", "labelPos"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AAIvBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;yBAQTQ,kB,WADpBF,OAAO,CAAC,oBAAD,C,UAEHC,QAAQ,CAACL,KAAD,C,UAERK,QAAQ,CAACL,KAAD,C,oCAJb,MACqBM,kBADrB,SACgDP,SADhD,CAC0D;AAAA;AAAA;;AAAA;;AAAA;;AAAA,yCAMvB,IANuB;AAAA;;AAS5CQ,QAAAA,MAAM,GAAS,CAExB;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKC,KAAL,GAAa,IAAb;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKH,KAAT,EAAgB;AACZ;AAAA;AAAA,sCAASI,IAAT,CAAc;AAAA;AAAA,0CAAWC,WAAzB,EAAsC,KAAKL,KAAL,CAAWM,CAAjD,EAAoD,KAAKN,KAAL,CAAWO,CAA/D;AACH;AACJ;;AAEMC,QAAAA,WAAW,CAACC,IAAD,EAA0B;AACxC,eAAKT,KAAL,GAAaS,IAAb;;AACA,cAAI,KAAKT,KAAT,EAAgB;AACZ,iBAAKU,SAAL,CAAeC,MAAf,GAAwB,KAAKX,KAAL,CAAWY,IAAnC;AACA,iBAAKC,QAAL,CAAcF,MAAd,GAAuB,MAAM,KAAKX,KAAL,CAAWM,CAAjB,GAAqB,IAArB,GAA4B,KAAKN,KAAL,CAAWO,CAAvC,GAA2C,GAAlE;AACH;AACJ;;AA9BqD,O;;;;;iBAEnC,I;;;;;;;iBAED,I", "sourcesContent": ["import { _decorator, Component, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { MapCityData } from \"../MapCityProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('RightCityItemLogic')\nexport default class RightCityItemLogic extends Component {\n    @property(Label)\n    labelInfo: Label = null;\n    @property(Label)\n    labelPos: Label = null;\n\n    protected _data: MapCityData = null;\n\n\n    protected onLoad(): void {\n        \n    }\n\n    protected onDestroy(): void {\n        this._data = null;\n    }\n\n    protected onClickBg(): void {\n        AudioManager.instance.playClick();\n        if (this._data) {\n            EventMgr.emit(LogicEvent.scrollToMap, this._data.x, this._data.y);\n        }\n    }\n\n    public setArmyData(data: MapCityData): void {\n        this._data = data;\n        if (this._data) {\n            this.labelInfo.string = this._data.name;\n            this.labelPos.string = \"(\" + this._data.x + \", \" + this._data.y + \")\";\n        }\n    }\n}\n"]}