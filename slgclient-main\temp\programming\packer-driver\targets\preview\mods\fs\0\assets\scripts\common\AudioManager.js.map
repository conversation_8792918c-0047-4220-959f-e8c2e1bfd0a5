{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"], "names": ["AudioManager", "AudioClip", "assert", "clamp01", "warn", "resources", "LocalCache", "instance", "_instance", "init", "audioSource", "soundVolume", "getConfiguration", "_audioSource", "openMusic", "closeMusic", "isMusic", "state", "getMusic", "getSound", "undefined", "playMusic", "loop", "playing", "play", "playSound", "name", "path", "load", "err", "clip", "playOneShot", "playClick", "setMusicVolume", "flag", "console", "log", "volume", "setSoundVolume", "setMusic", "stop", "openSound", "setSound", "closeSound"], "mappings": ";;;uFAKaA,Y;;;;;;;;;;;;;;;AALQC,MAAAA,S,OAAAA,S;AAAwBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,O,OAAAA,O;AAASC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAC3DC,MAAAA,U,iBAAAA,U;;;;;;;8BAIIN,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,+CAaA,CAbA;AAAA;;AAIH,mBAARO,QAAQ,GAAI;AACnB,cAAI,KAAKC,SAAT,EAAoB;AAChB,mBAAO,KAAKA,SAAZ;AACH;;AAED,eAAKA,SAAL,GAAiB,IAAIR,YAAJ,EAAjB;AACA,iBAAO,KAAKQ,SAAZ;AACH;;AAID;AACAC,QAAAA,IAAI,CAAEC,WAAF,EAA4B;AAC5B,eAAKC,WAAL,GAAmB,KAAKC,gBAAL,CAAsB,KAAtB,IAA+B,CAA/B,GAAmC,CAAtD;AACAZ,UAAAA,YAAY,CAACa,YAAb,GAA4BH,WAA5B;;AAEA,cAAG,KAAKE,gBAAL,CAAsB,IAAtB,CAAH,EAA+B;AAC3B,iBAAKE,SAAL;AACH,WAFD,MAEK;AACD,iBAAKC,UAAL;AACH;AACJ;;AAEDH,QAAAA,gBAAgB,CAAEI,OAAF,EAAoB;AAChC,cAAIC,KAAJ;;AACA,cAAID,OAAJ,EAAa;AACTC,YAAAA,KAAK,GAAG;AAAA;AAAA,0CAAWC,QAAX,EAAR;AACH,WAFD,MAEO;AACHD,YAAAA,KAAK,GAAG;AAAA;AAAA,0CAAWE,QAAX,EAAR;AACH;;AAED,iBAAOF,KAAK,KAAKG,SAAV,IAAuBH,KAAvB,GAA+B,IAA/B,GAAsC,KAA7C;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACII,QAAAA,SAAS,CAAEC,IAAF,EAAiB;AAEtB,cAAMZ,WAAW,GAAGV,YAAY,CAACa,YAAjC;AACAX,UAAAA,MAAM,CAACQ,WAAD,EAAc,0BAAd,CAAN;AAEAA,UAAAA,WAAW,CAACY,IAAZ,GAAmBA,IAAnB;;AACA,cAAI,CAACZ,WAAW,CAACa,OAAjB,EAA0B;AACtBb,YAAAA,WAAW,CAACc,IAAZ;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAAEC,IAAF,EAAe;AACpB,cAAMhB,WAAW,GAAGV,YAAY,CAACa,YAAjC;AACAX,UAAAA,MAAM,CAACQ,WAAD,EAAc,0BAAd,CAAN;AACA,cAAIiB,IAAI,GAAG,eAAX;AACAtB,UAAAA,SAAS,CAACuB,IAAV,CAAeD,IAAI,GAAGD,IAAtB,EAA4BzB,SAA5B,EAAuC,CAAC4B,GAAD,EAAMC,IAAN,KAAc;AACjD,gBAAID,GAAJ,EAAS;AACLzB,cAAAA,IAAI,CAAC,yBAAD,EAA4ByB,GAA5B,CAAJ;AACA;AACH;;AAEDnB,YAAAA,WAAW,CAACqB,WAAZ,CAAwBD,IAAxB,EAA8B,KAAKnB,WAAnC;AACH,WAPD;AASH;;AAEDqB,QAAAA,SAAS,GAAE;AACP,eAAKP,SAAL,CAAe,OAAf;AACH;;AAEDQ,QAAAA,cAAc,CAAEC,IAAF,EAAgB;AAC1BC,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BF,IAA/B;AACA,cAAMxB,WAAW,GAAGV,YAAY,CAACa,YAAjC;AACAX,UAAAA,MAAM,CAACQ,WAAD,EAAc,0BAAd,CAAN;AAEAwB,UAAAA,IAAI,GAAG/B,OAAO,CAAC+B,IAAD,CAAd;AACAxB,UAAAA,WAAW,CAAC2B,MAAZ,GAAqBH,IAArB;AACH;;AAEDI,QAAAA,cAAc,CAAEJ,IAAF,EAAgB;AAC1B,eAAKvB,WAAL,GAAmBuB,IAAnB;AACH;;AAEDpB,QAAAA,SAAS,GAAI;AACT,eAAKmB,cAAL,CAAoB,GAApB;AACA,eAAKZ,SAAL,CAAe,IAAf;AACA;AAAA;AAAA,wCAAWkB,QAAX,CAAoB,IAApB;AACH;;AAEDxB,QAAAA,UAAU,GAAI;AACVf,UAAAA,YAAY,CAACa,YAAb,CAA0B2B,IAA1B;;AACA;AAAA;AAAA,wCAAWD,QAAX,CAAoB,KAApB;AACH;;AAEDE,QAAAA,SAAS,GAAI;AACT,eAAKH,cAAL,CAAoB,CAApB;AACA;AAAA;AAAA,wCAAWI,QAAX,CAAoB,IAApB;AACH;;AAEDC,QAAAA,UAAU,GAAI;AACV,eAAKL,cAAL,CAAoB,CAApB;AACA;AAAA;AAAA,wCAAWI,QAAX,CAAoB,KAApB;AACH;;AA7GqB,O;;sBAAb1C,Y;;sBAAAA,Y", "sourcesContent": ["import { _decorator, AudioClip, AudioSource, assert, clamp01, warn, resources } from \"cc\";\nimport { LocalCache } from \"../utils/LocalCache\";\n\n\n\nexport class AudioManager {\n    private static _instance: AudioManager;\n    private static _audioSource?: AudioSource;\n\n    static get instance () {\n        if (this._instance) {\n            return this._instance;\n        }\n\n        this._instance = new AudioManager();\n        return this._instance;\n    }\n\n    soundVolume: number = 1;\n\n    // init AudioManager in GameRoot.\n    init (audioSource: AudioSource) {\n        this.soundVolume = this.getConfiguration(false) ? 1 : 0;\n        AudioManager._audioSource = audioSource;\n        \n        if(this.getConfiguration(true)){\n            this.openMusic();\n        }else{\n            this.closeMusic();\n        }\n    }\n\n    getConfiguration (isMusic: boolean) {\n        let state;\n        if (isMusic) {\n            state = LocalCache.getMusic();\n        } else {\n            state = LocalCache.getSound();\n        }\n\n        return state === undefined || state ? true : false;\n    }\n\n    /**\n     * 播放音乐\n     * @param {String} name 音乐名称可通过constants.AUDIO_MUSIC 获取\n     * @param {Boolean} loop 是否循环播放\n     */\n    playMusic (loop: boolean) {\n        \n        const audioSource = AudioManager._audioSource!;\n        assert(audioSource, 'AudioManager not inited!');\n\n        audioSource.loop = loop;\n        if (!audioSource.playing) {\n            audioSource.play();\n        }\n    }\n\n    /**\n     * 播放音效\n     * @param {String} name 音效名称可通过constants.AUDIO_SOUND 获取\n     */\n    playSound (name:string) {\n        const audioSource = AudioManager._audioSource!;\n        assert(audioSource, 'AudioManager not inited!');\n        let path = '/audio/sound/';\n        resources.load(path + name, AudioClip, (err, clip)=> {\n            if (err) {\n                warn('load audioClip failed: ', err);\n                return;\n            }\n\n            audioSource.playOneShot(clip, this.soundVolume);\n        });\n\n    }\n\n    playClick(){\n        this.playSound(\"click\");\n    }\n\n    setMusicVolume (flag: number) {\n        console.log(\"setMusicVolume:\", flag);\n        const audioSource = AudioManager._audioSource!;\n        assert(audioSource, 'AudioManager not inited!');\n\n        flag = clamp01(flag);\n        audioSource.volume = flag;\n    }\n\n    setSoundVolume (flag: number) {\n        this.soundVolume = flag;\n    }\n\n    openMusic () {\n        this.setMusicVolume(0.2);\n        this.playMusic(true);\n        LocalCache.setMusic(true);\n    }\n\n    closeMusic () {\n        AudioManager._audioSource.stop();\n        LocalCache.setMusic(false);\n    }\n\n    openSound () {\n        this.setSoundVolume(1);\n        LocalCache.setSound(true);\n    }\n\n    closeSound () {\n        this.setSoundVolume(0);\n        LocalCache.setSound(false);\n    }\n}\n"]}