System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _decorator, Component, Canvas, view, UITransform, _dec, _dec2, _class, _temp, _crd, ccclass, property, requireComponent, CanvasAdapter;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Canvas = _cc.Canvas;
      view = _cc.view;
      UITransform = _cc.UITransform;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0d90b5JvC9GRIaKUptfriLg", "CanvasAdapter", undefined);

      ({
        ccclass,
        property,
        requireComponent
      } = _decorator);
      /**
       * Canvas适配组件
       * 解决Canvas在不同分辨率下的点击偏移问题
       */

      _export("CanvasAdapter", CanvasAdapter = (_dec = ccclass('CanvasAdapter'), _dec2 = requireComponent(Canvas), _dec(_class = _dec2(_class = (_temp = class CanvasAdapter extends Component {
        constructor() {
          super(...arguments);

          _defineProperty(this, "_canvas", null);

          _defineProperty(this, "_uiTransform", null);
        }

        onLoad() {
          this._canvas = this.getComponent(Canvas);
          this._uiTransform = this.getComponent(UITransform); // 确保Canvas不自动对齐屏幕，避免坐标系统混乱

          this._canvas.alignCanvasWithScreen = false; // 确保Canvas位置为原点

          this.node.setPosition(0, 0, 0); // 确保Canvas的锚点为(0.5, 0.5)，这是默认值

          if (this._uiTransform) {
            this._uiTransform.setAnchorPoint(0.5, 0.5);
          }

          console.log("[CanvasAdapter] Canvas\u9002\u914D\u5B8C\u6210: position=(0,0,0), anchor=(0.5,0.5), alignCanvasWithScreen=false");
        }

        onDestroy() {// 移除了屏幕尺寸变化监听，简化处理
        }
        /**
         * 获取Canvas适配信息
         */


        getCanvasInfo() {
          var visibleSize = view.getVisibleSize();
          var designSize = view.getDesignResolutionSize();
          var canvasSize = this._uiTransform.contentSize;
          return {
            visibleSize: visibleSize,
            designSize: designSize,
            canvasSize: canvasSize,
            scaleX: visibleSize.width / designSize.width,
            scaleY: visibleSize.height / designSize.height
          };
        }

      }, _temp)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=CanvasAdapter.js.map