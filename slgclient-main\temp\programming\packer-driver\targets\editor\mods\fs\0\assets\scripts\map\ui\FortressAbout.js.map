{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts"], "names": ["_decorator", "Component", "Node", "Label", "<PERSON><PERSON>", "Prefab", "instantiate", "ArmyCommand", "DateUtil", "MapCommand", "MapResType", "CityArmyItemLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "FortressAbout", "onLoad", "_cmd", "getInstance", "onEnable", "on", "updateBuilds", "onUpdateBuilds", "updateBuild", "onUpdateBuild", "deleteBuild", "onDeleteBuild", "initView", "onDisable", "targetOff", "i", "_armyCnt", "item", "armyItem", "parent", "armyLayer", "comp", "getComponent", "order", "_armyComps", "push", "updateArmyList", "armyList", "proxy", "getArmysByPos", "_data", "x", "y", "console", "log", "length", "level", "isOpenedArmy", "setArmyData", "cityId", "setData", "data", "nameLab", "string", "name", "lvLab", "startCountDownTime", "type", "SYS_FORTRESS", "upBtn", "node", "active", "destroyBtn", "FORTRESS", "areaIndex", "addIds", "removeIds", "updateIds", "buildProxy", "getBuild", "rid", "id", "onClickUpBuild", "instance", "playClick", "upBuild", "onClickDestroyBuild", "delBuild", "onClickClose", "stopCountDownTime", "schedule", "countDownTime", "isBuilding", "timeLab", "leftTimeStr", "endTime", "isUping", "isDestroying", "unschedule"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAGtDC,MAAAA,W;;AAEAC,MAAAA,Q;;AAEAC,MAAAA,U;;AACEC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,iB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAXH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;yBAcTiB,a,WADpBF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACb,KAAD,C,UAERa,QAAQ,CAACb,KAAD,C,UAERa,QAAQ,CAACb,KAAD,C,UAIRa,QAAQ,CAACZ,MAAD,C,UAGRY,QAAQ,CAACZ,MAAD,C,UAGRY,QAAQ,CAACX,MAAD,C,oCAlBb,MACqBY,aADrB,SAC2ChB,SAD3C,CACqD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,4CAoBpB,CApBoB;;AAAA,yCAqBjB,IArBiB;;AAAA,8CAsBL,EAtBK;;AAAA;AAAA;;AAyBvCiB,QAAAA,MAAM,GAAS;AAErB,eAAKC,IAAL,GAAY;AAAA;AAAA,wCAAWC,WAAX,EAAZ;AAEH;;AAEDC,QAAAA,QAAQ,GAAS;AACb;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,YAAvB,EAAqC,KAAKC,cAA1C,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AAEA,eAAKC,QAAL;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESF,QAAAA,QAAQ,GAAS;AACvB,eAAK,IAAIG,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKC,QAAjC,EAA2CD,CAAC,EAA5C,EAAgD;AAC5C,gBAAIE,IAAI,GAAG5B,WAAW,CAAC,KAAK6B,QAAN,CAAtB;AACAD,YAAAA,IAAI,CAACE,MAAL,GAAc,KAAKC,SAAnB;AACA,gBAAIC,IAAuB,GAAGJ,IAAI,CAACK,YAAL;AAAA;AAAA,uDAA9B;AACAD,YAAAA,IAAI,CAACE,KAAL,GAAaR,CAAC,GAAG,CAAjB;;AACA,iBAAKS,UAAL,CAAgBC,IAAhB,CAAqBJ,IAArB;AACH;AAEJ;;AAGSK,QAAAA,cAAc,GAAS;AAC7B,cAAIC,QAAoB,GAAG;AAAA;AAAA,0CAAYxB,WAAZ,GAA0ByB,KAA1B,CAAgCC,aAAhC,CAA8C,KAAKC,KAAL,CAAWC,CAAzD,EAA4D,KAAKD,KAAL,CAAWE,CAAvE,CAA3B;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BP,QAA/B,EAAyC,KAAKG,KAA9C;;AACA,eAAK,IAAIf,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKS,UAAL,CAAgBW,MAA5C,EAAoDpB,CAAC,EAArD,EAAyD;AACrD,gBAAI,KAAKe,KAAL,CAAWM,KAAX,GAAmBrB,CAAvB,EAAyB;AACrB,mBAAKS,UAAL,CAAgBT,CAAhB,EAAmBsB,YAAnB,CAAgC,IAAhC,EAAsC,IAAtC;AACH,aAFD,MAEK;AACD,mBAAKb,UAAL,CAAgBT,CAAhB,EAAmBsB,YAAnB,CAAgC,KAAhC,EAAuC,IAAvC;AACH;;AAED,iBAAKb,UAAL,CAAgBT,CAAhB,EAAmBuB,WAAnB,CAA+B,CAA/B,EAAkC,IAAlC;;AACA,gBAAIX,QAAQ,CAACQ,MAAT,GAAkBpB,CAAtB,EAAwB;AACpB,mBAAKS,UAAL,CAAgBT,CAAhB,EAAmBuB,WAAnB,CAA+BX,QAAQ,CAACZ,CAAD,CAAR,CAAYwB,MAA3C,EAAmDZ,QAAQ,CAACZ,CAAD,CAA3D;AACH;AACJ;AACJ;;AAEMyB,QAAAA,OAAO,CAACC,IAAD,EAA2B;AACrC,eAAKX,KAAL,GAAaW,IAAb;AACA,eAAKC,OAAL,CAAaC,MAAb,GAAsBF,IAAI,CAACG,IAA3B;AACA,eAAKC,KAAL,CAAWF,MAAX,GAAoB,QAAQF,IAAI,CAACL,KAAjC;AACA,eAAKU,kBAAL;AACA,eAAKpB,cAAL;;AAEA,cAAI,KAAKI,KAAL,CAAWiB,IAAX,IAAmB;AAAA;AAAA,wCAAWC,YAAlC,EAA+C;AAC3C,iBAAKC,KAAL,CAAWC,IAAX,CAAgBC,MAAhB,GAAyB,KAAzB;AACA,iBAAKC,UAAL,CAAgBF,IAAhB,CAAqBC,MAArB,GAA8B,KAA9B;AACH,WAHD,MAGM,IAAG,KAAKrB,KAAL,CAAWiB,IAAX,IAAmB;AAAA;AAAA,wCAAWM,QAAjC,EAA0C;AAC5C,iBAAKJ,KAAL,CAAWC,IAAX,CAAgBC,MAAhB,GAAyB,IAAzB;AACA,iBAAKC,UAAL,CAAgBF,IAAhB,CAAqBC,MAArB,GAA8B,IAA9B;AACH;AACJ;;AAES5C,QAAAA,cAAc,CAAC+C,SAAD,EAAoBC,MAApB,EAAsCC,SAAtC,EAA2DC,SAA3D,EAAsF;AAC1GxB,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BsB,SAA/B;;AAEA,eAAK,IAAIzC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGwC,MAAM,CAACpB,MAAnC,EAA2CpB,CAAC,EAA5C,EAAgD;AAC5C,gBAAI0B,IAAI,GAAG,KAAKvC,IAAL,CAAUwD,UAAV,CAAqBC,QAArB,CAA8BJ,MAAM,CAACxC,CAAD,CAApC,CAAX;;AACA,gBAAI0B,IAAI,CAACV,CAAL,IAAU,KAAKD,KAAL,CAAWC,CAArB,IAA0BU,IAAI,CAACT,CAAL,IAAU,KAAKF,KAAL,CAAWE,CAAnD,EAAqD;AACjD,mBAAKQ,OAAL,CAAaC,IAAb;AACH;AACJ;;AAED,eAAK,IAAI1B,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGyC,SAAS,CAACrB,MAAtC,EAA8CpB,CAAC,EAA/C,EAAmD;AAC/CkB,YAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqB,KAAKJ,KAA1B;;AACA,gBAAG,KAAKA,KAAL,CAAW8B,GAAX,IAAkB,CAArB,EAAuB;AACnB,mBAAKV,IAAL,CAAU/B,MAAV,GAAmB,IAAnB;AACH;AACJ;;AAED,eAAK,IAAIJ,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG0C,SAAS,CAACtB,MAAtC,EAA8CpB,CAAC,EAA/C,EAAmD;AAC/C,gBAAI0B,IAAI,GAAG,KAAKvC,IAAL,CAAUwD,UAAV,CAAqBC,QAArB,CAA8BF,SAAS,CAAC1C,CAAD,CAAvC,CAAX;;AACA,gBAAI0B,IAAI,CAACV,CAAL,IAAU,KAAKD,KAAL,CAAWC,CAArB,IAA0BU,IAAI,CAACT,CAAL,IAAU,KAAKF,KAAL,CAAWE,CAAnD,EAAqD;AACjD,mBAAKQ,OAAL,CAAaC,IAAb;AACH;AACJ;AACJ;;AAEShC,QAAAA,aAAa,CAACgC,IAAD,EAA2B;AAC/C,cAAGA,IAAI,CAACV,CAAL,IAAU,KAAKD,KAAL,CAAWC,CAArB,IAA0BU,IAAI,CAACT,CAAL,IAAU,KAAKF,KAAL,CAAWE,CAAlD,EAAoD;AAChD,iBAAKQ,OAAL,CAAaC,IAAb;AACH;AACH;;AAES9B,QAAAA,aAAa,CAACkD,EAAD,EAAa9B,CAAb,EAAwBC,CAAxB,EAAyC;AAC5D,cAAGD,CAAC,IAAI,KAAKD,KAAL,CAAWC,CAAhB,IAAqBC,CAAC,IAAI,KAAKF,KAAL,CAAWE,CAAxC,EAA0C;AACtC,iBAAKkB,IAAL,CAAU/B,MAAV,GAAmB,IAAnB;AACH;AACJ;;AAES2C,QAAAA,cAAc,GAAS;AAC7B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AACA,eAAK9D,IAAL,CAAU+D,OAAV,CAAkB,KAAKnC,KAAL,CAAWC,CAA7B,EAAgC,KAAKD,KAAL,CAAWE,CAA3C;AACH;;AAESkC,QAAAA,mBAAmB,GAAS;AAClC;AAAA;AAAA,4CAAaH,QAAb,CAAsBC,SAAtB;;AACA,eAAK9D,IAAL,CAAUiE,QAAV,CAAmB,KAAKrC,KAAL,CAAWC,CAA9B,EAAiC,KAAKD,KAAL,CAAWE,CAA5C;AACH;;AAESoC,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaL,QAAb,CAAsBC,SAAtB;AACA,eAAKd,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAGML,QAAAA,kBAAkB,GAAE;AACvB,eAAKuB,iBAAL;AACA,eAAKC,QAAL,CAAc,KAAKC,aAAnB,EAAkC,GAAlC;AACA,eAAKA,aAAL;AACH;;AAEMA,QAAAA,aAAa,GAAG;AACnB,cAAI,KAAKzC,KAAL,CAAW0C,UAAX,EAAJ,EAA4B;AACxB,iBAAKC,OAAL,CAAa9B,MAAb,GAAsB,WAAW;AAAA;AAAA,sCAAS+B,WAAT,CAAqB,KAAK5C,KAAL,CAAW6C,OAAhC,CAAjC;AACH,WAFD,MAEO,IAAG,KAAK7C,KAAL,CAAW8C,OAAX,EAAH,EAAwB;AAC3B,iBAAKH,OAAL,CAAa9B,MAAb,GAAsB,WAAW;AAAA;AAAA,sCAAS+B,WAAT,CAAqB,KAAK5C,KAAL,CAAW6C,OAAhC,CAAjC;AACH,WAFM,MAEA,IAAG,KAAK7C,KAAL,CAAW+C,YAAX,EAAH,EAA6B;AAChC,iBAAKJ,OAAL,CAAa9B,MAAb,GAAsB,WAAW;AAAA;AAAA,sCAAS+B,WAAT,CAAqB,KAAK5C,KAAL,CAAW6C,OAAhC,CAAjC;AACH,WAFM,MAEF;AACD,iBAAKF,OAAL,CAAa9B,MAAb,GAAsB,EAAtB;AACA,iBAAK0B,iBAAL;AACH;AACJ;;AAEMA,QAAAA,iBAAiB,GAAG;AACvB,eAAKS,UAAL,CAAgB,KAAKP,aAArB;AACH;;AAlKgD,O;;;;;iBAE/B,I;;;;;;;iBAED,I;;;;;;;iBAEF,I;;;;;;;iBAEE,I;;;;;;;iBAID,I;;;;;;;iBAGK,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, Node, Label, Button, Prefab, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport { ArmyData } from \"../../general/ArmyProxy\";\nimport DateUtil from \"../../utils/DateUtil\";\nimport { MapBuildData } from \"../MapBuildProxy\";\nimport MapCommand from \"../MapCommand\";\nimport { MapResType } from \"../MapProxy\";\nimport CityArmyItemLogic from \"./CityArmyItemLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('FortressAbout')\nexport default class FortressAbout extends Component {\n    @property(Node)\n    armyLayer: Node = null;\n    @property(Label)\n    nameLab: Label = null;\n    @property(Label)\n    lvLab: Label = null;\n    @property(Label)\n    timeLab: Label = null;\n\n\n    @property(Button)\n    upBtn: Button = null;\n\n    @property(Button)\n    destroyBtn: Button = null;\n\n    @property(Prefab)\n    armyItem: Prefab = null;\n\n    protected _armyCnt: number = 5;//队伍数量 固定值\n    protected _data: MapBuildData = null;\n    protected _armyComps: CityArmyItemLogic[] = [];\n    protected _cmd: MapCommand;\n\n    protected onLoad(): void {\n\n        this._cmd = MapCommand.getInstance();\n        \n    }\n\n    onEnable (): void{\n        EventMgr.on(LogicEvent.updateBuilds, this.onUpdateBuilds, this);\n        EventMgr.on(LogicEvent.updateBuild, this.onUpdateBuild, this);\n        EventMgr.on(LogicEvent.deleteBuild, this.onDeleteBuild, this);\n\n        this.initView();\n    }\n\n    protected onDisable(): void {\n        EventMgr.targetOff(this);\n    }\n\n    protected initView(): void {\n        for (let i: number = 0; i < this._armyCnt; i++) {\n            let item = instantiate(this.armyItem);\n            item.parent = this.armyLayer;\n            let comp: CityArmyItemLogic = item.getComponent(CityArmyItemLogic);\n            comp.order = i + 1;\n            this._armyComps.push(comp);\n        }\n\n    }\n\n\n    protected updateArmyList(): void {\n        let armyList: ArmyData[] = ArmyCommand.getInstance().proxy.getArmysByPos(this._data.x, this._data.y);\n        console.log(\"updateArmyList:\", armyList, this._data);\n        for (let i: number = 0; i < this._armyComps.length; i++) {\n            if (this._data.level > i){\n                this._armyComps[i].isOpenedArmy(true, true);\n            }else{\n                this._armyComps[i].isOpenedArmy(false, true);\n            }\n            \n            this._armyComps[i].setArmyData(0, null);\n            if (armyList.length > i){\n                this._armyComps[i].setArmyData(armyList[i].cityId, armyList[i]);\n            }\n        }\n    }\n\n    public setData(data: MapBuildData): void {\n        this._data = data;\n        this.nameLab.string = data.name;\n        this.lvLab.string = \"lv:\" + data.level;\n        this.startCountDownTime();\n        this.updateArmyList();\n\n        if (this._data.type == MapResType.SYS_FORTRESS){\n            this.upBtn.node.active = false;\n            this.destroyBtn.node.active = false;\n        }else if(this._data.type == MapResType.FORTRESS){\n            this.upBtn.node.active = true;\n            this.destroyBtn.node.active = true;\n        }\n    }\n\n    protected onUpdateBuilds(areaIndex: number, addIds: number[], removeIds: number[], updateIds: number[]): void {\n        console.log(\"onUpdateBuilds:\", removeIds);\n\n        for (let i: number = 0; i < addIds.length; i++) {\n            let data = this._cmd.buildProxy.getBuild(addIds[i]);\n            if (data.x == this._data.x && data.y == this._data.y){\n                this.setData(data);\n            }\n        }\n\n        for (let i: number = 0; i < removeIds.length; i++) {\n            console.log(\"data:\", this._data);\n            if(this._data.rid == 0){\n                this.node.parent = null;\n            }\n        }\n\n        for (let i: number = 0; i < updateIds.length; i++) {\n            let data = this._cmd.buildProxy.getBuild(updateIds[i]);\n            if (data.x == this._data.x && data.y == this._data.y){\n                this.setData(data);\n            }\n        }\n    }\n\n    protected onUpdateBuild(data: MapBuildData): void {\n       if(data.x == this._data.x && data.y == this._data.y){\n           this.setData(data);\n       }\n    }\n\n    protected onDeleteBuild(id: number, x: number, y: number): void {\n        if(x == this._data.x && y == this._data.y){\n            this.node.parent = null;\n        }\n    }\n\n    protected onClickUpBuild(): void {\n        AudioManager.instance.playClick();\n        this._cmd.upBuild(this._data.x, this._data.y);\n    }\n\n    protected onClickDestroyBuild(): void {\n        AudioManager.instance.playClick();\n        this._cmd.delBuild(this._data.x, this._data.y);\n    }\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n    }\n\n\n    public startCountDownTime(){\n        this.stopCountDownTime();\n        this.schedule(this.countDownTime, 1.0);\n        this.countDownTime();\n    }\n\n    public countDownTime() {\n        if (this._data.isBuilding()){\n            this.timeLab.string = \"建设中...\" + DateUtil.leftTimeStr(this._data.endTime);\n        } else if(this._data.isUping()){\n            this.timeLab.string = \"升级中...\" + DateUtil.leftTimeStr(this._data.endTime);\n        } else if(this._data.isDestroying()){\n            this.timeLab.string = \"拆除中...\" + DateUtil.leftTimeStr(this._data.endTime);\n        }else{\n            this.timeLab.string = \"\";\n            this.stopCountDownTime();\n        }\n    }\n\n    public stopCountDownTime() {\n        this.unschedule(this.countDownTime);\n    }\n}\n"]}