{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts"], "names": ["_decorator", "Component", "SpriteFrame", "Label", "Sprite", "SkillCommand", "ccclass", "property", "SkillIconLogic", "setData", "data", "gdata", "_data", "getComponent", "spriteFrame", "conf", "getInstance", "proxy", "getSkillCfg", "cfgId", "trigger", "sps", "length", "lvLab", "string", "lv", "isEmpty", "getSkill"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAK7CC,MAAAA,Y;;;;;;;OAJD;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBP,U;;yBASPQ,c,WADpBF,OAAO,CAAC,gBAAD,C,UAGHC,QAAQ,CAAC,CAACL,WAAD,CAAD,C,UAGRK,QAAQ,CAACJ,KAAD,C,oCANb,MACqBK,cADrB,SAC4CP,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,yCAQnC,IARmC;AAAA;;AAU3CQ,QAAAA,OAAO,CAACC,IAAD,EAAaC,KAAb,EAA+B;AAEzC,eAAKC,KAAL,GAAaF,IAAb;;AACA,cAAG,KAAKE,KAAL,IAAc,IAAjB,EAAsB;AAClB,iBAAKC,YAAL,CAAkBT,MAAlB,EAA0BU,WAA1B,GAAwC,IAAxC;AACH,WAFD,MAEK;AACD,gBAAIC,IAAI,GAAG;AAAA;AAAA,8CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,WAAjC,CAA6CR,IAAI,CAACS,KAAlD,CAAX;;AACA,gBAAGJ,IAAI,CAACK,OAAL,IAAgB,KAAKC,GAAL,CAASC,MAA5B,EAAmC;AAC/B,mBAAKT,YAAL,CAAkBT,MAAlB,EAA0BU,WAA1B,GAAwC,KAAKO,GAAL,CAASN,IAAI,CAACK,OAAL,GAAa,CAAtB,CAAxC;AACH,aAFD,MAEK;AACD,mBAAKP,YAAL,CAAkBT,MAAlB,EAA0BU,WAA1B,GAAwC,IAAxC;AACH;AACJ;;AAED,cAAGH,KAAH,EAAS;AACL,gBAAG,KAAKY,KAAR,EAAc;AACV,mBAAKA,KAAL,CAAWC,MAAX,GAAoB,QAAQb,KAAK,CAACc,EAAlC;AACH;AACJ,WAJD,MAIK;AACD,gBAAG,KAAKF,KAAR,EAAc;AACV,mBAAKA,KAAL,CAAWC,MAAX,GAAoB,EAApB;AACH;AACJ;AAEJ;;AAEME,QAAAA,OAAO,GAAW;AACrB,iBAAO,KAAKd,KAAL,IAAc,IAArB;AACH;;AAEMe,QAAAA,QAAQ,GAAS;AACpB,iBAAO,KAAKf,KAAZ;AACH;;AA1CiD,O;;;;;iBAG9B,E;;;;;;;iBAGN,I", "sourcesContent": ["import { _decorator, Component, SpriteFrame, Label, Sprite } from 'cc';\nconst {ccclass, property} = _decorator;\n\n\nimport { gSkill } from \"../../general/GeneralProxy\";\nimport SkillCommand from \"../../skill/SkillCommand\";\nimport { Skill } from \"../../skill/SkillProxy\";\n\n\n@ccclass('SkillIconLogic')\nexport default class SkillIconLogic extends Component {\n\n    @property([SpriteFrame])\n    sps:SpriteFrame[] = [];\n\n    @property(Label)\n    lvLab:Label = null;\n\n    _data: Skill = null;\n\n    public setData(data:Skill, gdata:gSkill):void{\n\n        this._data = data;\n        if(this._data == null){\n            this.getComponent(Sprite).spriteFrame = null;\n        }else{\n            var conf = SkillCommand.getInstance().proxy.getSkillCfg(data.cfgId);\n            if(conf.trigger <= this.sps.length){\n                this.getComponent(Sprite).spriteFrame = this.sps[conf.trigger-1];\n            }else{\n                this.getComponent(Sprite).spriteFrame = null;\n            }\n        }\n\n        if(gdata){\n            if(this.lvLab){\n                this.lvLab.string = \"lv:\" + gdata.lv;\n            }\n        }else{\n            if(this.lvLab){\n                this.lvLab.string = \"\";\n            }\n        }\n    \n    }\n\n    public isEmpty():boolean {\n        return this._data == null;\n    }\n\n    public getSkill():Skill {\n        return this._data;\n    }\n\n}\n"]}