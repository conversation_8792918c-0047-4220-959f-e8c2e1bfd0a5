{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts"], "names": ["_decorator", "Component", "Label", "DateUtil", "ccclass", "property", "ChatItemLogic", "onLoad", "updateItem", "data", "time", "converTimeStr", "name<PERSON><PERSON><PERSON>", "string", "nick_name", "msg"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAGSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AAFzBC,MAAAA,Q;;;;;;;OAKD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAETM,a,WADpBF,OAAO,CAAC,eAAD,C,UAIHC,QAAQ,CAACH,KAAD,C,oCAJb,MACqBI,aADrB,SAC2CL,SAD3C,CACqD;AAAA;AAAA;;AAAA;AAAA;;AAOvCM,QAAAA,MAAM,GAAO,CACtB;;AAESC,QAAAA,UAAU,CAACC,IAAD,EAAmB;AACnC,cAAIC,IAAI,GAAG;AAAA;AAAA,oCAASC,aAAT,CAAuBF,IAAI,CAACC,IAAL,GAAY,IAAnC,CAAX;AACA,eAAKE,SAAL,CAAeC,MAAf,GAAwBH,IAAI,GAAG,GAAP,GAAaD,IAAI,CAACK,SAAlB,GAA8B,GAA9B,GAAoCL,IAAI,CAACM,GAAjE;AACH;;AAbgD,O;;;;;iBAI9B,I", "sourcesContent": ["\nimport DateUtil from \"../utils/DateUtil\";\nimport { ChatMsg } from \"./ChatProxy\";\nimport { _decorator, Component, Label } from \"cc\";\n\n\nconst { ccclass, property } = _decorator;\n@ccclass('ChatItemLogic')\nexport default class ChatItemLogic extends Component {\n\n\n    @property(Label)\n    nameLabel: Label = null;\n\n\n    protected onLoad():void{\n    }\n\n    protected updateItem(data:ChatMsg):void{\n        var time = DateUtil.converTimeStr(data.time * 1000);\n        this.nameLabel.string = time + \" \" + data.nick_name + \":\"  +data.msg;\n    }\n\n}\n"]}