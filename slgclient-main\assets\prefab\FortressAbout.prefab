[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "FortressAbout", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}, {"__id__": 33}, {"__id__": 125}], "_active": true, "_components": [{"__id__": 137}, {"__id__": 139}, {"__id__": 141}, {"__id__": 143}, {"__id__": 145}, {"__id__": 147}], "_prefab": {"__id__": 149}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "034d7NNnFNzLMkufrFlO1T"}, {"__type__": "791008KQaNLzIyR/L9Rataa", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "scaleType": 1, "alignmentType": 3, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4/YVw+/xEXKpKDJWQ61cL"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cjcsT20FOT5cl2hSBT0ZA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eEs+Ui1xOtpDZaB4Qvhm6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d0KY6doUlPtIteVa5BBwmg"}, {"__type__": "cc.Node", "_name": "btnClose", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 23}, {"__id__": 26}, {"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": -587.5, "y": 320.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}, {"__id__": 20}], "_prefab": {"__id__": 22}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "707ebe96-6a9b-4226-bc49-362dfba2f47a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90NUJ7SN5G+5mCEKaIX6yy"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3VuYni3pL+6xPOvUOr6tR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4byPBw3cBDBJ3FKojcGSpW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 85, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8hkjSgxBKJYjD9ejuiZYE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c85kHju8FKFrfPRKtVcg+e"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 24}, "clickEvents": [{"__id__": 25}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "707ebe96-6a9b-4226-bc49-362dfba2f47a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 13}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4FBcE5a5DsYXaJA7L+1oB"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "79f56J5eZdKV4BkXv7s95Os", "handler": "onClickClose", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 27}, "_alignFlags": 9, "_target": null, "_left": 10, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6f/pg7CgxKnqzMalriF/W1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 29}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fr43Abg5L3LN14WvJDjk5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 85, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70KOzSlgtOTZCxWSFpSdGh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74ZxXtxr5GCI/Ji+XvssUr"}, {"__type__": "cc.Node", "_name": "mainNode", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 34}, {"__id__": 59}, {"__id__": 84}, {"__id__": 92}, {"__id__": 100}, {"__id__": 108}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 118}, {"__id__": 120}, {"__id__": 122}], "_prefab": {"__id__": 124}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -43, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btnUp", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 35}], "_active": true, "_components": [{"__id__": 51}, {"__id__": 54}, {"__id__": 56}], "_prefab": {"__id__": 58}, "_lpos": {"__type__": "cc.Vec3", "x": -120, "y": -90, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 34}, "_children": [{"__id__": 36}], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 50}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 512, "_parent": {"__id__": 35}, "_children": [], "_active": true, "_components": [{"__id__": 37}, {"__id__": 39}, {"__id__": 41}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 38}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "升级", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 38, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0DEyRaaFK6Kq3AglkTnOx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 40}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93IBgAIThCcYfVUCP1vOH4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 47.88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1p1uNvfpPAqwUTN9VFfhp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efeF81SplMXIPQwHau0JAe"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 45}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d572b55-9243-4277-b649-d29ac4fcb01f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62UjG5d7xNt7CVgclC8Y98"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 47}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fLCzPOMZL6ZWkRK1Rbdc2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 49}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4MddWH1xESolZIbd4Ocz/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "88BVyFA4JGLYMKkkURoA7X"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 52}, "clickEvents": [{"__id__": 53}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "c6ddc7b0-06d3-407a-b6a6-72b2f5afc968@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.1, "_target": {"__id__": 35}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2bbfaiSpHG5tw7jeg1JDB"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "79f56J5eZdKV4BkXv7s95Os", "handler": "onClickUpBuild", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 55}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58k7CXNmhILbrGxE0+4qP1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 57}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eedbkjxcFBGJrpAxgSd9Tt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "221ZhYJqxE0p+RIDDM7mHU"}, {"__type__": "cc.Node", "_name": "btnDestroy", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 60}], "_active": true, "_components": [{"__id__": 76}, {"__id__": 79}, {"__id__": 81}], "_prefab": {"__id__": 83}, "_lpos": {"__type__": "cc.Vec3", "x": 120, "y": -90, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 59}, "_children": [{"__id__": 61}], "_active": true, "_components": [{"__id__": 69}, {"__id__": 71}, {"__id__": 73}], "_prefab": {"__id__": 75}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 512, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 64}, {"__id__": 66}], "_prefab": {"__id__": 68}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 63}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "拆除", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 38, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78VMZLSuhCFKFFhcgWScpG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 65}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0FXSQ+81CkoRO2yHJh1rl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 67}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 47.88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84RDtfzwtM4ZN6WF8f5mkA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdBZqZBCpKwIgtglycfFlG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 70}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d572b55-9243-4277-b649-d29ac4fcb01f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e317pafUlBW7yyZ17a+aEx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 72}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99sMgoG6VK6aDZMHSrAHNx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 74}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ectUBwDzpOp69gPJ8XRQXa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5enpbZvHJDfaprLJy5bifS"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 77}, "clickEvents": [{"__id__": 78}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "c6ddc7b0-06d3-407a-b6a6-72b2f5afc968@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.1, "_target": {"__id__": 60}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcoj4H+A5JBoKJbG0JcHAS"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "79f56J5eZdKV4BkXv7s95Os", "handler": "onClickDestroyBuild", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 80}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55V46RIgNK4K3UyY1GsW89"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 82}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1KrUNLDVEaoqnZFmvuN83"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58caE4UZVNyLv+qXUUlmUZ"}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 85}, {"__id__": 87}, {"__id__": 89}], "_prefab": {"__id__": 91}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 119.459, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 86}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1ecf2021-4458-495e-aec1-458624434baf@4ccec", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "1ecf2021-4458-495e-aec1-458624434baf", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dIg5aW2dIh6K2zfII4Njw"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 88}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3TvBU6pRBGKPb/SaR0WkT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 90}, "_contentSize": {"__type__": "cc.Size", "width": 368, "height": 266}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26BB86qIBM4YxHhhxZMye2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74PI7vn7ZK9ogEXP4boMl6"}, {"__type__": "cc.Node", "_name": "nameLab", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 93}, {"__id__": 95}, {"__id__": 97}], "_prefab": {"__id__": 99}, "_lpos": {"__type__": "cc.Vec3", "x": -7, "y": 2.432, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "__prefab": {"__id__": 94}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "157yU+lThHYY/PCYkTG432"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "__prefab": {"__id__": 96}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66l/1RYC1OYalGzXk/dxHd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "__prefab": {"__id__": 98}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26zookwDRBuplojiB2lDe5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "09y8L+aBZCBIeWAAfPClML"}, {"__type__": "cc.Node", "_name": "lvLab", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 101}, {"__id__": 103}, {"__id__": 105}], "_prefab": {"__id__": 107}, "_lpos": {"__type__": "cc.Vec3", "x": 40, "y": 2.432, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 102}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3emPYosz5M9boG+BcO8MUe"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 104}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4/vvg3C5KeJGpnic8c20i"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 106}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3gFF4bldGE5NEjSbPLH+V"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dMmYrJAJLQaGJWc/cqjHo"}, {"__type__": "cc.Node", "_name": "timeLab", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 109}, {"__id__": 111}, {"__id__": 113}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 270, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 110}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ee5GbF3AFFnKCeR6RTDmFQ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 112}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20GGstijJEEJ+8arck3nfv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 114}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9Qhy0q5dMZJ10J/VqV5bR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74VToK3lJE/rpA+FpPsOad"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 117}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6b83118b-7b1b-46a6-9ccf-08a1a2ed0f68@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4c7aBBqBJBS78y62Wq0vOk"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 119}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 86, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86CFcjPDJB6Zy5qcYafY6E"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 121}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77BP7ZojlMkaawbIXF9GIR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 123}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 634}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2E/Cg4WhC9qyRiXocANG6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31k/jeeS1KdLbPqpnyVqM9"}, {"__type__": "cc.Node", "_name": "armyNode", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 126}, {"__id__": 128}, {"__id__": 130}, {"__id__": 132}, {"__id__": 134}], "_prefab": {"__id__": 136}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -290, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "__prefab": {"__id__": 127}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d0741e0a-e2b2-43ee-852c-7c86613877f5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ad894hjARDrLmIyIRfGZii"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "__prefab": {"__id__": 129}, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 20, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3dqD1E6RA6Z1sdk87p5gs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "__prefab": {"__id__": 131}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ae12hsIOxFOLZPKfgltO+U"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "__prefab": {"__id__": 133}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eb5hYhoRdIOJ3dg8zW1AHS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "__prefab": {"__id__": 135}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbQMRG7AlNlq6AeuU40B/e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfQAQcOsFLFagbbe1LT9ns"}, {"__type__": "f99ceIczuxM3rD2pb+w2bn1", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 138}, "mask": {"__id__": 2}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3f5RyPUedCCKOgrqX2v1n0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 140}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55J7wMzX1CFo2POVRm7ddQ"}, {"__type__": "79f56J5eZdKV4BkXv7s95Os", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 142}, "armyLayer": {"__id__": 125}, "nameLab": {"__id__": 93}, "lvLab": {"__id__": 101}, "timeLab": {"__id__": 109}, "upBtn": {"__id__": 51}, "destroyBtn": {"__id__": 76}, "armyItem": {"__uuid__": "4b28d971-51df-429a-ac52-8cf16ef02d58", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1JDRzeUdADp8Adyj3TSNV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 144}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41Ic0Zyf9N54apBeLeUAjn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 146}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5aiYcTM5IL5GY6B9E5w9B"}, {"__type__": "33c14fX9dBEbo67kq4aZweX", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 148}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baj1+mbQpPCLt6pwMSh8C6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3bbF4CBPtM+7ccMfus7gJ6"}]