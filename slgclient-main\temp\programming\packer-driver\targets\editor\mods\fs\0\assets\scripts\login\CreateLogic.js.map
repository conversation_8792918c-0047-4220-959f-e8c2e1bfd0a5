{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts"], "names": ["_decorator", "Component", "EditBox", "Toggle", "ServerConfig", "LoginCommand", "EventMgr", "createName", "AudioManager", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "on", "role_create", "create", "editName", "string", "getRandomName", "onClickCreate", "instance", "playClick", "sex", "manToggle", "isChecked", "loginData", "getInstance", "proxy", "getLoginData", "uid", "serverId", "onClickToggle", "data", "console", "log", "code", "node", "active", "onRandomName", "name", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;;AAGhCC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;yBASTW,W,WADpBF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAACR,OAAD,C,UAIRQ,QAAQ,CAACP,MAAD,C,oCAPb,MACqBQ,WADrB,SACyCV,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAUrCW,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,WAAzB,EAAsC,KAAKC,MAA3C,EAAmD,IAAnD;AACA,eAAKC,QAAL,CAAcC,MAAd,GAAuB,KAAKC,aAAL,EAAvB;AACH;;AAESC,QAAAA,aAAa,GAAG;AACtB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA,cAAIC,GAAG,GAAG,KAAKC,SAAL,CAAeC,SAAf,GAAyB,CAAzB,GAA2B,CAArC;AACA,cAAIC,SAAc,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,YAAjC,EAArB;AACA;AAAA;AAAA,4CAAaF,WAAb,GAA2BZ,WAA3B,CAAuCW,SAAS,CAACI,GAAjD,EAAsD,KAAKb,QAAL,CAAcC,MAApE,EAA4EK,GAA5E,EAAgF;AAAA;AAAA,4CAAaI,WAAb,GAA2BC,KAA3B,CAAiCG,QAAjH,EAA2H,CAA3H;AACH;;AAESC,QAAAA,aAAa,GAAI;AACvB;AAAA;AAAA,4CAAaX,QAAb,CAAsBC,SAAtB;AACH;;AAGSN,QAAAA,MAAM,CAACiB,IAAD,EAAW;AACvBC,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuBF,IAAvB;;AACA,cAAGA,IAAI,CAACG,IAAL,IAAa,CAAhB,EAAkB;AACd,iBAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;AACJ;;AAESC,QAAAA,YAAY,GAAO;AACzB;AAAA;AAAA,4CAAalB,QAAb,CAAsBC,SAAtB;AACA,eAAKL,QAAL,CAAcC,MAAd,GAAuB,KAAKC,aAAL,EAAvB;AACH;;AAIQA,QAAAA,aAAa,GAAS;AAC3B,cAAII,GAAG,GAAG,KAAKC,SAAL,CAAeC,SAAf,GAA2B,KAA3B,GAAmC,MAA7C;AACA,cAAIe,IAAI,GAAG;AAAA;AAAA,wCAAWjB,GAAX,CAAX;AACA,iBAAOiB,IAAP;AACH;;AAISC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAnD8C,O;;;;;iBAG3B,I;;;;;;;iBAIA,I", "sourcesContent": ["import { _decorator, Component, EditBox, Toggle } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { ServerConfig } from \"../config/ServerConfig\";\nimport LoginCommand from \"./LoginCommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { createName } from '../libs/NameDict';\nimport { AudioManager } from '../common/AudioManager';\n\n@ccclass('CreateLogic')\nexport default class CreateLogic extends Component {\n\n    @property(EditBox)\n    editName: EditBox = null;\n\n\n    @property(Toggle)\n    manToggle: Toggle = null;\n    \n\n    protected onLoad():void{\n        EventMgr.on(ServerConfig.role_create, this.create, this);\n        this.editName.string = this.getRandomName();\n    }\n\n    protected onClickCreate() {\n        AudioManager.instance.playClick();\n        var sex = this.manToggle.isChecked?0:1;\n        var loginData: any = LoginCommand.getInstance().proxy.getLoginData();\n        LoginCommand.getInstance().role_create(loginData.uid, this.editName.string, sex,LoginCommand.getInstance().proxy.serverId, 0)\n    }\n\n    protected onClickToggle () {\n        AudioManager.instance.playClick();\n    }\n\n\n    protected create(data):void{\n        console.log(\"create:\", data);\n        if(data.code == 0){\n            this.node.active = false;\n        }\n    }\n\n    protected onRandomName():void{\n        AudioManager.instance.playClick();\n        this.editName.string = this.getRandomName();\n    }\n\n\n\n   protected getRandomName():string{\n        var sex = this.manToggle.isChecked ? \"boy\" : \"girl\";\n        let name = createName(sex);\n        return name\n    }\n\n\n\n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n}\n"]}