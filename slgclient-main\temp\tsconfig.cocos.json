{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2015", "module": "ES2015", "strict": true, "types": ["./temp/declarations/cc.env", "./temp/declarations/cc.custom-macro", "./temp/declarations/jsb", "./temp/declarations/cc"], "paths": {"db://internal/*": ["D:\\Software\\Cocos3.4.0\\Creator\\3.4.0\\resources\\resources\\3d\\engine\\editor\\assets\\*"], "db://assets/*": ["D:\\CocosSLG\\slgclient-main\\assets\\*"]}, "experimentalDecorators": true, "isolatedModules": true, "moduleResolution": "node", "noEmit": true, "forceConsistentCasingInFileNames": true}}