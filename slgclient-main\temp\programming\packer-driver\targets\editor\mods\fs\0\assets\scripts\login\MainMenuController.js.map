{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts"], "names": ["_decorator", "Component", "Node", "<PERSON><PERSON>", "Prefab", "instantiate", "AudioManager", "ccclass", "property", "MainMenuController", "onLoad", "console", "log", "setupEventListeners", "start", "loginButton", "node", "on", "EventType", "CLICK", "onLoginButtonClick", "exitButton", "onExitButtonClick", "instance", "playClick", "showLoginDialog", "showExitDialog", "loginDialogPrefab", "error", "closeCurrentDialog", "currentDialog", "setParent", "dialogContainer", "setPosition", "exitDialogPrefab", "destroy", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAC7CC,MAAAA,Y,iBAAAA,Y;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAE9B;AACA;AACA;AACA;;oCAEaS,kB,WADZF,OAAO,CAAC,oBAAD,C,UAGHC,QAAQ,CAACL,MAAD,C,UAGRK,QAAQ,CAACL,MAAD,C,UAGRK,QAAQ,CAACJ,MAAD,C,UAGRI,QAAQ,CAACJ,MAAD,C,UAGRI,QAAQ,CAACN,IAAD,C,oCAfb,MACaO,kBADb,SACwCR,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,iDAiBhB,IAjBgB;AAAA;;AAmB9CS,QAAAA,MAAM,GAAG;AACLC,UAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACA,eAAKC,mBAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJH,UAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,mBAAmB,GAAS;AAChC,cAAI,KAAKE,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBC,IAAjB,CAAsBC,EAAtB,CAAyBd,MAAM,CAACe,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,kBAAtD,EAA0E,IAA1E;AACAT,YAAAA,OAAO,CAACC,GAAR,CAAY,mCAAZ;AACH;;AAED,cAAI,KAAKS,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBL,IAAhB,CAAqBC,EAArB,CAAwBd,MAAM,CAACe,SAAP,CAAiBC,KAAzC,EAAgD,KAAKG,iBAArD,EAAwE,IAAxE;AACAX,YAAAA,OAAO,CAACC,GAAR,CAAY,mCAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYQ,QAAAA,kBAAkB,GAAS;AAC/BT,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ,EAD+B,CAG/B;;AACA;AAAA;AAAA,4CAAaW,QAAb,CAAsBC,SAAtB,GAJ+B,CAM/B;;AACA,eAAKC,eAAL;AACH;AAED;AACJ;AACA;;;AACYH,QAAAA,iBAAiB,GAAS;AAC9BX,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ,EAD8B,CAG9B;;AACA;AAAA;AAAA,4CAAaW,QAAb,CAAsBC,SAAtB,GAJ8B,CAM9B;;AACA,eAAKE,cAAL;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAKE,iBAAV,EAA6B;AACzBhB,YAAAA,OAAO,CAACiB,KAAR,CAAc,kCAAd;AACA;AACH,WAJ2B,CAM5B;;;AACA,eAAKC,kBAAL,GAP4B,CAS5B;;AACA,eAAKC,aAAL,GAAqBzB,WAAW,CAAC,KAAKsB,iBAAN,CAAhC;AACA,eAAKG,aAAL,CAAmBC,SAAnB,CAA6B,KAAKC,eAAlC;AACA,eAAKF,aAAL,CAAmBG,WAAnB,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC;AAEAtB,UAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACH;AAED;AACJ;AACA;;;AACYc,QAAAA,cAAc,GAAS;AAC3B,cAAI,CAAC,KAAKQ,gBAAV,EAA4B;AACxBvB,YAAAA,OAAO,CAACiB,KAAR,CAAc,oCAAd;AACA;AACH,WAJ0B,CAM3B;;;AACA,eAAKC,kBAAL,GAP2B,CAS3B;;AACA,eAAKC,aAAL,GAAqBzB,WAAW,CAAC,KAAK6B,gBAAN,CAAhC;AACA,eAAKJ,aAAL,CAAmBC,SAAnB,CAA6B,KAAKC,eAAlC;AACA,eAAKF,aAAL,CAAmBG,WAAnB,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC;AAEAtB,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ;AACH;AAED;AACJ;AACA;;;AACWiB,QAAAA,kBAAkB,GAAS;AAC9B,cAAI,KAAKC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBK,OAAnB;AACA,iBAAKL,aAAL,GAAqB,IAArB;AACAnB,YAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACH;AACJ;;AAEDwB,QAAAA,SAAS,GAAG;AACR;AACA,cAAI,KAAKrB,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBC,IAAjB,CAAsBqB,GAAtB,CAA0BlC,MAAM,CAACe,SAAP,CAAiBC,KAA3C,EAAkD,KAAKC,kBAAvD,EAA2E,IAA3E;AACH;;AACD,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBL,IAAhB,CAAqBqB,GAArB,CAAyBlC,MAAM,CAACe,SAAP,CAAiBC,KAA1C,EAAiD,KAAKG,iBAAtD,EAAyE,IAAzE;AACH;;AACDX,UAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ;AACH;;AAjI6C,O;;;;;iBAGxB,I;;;;;;;iBAGD,I;;;;;;;iBAGO,I;;;;;;;iBAGD,I;;;;;;;iBAGH,I", "sourcesContent": ["import { _decorator, Component, Node, Button, Prefab, instantiate } from 'cc';\nimport { AudioManager } from '../common/AudioManager';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 主菜单控制器\n * 控制主菜单的两个按钮：登录游戏、离开游戏\n */\n@ccclass('MainMenuController')\nexport class MainMenuController extends Component {\n    \n    @property(Button)\n    loginButton: Button = null!;\n    \n    @property(Button)\n    exitButton: Button = null!;\n    \n    @property(Prefab)\n    loginDialogPrefab: Prefab = null!;\n    \n    @property(Prefab)\n    exitDialogPrefab: Prefab = null!;\n    \n    @property(Node)\n    dialogContainer: Node = null!;\n    \n    private currentDialog: Node = null;\n\n    onLoad() {\n        console.log('[MainMenuController] 主菜单控制器加载');\n        this.setupEventListeners();\n    }\n\n    start() {\n        console.log('[MainMenuController] 主菜单控制器启动');\n    }\n\n    /**\n     * 设置事件监听器\n     */\n    private setupEventListeners(): void {\n        if (this.loginButton) {\n            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);\n            console.log('[MainMenuController] 登录按钮事件监听器已设置');\n        }\n\n        if (this.exitButton) {\n            this.exitButton.node.on(Button.EventType.CLICK, this.onExitButtonClick, this);\n            console.log('[MainMenuController] 离开按钮事件监听器已设置');\n        }\n    }\n\n    /**\n     * 登录游戏按钮点击事件\n     */\n    private onLoginButtonClick(): void {\n        console.log('[MainMenuController] 登录游戏按钮被点击');\n        \n        // 播放点击音效\n        AudioManager.instance.playClick();\n        \n        // 显示登录对话框\n        this.showLoginDialog();\n    }\n\n    /**\n     * 离开游戏按钮点击事件\n     */\n    private onExitButtonClick(): void {\n        console.log('[MainMenuController] 离开游戏按钮被点击');\n        \n        // 播放点击音效\n        AudioManager.instance.playClick();\n        \n        // 显示离开游戏对话框\n        this.showExitDialog();\n    }\n\n    /**\n     * 显示登录对话框\n     */\n    private showLoginDialog(): void {\n        if (!this.loginDialogPrefab) {\n            console.error('[MainMenuController] 登录对话框预制体未设置');\n            return;\n        }\n\n        // 关闭当前对话框\n        this.closeCurrentDialog();\n\n        // 创建登录对话框\n        this.currentDialog = instantiate(this.loginDialogPrefab);\n        this.currentDialog.setParent(this.dialogContainer);\n        this.currentDialog.setPosition(0, 0, 0);\n\n        console.log('[MainMenuController] 登录对话框已显示');\n    }\n\n    /**\n     * 显示离开游戏对话框\n     */\n    private showExitDialog(): void {\n        if (!this.exitDialogPrefab) {\n            console.error('[MainMenuController] 离开游戏对话框预制体未设置');\n            return;\n        }\n\n        // 关闭当前对话框\n        this.closeCurrentDialog();\n\n        // 创建离开游戏对话框\n        this.currentDialog = instantiate(this.exitDialogPrefab);\n        this.currentDialog.setParent(this.dialogContainer);\n        this.currentDialog.setPosition(0, 0, 0);\n\n        console.log('[MainMenuController] 离开游戏对话框已显示');\n    }\n\n    /**\n     * 关闭当前对话框\n     */\n    public closeCurrentDialog(): void {\n        if (this.currentDialog) {\n            this.currentDialog.destroy();\n            this.currentDialog = null;\n            console.log('[MainMenuController] 当前对话框已关闭');\n        }\n    }\n\n    onDestroy() {\n        // 清理事件监听器\n        if (this.loginButton) {\n            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);\n        }\n        if (this.exitButton) {\n            this.exitButton.node.off(Button.EventType.CLICK, this.onExitButtonClick, this);\n        }\n        console.log('[MainMenuController] 主菜单控制器销毁');\n    }\n}\n"]}