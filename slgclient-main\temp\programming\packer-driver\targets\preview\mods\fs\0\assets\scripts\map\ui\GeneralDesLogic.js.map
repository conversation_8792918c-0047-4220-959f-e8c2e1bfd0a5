{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts"], "names": ["_decorator", "Component", "Label", "Layout", "Prefab", "Node", "instantiate", "GeneralCommand", "GeneralData", "SkillCommand", "GeneralItemLogic", "SkillIconLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "GeneralDesLogic", "onEnable", "on", "updateGeneral", "onDisable", "targetOff", "onLoad", "_nameObj", "force", "strategy", "defense", "speed", "destroy", "_generalNode", "generalItemPrefab", "parent", "generalItemParent", "data", "getInstance", "proxy", "getMyGeneral", "_currData", "id", "setData", "_cfgData", "cfgData", "curData", "nextCfg", "getGeneralLevelCfg", "level", "levelExp", "exp", "maxLevel", "getMaxLevel", "lvLabel", "string", "expLabel", "nameLab", "name", "_addPrObj", "force_added", "strategy_added", "defense_added", "speed_added", "destroy_added", "<PERSON><PERSON><PERSON><PERSON>", "getAttrStr", "strategyLabel", "defense<PERSON><PERSON><PERSON>", "speedLabel", "destroyLabel", "com", "getComponent", "updateItem", "powerLabel", "physical_power", "physical_power_limit", "costLabel", "cost", "index", "skills", "length", "gSkill", "icon", "skillIcons", "iconNameLab", "skillNameLab", "skillConf", "getSkillCfg", "cfgId", "skill", "getSkill", "key", "str", "getPrStr", "onClickSkill", "event", "pos", "instance", "playClick", "console", "log", "node", "target", "isEmpty", "emit", "openSkill", "openSkillInfo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAkBC,MAAAA,W,OAAAA,W;;AAGlEC,MAAAA,c;;AACCC,MAAAA,W,iBAAAA,W;;AACDC,MAAAA,Y;;AACAC,MAAAA,gB;;AACAC,MAAAA,c;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OATH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;yBAYTiB,e,WADpBF,OAAO,CAAC,iBAAD,C,UAGHC,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACb,MAAD,C,UAGRa,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACd,KAAD,C,WAGRc,QAAQ,CAACd,KAAD,C,WAGRc,QAAQ,CAACd,KAAD,C,WAGRc,QAAQ,CAACd,KAAD,C,WAIRc,QAAQ,CAACZ,MAAD,C,WAGRY,QAAQ,CAACX,IAAD,C,WAGRW,QAAQ,CAAC,CAACX,IAAD,CAAD,C,WAGRW,QAAQ,CAAC,CAACd,KAAD,CAAD,C,oCA9Cb,MACqBe,eADrB,SAC6ChB,SAD7C,CACuD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,6CAgDnB,IAhDmB;;AAAA,4CAiD5B,IAjD4B;;AAAA,4CAoD5B,EApD4B;;AAAA,6CAqD3B,EArD2B;;AAAA,gDAsDvB,IAtDuB;AAAA;;AAwDzCiB,QAAAA,QAAQ,GAAE;AAChB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,aAAvB,EAAsC,KAAKA,aAA3C,EAA0D,IAA1D;AACH;;AAESC,QAAAA,SAAS,GAAE;AACjB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,MAAM,GAAO;AAEnB,eAAKC,QAAL,GAAgB;AACZC,YAAAA,KAAK,EAAC,IADM;AAEZC,YAAAA,QAAQ,EAAC,IAFG;AAGZC,YAAAA,OAAO,EAAC,IAHI;AAIZC,YAAAA,KAAK,EAAC,IAJM;AAKZC,YAAAA,OAAO,EAAC;AALI,WAAhB;AAQA,eAAKC,YAAL,GAAoBxB,WAAW,CAAC,KAAKyB,iBAAN,CAA/B;AACA,eAAKD,YAAL,CAAkBE,MAAlB,GAA2B,KAAKC,iBAAhC;AACH;;AAESb,QAAAA,aAAa,GAAE;AACrB,cAAIc,IAAI,GAAG;AAAA;AAAA,gDAAeC,WAAf,GAA6BC,KAA7B,CAAmCC,YAAnC,CAAgD,KAAKC,SAAL,CAAeC,EAA/D,CAAX;;AACA,cAAGL,IAAH,EAAQ;AACJ,iBAAKM,OAAL,CAAa,KAAKC,QAAlB,EAA4BP,IAA5B;AACH;AACJ;;AAEMM,QAAAA,OAAO,CAACE,OAAD,EAAcC,OAAd,EAAuC;AACjD,eAAKL,SAAL,GAAiBK,OAAjB;AACA,eAAKF,QAAL,GAAgBC,OAAhB;AAEA,cAAIE,OAAO,GAAG;AAAA;AAAA,gDAAeT,WAAf,GAA6BC,KAA7B,CAAmCS,kBAAnC,CAAsD,KAAKP,SAAL,CAAeQ,KAAf,GAAuB,CAA7E,CAAd;AACA,cAAIC,QAAQ,GAAGH,OAAO,GAACA,OAAO,CAACI,GAAT,GAAa,KAAnC;AACA,cAAIC,QAAgB,GAAG;AAAA;AAAA,gDAAed,WAAf,GAA6BC,KAA7B,CAAmCc,WAAnC,EAAvB;AACA,eAAKC,OAAL,CAAaC,MAAb,GAAsB,QAAQ,KAAKd,SAAL,CAAeQ,KAAvB,GAA+B,GAA/B,GAAqCG,QAA3D;AACA,eAAKI,QAAL,CAAcD,MAAd,GAAuB,QAAQT,OAAO,CAACK,GAAhB,GAAqB,GAArB,GAA2BD,QAAlD;AAEA,eAAKO,OAAL,CAAaF,MAAb,GAAsB,KAAKX,QAAL,CAAcc,IAApC;AAEA,eAAKC,SAAL,GAAiB;AACb/B,YAAAA,KAAK,EAAC,KAAKa,SAAL,CAAemB,WADR;AAEb/B,YAAAA,QAAQ,EAAC,KAAKY,SAAL,CAAeoB,cAFX;AAGb/B,YAAAA,OAAO,EAAC,KAAKW,SAAL,CAAeqB,aAHV;AAIb/B,YAAAA,KAAK,EAAC,KAAKU,SAAL,CAAesB,WAJR;AAKb/B,YAAAA,OAAO,EAAC,KAAKS,SAAL,CAAeuB;AALV,WAAjB;AASA,eAAKC,SAAL,CAAeV,MAAf,GAAwB,KAAKW,UAAL,CAAgB,OAAhB,CAAxB;AACA,eAAKC,aAAL,CAAmBZ,MAAnB,GAA4B,KAAKW,UAAL,CAAgB,UAAhB,CAA5B;AACA,eAAKE,YAAL,CAAkBb,MAAlB,GAA2B,KAAKW,UAAL,CAAgB,SAAhB,CAA3B;AACA,eAAKG,UAAL,CAAgBd,MAAhB,GAAyB,KAAKW,UAAL,CAAgB,OAAhB,CAAzB;AACA,eAAKI,YAAL,CAAkBf,MAAlB,GAA2B,KAAKW,UAAL,CAAgB,SAAhB,CAA3B;;AAEA,cAAIK,GAAG,GAAG,KAAKtC,YAAL,CAAkBuC,YAAlB;AAAA;AAAA,mDAAV;;AACA,cAAGD,GAAH,EAAO;AACHA,YAAAA,GAAG,CAACE,UAAJ,CAAe,KAAKhC,SAApB;AACH;;AAED,eAAKiC,UAAL,CAAgBnB,MAAhB,GAAyB,QAAQT,OAAO,CAAC6B,cAAhB,GAAiC,GAAjC,GAAuC9B,OAAO,CAAC+B,oBAAxE;AACA,eAAKC,SAAL,CAAetB,MAAf,GAAwB,UAAQV,OAAO,CAACiC,IAAxC;;AAEA,eAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGjC,OAAO,CAACkC,MAAR,CAAeC,MAA3C,EAAmDF,KAAK,EAAxD,EAA4D;AACxD,gBAAIG,MAAM,GAAGpC,OAAO,CAACkC,MAAR,CAAeD,KAAf,CAAb;AACA,gBAAII,IAAI,GAAG,KAAKC,UAAL,CAAgBL,KAAhB,CAAX;AACA,gBAAIM,WAAW,GAAG,KAAKC,YAAL,CAAkBP,KAAlB,CAAlB;;AAEA,gBAAGG,MAAM,IAAI,IAAb,EAAkB;AACdC,cAAAA,IAAI,CAACX,YAAL;AAAA;AAAA,oDAAkC7B,OAAlC,CAA0C,IAA1C,EAAgD,IAAhD;AACA0C,cAAAA,WAAW,CAAC9B,MAAZ,GAAqB,EAArB;AACH,aAHD,MAGK;AAED,kBAAIgC,SAAS,GAAG;AAAA;AAAA,gDAAajD,WAAb,GAA2BC,KAA3B,CAAiCiD,WAAjC,CAA6CN,MAAM,CAACO,KAApD,CAAhB;AACA,kBAAIC,KAAK,GAAG;AAAA;AAAA,gDAAapD,WAAb,GAA2BC,KAA3B,CAAiCoD,QAAjC,CAA0CT,MAAM,CAACO,KAAjD,CAAZ;;AACA,kBAAGF,SAAS,IAAIG,KAAhB,EAAsB;AAClBP,gBAAAA,IAAI,CAACX,YAAL;AAAA;AAAA,sDAAkC7B,OAAlC,CAA0C+C,KAA1C,EAAiDR,MAAjD;AACAG,gBAAAA,WAAW,CAAC9B,MAAZ,GAAqBgC,SAAS,CAAC7B,IAA/B;AACH,eAHD,MAGK;AACDyB,gBAAAA,IAAI,CAACX,YAAL;AAAA;AAAA,sDAAkC7B,OAAlC,CAA0C,IAA1C,EAAgD,IAAhD;AACA0C,gBAAAA,WAAW,CAAC9B,MAAZ,GAAqB,EAArB;AACH;AACJ;AACJ;AACJ;;AAEOW,QAAAA,UAAU,CAAC0B,GAAD,EAAqB;AACnC,cAAIC,GAAG,GAAG;AAAA;AAAA,0CAAYC,QAAZ,CAAqB,KAAKlD,QAAL,CAAcgD,GAAd,CAArB,EAAyC,KAAKjC,SAAL,CAAeiC,GAAf,CAAzC,EAA8D,KAAKnD,SAAL,CAAeQ,KAA7E,EAAoF,KAAKL,QAAL,CAAcgD,GAAG,GAAG,OAApB,CAApF,CAAV;AACA,iBAAO,KAAKjE,QAAL,CAAciE,GAAd,IAAqB,GAArB,GAA2BC,GAAlC;AACH;;AAESE,QAAAA,YAAY,CAACC,KAAD,EAAoBC,GAApB,EAAwB;AAC1C;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBL,KAArB,EAA4BC,GAA5B;AACA,cAAIK,IAAU,GAAGN,KAAK,CAACO,MAAvB;AACA,cAAIC,OAAO,GAAGF,IAAI,CAAC9B,YAAL;AAAA;AAAA,gDAAkCgC,OAAlC,EAAd;;AACA,cAAGA,OAAH,EAAW;AACP;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,0CAAWC,SAAzB,EAAoC,CAApC,EAAuC,KAAKjE,SAA5C,EAAuDwD,GAAvD;AACH,WAFD,MAEK;AACD,gBAAIP,KAAK,GAAGY,IAAI,CAAC9B,YAAL;AAAA;AAAA,kDAAkCmB,QAAlC,EAAZ;AACA;AAAA;AAAA,sCAASc,IAAT,CAAc;AAAA;AAAA,0CAAWE,aAAzB,EAAwCjB,KAAxC,EAA+C,CAA/C,EAAkD,KAAKjD,SAAvD,EAAkEwD,GAAlE;AACH;AACJ;;AA/JkD,O;;;;;iBAGlC,I;;;;;;;iBAGI,I;;;;;;;iBAGJ,I;;;;;;;iBAGE,I;;;;;;;iBAGG,I;;;;;;;iBAGF,I;;;;;;;iBAGG,I;;;;;;;iBAGD,I;;;;;;;iBAGJ,I;;;;;;;iBAGE,I;;;;;;;iBAGD,I;;;;;;;iBAIS,I;;;;;;;iBAGF,I;;;;;;;iBAGL,E;;;;;;;iBAGG,E", "sourcesContent": ["import { _decorator, Component, Label, Layout, Prefab, Node, EventTouch, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport {GeneralData } from \"../../general/GeneralProxy\";\nimport SkillCommand from \"../../skill/SkillCommand\";\nimport GeneralItemLogic, { GeneralItemType } from \"./GeneralItemLogic\";\nimport SkillIconLogic from \"./SkillIconLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('GeneralDesLogic')\nexport default class GeneralDesLogic extends Component {\n  \n    @property(Label)\n    nameLab: Label = null;\n\n    @property(Layout)\n    srollLayout:Layout = null;\n\n    @property(Label)\n    lvLabel: Label = null;\n    \n    @property(Label)\n    foreLabel: Label = null;\n\n    @property(Label)\n    defenseLabel: Label = null;\n\n    @property(Label)\n    speedLabel: Label = null;\n\n    @property(Label)\n    strategyLabel: Label = null;\n\n    @property(Label)\n    destroyLabel: Label = null;\n\n    @property(Label)\n    expLabel: Label = null;\n\n    @property(Label)\n    powerLabel: Label = null;\n\n    @property(Label)\n    costLabel: Label = null;\n\n\n    @property(Prefab)\n    generalItemPrefab: Prefab = null;\n\n    @property(Node)\n    generalItemParent: Node = null;\n\n    @property([Node])\n    skillIcons: Node[] = [];\n\n    @property([Label])\n    skillNameLab: Label[] = [];\n\n    private _currData:GeneralData = null;\n    private _cfgData:any = null;\n\n\n    private _nameObj:any = {};\n    private _addPrObj:any = {};\n    private _generalNode:Node = null;\n\n    protected onEnable(){\n        EventMgr.on(LogicEvent.updateGeneral, this.updateGeneral, this)\n    }\n\n    protected onDisable(){\n        EventMgr.targetOff(this);\n    }\n\n    protected onLoad():void{\n\n        this._nameObj = {\n            force:\"武力\",\n            strategy:\"战略\",\n            defense:\"防御\",\n            speed:\"速度\",\n            destroy:\"破坏\",\n        };\n       \n        this._generalNode = instantiate(this.generalItemPrefab);\n        this._generalNode.parent = this.generalItemParent;\n    }\n\n    protected updateGeneral(){\n        var data = GeneralCommand.getInstance().proxy.getMyGeneral(this._currData.id);\n        if(data){\n            this.setData(this._cfgData, data);\n        }\n    }\n\n    public setData(cfgData:any, curData:GeneralData):void{\n        this._currData = curData;\n        this._cfgData = cfgData;\n    \n        var nextCfg = GeneralCommand.getInstance().proxy.getGeneralLevelCfg(this._currData.level + 1);\n        var levelExp = nextCfg?nextCfg.exp:\"MAX\";\n        var maxLevel: number = GeneralCommand.getInstance().proxy.getMaxLevel();\n        this.lvLabel.string = '等级:' + this._currData.level + \"/\" + maxLevel;\n        this.expLabel.string = \"经验:\" + curData.exp +\"/\" + levelExp;\n        \n        this.nameLab.string = this._cfgData.name;\n\n        this._addPrObj = {\n            force:this._currData.force_added,\n            strategy:this._currData.strategy_added,\n            defense:this._currData.defense_added,\n            speed:this._currData.speed_added,\n            destroy:this._currData.destroy_added,\n        };\n\n       \n        this.foreLabel.string = this.getAttrStr(\"force\");\n        this.strategyLabel.string = this.getAttrStr(\"strategy\");\n        this.defenseLabel.string = this.getAttrStr(\"defense\");\n        this.speedLabel.string = this.getAttrStr(\"speed\");\n        this.destroyLabel.string = this.getAttrStr(\"destroy\");\n     \n        var com = this._generalNode.getComponent(GeneralItemLogic);\n        if(com){\n            com.updateItem(this._currData);\n        }\n\n        this.powerLabel.string = \"体力:\" + curData.physical_power + \"/\" + cfgData.physical_power_limit;\n        this.costLabel.string = \"cost:\"+cfgData.cost;\n\n        for (let index = 0; index < curData.skills.length; index++) {\n            let gSkill = curData.skills[index];\n            let icon = this.skillIcons[index];\n            let iconNameLab = this.skillNameLab[index];\n\n            if(gSkill == null){\n                icon.getComponent(SkillIconLogic).setData(null, null);\n                iconNameLab.string = \"\";\n            }else{\n                \n                let skillConf = SkillCommand.getInstance().proxy.getSkillCfg(gSkill.cfgId);\n                let skill = SkillCommand.getInstance().proxy.getSkill(gSkill.cfgId);\n                if(skillConf && skill){\n                    icon.getComponent(SkillIconLogic).setData(skill, gSkill);\n                    iconNameLab.string = skillConf.name;\n                }else{\n                    icon.getComponent(SkillIconLogic).setData(null, null);\n                    iconNameLab.string = \"\";\n                }\n            }\n        }\n    }\n\n    private getAttrStr(key: string) :string{\n        var str = GeneralData.getPrStr(this._cfgData[key], this._addPrObj[key], this._currData.level, this._cfgData[key + \"_grow\"])\n        return this._nameObj[key] + \":\" + str;\n    }\n\n    protected onClickSkill(event: EventTouch, pos){\n        AudioManager.instance.playClick();\n        console.log(\"event\", event, pos);\n        var node: Node = event.target;\n        var isEmpty = node.getComponent(SkillIconLogic).isEmpty();\n        if(isEmpty){\n            EventMgr.emit(LogicEvent.openSkill, 1, this._currData, pos);\n        }else{\n            let skill = node.getComponent(SkillIconLogic).getSkill();\n            EventMgr.emit(LogicEvent.openSkillInfo, skill, 2, this._currData, pos);\n        }\n    }\n\n}\n"]}