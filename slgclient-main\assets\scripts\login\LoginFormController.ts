import { _decorator, Component, Node, EditBox, Button, Label, Color } from 'cc';
import LoginCommand from './LoginCommand';
import { EventMgr } from '../utils/EventMgr';
import { LogicEvent } from '../common/LogicEvent';
import { AudioManager } from '../common/AudioManager';

const { ccclass, property } = _decorator;

/**
 * 登录表单控制器
 * 使用预制体创建的登录表单，解决点击偏移问题
 */
@ccclass('LoginFormController')
export class LoginFormController extends Component {
    
    @property(EditBox)
    usernameInput: EditBox = null!;
    
    @property(EditBox)
    passwordInput: EditBox = null!;
    
    @property(Button)
    loginButton: Button = null!;
    
    @property(Label)
    statusLabel: Label = null!;
    
    private isConnecting: boolean = false;

    onLoad() {
        console.log('[LoginFormController] 登录表单控制器加载');
        this.initializeForm();
        this.setupEventListeners();
    }

    start() {
        console.log('[LoginFormController] 登录表单控制器启动');
        // 监听登录完成事件
        EventMgr.on(LogicEvent.loginComplete, this.onLoginComplete, this);
    }

    /**
     * 初始化表单
     */
    private initializeForm(): void {
        // 设置默认状态
        this.updateStatus('请输入用户名和密码', new Color(255, 255, 255, 255));
        
        // 验证组件引用
        if (!this.usernameInput) {
            console.error('[LoginFormController] 用户名输入框未设置');
        }
        if (!this.passwordInput) {
            console.error('[LoginFormController] 密码输入框未设置');
        }
        if (!this.loginButton) {
            console.error('[LoginFormController] 登录按钮未设置');
        }
        if (!this.statusLabel) {
            console.error('[LoginFormController] 状态标签未设置');
        }
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        if (this.loginButton) {
            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
            console.log('[LoginFormController] 登录按钮事件监听器已设置');
        }

        // 输入框回车事件
        if (this.usernameInput) {
            this.usernameInput.node.on('editing-return', this.onUsernameReturn, this);
        }
        if (this.passwordInput) {
            this.passwordInput.node.on('editing-return', this.onPasswordReturn, this);
        }
    }

    /**
     * 用户名输入框回车事件
     */
    private onUsernameReturn(): void {
        console.log('[LoginFormController] 用户名输入框回车，焦点转移到密码框');
        if (this.passwordInput) {
            this.passwordInput.focus();
        }
    }

    /**
     * 密码输入框回车事件
     */
    private onPasswordReturn(): void {
        console.log('[LoginFormController] 密码输入框回车，执行登录');
        this.onLoginButtonClick();
    }

    /**
     * 登录按钮点击事件
     */
    private onLoginButtonClick(): void {
        console.log('[LoginFormController] 登录按钮被点击');

        // 播放点击音效
        AudioManager.instance.playClick();

        if (this.isConnecting) {
            console.log('[LoginFormController] 正在连接中，忽略重复点击');
            return;
        }

        const username = this.usernameInput?.string?.trim() || '';
        const password = this.passwordInput?.string?.trim() || '';

        // 输入验证
        if (!username) {
            this.updateStatus('请输入用户名', new Color(255, 100, 100, 255));
            EventMgr.emit(LogicEvent.showToast, "请输入用户名");
            return;
        }

        if (!password) {
            this.updateStatus('请输入密码', new Color(255, 100, 100, 255));
            EventMgr.emit(LogicEvent.showToast, "请输入密码");
            return;
        }

        if (username.length < 3) {
            this.updateStatus('用户名至少3个字符', new Color(255, 100, 100, 255));
            EventMgr.emit(LogicEvent.showToast, "用户名至少3个字符");
            return;
        }

        if (password.length < 3) {
            this.updateStatus('密码至少3个字符', new Color(255, 100, 100, 255));
            EventMgr.emit(LogicEvent.showToast, "密码至少3个字符");
            return;
        }

        // 执行登录
        this.performLogin(username, password);
    }

    /**
     * 执行登录
     */
    private performLogin(username: string, password: string): void {
        this.isConnecting = true;
        this.updateStatus('正在登录...', new Color(100, 200, 255, 255));
        this.setButtonEnabled(false);

        console.log(`[LoginFormController] 开始登录: ${username}`);

        // 使用现有的登录命令
        LoginCommand.getInstance().accountLogin(username, password);
    }

    /**
     * 登录完成回调
     */
    private onLoginComplete(): void {
        console.log('[LoginFormController] 登录完成');
        this.updateStatus('登录成功！', new Color(100, 255, 100, 255));
        this.isConnecting = false;
        this.setButtonEnabled(true);

        // 隐藏登录表单
        this.node.active = false;
    }

    /**
     * 更新状态显示
     */
    private updateStatus(message: string, color: Color): void {
        if (this.statusLabel) {
            this.statusLabel.string = message;
            this.statusLabel.color = color;
        }
        console.log(`[LoginFormController] 状态更新: ${message}`);
    }

    /**
     * 设置按钮启用状态
     */
    private setButtonEnabled(enabled: boolean): void {
        if (this.loginButton) {
            this.loginButton.interactable = enabled;
            // 视觉反馈
            const color = enabled ? new Color(255, 255, 255, 255) : new Color(128, 128, 128, 255);
            this.loginButton.node.getComponent(Label)?.color.set(color);
        }
    }

    onDestroy() {
        // 清理事件监听器
        EventMgr.targetOff(this);

        if (this.loginButton) {
            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);
        }
        if (this.usernameInput) {
            this.usernameInput.node.off('editing-return', this.onUsernameReturn, this);
        }
        if (this.passwordInput) {
            this.passwordInput.node.off('editing-return', this.onPasswordReturn, this);
        }
        console.log('[LoginFormController] 登录表单控制器销毁');
    }
}
