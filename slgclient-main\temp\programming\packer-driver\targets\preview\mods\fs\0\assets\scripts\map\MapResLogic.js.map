{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts"], "names": ["_decorator", "Vec2", "Vec3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MapBaseLayerLogic", "MapUtil", "ccclass", "property", "MapResLogic", "onLoad", "onDestroy", "setItemData", "item", "data", "resData", "position", "mapCellToPixelPoint", "x", "y", "setPosition", "getComponent", "setResourceData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG1BC,MAAAA,Q;;AACAC,MAAAA,iB;;AAEAC,MAAAA,O;;;;;;;OALD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;yBAQTQ,W,WADpBF,OAAO,CAAC,aAAD,C,gBAAR,MACqBE,WADrB;AAAA;AAAA,kDAC2D;AAE7CC,QAAAA,MAAM,GAAS;AACrB,gBAAMA,MAAN;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,gBAAMA,SAAN;AACH;;AAEMC,QAAAA,WAAW,CAACC,IAAD,EAAaC,IAAb,EAA8B;AAC5C,cAAIC,OAAmB,GAAGD,IAA1B;AACA,cAAIE,QAAc,GAAG;AAAA;AAAA,kCAAQC,mBAAR,CAA4B,IAAIf,IAAJ,CAASa,OAAO,CAACG,CAAjB,EAAoBH,OAAO,CAACI,CAA5B,CAA5B,CAArB;AACAN,UAAAA,IAAI,CAACO,WAAL,CAAiB,IAAIjB,IAAJ,CAASa,QAAQ,CAACE,CAAlB,EAAqBF,QAAQ,CAACG,CAA9B,EAAiC,CAAjC,CAAjB;AACAN,UAAAA,IAAI,CAACQ,YAAL;AAAA;AAAA,oCAA4BC,eAA5B,CAA4CP,OAA5C;AACH;;AAfsD,O", "sourcesContent": ["import { _decorator, Node, Vec2, Vec3 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport ResLogic from \"./entries/ResLogic\";\nimport MapBaseLayerLogic from \"./MapBaseLayerLogic\";\nimport { MapResData } from \"./MapProxy\";\nimport MapUtil from \"./MapUtil\";\n\n@ccclass('MapResLogic')\nexport default class MapResLogic extends MapBaseLayerLogic {\n\n    protected onLoad(): void {\n        super.onLoad();\n    }\n\n    protected onDestroy(): void {\n        super.onDestroy();\n    }\n\n    public setItemData(item: Node, data: any): void {\n        let resData: MapResData = data as MapResData;\n        let position: Vec2 = MapUtil.mapCellToPixelPoint(new Vec2(resData.x, resData.y));\n        item.setPosition(new Vec3(position.x, position.y, 0));\n        item.getComponent(ResLogic).setResourceData(resData);\n    }\n}\n"]}