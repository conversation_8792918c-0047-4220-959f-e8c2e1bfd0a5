{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts"], "names": ["_decorator", "Component", "Node", "<PERSON><PERSON>", "Label", "EditBox", "<PERSON><PERSON>", "UITransform", "Color", "Sprite", "screen", "LoginCommand", "ccclass", "property", "SimpleLoginUI", "onLoad", "console", "log", "createLoginUI", "canvas", "getComponent", "addComponent", "alignCanvasWithScreen", "node", "setPosition", "uiTransform", "windowSize", "setContentSize", "width", "height", "setAnchorPoint", "createBackground", "createTitle", "createUsernameInput", "createPasswordInput", "createLoginButton", "createStatusLabel", "bgNode", "setParent", "bgTransform", "bgSprite", "color", "titleNode", "titleTransform", "titleLabel", "string", "fontSize", "labelNode", "labelTransform", "label", "inputNode", "inputTransform", "usernameInput", "placeholder", "max<PERSON><PERSON><PERSON>", "fontColor", "placeholderFontColor", "inputMode", "InputMode", "SINGLE_LINE", "returnType", "KeyboardReturnType", "DONE", "backgroundImage", "position", "x", "y", "passwordInput", "inputFlag", "InputFlag", "PASSWORD", "buttonNode", "buttonTransform", "buttonSprite", "loginButton", "transition", "Transition", "COLOR", "normalColor", "pressedColor", "hoverColor", "disabledColor", "on", "EventType", "CLICK", "onLoginButtonClick", "statusLabel", "username", "trim", "password", "showStatus", "interactable", "performLogin", "loginCommand", "getInstance", "accountLogin", "scheduleOnce", "error", "message"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;;AAC3FC,MAAAA,Y;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;AAE9B;AACA;AACA;AACA;;+BAEac,a,WADZF,OAAO,CAAC,eAAD,C,yBAAR,MACaE,aADb,SACmCb,SADnC,CAC6C;AAAA;AAAA;;AAAA,iDAER,IAFQ;;AAAA,iDAGR,IAHQ;;AAAA,+CAIX,IAJW;;AAAA,+CAKZ,IALY;AAAA;;AAO/Bc,QAAAA,MAAM,GAAS;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EADqB,CAGrB;;AACA,eAAKC,aAAL;AAEAF,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,aAAa,GAAS;AAC1B;AACA,cAAIC,MAAM,GAAG,KAAKC,YAAL,CAAkBd,MAAlB,CAAb;;AACA,cAAI,CAACa,MAAL,EAAa;AACTA,YAAAA,MAAM,GAAG,KAAKE,YAAL,CAAkBf,MAAlB,CAAT;AACH,WALyB,CAO1B;;;AACAa,UAAAA,MAAM,CAACG,qBAAP,GAA+B,KAA/B;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAT0B,CAW1B;;AACA,gBAAMC,WAAW,GAAG,KAAKL,YAAL,CAAkBb,WAAlB,CAApB;;AACA,cAAIkB,WAAJ,EAAiB;AACb,kBAAMC,UAAU,GAAGhB,MAAM,CAACgB,UAA1B;AACAD,YAAAA,WAAW,CAACE,cAAZ,CAA2BD,UAAU,CAACE,KAAtC,EAA6CF,UAAU,CAACG,MAAxD;AACAJ,YAAAA,WAAW,CAACK,cAAZ,CAA2B,GAA3B,EAAgC,GAAhC;AACH,WAjByB,CAmB1B;;;AACA,eAAKC,gBAAL,GApB0B,CAsB1B;;AACA,eAAKC,WAAL,GAvB0B,CAyB1B;;AACA,eAAKC,mBAAL,GA1B0B,CA4B1B;;AACA,eAAKC,mBAAL,GA7B0B,CA+B1B;;AACA,eAAKC,iBAAL,GAhC0B,CAkC1B;;AACA,eAAKC,iBAAL;AACH;AAED;AACJ;AACA;;;AACYL,QAAAA,gBAAgB,GAAS;AAC7B,gBAAMM,MAAM,GAAG,IAAInC,IAAJ,CAAS,YAAT,CAAf;AACAmC,UAAAA,MAAM,CAACC,SAAP,CAAiB,KAAKf,IAAtB;AAEA,gBAAMgB,WAAW,GAAGF,MAAM,CAAChB,YAAP,CAAoBd,WAApB,CAApB;AACA,gBAAMmB,UAAU,GAAGhB,MAAM,CAACgB,UAA1B;AACAa,UAAAA,WAAW,CAACZ,cAAZ,CAA2BD,UAAU,CAACE,KAAtC,EAA6CF,UAAU,CAACG,MAAxD;AACAU,UAAAA,WAAW,CAACT,cAAZ,CAA2B,GAA3B,EAAgC,GAAhC;AAEA,gBAAMU,QAAQ,GAAGH,MAAM,CAAChB,YAAP,CAAoBZ,MAApB,CAAjB;AACA+B,UAAAA,QAAQ,CAACC,KAAT,GAAiB,IAAIjC,KAAJ,CAAU,EAAV,EAAc,EAAd,EAAkB,EAAlB,EAAsB,GAAtB,CAAjB,CAV6B,CAUgB;;AAE7C6B,UAAAA,MAAM,CAACb,WAAP,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB;AACH;AAED;AACJ;AACA;;;AACYQ,QAAAA,WAAW,GAAS;AACxB,gBAAMU,SAAS,GAAG,IAAIxC,IAAJ,CAAS,OAAT,CAAlB;AACAwC,UAAAA,SAAS,CAACJ,SAAV,CAAoB,KAAKf,IAAzB;AAEA,gBAAMoB,cAAc,GAAGD,SAAS,CAACrB,YAAV,CAAuBd,WAAvB,CAAvB;AACAoC,UAAAA,cAAc,CAAChB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAgB,UAAAA,cAAc,CAACb,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAY,UAAAA,SAAS,CAAClB,WAAV,CAAsB,CAAtB,EAAyB,GAAzB,EAA8B,CAA9B;AAEA,gBAAMoB,UAAU,GAAGF,SAAS,CAACrB,YAAV,CAAuBjB,KAAvB,CAAnB;AACAwC,UAAAA,UAAU,CAACC,MAAX,GAAoB,SAApB;AACAD,UAAAA,UAAU,CAACE,QAAX,GAAsB,EAAtB;AACAF,UAAAA,UAAU,CAACH,KAAX,GAAmB,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAnB;AACH;AAED;AACJ;AACA;;;AACYyB,QAAAA,mBAAmB,GAAS;AAChC;AACA,gBAAMc,SAAS,GAAG,IAAI7C,IAAJ,CAAS,eAAT,CAAlB;AACA6C,UAAAA,SAAS,CAACT,SAAV,CAAoB,KAAKf,IAAzB;AACA,gBAAMyB,cAAc,GAAGD,SAAS,CAAC1B,YAAV,CAAuBd,WAAvB,CAAvB;AACAyC,UAAAA,cAAc,CAACrB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAqB,UAAAA,cAAc,CAAClB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAiB,UAAAA,SAAS,CAACvB,WAAV,CAAsB,CAAtB,EAAyB,EAAzB,EAA6B,CAA7B;AAEA,gBAAMyB,KAAK,GAAGF,SAAS,CAAC1B,YAAV,CAAuBjB,KAAvB,CAAd;AACA6C,UAAAA,KAAK,CAACJ,MAAN,GAAe,MAAf;AACAI,UAAAA,KAAK,CAACH,QAAN,GAAiB,EAAjB;AACAG,UAAAA,KAAK,CAACR,KAAN,GAAc,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAd,CAZgC,CAchC;;AACA,gBAAM0C,SAAS,GAAG,IAAIhD,IAAJ,CAAS,eAAT,CAAlB;AACAgD,UAAAA,SAAS,CAACZ,SAAV,CAAoB,KAAKf,IAAzB,EAhBgC,CAkBhC;;AACA,gBAAM4B,cAAc,GAAGD,SAAS,CAAC7B,YAAV,CAAuBd,WAAvB,CAAvB;AACA4C,UAAAA,cAAc,CAACxB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAwB,UAAAA,cAAc,CAACrB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAoB,UAAAA,SAAS,CAAC1B,WAAV,CAAsB,CAAtB,EAAyB,EAAzB,EAA6B,CAA7B,EAtBgC,CAwBhC;;AACA,gBAAMgB,QAAQ,GAAGU,SAAS,CAAC7B,YAAV,CAAuBZ,MAAvB,CAAjB,CAzBgC,CA0BhC;;AACA+B,UAAAA,QAAQ,CAACC,KAAT,GAAiB,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAjB,CA3BgC,CA6BhC;;AACA,eAAK4C,aAAL,GAAqBF,SAAS,CAAC7B,YAAV,CAAuBhB,OAAvB,CAArB;AACA,eAAK+C,aAAL,CAAmBP,MAAnB,GAA4B,EAA5B;AACA,eAAKO,aAAL,CAAmBC,WAAnB,GAAiC,QAAjC;AACA,eAAKD,aAAL,CAAmBE,SAAnB,GAA+B,EAA/B;AACA,eAAKF,aAAL,CAAmBN,QAAnB,GAA8B,EAA9B;AACA,eAAKM,aAAL,CAAmBG,SAAnB,GAA+B,IAAI/C,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,GAAnB,CAA/B;AACA,eAAK4C,aAAL,CAAmBI,oBAAnB,GAA0C,IAAIhD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA1C;AACA,eAAK4C,aAAL,CAAmBK,SAAnB,GAA+BpD,OAAO,CAACqD,SAAR,CAAkBC,WAAjD;AACA,eAAKP,aAAL,CAAmBQ,UAAnB,GAAgCvD,OAAO,CAACwD,kBAAR,CAA2BC,IAA3D,CAtCgC,CAwChC;;AACA,eAAKV,aAAL,CAAmBW,eAAnB,GAAqCvB,QAArC,CAzCgC,CA2ChC;;AACAxB,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4CiC,SAAS,CAACc,QAAV,CAAmBC,CAA/D,EAAkEf,SAAS,CAACc,QAAV,CAAmBE,CAArF;AACAlD,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0CkC,cAAc,CAACvB,KAAzD,EAAgEuB,cAAc,CAACtB,MAA/E;AACH;AAED;AACJ;AACA;;;AACYK,QAAAA,mBAAmB,GAAS;AAChC;AACA,gBAAMa,SAAS,GAAG,IAAI7C,IAAJ,CAAS,eAAT,CAAlB;AACA6C,UAAAA,SAAS,CAACT,SAAV,CAAoB,KAAKf,IAAzB;AACA,gBAAMyB,cAAc,GAAGD,SAAS,CAAC1B,YAAV,CAAuBd,WAAvB,CAAvB;AACAyC,UAAAA,cAAc,CAACrB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAqB,UAAAA,cAAc,CAAClB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAiB,UAAAA,SAAS,CAACvB,WAAV,CAAsB,CAAtB,EAAyB,CAAC,EAA1B,EAA8B,CAA9B;AAEA,gBAAMyB,KAAK,GAAGF,SAAS,CAAC1B,YAAV,CAAuBjB,KAAvB,CAAd;AACA6C,UAAAA,KAAK,CAACJ,MAAN,GAAe,KAAf;AACAI,UAAAA,KAAK,CAACH,QAAN,GAAiB,EAAjB;AACAG,UAAAA,KAAK,CAACR,KAAN,GAAc,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAd,CAZgC,CAchC;;AACA,gBAAM0C,SAAS,GAAG,IAAIhD,IAAJ,CAAS,eAAT,CAAlB;AACAgD,UAAAA,SAAS,CAACZ,SAAV,CAAoB,KAAKf,IAAzB,EAhBgC,CAkBhC;;AACA,gBAAM4B,cAAc,GAAGD,SAAS,CAAC7B,YAAV,CAAuBd,WAAvB,CAAvB;AACA4C,UAAAA,cAAc,CAACxB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAwB,UAAAA,cAAc,CAACrB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAoB,UAAAA,SAAS,CAAC1B,WAAV,CAAsB,CAAtB,EAAyB,CAAC,EAA1B,EAA8B,CAA9B,EAtBgC,CAwBhC;;AACA,gBAAMgB,QAAQ,GAAGU,SAAS,CAAC7B,YAAV,CAAuBZ,MAAvB,CAAjB,CAzBgC,CA0BhC;;AACA+B,UAAAA,QAAQ,CAACC,KAAT,GAAiB,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAjB,CA3BgC,CA6BhC;;AACA,eAAK2D,aAAL,GAAqBjB,SAAS,CAAC7B,YAAV,CAAuBhB,OAAvB,CAArB;AACA,eAAK8D,aAAL,CAAmBtB,MAAnB,GAA4B,EAA5B;AACA,eAAKsB,aAAL,CAAmBd,WAAnB,GAAiC,OAAjC;AACA,eAAKc,aAAL,CAAmBb,SAAnB,GAA+B,EAA/B;AACA,eAAKa,aAAL,CAAmBrB,QAAnB,GAA8B,EAA9B;AACA,eAAKqB,aAAL,CAAmBZ,SAAnB,GAA+B,IAAI/C,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,GAAnB,CAA/B;AACA,eAAK2D,aAAL,CAAmBX,oBAAnB,GAA0C,IAAIhD,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA1C;AACA,eAAK2D,aAAL,CAAmBC,SAAnB,GAA+B/D,OAAO,CAACgE,SAAR,CAAkBC,QAAjD,CArCgC,CAqC2B;;AAC3D,eAAKH,aAAL,CAAmBV,SAAnB,GAA+BpD,OAAO,CAACqD,SAAR,CAAkBC,WAAjD;AACA,eAAKQ,aAAL,CAAmBP,UAAnB,GAAgCvD,OAAO,CAACwD,kBAAR,CAA2BC,IAA3D,CAvCgC,CAyChC;;AACA,eAAKK,aAAL,CAAmBJ,eAAnB,GAAqCvB,QAArC,CA1CgC,CA4ChC;;AACAxB,UAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4CiC,SAAS,CAACc,QAAV,CAAmBC,CAA/D,EAAkEf,SAAS,CAACc,QAAV,CAAmBE,CAArF;AACAlD,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0CkC,cAAc,CAACvB,KAAzD,EAAgEuB,cAAc,CAACtB,MAA/E;AACH;AAED;AACJ;AACA;;;AACYM,QAAAA,iBAAiB,GAAS;AAC9B,gBAAMoC,UAAU,GAAG,IAAIrE,IAAJ,CAAS,aAAT,CAAnB;AACAqE,UAAAA,UAAU,CAACjC,SAAX,CAAqB,KAAKf,IAA1B;AAEA,gBAAMiD,eAAe,GAAGD,UAAU,CAAClD,YAAX,CAAwBd,WAAxB,CAAxB;AACAiE,UAAAA,eAAe,CAAC7C,cAAhB,CAA+B,GAA/B,EAAoC,EAApC;AACA6C,UAAAA,eAAe,CAAC1C,cAAhB,CAA+B,GAA/B,EAAoC,GAApC;AACAyC,UAAAA,UAAU,CAAC/C,WAAX,CAAuB,CAAvB,EAA0B,CAAC,GAA3B,EAAgC,CAAhC,EAP8B,CAS9B;;AACA,gBAAMiD,YAAY,GAAGF,UAAU,CAAClD,YAAX,CAAwBZ,MAAxB,CAArB;AACAgE,UAAAA,YAAY,CAAChC,KAAb,GAAqB,IAAIjC,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,GAAlB,EAAuB,GAAvB,CAArB,CAX8B,CAa9B;;AACA,eAAKkE,WAAL,GAAmBH,UAAU,CAAClD,YAAX,CAAwBlB,MAAxB,CAAnB;AACA,eAAKuE,WAAL,CAAiBC,UAAjB,GAA8BxE,MAAM,CAACyE,UAAP,CAAkBC,KAAhD;AACA,eAAKH,WAAL,CAAiBI,WAAjB,GAA+B,IAAItE,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,GAAlB,EAAuB,GAAvB,CAA/B;AACA,eAAKkE,WAAL,CAAiBK,YAAjB,GAAgC,IAAIvE,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,GAAlB,EAAuB,GAAvB,CAAhC;AACA,eAAKkE,WAAL,CAAiBM,UAAjB,GAA8B,IAAIxE,KAAJ,CAAU,EAAV,EAAc,GAAd,EAAmB,GAAnB,EAAwB,GAAxB,CAA9B;AACA,eAAKkE,WAAL,CAAiBO,aAAjB,GAAiC,IAAIzE,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAjC,CAnB8B,CAqB9B;;AACA,gBAAMuC,SAAS,GAAG,IAAI7C,IAAJ,CAAS,OAAT,CAAlB;AACA6C,UAAAA,SAAS,CAACT,SAAV,CAAoBiC,UAApB;AAEA,gBAAMvB,cAAc,GAAGD,SAAS,CAAC1B,YAAV,CAAuBd,WAAvB,CAAvB;AACAyC,UAAAA,cAAc,CAACrB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAqB,UAAAA,cAAc,CAAClB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAiB,UAAAA,SAAS,CAACvB,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AAEA,gBAAMyB,KAAK,GAAGF,SAAS,CAAC1B,YAAV,CAAuBjB,KAAvB,CAAd;AACA6C,UAAAA,KAAK,CAACJ,MAAN,GAAe,IAAf;AACAI,UAAAA,KAAK,CAACH,QAAN,GAAiB,EAAjB;AACAG,UAAAA,KAAK,CAACR,KAAN,GAAc,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAd,CAjC8B,CAmC9B;;AACA,eAAKkE,WAAL,CAAiBnD,IAAjB,CAAsB2D,EAAtB,CAAyB/E,MAAM,CAACgF,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,kBAAtD,EAA0E,IAA1E;AACH;AAED;AACJ;AACA;;;AACYjD,QAAAA,iBAAiB,GAAS;AAC9B,gBAAMW,SAAS,GAAG,IAAI7C,IAAJ,CAAS,aAAT,CAAlB;AACA6C,UAAAA,SAAS,CAACT,SAAV,CAAoB,KAAKf,IAAzB;AAEA,gBAAMyB,cAAc,GAAGD,SAAS,CAAC1B,YAAV,CAAuBd,WAAvB,CAAvB;AACAyC,UAAAA,cAAc,CAACrB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAqB,UAAAA,cAAc,CAAClB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAiB,UAAAA,SAAS,CAACvB,WAAV,CAAsB,CAAtB,EAAyB,CAAC,GAA1B,EAA+B,CAA/B;AAEA,eAAK8D,WAAL,GAAmBvC,SAAS,CAAC1B,YAAV,CAAuBjB,KAAvB,CAAnB;AACA,eAAKkF,WAAL,CAAiBzC,MAAjB,GAA0B,WAA1B;AACA,eAAKyC,WAAL,CAAiBxC,QAAjB,GAA4B,EAA5B;AACA,eAAKwC,WAAL,CAAiB7C,KAAjB,GAAyB,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAzB;AACH;AAED;AACJ;AACA;;;AACY6E,QAAAA,kBAAkB,GAAS;AAC/B,gBAAME,QAAQ,GAAG,KAAKnC,aAAL,CAAmBP,MAAnB,CAA0B2C,IAA1B,EAAjB;AACA,gBAAMC,QAAQ,GAAG,KAAKtB,aAAL,CAAmBtB,MAAnB,CAA0B2C,IAA1B,EAAjB;AAEAxE,UAAAA,OAAO,CAACC,GAAR,CAAa,gCAA+BsE,QAAS,EAArD;;AAEA,cAAI,CAACA,QAAL,EAAe;AACX,iBAAKG,UAAL,CAAgB,QAAhB,EAA0B,IAAIlF,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA1B;AACA;AACH;;AAED,cAAI,CAACiF,QAAL,EAAe;AACX,iBAAKC,UAAL,CAAgB,OAAhB,EAAyB,IAAIlF,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAzB;AACA;AACH,WAd8B,CAgB/B;;;AACA,eAAKkF,UAAL,CAAgB,SAAhB,EAA2B,IAAIlF,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,CAApB,EAAuB,GAAvB,CAA3B;AACA,eAAKkE,WAAL,CAAiBiB,YAAjB,GAAgC,KAAhC,CAlB+B,CAoB/B;;AACA,eAAKC,YAAL,CAAkBL,QAAlB,EAA4BE,QAA5B;AACH;AAED;AACJ;AACA;;;AACYG,QAAAA,YAAY,CAACL,QAAD,EAAmBE,QAAnB,EAA2C;AAC3DzE,UAAAA,OAAO,CAACC,GAAR,CAAa,yBAAwBsE,QAAS,EAA9C;;AAEA,cAAI;AACA;AACA,kBAAMM,YAAY,GAAG;AAAA;AAAA,8CAAaC,WAAb,EAArB;AACAD,YAAAA,YAAY,CAACE,YAAb,CAA0BR,QAA1B,EAAoCE,QAApC,EAHA,CAKA;;AACA,iBAAKO,YAAL,CAAkB,MAAM;AACpB;AACA;AACA,kBAAIT,QAAQ,KAAK,MAAb,IAAuBE,QAAQ,KAAK,MAAxC,EAAgD;AAC5C,qBAAKC,UAAL,CAAgB,OAAhB,EAAyB,IAAIlF,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,CAAlB,EAAqB,GAArB,CAAzB;AACAQ,gBAAAA,OAAO,CAACC,GAAR,CAAY,6BAAZ;AACH,eAHD,MAGO;AACH,qBAAKyE,UAAL,CAAgB,YAAhB,EAA8B,IAAIlF,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,CAApB,EAAuB,GAAvB,CAA9B,EADG,CAEH;;AACA,qBAAKwF,YAAL,CAAkB,MAAM;AACpB,uBAAKN,UAAL,CAAgB,eAAhB,EAAiC,IAAIlF,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAjC;AACA,uBAAKkE,WAAL,CAAiBiB,YAAjB,GAAgC,IAAhC;AACH,iBAHD,EAGG,GAHH;AAIH;AACJ,aAdD,EAcG,GAdH;AAeH,WArBD,CAqBE,OAAOM,KAAP,EAAc;AACZjF,YAAAA,OAAO,CAACiF,KAAR,CAAc,uBAAd,EAAuCA,KAAvC;AACA,iBAAKP,UAAL,CAAgB,UAAhB,EAA4B,IAAIlF,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA5B;AACA,iBAAKkE,WAAL,CAAiBiB,YAAjB,GAAgC,IAAhC;AACH;AACJ;AAED;AACJ;AACA;;;AACYD,QAAAA,UAAU,CAACQ,OAAD,EAAkBzD,KAAlB,EAAsC;AACpD,cAAI,KAAK6C,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBzC,MAAjB,GAA0BqD,OAA1B;AACA,iBAAKZ,WAAL,CAAiB7C,KAAjB,GAAyBA,KAAzB;AACH;;AACDzB,UAAAA,OAAO,CAACC,GAAR,CAAa,uBAAsBiF,OAAQ,EAA3C;AACH;;AAxUwC,O", "sourcesContent": ["import { _decorator, Component, Node, Button, Label, EditBox, Canvas, UITransform, Color, Sprite, screen } from 'cc';\nimport LoginCommand from './LoginCommand';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 简单登录UI\n * 基于测试成功的UI结构创建的实际登录界面\n */\n@ccclass('SimpleLoginUI')\nexport class SimpleLoginUI extends Component {\n    \n    private usernameInput: EditBox = null;\n    private passwordInput: EditBox = null;\n    private loginButton: Button = null;\n    private statusLabel: Label = null;\n    \n    protected onLoad(): void {\n        console.log('[SimpleLoginUI] 开始创建简单登录界面');\n        \n        // 创建登录UI\n        this.createLoginUI();\n        \n        console.log('[SimpleLoginUI] 简单登录界面创建完成');\n    }\n    \n    /**\n     * 创建登录UI\n     */\n    private createLoginUI(): void {\n        // 确保当前节点有Canvas组件\n        let canvas = this.getComponent(Canvas);\n        if (!canvas) {\n            canvas = this.addComponent(Canvas);\n        }\n        \n        // 设置Canvas（使用成功的配置）\n        canvas.alignCanvasWithScreen = false;\n        this.node.setPosition(0, 0, 0);\n        \n        // 设置UITransform\n        const uiTransform = this.getComponent(UITransform);\n        if (uiTransform) {\n            const windowSize = screen.windowSize;\n            uiTransform.setContentSize(windowSize.width, windowSize.height);\n            uiTransform.setAnchorPoint(0.5, 0.5);\n        }\n        \n        // 创建背景\n        this.createBackground();\n        \n        // 创建标题\n        this.createTitle();\n        \n        // 创建用户名输入框\n        this.createUsernameInput();\n        \n        // 创建密码输入框\n        this.createPasswordInput();\n        \n        // 创建登录按钮\n        this.createLoginButton();\n        \n        // 创建状态标签\n        this.createStatusLabel();\n    }\n    \n    /**\n     * 创建背景\n     */\n    private createBackground(): void {\n        const bgNode = new Node('Background');\n        bgNode.setParent(this.node);\n        \n        const bgTransform = bgNode.addComponent(UITransform);\n        const windowSize = screen.windowSize;\n        bgTransform.setContentSize(windowSize.width, windowSize.height);\n        bgTransform.setAnchorPoint(0.5, 0.5);\n        \n        const bgSprite = bgNode.addComponent(Sprite);\n        bgSprite.color = new Color(30, 30, 50, 255); // 深蓝色背景\n        \n        bgNode.setPosition(0, 0, 0);\n    }\n    \n    /**\n     * 创建标题\n     */\n    private createTitle(): void {\n        const titleNode = new Node('Title');\n        titleNode.setParent(this.node);\n        \n        const titleTransform = titleNode.addComponent(UITransform);\n        titleTransform.setContentSize(400, 60);\n        titleTransform.setAnchorPoint(0.5, 0.5);\n        titleNode.setPosition(0, 150, 0);\n        \n        const titleLabel = titleNode.addComponent(Label);\n        titleLabel.string = 'SLG游戏登录';\n        titleLabel.fontSize = 36;\n        titleLabel.color = new Color(255, 255, 255, 255);\n    }\n    \n    /**\n     * 创建用户名输入框\n     */\n    private createUsernameInput(): void {\n        // 标签\n        const labelNode = new Node('UsernameLabel');\n        labelNode.setParent(this.node);\n        const labelTransform = labelNode.addComponent(UITransform);\n        labelTransform.setContentSize(300, 30);\n        labelTransform.setAnchorPoint(0.5, 0.5);\n        labelNode.setPosition(0, 80, 0);\n\n        const label = labelNode.addComponent(Label);\n        label.string = '用户名：';\n        label.fontSize = 20;\n        label.color = new Color(255, 255, 255, 255);\n\n        // 创建一个简单的EditBox节点\n        const inputNode = new Node('UsernameInput');\n        inputNode.setParent(this.node);\n\n        // 设置变换组件\n        const inputTransform = inputNode.addComponent(UITransform);\n        inputTransform.setContentSize(300, 50);\n        inputTransform.setAnchorPoint(0.5, 0.5);\n        inputNode.setPosition(0, 40, 0);\n\n        // 添加背景精灵\n        const bgSprite = inputNode.addComponent(Sprite);\n        // 使用纯色背景\n        bgSprite.color = new Color(255, 255, 255, 255);\n\n        // 添加EditBox组件\n        this.usernameInput = inputNode.addComponent(EditBox);\n        this.usernameInput.string = '';\n        this.usernameInput.placeholder = '请输入用户名';\n        this.usernameInput.maxLength = 20;\n        this.usernameInput.fontSize = 20;\n        this.usernameInput.fontColor = new Color(0, 0, 0, 255);\n        this.usernameInput.placeholderFontColor = new Color(128, 128, 128, 255);\n        this.usernameInput.inputMode = EditBox.InputMode.SINGLE_LINE;\n        this.usernameInput.returnType = EditBox.KeyboardReturnType.DONE;\n\n        // 设置背景图像类型\n        this.usernameInput.backgroundImage = bgSprite;\n\n        // 添加调试日志\n        console.log('[SimpleLoginUI] 用户名输入框创建完成');\n        console.log('[SimpleLoginUI] EditBox节点位置:', inputNode.position.x, inputNode.position.y);\n        console.log('[SimpleLoginUI] EditBox尺寸:', inputTransform.width, inputTransform.height);\n    }\n    \n    /**\n     * 创建密码输入框\n     */\n    private createPasswordInput(): void {\n        // 标签\n        const labelNode = new Node('PasswordLabel');\n        labelNode.setParent(this.node);\n        const labelTransform = labelNode.addComponent(UITransform);\n        labelTransform.setContentSize(300, 30);\n        labelTransform.setAnchorPoint(0.5, 0.5);\n        labelNode.setPosition(0, -20, 0);\n\n        const label = labelNode.addComponent(Label);\n        label.string = '密码：';\n        label.fontSize = 20;\n        label.color = new Color(255, 255, 255, 255);\n\n        // 创建一个简单的EditBox节点\n        const inputNode = new Node('PasswordInput');\n        inputNode.setParent(this.node);\n\n        // 设置变换组件\n        const inputTransform = inputNode.addComponent(UITransform);\n        inputTransform.setContentSize(300, 50);\n        inputTransform.setAnchorPoint(0.5, 0.5);\n        inputNode.setPosition(0, -60, 0);\n\n        // 添加背景精灵\n        const bgSprite = inputNode.addComponent(Sprite);\n        // 使用纯色背景\n        bgSprite.color = new Color(255, 255, 255, 255);\n\n        // 添加EditBox组件\n        this.passwordInput = inputNode.addComponent(EditBox);\n        this.passwordInput.string = '';\n        this.passwordInput.placeholder = '请输入密码';\n        this.passwordInput.maxLength = 20;\n        this.passwordInput.fontSize = 20;\n        this.passwordInput.fontColor = new Color(0, 0, 0, 255);\n        this.passwordInput.placeholderFontColor = new Color(128, 128, 128, 255);\n        this.passwordInput.inputFlag = EditBox.InputFlag.PASSWORD; // 密码模式\n        this.passwordInput.inputMode = EditBox.InputMode.SINGLE_LINE;\n        this.passwordInput.returnType = EditBox.KeyboardReturnType.DONE;\n\n        // 设置背景图像类型\n        this.passwordInput.backgroundImage = bgSprite;\n\n        // 添加调试日志\n        console.log('[SimpleLoginUI] 密码输入框创建完成');\n        console.log('[SimpleLoginUI] EditBox节点位置:', inputNode.position.x, inputNode.position.y);\n        console.log('[SimpleLoginUI] EditBox尺寸:', inputTransform.width, inputTransform.height);\n    }\n    \n    /**\n     * 创建登录按钮\n     */\n    private createLoginButton(): void {\n        const buttonNode = new Node('LoginButton');\n        buttonNode.setParent(this.node);\n        \n        const buttonTransform = buttonNode.addComponent(UITransform);\n        buttonTransform.setContentSize(200, 60);\n        buttonTransform.setAnchorPoint(0.5, 0.5);\n        buttonNode.setPosition(0, -140, 0);\n        \n        // 按钮背景\n        const buttonSprite = buttonNode.addComponent(Sprite);\n        buttonSprite.color = new Color(0, 150, 255, 255);\n        \n        // 按钮组件\n        this.loginButton = buttonNode.addComponent(Button);\n        this.loginButton.transition = Button.Transition.COLOR;\n        this.loginButton.normalColor = new Color(0, 150, 255, 255);\n        this.loginButton.pressedColor = new Color(0, 100, 200, 255);\n        this.loginButton.hoverColor = new Color(50, 180, 255, 255);\n        this.loginButton.disabledColor = new Color(100, 100, 100, 255);\n        \n        // 按钮文字\n        const labelNode = new Node('Label');\n        labelNode.setParent(buttonNode);\n        \n        const labelTransform = labelNode.addComponent(UITransform);\n        labelTransform.setContentSize(200, 60);\n        labelTransform.setAnchorPoint(0.5, 0.5);\n        labelNode.setPosition(0, 0, 0);\n        \n        const label = labelNode.addComponent(Label);\n        label.string = '登录';\n        label.fontSize = 24;\n        label.color = new Color(255, 255, 255, 255);\n        \n        // 绑定点击事件\n        this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);\n    }\n    \n    /**\n     * 创建状态标签\n     */\n    private createStatusLabel(): void {\n        const labelNode = new Node('StatusLabel');\n        labelNode.setParent(this.node);\n        \n        const labelTransform = labelNode.addComponent(UITransform);\n        labelTransform.setContentSize(400, 50);\n        labelTransform.setAnchorPoint(0.5, 0.5);\n        labelNode.setPosition(0, -220, 0);\n        \n        this.statusLabel = labelNode.addComponent(Label);\n        this.statusLabel.string = '请输入用户名和密码';\n        this.statusLabel.fontSize = 16;\n        this.statusLabel.color = new Color(200, 200, 200, 255);\n    }\n    \n    /**\n     * 登录按钮点击事件\n     */\n    private onLoginButtonClick(): void {\n        const username = this.usernameInput.string.trim();\n        const password = this.passwordInput.string.trim();\n        \n        console.log(`[SimpleLoginUI] 登录按钮被点击，用户名: ${username}`);\n        \n        if (!username) {\n            this.showStatus('请输入用户名', new Color(255, 100, 100, 255));\n            return;\n        }\n        \n        if (!password) {\n            this.showStatus('请输入密码', new Color(255, 100, 100, 255));\n            return;\n        }\n        \n        // 显示登录中状态\n        this.showStatus('正在登录...', new Color(255, 255, 0, 255));\n        this.loginButton.interactable = false;\n        \n        // 调用登录逻辑\n        this.performLogin(username, password);\n    }\n    \n    /**\n     * 执行登录\n     */\n    private performLogin(username: string, password: string): void {\n        console.log(`[SimpleLoginUI] 执行登录: ${username}`);\n\n        try {\n            // 调用实际的登录逻辑\n            const loginCommand = LoginCommand.getInstance();\n            loginCommand.accountLogin(username, password);\n\n            // 监听登录结果\n            this.scheduleOnce(() => {\n                // 这里可以根据实际的登录状态来判断\n                // 暂时使用简单的测试逻辑\n                if (username === 'test' && password === 'test') {\n                    this.showStatus('登录成功！', new Color(0, 255, 0, 255));\n                    console.log('[SimpleLoginUI] 登录成功，准备进入游戏');\n                } else {\n                    this.showStatus('正在连接服务器...', new Color(255, 255, 0, 255));\n                    // 实际项目中这里会通过事件监听登录结果\n                    this.scheduleOnce(() => {\n                        this.showStatus('连接服务器失败，请检查网络', new Color(255, 100, 100, 255));\n                        this.loginButton.interactable = true;\n                    }, 3.0);\n                }\n            }, 1.0);\n        } catch (error) {\n            console.error('[SimpleLoginUI] 登录出错:', error);\n            this.showStatus('登录出错，请重试', new Color(255, 100, 100, 255));\n            this.loginButton.interactable = true;\n        }\n    }\n    \n    /**\n     * 显示状态信息\n     */\n    private showStatus(message: string, color: Color): void {\n        if (this.statusLabel) {\n            this.statusLabel.string = message;\n            this.statusLabel.color = color;\n        }\n        console.log(`[SimpleLoginUI] 状态: ${message}`);\n    }\n}\n"]}