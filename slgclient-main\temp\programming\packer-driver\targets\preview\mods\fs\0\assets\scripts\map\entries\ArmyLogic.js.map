{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts"], "names": ["ArmyLogic", "Vec2", "Animation", "Vec3", "UITransform", "DateUtil", "MapUtil", "clear", "data", "aniNode", "arrowNode", "_parentLayer", "destroy", "parent", "update", "state", "nowTime", "getServerTime", "endTime", "percent", "Math", "max", "startTime", "pos", "position", "clone", "x", "_startPixelPos", "_lenX", "y", "_lenY", "setPosition", "cellPoint", "mapPixelToCellPoint", "_endPixelPos", "toX", "toY", "updateArrow", "active", "getPosition", "len", "sqrt", "abs", "angle", "atan2", "PI", "getComponent", "height", "setArmyData", "startPos", "mapCellToPixelPoint", "fromX", "fromY", "endPos", "_aniName", "play"], "mappings": ";;;qFAKqBA,S;;;;;;;;;;;;;;;;;;;;;;;AAFZC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAF/BC,MAAAA,Q;;AACAC,MAAAA,O;;;;;;;yBAGcN,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,wCACH,IADG;;AAAA,2CAEJ,IAFI;;AAAA,6CAGF,IAHE;;AAAA;;AAAA,4CAOE,EAPF;;AAAA,kDAQM,IAAIG,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CARN;;AAAA,gDASI,IAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CATJ;;AAAA,yCAUD,CAVC;;AAAA,yCAWD,CAXC;AAAA;;AAapBI,QAAAA,KAAK,GAAG;AACX,eAAKC,IAAL,GAAY,IAAZ;AACA,eAAKC,OAAL,GAAe,IAAf;AACA,eAAKC,SAAL,GAAiB,IAAjB;AACA,eAAKC,YAAL,GAAoB,IAApB;AACH;;AAEMC,QAAAA,OAAO,GAAS;AACnB,eAAKH,OAAL,CAAaI,MAAb,GAAsB,IAAtB;AACA,eAAKH,SAAL,CAAeG,MAAf,GAAwB,IAAxB;AACA,eAAKN,KAAL;AACH;;AAEMO,QAAAA,MAAM,GAAS;AAClB,cAAI,KAAKN,IAAL,IAAa,KAAKA,IAAL,CAAUO,KAAV,GAAkB,CAAnC,EAAsC;AAClC,gBAAIC,OAAe,GAAG;AAAA;AAAA,sCAASC,aAAT,EAAtB;;AACA,gBAAID,OAAO,GAAG,KAAKR,IAAL,CAAUU,OAAxB,EAAiC;AAC7B;AACA,kBAAIC,OAAe,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,CAACL,OAAO,GAAG,KAAKR,IAAL,CAAUc,SAArB,KAAmC,KAAKd,IAAL,CAAUU,OAAV,GAAoB,KAAKV,IAAL,CAAUc,SAAjE,CAAZ,CAAtB;AAEA,kBAAIC,GAAG,GAAG,KAAKd,OAAL,CAAae,QAAb,CAAsBC,KAAtB,EAAV;AACAF,cAAAA,GAAG,CAACG,CAAJ,GAAQ,KAAKC,cAAL,CAAoBD,CAApB,GAAwBP,OAAO,GAAG,KAAKS,KAA/C;AACAL,cAAAA,GAAG,CAACM,CAAJ,GAAQ,KAAKF,cAAL,CAAoBE,CAApB,GAAwBV,OAAO,GAAG,KAAKW,KAA/C;AACA,mBAAKrB,OAAL,CAAasB,WAAb,CAAyBR,GAAzB;AAEA,kBAAIS,SAAe,GAAG;AAAA;AAAA,sCAAQC,mBAAR,CAA4B,IAAIhC,IAAJ,CAASsB,GAAG,CAACG,CAAb,EAAgBH,GAAG,CAACM,CAApB,CAA5B,CAAtB;AACA,mBAAKrB,IAAL,CAAUkB,CAAV,GAAcM,SAAS,CAACN,CAAxB;AACA,mBAAKlB,IAAL,CAAUqB,CAAV,GAAcG,SAAS,CAACH,CAAxB;AACH,aAZD,MAYO;AACH,mBAAKpB,OAAL,CAAasB,WAAb,CAAyB,KAAKG,YAA9B;AACA,mBAAK1B,IAAL,CAAUkB,CAAV,GAAc,KAAKlB,IAAL,CAAU2B,GAAxB;AACA,mBAAK3B,IAAL,CAAUqB,CAAV,GAAc,KAAKrB,IAAL,CAAU4B,GAAxB;AACH;;AACD,iBAAKC,WAAL;AACA,mBAAO,IAAIpC,IAAJ,CAAS,KAAKO,IAAL,CAAUkB,CAAnB,EAAsB,KAAKlB,IAAL,CAAUqB,CAAhC,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAESQ,QAAAA,WAAW,GAAS;AAC1B,eAAK3B,SAAL,CAAe4B,MAAf,GAAwB,KAAK9B,IAAL,IAAa,KAAKA,IAAL,CAAUO,KAAV,GAAkB,CAAvD;;AACA,cAAI,KAAKL,SAAL,CAAe4B,MAAf,IAAyB,IAA7B,EAAmC;AAE/B,iBAAK5B,SAAL,CAAeqB,WAAf,CAA2B,KAAKtB,OAAL,CAAa8B,WAAb,EAA3B;AACA,gBAAIC,GAAW,GAAGpB,IAAI,CAACqB,IAAL,CACdrB,IAAI,CAACsB,GAAL,CAAS,CAAC,KAAKR,YAAL,CAAkBL,CAAlB,GAAsB,KAAKnB,SAAL,CAAec,QAAf,CAAwBK,CAA/C,KAAqD,KAAKK,YAAL,CAAkBL,CAAlB,GAAsB,KAAKnB,SAAL,CAAec,QAAf,CAAwBK,CAAnG,CAAT,IACET,IAAI,CAACsB,GAAL,CAAS,CAAC,KAAKR,YAAL,CAAkBR,CAAlB,GAAsB,KAAKhB,SAAL,CAAec,QAAf,CAAwBE,CAA/C,KAAqD,KAAKQ,YAAL,CAAkBR,CAAlB,GAAsB,KAAKhB,SAAL,CAAec,QAAf,CAAwBE,CAAnG,CAAT,CAFY,CAAlB;AAGA,gBAAIiB,KAAa,GAAGvB,IAAI,CAACwB,KAAL,CAAW,KAAKV,YAAL,CAAkBL,CAAlB,GAAsB,KAAKnB,SAAL,CAAec,QAAf,CAAwBK,CAAzD,EAA4D,KAAKK,YAAL,CAAkBR,CAAlB,GAAsB,KAAKhB,SAAL,CAAec,QAAf,CAAwBE,CAA1G,CAApB;AACA,iBAAKhB,SAAL,CAAeiC,KAAf,GAAuBA,KAAK,GAAG,GAAR,GAAcvB,IAAI,CAACyB,EAAnB,GAAwB,EAA/C;AACA,iBAAKnC,SAAL,CAAeoC,YAAf,CAA4B1C,WAA5B,EAAyC2C,MAAzC,GAAkDP,GAAlD;AACH;AACJ;;AAEMQ,QAAAA,WAAW,CAACxC,IAAD,EAAiBC,OAAjB,EAAgCC,SAAhC,EAAuD;AACrE,eAAKF,IAAL,GAAYA,IAAZ;AACA,eAAKC,OAAL,GAAeA,OAAf;AACA,eAAKC,SAAL,GAAiBA,SAAjB;AAEA,cAAIuC,QAAa,GAAG;AAAA;AAAA,kCAAQC,mBAAR,CAA4B,IAAIjD,IAAJ,CAAS,KAAKO,IAAL,CAAU2C,KAAnB,EAA0B,KAAK3C,IAAL,CAAU4C,KAApC,CAA5B,CAApB;AACA,eAAKzB,cAAL,CAAoBD,CAApB,GAAwBuB,QAAQ,CAACvB,CAAjC;AACA,eAAKC,cAAL,CAAoBE,CAApB,GAAwBoB,QAAQ,CAACpB,CAAjC;AAEA,cAAIwB,MAAW,GAAG;AAAA;AAAA,kCAAQH,mBAAR,CAA4B,IAAIjD,IAAJ,CAAS,KAAKO,IAAL,CAAU2B,GAAnB,EAAwB,KAAK3B,IAAL,CAAU4B,GAAlC,CAA5B,CAAlB;AACA,eAAKF,YAAL,CAAkBR,CAAlB,GAAsB2B,MAAM,CAAC3B,CAA7B;AACA,eAAKQ,YAAL,CAAkBL,CAAlB,GAAsBwB,MAAM,CAACxB,CAA7B;AAEA,eAAKD,KAAL,GAAa,KAAKM,YAAL,CAAkBR,CAAlB,GAAsB,KAAKC,cAAL,CAAoBD,CAAvD;AACA,eAAKI,KAAL,GAAa,KAAKI,YAAL,CAAkBL,CAAlB,GAAsB,KAAKF,cAAL,CAAoBE,CAAvD;AAEA,cAAIN,GAAG,GAAG;AAAA;AAAA,kCAAQ2B,mBAAR,CAA4B,IAAIjD,IAAJ,CAAS,KAAKO,IAAL,CAAUkB,CAAnB,EAAsB,KAAKlB,IAAL,CAAUqB,CAAhC,CAA5B,CAAV;AACA,eAAKpB,OAAL,CAAasB,WAAb,CAAyB,IAAI5B,IAAJ,CAASoB,GAAG,CAACG,CAAb,EAAgBH,GAAG,CAACM,CAApB,EAAuB,CAAvB,CAAzB;AAEA,eAAKyB,QAAL,GAAgB,UAAhB;;AACA,cAAI,KAAK3B,cAAL,CAAoBE,CAApB,IAAyB,KAAKK,YAAL,CAAkBL,CAA/C,EAAkD;AAC9C;AACA,gBAAI,KAAKF,cAAL,CAAoBD,CAApB,GAAwB,KAAKQ,YAAL,CAAkBR,CAA9C,EAAiD;AAC7C,mBAAK4B,QAAL,GAAgB,UAAhB;AACH,aAFD,MAEO;AACH,mBAAKA,QAAL,GAAgB,UAAhB;AACH;AACJ,WAPD,MAOO,IAAI,KAAK3B,cAAL,CAAoBE,CAApB,GAAwB,KAAKK,YAAL,CAAkBL,CAA9C,EAAiD;AACpD;AACA,gBAAI,KAAKF,cAAL,CAAoBD,CAApB,GAAwB,KAAKQ,YAAL,CAAkBR,CAA9C,EAAiD;AAC7C,mBAAK4B,QAAL,GAAgB,WAAhB;AACH,aAFD,MAEO,IAAI,KAAK3B,cAAL,CAAoBD,CAApB,IAAyB,KAAKQ,YAAL,CAAkBR,CAA/C,EAAkD;AACrD,mBAAK4B,QAAL,GAAgB,UAAhB;AACH,aAFM,MAEA;AACH,mBAAKA,QAAL,GAAgB,WAAhB;AACH;AACJ,WATM,MASA,IAAI,KAAK3B,cAAL,CAAoBE,CAApB,GAAwB,KAAKK,YAAL,CAAkBL,CAA9C,EAAiD;AACpD;AACA,gBAAI,KAAKF,cAAL,CAAoBD,CAApB,GAAwB,KAAKQ,YAAL,CAAkBR,CAA9C,EAAiD;AAC7C,mBAAK4B,QAAL,GAAgB,WAAhB;AACH,aAFD,MAEO,IAAI,KAAK3B,cAAL,CAAoBD,CAApB,IAAyB,KAAKQ,YAAL,CAAkBR,CAA/C,EAAkD;AACrD,mBAAK4B,QAAL,GAAgB,UAAhB;AACH,aAFM,MAEA;AACH,mBAAKA,QAAL,GAAgB,WAAhB;AACH;AACJ;;AAED,eAAK7C,OAAL,CAAaqC,YAAb,CAA0B5C,SAA1B,EAAqCqD,IAArC,CAA0C,KAAKD,QAA/C;AAEA,eAAKjB,WAAL;AACH;;AApH0B,O", "sourcesContent": ["import { ArmyData } from \"../../general/ArmyProxy\";\nimport DateUtil from \"../../utils/DateUtil\";\nimport MapUtil from \"../MapUtil\";\nimport { Vec2, Node, Animation, Vec3, UITransform } from \"cc\";\n\nexport default class ArmyLogic {\n    public data: ArmyData = null;\n    public aniNode: Node = null;\n    public arrowNode: Node = null;\n\n    protected _parentLayer: Node;\n\n    protected _aniName: string = \"\";\n    protected _startPixelPos: Vec3 = new Vec3(0, 0, 0);\n    protected _endPixelPos: Vec3 = new Vec3(0, 0, 0);\n    protected _lenX: number = 0;\n    protected _lenY: number = 0;\n\n    public clear() {\n        this.data = null;\n        this.aniNode = null;\n        this.arrowNode = null;\n        this._parentLayer = null;\n    }\n\n    public destroy(): void {\n        this.aniNode.parent = null;\n        this.arrowNode.parent = null;\n        this.clear();\n    }\n\n    public update(): Vec2 {\n        if (this.data && this.data.state > 0) {\n            let nowTime: number = DateUtil.getServerTime();\n            if (nowTime < this.data.endTime) {\n                //代表移动中\n                let percent: number = Math.max(0, (nowTime - this.data.startTime) / (this.data.endTime - this.data.startTime));\n               \n                let pos = this.aniNode.position.clone();\n                pos.x = this._startPixelPos.x + percent * this._lenX;\n                pos.y = this._startPixelPos.y + percent * this._lenY;\n                this.aniNode.setPosition(pos);\n\n                let cellPoint: Vec2 = MapUtil.mapPixelToCellPoint(new Vec2(pos.x, pos.y));\n                this.data.x = cellPoint.x;\n                this.data.y = cellPoint.y;\n            } else {\n                this.aniNode.setPosition(this._endPixelPos);\n                this.data.x = this.data.toX;\n                this.data.y = this.data.toY;\n            }\n            this.updateArrow();\n            return new Vec2(this.data.x, this.data.y);\n        }\n        return null;\n    }\n\n    protected updateArrow(): void {\n        this.arrowNode.active = this.data && this.data.state > 0;\n        if (this.arrowNode.active == true) {\n        \n            this.arrowNode.setPosition(this.aniNode.getPosition());\n            let len: number = Math.sqrt(\n                Math.abs((this._endPixelPos.y - this.arrowNode.position.y) * (this._endPixelPos.y - this.arrowNode.position.y))\n                + Math.abs((this._endPixelPos.x - this.arrowNode.position.x) * (this._endPixelPos.x - this.arrowNode.position.x)));\n            let angle: number = Math.atan2(this._endPixelPos.y - this.arrowNode.position.y, this._endPixelPos.x - this.arrowNode.position.x);\n            this.arrowNode.angle = angle * 180 / Math.PI + 90;\n            this.arrowNode.getComponent(UITransform).height = len;\n        }\n    }\n\n    public setArmyData(data: ArmyData, aniNode: Node, arrowNode: Node): void {\n        this.data = data;\n        this.aniNode = aniNode;\n        this.arrowNode = arrowNode;\n\n        let startPos:Vec2 = MapUtil.mapCellToPixelPoint(new Vec2(this.data.fromX, this.data.fromY));\n        this._startPixelPos.x = startPos.x;\n        this._startPixelPos.y = startPos.y;\n\n        let endPos:Vec2 = MapUtil.mapCellToPixelPoint(new Vec2(this.data.toX, this.data.toY));\n        this._endPixelPos.x = endPos.x;\n        this._endPixelPos.y = endPos.y;\n\n        this._lenX = this._endPixelPos.x - this._startPixelPos.x;\n        this._lenY = this._endPixelPos.y - this._startPixelPos.y;\n\n        let pos = MapUtil.mapCellToPixelPoint(new Vec2(this.data.x, this.data.y));\n        this.aniNode.setPosition(new Vec3(pos.x, pos.y, 0));\n\n        this._aniName = \"qb_run_r\";\n        if (this._startPixelPos.y == this._endPixelPos.y) {\n            //平行\n            if (this._startPixelPos.x < this._endPixelPos.x) {\n                this._aniName = \"qb_run_r\";\n            } else {\n                this._aniName = \"qb_run_l\";\n            }\n        } else if (this._startPixelPos.y < this._endPixelPos.y) {\n            //往上走\n            if (this._startPixelPos.x < this._endPixelPos.x) {\n                this._aniName = \"qb_run_ru\";\n            } else if (this._startPixelPos.x == this._endPixelPos.x) {\n                this._aniName = \"qb_run_u\";\n            } else {\n                this._aniName = \"qb_run_lu\";\n            }\n        } else if (this._startPixelPos.y > this._endPixelPos.y) {\n            //往下走\n            if (this._startPixelPos.x < this._endPixelPos.x) {\n                this._aniName = \"qb_run_rd\";\n            } else if (this._startPixelPos.x == this._endPixelPos.x) {\n                this._aniName = \"qb_run_d\";\n            } else {\n                this._aniName = \"qb_run_ld\";\n            }\n        }\n\n        this.aniNode.getComponent(Animation).play(this._aniName);\n\n        this.updateArrow();\n    }\n}\n"]}