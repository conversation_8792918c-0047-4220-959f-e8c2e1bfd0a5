{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts"], "names": ["GeneralDataX", "_decorator", "Component", "RichText", "Label", "UITransform", "Node", "AudioManager", "LogicEvent", "SkillEffectType", "GeneralCommand", "SkillCommand", "EventMgr", "ccclass", "property", "WarReportDesItemLogic", "setData", "data", "warReport", "isEnd", "_reportRound", "endLab", "node", "active", "warLab", "string", "roundsLabel", "round", "turn", "str", "skillString", "attackBefore", "attack", "defense", "att_cfg", "getInstance", "proxy", "getGeneralCfg", "cfgId", "def_cfg", "isAttack", "aName", "nameString", "bName", "attColor", "endColor", "denColor", "lossColor", "defenseLoss", "attackAfter", "length", "defenseAfter", "cNode", "getComponent", "height", "result", "occupy", "x", "y", "destroy", "destroy_durable", "Math", "ceil", "getGeneralX", "id", "gx", "attgs", "beg_attack_general", "i", "g", "gdata", "gcfg", "dengs", "beg_defense_general", "skills", "b", "gx1", "fromId", "skillCfg", "getSkillCfg", "skillColor", "name", "lv", "j", "toId", "to", "gx2", "estr", "effectString", "killString", "skill", "includeEffect", "ie", "ev", "effectValue", "er", "effectRound", "Defense", "Force", "Strategy", "Speed", "Destroy", "kill", "cfg", "general", "position", "index", "attstr", "positionString", "denStr", "clickPos", "console", "log", "instance", "playClick", "emit", "closeReport", "scrollToMap"], "mappings": ";;;4KAcaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,I,OAAAA,I;;AAC3DC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,e,iBAAAA,e;;AAGFC,MAAAA,c;;AAEAC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;8BAUjBD,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;yBAOLe,qB,WADpBF,OAAO,CAAC,uBAAD,C,UAKHC,QAAQ,CAACX,QAAD,C,UAGRW,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACR,IAAD,C,oCAdb,MACqBS,qBADrB,SACmDb,SADnD,CAC6D;AAAA;AAAA;;AAAA,gDAEnB,IAFmB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,6CAgBnC,IAhBmC;;AAAA,4CAkBvC,iBAlBuC;;AAAA,4CAmBvC,iBAnBuC;;AAAA,8CAoBrC,iBApBqC;;AAAA,6CAqBtC,iBArBsC;;AAAA,4CAsBvC,UAtBuC;;AAAA,0CAuBzC,GAvByC;;AAAA,0CAwBzC,GAxByC;AAAA;;AA2BlDc,QAAAA,OAAO,CAACC,IAAD,EAAsBC,SAAtB,EAA2CC,KAA3C,EAA8D;AAExE,eAAKC,YAAL,GAAoBH,IAApB;AACA,eAAKC,SAAL,GAAiBA,SAAjB;AACA,eAAKG,MAAL,CAAYC,IAAZ,CAAiBC,MAAjB,GAA0B,KAA1B;AACA,eAAKC,MAAL,CAAYC,MAAZ,GAAqB,EAArB;AACA,eAAKC,WAAL,CAAiBD,MAAjB,GAA0B,MAAM,KAAKL,YAAL,CAAkBO,KAAxB,GAAgC,IAAhC,GAAuC,KAAKP,YAAL,CAAkBQ,IAAzD,GAA8D,IAAxF,CANwE,CAQxE;;AACA,cAAIC,GAAG,GAAG,KAAKC,WAAL,CAAiBb,IAAI,CAACc,YAAtB,CAAV;AACA,eAAKP,MAAL,CAAYC,MAAZ,GAAqBI,GAArB,CAVwE,CAYxE;;AACA,cAAG,KAAKT,YAAL,CAAkBY,MAAlB,IAA4B,KAAKZ,YAAL,CAAkBa,OAAjD,EAAyD;AACrD,iBAAKT,MAAL,CAAYC,MAAZ,IAAsB,IAAtB;AAEA,gBAAIS,OAAO,GAAG;AAAA;AAAA,kDAAeC,WAAf,GAA6BC,KAA7B,CAAmCC,aAAnC,CAAiD,KAAKjB,YAAL,CAAkBY,MAAlB,CAAyBM,KAA1E,CAAd;AACA,gBAAIC,OAAO,GAAG;AAAA;AAAA,kDAAeJ,WAAf,GAA6BC,KAA7B,CAAmCC,aAAnC,CAAiD,KAAKjB,YAAL,CAAkBa,OAAlB,CAA0BK,KAA3E,CAAd;;AAEA,gBAAGrB,IAAI,CAACuB,QAAR,EAAiB;AACb,kBAAIC,KAAK,GAAG,KAAKC,UAAL,CAAgB,IAAhB,EAAsBR,OAAtB,EAA+B,KAAKd,YAAL,CAAkBY,MAAjD,CAAZ;AACA,kBAAIW,KAAK,GAAG,KAAKD,UAAL,CAAgB,KAAhB,EAAuBH,OAAvB,EAAgC,KAAKnB,YAAL,CAAkBa,OAAlD,CAAZ;AAEA,mBAAKT,MAAL,CAAYC,MAAZ,IAAuB,KAAKmB,QAAL,GAAgBH,KAAhB,GAAwB,KAAKI,QAA7B,GAAyC,KAAzC,GACrB,KAAKC,QADgB,GACJH,KADI,GACI,KAAKE,QADT,GAEvB,QAFuB,GAEZ,KAAKC,QAFO,GAEIH,KAFJ,GAEY,KAAKE,QAFjB,GAE4B,MAF5B,GAGvB,KAAKE,SAHkB,GAGN,KAAK3B,YAAL,CAAkB4B,WAHZ,GAG0B,KAAKH,QAH/B,GAG2C,KAHlE;AAIH,aARD,MAQK;AACD,kBAAIF,MAAK,GAAG,KAAKD,UAAL,CAAgB,IAAhB,EAAsBR,OAAtB,EAA+B,KAAKd,YAAL,CAAkBY,MAAjD,CAAZ;;AACA,kBAAIS,MAAK,GAAG,KAAKC,UAAL,CAAgB,KAAhB,EAAuBH,OAAvB,EAAgC,KAAKnB,YAAL,CAAkBa,OAAlD,CAAZ;;AAEA,mBAAKT,MAAL,CAAYC,MAAZ,IAAuB,KAAKqB,QAAL,GAAgBL,MAAhB,GAAwB,KAAKI,QAA7B,GAAyC,KAAzC,GACrB,KAAKD,QADgB,GACLD,MADK,GACG,KAAKE,QADR,GACmB,QADnB,GAErB,KAAKD,QAFgB,GAELD,MAFK,GAEG,KAAKE,QAFR,GAEmB,MAFnB,GAGvB,KAAKE,SAHkB,GAGN,KAAK3B,YAAL,CAAkB4B,WAHZ,GAG0B,KAAKH,QAH/B,GAG2C,KAHlE;AAIH;AAEJ;;AAED,cAAG5B,IAAI,CAACgC,WAAL,CAAiBC,MAAjB,GAA0B,CAA7B,EAA+B;AAC3B,iBAAK1B,MAAL,CAAYC,MAAZ,IAAsB,IAAtB;;AACA,gBAAII,IAAG,GAAG,KAAKC,WAAL,CAAiBb,IAAI,CAACgC,WAAtB,CAAV;;AACA,iBAAKzB,MAAL,CAAYC,MAAZ,IAAsBI,IAAtB;AACH;;AAGD,cAAGZ,IAAI,CAACkC,YAAL,CAAkBD,MAAlB,GAA2B,CAA9B,EAAgC;AAC5B,iBAAK1B,MAAL,CAAYC,MAAZ,IAAsB,IAAtB;;AACA,gBAAII,KAAG,GAAG,KAAKC,WAAL,CAAiBb,IAAI,CAACkC,YAAtB,CAAV;;AACA,iBAAK3B,MAAL,CAAYC,MAAZ,IAAsBI,KAAtB;AACH;;AAED,eAAKuB,KAAL,CAAWC,YAAX,CAAwBhD,WAAxB,EAAqCiD,MAArC,GAA8C,KAAK9B,MAAL,CAAY6B,YAAZ,CAAyBhD,WAAzB,EAAsCiD,MAApF;;AACA,cAAGnC,KAAH,EAAS;AACL,iBAAKE,MAAL,CAAYC,IAAZ,CAAiBC,MAAjB,GAA0B,IAA1B;AACA,iBAAKF,MAAL,CAAYI,MAAZ,GAAqB,EAArB;;AACA,gBAAI,KAAKP,SAAL,CAAeqC,MAAf,IAAyB,CAA7B,EAA+B;AAC3B,kBAAI1B,KAAG,GAAG,kBAAV;AACA,mBAAKR,MAAL,CAAYI,MAAZ,GAAqBI,KAArB;AACH,aAHD,MAGM,IAAG,KAAKX,SAAL,CAAeqC,MAAf,IAAyB,CAA5B,EAA8B;AAChC,kBAAI1B,KAAG,GAAG,WAAV;AACA,mBAAKR,MAAL,CAAYI,MAAZ,GAAqBI,KAArB;AACH,aAHK,MAGA,IAAG,KAAKX,SAAL,CAAeqC,MAAf,IAAyB,CAA5B,EAA8B;AAChC,kBAAI1B,KAAG,GAAG,cAAV;;AACA,kBAAG,KAAK,KAAKX,SAAL,CAAesC,MAAvB,EAA8B;AAC1B3B,gBAAAA,KAAG,IAAK,WAAU,KAAKX,SAAL,CAAeuC,CAAzB,GAA6B,GAA7B,GAAmC,KAAKvC,SAAL,CAAewC,CAAlD,GAAsD,KAA9D;AACA,qBAAKrC,MAAL,CAAYI,MAAZ,GAAqBI,KAArB;AACH,eAHD,MAGK;AACD,oBAAI8B,OAAO,GAAG,KAAKzC,SAAL,CAAe0C,eAAf,GAAiC,GAA/C;AACA/B,gBAAAA,KAAG,IAAK,OAAM,KAAKX,SAAL,CAAeuC,CAArB,GAAyB,GAAzB,GAA+B,KAAKvC,SAAL,CAAewC,CAA9C,GAAkD,OAAlD,GAA2DG,IAAI,CAACC,IAAL,CAAUH,OAAV,CAA3D,GAAgF,IAAxF;AACA,qBAAKtC,MAAL,CAAYI,MAAZ,GAAqBI,KAArB;AACH;AACJ;;AAED,iBAAKuB,KAAL,CAAWC,YAAX,CAAwBhD,WAAxB,EAAqCiD,MAArC,GAA8C,KAAK9B,MAAL,CAAY6B,YAAZ,CAAyBhD,WAAzB,EAAsCiD,MAAtC,GAA+C,KAAKjC,MAAL,CAAYgC,YAAZ,CAAyBhD,WAAzB,EAAsCiD,MAArF,GAA8F,EAA5I;AACH;;AAGD,eAAKhC,IAAL,CAAU+B,YAAV,CAAuBhD,WAAvB,EAAoCiD,MAApC,GAA6C,KAAKF,KAAL,CAAWC,YAAX,CAAwBhD,WAAxB,EAAqCiD,MAArC,GAA8C,EAA3F;AAEH;;AAGOS,QAAAA,WAAW,CAACC,EAAD,EAAwB;AACvC,cAAIC,EAAE,GAAG,IAAIjE,YAAJ,EAAT,CADuC,CAEvC;;AACA,cAAIkE,KAAK,GAAG,KAAKhD,SAAL,CAAeiD,kBAA3B;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAAK,CAAChB,MAA1B,EAAkCkB,CAAC,EAAnC,EAAuC;AACnC,gBAAMC,CAAC,GAAGH,KAAK,CAACE,CAAD,CAAf;;AACA,gBAAGC,CAAC,CAACL,EAAF,IAAQA,EAAX,EAAc;AACVC,cAAAA,EAAE,CAACK,KAAH,GAAWD,CAAX;AACAJ,cAAAA,EAAE,CAACzB,QAAH,GAAc,IAAd;AACAyB,cAAAA,EAAE,CAACM,IAAH,GAAU;AAAA;AAAA,oDAAepC,WAAf,GAA6BC,KAA7B,CAAmCC,aAAnC,CAAiD4B,EAAE,CAACK,KAAH,CAAShC,KAA1D,CAAV;AACA,qBAAO2B,EAAP;AACH;AACJ;;AAED,cAAIO,KAAK,GAAG,KAAKtD,SAAL,CAAeuD,mBAA3B;;AACA,eAAK,IAAIL,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGI,KAAK,CAACtB,MAA1B,EAAkCkB,EAAC,EAAnC,EAAuC;AACnC,gBAAMC,EAAC,GAAGG,KAAK,CAACJ,EAAD,CAAf;;AACA,gBAAGC,EAAC,CAACL,EAAF,IAAQA,EAAX,EAAc;AACVC,cAAAA,EAAE,CAACK,KAAH,GAAWD,EAAX;AACAJ,cAAAA,EAAE,CAACzB,QAAH,GAAc,KAAd;AACAyB,cAAAA,EAAE,CAACM,IAAH,GAAU;AAAA;AAAA,oDAAepC,WAAf,GAA6BC,KAA7B,CAAmCC,aAAnC,CAAiD4B,EAAE,CAACK,KAAH,CAAShC,KAA1D,CAAV;AACA,qBAAO2B,EAAP;AACH;AACJ;AACJ;;AAEOnC,QAAAA,WAAW,CAAC4C,MAAD,EAAiC;AAChD,cAAI7C,GAAG,GAAG,EAAV;;AACA,eAAK,IAAIuC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGM,MAAM,CAACxB,MAA3B,EAAmCkB,CAAC,EAApC,EAAwC;AACpC,gBAAIO,CAAC,GAAGD,MAAM,CAACN,CAAD,CAAd;AACA,gBAAIQ,GAAG,GAAG,KAAKb,WAAL,CAAiBY,CAAC,CAACE,MAAnB,CAAV;AAEA,gBAAIC,QAAQ,GAAG;AAAA;AAAA,8CAAa3C,WAAb,GAA2BC,KAA3B,CAAiC2C,WAAjC,CAA6CJ,CAAC,CAACrC,KAA/C,CAAf;;AACA,gBAAIsC,GAAG,CAACpC,QAAR,EAAiB;AACbX,cAAAA,GAAG,IAAK,KAAKe,QAAL,GAAgB,KAAKF,UAAL,CAAgB,IAAhB,EAAsBkC,GAAG,CAACL,IAA1B,EAAgCK,GAAG,CAACN,KAApC,CAAhB,GAA6D,KAAKzB,QAA1E;AACH,aAFD,MAEK;AACDhB,cAAAA,GAAG,IAAK,KAAKiB,QAAL,GAAgB,KAAKJ,UAAL,CAAgB,KAAhB,EAAuBkC,GAAG,CAACL,IAA3B,EAAiCK,GAAG,CAACN,KAArC,CAAhB,GAA8D,KAAKzB,QAA3E;AACH;;AAGDhB,YAAAA,GAAG,IAAI,QAAP;AACAA,YAAAA,GAAG,IAAK,KAAKmD,UAAL,GAAkBF,QAAQ,CAACG,IAA3B,GAAkC,KAAlC,GAA0CN,CAAC,CAACO,EAA5C,GAAiD,IAAjD,GAAuD,KAAKrC,QAApE;AACAhB,YAAAA,GAAG,IAAI,MAAP;;AAEA,iBAAK,IAAIsD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,CAAC,CAACS,IAAF,CAAOlC,MAA3B,EAAmCiC,CAAC,EAApC,EAAwC;AACpC,kBAAIE,EAAE,GAAGV,CAAC,CAACS,IAAF,CAAOD,CAAP,CAAT;AACA,kBAAIG,GAAG,GAAG,KAAKvB,WAAL,CAAiBsB,EAAjB,CAAV;;AAEA,kBAAGC,GAAG,CAAC9C,QAAP,EAAgB;AACZX,gBAAAA,GAAG,IAAK,KAAKe,QAAL,GAAgB,KAAKF,UAAL,CAAgB,IAAhB,EAAsB4C,GAAG,CAACf,IAA1B,EAAgCe,GAAG,CAAChB,KAApC,CAAxB;AACH,eAFD,MAEK;AACDzC,gBAAAA,GAAG,IAAK,KAAKiB,QAAL,GAAgB,KAAKJ,UAAL,CAAgB,KAAhB,EAAuB4C,GAAG,CAACf,IAA3B,EAAiCe,GAAG,CAAChB,KAArC,CAAxB;AACH;;AAED,kBAAGa,CAAC,GAAGR,CAAC,CAACS,IAAF,CAAOlC,MAAP,GAAc,CAArB,EAAuB;AACnBrB,gBAAAA,GAAG,IAAI,GAAP;AACAA,gBAAAA,GAAG,IAAI,KAAKgB,QAAZ;AACH,eAHD,MAGK;AACDhB,gBAAAA,GAAG,IAAI,KAAKgB,QAAZ;AACAhB,gBAAAA,GAAG,IAAI,KAAP;AACH;AACJ;;AACDA,YAAAA,GAAG,IAAI,KAAKmD,UAAZ;AACA,gBAAIO,IAAI,GAAG,KAAKC,YAAL,CAAkBb,CAAlB,CAAX;AACA9C,YAAAA,GAAG,IAAI0D,IAAP;AACA1D,YAAAA,GAAG,IAAI,KAAKgB,QAAZ;AACAhB,YAAAA,GAAG,IAAI,KAAK4D,UAAL,CAAgBd,CAAhB,CAAP;AAEH;;AAED,iBAAO9C,GAAP;AACH;;AAEO2D,QAAAA,YAAY,CAACE,KAAD,EAA8B;AAC9C,cAAI7D,GAAG,GAAG,EAAV;;AACA,eAAK,IAAIuC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsB,KAAK,CAACC,aAAN,CAAoBzC,MAAxC,EAAgDkB,CAAC,EAAjD,EAAqD;AACjD,gBAAIwB,EAAE,GAAGF,KAAK,CAACC,aAAN,CAAoBvB,CAApB,CAAT;AACA,gBAAIyB,EAAE,GAAGH,KAAK,CAACI,WAAN,CAAkB1B,CAAlB,CAAT;AACA,gBAAI2B,EAAE,GAAGL,KAAK,CAACM,WAAN,CAAkB5B,CAAlB,CAAT;;AAEA,gBAAIwB,EAAE,IAAI;AAAA;AAAA,oDAAgBK,OAA1B,EAAkC;AAC9BpE,cAAAA,GAAG,IAAK,SAASgE,EAAjB;AACH,aAFD,MAEM,IAAID,EAAE,IAAI;AAAA;AAAA,oDAAgBM,KAA1B,EAAgC;AAClCrE,cAAAA,GAAG,IAAK,SAASgE,EAAjB;AACH,aAFK,MAEA,IAAID,EAAE,IAAI;AAAA;AAAA,oDAAgBO,QAA1B,EAAmC;AACrCtE,cAAAA,GAAG,IAAK,SAASgE,EAAjB;AACH,aAFK,MAEA,IAAID,EAAE,IAAI;AAAA;AAAA,oDAAgBQ,KAA1B,EAAgC;AAClCvE,cAAAA,GAAG,IAAK,SAASgE,EAAjB;AACH,aAFK,MAEA,IAAID,EAAE,IAAI;AAAA;AAAA,oDAAgBS,OAA1B,EAAkC;AACpCxE,cAAAA,GAAG,IAAK,SAASgE,EAAjB;AACH;;AACD,gBAAGE,EAAE,GAAG,CAAR,EAAU;AACNlE,cAAAA,GAAG,IAAM,OAAOkE,EAAP,GAAY,IAArB;AACH;AAEJ;;AACD,iBAAOlE,GAAP;AACH;;AAEO4D,QAAAA,UAAU,CAACC,KAAD,EAA8B;AAC5C,cAAG,CAACA,KAAK,CAACY,IAAV,EAAe;AACX,mBAAO,EAAP;AACH;;AAED,cAAIzE,GAAG,GAAG,IAAV;;AACA,eAAK,IAAIuC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsB,KAAK,CAACY,IAAN,CAAWpD,MAA/B,EAAuCkB,CAAC,EAAxC,EAA4C;AACxC,gBAAIkC,IAAI,GAAGZ,KAAK,CAACY,IAAN,CAAWlC,CAAX,CAAX;AACA,gBAAIiB,EAAE,GAAGK,KAAK,CAACN,IAAN,CAAWhB,CAAX,CAAT;AACA,gBAAIC,CAAC,GAAG,KAAKN,WAAL,CAAiBsB,EAAjB,CAAR;;AACA,gBAAGhB,CAAC,CAAC7B,QAAL,EAAc;AACVX,cAAAA,GAAG,IAAK,KAAKe,QAAL,GAAgB,GAAhB,GAAsB,KAAKF,UAAL,CAAgB,IAAhB,EAAsB2B,CAAC,CAACE,IAAxB,EAA8BF,CAAC,CAACC,KAAhC,CAAtB,GAA+D,GAA/D,GAAsE,KAAKzB,QAA3E,GAAsF,IAAtF,GAA6FyD,IAA7F,GAAoG,IAA5G;AACH,aAFD,MAEK;AACDzE,cAAAA,GAAG,IAAK,KAAKiB,QAAL,GAAgB,GAAhB,GAAsB,KAAKJ,UAAL,CAAgB,KAAhB,EAAuB2B,CAAC,CAACE,IAAzB,EAA+BF,CAAC,CAACC,KAAjC,CAAtB,GAAgE,GAAhE,GAAuE,KAAKzB,QAA5E,GAAuF,IAAvF,GAA8FyD,IAA9F,GAAqG,IAA7G;AACH;AACJ;;AACD,iBAAOzE,GAAP;AACH;;AAEOa,QAAAA,UAAU,CAACF,QAAD,EAAmB+D,GAAnB,EAAsCC,OAAtC,EAAmD;AACjE,cAAGhE,QAAH,EAAY;AACR,gBAAIiE,QAAQ,GAAG,CAAC,CAAhB;;AACA,iBAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAKxF,SAAL,CAAeiD,kBAAf,CAAkCjB,MAA9D,EAAsEwD,KAAK,EAA3E,EAA+E;AAC3E,kBAAMrC,CAAC,GAAG,KAAKnD,SAAL,CAAeiD,kBAAf,CAAkCuC,KAAlC,CAAV;;AACA,kBAAGrC,CAAC,CAACL,EAAF,IAAQwC,OAAO,CAACxC,EAAnB,EAAsB;AAClByC,gBAAAA,QAAQ,GAAGC,KAAX;AACA;AACH;AACJ;;AACD,mBAAO,KAAKC,MAAL,GAAcJ,GAAG,CAACtB,IAAlB,GAAyB,GAAzB,GAA+B,KAAK2B,cAAL,CAAoBH,QAApB,CAA/B,GAA+D,GAAtE;AACH,WAVD,MAUK;AACD,gBAAIA,SAAQ,GAAG,CAAC,CAAhB;;AACA,iBAAK,IAAIC,MAAK,GAAG,CAAjB,EAAoBA,MAAK,GAAG,KAAKxF,SAAL,CAAeiD,kBAAf,CAAkCjB,MAA9D,EAAsEwD,MAAK,EAA3E,EAA+E;AAC3E,kBAAMrC,GAAC,GAAG,KAAKnD,SAAL,CAAeuD,mBAAf,CAAmCiC,MAAnC,CAAV;;AACA,kBAAGrC,GAAC,CAACL,EAAF,IAAQwC,OAAO,CAACxC,EAAnB,EAAsB;AAClByC,gBAAAA,SAAQ,GAAGC,MAAX;AACA;AACH;AACJ;;AAED,mBAAO,KAAKG,MAAL,GAAcN,GAAG,CAACtB,IAAlB,GAAyB,GAAzB,GAA+B,KAAK2B,cAAL,CAAoBH,SAApB,CAA/B,GAA+D,GAAtE;AACH;AACJ;;AAEOG,QAAAA,cAAc,CAACH,QAAD,EAAkB;AACpC,cAAGA,QAAQ,IAAI,CAAf,EAAiB;AACb,mBAAO,IAAP;AACH,WAFD,MAEK;AACD,mBAAO,IAAP;AACH;AACJ;;AAESK,QAAAA,QAAQ,GAAG;AACjBC,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,WAAzB;AACA;AAAA;AAAA,oCAASD,IAAT,CAAc;AAAA;AAAA,wCAAWE,WAAzB,EAAsC,KAAKnG,SAAL,CAAeuC,CAArD,EAAwD,KAAKvC,SAAL,CAAewC,CAAvE;AACH;;AA1QwD,O;;;;;iBAKvC,I;;;;;;;iBAGE,I;;;;;;;iBAGL,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, RichText, Label, UITransform, math, Node } from 'cc';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\nimport { SkillEffectType } from '../../config/skill/Skill';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport { GeneralConfig, GeneralData } from '../../general/GeneralProxy';\nimport SkillCommand from '../../skill/SkillCommand';\nimport { EventMgr } from '../../utils/EventMgr';\nimport MapUICommand from './MapUICommand';\nimport { WarReport, WarReportRound, WarReportSkill } from \"./MapUIProxy\";\n\n\nexport class GeneralDataX {\n    gdata:GeneralData\n    gcfg:GeneralConfig\n    isAttack:boolean\n}\n\n@ccclass('WarReportDesItemLogic')\nexport default class WarReportDesItemLogic extends Component {\n\n    private _reportRound:WarReportRound = null;\n\n    @property(RichText)\n    warLab:RichText = null;\n\n    @property(Label)\n    roundsLabel:Label = null;\n\n    @property(Label)\n    endLab:Label = null;\n\n    @property(Node)\n    cNode:Node = null;\n\n    warReport:WarReport = null;\n\n    attColor:string = \"<color=#ff0000>\";\n    denColor:string = \"<color=#00ff00>\";\n    skillColor:string = \"<color=#FD6500>\";\n    lossColor:string = \"<color=#F2C420>\"\n    endColor:string = \"</color>\";\n    attstr:string = \"攻\";\n    denStr:string = \"防\";\n\n\n    public setData(data:WarReportRound, warReport:WarReport, isEnd:boolean):void{\n\n        this._reportRound = data;\n        this.warReport = warReport;\n        this.endLab.node.active = false;\n        this.warLab.string = \"\";\n        this.roundsLabel.string = \"第\" + this._reportRound.round + \"轮/\" + this._reportRound.turn+\"回合\";\n \n        //技能\n        let str = this.skillString(data.attackBefore);\n        this.warLab.string = str;\n        \n        //伤害\n        if(this._reportRound.attack && this._reportRound.defense){\n            this.warLab.string += \"\\n\";\n\n            var att_cfg = GeneralCommand.getInstance().proxy.getGeneralCfg(this._reportRound.attack.cfgId);\n            var def_cfg = GeneralCommand.getInstance().proxy.getGeneralCfg(this._reportRound.defense.cfgId);\n       \n            if(data.isAttack){\n                let aName = this.nameString(true, att_cfg, this._reportRound.attack);\n                let bName = this.nameString(false, def_cfg, this._reportRound.defense);\n\n                this.warLab.string += (this.attColor + aName + this.endColor  + \" 对 \" \n                + this.denColor +  bName + this.endColor + \n                \" 发起攻击，\" + this.denColor + bName + this.endColor + \" 损失 \" + \n                this.lossColor + this._reportRound.defenseLoss + this.endColor  + \" 士兵\");\n            }else{\n                let bName = this.nameString(true, att_cfg, this._reportRound.attack);\n                let aName = this.nameString(false, def_cfg, this._reportRound.defense);\n\n                this.warLab.string += (this.denColor + aName + this.endColor  + \" 对 \" \n                + this.attColor + bName + this.endColor + \" 发起攻击，\" \n                + this.attColor + bName + this.endColor + \" 损失 \" + \n                this.lossColor + this._reportRound.defenseLoss + this.endColor  + \" 士兵\");\n            }\n    \n        }\n\n        if(data.attackAfter.length > 0){\n            this.warLab.string += \"\\n\";\n            let str = this.skillString(data.attackAfter);\n            this.warLab.string += str;\n        }\n        \n        \n        if(data.defenseAfter.length > 0){\n            this.warLab.string += \"\\n\";\n            let str = this.skillString(data.defenseAfter);\n            this.warLab.string += str;\n        }\n      \n        this.cNode.getComponent(UITransform).height = this.warLab.getComponent(UITransform).height;\n        if(isEnd){\n            this.endLab.node.active = true;\n            this.endLab.string = \"\";\n            if (this.warReport.result == 0){\n                let str = \"我方主将兵力被消耗殆尽，战斗失败\";\n                this.endLab.string = str;\n            }else if(this.warReport.result == 1){\n                let str = \"战斗不分胜负，打平\";\n                this.endLab.string = str;\n            }else if(this.warReport.result == 2){\n                let str = \"对方主将兵力被消耗殆尽，\";\n                if(1 == this.warReport.occupy){\n                    str += (\"我方占领了(\"+ this.warReport.x + \",\" + this.warReport.y + \")领地\");\n                    this.endLab.string = str;\n                }else{\n                    let destroy = this.warReport.destroy_durable / 100;\n                    str += (\"对(\"+ this.warReport.x + \",\" + this.warReport.y + \")领地造成\"+ Math.ceil(destroy) + \"破坏\");\n                    this.endLab.string = str;\n                }\n            }\n            \n            this.cNode.getComponent(UITransform).height = this.warLab.getComponent(UITransform).height + this.endLab.getComponent(UITransform).height + 20;\n        }\n       \n\n        this.node.getComponent(UITransform).height = this.cNode.getComponent(UITransform).height + 40;\n       \n    }\n\n\n    private getGeneralX(id:Number):GeneralDataX{\n        let gx = new GeneralDataX();\n        // console.log(\"getGeneralX:\", this.warReport);\n        let attgs = this.warReport.beg_attack_general;\n        for (let i = 0; i < attgs.length; i++) {\n            const g = attgs[i];\n            if(g.id == id){\n                gx.gdata = g;\n                gx.isAttack = true;\n                gx.gcfg = GeneralCommand.getInstance().proxy.getGeneralCfg(gx.gdata.cfgId);\n                return gx;\n            }\n        }\n\n        let dengs = this.warReport.beg_defense_general;\n        for (let i = 0; i < dengs.length; i++) {\n            const g = dengs[i];\n            if(g.id == id){\n                gx.gdata = g;\n                gx.isAttack = false;\n                gx.gcfg = GeneralCommand.getInstance().proxy.getGeneralCfg(gx.gdata.cfgId);\n                return gx;\n            }\n        }\n    }\n\n    private skillString(skills:WarReportSkill[]):string {\n        let str = \"\";\n        for (let i = 0; i < skills.length; i++) {\n            let b = skills[i];\n            let gx1 = this.getGeneralX(b.fromId);\n            \n            let skillCfg = SkillCommand.getInstance().proxy.getSkillCfg(b.cfgId);\n            if (gx1.isAttack){\n                str += (this.attColor + this.nameString(true, gx1.gcfg, gx1.gdata) + this.endColor)\n            }else{\n                str += (this.denColor + this.nameString(false, gx1.gcfg, gx1.gdata) + this.endColor)\n            }\n\n           \n            str += \" 使用技能 \";\n            str += (this.skillColor + skillCfg.name + \"(lv\" + b.lv + \") \"+ this.endColor);\n            str += \"作用于 \"\n            \n            for (let j = 0; j < b.toId.length; j++) {\n                let to = b.toId[j];\n                let gx2 = this.getGeneralX(to);\n                \n                if(gx2.isAttack){\n                    str += (this.attColor + this.nameString(true, gx2.gcfg, gx2.gdata));\n                }else{\n                    str += (this.denColor + this.nameString(false, gx2.gcfg, gx2.gdata));\n                }\n               \n                if(j < b.toId.length-1){\n                    str += \",\"\n                    str += this.endColor;\n                }else{\n                    str += this.endColor;\n                    str += \" 身上\"\n                }\n            }\n            str += this.skillColor\n            let estr = this.effectString(b);\n            str += estr;\n            str += this.endColor;\n            str += this.killString(b);\n           \n        }\n\n        return str;\n    }\n\n    private effectString(skill:WarReportSkill):string {\n        let str = \"\"\n        for (let i = 0; i < skill.includeEffect.length; i++) {\n            let ie = skill.includeEffect[i];\n            let ev = skill.effectValue[i];\n            let er = skill.effectRound[i];\n\n            if (ie == SkillEffectType.Defense){\n                str += (\"防御提升\" + ev);\n            }else if (ie == SkillEffectType.Force){\n                str += (\"武力提升\" + ev);\n            }else if (ie == SkillEffectType.Strategy){\n                str += (\"谋略提升\" + ev);\n            }else if (ie == SkillEffectType.Speed){\n                str += (\"速度提升\" + ev);\n            }else if (ie == SkillEffectType.Destroy){\n                str += (\"破坏提升\" + ev);\n            }\n            if(er > 0){\n                str += ( \"持续\" + er + \"回合\");\n            }\n           \n        }\n        return str\n    }\n\n    private killString(skill:WarReportSkill):string {\n        if(!skill.kill){\n            return \"\";\n        }\n\n        let str = \"造成\"\n        for (let i = 0; i < skill.kill.length; i++) {\n            let kill = skill.kill[i];\n            let to = skill.toId[i];\n            let g = this.getGeneralX(to);\n            if(g.isAttack){\n                str += (this.attColor + \" \" + this.nameString(true, g.gcfg, g.gdata) + \" \"  + this.endColor + \"损失\" + kill + \"士兵\")\n            }else{\n                str += (this.denColor + \" \" + this.nameString(false, g.gcfg, g.gdata) + \" \"  + this.endColor + \"损失\" + kill + \"士兵\")\n            }\n        }\n        return str\n    }\n\n    private nameString(isAttack:boolean, cfg:GeneralConfig, general:any) {\n        if(isAttack){\n            let position = -1;\n            for (let index = 0; index < this.warReport.beg_attack_general.length; index++) {\n                const g = this.warReport.beg_attack_general[index];\n                if(g.id == general.id){\n                    position = index;\n                    break;\n                }\n            }\n            return this.attstr + cfg.name + \"(\" + this.positionString(position) + \")\"\n        }else{\n            let position = -1;\n            for (let index = 0; index < this.warReport.beg_attack_general.length; index++) {\n                const g = this.warReport.beg_defense_general[index];\n                if(g.id == general.id){\n                    position = index;\n                    break;\n                }\n            }\n\n            return this.denStr + cfg.name + \"(\" + this.positionString(position) + \")\"\n        }\n    }\n\n    private positionString(position:number) {\n        if(position == 0){\n            return \"主将\";\n        }else{\n            return \"副将\";\n        }\n    }\n\n    protected clickPos() {\n        console.log(\"clickPos\");\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.closeReport);\n        EventMgr.emit(LogicEvent.scrollToMap, this.warReport.x, this.warReport.y);\n    }\n\n}\n"]}