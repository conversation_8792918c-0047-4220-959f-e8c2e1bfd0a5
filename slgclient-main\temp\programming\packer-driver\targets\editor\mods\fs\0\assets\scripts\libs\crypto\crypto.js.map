{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts"], "names": ["Hex", "WordArray", "Latin1", "Utf8", "BufferedBlockAlgorithm", "Base", "CipherParams", "Base64", "OpenSSL", "SerializableCipher", "<PERSON><PERSON>", "MD5", "EvpKDF", "OpenSSLKdf", "PasswordBasedCipher", "Cipher", "BlockCipherModeAlgorithm", "BlockCipherMode", "CBCEncryptor", "CBCDecryptor", "CBC", "PKCS7", "ZeroPadding", "BlockCipher", "AES", "SHA256", "NoPadding", "ECBEncryptor", "ECBDecryptor", "ECB", "stringify", "wordArray", "hexChars", "i", "sigBytes", "bite", "words", "push", "toString", "join", "parse", "hexStr", "hexStr<PERSON>ength", "length", "parseInt", "substr", "random", "nBytes", "r", "m_w", "m_z", "mask", "result", "Math", "rcache", "_r", "constructor", "undefined", "encoder", "concat", "clamp", "thatByte", "ceil", "clone", "slice", "start", "end", "latin1Chars", "String", "fromCharCode", "latin1Str", "latin1StrLength", "charCodeAt", "decodeURIComponent", "escape", "e", "Error", "utf8Str", "unescape", "encodeURIComponent", "cfg", "_minBufferSize", "Object", "assign", "blockSize", "_data", "_nDataBytes", "reset", "_append", "data", "_process", "do<PERSON><PERSON><PERSON>", "blockSizeBytes", "nBlocksReady", "max", "nWordsReady", "nBytesReady", "min", "processedWords", "offset", "_doProcessBlock", "splice", "attr", "hasOwnProperty", "cipherParams", "ciphertext", "key", "iv", "salt", "algorithm", "mode", "padding", "formatter", "extend", "additionalParams", "base64Chars", "byte1", "byte2", "byte3", "triplet", "j", "_map", "char<PERSON>t", "paddingChar", "base64Str", "base64StrLength", "_reverseMap", "paddingIndex", "indexOf", "parseLoop", "reverseMap", "bits1", "bits2", "openSSLStr", "encrypt", "cipher", "message", "config", "encryptor", "createEncryptor", "finalize", "format", "decrypt", "optionalCfg", "_parse", "plaintext", "createDecryptor", "_createHelper", "hasher", "helper", "hasherClass", "hasherInstance", "update", "messageUpdate", "hash", "_doFinalize", "T", "abs", "sin", "FF", "a", "b", "c", "d", "x", "s", "t", "n", "GG", "HH", "II", "_hash", "M", "offset_i", "M_offset_i", "H", "M_offset_0", "M_offset_1", "M_offset_2", "M_offset_3", "M_offset_4", "M_offset_5", "M_offset_6", "M_offset_7", "M_offset_8", "M_offset_9", "M_offset_10", "M_offset_11", "M_offset_12", "M_offset_13", "M_offset_14", "M_offset_15", "dataWords", "nBitsTotal", "nBitsLeft", "nBitsTotalH", "floor", "nBitsTotalL", "H_i", "keySize", "iterations", "compute", "password", "<PERSON><PERSON><PERSON>", "block", "execute", "ivSize", "kdf", "derivedParams", "call", "xformMode", "_xformMode", "_key", "thisClass", "_ENC_XFORM_MODE", "_DEC_XFORM_MODE", "process", "dataUpdate", "finalProcessedData", "init", "_cipher", "_iv", "encryptorClass", "Encryptor", "decryptorClass", "Decryptor", "processBlock", "xorBlock", "encryptBlock", "_prevBlock", "thisBlock", "decryptBlock", "pad", "nPaddingBytes", "paddingWord", "paddingWords", "unpad", "modeCreator", "_mode", "__creator", "finalProcessedBlocks", "SBOX", "INV_SBOX", "SUB_MIX_0", "SUB_MIX_1", "SUB_MIX_2", "SUB_MIX_3", "INV_SUB_MIX_0", "INV_SUB_MIX_1", "INV_SUB_MIX_2", "INV_SUB_MIX_3", "xi", "sx", "x2", "x4", "x8", "RCON", "_nRounds", "_keyPriorReset", "key<PERSON>ords", "nRounds", "ksRows", "keySchedule", "_keySchedule", "ksRow", "invKeySchedule", "_invKeySchedule", "invKsRow", "_doCryptBlock", "sub_mix_0", "sub_mix_1", "sub_mix_2", "sub_mix_3", "sbox", "s0", "s1", "s2", "s3", "round", "t0", "t1", "t2", "t3", "t0g", "t1g", "t2g", "t3g", "K", "W", "Hl", "f", "g", "h", "gamma0x", "gamma0", "gamma1x", "gamma1", "ch", "maj", "sigma0", "sigma1", "lib", "algo", "enc", "AES$1", "SHA256$1"], "mappings": ";;;iBASMA,G,EAiDAC,S,EA2IAC,M,EAgDAC,I,EA2CAC,sB,EAwHAC,I,EAOAC,Y,EAkGAC,M,EA6FAC,O,EA+DAC,kB,EAqHAC,M,EAiGAC,G,EAuNAC,M,EA4DAC,U,EAmCAC,mB,EA8GAC,M,EAwKAC,wB,EAgCAC,e,EAyCAC,Y,EAsDAC,Y,EA4DAC,G,EASAC,K,EAoDAC,W,EAwCAC,W,EAgJAC,G,EA6JAC,M,EAyFAC,S,EAgCAC,Y,EAqBAC,Y,EAyBAC,G;;;;;;;;;;;AAnrEN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;yBACM7B,G,GAAN,MAAMA,GAAN,CAAU;AACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACoB,eAAT8B,SAAS,CAACC,SAAD,EAAY;AACxB;AACA;AAAM;AAAiBC,UAAAA,QAAQ,GAAG,EAAlC;;AACA,eAAK;AAAI;AAAiBC,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGF,SAAS,CAACG,QAA/C,EAAyDD,CAAC,EAA1D,EAA8D;AAC1D;AAAM;AAAiBE,YAAAA,IAAI,GAAIJ,SAAS,CAACK,KAAV,CAAgBH,CAAC,KAAK,CAAtB,MAA8B,KAAMA,CAAC,GAAG,CAAL,GAAU,CAA9C,GAAoD,IAAlF;AACAD,YAAAA,QAAQ,CAACK,IAAT,CAAc,CAACF,IAAI,KAAK,CAAV,EAAaG,QAAb,CAAsB,EAAtB,CAAd;AACAN,YAAAA,QAAQ,CAACK,IAAT,CAAc,CAACF,IAAI,GAAG,IAAR,EAAcG,QAAd,CAAuB,EAAvB,CAAd;AACH;;AACD,iBAAON,QAAQ,CAACO,IAAT,CAAc,EAAd,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,eAALC,KAAK,CAACC,MAAD,EAAS;AACjB;AACA;AAAM;AAAiBC,UAAAA,YAAY,GAAGD,MAAM,CAACE,MAA7C,CAFiB,CAGjB;;AACA;AAAM;AAAiBP,UAAAA,KAAK,GAAG,EAA/B;;AACA,eAAK;AAAI;AAAiBH,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGS,YAArC,EAAmDT,CAAC,IAAI,CAAxD,EAA2D;AACvDG,YAAAA,KAAK,CAACH,CAAC,KAAK,CAAP,CAAL,IAAkBW,QAAQ,CAACH,MAAM,CAACI,MAAP,CAAcZ,CAAd,EAAiB,CAAjB,CAAD,EAAsB,EAAtB,CAAR,IAAsC,KAAMA,CAAC,GAAG,CAAL,GAAU,CAAvE;AACH;;AACD,iBAAO,IAAIhC,SAAJ,CAAcmC,KAAd,EAAqBM,YAAY,GAAG,CAApC,CAAP;AACH;;AA1CK,O;AA6CV;AACA;AACA;AACA;;;yBACMzC,S,GAAN,MAAMA,SAAN,CAAgB;AACZ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACiB,eAAN6C,MAAM,CAACC,MAAD,EAAS;AAClB;AAAM;AAAiBX,UAAAA,KAAK,GAAG,EAA/B;;AACA;AAAM;AAAiBY,UAAAA,CAAC,GAAI,UAAUC,GAAV,EAAe;AACvC;AAAI;AAAiBC,YAAAA,GAAG,GAAG,UAA3B;AACA;AAAM;AAAiBC,YAAAA,IAAI,GAAG,UAA9B;AACA,mBAAO,YAAY;AACfD,cAAAA,GAAG,GAAI,UAAUA,GAAG,GAAG,MAAhB,KAA2BA,GAAG,IAAI,IAAlC,CAAD,GAA4CC,IAAlD;AACAF,cAAAA,GAAG,GAAI,UAAUA,GAAG,GAAG,MAAhB,KAA2BA,GAAG,IAAI,IAAlC,CAAD,GAA4CE,IAAlD;AACA;AAAI;AAAiBC,cAAAA,MAAM,GAAI,CAACF,GAAG,IAAI,IAAR,IAAgBD,GAAjB,GAAwBE,IAAtD;AACAC,cAAAA,MAAM,IAAI,WAAV;AACAA,cAAAA,MAAM,IAAI,GAAV;AACA,qBAAOA,MAAM,IAAIC,IAAI,CAACP,MAAL,KAAgB,EAAhB,GAAqB,CAArB,GAAyB,CAAC,CAA9B,CAAb;AACH,aAPD;AAQH,WAXD;;AAYA,eAAK;AAAI;AAAiBb,UAAAA,CAAC,GAAG,CAAzB;AAA4B;AAAiBqB,UAAAA,MAAlD,EAA0DrB,CAAC,GAAGc,MAA9D,EAAsEd,CAAC,IAAI,CAA3E,EAA8E;AAC1E;AAAM;AAAiBsB,YAAAA,EAAE,GAAGP,CAAC,CAAC,CAACM,MAAM,IAAID,IAAI,CAACP,MAAL,EAAX,IAA4B,WAA7B,CAA7B;;AACAQ,YAAAA,MAAM,GAAGC,EAAE,KAAK,UAAhB;AACAnB,YAAAA,KAAK,CAACC,IAAN,CAAYkB,EAAE,KAAK,WAAR,GAAuB,CAAlC;AACH;;AACD,iBAAO,IAAItD,SAAJ,CAAcmC,KAAd,EAAqBW,MAArB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIS,QAAAA,WAAW,CAACpB,KAAD,EAAQF,QAAR,EAAkB;AACzB,eAAKE,KAAL,GAAaA,KAAK,IAAI,EAAtB;;AACA,cAAIF,QAAQ,KAAKuB,SAAjB,EAA4B;AACxB,iBAAKvB,QAAL,GAAgBA,QAAhB;AACH,WAFD,MAGK;AACD,iBAAKA,QAAL,GAAgB,KAAKE,KAAL,CAAWO,MAAX,GAAoB,CAApC;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIL,QAAAA,QAAQ,CAACoB,OAAD,EAAU;AACd,iBAAO,CAACA,OAAO,IAAI1D,GAAZ,EAAiB8B,SAAjB,CAA2B,IAA3B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI6B,QAAAA,MAAM,CAAC5B,SAAD,EAAY;AACd;AACA,eAAK6B,KAAL,GAFc,CAGd;;AACA,cAAI,KAAK1B,QAAL,GAAgB,CAApB,EAAuB;AACnB;AACA,iBAAK;AAAI;AAAiBD,YAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGF,SAAS,CAACG,QAA/C,EAAyDD,CAAC,EAA1D,EAA8D;AAC1D;AAAM;AAAiB4B,cAAAA,QAAQ,GAAI9B,SAAS,CAACK,KAAV,CAAgBH,CAAC,KAAK,CAAtB,MAA8B,KAAMA,CAAC,GAAG,CAAL,GAAU,CAA9C,GAAoD,IAAtF;AACA,mBAAKG,KAAL,CAAY,KAAKF,QAAL,GAAgBD,CAAjB,KAAwB,CAAnC,KAAyC4B,QAAQ,IAAK,KAAM,CAAC,KAAK3B,QAAL,GAAgBD,CAAjB,IAAsB,CAAvB,GAA4B,CAAvF;AACH;AACJ,WAND,MAOK;AACD;AACA,iBAAK;AAAI;AAAiBA,YAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGF,SAAS,CAACG,QAA/C,EAAyDD,CAAC,IAAI,CAA9D,EAAiE;AAC7D,mBAAKG,KAAL,CAAY,KAAKF,QAAL,GAAgBD,CAAjB,KAAwB,CAAnC,IAAwCF,SAAS,CAACK,KAAV,CAAgBH,CAAC,KAAK,CAAtB,CAAxC;AACH;AACJ;;AACD,eAAKC,QAAL,IAAiBH,SAAS,CAACG,QAA3B,CAjBc,CAkBd;;AACA,iBAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI0B,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKxB,KAAL,CAAW,KAAKF,QAAL,KAAkB,CAA7B,KAAmC,cAAe,KAAM,KAAKA,QAAL,GAAgB,CAAjB,GAAsB,CAA7E;AACA,eAAKE,KAAL,CAAWO,MAAX,GAAoBU,IAAI,CAACS,IAAL,CAAU,KAAK5B,QAAL,GAAgB,CAA1B,CAApB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI6B,QAAAA,KAAK,GAAG;AACJ,iBAAO,IAAI9D,SAAJ,CAAc,KAAKmC,KAAL,CAAW4B,KAAX,CAAiB,CAAjB,CAAd,EAAmC,KAAK9B,QAAxC,CAAP;AACH;;AAED8B,QAAAA,KAAK,CAACC,KAAD,EAAQC,GAAR,EAAY;AACb;AACA,eAAK9B,KAAL,GAAa,KAAKA,KAAL,CAAW4B,KAAX,CAAiBC,KAAjB,EAAwBC,GAAxB,CAAb;AACA,eAAKhC,QAAL,GAAgB,KAAKE,KAAL,CAAWO,MAAX,GAAoB,CAApC;AACH;;AApIW,O;AAuIhB;AACA;AACA;AACA;;;yBACMzC,M,GAAN,MAAMA,MAAN,CAAa;AACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACoB,eAAT4B,SAAS,CAACC,SAAD,EAAY;AACxB;AACA;AAAM;AAAiBoC,UAAAA,WAAW,GAAG,EAArC;;AACA,eAAK;AAAI;AAAiBlC,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGF,SAAS,CAACG,QAA/C,EAAyDD,CAAC,EAA1D,EAA8D;AAC1D;AAAM;AAAiBE,YAAAA,IAAI,GAAIJ,SAAS,CAACK,KAAV,CAAgBH,CAAC,KAAK,CAAtB,MAA8B,KAAMA,CAAC,GAAG,CAAL,GAAU,CAA9C,GAAoD,IAAlF;AACAkC,YAAAA,WAAW,CAAC9B,IAAZ,CAAiB+B,MAAM,CAACC,YAAP,CAAoBlC,IAApB,CAAjB;AACH;;AACD,iBAAOgC,WAAW,CAAC5B,IAAZ,CAAiB,EAAjB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,eAALC,KAAK,CAAC8B,SAAD,EAAY;AACpB;AACA;AAAM;AAAiBC,UAAAA,eAAe,GAAGD,SAAS,CAAC3B,MAAnD,CAFoB,CAGpB;;AACA;AAAM;AAAiBP,UAAAA,KAAK,GAAG,EAA/B;;AACA,eAAK;AAAI;AAAiBH,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGsC,eAArC,EAAsDtC,CAAC,EAAvD,EAA2D;AACvDG,YAAAA,KAAK,CAACH,CAAC,KAAK,CAAP,CAAL,IAAkB,CAACqC,SAAS,CAACE,UAAV,CAAqBvC,CAArB,IAA0B,IAA3B,KAAqC,KAAMA,CAAC,GAAG,CAAL,GAAU,CAAtE;AACH;;AACD,iBAAO,IAAIhC,SAAJ,CAAcmC,KAAd,EAAqBmC,eAArB,CAAP;AACH;;AAzCQ,O;AA4Cb;AACA;AACA;AACA;;;yBACMpE,I,GAAN,MAAMA,IAAN,CAAW;AACP;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACoB,eAAT2B,SAAS,CAACC,SAAD,EAAY;AACxB,cAAI;AACA,mBAAO0C,kBAAkB,CAACC,MAAM,CAACxE,MAAM,CAAC4B,SAAP,CAAiBC,SAAjB,CAAD,CAAP,CAAzB;AACH,WAFD,CAGA;AAAO;AAAiB4C,UAAAA,CAAxB,EAA2B;AACvB,kBAAM,IAAIC,KAAJ,CAAU,sBAAV,CAAN;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,eAALpC,KAAK,CAACqC,OAAD,EAAU;AAClB,iBAAO3E,MAAM,CAACsC,KAAP,CAAasC,QAAQ,CAACC,kBAAkB,CAACF,OAAD,CAAnB,CAArB,CAAP;AACH;;AAjCM,O;AAoCX;AACA;AACA;AACA;;AACA;AACA;AACA;;;yBACMzE,sB,GAAN,MAAMA,sBAAN,CAA6B;AACzB;AACJ;AACA;AACIoD,QAAAA,WAAW,CAACwB,GAAD,EAAM;AACb,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKD,GAAL,GAAWE,MAAM,CAACC,MAAP,CAAc;AACrBC,YAAAA,SAAS,EAAE;AADU,WAAd,EAERJ,GAFQ,CAAX,CAFa,CAKb;;AACA,eAAKK,KAAL,GAAa,IAAIpF,SAAJ,EAAb;AACA,eAAKqF,WAAL,GAAmB,CAAnB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKF,KAAL,GAAa,IAAIpF,SAAJ,EAAb;AACA,eAAKqF,WAAL,GAAmB,CAAnB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIE,QAAAA,OAAO,CAACC,IAAD,EAAO;AACV;AACA,cAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;AAC1BA,YAAAA,IAAI,GAAGtF,IAAI,CAACqC,KAAL,CAAWiD,IAAX,CAAP;AACH,WAJS,CAKV;;;AACA,eAAKJ,KAAL,CAAW1B,MAAX,CAAkB8B,IAAlB;;AACA,eAAKH,WAAL,IAAoBG,IAAI,CAACvD,QAAzB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIwD,QAAAA,QAAQ,CAACC,OAAD,EAAU;AACd,cAAI,CAAC,KAAKX,GAAL,CAASI,SAAd,EAAyB;AACrB,kBAAM,IAAIR,KAAJ,CAAU,6BAAV,CAAN;AACH,WAHa,CAId;;;AACA;AAAM;AAAiBgB,UAAAA,cAAc,GAAG,KAAKZ,GAAL,CAASI,SAAT,GAAqB,CAA7D,CALc,CAMd;;AACA;AAAI;AAAiBS,UAAAA,YAAY,GAAG,KAAKR,KAAL,CAAWnD,QAAX,GAAsB0D,cAA1D;;AACA,cAAID,OAAJ,EAAa;AACT;AACAE,YAAAA,YAAY,GAAGxC,IAAI,CAACS,IAAL,CAAU+B,YAAV,CAAf;AACH,WAHD,MAIK;AACD;AACA;AACAA,YAAAA,YAAY,GAAGxC,IAAI,CAACyC,GAAL,CAAS,CAACD,YAAY,GAAG,CAAhB,IAAqB,KAAKZ,cAAnC,EAAmD,CAAnD,CAAf;AACH,WAhBa,CAiBd;;;AACA;AAAM;AAAiBc,UAAAA,WAAW,GAAGF,YAAY,GAAG,KAAKb,GAAL,CAASI,SAA7D,CAlBc,CAmBd;;AACA;AAAM;AAAiBY,UAAAA,WAAW,GAAG3C,IAAI,CAAC4C,GAAL,CAASF,WAAW,GAAG,CAAvB,EAA0B,KAAKV,KAAL,CAAWnD,QAArC,CAArC,CApBc,CAqBd;;AACA;AAAI;AAAiBgE,UAAAA,cAArB;;AACA,cAAIH,WAAJ,EAAiB;AACb,iBAAK;AAAI;AAAiBI,YAAAA,MAAM,GAAG,CAAnC,EAAsCA,MAAM,GAAGJ,WAA/C,EAA4DI,MAAM,IAAI,KAAKnB,GAAL,CAASI,SAA/E,EAA0F;AACtF;AACA,mBAAKgB,eAAL,CAAqB,KAAKf,KAAL,CAAWjD,KAAhC,EAAuC+D,MAAvC;AACH,aAJY,CAKb;;;AACAD,YAAAA,cAAc,GAAG,KAAKb,KAAL,CAAWjD,KAAX,CAAiBiE,MAAjB,CAAwB,CAAxB,EAA2BN,WAA3B,CAAjB;AACA,iBAAKV,KAAL,CAAWnD,QAAX,IAAuB8D,WAAvB;AACH,WA/Ba,CAgCd;;;AACA,iBAAO,IAAI/F,SAAJ,CAAciG,cAAd,EAA8BF,WAA9B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIjC,QAAAA,KAAK,GAAG;AACJ;AAAM;AAAiBA,UAAAA,KAAK,GAAG,KAAKP,WAAL,EAA/B;;AACA,eAAK;AAAM;AAAiB8C,UAAAA,IAA5B,IAAoC,IAApC,EAA0C;AACtC,gBAAI,KAAKC,cAAL,CAAoBD,IAApB,CAAJ,EAA+B;AAC3BvC,cAAAA,KAAK,CAACuC,IAAD,CAAL,GAAc,KAAKA,IAAL,CAAd;AACH;AACJ;;AACDvC,UAAAA,KAAK,CAACsB,KAAN,GAAc,KAAKA,KAAL,CAAWtB,KAAX,EAAd;AACA,iBAAOA,KAAP;AACH;;AAjHwB,O;AAoH7B;AACA;AACA;AACA;;;yBACM1D,I,GAAN,MAAMA,IAAN,CAAW,E;AAGX;AACA;AACA;AACA;;;yBACMC,Y,GAAN,MAAMA,YAAN,SAA2BD,IAA3B,CAAgC;AAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACImD,QAAAA,WAAW,CAACgD,YAAD,EAAe;AACtB;AACA,eAAKC,UAAL,GAAkBD,YAAY,CAACC,UAA/B;AACA,eAAKC,GAAL,GAAWF,YAAY,CAACE,GAAxB;AACA,eAAKC,EAAL,GAAUH,YAAY,CAACG,EAAvB;AACA,eAAKC,IAAL,GAAYJ,YAAY,CAACI,IAAzB;AACA,eAAKC,SAAL,GAAiBL,YAAY,CAACK,SAA9B;AACA,eAAKC,IAAL,GAAYN,YAAY,CAACM,IAAzB;AACA,eAAKC,OAAL,GAAeP,YAAY,CAACO,OAA5B;AACA,eAAK3B,SAAL,GAAiBoB,YAAY,CAACpB,SAA9B;AACA,eAAK4B,SAAL,GAAiBR,YAAY,CAACQ,SAA9B;AACH;AACD;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,gBAAD,EAAmB;AACrB,cAAIA,gBAAgB,CAACT,UAAjB,KAAgChD,SAApC,EAA+C;AAC3C,iBAAKgD,UAAL,GAAkBS,gBAAgB,CAACT,UAAnC;AACH;;AACD,cAAIS,gBAAgB,CAACR,GAAjB,KAAyBjD,SAA7B,EAAwC;AACpC,iBAAKiD,GAAL,GAAWQ,gBAAgB,CAACR,GAA5B;AACH;;AACD,cAAIQ,gBAAgB,CAACP,EAAjB,KAAwBlD,SAA5B,EAAuC;AACnC,iBAAKkD,EAAL,GAAUO,gBAAgB,CAACP,EAA3B;AACH;;AACD,cAAIO,gBAAgB,CAACN,IAAjB,KAA0BnD,SAA9B,EAAyC;AACrC,iBAAKmD,IAAL,GAAYM,gBAAgB,CAACN,IAA7B;AACH;;AACD,cAAIM,gBAAgB,CAACL,SAAjB,KAA+BpD,SAAnC,EAA8C;AAC1C,iBAAKoD,SAAL,GAAiBK,gBAAgB,CAACL,SAAlC;AACH;;AACD,cAAIK,gBAAgB,CAACJ,IAAjB,KAA0BrD,SAA9B,EAAyC;AACrC,iBAAKqD,IAAL,GAAYI,gBAAgB,CAACJ,IAA7B;AACH;;AACD,cAAII,gBAAgB,CAACH,OAAjB,KAA6BtD,SAAjC,EAA4C;AACxC,iBAAKsD,OAAL,GAAeG,gBAAgB,CAACH,OAAhC;AACH;;AACD,cAAIG,gBAAgB,CAAC9B,SAAjB,KAA+B3B,SAAnC,EAA8C;AAC1C,iBAAK2B,SAAL,GAAiB8B,gBAAgB,CAAC9B,SAAlC;AACH;;AACD,cAAI8B,gBAAgB,CAACF,SAAjB,KAA+BvD,SAAnC,EAA8C;AAC1C,iBAAKuD,SAAL,GAAiBE,gBAAgB,CAACF,SAAlC;AACH;;AACD,iBAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI1E,QAAAA,QAAQ,CAAC0E,SAAD,EAAY;AAChB,cAAIA,SAAJ,EAAe;AACX,mBAAOA,SAAS,CAAClF,SAAV,CAAoB,IAApB,CAAP;AACH,WAFD,MAGK,IAAI,KAAKkF,SAAT,EAAoB;AACrB,mBAAO,KAAKA,SAAL,CAAelF,SAAf,CAAyB,IAAzB,CAAP;AACH,WAFI,MAGA;AACD,kBAAM,IAAI8C,KAAJ,CAAU,yEAAV,CAAN;AACH;AACJ;;AA3F2B,O;AA8FhC;AACA;AACA;AACA;;;wBACMrE,M,GAAN,MAAMA,MAAN,CAAa;AACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACoB,eAATuB,SAAS,CAACC,SAAD,EAAY;AACxB;AACAA,UAAAA,SAAS,CAAC6B,KAAV,GAFwB,CAGxB;;AACA;AAAM;AAAiBuD,UAAAA,WAAW,GAAG,EAArC;;AACA,eAAK;AAAI;AAAiBlF,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGF,SAAS,CAACG,QAA/C,EAAyDD,CAAC,IAAI,CAA9D,EAAiE;AAC7D;AAAM;AAAiBmF,YAAAA,KAAK,GAAIrF,SAAS,CAACK,KAAV,CAAgBH,CAAC,KAAK,CAAtB,MAA8B,KAAMA,CAAC,GAAG,CAAL,GAAU,CAA9C,GAAoD,IAAnF;AACA;AAAM;AAAiBoF,YAAAA,KAAK,GAAItF,SAAS,CAACK,KAAV,CAAiBH,CAAC,GAAG,CAAL,KAAY,CAA5B,MAAoC,KAAM,CAACA,CAAC,GAAG,CAAL,IAAU,CAAX,GAAgB,CAA1D,GAAgE,IAA/F;AACA;AAAM;AAAiBqF,YAAAA,KAAK,GAAIvF,SAAS,CAACK,KAAV,CAAiBH,CAAC,GAAG,CAAL,KAAY,CAA5B,MAAoC,KAAM,CAACA,CAAC,GAAG,CAAL,IAAU,CAAX,GAAgB,CAA1D,GAAgE,IAA/F;AACA;AAAM;AAAiBsF,YAAAA,OAAO,GAAIH,KAAK,IAAI,EAAV,GAAiBC,KAAK,IAAI,CAA1B,GAA+BC,KAAhE;;AACA,iBAAK;AAAI;AAAiBE,YAAAA,CAAC,GAAG,CAA9B,EAAkCA,CAAC,GAAG,CAAL,IAAYvF,CAAC,GAAGuF,CAAC,GAAG,IAAR,GAAezF,SAAS,CAACG,QAAtE,EAAiFsF,CAAC,EAAlF,EAAsF;AAClFL,cAAAA,WAAW,CAAC9E,IAAZ,CAAiB,KAAKoF,IAAL,CAAUC,MAAV,CAAkBH,OAAO,KAAM,KAAK,IAAIC,CAAT,CAAd,GAA8B,IAA/C,CAAjB;AACH;AACJ,WAbuB,CAcxB;;;AACA;AAAM;AAAiBG,UAAAA,WAAW,GAAG,KAAKF,IAAL,CAAUC,MAAV,CAAiB,EAAjB,CAArC;;AACA,cAAIC,WAAJ,EAAiB;AACb,mBAAOR,WAAW,CAACxE,MAAZ,GAAqB,CAA5B,EAA+B;AAC3BwE,cAAAA,WAAW,CAAC9E,IAAZ,CAAiBsF,WAAjB;AACH;AACJ;;AACD,iBAAOR,WAAW,CAAC5E,IAAZ,CAAiB,EAAjB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,eAALC,KAAK,CAACoF,SAAD,EAAY;AACpB;AACA;AAAI;AAAiBC,UAAAA,eAAe,GAAGD,SAAS,CAACjF,MAAjD;;AACA,cAAI,KAAKmF,WAAL,KAAqBrE,SAAzB,EAAoC;AAChC,iBAAKqE,WAAL,GAAmB,EAAnB;;AACA,iBAAK;AAAI;AAAiBN,YAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAG,KAAKC,IAAL,CAAU9E,MAA/C,EAAuD6E,CAAC,EAAxD,EAA4D;AACxD,mBAAKM,WAAL,CAAiB,KAAKL,IAAL,CAAUjD,UAAV,CAAqBgD,CAArB,CAAjB,IAA4CA,CAA5C;AACH;AACJ,WARmB,CASpB;;;AACA;AAAM;AAAiBG,UAAAA,WAAW,GAAG,KAAKF,IAAL,CAAUC,MAAV,CAAiB,EAAjB,CAArC;;AACA,cAAIC,WAAJ,EAAiB;AACb;AAAM;AAAiBI,YAAAA,YAAY,GAAGH,SAAS,CAACI,OAAV,CAAkBL,WAAlB,CAAtC;;AACA,gBAAII,YAAY,KAAK,CAAC,CAAtB,EAAyB;AACrBF,cAAAA,eAAe,GAAGE,YAAlB;AACH;AACJ,WAhBmB,CAiBpB;;;AACA,iBAAO,KAAKE,SAAL,CAAeL,SAAf,EAA0BC,eAA1B,EAA2C,KAAKC,WAAhD,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACoB,eAATG,SAAS,CAACL,SAAD,EAAYC,eAAZ,EAA6BK,UAA7B,EAAyC;AACrD;AAAM;AAAiB9F,UAAAA,KAAK,GAAG,EAA/B;AACA;AAAI;AAAiBW,UAAAA,MAAM,GAAG,CAA9B;;AACA,eAAK;AAAI;AAAiBd,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAG4F,eAArC,EAAsD5F,CAAC,EAAvD,EAA2D;AACvD,gBAAIA,CAAC,GAAG,CAAR,EAAW;AACP;AAAM;AAAiBkG,cAAAA,KAAK,GAAGD,UAAU,CAACN,SAAS,CAACpD,UAAV,CAAqBvC,CAAC,GAAG,CAAzB,CAAD,CAAV,IAA6CA,CAAC,GAAG,CAAL,GAAU,CAArF;AACA;AAAM;AAAiBmG,cAAAA,KAAK,GAAGF,UAAU,CAACN,SAAS,CAACpD,UAAV,CAAqBvC,CAArB,CAAD,CAAV,KAAyC,IAAKA,CAAC,GAAG,CAAL,GAAU,CAAtF;AACAG,cAAAA,KAAK,CAACW,MAAM,KAAK,CAAZ,CAAL,IAAuB,CAACoF,KAAK,GAAGC,KAAT,KAAoB,KAAMrF,MAAM,GAAG,CAAV,GAAe,CAA/D;AACAA,cAAAA,MAAM;AACT;AACJ;;AACD,iBAAO,IAAI9C,SAAJ,CAAcmC,KAAd,EAAqBW,MAArB,CAAP;AACH;;AApFQ,O;;AAsFbxC,MAAAA,MAAM,CAACkH,IAAP,GAAc,mEAAd;AACAlH,MAAAA,MAAM,CAACuH,WAAP,GAAqBrE,SAArB;AAEA;AACA;AACA;AACA;;AACMjD,MAAAA,O,GAAN,MAAMA,OAAN,CAAc;AACV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACoB,eAATsB,SAAS,CAAC0E,YAAD,EAAe;AAC3B,cAAI,CAACA,YAAY,CAACC,UAAlB,EAA8B;AAC1B,kBAAM,IAAI7B,KAAJ,CAAU,8BAAV,CAAN;AACH,WAH0B,CAI3B;;;AACA;AAAM;AAAiB6B,UAAAA,UAAU,GAAGD,YAAY,CAACC,UAAjD;AACA;AAAM;AAAiBG,UAAAA,IAAI,GAAGJ,YAAY,CAACI,IAA3C,CAN2B,CAO3B;;AACA;AAAI;AAAiB7E,UAAAA,SAArB;;AACA,cAAI6E,IAAJ,EAAU;AACN,gBAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;AAC1B,oBAAM,IAAIhC,KAAJ,CAAU,oCAAV,CAAN;AACH;;AACD7C,YAAAA,SAAS,GAAI,IAAI9B,SAAJ,CAAc,CAAC,UAAD,EAAa,UAAb,CAAd,CAAD,CAA0C0D,MAA1C,CAAiDiD,IAAjD,EAAuDjD,MAAvD,CAA8D8C,UAA9D,CAAZ;AACH,WALD,MAMK;AACD1E,YAAAA,SAAS,GAAG0E,UAAZ;AACH;;AACD,iBAAO1E,SAAS,CAACO,QAAV,CAAmB/B,MAAnB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,eAALiC,KAAK,CAAC6F,UAAD,EAAa;AACrB;AACA;AAAM;AAAiB5B,UAAAA,UAAU,GAAGlG,MAAM,CAACiC,KAAP,CAAa6F,UAAb,CAApC,CAFqB,CAGrB;;AACA;AAAI;AAAiBzB,UAAAA,IAArB;;AACA,cAAIH,UAAU,CAACrE,KAAX,CAAiB,CAAjB,MAAwB,UAAxB,IAAsCqE,UAAU,CAACrE,KAAX,CAAiB,CAAjB,MAAwB,UAAlE,EAA8E;AAC1E;AACAwE,YAAAA,IAAI,GAAG,IAAI3G,SAAJ,CAAcwG,UAAU,CAACrE,KAAX,CAAiB4B,KAAjB,CAAuB,CAAvB,EAA0B,CAA1B,CAAd,CAAP,CAF0E,CAG1E;;AACAyC,YAAAA,UAAU,CAACrE,KAAX,CAAiBiE,MAAjB,CAAwB,CAAxB,EAA2B,CAA3B;AACAI,YAAAA,UAAU,CAACvE,QAAX,IAAuB,EAAvB;AACH;;AACD,iBAAO,IAAI5B,YAAJ,CAAiB;AAAEmG,YAAAA,UAAU,EAAEA,UAAd;AAA0BG,YAAAA,IAAI,EAAEA;AAAhC,WAAjB,CAAP;AACH;;AAxDS,O;AA2Dd;AACA;AACA;AACA;;yBACMnG,kB,GAAN,MAAMA,kBAAN,CAAyB;AACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACkB,eAAP6H,OAAO,CAACC,MAAD,EAASC,OAAT,EAAkB9B,GAAlB,EAAuB1B,GAAvB,EAA4B;AACtC;AACA;AAAM;AAAiByD,UAAAA,MAAM,GAAGvD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKH,GAAvB,EAA4BA,GAA5B,CAAhC,CAFsC,CAGtC;;AACA;AAAM;AAAiB0D,UAAAA,SAAS,GAAGH,MAAM,CAACI,eAAP,CAAuBjC,GAAvB,EAA4B+B,MAA5B,CAAnC;AACA;AAAM;AAAiBhC,UAAAA,UAAU,GAAGiC,SAAS,CAACE,QAAV,CAAmBJ,OAAnB,CAApC,CALsC,CAMtC;;AACA,iBAAO,IAAIlI,YAAJ,CAAiB;AACpBmG,YAAAA,UAAU,EAAEA,UADQ;AAEpBC,YAAAA,GAAG,EAAEA,GAFe;AAGpBC,YAAAA,EAAE,EAAE+B,SAAS,CAAC1D,GAAV,CAAc2B,EAHE;AAIpBE,YAAAA,SAAS,EAAE0B,MAJS;AAKpBzB,YAAAA,IAAI;AAAG;AAAkB4B,YAAAA,SAAS,CAAC1D,GAA7B,CAAmC8B,IALrB;AAMpBC,YAAAA,OAAO;AAAG;AAAkB2B,YAAAA,SAAS,CAAC1D,GAA7B,CAAmC+B,OANxB;AAOpB3B,YAAAA,SAAS,EAAEsD,SAAS,CAAC1D,GAAV,CAAcI,SAPL;AAQpB4B,YAAAA,SAAS,EAAEyB,MAAM,CAACI;AARE,WAAjB,CAAP;AAUH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACkB,eAAPC,OAAO,CAACP,MAAD,EAAS9B,UAAT,EAAqBC,GAArB,EAA0BqC,WAA1B,EAAuC;AACjD;AACA;AAAM;AAAiB/D,UAAAA,GAAG,GAAGE,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKH,GAAvB,EAA4B+D,WAA5B,CAA7B;;AACA,cAAI,CAAC/D,GAAG,CAAC6D,MAAT,EAAiB;AACb,kBAAM,IAAIjE,KAAJ,CAAU,4BAAV,CAAN;AACH,WALgD,CAMjD;;;AACA6B,UAAAA,UAAU,GAAG,KAAKuC,MAAL,CAAYvC,UAAZ,EAAwBzB,GAAG,CAAC6D,MAA5B,CAAb;;AACA,cAAI,CAACpC,UAAU,CAACA,UAAhB,EAA4B;AACxB,kBAAM,IAAI7B,KAAJ,CAAU,gCAAV,CAAN;AACH,WAVgD,CAWjD;;;AACA;AAAM;AAAiBqE,UAAAA,SAAS,GAAGV,MAAM,CAACW,eAAP,CAAuBxC,GAAvB,EAA4B1B,GAA5B,EAAiC4D,QAAjC,CAA0CnC,UAAU,CAACA,UAArD,CAAnC;AACA,iBAAOwC,SAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACiB,eAAND,MAAM,CAACvC,UAAD,EAAaoC,MAAb,EAAqB;AAC9B,cAAI,OAAOpC,UAAP,KAAsB,QAA1B,EAAoC;AAChC,mBAAOoC,MAAM,CAACrG,KAAP,CAAaiE,UAAb,CAAP;AACH,WAFD,MAGK;AACD,mBAAOA,UAAP;AACH;AACJ;;AAtGoB,O;;AAwGzBhG,MAAAA,kBAAkB,CAACuE,GAAnB,GAAyB;AACrBI,QAAAA,SAAS,EAAE,CADU;AAErBuB,QAAAA,EAAE,EAAE,IAAI1G,SAAJ,CAAc,EAAd,CAFiB;AAGrB4I,QAAAA,MAAM,EAAErI;AAHa,OAAzB;AAMA;AACA;AACA;AACA;;AACA;AACA;AACA;;yBACME,M,GAAN,MAAMA,MAAN,SAAqBN,sBAArB,CAA4C;AACxC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACwB,eAAb+I,aAAa,CAACC,MAAD,EAAS;AACzB;AACR;AACA;AACA;AACA;AACQ,mBAASC,MAAT,CAAgBb,OAAhB,EAAyBxD,GAAzB,EAA8B;AAC1B;AAAM;AAAiBsE,YAAAA,WAAW,GAAGF,MAArC;AACA;AAAM;AAAiBG,YAAAA,cAAc,GAAG,IAAID,WAAJ,CAAgBtE,GAAhB,CAAxC;AACA,mBAAOuE,cAAc,CAACX,QAAf,CAAwBJ,OAAxB,CAAP;AACH;;AACD,iBAAOa,MAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI7F,QAAAA,WAAW,CAACwB,GAAD,EAAM;AACb;AACA,gBAAME,MAAM,CAACC,MAAP,CAAc;AAChBC,YAAAA,SAAS,EAAE,MAAM;AADD,WAAd,EAEHJ,GAFG,CAAN,EAFa,CAKb;;AACA,eAAKO,KAAL;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIiE,QAAAA,MAAM,CAACC,aAAD,EAAgB;AAClB;AACA,eAAKjE,OAAL,CAAaiE,aAAb,EAFkB,CAGlB;;;AACA,eAAK/D,QAAL,GAJkB,CAKlB;;;AACA,iBAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIkD,QAAAA,QAAQ,CAACa,aAAD,EAAgB;AACpB;AACA,cAAIA,aAAJ,EAAmB;AACf,iBAAKjE,OAAL,CAAaiE,aAAb;AACH,WAJmB,CAKpB;;;AACA;AAAM;AAAiBC,UAAAA,IAAI,GAAG,KAAKC,WAAL,EAA9B;;AACA,iBAAOD,IAAP;AACH;;AApFuC,O;AAuF5C;AACA;AACA;AACA;AACA;;;AACuBE,MAAAA,C,GAAI,E,EAC3B;;AACA,WAA0B3H,CAA1B,GAA8B,CAA9B,EAAiCA,CAAC,GAAG,EAArC,EAAyCA,CAAC,EAA1C,EAA8C;AAC1C2H,QAAAA,CAAC,CAAC3H,CAAD,CAAD,GAAQoB,IAAI,CAACwG,GAAL,CAASxG,IAAI,CAACyG,GAAL,CAAS7H,CAAC,GAAG,CAAb,CAAT,IAA4B,WAA7B,GAA4C,CAAnD;AACH;;AACKtB,MAAAA,G,GAAN,MAAMA,GAAN,SAAkBD,MAAlB,CAAyB;AACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACa,eAAFqJ,EAAE,CAACC,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,EAAsB;AAC3B;AAAM;AAAiBC,UAAAA,CAAC,GAAGP,CAAC,IAAKC,CAAC,GAAGC,CAAL,GAAW,CAACD,CAAD,GAAKE,CAApB,CAAD,GAA2BC,CAA3B,GAA+BE,CAA1D;AACA,iBAAO,CAAEC,CAAC,IAAIF,CAAN,GAAYE,CAAC,KAAM,KAAKF,CAAzB,IAAgCJ,CAAvC;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACa,eAAFO,EAAE,CAACR,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,EAAsB;AAC3B;AAAM;AAAiBC,UAAAA,CAAC,GAAGP,CAAC,IAAKC,CAAC,GAAGE,CAAL,GAAWD,CAAC,GAAG,CAACC,CAApB,CAAD,GAA2BC,CAA3B,GAA+BE,CAA1D;AACA,iBAAO,CAAEC,CAAC,IAAIF,CAAN,GAAYE,CAAC,KAAM,KAAKF,CAAzB,IAAgCJ,CAAvC;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACa,eAAFQ,EAAE,CAACT,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,EAAsB;AAC3B;AAAM;AAAiBC,UAAAA,CAAC,GAAGP,CAAC,IAAIC,CAAC,GAAGC,CAAJ,GAAQC,CAAZ,CAAD,GAAkBC,CAAlB,GAAsBE,CAAjD;AACA,iBAAO,CAAEC,CAAC,IAAIF,CAAN,GAAYE,CAAC,KAAM,KAAKF,CAAzB,IAAgCJ,CAAvC;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACa,eAAFS,EAAE,CAACV,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,EAAsB;AAC3B;AAAM;AAAiBC,UAAAA,CAAC,GAAGP,CAAC,IAAIE,CAAC,IAAID,CAAC,GAAG,CAACE,CAAT,CAAL,CAAD,GAAqBC,CAArB,GAAyBE,CAApD;AACA,iBAAO,CAAEC,CAAC,IAAIF,CAAN,GAAYE,CAAC,KAAM,KAAKF,CAAzB,IAAgCJ,CAAvC;AACH;AACD;AACJ;AACA;;;AACI1E,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMA,KAAN;AACA,eAAKoF,KAAL,GAAa,IAAI1K,SAAJ,CAAc,CACvB,UADuB,EACX,UADW,EAEvB,UAFuB,EAEX,UAFW,CAAd,CAAb;AAIH;AACD;AACJ;AACA;AACA;AACA;;;AACImG,QAAAA,eAAe,CAACwE,CAAD,EAAIzE,MAAJ,EAAY;AACvB;AACA,eAAK;AAAI;AAAiBlE,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAG,EAArC,EAAyCA,CAAC,EAA1C,EAA8C;AAC1C;AACA;AAAM;AAAiB4I,YAAAA,QAAQ,GAAG1E,MAAM,GAAGlE,CAA3C;AACA;AAAM;AAAiB6I,YAAAA,UAAU,GAAGF,CAAC,CAACC,QAAD,CAArC;AACAD,YAAAA,CAAC,CAACC,QAAD,CAAD,GAAgB,CAAEC,UAAU,IAAI,CAAf,GAAqBA,UAAU,KAAK,EAArC,IAA4C,UAA7C,GACV,CAAEA,UAAU,IAAI,EAAf,GAAsBA,UAAU,KAAK,CAAtC,IAA4C,UADjD;AAEH,WARsB,CASvB;;;AACA;AAAM;AAAiBC,UAAAA,CAAC,GAAG,KAAKJ,KAAL,CAAWvI,KAAtC;AACA;AAAM;AAAiB4I,UAAAA,UAAU,GAAGJ,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiB8E,UAAAA,UAAU,GAAGL,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiB+E,UAAAA,UAAU,GAAGN,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiBgF,UAAAA,UAAU,GAAGP,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiBiF,UAAAA,UAAU,GAAGR,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiBkF,UAAAA,UAAU,GAAGT,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiBmF,UAAAA,UAAU,GAAGV,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiBoF,UAAAA,UAAU,GAAGX,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiBqF,UAAAA,UAAU,GAAGZ,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiBsF,UAAAA,UAAU,GAAGb,CAAC,CAACzE,MAAM,GAAG,CAAV,CAArC;AACA;AAAM;AAAiBuF,UAAAA,WAAW,GAAGd,CAAC,CAACzE,MAAM,GAAG,EAAV,CAAtC;AACA;AAAM;AAAiBwF,UAAAA,WAAW,GAAGf,CAAC,CAACzE,MAAM,GAAG,EAAV,CAAtC;AACA;AAAM;AAAiByF,UAAAA,WAAW,GAAGhB,CAAC,CAACzE,MAAM,GAAG,EAAV,CAAtC;AACA;AAAM;AAAiB0F,UAAAA,WAAW,GAAGjB,CAAC,CAACzE,MAAM,GAAG,EAAV,CAAtC;AACA;AAAM;AAAiB2F,UAAAA,WAAW,GAAGlB,CAAC,CAACzE,MAAM,GAAG,EAAV,CAAtC;AACA;AAAM;AAAiB4F,UAAAA,WAAW,GAAGnB,CAAC,CAACzE,MAAM,GAAG,EAAV,CAAtC,CA1BuB,CA2BvB;;AACA;AAAI;AAAiB6D,UAAAA,CAAC,GAAGe,CAAC,CAAC,CAAD,CAA1B;AACA;AAAI;AAAiBd,UAAAA,CAAC,GAAGc,CAAC,CAAC,CAAD,CAA1B;AACA;AAAI;AAAiBb,UAAAA,CAAC,GAAGa,CAAC,CAAC,CAAD,CAA1B;AACA;AAAI;AAAiBZ,UAAAA,CAAC,GAAGY,CAAC,CAAC,CAAD,CAA1B,CA/BuB,CAgCvB;;AACAf,UAAAA,CAAC,GAAGrJ,GAAG,CAACoJ,EAAJ,CAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBa,UAAnB,EAA+B,CAA/B,EAAkCpB,CAAC,CAAC,CAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAACoJ,EAAJ,CAAOI,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBe,UAAnB,EAA+B,EAA/B,EAAmCrB,CAAC,CAAC,CAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAACoJ,EAAJ,CAAOG,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBiB,UAAnB,EAA+B,EAA/B,EAAmCtB,CAAC,CAAC,CAAD,CAApC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAACoJ,EAAJ,CAAOE,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBmB,UAAnB,EAA+B,EAA/B,EAAmCvB,CAAC,CAAC,CAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAACoJ,EAAJ,CAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBiB,UAAnB,EAA+B,CAA/B,EAAkCxB,CAAC,CAAC,CAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAACoJ,EAAJ,CAAOI,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBmB,UAAnB,EAA+B,EAA/B,EAAmCzB,CAAC,CAAC,CAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAACoJ,EAAJ,CAAOG,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBqB,UAAnB,EAA+B,EAA/B,EAAmC1B,CAAC,CAAC,CAAD,CAApC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAACoJ,EAAJ,CAAOE,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBuB,UAAnB,EAA+B,EAA/B,EAAmC3B,CAAC,CAAC,CAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAACoJ,EAAJ,CAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBqB,UAAnB,EAA+B,CAA/B,EAAkC5B,CAAC,CAAC,CAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAACoJ,EAAJ,CAAOI,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBuB,UAAnB,EAA+B,EAA/B,EAAmC7B,CAAC,CAAC,CAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAACoJ,EAAJ,CAAOG,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmByB,WAAnB,EAAgC,EAAhC,EAAoC9B,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAACoJ,EAAJ,CAAOE,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmB2B,WAAnB,EAAgC,EAAhC,EAAoC/B,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAACoJ,EAAJ,CAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmByB,WAAnB,EAAgC,CAAhC,EAAmChC,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAACoJ,EAAJ,CAAOI,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmB2B,WAAnB,EAAgC,EAAhC,EAAoCjC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAACoJ,EAAJ,CAAOG,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmB6B,WAAnB,EAAgC,EAAhC,EAAoClC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAACoJ,EAAJ,CAAOE,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmB+B,WAAnB,EAAgC,EAAhC,EAAoCnC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC6J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBc,UAAnB,EAA+B,CAA/B,EAAkCrB,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC6J,EAAJ,CAAOL,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBoB,UAAnB,EAA+B,CAA/B,EAAkC1B,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC6J,EAAJ,CAAON,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmB0B,WAAnB,EAAgC,EAAhC,EAAoC/B,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC6J,EAAJ,CAAOP,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBgB,UAAnB,EAA+B,EAA/B,EAAmCpB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC6J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBkB,UAAnB,EAA+B,CAA/B,EAAkCzB,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC6J,EAAJ,CAAOL,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBwB,WAAnB,EAAgC,CAAhC,EAAmC9B,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC6J,EAAJ,CAAON,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmB8B,WAAnB,EAAgC,EAAhC,EAAoCnC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC6J,EAAJ,CAAOP,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBoB,UAAnB,EAA+B,EAA/B,EAAmCxB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC6J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBsB,UAAnB,EAA+B,CAA/B,EAAkC7B,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC6J,EAAJ,CAAOL,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmB4B,WAAnB,EAAgC,CAAhC,EAAmClC,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC6J,EAAJ,CAAON,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBkB,UAAnB,EAA+B,EAA/B,EAAmCvB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC6J,EAAJ,CAAOP,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBwB,UAAnB,EAA+B,EAA/B,EAAmC5B,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC6J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmB0B,WAAnB,EAAgC,CAAhC,EAAmCjC,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC6J,EAAJ,CAAOL,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBgB,UAAnB,EAA+B,CAA/B,EAAkCtB,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC6J,EAAJ,CAAON,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBsB,UAAnB,EAA+B,EAA/B,EAAmC3B,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC6J,EAAJ,CAAOP,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmB4B,WAAnB,EAAgC,EAAhC,EAAoChC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC8J,EAAJ,CAAOT,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBkB,UAAnB,EAA+B,CAA/B,EAAkCzB,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC8J,EAAJ,CAAON,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBsB,UAAnB,EAA+B,EAA/B,EAAmC5B,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC8J,EAAJ,CAAOP,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmB0B,WAAnB,EAAgC,EAAhC,EAAoC/B,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC8J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmB8B,WAAnB,EAAgC,EAAhC,EAAoClC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC8J,EAAJ,CAAOT,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBc,UAAnB,EAA+B,CAA/B,EAAkCrB,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC8J,EAAJ,CAAON,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBkB,UAAnB,EAA+B,EAA/B,EAAmCxB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC8J,EAAJ,CAAOP,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBsB,UAAnB,EAA+B,EAA/B,EAAmC3B,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC8J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmB0B,WAAnB,EAAgC,EAAhC,EAAoC9B,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC8J,EAAJ,CAAOT,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmB0B,WAAnB,EAAgC,CAAhC,EAAmCjC,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC8J,EAAJ,CAAON,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBc,UAAnB,EAA+B,EAA/B,EAAmCpB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC8J,EAAJ,CAAOP,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBkB,UAAnB,EAA+B,EAA/B,EAAmCvB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC8J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBsB,UAAnB,EAA+B,EAA/B,EAAmC1B,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC8J,EAAJ,CAAOT,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBsB,UAAnB,EAA+B,CAA/B,EAAkC7B,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC8J,EAAJ,CAAON,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmB0B,WAAnB,EAAgC,EAAhC,EAAoChC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC8J,EAAJ,CAAOP,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmB8B,WAAnB,EAAgC,EAAhC,EAAoCnC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC8J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBkB,UAAnB,EAA+B,EAA/B,EAAmCtB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC+J,EAAJ,CAAOV,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBa,UAAnB,EAA+B,CAA/B,EAAkCpB,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC+J,EAAJ,CAAOP,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBqB,UAAnB,EAA+B,EAA/B,EAAmC3B,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC+J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmB6B,WAAnB,EAAgC,EAAhC,EAAoClC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC+J,EAAJ,CAAOT,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBqB,UAAnB,EAA+B,EAA/B,EAAmCzB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC+J,EAAJ,CAAOV,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmByB,WAAnB,EAAgC,CAAhC,EAAmChC,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC+J,EAAJ,CAAOP,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBiB,UAAnB,EAA+B,EAA/B,EAAmCvB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC+J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmByB,WAAnB,EAAgC,EAAhC,EAAoC9B,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC+J,EAAJ,CAAOT,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmBiB,UAAnB,EAA+B,EAA/B,EAAmCrB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC+J,EAAJ,CAAOV,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBqB,UAAnB,EAA+B,CAA/B,EAAkC5B,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC+J,EAAJ,CAAOP,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmB6B,WAAnB,EAAgC,EAAhC,EAAoCnC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC+J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBqB,UAAnB,EAA+B,EAA/B,EAAmC1B,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC+J,EAAJ,CAAOT,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmB6B,WAAnB,EAAgC,EAAhC,EAAoCjC,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAI,UAAAA,CAAC,GAAGrJ,GAAG,CAAC+J,EAAJ,CAAOV,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBiB,UAAnB,EAA+B,CAA/B,EAAkCxB,CAAC,CAAC,EAAD,CAAnC,CAAJ;AACAO,UAAAA,CAAC,GAAGxJ,GAAG,CAAC+J,EAAJ,CAAOP,CAAP,EAAUH,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmByB,WAAnB,EAAgC,EAAhC,EAAoC/B,CAAC,CAAC,EAAD,CAArC,CAAJ;AACAM,UAAAA,CAAC,GAAGvJ,GAAG,CAAC+J,EAAJ,CAAOR,CAAP,EAAUC,CAAV,EAAaH,CAAb,EAAgBC,CAAhB,EAAmBiB,UAAnB,EAA+B,EAA/B,EAAmCtB,CAAC,CAAC,EAAD,CAApC,CAAJ;AACAK,UAAAA,CAAC,GAAGtJ,GAAG,CAAC+J,EAAJ,CAAOT,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBH,CAAhB,EAAmByB,UAAnB,EAA+B,EAA/B,EAAmC7B,CAAC,CAAC,EAAD,CAApC,CAAJ,CAhGuB,CAiGvB;;AACAmB,UAAAA,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOf,CAAR,GAAa,CAApB;AACAe,UAAAA,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOd,CAAR,GAAa,CAApB;AACAc,UAAAA,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOb,CAAR,GAAa,CAApB;AACAa,UAAAA,CAAC,CAAC,CAAD,CAAD,GAAQA,CAAC,CAAC,CAAD,CAAD,GAAOZ,CAAR,GAAa,CAApB;AACH;AACD;AACJ;AACA;;;AACIR,QAAAA,WAAW,GAAG;AACV;AACA;AAAM;AAAiBlE,UAAAA,IAAI,GAAG,KAAKJ,KAAnC;AACA;AAAM;AAAiB2G,UAAAA,SAAS,GAAGvG,IAAI,CAACrD,KAAxC;AACA;AAAM;AAAiB6J,UAAAA,UAAU,GAAG,KAAK3G,WAAL,GAAmB,CAAvD;AACA;AAAM;AAAiB4G,UAAAA,SAAS,GAAGzG,IAAI,CAACvD,QAAL,GAAgB,CAAnD,CALU,CAMV;;AACA8J,UAAAA,SAAS,CAACE,SAAS,KAAK,CAAf,CAAT,IAA8B,QAAS,KAAKA,SAAS,GAAG,EAAxD;AACA;AAAM;AAAiBC,UAAAA,WAAW,GAAG9I,IAAI,CAAC+I,KAAL,CAAWH,UAAU,GAAG,WAAxB,CAArC;AACA;AAAM;AAAiBI,UAAAA,WAAW,GAAGJ,UAArC;AACAD,UAAAA,SAAS,CAAC,CAAGE,SAAS,GAAG,EAAb,KAAqB,CAAtB,IAA4B,CAA7B,IAAkC,EAAnC,CAAT,GAAoD,CAAEC,WAAW,IAAI,CAAhB,GAAsBA,WAAW,KAAK,EAAvC,IAA8C,UAA/C,GAC9C,CAAEA,WAAW,IAAI,EAAhB,GAAuBA,WAAW,KAAK,CAAxC,IAA8C,UADnD;AAEAH,UAAAA,SAAS,CAAC,CAAGE,SAAS,GAAG,EAAb,KAAqB,CAAtB,IAA4B,CAA7B,IAAkC,EAAnC,CAAT,GAAoD,CAAEG,WAAW,IAAI,CAAhB,GAAsBA,WAAW,KAAK,EAAvC,IAA8C,UAA/C,GAC9C,CAAEA,WAAW,IAAI,EAAhB,GAAuBA,WAAW,KAAK,CAAxC,IAA8C,UADnD;AAEA5G,UAAAA,IAAI,CAACvD,QAAL,GAAgB,CAAC8J,SAAS,CAACrJ,MAAV,GAAmB,CAApB,IAAyB,CAAzC,CAdU,CAeV;;AACA,eAAK+C,QAAL,GAhBU,CAiBV;;;AACA;AAAM;AAAiBgE,UAAAA,IAAI,GAAG,KAAKiB,KAAnC;AACA;AAAM;AAAiBI,UAAAA,CAAC,GAAGrB,IAAI,CAACtH,KAAhC,CAnBU,CAoBV;;AACA,eAAK;AAAI;AAAiBH,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAG,CAArC,EAAwCA,CAAC,EAAzC,EAA6C;AACzC;AACA;AAAM;AAAiBqK,YAAAA,GAAG,GAAGvB,CAAC,CAAC9I,CAAD,CAA9B;AACA8I,YAAAA,CAAC,CAAC9I,CAAD,CAAD,GAAQ,CAAEqK,GAAG,IAAI,CAAR,GAAcA,GAAG,KAAK,EAAvB,IAA8B,UAA/B,GACF,CAAEA,GAAG,IAAI,EAAR,GAAeA,GAAG,KAAK,CAAxB,IAA8B,UADnC;AAEH,WA1BS,CA2BV;;;AACA,iBAAO5C,IAAP;AACH;;AAhNoB,O;AAmNzB;AACA;AACA;AACA;;AACM9I,MAAAA,M,GAAN,MAAMA,MAAN,CAAa;AACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI4C,QAAAA,WAAW,CAACwB,GAAD,EAAM;AACb,eAAKA,GAAL,GAAWE,MAAM,CAACC,MAAP,CAAc;AACrBoH,YAAAA,OAAO,EAAE,MAAM,EADM;AAErBnD,YAAAA,MAAM,EAAEzI,GAFa;AAGrB6L,YAAAA,UAAU,EAAE;AAHS,WAAd,EAIRxH,GAJQ,CAAX;AAKH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIyH,QAAAA,OAAO,CAACC,QAAD,EAAW9F,IAAX,EAAiB;AACpB;AACA;AAAM;AAAiBwC,UAAAA,MAAM,GAAG;AAAK;AAAkB,eAAKpE,GAAL,CAASoE,MAAhC,EAAhC,CAFoB,CAGpB;;AACA;AAAM;AAAiBuD,UAAAA,UAAU,GAAG,IAAI1M,SAAJ,EAApC,CAJoB,CAKpB;;AACA;AAAI;AAAiB2M,UAAAA,KAArB;;AACA,iBAAOD,UAAU,CAACvK,KAAX,CAAiBO,MAAjB,GAA0B,KAAKqC,GAAL,CAASuH,OAA1C,EAAmD;AAC/C,gBAAIK,KAAJ,EAAW;AACPxD,cAAAA,MAAM,CAACI,MAAP,CAAcoD,KAAd;AACH;;AACDA,YAAAA,KAAK,GAAGxD,MAAM,CAACI,MAAP,CAAckD,QAAd,EAAwB9D,QAAxB,CAAiChC,IAAjC,CAAR;AACAwC,YAAAA,MAAM,CAAC7D,KAAP,GAL+C,CAM/C;;AACA,iBAAK;AAAI;AAAiBtD,YAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAG,KAAK+C,GAAL,CAASwH,UAA9C,EAA0DvK,CAAC,EAA3D,EAA+D;AAC3D2K,cAAAA,KAAK,GAAGxD,MAAM,CAACR,QAAP,CAAgBgE,KAAhB,CAAR;AACAxD,cAAAA,MAAM,CAAC7D,KAAP;AACH;;AACDoH,YAAAA,UAAU,CAAChJ,MAAX,CAAkBiJ,KAAlB;AACH;;AACDD,UAAAA,UAAU,CAACzK,QAAX,GAAsB,KAAK8C,GAAL,CAASuH,OAAT,GAAmB,CAAzC;AACA,iBAAOI,UAAP;AACH;;AArDQ,O;AAwDb;AACA;AACA;AACA;;AACM9L,MAAAA,U,GAAN,MAAMA,UAAN,CAAiB;AACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACkB,eAAPgM,OAAO,CAACH,QAAD,EAAWH,OAAX,EAAoBO,MAApB,EAA4BlG,IAA5B,EAAkC;AAC5C;AACA,cAAI,CAACA,IAAL,EAAW;AACPA,YAAAA,IAAI,GAAG3G,SAAS,CAAC6C,MAAV,CAAiB,KAAK,CAAtB,CAAP;AACH,WAJ2C,CAK5C;;;AACA;AAAM;AAAiB4D,UAAAA,GAAG,GAAI,IAAI9F,MAAJ,CAAW;AAAE2L,YAAAA,OAAO,EAAEA,OAAO,GAAGO;AAArB,WAAX,CAAD,CAA4CL,OAA5C,CAAoDC,QAApD,EAA8D9F,IAA9D,CAA7B,CAN4C,CAO5C;;AACA;AAAM;AAAiBD,UAAAA,EAAE,GAAG,IAAI1G,SAAJ,CAAcyG,GAAG,CAACtE,KAAJ,CAAU4B,KAAV,CAAgBuI,OAAhB,CAAd,EAAwCO,MAAM,GAAG,CAAjD,CAA5B;AACApG,UAAAA,GAAG,CAACxE,QAAJ,GAAeqK,OAAO,GAAG,CAAzB,CAT4C,CAU5C;;AACA,iBAAO,IAAIjM,YAAJ,CAAiB;AAAEoG,YAAAA,GAAG,EAAEA,GAAP;AAAYC,YAAAA,EAAE,EAAEA,EAAhB;AAAoBC,YAAAA,IAAI,EAAEA;AAA1B,WAAjB,CAAP;AACH;;AA5BY,O;AA+BjB;AACA;AACA;AACA;;yBACM9F,mB,GAAN,MAAMA,mBAAN,CAA0B;AACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACkB,eAAPwH,OAAO,CAACC,MAAD,EAASC,OAAT,EAAkBkE,QAAlB,EAA4B1H,GAA5B,EAAiC;AAC3C;AACA;AAAM;AAAiByD,UAAAA,MAAM,GAAGvD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKH,GAAvB,EAA4BA,GAA5B,CAAhC,CAF2C,CAG3C;;AACA,cAAIyD,MAAM,CAACsE,GAAP,KAAetJ,SAAnB,EAA8B;AAC1B,kBAAM,IAAImB,KAAJ,CAAU,uBAAV,CAAN;AACH,WAN0C,CAO3C;;;AACA;AAAM;AAAiBoI,UAAAA,aAAa,GAAGvE,MAAM,CAACsE,GAAP,CAAWF,OAAX,CAAmBH,QAAnB,EAA6BnE,MAAM,CAACgE,OAApC,EAA6ChE,MAAM,CAACuE,MAApD,CAAvC,CAR2C,CAS3C;;AACA,cAAIE,aAAa,CAACrG,EAAd,KAAqBlD,SAAzB,EAAoC;AAChC;AACAgF,YAAAA,MAAM,CAAC9B,EAAP,GAAYqG,aAAa,CAACrG,EAA1B;AACH,WAb0C,CAc3C;;;AACA;AAAM;AAAiBF,UAAAA,UAAU,GAAGhG,kBAAkB,CAAC6H,OAAnB,CAA2B2E,IAA3B,CAAgC,IAAhC,EAAsC1E,MAAtC,EAA8CC,OAA9C,EAAuDwE,aAAa,CAACtG,GAArE,EAA0E+B,MAA1E,CAApC,CAf2C,CAgB3C;;AACA,iBAAOhC,UAAU,CAACQ,MAAX,CAAkB+F,aAAlB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACkB,eAAPlE,OAAO,CAACP,MAAD,EAAS9B,UAAT,EAAqBiG,QAArB,EAA+B1H,GAA/B,EAAoC;AAC9C;AACA;AAAM;AAAiByD,UAAAA,MAAM,GAAGvD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKH,GAAvB,EAA4BA,GAA5B,CAAhC,CAF8C,CAG9C;;AACA,cAAIyD,MAAM,CAACI,MAAP,KAAkBpF,SAAtB,EAAiC;AAC7B,kBAAM,IAAImB,KAAJ,CAAU,0BAAV,CAAN;AACH,WAN6C,CAO9C;;;AACA6B,UAAAA,UAAU,GAAG,KAAKuC,MAAL,CAAYvC,UAAZ,EAAwBgC,MAAM,CAACI,MAA/B,CAAb,CAR8C,CAS9C;;AACA,cAAIJ,MAAM,CAACsE,GAAP,KAAetJ,SAAnB,EAA8B;AAC1B,kBAAM,IAAImB,KAAJ,CAAU,yCAAV,CAAN;AACH,WAZ6C,CAa9C;;;AACA;AAAM;AAAiBoI,UAAAA,aAAa,GAAGvE,MAAM,CAACsE,GAAP,CAAWF,OAAX,CAAmBH,QAAnB,EAA6BnE,MAAM,CAACgE,OAApC,EAA6ChE,MAAM,CAACuE,MAApD,EAA4DrG,UAAU,CAACG,IAAvE,CAAvC,CAd8C,CAe9C;;AACA,cAAIoG,aAAa,CAACrG,EAAd,KAAqBlD,SAAzB,EAAoC;AAChC;AACAgF,YAAAA,MAAM,CAAC9B,EAAP,GAAYqG,aAAa,CAACrG,EAA1B;AACH,WAnB6C,CAoB9C;;;AACA;AAAM;AAAiBsC,UAAAA,SAAS,GAAGxI,kBAAkB,CAACqI,OAAnB,CAA2BmE,IAA3B,CAAgC,IAAhC,EAAsC1E,MAAtC,EAA8C9B,UAA9C,EAA0DuG,aAAa,CAACtG,GAAxE,EAA6E+B,MAA7E,CAAnC;AACA,iBAAOQ,SAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACiB,eAAND,MAAM,CAACvC,UAAD,EAAaoC,MAAb,EAAqB;AAC9B,cAAI,OAAOpC,UAAP,KAAsB,QAA1B,EAAoC;AAChC,mBAAOoC,MAAM,CAACrG,KAAP,CAAaiE,UAAb,CAAP;AACH,WAFD,MAGK;AACD,mBAAOA,UAAP;AACH;AACJ;;AA9FqB,O;;AAgG1B3F,MAAAA,mBAAmB,CAACkE,GAApB,GAA0B;AACtBI,QAAAA,SAAS,EAAE,CADW;AAEtBuB,QAAAA,EAAE,EAAE,IAAI1G,SAAJ,CAAc,EAAd,CAFkB;AAGtB4I,QAAAA,MAAM,EAAErI,OAHc;AAItBuM,QAAAA,GAAG,EAAElM;AAJiB,OAA1B;AAOA;AACA;AACA;AACA;;AACA;AACA;AACA;;yBACME,M,GAAN,MAAMA,MAAN,SAAqBX,sBAArB,CAA4C;AACxC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIoD,QAAAA,WAAW,CAAC0J,SAAD,EAAYxG,GAAZ,EAAiB1B,GAAjB,EAAsB;AAC7B;AACA,gBAAME,MAAM,CAACC,MAAP,CAAc;AAChBC,YAAAA,SAAS,EAAE;AADK,WAAd,EAEHJ,GAFG,CAAN,EAF6B,CAK7B;;AACA,eAAKmI,UAAL,GAAkBD,SAAlB;AACA,eAAKE,IAAL,GAAY1G,GAAZ,CAP6B,CAQ7B;;AACA,eAAKnB,KAAL;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC0B,eAAfoD,eAAe,CAACjC,GAAD,EAAM1B,GAAN,EAAW;AAC7B;AACA;AAAM;AAAiBqI,UAAAA,SAAS,GAAG,IAAnC;AACA,iBAAO,IAAIA,SAAJ,CAAc,KAAKC,eAAnB,EAAoC5G,GAApC,EAAyC1B,GAAzC,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC0B,eAAfkE,eAAe,CAACxC,GAAD,EAAM1B,GAAN,EAAW;AAC7B;AACA;AAAM;AAAiBqI,UAAAA,SAAS,GAAG,IAAnC;AACA,iBAAO,IAAIA,SAAJ,CAAc,KAAKE,eAAnB,EAAoC7G,GAApC,EAAyC1B,GAAzC,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACwB,eAAbmE,aAAa,CAACZ,MAAD,EAAS;AACzB;AACR;AACA;AACA;AACA;AACA;AACQ,mBAASD,OAAT,CAAiBE,OAAjB,EAA0B9B,GAA1B,EAA+B1B,GAA/B,EAAoC;AAChC,gBAAI,OAAO0B,GAAP,KAAe,QAAnB,EAA6B;AACzB,qBAAO5F,mBAAmB,CAACwH,OAApB,CAA4BC,MAA5B,EAAoCC,OAApC,EAA6C9B,GAA7C,EAAkD1B,GAAlD,CAAP;AACH,aAFD,MAGK;AACD,qBAAOvE,kBAAkB,CAAC6H,OAAnB,CAA2BC,MAA3B,EAAmCC,OAAnC,EAA4C9B,GAA5C,EAAiD1B,GAAjD,CAAP;AACH;AACJ;AACD;AACR;AACA;AACA;AACA;AACA;;;AACQ,mBAAS8D,OAAT,CAAiBrC,UAAjB,EAA6BC,GAA7B,EAAkC1B,GAAlC,EAAuC;AACnC,gBAAI,OAAO0B,GAAP,KAAe,QAAnB,EAA6B;AACzB,qBAAO5F,mBAAmB,CAACgI,OAApB,CAA4BP,MAA5B,EAAoC9B,UAApC,EAAgDC,GAAhD,EAAqD1B,GAArD,CAAP;AACH,aAFD,MAGK;AACD,qBAAOvE,kBAAkB,CAACqI,OAAnB,CAA2BP,MAA3B,EAAmC9B,UAAnC,EAA+CC,GAA/C,EAAoD1B,GAApD,CAAP;AACH;AACJ;;AACD,iBAAO;AACHsD,YAAAA,OAAO,EAAEA,OADN;AAEHQ,YAAAA,OAAO,EAAEA;AAFN,WAAP;AAIH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI0E,QAAAA,OAAO,CAACC,UAAD,EAAa;AAChB;AACA,eAAKjI,OAAL,CAAaiI,UAAb,EAFgB,CAGhB;;;AACA,iBAAO,KAAK/H,QAAL,EAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIkD,QAAAA,QAAQ,CAAC6E,UAAD,EAAa;AACjB;AACA,cAAIA,UAAJ,EAAgB;AACZ,iBAAKjI,OAAL,CAAaiI,UAAb;AACH,WAJgB,CAKjB;;;AACA;AAAM;AAAiBC,UAAAA,kBAAkB,GAAG,KAAK/D,WAAL,EAA5C;;AACA,iBAAO+D,kBAAP;AACH;;AA9IuC,O;AAgJ5C;AACA;AACA;;;AACA3M,MAAAA,MAAM,CAACuM,eAAP,GAAyB,CAAzB;AACA;AACA;AACA;;AACAvM,MAAAA,MAAM,CAACwM,eAAP,GAAyB,CAAzB;AACA;AACA;AACA;;AACAxM,MAAAA,MAAM,CAACwL,OAAP,GAAiB,CAAjB;AACA;AACA;AACA;;AACAxL,MAAAA,MAAM,CAAC+L,MAAP,GAAgB,CAAhB;AAEA;AACA;AACA;AACA;;AACA;AACA;AACA;;AACM9L,MAAAA,wB,GAAN,MAAMA,wBAAN,CAA+B;AAC3B;AACJ;AACA;AACA;AACIwC,QAAAA,WAAW,CAAC+E,MAAD,EAAS5B,EAAT,EAAa;AACpB,eAAKgH,IAAL,CAAUpF,MAAV,EAAkB5B,EAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIgH,QAAAA,IAAI,CAACpF,MAAD,EAAS5B,EAAT,EAAa;AACb,eAAKiH,OAAL,GAAerF,MAAf;AACA,eAAKsF,GAAL,GAAWlH,EAAX;AACH;;AAtB0B,O;AAyB/B;AACA;AACA;AACA;;AACA;AACA;AACA;;yBACM1F,e,GAAN,MAAMA,eAAN,CAAsB;AAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAC0B,eAAf0H,eAAe,CAACJ,MAAD,EAAS5B,EAAT,EAAa;AAC/B;AACA;AAAM;AAAiBmH,UAAAA,cAAc,GAAG,KAAKC,SAA7C;AACA,iBAAO,IAAID,cAAJ,CAAmBvF,MAAnB,EAA2B5B,EAA3B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC0B,eAAfuC,eAAe,CAACX,MAAD,EAAS5B,EAAT,EAAa;AAC/B;AACA;AAAM;AAAiBqH,UAAAA,cAAc,GAAG,KAAKC,SAA7C;AACA,iBAAO,IAAID,cAAJ,CAAmBzF,MAAnB,EAA2B5B,EAA3B,CAAP;AACH;;AAhCiB,O;;AAkCtB1F,MAAAA,eAAe,CAAC8M,SAAhB,GAA4B/M,wBAA5B;AACAC,MAAAA,eAAe,CAACgN,SAAhB,GAA4BjN,wBAA5B;AAEA;AACA;AACA;AACA;;AACME,MAAAA,Y,GAAN,MAAMA,YAAN,SAA2BF,wBAA3B,CAAoD;AAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIkN,QAAAA,YAAY,CAAC9L,KAAD,EAAQ+D,MAAR,EAAgB;AACxB;AACA,cAAI,KAAKyH,OAAL,CAAa5I,GAAb,CAAiBI,SAAjB,KAA+B3B,SAAnC,EAA8C;AAC1C,kBAAM,IAAImB,KAAJ,CAAU,oCAAV,CAAN;AACH,WAJuB,CAKxB;;;AACA,eAAKuJ,QAAL,CAAc/L,KAAd,EAAqB+D,MAArB,EAA6B,KAAKyH,OAAL,CAAa5I,GAAb,CAAiBI,SAA9C;;AACA,eAAKwI,OAAL,CAAaQ,YAAb,CAA0BhM,KAA1B,EAAiC+D,MAAjC,EAPwB,CAQxB;;;AACA,eAAKkI,UAAL,GAAkBjM,KAAK,CAAC4B,KAAN,CAAYmC,MAAZ,EAAoBA,MAAM,GAAG,KAAKyH,OAAL,CAAa5I,GAAb,CAAiBI,SAA9C,CAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI+I,QAAAA,QAAQ,CAAC/L,KAAD,EAAQ+D,MAAR,EAAgBf,SAAhB,EAA2B;AAC/B;AACA;AAAI;AAAiBwH,UAAAA,KAArB;;AACA,cAAI,KAAKiB,GAAT,EAAc;AACVjB,YAAAA,KAAK,GAAG,KAAKiB,GAAb,CADU,CAEV;;AACA,iBAAKA,GAAL,GAAWpK,SAAX;AACH,WAJD,MAKK;AACDmJ,YAAAA,KAAK,GAAG,KAAKyB,UAAb;AACH,WAV8B,CAW/B;;;AACA,cAAIzB,KAAK,KAAKnJ,SAAd,EAAyB;AACrB;AACA,iBAAK;AAAI;AAAiBxB,YAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGmD,SAArC,EAAgDnD,CAAC,EAAjD,EAAqD;AACjDG,cAAAA,KAAK,CAAC+D,MAAM,GAAGlE,CAAV,CAAL,IAAqB2K,KAAK,CAAC3K,CAAD,CAA1B;AACH;AACJ;AACJ;;AA/C+C,O;AAkDpD;AACA;AACA;AACA;;AACMd,MAAAA,Y,GAAN,MAAMA,YAAN,SAA2BH,wBAA3B,CAAoD;AAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIkN,QAAAA,YAAY,CAAC9L,KAAD,EAAQ+D,MAAR,EAAgB;AACxB;AACA,cAAI,KAAKyH,OAAL,CAAa5I,GAAb,CAAiBI,SAAjB,KAA+B3B,SAAnC,EAA8C;AAC1C,kBAAM,IAAImB,KAAJ,CAAU,oCAAV,CAAN;AACH,WAJuB,CAKxB;;;AACA;AAAM;AAAiB0J,UAAAA,SAAS,GAAGlM,KAAK,CAAC4B,KAAN,CAAYmC,MAAZ,EAAoBA,MAAM,GAAG,KAAKyH,OAAL,CAAa5I,GAAb,CAAiBI,SAA9C,CAAnC,CANwB,CAOxB;;AACA,eAAKwI,OAAL,CAAaW,YAAb,CAA0BnM,KAA1B,EAAiC+D,MAAjC;;AACA,eAAKgI,QAAL,CAAc/L,KAAd,EAAqB+D,MAArB,EAA6B,KAAKyH,OAAL,CAAa5I,GAAb,CAAiBI,SAA9C,EATwB,CAUxB;;AACA,eAAKiJ,UAAL,GAAkBC,SAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIH,QAAAA,QAAQ,CAAC/L,KAAD,EAAQ+D,MAAR,EAAgBf,SAAhB,EAA2B;AAC/B;AACA;AAAI;AAAiBwH,UAAAA,KAArB;;AACA,cAAI,KAAKiB,GAAT,EAAc;AACVjB,YAAAA,KAAK,GAAG,KAAKiB,GAAb,CADU,CAEV;;AACA,iBAAKA,GAAL,GAAWpK,SAAX;AACH,WAJD,MAKK;AACDmJ,YAAAA,KAAK,GAAG,KAAKyB,UAAb;AACH,WAV8B,CAW/B;;;AACA,cAAIzB,KAAK,KAAKnJ,SAAd,EAAyB;AACrB;AACA,iBAAK;AAAI;AAAiBxB,YAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGmD,SAArC,EAAgDnD,CAAC,EAAjD,EAAqD;AACjDG,cAAAA,KAAK,CAAC+D,MAAM,GAAGlE,CAAV,CAAL,IAAqB2K,KAAK,CAAC3K,CAAD,CAA1B;AACH;AACJ;AACJ;;AAjD+C,O;AAoDpD;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;yBACMb,G,GAAN,MAAMA,GAAN,SAAkBH,eAAlB,CAAkC,E;;AAElCG,MAAAA,GAAG,CAAC2M,SAAJ,GAAgB7M,YAAhB;AACAE,MAAAA,GAAG,CAAC6M,SAAJ,GAAgB9M,YAAhB;AAEA;AACA;AACA;AACA;;yBACME,K,GAAN,MAAMA,KAAN,CAAY;AACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACc,eAAHmN,GAAG,CAAC/I,IAAD,EAAOL,SAAP,EAAkB;AACxB;AACA;AAAM;AAAiBQ,UAAAA,cAAc,GAAGR,SAAS,GAAG,CAApD,CAFwB,CAGxB;;AACA;AAAM;AAAiBqJ,UAAAA,aAAa,GAAG7I,cAAc,GAAGH,IAAI,CAACvD,QAAL,GAAgB0D,cAAxE,CAJwB,CAKxB;;AACA;AAAM;AAAiB8I,UAAAA,WAAW,GAAID,aAAa,IAAI,EAAlB,GAAyBA,aAAa,IAAI,EAA1C,GAAiDA,aAAa,IAAI,CAAlE,GAAuEA,aAA5G,CANwB,CAOxB;;AACA;AAAM;AAAiBE,UAAAA,YAAY,GAAG,EAAtC;;AACA,eAAK;AAAI;AAAiB1M,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGwM,aAArC,EAAoDxM,CAAC,IAAI,CAAzD,EAA4D;AACxD0M,YAAAA,YAAY,CAACtM,IAAb,CAAkBqM,WAAlB;AACH;;AACD;AAAM;AAAiB3H,UAAAA,OAAO,GAAG,IAAI9G,SAAJ,CAAc0O,YAAd,EAA4BF,aAA5B,CAAjC,CAZwB,CAaxB;;AACAhJ,UAAAA,IAAI,CAAC9B,MAAL,CAAYoD,OAAZ;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,eAAL6H,KAAK,CAACnJ,IAAD,EAAO;AACf;AACA;AAAM;AAAiBgJ,UAAAA,aAAa,GAAGhJ,IAAI,CAACrD,KAAL,CAAYqD,IAAI,CAACvD,QAAL,GAAgB,CAAjB,KAAwB,CAAnC,IAAwC,IAA/E,CAFe,CAGf;;AACAuD,UAAAA,IAAI,CAACvD,QAAL,IAAiBuM,aAAjB;AACH;;AA3CO,O;AAgDZ;AACA;AACA;AACA;;;AACMnN,MAAAA,W,GAAN,MAAMA,WAAN,CAAkB;AAEJ,eAAHkN,GAAG,CAAC/I,IAAD,EAAOL,SAAP,EAAkB;AACxB;AACA;AAAM;AAAiBQ,UAAAA,cAAc,GAAGR,SAAS,GAAG,CAApD,CAFwB,CAGxB;;AACA;AAAM;AAAiBqJ,UAAAA,aAAa,GAAG7I,cAAc,GAAGH,IAAI,CAACvD,QAAL,GAAgB0D,cAAxE,CAJwB,CAKxB;;AACA;AAAM;AAAiB+I,UAAAA,YAAY,GAAG,EAAtC;;AACA,eAAK;AAAI;AAAiB1M,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAGwM,aAArC,EAAoDxM,CAAC,IAAI,CAAzD,EAA4D;AACxD0M,YAAAA,YAAY,CAACtM,IAAb,CAAkB,CAAlB;AACH;;AACD;AAAM;AAAiB0E,UAAAA,OAAO,GAAG,IAAI9G,SAAJ,CAAc0O,YAAd,EAA4BF,aAA5B,CAAjC,CAVwB,CAWxB;;AACAhJ,UAAAA,IAAI,CAAC9B,MAAL,CAAYoD,OAAZ;AACH;;AAGW,eAAL6H,KAAK,CAACnJ,IAAD,EAAO;AAEf;AACA,eAAK,IAAIxD,CAAC,GAAGwD,IAAI,CAACrD,KAAL,CAAWO,MAAX,GAAoB,CAAjC,EAAoCV,CAAC,IAAE,CAAvC,EAA0CA,CAAC,EAA3C,EAA+C;AAC3C,gBAAGwD,IAAI,CAACrD,KAAL,CAAWH,CAAX,KAAiB,CAApB,EAAsB;AAClBwD,cAAAA,IAAI,CAACzB,KAAL,CAAW,CAAX,EAAc/B,CAAC,GAAC,CAAhB,EADkB,CAElB;;AACA;AACH;AACJ;AAEJ;;AA7Ba,O;AAiClB;AACA;AACA;AACA;;AACA;AACA;AACA;;yBACMV,W,GAAN,MAAMA,WAAN,SAA0BR,MAA1B,CAAiC;AAC7B;AACJ;AACA;AACA;AACA;AACIyC,QAAAA,WAAW,CAAC0J,SAAD,EAAYxG,GAAZ,EAAiB1B,GAAjB,EAAsB;AAC7B,gBAAMkI,SAAN,EAAiBxG,GAAjB,EAAsBxB,MAAM,CAACC,MAAP,CAAc;AAChC;AACAC,YAAAA,SAAS,EAAE,CAFqB;AAGhC0B,YAAAA,IAAI,EAAE1F,GAH0B;AAIhC2F,YAAAA,OAAO,EAAE1F;AAJuB,WAAd,EAKnB2D,GALmB,CAAtB;AAMH;AACD;AACJ;AACA;;;AACIO,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMA,KAAN,GAFI,CAGJ;;AACA,cAAI,KAAKP,GAAL,CAAS8B,IAAT,KAAkBrD,SAAtB,EAAiC;AAC7B,kBAAM,IAAImB,KAAJ,CAAU,wBAAV,CAAN;AACH,WANG,CAOJ;;;AACA;AAAI;AAAiBiK,UAAAA,WAArB;;AACA,cAAI,KAAK1B,UAAL;AAAqB;AAAkB,eAAK3J,WAAxB,CAAsC8J,eAA9D,EAA+E;AAC3EuB,YAAAA,WAAW,GAAG,KAAK7J,GAAL,CAAS8B,IAAT,CAAc6B,eAA5B;AACH,WAFD;AAGK;AAAmD;AACpDkG,cAAAA,WAAW,GAAG,KAAK7J,GAAL,CAAS8B,IAAT,CAAcoC,eAA5B,CADoD,CAEpD;;AACA,mBAAKjE,cAAL,GAAsB,CAAtB;AACH;;AACD,cAAI,KAAK6J,KAAL,IAAc,KAAKA,KAAL,CAAWC,SAAX,KAAyBF,WAA3C,EAAwD;AACpD,iBAAKC,KAAL,CAAWnB,IAAX,CAAgB,IAAhB,EAAsB,KAAK3I,GAAL,CAAS2B,EAAT,IAAe,KAAK3B,GAAL,CAAS2B,EAAT,CAAYvE,KAAjD;AACH,WAFD,MAGK;AACD,iBAAK0M,KAAL,GAAaD,WAAW,CAAC5B,IAAZ,CAAiB,KAAKjI,GAAL,CAAS8B,IAA1B,EAAgC,IAAhC,EAAsC,KAAK9B,GAAL,CAAS2B,EAAT,IAAe,KAAK3B,GAAL,CAAS2B,EAAT,CAAYvE,KAAjE,CAAb;AACA,iBAAK0M,KAAL,CAAWC,SAAX,GAAuBF,WAAvB;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIzI,QAAAA,eAAe,CAAChE,KAAD,EAAQ+D,MAAR,EAAgB;AAC3B,eAAK2I,KAAL,CAAWZ,YAAX,CAAwB9L,KAAxB,EAA+B+D,MAA/B;AACH;AACD;AACJ;AACA;;;AACIwD,QAAAA,WAAW,GAAG;AACV;AACA,cAAI,KAAK3E,GAAL,CAAS+B,OAAT,KAAqBtD,SAAzB,EAAoC;AAChC,kBAAM,IAAImB,KAAJ,CAAU,2BAAV,CAAN;AACH,WAJS,CAKV;;;AACA;AAAI;AAAiBoK,UAAAA,oBAArB;;AACA,cAAI,KAAK7B,UAAL;AAAqB;AAAkB,eAAK3J,WAAxB,CAAsC8J,eAA9D,EAA+E;AAC3E;AACA,gBAAI,KAAKtI,GAAL,CAASI,SAAT,KAAuB3B,SAA3B,EAAsC;AAClC,oBAAM,IAAImB,KAAJ,CAAU,6BAAV,CAAN;AACH,aAJ0E,CAK3E;;;AACA,iBAAKI,GAAL,CAAS+B,OAAT,CAAiByH,GAAjB,CAAqB,KAAKnJ,KAA1B,EAAiC,KAAKL,GAAL,CAASI,SAA1C,EAN2E,CAO3E;;AACA4J,YAAAA,oBAAoB,GAAG,KAAKtJ,QAAL,CAAc,CAAC,CAAC,OAAhB,CAAvB;AACH,WATD;AAUK;AAAmD;AACpD;AACAsJ,cAAAA,oBAAoB,GAAG,KAAKtJ,QAAL,CAAc,CAAC,CAAC,OAAhB,CAAvB,CAFoD,CAGpD;;AACA,mBAAKV,GAAL,CAAS+B,OAAT,CAAiB6H,KAAjB,CAAuBI,oBAAvB;AACH;;AACD,iBAAOA,oBAAP;AACH;;AA7E4B,O;AAgFjC;AACA;AACA;AACA;AACA;;;AACuBC,MAAAA,I,GAAO,E;AACPC,MAAAA,Q,GAAW,E;AACXC,MAAAA,S,GAAY,E;AACZC,MAAAA,S,GAAY,E;AACZC,MAAAA,S,GAAY,E;AACZC,MAAAA,S,GAAY,E;AACZC,MAAAA,a,GAAgB,E;AAChBC,MAAAA,a,GAAgB,E;AAChBC,MAAAA,a,GAAgB,E;AAChBC,MAAAA,a,GAAgB,E,EACvC;;AACC,mBAAY;AACT;AACA;AAAM;AAAiBvF,QAAAA,CAAC,GAAG,EAA3B;;AACA,aAAK;AAAI;AAAiBlI,QAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAG,GAArC,EAA0CA,CAAC,EAA3C,EAA+C;AAC3C,cAAIA,CAAC,GAAG,GAAR,EAAa;AACTkI,YAAAA,CAAC,CAAClI,CAAD,CAAD,GAAOA,CAAC,IAAI,CAAZ;AACH,WAFD,MAGK;AACDkI,YAAAA,CAAC,CAAClI,CAAD,CAAD,GAAQA,CAAC,IAAI,CAAN,GAAW,KAAlB;AACH;AACJ,SAVQ,CAWT;;;AACA;AAAI;AAAiBmI,QAAAA,CAAC,GAAG,CAAzB;AACA;AAAI;AAAiBuF,QAAAA,EAAE,GAAG,CAA1B;;AACA,aAAK;AAAI;AAAiB1N,QAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAG,GAArC,EAA0CA,CAAC,EAA3C,EAA+C;AAC3C;AACA;AAAI;AAAiB2N,UAAAA,EAAE,GAAGD,EAAE,GAAIA,EAAE,IAAI,CAAZ,GAAkBA,EAAE,IAAI,CAAxB,GAA8BA,EAAE,IAAI,CAApC,GAA0CA,EAAE,IAAI,CAA1E;AACAC,UAAAA,EAAE,GAAIA,EAAE,KAAK,CAAR,GAAcA,EAAE,GAAG,IAAnB,GAA2B,IAAhC;AACAX,UAAAA,IAAI,CAAC7E,CAAD,CAAJ,GAAUwF,EAAV;AACAV,UAAAA,QAAQ,CAACU,EAAD,CAAR,GAAexF,CAAf,CAL2C,CAM3C;;AACA;AAAM;AAAiByF,UAAAA,EAAE,GAAG1F,CAAC,CAACC,CAAD,CAA7B;AACA;AAAM;AAAiB0F,UAAAA,EAAE,GAAG3F,CAAC,CAAC0F,EAAD,CAA7B;AACA;AAAM;AAAiBE,UAAAA,EAAE,GAAG5F,CAAC,CAAC2F,EAAD,CAA7B,CAT2C,CAU3C;;AACA;AAAI;AAAiBxF,UAAAA,CAAC,GAAIH,CAAC,CAACyF,EAAD,CAAD,GAAQ,KAAT,GAAmBA,EAAE,GAAG,SAAjD;AACAT,UAAAA,SAAS,CAAC/E,CAAD,CAAT,GAAgBE,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,CAAlC;AACA8E,UAAAA,SAAS,CAAChF,CAAD,CAAT,GAAgBE,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,EAAlC;AACA+E,UAAAA,SAAS,CAACjF,CAAD,CAAT,GAAgBE,CAAC,IAAI,CAAN,GAAYA,CAAC,KAAK,EAAjC;AACAgF,UAAAA,SAAS,CAAClF,CAAD,CAAT,GAAeE,CAAf,CAf2C,CAgB3C;;AACAA,UAAAA,CAAC,GAAIyF,EAAE,GAAG,SAAN,GAAoBD,EAAE,GAAG,OAAzB,GAAqCD,EAAE,GAAG,KAA1C,GAAoDzF,CAAC,GAAG,SAA5D;AACAmF,UAAAA,aAAa,CAACK,EAAD,CAAb,GAAqBtF,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,CAAvC;AACAkF,UAAAA,aAAa,CAACI,EAAD,CAAb,GAAqBtF,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,EAAvC;AACAmF,UAAAA,aAAa,CAACG,EAAD,CAAb,GAAqBtF,CAAC,IAAI,CAAN,GAAYA,CAAC,KAAK,EAAtC;AACAoF,UAAAA,aAAa,CAACE,EAAD,CAAb,GAAoBtF,CAApB,CArB2C,CAsB3C;;AACA,cAAI,CAACF,CAAL,EAAQ;AACJA,YAAAA,CAAC,GAAGuF,EAAE,GAAG,CAAT;AACH,WAFD,MAGK;AACDvF,YAAAA,CAAC,GAAGyF,EAAE,GAAG1F,CAAC,CAACA,CAAC,CAACA,CAAC,CAAC4F,EAAE,GAAGF,EAAN,CAAF,CAAF,CAAV;AACAF,YAAAA,EAAE,IAAIxF,CAAC,CAACA,CAAC,CAACwF,EAAD,CAAF,CAAP;AACH;AACJ;AACJ,OA7CA,GAAD,C,CA8CA;;;AACuBK,MAAAA,I,GAAO,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC,EAA2C,IAA3C,EAAiD,IAAjD,EAAuD,IAAvD,EAA6D,IAA7D,C;;yBACxBxO,G,GAAN,MAAMA,GAAN,SAAkBD,WAAlB,CAA8B;AAC1B;AACJ;AACA;AACA;AACA;AACIiC,QAAAA,WAAW,CAAC0J,SAAD,EAAYxG,GAAZ,EAAiB1B,GAAjB,EAAsB;AAC7B,gBAAMkI,SAAN,EAAiBxG,GAAjB,EAAsB1B,GAAtB;AACH;AACD;AACJ;AACA;;;AACIO,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMA,KAAN,GAFI,CAGJ;;AACA,cAAI,KAAK0K,QAAL,IAAiB,KAAKC,cAAL,KAAwB,KAAK9C,IAAlD,EAAwD;AACpD;AACH,WANG,CAOJ;;;AACA;AAAM;AAAiB1G,UAAAA,GAAG,GAAG,KAAKwJ,cAAL,GAAsB,KAAK9C,IAAxD;AACA;AAAM;AAAiB+C,UAAAA,QAAQ,GAAGzJ,GAAG,CAACtE,KAAtC;AACA;AAAM;AAAiBmK,UAAAA,OAAO,GAAG7F,GAAG,CAACxE,QAAJ,GAAe,CAAhD,CAVI,CAWJ;;AACA;AAAM;AAAiBkO,UAAAA,OAAO,GAAG,KAAKH,QAAL,GAAgB1D,OAAO,GAAG,CAA3D,CAZI,CAaJ;;AACA;AAAM;AAAiB8D,UAAAA,MAAM,GAAG,CAACD,OAAO,GAAG,CAAX,IAAgB,CAAhD,CAdI,CAeJ;;AACA;AAAM;AAAiBE,UAAAA,WAAW,GAAG,KAAKC,YAAL,GAAoB,EAAzD;;AACA,eAAK;AAAI;AAAiBC,UAAAA,KAAK,GAAG,CAAlC,EAAqCA,KAAK,GAAGH,MAA7C,EAAqDG,KAAK,EAA1D,EAA8D;AAC1D,gBAAIA,KAAK,GAAGjE,OAAZ,EAAqB;AACjB+D,cAAAA,WAAW,CAACE,KAAD,CAAX,GAAqBL,QAAQ,CAACK,KAAD,CAA7B;AACH,aAFD,MAGK;AACD;AAAI;AAAiBlG,cAAAA,CAAC,GAAGgG,WAAW,CAACE,KAAK,GAAG,CAAT,CAApC;;AACA,kBAAI,EAAEA,KAAK,GAAGjE,OAAV,CAAJ,EAAwB;AACpB;AACAjC,gBAAAA,CAAC,GAAIA,CAAC,IAAI,CAAN,GAAYA,CAAC,KAAK,EAAtB,CAFoB,CAGpB;;AACAA,gBAAAA,CAAC,GAAI2E,IAAI,CAAC3E,CAAC,KAAK,EAAP,CAAJ,IAAkB,EAAnB,GAA0B2E,IAAI,CAAE3E,CAAC,KAAK,EAAP,GAAa,IAAd,CAAJ,IAA2B,EAArD,GAA4D2E,IAAI,CAAE3E,CAAC,KAAK,CAAP,GAAY,IAAb,CAAJ,IAA0B,CAAtF,GAA2F2E,IAAI,CAAC3E,CAAC,GAAG,IAAL,CAAnG,CAJoB,CAKpB;;AACAA,gBAAAA,CAAC,IAAI0F,IAAI,CAAEQ,KAAK,GAAGjE,OAAT,GAAoB,CAArB,CAAJ,IAA+B,EAApC;AACH,eAPD,MAQK,IAAIA,OAAO,GAAG,CAAV,IAAeiE,KAAK,GAAGjE,OAAR,KAAoB,CAAvC,EAA0C;AAC3C;AACAjC,gBAAAA,CAAC,GAAI2E,IAAI,CAAC3E,CAAC,KAAK,EAAP,CAAJ,IAAkB,EAAnB,GAA0B2E,IAAI,CAAE3E,CAAC,KAAK,EAAP,GAAa,IAAd,CAAJ,IAA2B,EAArD,GAA4D2E,IAAI,CAAE3E,CAAC,KAAK,CAAP,GAAY,IAAb,CAAJ,IAA0B,CAAtF,GAA2F2E,IAAI,CAAC3E,CAAC,GAAG,IAAL,CAAnG;AACH;;AACDgG,cAAAA,WAAW,CAACE,KAAD,CAAX,GAAqBF,WAAW,CAACE,KAAK,GAAGjE,OAAT,CAAX,GAA+BjC,CAApD;AACH;AACJ,WArCG,CAsCJ;;;AACA;AAAM;AAAiBmG,UAAAA,cAAc,GAAG,KAAKC,eAAL,GAAuB,EAA/D;;AACA,eAAK;AAAI;AAAiBC,UAAAA,QAAQ,GAAG,CAArC,EAAwCA,QAAQ,GAAGN,MAAnD,EAA2DM,QAAQ,EAAnE,EAAuE;AACnE;AAAM;AAAiBH,YAAAA,KAAK,GAAGH,MAAM,GAAGM,QAAxC;AACA;AAAI;AAAiBrG,YAAAA,CAArB;;AACA,gBAAIqG,QAAQ,GAAG,CAAf,EAAkB;AACdrG,cAAAA,CAAC,GAAGgG,WAAW,CAACE,KAAD,CAAf;AACH,aAFD,MAGK;AACDlG,cAAAA,CAAC,GAAGgG,WAAW,CAACE,KAAK,GAAG,CAAT,CAAf;AACH;;AACD,gBAAIG,QAAQ,GAAG,CAAX,IAAgBH,KAAK,IAAI,CAA7B,EAAgC;AAC5BC,cAAAA,cAAc,CAACE,QAAD,CAAd,GAA2BrG,CAA3B;AACH,aAFD,MAGK;AACDmG,cAAAA,cAAc,CAACE,QAAD,CAAd,GAA2BpB,aAAa,CAACN,IAAI,CAAC3E,CAAC,KAAK,EAAP,CAAL,CAAb,GAAgCkF,aAAa,CAACP,IAAI,CAAE3E,CAAC,KAAK,EAAP,GAAa,IAAd,CAAL,CAA7C,GACvBmF,aAAa,CAACR,IAAI,CAAE3E,CAAC,KAAK,CAAP,GAAY,IAAb,CAAL,CADU,GACiBoF,aAAa,CAACT,IAAI,CAAC3E,CAAC,GAAG,IAAL,CAAL,CADzD;AAEH;AACJ;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACI8D,QAAAA,YAAY,CAACxD,CAAD,EAAIzE,MAAJ,EAAY;AACpB,eAAKyK,aAAL,CAAmBhG,CAAnB,EAAsBzE,MAAtB,EAA8B,KAAKoK,YAAnC,EAAiDpB,SAAjD,EAA4DC,SAA5D,EAAuEC,SAAvE,EAAkFC,SAAlF,EAA6FL,IAA7F;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIV,QAAAA,YAAY,CAAC3D,CAAD,EAAIzE,MAAJ,EAAY;AACpB;AACA;AAAI;AAAiBmE,UAAAA,CAAC,GAAGM,CAAC,CAACzE,MAAM,GAAG,CAAV,CAA1B;AACAyE,UAAAA,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgByE,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAjB;AACAyE,UAAAA,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgBmE,CAAhB;;AACA,eAAKsG,aAAL,CAAmBhG,CAAnB,EAAsBzE,MAAtB,EAA8B,KAAKuK,eAAnC,EAAoDnB,aAApD,EAAmEC,aAAnE,EAAkFC,aAAlF,EAAiGC,aAAjG,EAAgHR,QAAhH,EALoB,CAMpB;;;AACA5E,UAAAA,CAAC,GAAGM,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAL;AACAyE,UAAAA,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgByE,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAjB;AACAyE,UAAAA,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgBmE,CAAhB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIsG,QAAAA,aAAa,CAAChG,CAAD,EAAIzE,MAAJ,EAAYmK,WAAZ,EAAyBO,SAAzB,EAAoCC,SAApC,EAA+CC,SAA/C,EAA0DC,SAA1D,EAAqEC,IAArE,EAA2E;AACpF;AACA;AAAI;AAAiBC,UAAAA,EAAE,GAAGtG,CAAC,CAACzE,MAAD,CAAD,GAAYmK,WAAW,CAAC,CAAD,CAAjD;AACA;AAAI;AAAiBa,UAAAA,EAAE,GAAGvG,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgBmK,WAAW,CAAC,CAAD,CAArD;AACA;AAAI;AAAiBc,UAAAA,EAAE,GAAGxG,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgBmK,WAAW,CAAC,CAAD,CAArD;AACA;AAAI;AAAiBe,UAAAA,EAAE,GAAGzG,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgBmK,WAAW,CAAC,CAAD,CAArD,CALoF,CAMpF;;AACA;AAAI;AAAiBE,UAAAA,KAAK,GAAG,CAA7B,CAPoF,CAQpF;;AACA,eAAK;AAAI;AAAiBc,UAAAA,KAAK,GAAG,CAAlC,EAAqCA,KAAK,GAAG,KAAKrB,QAAlD,EAA4DqB,KAAK,EAAjE,EAAqE;AACjE;AACA;AAAM;AAAiBC,YAAAA,EAAE,GAAGV,SAAS,CAACK,EAAE,KAAK,EAAR,CAAT,GAAuBJ,SAAS,CAAEK,EAAE,KAAK,EAAR,GAAc,IAAf,CAAhC,GAAuDJ,SAAS,CAAEK,EAAE,KAAK,CAAR,GAAa,IAAd,CAAhE,GAAsFJ,SAAS,CAACK,EAAE,GAAG,IAAN,CAA/F,GACxBf,WAAW,CAACE,KAAK,EAAN,CADf;AAEA;AAAM;AAAiBgB,YAAAA,EAAE,GAAGX,SAAS,CAACM,EAAE,KAAK,EAAR,CAAT,GAAuBL,SAAS,CAAEM,EAAE,KAAK,EAAR,GAAc,IAAf,CAAhC,GAAuDL,SAAS,CAAEM,EAAE,KAAK,CAAR,GAAa,IAAd,CAAhE,GAAsFL,SAAS,CAACE,EAAE,GAAG,IAAN,CAA/F,GACxBZ,WAAW,CAACE,KAAK,EAAN,CADf;AAEA;AAAM;AAAiBiB,YAAAA,EAAE,GAAGZ,SAAS,CAACO,EAAE,KAAK,EAAR,CAAT,GAAuBN,SAAS,CAAEO,EAAE,KAAK,EAAR,GAAc,IAAf,CAAhC,GAAuDN,SAAS,CAAEG,EAAE,KAAK,CAAR,GAAa,IAAd,CAAhE,GAAsFF,SAAS,CAACG,EAAE,GAAG,IAAN,CAA/F,GACxBb,WAAW,CAACE,KAAK,EAAN,CADf;AAEA;AAAM;AAAiBkB,YAAAA,EAAE,GAAGb,SAAS,CAACQ,EAAE,KAAK,EAAR,CAAT,GAAuBP,SAAS,CAAEI,EAAE,KAAK,EAAR,GAAc,IAAf,CAAhC,GAAuDH,SAAS,CAAEI,EAAE,KAAK,CAAR,GAAa,IAAd,CAAhE,GAAsFH,SAAS,CAACI,EAAE,GAAG,IAAN,CAA/F,GACxBd,WAAW,CAACE,KAAK,EAAN,CADf,CARiE,CAUjE;;AACAU,YAAAA,EAAE,GAAGK,EAAL;AACAJ,YAAAA,EAAE,GAAGK,EAAL;AACAJ,YAAAA,EAAE,GAAGK,EAAL;AACAJ,YAAAA,EAAE,GAAGK,EAAL;AACH,WAxBmF,CAyBpF;;;AACA;AAAM;AAAiBC,UAAAA,GAAG,GAAG,CAAEV,IAAI,CAACC,EAAE,KAAK,EAAR,CAAJ,IAAmB,EAApB,GAA2BD,IAAI,CAAEE,EAAE,KAAK,EAAR,GAAc,IAAf,CAAJ,IAA4B,EAAvD,GAA8DF,IAAI,CAAEG,EAAE,KAAK,CAAR,GAAa,IAAd,CAAJ,IAA2B,CAAzF,GAA8FH,IAAI,CAACI,EAAE,GAAG,IAAN,CAAnG,IACzBf,WAAW,CAACE,KAAK,EAAN,CADf;AAEA;AAAM;AAAiBoB,UAAAA,GAAG,GAAG,CAAEX,IAAI,CAACE,EAAE,KAAK,EAAR,CAAJ,IAAmB,EAApB,GAA2BF,IAAI,CAAEG,EAAE,KAAK,EAAR,GAAc,IAAf,CAAJ,IAA4B,EAAvD,GAA8DH,IAAI,CAAEI,EAAE,KAAK,CAAR,GAAa,IAAd,CAAJ,IAA2B,CAAzF,GAA8FJ,IAAI,CAACC,EAAE,GAAG,IAAN,CAAnG,IACzBZ,WAAW,CAACE,KAAK,EAAN,CADf;AAEA;AAAM;AAAiBqB,UAAAA,GAAG,GAAG,CAAEZ,IAAI,CAACG,EAAE,KAAK,EAAR,CAAJ,IAAmB,EAApB,GAA2BH,IAAI,CAAEI,EAAE,KAAK,EAAR,GAAc,IAAf,CAAJ,IAA4B,EAAvD,GAA8DJ,IAAI,CAAEC,EAAE,KAAK,CAAR,GAAa,IAAd,CAAJ,IAA2B,CAAzF,GAA8FD,IAAI,CAACE,EAAE,GAAG,IAAN,CAAnG,IACzBb,WAAW,CAACE,KAAK,EAAN,CADf;AAEA;AAAM;AAAiBsB,UAAAA,GAAG,GAAG,CAAEb,IAAI,CAACI,EAAE,KAAK,EAAR,CAAJ,IAAmB,EAApB,GAA2BJ,IAAI,CAAEC,EAAE,KAAK,EAAR,GAAc,IAAf,CAAJ,IAA4B,EAAvD,GAA8DD,IAAI,CAAEE,EAAE,KAAK,CAAR,GAAa,IAAd,CAAJ,IAA2B,CAAzF,GAA8FF,IAAI,CAACG,EAAE,GAAG,IAAN,CAAnG,IACzBd,WAAW,CAACE,KAAK,EAAN,CADf,CAhCoF,CAkCpF;;AACA5F,UAAAA,CAAC,CAACzE,MAAD,CAAD,GAAYwL,GAAZ;AACA/G,UAAAA,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgByL,GAAhB;AACAhH,UAAAA,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgB0L,GAAhB;AACAjH,UAAAA,CAAC,CAACzE,MAAM,GAAG,CAAV,CAAD,GAAgB2L,GAAhB;AACH;;AAhJyB,O;;AAkJ9BtQ,MAAAA,GAAG,CAAC+K,OAAJ,GAAc,CAAd;AAEA;AACA;AACA;AACA;AACA;;AACuBxB,MAAAA,C,GAAI,E;AACJgH,MAAAA,C,GAAI,E,EAC3B;;AACuBC,MAAAA,C,GAAI,E;;yBACrBvQ,M,GAAN,MAAMA,MAAN,SAAqBf,MAArB,CAA4B;AACxB;AACJ;AACA;AACI6E,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMA,KAAN;AACA,eAAKoF,KAAL,GAAa,IAAI1K,SAAJ,CAAc8K,CAAC,CAAC/G,KAAF,CAAQ,CAAR,CAAd,CAAb;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIoC,QAAAA,eAAe,CAACwE,CAAD,EAAIzE,MAAJ,EAAY;AACvB;AACA;AAAM;AAAiB8L,UAAAA,EAAE,GAAG,KAAKtH,KAAL,CAAWvI,KAAvC,CAFuB,CAGvB;;AACA;AAAI;AAAiB4H,UAAAA,CAAC,GAAGiI,EAAE,CAAC,CAAD,CAA3B;AACA;AAAI;AAAiBhI,UAAAA,CAAC,GAAGgI,EAAE,CAAC,CAAD,CAA3B;AACA;AAAI;AAAiB/H,UAAAA,CAAC,GAAG+H,EAAE,CAAC,CAAD,CAA3B;AACA;AAAI;AAAiB9H,UAAAA,CAAC,GAAG8H,EAAE,CAAC,CAAD,CAA3B;AACA;AAAI;AAAiBtN,UAAAA,CAAC,GAAGsN,EAAE,CAAC,CAAD,CAA3B;AACA;AAAI;AAAiBC,UAAAA,CAAC,GAAGD,EAAE,CAAC,CAAD,CAA3B;AACA;AAAI;AAAiBE,UAAAA,CAAC,GAAGF,EAAE,CAAC,CAAD,CAA3B;AACA;AAAI;AAAiBG,UAAAA,CAAC,GAAGH,EAAE,CAAC,CAAD,CAA3B,CAXuB,CAYvB;;AACA,eAAK;AAAI;AAAiBhQ,UAAAA,CAAC,GAAG,CAA9B,EAAiCA,CAAC,GAAG,EAArC,EAAyCA,CAAC,EAA1C,EAA8C;AAC1C,gBAAIA,CAAC,GAAG,EAAR,EAAY;AACR+P,cAAAA,CAAC,CAAC/P,CAAD,CAAD,GAAO2I,CAAC,CAACzE,MAAM,GAAGlE,CAAV,CAAD,GAAgB,CAAvB;AACH,aAFD,MAGK;AACD;AAAM;AAAiBoQ,cAAAA,OAAO,GAAGL,CAAC,CAAC/P,CAAC,GAAG,EAAL,CAAlC;AACA;AAAM;AAAiBqQ,cAAAA,MAAM,GAAG,CAAED,OAAO,IAAI,EAAZ,GAAmBA,OAAO,KAAK,CAAhC,KAC1BA,OAAO,IAAI,EAAZ,GAAmBA,OAAO,KAAK,EADJ,IAE3BA,OAAO,KAAK,CAFjB;AAGA;AAAM;AAAiBE,cAAAA,OAAO,GAAGP,CAAC,CAAC/P,CAAC,GAAG,CAAL,CAAlC;AACA;AAAM;AAAiBuQ,cAAAA,MAAM,GAAG,CAAED,OAAO,IAAI,EAAZ,GAAmBA,OAAO,KAAK,EAAhC,KAC1BA,OAAO,IAAI,EAAZ,GAAmBA,OAAO,KAAK,EADJ,IAE3BA,OAAO,KAAK,EAFjB;AAGAP,cAAAA,CAAC,CAAC/P,CAAD,CAAD,GAAOqQ,MAAM,GAAGN,CAAC,CAAC/P,CAAC,GAAG,CAAL,CAAV,GAAoBuQ,MAApB,GAA6BR,CAAC,CAAC/P,CAAC,GAAG,EAAL,CAArC;AACH;;AACD;AAAM;AAAiBwQ,YAAAA,EAAE,GAAI9N,CAAC,GAAGuN,CAAL,GAAW,CAACvN,CAAD,GAAKwN,CAA5C;AACA;AAAM;AAAiBO,YAAAA,GAAG,GAAI1I,CAAC,GAAGC,CAAL,GAAWD,CAAC,GAAGE,CAAf,GAAqBD,CAAC,GAAGC,CAAtD;AACA;AAAM;AAAiByI,YAAAA,MAAM,GAAG,CAAE3I,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,CAApB,KAA4BA,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,EAA9C,KAAuDA,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,EAAzE,CAAhC;AACA;AAAM;AAAiB4I,YAAAA,MAAM,GAAG,CAAEjO,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,CAApB,KAA4BA,CAAC,IAAI,EAAN,GAAaA,CAAC,KAAK,EAA9C,KAAuDA,CAAC,IAAI,CAAN,GAAYA,CAAC,KAAK,EAAxE,CAAhC;AACA;AAAM;AAAiB6M,YAAAA,EAAE,GAAGY,CAAC,GAAGQ,MAAJ,GAAaH,EAAb,GAAkBV,CAAC,CAAC9P,CAAD,CAAnB,GAAyB+P,CAAC,CAAC/P,CAAD,CAAtD;AACA;AAAM;AAAiBwP,YAAAA,EAAE,GAAGkB,MAAM,GAAGD,GAArC;AACAN,YAAAA,CAAC,GAAGD,CAAJ;AACAA,YAAAA,CAAC,GAAGD,CAAJ;AACAA,YAAAA,CAAC,GAAGvN,CAAJ;AACAA,YAAAA,CAAC,GAAIwF,CAAC,GAAGqH,EAAL,GAAW,CAAf;AACArH,YAAAA,CAAC,GAAGD,CAAJ;AACAA,YAAAA,CAAC,GAAGD,CAAJ;AACAA,YAAAA,CAAC,GAAGD,CAAJ;AACAA,YAAAA,CAAC,GAAIwH,EAAE,GAAGC,EAAN,GAAY,CAAhB;AACH,WA1CsB,CA2CvB;;;AACAQ,UAAAA,EAAE,CAAC,CAAD,CAAF,GAASA,EAAE,CAAC,CAAD,CAAF,GAAQjI,CAAT,GAAc,CAAtB;AACAiI,UAAAA,EAAE,CAAC,CAAD,CAAF,GAASA,EAAE,CAAC,CAAD,CAAF,GAAQhI,CAAT,GAAc,CAAtB;AACAgI,UAAAA,EAAE,CAAC,CAAD,CAAF,GAASA,EAAE,CAAC,CAAD,CAAF,GAAQ/H,CAAT,GAAc,CAAtB;AACA+H,UAAAA,EAAE,CAAC,CAAD,CAAF,GAASA,EAAE,CAAC,CAAD,CAAF,GAAQ9H,CAAT,GAAc,CAAtB;AACA8H,UAAAA,EAAE,CAAC,CAAD,CAAF,GAASA,EAAE,CAAC,CAAD,CAAF,GAAQtN,CAAT,GAAc,CAAtB;AACAsN,UAAAA,EAAE,CAAC,CAAD,CAAF,GAASA,EAAE,CAAC,CAAD,CAAF,GAAQC,CAAT,GAAc,CAAtB;AACAD,UAAAA,EAAE,CAAC,CAAD,CAAF,GAASA,EAAE,CAAC,CAAD,CAAF,GAAQE,CAAT,GAAc,CAAtB;AACAF,UAAAA,EAAE,CAAC,CAAD,CAAF,GAASA,EAAE,CAAC,CAAD,CAAF,GAAQG,CAAT,GAAc,CAAtB;AACH;AACD;AACJ;AACA;;;AACIzI,QAAAA,WAAW,GAAG;AACV;AAAM;AAAiBsC,UAAAA,UAAU,GAAG,KAAK3G,WAAL,GAAmB,CAAvD;AACA;AAAM;AAAiB4G,UAAAA,SAAS,GAAG,KAAK7G,KAAL,CAAWnD,QAAX,GAAsB,CAAzD,CAFU,CAGV;;AACA,eAAKmD,KAAL,CAAWjD,KAAX,CAAiB8J,SAAS,KAAK,CAA/B,KAAqC,QAAS,KAAKA,SAAS,GAAG,EAA/D;AACA,eAAK7G,KAAL,CAAWjD,KAAX,CAAiB,CAAG8J,SAAS,GAAG,EAAb,KAAqB,CAAtB,IAA4B,CAA7B,IAAkC,EAAnD,IAAyD7I,IAAI,CAAC+I,KAAL,CAAWH,UAAU,GAAG,WAAxB,CAAzD;AACA,eAAK5G,KAAL,CAAWjD,KAAX,CAAiB,CAAG8J,SAAS,GAAG,EAAb,KAAqB,CAAtB,IAA4B,CAA7B,IAAkC,EAAnD,IAAyDD,UAAzD;AACA,eAAK5G,KAAL,CAAWnD,QAAX,GAAsB,KAAKmD,KAAL,CAAWjD,KAAX,CAAiBO,MAAjB,GAA0B,CAAhD,CAPU,CAQV;;AACA,eAAK+C,QAAL,GATU,CAUV;;;AACA,iBAAO,KAAKiF,KAAZ;AACH;;AAlFuB,O;AAqF5B;AACA;AACA;AACA;;;yBACMjJ,S,GAAN,MAAMA,SAAN,CAAgB;AACZ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACc,eAAH8M,GAAG,CAAC/I,IAAD,EAAOL,SAAP,EAAkB,CAC3B;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgB,eAALwJ,KAAK,CAACnJ,IAAD,EAAO,CAClB;;AAzBW,O;AA4BhB;AACA;AACA;AACA;;;AACM9D,MAAAA,Y,GAAN,MAAMA,YAAN,SAA2BX,wBAA3B,CAAoD;AAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIkN,QAAAA,YAAY,CAAC9L,KAAD,EAAQ+D,MAAR,EAAgB;AACxB,eAAKyH,OAAL,CAAaQ,YAAb,CAA0BhM,KAA1B,EAAiC+D,MAAjC;AACH;;AAd+C,O;AAiBpD;AACA;AACA;AACA;;AACMvE,MAAAA,Y,GAAN,MAAMA,YAAN,SAA2BZ,wBAA3B,CAAoD;AAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIkN,QAAAA,YAAY,CAAC9L,KAAD,EAAQ+D,MAAR,EAAgB;AACxB,eAAKyH,OAAL,CAAaW,YAAb,CAA0BnM,KAA1B,EAAiC+D,MAAjC;AACH;;AAd+C,O;AAiBpD;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;yBACMtE,G,GAAN,MAAMA,GAAN,SAAkBZ,eAAlB,CAAkC,E;;AAElCY,MAAAA,GAAG,CAACkM,SAAJ,GAAgBpM,YAAhB;AACAE,MAAAA,GAAG,CAACoM,SAAJ,GAAgBrM,YAAhB;AAEA;AACA;AACA;AACA;;qBACuBiR,G,GAAM;AACzBtR,QAAAA,WAAW,EAAEA,WADY;AAEzBtB,QAAAA,SAAS,EAAEA,SAFc;AAGzBK,QAAAA,YAAY,EAAEA,YAHW;AAIzBI,QAAAA,MAAM,EAAEA,MAJiB;AAKzBD,QAAAA,kBAAkB,EAAEA,kBALK;AAMzBK,QAAAA,mBAAmB,EAAEA;AANI,O;;sBAQNgS,I,GAAO;AAC1BtR,QAAAA,GAAG,EAAEA,GADqB;AAE1BC,QAAAA,MAAM,EAAEA;AAFkB,O;;qBAIPsR,G,GAAM;AACzB5S,QAAAA,IAAI,EAAEA,IADmB;AAEzBH,QAAAA,GAAG,EAAEA;AAFoB,O;;qBAINwO,G,GAAM;AACzB9M,QAAAA,SAAS,EAAEA,SADc;AAEzBL,QAAAA,KAAK,EAAEA,KAFkB;AAGzBC,QAAAA,WAAW,EAAEA;AAHY,O;;sBAKNwF,I,GAAO;AAC1B1F,QAAAA,GAAG,EAAEA,GADqB;AAE1BS,QAAAA,GAAG,EAAEA;AAFqB,O,GAI9B;;;qBACuBmR,K,GAAQH,GAAG,CAACtR,WAAJ,CAAgB4H,aAAhB,CAA8B2J,IAAI,CAACtR,GAAnC,C;;wBACRyR,Q,GAAWJ,GAAG,CAACnS,MAAJ,CAAWyI,aAAX,CAAyB2J,IAAI,CAACrR,MAA9B,C;AAElC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "sourcesContent": ["/**\n * @license crypto-ts\n * MIT license\n */\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass Hex {\n    /**\n     * Converts a word array to a hex string.\n     *\n     * \\@example\n     *\n     *     let hexString = Hex.stringify(wordArray);\n     * @param {?} wordArray The word array.\n     *\n     * @return {?} The hex string.\n     *\n     */\n    static stringify(wordArray) {\n        // Convert\n        const /** @type {?} */ hexChars = [];\n        for (let /** @type {?} */ i = 0; i < wordArray.sigBytes; i++) {\n            const /** @type {?} */ bite = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;\n            hexChars.push((bite >>> 4).toString(16));\n            hexChars.push((bite & 0x0f).toString(16));\n        }\n        return hexChars.join('');\n    }\n    /**\n     * Converts a hex string to a word array.\n     *\n     * \\@example\n     *\n     *     let wordArray = Hex.parse(hexString);\n     * @param {?} hexStr The hex string.\n     *\n     * @return {?} The word array.\n     *\n     */\n    static parse(hexStr) {\n        // Shortcut\n        const /** @type {?} */ hexStrLength = hexStr.length;\n        // Convert\n        const /** @type {?} */ words = [];\n        for (let /** @type {?} */ i = 0; i < hexStrLength; i += 2) {\n            words[i >>> 3] |= parseInt(hexStr.substr(i, 2), 16) << (24 - (i % 8) * 4);\n        }\n        return new WordArray(words, hexStrLength / 2);\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass WordArray {\n    /**\n     * Creates a word array filled with random bytes.\n     *\n     * \\@example\n     *\n     *     let wordArray = WordArray.random(16);\n     * @param {?} nBytes The number of random bytes to generate.\n     *\n     * @return {?} The random word array.\n     *\n     */\n    static random(nBytes) {\n        const /** @type {?} */ words = [];\n        const /** @type {?} */ r = (function (m_w) {\n            let /** @type {?} */ m_z = 0x3ade68b1;\n            const /** @type {?} */ mask = 0xffffffff;\n            return function () {\n                m_z = (0x9069 * (m_z & 0xFFFF) + (m_z >> 0x10)) & mask;\n                m_w = (0x4650 * (m_w & 0xFFFF) + (m_w >> 0x10)) & mask;\n                let /** @type {?} */ result = ((m_z << 0x10) + m_w) & mask;\n                result /= 0x100000000;\n                result += 0.5;\n                return result * (Math.random() > .5 ? 1 : -1);\n            };\n        });\n        for (let /** @type {?} */ i = 0, /** @type {?} */ rcache; i < nBytes; i += 4) {\n            const /** @type {?} */ _r = r((rcache || Math.random()) * 0x100000000);\n            rcache = _r() * 0x3ade67b7;\n            words.push((_r() * 0x100000000) | 0);\n        }\n        return new WordArray(words, nBytes);\n    }\n    /**\n     * Initializes a newly created word array.\n     *\n     * \\@example\n     *\n     *     let wordArray = new WordArray();\n     *     let wordArray = new WordArray([0x00010203, 0x04050607]);\n     *     let wordArray = new WordArray([0x00010203, 0x04050607], 6);\n     * @param {?=} words (Optional) An array of 32-bit words.\n     * @param {?=} sigBytes (Optional) The number of significant bytes in the words.\n     *\n     */\n    constructor(words, sigBytes) {\n        this.words = words || [];\n        if (sigBytes !== undefined) {\n            this.sigBytes = sigBytes;\n        }\n        else {\n            this.sigBytes = this.words.length * 4;\n        }\n    }\n    /**\n     * Converts this word array to a string.\n     *\n     * \\@example\n     *\n     *     let string = wordArray + '';\n     *     let string = wordArray.toString();\n     *     let string = wordArray.toString(CryptoJS.enc.Utf8);\n     * @param {?=} encoder (Optional) The encoding strategy to use. Default: CryptoJS.enc.Hex\n     *\n     * @return {?} The stringified word array.\n     *\n     */\n    toString(encoder) {\n        return (encoder || Hex).stringify(this);\n    }\n    /**\n     * Concatenates a word array to this word array.\n     *\n     * \\@example\n     *\n     *     wordArray1.concat(wordArray2);\n     * @param {?} wordArray The word array to append.\n     *\n     * @return {?} This word array.\n     *\n     */\n    concat(wordArray) {\n        // Clamp excess bits\n        this.clamp();\n        // Concat\n        if (this.sigBytes % 4) {\n            // Copy one byte at a time\n            for (let /** @type {?} */ i = 0; i < wordArray.sigBytes; i++) {\n                const /** @type {?} */ thatByte = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;\n                this.words[(this.sigBytes + i) >>> 2] |= thatByte << (24 - ((this.sigBytes + i) % 4) * 8);\n            }\n        }\n        else {\n            // Copy one word at a time\n            for (let /** @type {?} */ i = 0; i < wordArray.sigBytes; i += 4) {\n                this.words[(this.sigBytes + i) >>> 2] = wordArray.words[i >>> 2];\n            }\n        }\n        this.sigBytes += wordArray.sigBytes;\n        // Chainable\n        return this;\n    }\n    /**\n     * Removes insignificant bits.\n     *\n     * \\@example\n     *\n     *     wordArray.clamp();\n     * @return {?}\n     */\n    clamp() {\n        // Clamp\n        this.words[this.sigBytes >>> 2] &= 0xffffffff << (32 - (this.sigBytes % 4) * 8);\n        this.words.length = Math.ceil(this.sigBytes / 4);\n    }\n    /**\n     * Creates a copy of this word array.\n     *\n     * \\@example\n     *\n     *     let clone = wordArray.clone();\n     * @return {?} The clone.\n     *\n     */\n    clone() {\n        return new WordArray(this.words.slice(0), this.sigBytes);\n    }\n\n    slice(start, end){\n        // console.log(\"slice:\", start, end, this.words.length);\n        this.words = this.words.slice(start, end);\n        this.sigBytes = this.words.length * 4;\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass Latin1 {\n    /**\n     * Converts a word array to a Latin1 string.\n     *\n     * \\@example\n     *\n     *     let latin1String = Latin1.stringify(wordArray);\n     * @param {?} wordArray The word array.\n     *\n     * @return {?} The Latin1 string.\n     *\n     */\n    static stringify(wordArray) {\n        // Convert\n        const /** @type {?} */ latin1Chars = [];\n        for (let /** @type {?} */ i = 0; i < wordArray.sigBytes; i++) {\n            const /** @type {?} */ bite = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;\n            latin1Chars.push(String.fromCharCode(bite));\n        }\n        return latin1Chars.join('');\n    }\n    /**\n     * Converts a Latin1 string to a word array.\n     *\n     * \\@example\n     *\n     *     let wordArray = Latin1.parse(latin1String);\n     * @param {?} latin1Str The Latin1 string.\n     *\n     * @return {?} The word array.\n     *\n     */\n    static parse(latin1Str) {\n        // Shortcut\n        const /** @type {?} */ latin1StrLength = latin1Str.length;\n        // Convert\n        const /** @type {?} */ words = [];\n        for (let /** @type {?} */ i = 0; i < latin1StrLength; i++) {\n            words[i >>> 2] |= (latin1Str.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8);\n        }\n        return new WordArray(words, latin1StrLength);\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass Utf8 {\n    /**\n     * Converts a word array to a UTF-8 string.\n     *\n     * \\@example\n     *\n     *     let utf8String = Utf8.stringify(wordArray);\n     * @param {?} wordArray The word array.\n     *\n     * @return {?} The UTF-8 string.\n     *\n     */\n    static stringify(wordArray) {\n        try {\n            return decodeURIComponent(escape(Latin1.stringify(wordArray)));\n        }\n        catch (/** @type {?} */ e) {\n            throw new Error('Malformed UTF-8 data');\n        }\n    }\n    /**\n     * Converts a UTF-8 string to a word array.\n     *\n     * \\@example\n     *\n     *     let wordArray = Utf8.parse(utf8String);\n     * @param {?} utf8Str The UTF-8 string.\n     *\n     * @return {?} The word array.\n     *\n     */\n    static parse(utf8Str) {\n        return Latin1.parse(unescape(encodeURIComponent(utf8Str)));\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @abstract\n */\nclass BufferedBlockAlgorithm {\n    /**\n     * @param {?=} cfg\n     */\n    constructor(cfg) {\n        this._minBufferSize = 0;\n        this.cfg = Object.assign({\n            blockSize: 1\n        }, cfg);\n        // Initial values\n        this._data = new WordArray();\n        this._nDataBytes = 0;\n    }\n    /**\n     * Resets this block algorithm's data buffer to its initial state.\n     *\n     * \\@example\n     *\n     *     bufferedBlockAlgorithm.reset();\n     * @return {?}\n     */\n    reset() {\n        // Initial values\n        this._data = new WordArray();\n        this._nDataBytes = 0;\n    }\n    /**\n     * Adds new data to this block algorithm's buffer.\n     *\n     * \\@example\n     *\n     *     bufferedBlockAlgorithm._append('data');\n     *     bufferedBlockAlgorithm._append(wordArray);\n     * @param {?} data The data to append. Strings are converted to a WordArray using UTF-8.\n     *\n     * @return {?}\n     */\n    _append(data) {\n        // Convert string to WordArray, else assume WordArray already\n        if (typeof data === 'string') {\n            data = Utf8.parse(data);\n        }\n        // Append\n        this._data.concat(data);\n        this._nDataBytes += data.sigBytes;\n    }\n    /**\n     * Processes available data blocks.\n     *\n     * This method invokes _doProcessBlock(offset), which must be implemented by a concrete subtype.\n     *\n     * \\@example\n     *\n     *     let processedData = bufferedBlockAlgorithm._process();\n     *     let processedData = bufferedBlockAlgorithm._process(!!'flush');\n     * @param {?=} doFlush Whether all blocks and partial blocks should be processed.\n     *\n     * @return {?} The processed data.\n     *\n     */\n    _process(doFlush) {\n        if (!this.cfg.blockSize) {\n            throw new Error('missing blockSize in config');\n        }\n        // Shortcuts\n        const /** @type {?} */ blockSizeBytes = this.cfg.blockSize * 4;\n        // Count blocks ready\n        let /** @type {?} */ nBlocksReady = this._data.sigBytes / blockSizeBytes;\n        if (doFlush) {\n            // Round up to include partial blocks\n            nBlocksReady = Math.ceil(nBlocksReady);\n        }\n        else {\n            // Round down to include only full blocks,\n            // less the number of blocks that must remain in the buffer\n            nBlocksReady = Math.max((nBlocksReady | 0) - this._minBufferSize, 0);\n        }\n        // Count words ready\n        const /** @type {?} */ nWordsReady = nBlocksReady * this.cfg.blockSize;\n        // Count bytes ready\n        const /** @type {?} */ nBytesReady = Math.min(nWordsReady * 4, this._data.sigBytes);\n        // Process blocks\n        let /** @type {?} */ processedWords;\n        if (nWordsReady) {\n            for (let /** @type {?} */ offset = 0; offset < nWordsReady; offset += this.cfg.blockSize) {\n                // Perform concrete-algorithm logic\n                this._doProcessBlock(this._data.words, offset);\n            }\n            // Remove processed words\n            processedWords = this._data.words.splice(0, nWordsReady);\n            this._data.sigBytes -= nBytesReady;\n        }\n        // Return processed words\n        return new WordArray(processedWords, nBytesReady);\n    }\n    /**\n     * Creates a copy of this object.\n     *\n     * \\@example\n     *\n     *     let clone = bufferedBlockAlgorithm.clone();\n     * @return {?} The clone.\n     *\n     */\n    clone() {\n        const /** @type {?} */ clone = this.constructor();\n        for (const /** @type {?} */ attr in this) {\n            if (this.hasOwnProperty(attr)) {\n                clone[attr] = this[attr];\n            }\n        }\n        clone._data = this._data.clone();\n        return clone;\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass Base {\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass CipherParams extends Base {\n    /**\n     * Initializes a newly created cipher params object.\n     *\n     * \\@example\n     *\n     *     let cipherParams = CipherParams.create({\n     *         ciphertext: ciphertextWordArray,\n     *         key: keyWordArray,\n     *         iv: ivWordArray,\n     *         salt: saltWordArray,\n     *         algorithm: AESAlgorithm,\n     *         mode: CBC,\n     *         padding: PKCS7,\n     *         blockSize: 4,\n     *         formatter: OpenSSLFormatter\n     *     });\n     * @param {?} cipherParams An object with any of the possible cipher parameters.\n     *\n     */\n    constructor(cipherParams) {\n        super();\n        this.ciphertext = cipherParams.ciphertext;\n        this.key = cipherParams.key;\n        this.iv = cipherParams.iv;\n        this.salt = cipherParams.salt;\n        this.algorithm = cipherParams.algorithm;\n        this.mode = cipherParams.mode;\n        this.padding = cipherParams.padding;\n        this.blockSize = cipherParams.blockSize;\n        this.formatter = cipherParams.formatter;\n    }\n    /**\n     * @param {?} additionalParams\n     * @return {?}\n     */\n    extend(additionalParams) {\n        if (additionalParams.ciphertext !== undefined) {\n            this.ciphertext = additionalParams.ciphertext;\n        }\n        if (additionalParams.key !== undefined) {\n            this.key = additionalParams.key;\n        }\n        if (additionalParams.iv !== undefined) {\n            this.iv = additionalParams.iv;\n        }\n        if (additionalParams.salt !== undefined) {\n            this.salt = additionalParams.salt;\n        }\n        if (additionalParams.algorithm !== undefined) {\n            this.algorithm = additionalParams.algorithm;\n        }\n        if (additionalParams.mode !== undefined) {\n            this.mode = additionalParams.mode;\n        }\n        if (additionalParams.padding !== undefined) {\n            this.padding = additionalParams.padding;\n        }\n        if (additionalParams.blockSize !== undefined) {\n            this.blockSize = additionalParams.blockSize;\n        }\n        if (additionalParams.formatter !== undefined) {\n            this.formatter = additionalParams.formatter;\n        }\n        return this;\n    }\n    /**\n     * Converts this cipher params object to a string.\n     *\n     * @throws Error If neither the formatter nor the default formatter is set.\n     *\n     * \\@example\n     *\n     *     let string = cipherParams + '';\n     *     let string = cipherParams.toString();\n     *     let string = cipherParams.toString(CryptoJS.format.OpenSSL);\n     * @param {?=} formatter (Optional) The formatting strategy to use.\n     *\n     * @return {?} The stringified cipher params.\n     *\n     */\n    toString(formatter) {\n        if (formatter) {\n            return formatter.stringify(this);\n        }\n        else if (this.formatter) {\n            return this.formatter.stringify(this);\n        }\n        else {\n            throw new Error('cipher needs a formatter to be able to convert the result into a string');\n        }\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass Base64 {\n    /**\n     * Converts a word array to a Base64 string.\n     *\n     * \\@example\n     *\n     *     let base64String = Base64.stringify(wordArray);\n     * @param {?} wordArray The word array.\n     *\n     * @return {?} The Base64 string.\n     *\n     */\n    static stringify(wordArray) {\n        // Clamp excess bits\n        wordArray.clamp();\n        // Convert\n        const /** @type {?} */ base64Chars = [];\n        for (let /** @type {?} */ i = 0; i < wordArray.sigBytes; i += 3) {\n            const /** @type {?} */ byte1 = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;\n            const /** @type {?} */ byte2 = (wordArray.words[(i + 1) >>> 2] >>> (24 - ((i + 1) % 4) * 8)) & 0xff;\n            const /** @type {?} */ byte3 = (wordArray.words[(i + 2) >>> 2] >>> (24 - ((i + 2) % 4) * 8)) & 0xff;\n            const /** @type {?} */ triplet = (byte1 << 16) | (byte2 << 8) | byte3;\n            for (let /** @type {?} */ j = 0; (j < 4) && (i + j * 0.75 < wordArray.sigBytes); j++) {\n                base64Chars.push(this._map.charAt((triplet >>> (6 * (3 - j))) & 0x3f));\n            }\n        }\n        // Add padding\n        const /** @type {?} */ paddingChar = this._map.charAt(64);\n        if (paddingChar) {\n            while (base64Chars.length % 4) {\n                base64Chars.push(paddingChar);\n            }\n        }\n        return base64Chars.join('');\n    }\n    /**\n     * Converts a Base64 string to a word array.\n     *\n     * \\@example\n     *\n     *     let wordArray = Base64.parse(base64String);\n     * @param {?} base64Str The Base64 string.\n     *\n     * @return {?} The word array.\n     *\n     */\n    static parse(base64Str) {\n        // Shortcuts\n        let /** @type {?} */ base64StrLength = base64Str.length;\n        if (this._reverseMap === undefined) {\n            this._reverseMap = [];\n            for (let /** @type {?} */ j = 0; j < this._map.length; j++) {\n                this._reverseMap[this._map.charCodeAt(j)] = j;\n            }\n        }\n        // Ignore padding\n        const /** @type {?} */ paddingChar = this._map.charAt(64);\n        if (paddingChar) {\n            const /** @type {?} */ paddingIndex = base64Str.indexOf(paddingChar);\n            if (paddingIndex !== -1) {\n                base64StrLength = paddingIndex;\n            }\n        }\n        // Convert\n        return this.parseLoop(base64Str, base64StrLength, this._reverseMap);\n    }\n    /**\n     * @param {?} base64Str\n     * @param {?} base64StrLength\n     * @param {?} reverseMap\n     * @return {?}\n     */\n    static parseLoop(base64Str, base64StrLength, reverseMap) {\n        const /** @type {?} */ words = [];\n        let /** @type {?} */ nBytes = 0;\n        for (let /** @type {?} */ i = 0; i < base64StrLength; i++) {\n            if (i % 4) {\n                const /** @type {?} */ bits1 = reverseMap[base64Str.charCodeAt(i - 1)] << ((i % 4) * 2);\n                const /** @type {?} */ bits2 = reverseMap[base64Str.charCodeAt(i)] >>> (6 - (i % 4) * 2);\n                words[nBytes >>> 2] |= (bits1 | bits2) << (24 - (nBytes % 4) * 8);\n                nBytes++;\n            }\n        }\n        return new WordArray(words, nBytes);\n    }\n}\nBase64._map = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\nBase64._reverseMap = undefined;\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass OpenSSL {\n    /**\n     * Converts a cipher params object to an OpenSSL-compatible string.\n     *\n     * \\@example\n     *\n     *     let openSSLString = OpenSSLFormatter.stringify(cipherParams);\n     * @param {?} cipherParams The cipher params object.\n     *\n     * @return {?} The OpenSSL-compatible string.\n     *\n     */\n    static stringify(cipherParams) {\n        if (!cipherParams.ciphertext) {\n            throw new Error('missing ciphertext in params');\n        }\n        // Shortcuts\n        const /** @type {?} */ ciphertext = cipherParams.ciphertext;\n        const /** @type {?} */ salt = cipherParams.salt;\n        // Format\n        let /** @type {?} */ wordArray;\n        if (salt) {\n            if (typeof salt === 'string') {\n                throw new Error('salt is expected to be a WordArray');\n            }\n            wordArray = (new WordArray([0x53616c74, 0x65645f5f])).concat(salt).concat(ciphertext);\n        }\n        else {\n            wordArray = ciphertext;\n        }\n        return wordArray.toString(Base64);\n    }\n    /**\n     * Converts an OpenSSL-compatible string to a cipher params object.\n     *\n     * \\@example\n     *\n     *     let cipherParams = OpenSSLFormatter.parse(openSSLString);\n     * @param {?} openSSLStr The OpenSSL-compatible string.\n     *\n     * @return {?} The cipher params object.\n     *\n     */\n    static parse(openSSLStr) {\n        // Parse base64\n        const /** @type {?} */ ciphertext = Base64.parse(openSSLStr);\n        // Test for salt\n        let /** @type {?} */ salt;\n        if (ciphertext.words[0] === 0x53616c74 && ciphertext.words[1] === 0x65645f5f) {\n            // Extract salt\n            salt = new WordArray(ciphertext.words.slice(2, 4));\n            // Remove salt from ciphertext\n            ciphertext.words.splice(0, 4);\n            ciphertext.sigBytes -= 16;\n        }\n        return new CipherParams({ ciphertext: ciphertext, salt: salt });\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass SerializableCipher {\n    /**\n     * Encrypts a message.\n     *\n     * \\@example\n     *\n     *     let ciphertextParams = SerializableCipher.encrypt(CryptoJS.algo.AES, message, key);\n     *     let ciphertextParams = SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv });\n     *     let ciphertextParams = SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, {\n     *       iv: iv,\n     *       format: CryptoJS.format.OpenSSL\n     *     });\n     * @param {?} cipher The cipher algorithm to use.\n     * @param {?} message The message to encrypt.\n     * @param {?} key The key.\n     * @param {?=} cfg (Optional) The configuration options to use for this operation.\n     *\n     * @return {?} A cipher params object.\n     *\n     */\n    static encrypt(cipher, message, key, cfg) {\n        // Apply config defaults\n        const /** @type {?} */ config = Object.assign({}, this.cfg, cfg);\n        // Encrypt\n        const /** @type {?} */ encryptor = cipher.createEncryptor(key, config);\n        const /** @type {?} */ ciphertext = encryptor.finalize(message);\n        // Create and return serializable cipher params\n        return new CipherParams({\n            ciphertext: ciphertext,\n            key: key,\n            iv: encryptor.cfg.iv,\n            algorithm: cipher,\n            mode: (/** @type {?} */ (encryptor.cfg)).mode,\n            padding: (/** @type {?} */ (encryptor.cfg)).padding,\n            blockSize: encryptor.cfg.blockSize,\n            formatter: config.format\n        });\n    }\n    /**\n     * Decrypts serialized ciphertext.\n     *\n     * \\@example\n     *\n     *     let plaintext = SerializableCipher.decrypt(\n     *         AESAlgorithm,\n     *         formattedCiphertext,\n     *         key, {\n     *             iv: iv,\n     *             format: CryptoJS.format.OpenSSL\n     *         }\n     *     );\n     *\n     *     let plaintext = SerializableCipher.decrypt(\n     *         AESAlgorithm,\n     *         ciphertextParams,\n     *         key, {\n     *             iv: iv,\n     *             format: CryptoJS.format.OpenSSL\n     *         }\n     *     );\n     * @param {?} cipher The cipher algorithm to use.\n     * @param {?} ciphertext The ciphertext to decrypt.\n     * @param {?} key The key.\n     * @param {?=} optionalCfg\n     * @return {?} The plaintext.\n     *\n     */\n    static decrypt(cipher, ciphertext, key, optionalCfg) {\n        // Apply config defaults\n        const /** @type {?} */ cfg = Object.assign({}, this.cfg, optionalCfg);\n        if (!cfg.format) {\n            throw new Error('could not determine format');\n        }\n        // Convert string to CipherParams\n        ciphertext = this._parse(ciphertext, cfg.format);\n        if (!ciphertext.ciphertext) {\n            throw new Error('could not determine ciphertext');\n        }\n        // Decrypt\n        const /** @type {?} */ plaintext = cipher.createDecryptor(key, cfg).finalize(ciphertext.ciphertext);\n        return plaintext;\n    }\n    /**\n     * Converts serialized ciphertext to CipherParams,\n     * else assumed CipherParams already and returns ciphertext unchanged.\n     *\n     * \\@example\n     *\n     *     var ciphertextParams = CryptoJS.lib.SerializableCipher._parse(ciphertextStringOrParams, format);\n     * @param {?} ciphertext The ciphertext.\n     * @param {?} format The formatting strategy to use to parse serialized ciphertext.\n     *\n     * @return {?} The unserialized ciphertext.\n     *\n     */\n    static _parse(ciphertext, format) {\n        if (typeof ciphertext === 'string') {\n            return format.parse(ciphertext);\n        }\n        else {\n            return ciphertext;\n        }\n    }\n}\nSerializableCipher.cfg = {\n    blockSize: 4,\n    iv: new WordArray([]),\n    format: OpenSSL\n};\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @abstract\n */\nclass Hasher extends BufferedBlockAlgorithm {\n    /**\n     * Creates a shortcut function to a hasher's object interface.\n     *\n     * \\@example\n     *\n     *     let SHA256 = Hasher._createHelper(SHA256);\n     * @param {?} hasher The hasher to create a helper for.\n     *\n     * @return {?} The shortcut function.\n     *\n     */\n    static _createHelper(hasher) {\n        /**\n         * @param {?} message\n         * @param {?=} cfg\n         * @return {?}\n         */\n        function helper(message, cfg) {\n            const /** @type {?} */ hasherClass = hasher;\n            const /** @type {?} */ hasherInstance = new hasherClass(cfg);\n            return hasherInstance.finalize(message);\n        }\n        return helper;\n    }\n    /**\n     * Initializes a newly created hasher.\n     *\n     * \\@example\n     *\n     *     let hasher = CryptoJS.algo.SHA256.create();\n     * @param {?=} cfg (Optional) The configuration options to use for this hash computation.\n     *\n     */\n    constructor(cfg) {\n        // Apply config defaults\n        super(Object.assign({\n            blockSize: 512 / 32\n        }, cfg));\n        // Set initial values\n        this.reset();\n    }\n    /**\n     * Updates this hasher with a message.\n     *\n     * \\@example\n     *\n     *     hasher.update('message');\n     *     hasher.update(wordArray);\n     * @param {?} messageUpdate The message to append.\n     *\n     * @return {?} This hasher.\n     *\n     */\n    update(messageUpdate) {\n        // Append\n        this._append(messageUpdate);\n        // Update the hash\n        this._process();\n        // Chainable\n        return this;\n    }\n    /**\n     * Finalizes the hash computation.\n     * Note that the finalize operation is effectively a destructive, read-once operation.\n     *\n     * \\@example\n     *\n     *     let hash = hasher.finalize();\n     *     let hash = hasher.finalize('message');\n     *     let hash = hasher.finalize(wordArray);\n     * @param {?} messageUpdate (Optional) A final message update.\n     *\n     * @return {?} The hash.\n     *\n     */\n    finalize(messageUpdate) {\n        // Final message update\n        if (messageUpdate) {\n            this._append(messageUpdate);\n        }\n        // Perform concrete-hasher logic\n        const /** @type {?} */ hash = this._doFinalize();\n        return hash;\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n// Constants table\nconst /** @type {?} */ T = [];\n// Compute constants\nfor (let /** @type {?} */ i = 0; i < 64; i++) {\n    T[i] = (Math.abs(Math.sin(i + 1)) * 0x100000000) | 0;\n}\nclass MD5 extends Hasher {\n    /**\n     * @param {?} a\n     * @param {?} b\n     * @param {?} c\n     * @param {?} d\n     * @param {?} x\n     * @param {?} s\n     * @param {?} t\n     * @return {?}\n     */\n    static FF(a, b, c, d, x, s, t) {\n        const /** @type {?} */ n = a + ((b & c) | (~b & d)) + x + t;\n        return ((n << s) | (n >>> (32 - s))) + b;\n    }\n    /**\n     * @param {?} a\n     * @param {?} b\n     * @param {?} c\n     * @param {?} d\n     * @param {?} x\n     * @param {?} s\n     * @param {?} t\n     * @return {?}\n     */\n    static GG(a, b, c, d, x, s, t) {\n        const /** @type {?} */ n = a + ((b & d) | (c & ~d)) + x + t;\n        return ((n << s) | (n >>> (32 - s))) + b;\n    }\n    /**\n     * @param {?} a\n     * @param {?} b\n     * @param {?} c\n     * @param {?} d\n     * @param {?} x\n     * @param {?} s\n     * @param {?} t\n     * @return {?}\n     */\n    static HH(a, b, c, d, x, s, t) {\n        const /** @type {?} */ n = a + (b ^ c ^ d) + x + t;\n        return ((n << s) | (n >>> (32 - s))) + b;\n    }\n    /**\n     * @param {?} a\n     * @param {?} b\n     * @param {?} c\n     * @param {?} d\n     * @param {?} x\n     * @param {?} s\n     * @param {?} t\n     * @return {?}\n     */\n    static II(a, b, c, d, x, s, t) {\n        const /** @type {?} */ n = a + (c ^ (b | ~d)) + x + t;\n        return ((n << s) | (n >>> (32 - s))) + b;\n    }\n    /**\n     * @return {?}\n     */\n    reset() {\n        // reset core values\n        super.reset();\n        this._hash = new WordArray([\n            0x67452301, 0xefcdab89,\n            0x98badcfe, 0x10325476\n        ]);\n    }\n    /**\n     * @param {?} M\n     * @param {?} offset\n     * @return {?}\n     */\n    _doProcessBlock(M, offset) {\n        // Swap endian\n        for (let /** @type {?} */ i = 0; i < 16; i++) {\n            // Shortcuts\n            const /** @type {?} */ offset_i = offset + i;\n            const /** @type {?} */ M_offset_i = M[offset_i];\n            M[offset_i] = ((((M_offset_i << 8) | (M_offset_i >>> 24)) & 0x00ff00ff) |\n                (((M_offset_i << 24) | (M_offset_i >>> 8)) & 0xff00ff00));\n        }\n        // Shortcuts\n        const /** @type {?} */ H = this._hash.words;\n        const /** @type {?} */ M_offset_0 = M[offset + 0];\n        const /** @type {?} */ M_offset_1 = M[offset + 1];\n        const /** @type {?} */ M_offset_2 = M[offset + 2];\n        const /** @type {?} */ M_offset_3 = M[offset + 3];\n        const /** @type {?} */ M_offset_4 = M[offset + 4];\n        const /** @type {?} */ M_offset_5 = M[offset + 5];\n        const /** @type {?} */ M_offset_6 = M[offset + 6];\n        const /** @type {?} */ M_offset_7 = M[offset + 7];\n        const /** @type {?} */ M_offset_8 = M[offset + 8];\n        const /** @type {?} */ M_offset_9 = M[offset + 9];\n        const /** @type {?} */ M_offset_10 = M[offset + 10];\n        const /** @type {?} */ M_offset_11 = M[offset + 11];\n        const /** @type {?} */ M_offset_12 = M[offset + 12];\n        const /** @type {?} */ M_offset_13 = M[offset + 13];\n        const /** @type {?} */ M_offset_14 = M[offset + 14];\n        const /** @type {?} */ M_offset_15 = M[offset + 15];\n        // Working variables\n        let /** @type {?} */ a = H[0];\n        let /** @type {?} */ b = H[1];\n        let /** @type {?} */ c = H[2];\n        let /** @type {?} */ d = H[3];\n        // Computation\n        a = MD5.FF(a, b, c, d, M_offset_0, 7, T[0]);\n        d = MD5.FF(d, a, b, c, M_offset_1, 12, T[1]);\n        c = MD5.FF(c, d, a, b, M_offset_2, 17, T[2]);\n        b = MD5.FF(b, c, d, a, M_offset_3, 22, T[3]);\n        a = MD5.FF(a, b, c, d, M_offset_4, 7, T[4]);\n        d = MD5.FF(d, a, b, c, M_offset_5, 12, T[5]);\n        c = MD5.FF(c, d, a, b, M_offset_6, 17, T[6]);\n        b = MD5.FF(b, c, d, a, M_offset_7, 22, T[7]);\n        a = MD5.FF(a, b, c, d, M_offset_8, 7, T[8]);\n        d = MD5.FF(d, a, b, c, M_offset_9, 12, T[9]);\n        c = MD5.FF(c, d, a, b, M_offset_10, 17, T[10]);\n        b = MD5.FF(b, c, d, a, M_offset_11, 22, T[11]);\n        a = MD5.FF(a, b, c, d, M_offset_12, 7, T[12]);\n        d = MD5.FF(d, a, b, c, M_offset_13, 12, T[13]);\n        c = MD5.FF(c, d, a, b, M_offset_14, 17, T[14]);\n        b = MD5.FF(b, c, d, a, M_offset_15, 22, T[15]);\n        a = MD5.GG(a, b, c, d, M_offset_1, 5, T[16]);\n        d = MD5.GG(d, a, b, c, M_offset_6, 9, T[17]);\n        c = MD5.GG(c, d, a, b, M_offset_11, 14, T[18]);\n        b = MD5.GG(b, c, d, a, M_offset_0, 20, T[19]);\n        a = MD5.GG(a, b, c, d, M_offset_5, 5, T[20]);\n        d = MD5.GG(d, a, b, c, M_offset_10, 9, T[21]);\n        c = MD5.GG(c, d, a, b, M_offset_15, 14, T[22]);\n        b = MD5.GG(b, c, d, a, M_offset_4, 20, T[23]);\n        a = MD5.GG(a, b, c, d, M_offset_9, 5, T[24]);\n        d = MD5.GG(d, a, b, c, M_offset_14, 9, T[25]);\n        c = MD5.GG(c, d, a, b, M_offset_3, 14, T[26]);\n        b = MD5.GG(b, c, d, a, M_offset_8, 20, T[27]);\n        a = MD5.GG(a, b, c, d, M_offset_13, 5, T[28]);\n        d = MD5.GG(d, a, b, c, M_offset_2, 9, T[29]);\n        c = MD5.GG(c, d, a, b, M_offset_7, 14, T[30]);\n        b = MD5.GG(b, c, d, a, M_offset_12, 20, T[31]);\n        a = MD5.HH(a, b, c, d, M_offset_5, 4, T[32]);\n        d = MD5.HH(d, a, b, c, M_offset_8, 11, T[33]);\n        c = MD5.HH(c, d, a, b, M_offset_11, 16, T[34]);\n        b = MD5.HH(b, c, d, a, M_offset_14, 23, T[35]);\n        a = MD5.HH(a, b, c, d, M_offset_1, 4, T[36]);\n        d = MD5.HH(d, a, b, c, M_offset_4, 11, T[37]);\n        c = MD5.HH(c, d, a, b, M_offset_7, 16, T[38]);\n        b = MD5.HH(b, c, d, a, M_offset_10, 23, T[39]);\n        a = MD5.HH(a, b, c, d, M_offset_13, 4, T[40]);\n        d = MD5.HH(d, a, b, c, M_offset_0, 11, T[41]);\n        c = MD5.HH(c, d, a, b, M_offset_3, 16, T[42]);\n        b = MD5.HH(b, c, d, a, M_offset_6, 23, T[43]);\n        a = MD5.HH(a, b, c, d, M_offset_9, 4, T[44]);\n        d = MD5.HH(d, a, b, c, M_offset_12, 11, T[45]);\n        c = MD5.HH(c, d, a, b, M_offset_15, 16, T[46]);\n        b = MD5.HH(b, c, d, a, M_offset_2, 23, T[47]);\n        a = MD5.II(a, b, c, d, M_offset_0, 6, T[48]);\n        d = MD5.II(d, a, b, c, M_offset_7, 10, T[49]);\n        c = MD5.II(c, d, a, b, M_offset_14, 15, T[50]);\n        b = MD5.II(b, c, d, a, M_offset_5, 21, T[51]);\n        a = MD5.II(a, b, c, d, M_offset_12, 6, T[52]);\n        d = MD5.II(d, a, b, c, M_offset_3, 10, T[53]);\n        c = MD5.II(c, d, a, b, M_offset_10, 15, T[54]);\n        b = MD5.II(b, c, d, a, M_offset_1, 21, T[55]);\n        a = MD5.II(a, b, c, d, M_offset_8, 6, T[56]);\n        d = MD5.II(d, a, b, c, M_offset_15, 10, T[57]);\n        c = MD5.II(c, d, a, b, M_offset_6, 15, T[58]);\n        b = MD5.II(b, c, d, a, M_offset_13, 21, T[59]);\n        a = MD5.II(a, b, c, d, M_offset_4, 6, T[60]);\n        d = MD5.II(d, a, b, c, M_offset_11, 10, T[61]);\n        c = MD5.II(c, d, a, b, M_offset_2, 15, T[62]);\n        b = MD5.II(b, c, d, a, M_offset_9, 21, T[63]);\n        // Intermediate hash value\n        H[0] = (H[0] + a) | 0;\n        H[1] = (H[1] + b) | 0;\n        H[2] = (H[2] + c) | 0;\n        H[3] = (H[3] + d) | 0;\n    }\n    /**\n     * @return {?}\n     */\n    _doFinalize() {\n        // Shortcuts\n        const /** @type {?} */ data = this._data;\n        const /** @type {?} */ dataWords = data.words;\n        const /** @type {?} */ nBitsTotal = this._nDataBytes * 8;\n        const /** @type {?} */ nBitsLeft = data.sigBytes * 8;\n        // Add padding\n        dataWords[nBitsLeft >>> 5] |= 0x80 << (24 - nBitsLeft % 32);\n        const /** @type {?} */ nBitsTotalH = Math.floor(nBitsTotal / 0x100000000);\n        const /** @type {?} */ nBitsTotalL = nBitsTotal;\n        dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 15] = ((((nBitsTotalH << 8) | (nBitsTotalH >>> 24)) & 0x00ff00ff) |\n            (((nBitsTotalH << 24) | (nBitsTotalH >>> 8)) & 0xff00ff00));\n        dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 14] = ((((nBitsTotalL << 8) | (nBitsTotalL >>> 24)) & 0x00ff00ff) |\n            (((nBitsTotalL << 24) | (nBitsTotalL >>> 8)) & 0xff00ff00));\n        data.sigBytes = (dataWords.length + 1) * 4;\n        // Hash final blocks\n        this._process();\n        // Shortcuts\n        const /** @type {?} */ hash = this._hash;\n        const /** @type {?} */ H = hash.words;\n        // Swap endian\n        for (let /** @type {?} */ i = 0; i < 4; i++) {\n            // Shortcut\n            const /** @type {?} */ H_i = H[i];\n            H[i] = (((H_i << 8) | (H_i >>> 24)) & 0x00ff00ff) |\n                (((H_i << 24) | (H_i >>> 8)) & 0xff00ff00);\n        }\n        // Return final computed hash\n        return hash;\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass EvpKDF {\n    /**\n     * Initializes a newly created key derivation function.\n     *\n     * \\@example\n     *\n     *     let kdf = EvpKDF.create();\n     *     let kdf = EvpKDF.create({ keySize: 8 });\n     *     let kdf = EvpKDF.create({ keySize: 8, iterations: 1000 });\n     * @param {?=} cfg (Optional) The configuration options to use for the derivation.\n     *\n     */\n    constructor(cfg) {\n        this.cfg = Object.assign({\n            keySize: 128 / 32,\n            hasher: MD5,\n            iterations: 1\n        }, cfg);\n    }\n    /**\n     * Derives a key from a password.\n     *\n     * \\@example\n     *\n     *     let key = kdf.compute(password, salt);\n     * @param {?} password The password.\n     * @param {?} salt A salt.\n     *\n     * @return {?} The derived key.\n     *\n     */\n    compute(password, salt) {\n        // Init hasher\n        const /** @type {?} */ hasher = new (/** @type {?} */ (this.cfg.hasher))();\n        // Initial values\n        const /** @type {?} */ derivedKey = new WordArray();\n        // Generate key\n        let /** @type {?} */ block;\n        while (derivedKey.words.length < this.cfg.keySize) {\n            if (block) {\n                hasher.update(block);\n            }\n            block = hasher.update(password).finalize(salt);\n            hasher.reset();\n            // Iterations\n            for (let /** @type {?} */ i = 1; i < this.cfg.iterations; i++) {\n                block = hasher.finalize(block);\n                hasher.reset();\n            }\n            derivedKey.concat(block);\n        }\n        derivedKey.sigBytes = this.cfg.keySize * 4;\n        return derivedKey;\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass OpenSSLKdf {\n    /**\n     * Derives a key and IV from a password.\n     *\n     * \\@example\n     *\n     *     let derivedParams = OpenSSL.execute('Password', 256/32, 128/32);\n     *     let derivedParams = OpenSSL.execute('Password', 256/32, 128/32, 'saltsalt');\n     * @param {?} password The password to derive from.\n     * @param {?} keySize The size in words of the key to generate.\n     * @param {?} ivSize The size in words of the IV to generate.\n     * @param {?=} salt (Optional) A 64-bit salt to use. If omitted, a salt will be generated randomly.\n     *\n     * @return {?} A cipher params object with the key, IV, and salt.\n     *\n     */\n    static execute(password, keySize, ivSize, salt) {\n        // Generate random salt\n        if (!salt) {\n            salt = WordArray.random(64 / 8);\n        }\n        // Derive key and IV\n        const /** @type {?} */ key = (new EvpKDF({ keySize: keySize + ivSize })).compute(password, salt);\n        // Separate key and IV\n        const /** @type {?} */ iv = new WordArray(key.words.slice(keySize), ivSize * 4);\n        key.sigBytes = keySize * 4;\n        // Return params\n        return new CipherParams({ key: key, iv: iv, salt: salt });\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass PasswordBasedCipher {\n    /**\n     * Encrypts a message using a password.\n     *\n     * \\@example\n     *\n     *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(AES, message, 'password');\n     *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(AES, message, 'password', { format: OpenSSL });\n     * @param {?} cipher The cipher algorithm to use.\n     * @param {?} message The message to encrypt.\n     * @param {?} password The password.\n     * @param {?=} cfg (Optional) The configuration options to use for this operation.\n     *\n     * @return {?} A cipher params object.\n     *\n     */\n    static encrypt(cipher, message, password, cfg) {\n        // Apply config defaults\n        const /** @type {?} */ config = Object.assign({}, this.cfg, cfg);\n        // Check if we have a kdf\n        if (config.kdf === undefined) {\n            throw new Error('missing kdf in config');\n        }\n        // Derive key and other params\n        const /** @type {?} */ derivedParams = config.kdf.execute(password, cipher.keySize, cipher.ivSize);\n        // Check if we have an IV\n        if (derivedParams.iv !== undefined) {\n            // Add IV to config\n            config.iv = derivedParams.iv;\n        }\n        // Encrypt\n        const /** @type {?} */ ciphertext = SerializableCipher.encrypt.call(this, cipher, message, derivedParams.key, config);\n        // Mix in derived params\n        return ciphertext.extend(derivedParams);\n    }\n    /**\n     * Decrypts serialized ciphertext using a password.\n     *\n     * \\@example\n     *\n     *     var plaintext = PasswordBasedCipher.decrypt(AES, formattedCiphertext, 'password', { format: OpenSSL });\n     *     var plaintext = PasswordBasedCipher.decrypt(AES, ciphertextParams, 'password', { format: OpenSSL });\n     * @param {?} cipher The cipher algorithm to use.\n     * @param {?} ciphertext The ciphertext to decrypt.\n     * @param {?} password The password.\n     * @param {?=} cfg (Optional) The configuration options to use for this operation.\n     *\n     * @return {?} The plaintext.\n     *\n     */\n    static decrypt(cipher, ciphertext, password, cfg) {\n        // Apply config defaults\n        const /** @type {?} */ config = Object.assign({}, this.cfg, cfg);\n        // Check if we have a kdf\n        if (config.format === undefined) {\n            throw new Error('missing format in config');\n        }\n        // Convert string to CipherParams\n        ciphertext = this._parse(ciphertext, config.format);\n        // Check if we have a kdf\n        if (config.kdf === undefined) {\n            throw new Error('the key derivation function must be set');\n        }\n        // Derive key and other params\n        const /** @type {?} */ derivedParams = config.kdf.execute(password, cipher.keySize, cipher.ivSize, ciphertext.salt);\n        // Check if we have an IV\n        if (derivedParams.iv !== undefined) {\n            // Add IV to config\n            config.iv = derivedParams.iv;\n        }\n        // Decrypt\n        const /** @type {?} */ plaintext = SerializableCipher.decrypt.call(this, cipher, ciphertext, derivedParams.key, config);\n        return plaintext;\n    }\n    /**\n     * Converts serialized ciphertext to CipherParams,\n     * else assumed CipherParams already and returns ciphertext unchanged.\n     *\n     * \\@example\n     *\n     *     var ciphertextParams = CryptoJS.lib.SerializableCipher._parse(ciphertextStringOrParams, format);\n     * @param {?} ciphertext The ciphertext.\n     * @param {?} format The formatting strategy to use to parse serialized ciphertext.\n     *\n     * @return {?} The unserialized ciphertext.\n     *\n     */\n    static _parse(ciphertext, format) {\n        if (typeof ciphertext === 'string') {\n            return format.parse(ciphertext);\n        }\n        else {\n            return ciphertext;\n        }\n    }\n}\nPasswordBasedCipher.cfg = {\n    blockSize: 4,\n    iv: new WordArray([]),\n    format: OpenSSL,\n    kdf: OpenSSLKdf\n};\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @abstract\n */\nclass Cipher extends BufferedBlockAlgorithm {\n    /**\n     * Initializes a newly created cipher.\n     *\n     * \\@example\n     *\n     *     let cipher = AES.create(AES._ENC_XFORM_MODE, keyWordArray, { iv: ivWordArray });\n     * @param {?} xformMode Either the encryption or decryption transormation mode constant.\n     * @param {?} key The key.\n     * @param {?=} cfg (Optional) The configuration options to use for this operation.\n     *\n     */\n    constructor(xformMode, key, cfg) {\n        // Apply config defaults\n        super(Object.assign({\n            blockSize: 1\n        }, cfg));\n        // Store transform mode and key\n        this._xformMode = xformMode;\n        this._key = key;\n        // Set initial values\n        this.reset();\n    }\n    /**\n     * Creates this cipher in encryption mode.\n     *\n     * \\@example\n     *\n     *     let cipher = AES.createEncryptor(keyWordArray, { iv: ivWordArray });\n     * @param {?} key The key.\n     * @param {?=} cfg (Optional) The configuration options to use for this operation.\n     *\n     * @return {?} A cipher instance.\n     *\n     */\n    static createEncryptor(key, cfg) {\n        // workaround for typescript not being able to create a abstract creator function directly\n        const /** @type {?} */ thisClass = this;\n        return new thisClass(this._ENC_XFORM_MODE, key, cfg);\n    }\n    /**\n     * Creates this cipher in decryption mode.\n     *\n     * \\@example\n     *\n     *     let cipher = AES.createDecryptor(keyWordArray, { iv: ivWordArray });\n     * @param {?} key The key.\n     * @param {?=} cfg (Optional) The configuration options to use for this operation.\n     *\n     * @return {?} A cipher instance.\n     *\n     */\n    static createDecryptor(key, cfg) {\n        // workaround for typescript not being able to create a abstract creator function directly\n        const /** @type {?} */ thisClass = this;\n        return new thisClass(this._DEC_XFORM_MODE, key, cfg);\n    }\n    /**\n     * Creates shortcut functions to a cipher's object interface.\n     *\n     * \\@example\n     *\n     *     let AES = Cipher._createHelper(AESAlgorithm);\n     * @param {?} cipher The cipher to create a helper for.\n     *\n     * @return {?} An object with encrypt and decrypt shortcut functions.\n     *\n     */\n    static _createHelper(cipher) {\n        /**\n         * @param {?} message\n         * @param {?} key\n         * @param {?=} cfg\n         * @return {?}\n         */\n        function encrypt(message, key, cfg) {\n            if (typeof key === 'string') {\n                return PasswordBasedCipher.encrypt(cipher, message, key, cfg);\n            }\n            else {\n                return SerializableCipher.encrypt(cipher, message, key, cfg);\n            }\n        }\n        /**\n         * @param {?} ciphertext\n         * @param {?} key\n         * @param {?=} cfg\n         * @return {?}\n         */\n        function decrypt(ciphertext, key, cfg) {\n            if (typeof key === 'string') {\n                return PasswordBasedCipher.decrypt(cipher, ciphertext, key, cfg);\n            }\n            else {\n                return SerializableCipher.decrypt(cipher, ciphertext, key, cfg);\n            }\n        }\n        return {\n            encrypt: encrypt,\n            decrypt: decrypt\n        };\n    }\n    /**\n     * Adds data to be encrypted or decrypted.\n     *\n     * \\@example\n     *\n     *     let encrypted = cipher.process('data');\n     *     let encrypted = cipher.process(wordArray);\n     * @param {?} dataUpdate The data to encrypt or decrypt.\n     *\n     * @return {?} The data after processing.\n     *\n     */\n    process(dataUpdate) {\n        // Append\n        this._append(dataUpdate);\n        // Process available blocks\n        return this._process();\n    }\n    /**\n     * Finalizes the encryption or decryption process.\n     * Note that the finalize operation is effectively a destructive, read-once operation.\n     *\n     * \\@example\n     *\n     *     var encrypted = cipher.finalize();\n     *     var encrypted = cipher.finalize('data');\n     *     var encrypted = cipher.finalize(wordArray);\n     * @param {?=} dataUpdate The final data to encrypt or decrypt.\n     *\n     * @return {?} The data after final processing.\n     *\n     */\n    finalize(dataUpdate) {\n        // Final data update\n        if (dataUpdate) {\n            this._append(dataUpdate);\n        }\n        // Perform concrete-cipher logic\n        const /** @type {?} */ finalProcessedData = this._doFinalize();\n        return finalProcessedData;\n    }\n}\n/**\n * A constant representing encryption mode.\n */\nCipher._ENC_XFORM_MODE = 1;\n/**\n * A constant representing decryption mode.\n */\nCipher._DEC_XFORM_MODE = 2;\n/**\n * This cipher's key size. Default: 4 (128 bits / 32 Bits)\n */\nCipher.keySize = 4;\n/**\n * This cipher's IV size. Default: 4 (128 bits / 32 Bits)\n */\nCipher.ivSize = 4;\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @abstract\n */\nclass BlockCipherModeAlgorithm {\n    /**\n     * @param {?} cipher\n     * @param {?} iv\n     */\n    constructor(cipher, iv) {\n        this.init(cipher, iv);\n    }\n    /**\n     * Initializes a newly created mode.\n     *\n     * \\@example\n     *\n     *     var mode = CBC.Encryptor.create(cipher, iv.words);\n     * @param {?} cipher A block cipher instance.\n     * @param {?=} iv The IV words.\n     *\n     * @return {?}\n     */\n    init(cipher, iv) {\n        this._cipher = cipher;\n        this._iv = iv;\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @abstract\n */\nclass BlockCipherMode {\n    /**\n     * Creates this mode for encryption.\n     *\n     * \\@example\n     *\n     *     var mode = CBC.createEncryptor(cipher, iv.words);\n     * @param {?} cipher A block cipher instance.\n     * @param {?} iv The IV words.\n     *\n     * @return {?}\n     */\n    static createEncryptor(cipher, iv) {\n        // workaround for typescript not being able to create a abstract creator function directly\n        const /** @type {?} */ encryptorClass = this.Encryptor;\n        return new encryptorClass(cipher, iv);\n    }\n    /**\n     * Creates this mode for decryption.\n     *\n     * \\@example\n     *\n     *     var mode = CBC.createDecryptor(cipher, iv.words);\n     * @param {?} cipher A block cipher instance.\n     * @param {?} iv The IV words.\n     *\n     * @return {?}\n     */\n    static createDecryptor(cipher, iv) {\n        // workaround for typescript not being able to create a abstract creator function directly\n        const /** @type {?} */ decryptorClass = this.Decryptor;\n        return new decryptorClass(cipher, iv);\n    }\n}\nBlockCipherMode.Encryptor = BlockCipherModeAlgorithm;\nBlockCipherMode.Decryptor = BlockCipherModeAlgorithm;\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass CBCEncryptor extends BlockCipherModeAlgorithm {\n    /**\n     * Processes the data block at offset.\n     *\n     * \\@example\n     *\n     *     mode.processBlock(data.words, offset);\n     * @param {?} words The data words to operate on.\n     * @param {?} offset The offset where the block starts.\n     *\n     * @return {?}\n     */\n    processBlock(words, offset) {\n        // Check if we have a blockSize\n        if (this._cipher.cfg.blockSize === undefined) {\n            throw new Error('missing blockSize in cipher config');\n        }\n        // XOR and encrypt\n        this.xorBlock(words, offset, this._cipher.cfg.blockSize);\n        this._cipher.encryptBlock(words, offset);\n        // Remember this block to use with next block\n        this._prevBlock = words.slice(offset, offset + this._cipher.cfg.blockSize);\n    }\n    /**\n     * @param {?} words\n     * @param {?} offset\n     * @param {?} blockSize\n     * @return {?}\n     */\n    xorBlock(words, offset, blockSize) {\n        // Choose mixing block\n        let /** @type {?} */ block;\n        if (this._iv) {\n            block = this._iv;\n            // Remove IV for subsequent blocks\n            this._iv = undefined;\n        }\n        else {\n            block = this._prevBlock;\n        }\n        // block should never be undefined but we want to make typescript happy\n        if (block !== undefined) {\n            // XOR blocks\n            for (let /** @type {?} */ i = 0; i < blockSize; i++) {\n                words[offset + i] ^= block[i];\n            }\n        }\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass CBCDecryptor extends BlockCipherModeAlgorithm {\n    /**\n     * Processes the data block at offset.\n     *\n     * \\@example\n     *\n     *     mode.processBlock(data.words, offset);\n     * @param {?} words The data words to operate on.\n     * @param {?} offset The offset where the block starts.\n     *\n     * @return {?}\n     */\n    processBlock(words, offset) {\n        // Check if we have a blockSize\n        if (this._cipher.cfg.blockSize === undefined) {\n            throw new Error('missing blockSize in cipher config');\n        }\n        // Remember this block to use with next block\n        const /** @type {?} */ thisBlock = words.slice(offset, offset + this._cipher.cfg.blockSize);\n        // Decrypt and XOR\n        this._cipher.decryptBlock(words, offset);\n        this.xorBlock(words, offset, this._cipher.cfg.blockSize);\n        // This block becomes the previous block\n        this._prevBlock = thisBlock;\n    }\n    /**\n     * @param {?} words\n     * @param {?} offset\n     * @param {?} blockSize\n     * @return {?}\n     */\n    xorBlock(words, offset, blockSize) {\n        // Choose mixing block\n        let /** @type {?} */ block;\n        if (this._iv) {\n            block = this._iv;\n            // Remove IV for subsequent blocks\n            this._iv = undefined;\n        }\n        else {\n            block = this._prevBlock;\n        }\n        // block should never be undefined but we want to make typescript happy\n        if (block !== undefined) {\n            // XOR blocks\n            for (let /** @type {?} */ i = 0; i < blockSize; i++) {\n                words[offset + i] ^= block[i];\n            }\n        }\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Cipher Block Chaining mode.\n * @abstract\n */\nclass CBC extends BlockCipherMode {\n}\nCBC.Encryptor = CBCEncryptor;\nCBC.Decryptor = CBCDecryptor;\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass PKCS7 {\n    /**\n     * Pads data using the algorithm defined in PKCS #5/7.\n     *\n     * \\@example\n     *\n     *     PKCS7.pad(wordArray, 4);\n     * @param {?} data The data to pad.\n     * @param {?} blockSize The multiple that the data should be padded to.\n     *\n     * @return {?}\n     */\n    static pad(data, blockSize) {\n        // Shortcut\n        const /** @type {?} */ blockSizeBytes = blockSize * 4;\n        // Count padding bytes\n        const /** @type {?} */ nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n        // Create padding word\n        const /** @type {?} */ paddingWord = (nPaddingBytes << 24) | (nPaddingBytes << 16) | (nPaddingBytes << 8) | nPaddingBytes;\n        // Create padding\n        const /** @type {?} */ paddingWords = [];\n        for (let /** @type {?} */ i = 0; i < nPaddingBytes; i += 4) {\n            paddingWords.push(paddingWord);\n        }\n        const /** @type {?} */ padding = new WordArray(paddingWords, nPaddingBytes);\n        // Add padding\n        data.concat(padding);\n    }\n    /**\n     * Unpads data that had been padded using the algorithm defined in PKCS #5/7.\n     *\n     * \\@example\n     *\n     *     PKCS7.unpad(wordArray);\n     * @param {?} data The data to unpad.\n     *\n     * @return {?}\n     */\n    static unpad(data) {\n        // Get number of padding bytes from last byte\n        const /** @type {?} */ nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;\n        // Remove padding\n        data.sigBytes -= nPaddingBytes;\n    }\n}\n\n\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass ZeroPadding {\n   \n    static pad(data, blockSize) {\n        // Shortcut\n        const /** @type {?} */ blockSizeBytes = blockSize * 4;\n        // Count padding bytes\n        const /** @type {?} */ nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n        // Create padding\n        const /** @type {?} */ paddingWords = [];\n        for (let /** @type {?} */ i = 0; i < nPaddingBytes; i += 4) {\n            paddingWords.push(0);\n        }\n        const /** @type {?} */ padding = new WordArray(paddingWords, nPaddingBytes);\n        // Add padding\n        data.concat(padding);\n    }\n    \n\n    static unpad(data) {\n        \n        // console.log(\"unpad 1:\", data.clone());\n        for (let i = data.words.length - 1; i>=0; i--) {\n            if(data.words[i] != 0){\n                data.slice(0, i+1);\n                // console.log(\"unpad 2:\", data);\n                return\n            }\n        }\n        \n    }\n}\n\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * @abstract\n */\nclass BlockCipher extends Cipher {\n    /**\n     * @param {?} xformMode\n     * @param {?} key\n     * @param {?=} cfg\n     */\n    constructor(xformMode, key, cfg) {\n        super(xformMode, key, Object.assign({\n            // default: 128 / 32\n            blockSize: 4,\n            mode: CBC,\n            padding: PKCS7\n        }, cfg));\n    }\n    /**\n     * @return {?}\n     */\n    reset() {\n        // Reset cipher\n        super.reset();\n        // Check if we have a blockSize\n        if (this.cfg.mode === undefined) {\n            throw new Error('missing mode in config');\n        }\n        // Reset block mode\n        let /** @type {?} */ modeCreator;\n        if (this._xformMode === (/** @type {?} */ (this.constructor))._ENC_XFORM_MODE) {\n            modeCreator = this.cfg.mode.createEncryptor;\n        }\n        else /* if (this._xformMode == this._DEC_XFORM_MODE) */ {\n            modeCreator = this.cfg.mode.createDecryptor;\n            // Keep at least one block in the buffer for unpadding\n            this._minBufferSize = 1;\n        }\n        if (this._mode && this._mode.__creator === modeCreator) {\n            this._mode.init(this, this.cfg.iv && this.cfg.iv.words);\n        }\n        else {\n            this._mode = modeCreator.call(this.cfg.mode, this, this.cfg.iv && this.cfg.iv.words);\n            this._mode.__creator = modeCreator;\n        }\n    }\n    /**\n     * @param {?} words\n     * @param {?} offset\n     * @return {?}\n     */\n    _doProcessBlock(words, offset) {\n        this._mode.processBlock(words, offset);\n    }\n    /**\n     * @return {?}\n     */\n    _doFinalize() {\n        // Check if we have a padding strategy\n        if (this.cfg.padding === undefined) {\n            throw new Error('missing padding in config');\n        }\n        // Finalize\n        let /** @type {?} */ finalProcessedBlocks;\n        if (this._xformMode === (/** @type {?} */ (this.constructor))._ENC_XFORM_MODE) {\n            // Check if we have a blockSize\n            if (this.cfg.blockSize === undefined) {\n                throw new Error('missing blockSize in config');\n            }\n            // Pad data\n            this.cfg.padding.pad(this._data, this.cfg.blockSize);\n            // Process final blocks\n            finalProcessedBlocks = this._process(!!'flush');\n        }\n        else /* if (this._xformMode == this._DEC_XFORM_MODE) */ {\n            // Process final blocks\n            finalProcessedBlocks = this._process(!!'flush');\n            // Unpad data\n            this.cfg.padding.unpad(finalProcessedBlocks);\n        }\n        return finalProcessedBlocks;\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n// Define lookup tables\nconst /** @type {?} */ SBOX = [];\nconst /** @type {?} */ INV_SBOX = [];\nconst /** @type {?} */ SUB_MIX_0 = [];\nconst /** @type {?} */ SUB_MIX_1 = [];\nconst /** @type {?} */ SUB_MIX_2 = [];\nconst /** @type {?} */ SUB_MIX_3 = [];\nconst /** @type {?} */ INV_SUB_MIX_0 = [];\nconst /** @type {?} */ INV_SUB_MIX_1 = [];\nconst /** @type {?} */ INV_SUB_MIX_2 = [];\nconst /** @type {?} */ INV_SUB_MIX_3 = [];\n// Compute lookup tables\n(function () {\n    // Compute double table\n    const /** @type {?} */ d = [];\n    for (let /** @type {?} */ i = 0; i < 256; i++) {\n        if (i < 128) {\n            d[i] = i << 1;\n        }\n        else {\n            d[i] = (i << 1) ^ 0x11b;\n        }\n    }\n    // Walk GF(2^8)\n    let /** @type {?} */ x = 0;\n    let /** @type {?} */ xi = 0;\n    for (let /** @type {?} */ i = 0; i < 256; i++) {\n        // Compute sbox\n        let /** @type {?} */ sx = xi ^ (xi << 1) ^ (xi << 2) ^ (xi << 3) ^ (xi << 4);\n        sx = (sx >>> 8) ^ (sx & 0xff) ^ 0x63;\n        SBOX[x] = sx;\n        INV_SBOX[sx] = x;\n        // Compute multiplication\n        const /** @type {?} */ x2 = d[x];\n        const /** @type {?} */ x4 = d[x2];\n        const /** @type {?} */ x8 = d[x4];\n        // Compute sub bytes, mix columns tables\n        let /** @type {?} */ t = (d[sx] * 0x101) ^ (sx * 0x1010100);\n        SUB_MIX_0[x] = (t << 24) | (t >>> 8);\n        SUB_MIX_1[x] = (t << 16) | (t >>> 16);\n        SUB_MIX_2[x] = (t << 8) | (t >>> 24);\n        SUB_MIX_3[x] = t;\n        // Compute inv sub bytes, inv mix columns tables\n        t = (x8 * 0x1010101) ^ (x4 * 0x10001) ^ (x2 * 0x101) ^ (x * 0x1010100);\n        INV_SUB_MIX_0[sx] = (t << 24) | (t >>> 8);\n        INV_SUB_MIX_1[sx] = (t << 16) | (t >>> 16);\n        INV_SUB_MIX_2[sx] = (t << 8) | (t >>> 24);\n        INV_SUB_MIX_3[sx] = t;\n        // Compute next counter\n        if (!x) {\n            x = xi = 1;\n        }\n        else {\n            x = x2 ^ d[d[d[x8 ^ x2]]];\n            xi ^= d[d[xi]];\n        }\n    }\n}());\n// Precomputed Rcon lookup\nconst /** @type {?} */ RCON = [0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36];\nclass AES extends BlockCipher {\n    /**\n     * @param {?} xformMode\n     * @param {?} key\n     * @param {?=} cfg\n     */\n    constructor(xformMode, key, cfg) {\n        super(xformMode, key, cfg);\n    }\n    /**\n     * @return {?}\n     */\n    reset() {\n        // reset core values\n        super.reset();\n        // Skip reset of nRounds has been set before and key did not change\n        if (this._nRounds && this._keyPriorReset === this._key) {\n            return;\n        }\n        // Shortcuts\n        const /** @type {?} */ key = this._keyPriorReset = this._key;\n        const /** @type {?} */ keyWords = key.words;\n        const /** @type {?} */ keySize = key.sigBytes / 4;\n        // Compute number of rounds\n        const /** @type {?} */ nRounds = this._nRounds = keySize + 6;\n        // Compute number of key schedule rows\n        const /** @type {?} */ ksRows = (nRounds + 1) * 4;\n        // Compute key schedule\n        const /** @type {?} */ keySchedule = this._keySchedule = [];\n        for (let /** @type {?} */ ksRow = 0; ksRow < ksRows; ksRow++) {\n            if (ksRow < keySize) {\n                keySchedule[ksRow] = keyWords[ksRow];\n            }\n            else {\n                let /** @type {?} */ t = keySchedule[ksRow - 1];\n                if (!(ksRow % keySize)) {\n                    // Rot word\n                    t = (t << 8) | (t >>> 24);\n                    // Sub word\n                    t = (SBOX[t >>> 24] << 24) | (SBOX[(t >>> 16) & 0xff] << 16) | (SBOX[(t >>> 8) & 0xff] << 8) | SBOX[t & 0xff];\n                    // Mix Rcon\n                    t ^= RCON[(ksRow / keySize) | 0] << 24;\n                }\n                else if (keySize > 6 && ksRow % keySize === 4) {\n                    // Sub word\n                    t = (SBOX[t >>> 24] << 24) | (SBOX[(t >>> 16) & 0xff] << 16) | (SBOX[(t >>> 8) & 0xff] << 8) | SBOX[t & 0xff];\n                }\n                keySchedule[ksRow] = keySchedule[ksRow - keySize] ^ t;\n            }\n        }\n        // Compute inv key schedule\n        const /** @type {?} */ invKeySchedule = this._invKeySchedule = [];\n        for (let /** @type {?} */ invKsRow = 0; invKsRow < ksRows; invKsRow++) {\n            const /** @type {?} */ ksRow = ksRows - invKsRow;\n            let /** @type {?} */ t;\n            if (invKsRow % 4) {\n                t = keySchedule[ksRow];\n            }\n            else {\n                t = keySchedule[ksRow - 4];\n            }\n            if (invKsRow < 4 || ksRow <= 4) {\n                invKeySchedule[invKsRow] = t;\n            }\n            else {\n                invKeySchedule[invKsRow] = INV_SUB_MIX_0[SBOX[t >>> 24]] ^ INV_SUB_MIX_1[SBOX[(t >>> 16) & 0xff]] ^\n                    INV_SUB_MIX_2[SBOX[(t >>> 8) & 0xff]] ^ INV_SUB_MIX_3[SBOX[t & 0xff]];\n            }\n        }\n    }\n    /**\n     * @param {?} M\n     * @param {?} offset\n     * @return {?}\n     */\n    encryptBlock(M, offset) {\n        this._doCryptBlock(M, offset, this._keySchedule, SUB_MIX_0, SUB_MIX_1, SUB_MIX_2, SUB_MIX_3, SBOX);\n    }\n    /**\n     * @param {?} M\n     * @param {?} offset\n     * @return {?}\n     */\n    decryptBlock(M, offset) {\n        // Swap 2nd and 4th rows\n        let /** @type {?} */ t = M[offset + 1];\n        M[offset + 1] = M[offset + 3];\n        M[offset + 3] = t;\n        this._doCryptBlock(M, offset, this._invKeySchedule, INV_SUB_MIX_0, INV_SUB_MIX_1, INV_SUB_MIX_2, INV_SUB_MIX_3, INV_SBOX);\n        // Inv swap 2nd and 4th rows\n        t = M[offset + 1];\n        M[offset + 1] = M[offset + 3];\n        M[offset + 3] = t;\n    }\n    /**\n     * @param {?} M\n     * @param {?} offset\n     * @param {?} keySchedule\n     * @param {?} sub_mix_0\n     * @param {?} sub_mix_1\n     * @param {?} sub_mix_2\n     * @param {?} sub_mix_3\n     * @param {?} sbox\n     * @return {?}\n     */\n    _doCryptBlock(M, offset, keySchedule, sub_mix_0, sub_mix_1, sub_mix_2, sub_mix_3, sbox) {\n        // Get input, add round key\n        let /** @type {?} */ s0 = M[offset] ^ keySchedule[0];\n        let /** @type {?} */ s1 = M[offset + 1] ^ keySchedule[1];\n        let /** @type {?} */ s2 = M[offset + 2] ^ keySchedule[2];\n        let /** @type {?} */ s3 = M[offset + 3] ^ keySchedule[3];\n        // Key schedule row counter\n        let /** @type {?} */ ksRow = 4;\n        // Rounds\n        for (let /** @type {?} */ round = 1; round < this._nRounds; round++) {\n            // Shift rows, sub bytes, mix columns, add round key\n            const /** @type {?} */ t0 = sub_mix_0[s0 >>> 24] ^ sub_mix_1[(s1 >>> 16) & 0xff] ^ sub_mix_2[(s2 >>> 8) & 0xff] ^ sub_mix_3[s3 & 0xff] ^\n                keySchedule[ksRow++];\n            const /** @type {?} */ t1 = sub_mix_0[s1 >>> 24] ^ sub_mix_1[(s2 >>> 16) & 0xff] ^ sub_mix_2[(s3 >>> 8) & 0xff] ^ sub_mix_3[s0 & 0xff] ^\n                keySchedule[ksRow++];\n            const /** @type {?} */ t2 = sub_mix_0[s2 >>> 24] ^ sub_mix_1[(s3 >>> 16) & 0xff] ^ sub_mix_2[(s0 >>> 8) & 0xff] ^ sub_mix_3[s1 & 0xff] ^\n                keySchedule[ksRow++];\n            const /** @type {?} */ t3 = sub_mix_0[s3 >>> 24] ^ sub_mix_1[(s0 >>> 16) & 0xff] ^ sub_mix_2[(s1 >>> 8) & 0xff] ^ sub_mix_3[s2 & 0xff] ^\n                keySchedule[ksRow++];\n            // Update state\n            s0 = t0;\n            s1 = t1;\n            s2 = t2;\n            s3 = t3;\n        }\n        // Shift rows, sub bytes, add round key\n        const /** @type {?} */ t0g = ((sbox[s0 >>> 24] << 24) | (sbox[(s1 >>> 16) & 0xff] << 16) | (sbox[(s2 >>> 8) & 0xff] << 8) | sbox[s3 & 0xff]) ^\n            keySchedule[ksRow++];\n        const /** @type {?} */ t1g = ((sbox[s1 >>> 24] << 24) | (sbox[(s2 >>> 16) & 0xff] << 16) | (sbox[(s3 >>> 8) & 0xff] << 8) | sbox[s0 & 0xff]) ^\n            keySchedule[ksRow++];\n        const /** @type {?} */ t2g = ((sbox[s2 >>> 24] << 24) | (sbox[(s3 >>> 16) & 0xff] << 16) | (sbox[(s0 >>> 8) & 0xff] << 8) | sbox[s1 & 0xff]) ^\n            keySchedule[ksRow++];\n        const /** @type {?} */ t3g = ((sbox[s3 >>> 24] << 24) | (sbox[(s0 >>> 16) & 0xff] << 16) | (sbox[(s1 >>> 8) & 0xff] << 8) | sbox[s2 & 0xff]) ^\n            keySchedule[ksRow++];\n        // Set output\n        M[offset] = t0g;\n        M[offset + 1] = t1g;\n        M[offset + 2] = t2g;\n        M[offset + 3] = t3g;\n    }\n}\nAES.keySize = 8;\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n// Initialization and round constants tables\nconst /** @type {?} */ H = [];\nconst /** @type {?} */ K = [];\n// Reusable object\nconst /** @type {?} */ W = [];\nclass SHA256 extends Hasher {\n    /**\n     * @return {?}\n     */\n    reset() {\n        // reset core values\n        super.reset();\n        this._hash = new WordArray(H.slice(0));\n    }\n    /**\n     * @param {?} M\n     * @param {?} offset\n     * @return {?}\n     */\n    _doProcessBlock(M, offset) {\n        // Shortcut\n        const /** @type {?} */ Hl = this._hash.words;\n        // Working variables\n        let /** @type {?} */ a = Hl[0];\n        let /** @type {?} */ b = Hl[1];\n        let /** @type {?} */ c = Hl[2];\n        let /** @type {?} */ d = Hl[3];\n        let /** @type {?} */ e = Hl[4];\n        let /** @type {?} */ f = Hl[5];\n        let /** @type {?} */ g = Hl[6];\n        let /** @type {?} */ h = Hl[7];\n        // Computation\n        for (let /** @type {?} */ i = 0; i < 64; i++) {\n            if (i < 16) {\n                W[i] = M[offset + i] | 0;\n            }\n            else {\n                const /** @type {?} */ gamma0x = W[i - 15];\n                const /** @type {?} */ gamma0 = ((gamma0x << 25) | (gamma0x >>> 7)) ^\n                    ((gamma0x << 14) | (gamma0x >>> 18)) ^\n                    (gamma0x >>> 3);\n                const /** @type {?} */ gamma1x = W[i - 2];\n                const /** @type {?} */ gamma1 = ((gamma1x << 15) | (gamma1x >>> 17)) ^\n                    ((gamma1x << 13) | (gamma1x >>> 19)) ^\n                    (gamma1x >>> 10);\n                W[i] = gamma0 + W[i - 7] + gamma1 + W[i - 16];\n            }\n            const /** @type {?} */ ch = (e & f) ^ (~e & g);\n            const /** @type {?} */ maj = (a & b) ^ (a & c) ^ (b & c);\n            const /** @type {?} */ sigma0 = ((a << 30) | (a >>> 2)) ^ ((a << 19) | (a >>> 13)) ^ ((a << 10) | (a >>> 22));\n            const /** @type {?} */ sigma1 = ((e << 26) | (e >>> 6)) ^ ((e << 21) | (e >>> 11)) ^ ((e << 7) | (e >>> 25));\n            const /** @type {?} */ t1 = h + sigma1 + ch + K[i] + W[i];\n            const /** @type {?} */ t2 = sigma0 + maj;\n            h = g;\n            g = f;\n            f = e;\n            e = (d + t1) | 0;\n            d = c;\n            c = b;\n            b = a;\n            a = (t1 + t2) | 0;\n        }\n        // Intermediate hash value\n        Hl[0] = (Hl[0] + a) | 0;\n        Hl[1] = (Hl[1] + b) | 0;\n        Hl[2] = (Hl[2] + c) | 0;\n        Hl[3] = (Hl[3] + d) | 0;\n        Hl[4] = (Hl[4] + e) | 0;\n        Hl[5] = (Hl[5] + f) | 0;\n        Hl[6] = (Hl[6] + g) | 0;\n        Hl[7] = (Hl[7] + h) | 0;\n    }\n    /**\n     * @return {?}\n     */\n    _doFinalize() {\n        const /** @type {?} */ nBitsTotal = this._nDataBytes * 8;\n        const /** @type {?} */ nBitsLeft = this._data.sigBytes * 8;\n        // Add padding\n        this._data.words[nBitsLeft >>> 5] |= 0x80 << (24 - nBitsLeft % 32);\n        this._data.words[(((nBitsLeft + 64) >>> 9) << 4) + 14] = Math.floor(nBitsTotal / 0x100000000);\n        this._data.words[(((nBitsLeft + 64) >>> 9) << 4) + 15] = nBitsTotal;\n        this._data.sigBytes = this._data.words.length * 4;\n        // Hash final blocks\n        this._process();\n        // Return final computed hash\n        return this._hash;\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass NoPadding {\n    /**\n     * Doesn't pad the data provided.\n     *\n     * \\@example\n     *\n     *     NoPadding.pad(wordArray, 4);\n     * @param {?} data The data to pad.\n     * @param {?} blockSize The multiple that the data should be padded to.\n     *\n     * @return {?}\n     */\n    static pad(data, blockSize) {\n    }\n    /**\n     * Doesn't unpad the data provided.\n     *\n     * \\@example\n     *\n     *     NoPadding.unpad(wordArray);\n     * @param {?} data The data to unpad.\n     *\n     * @return {?}\n     */\n    static unpad(data) {\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass ECBEncryptor extends BlockCipherModeAlgorithm {\n    /**\n     * Processes the data block at offset.\n     *\n     * \\@example\n     *\n     *     mode.processBlock(data.words, offset);\n     * @param {?} words The data words to operate on.\n     * @param {?} offset The offset where the block starts.\n     *\n     * @return {?}\n     */\n    processBlock(words, offset) {\n        this._cipher.encryptBlock(words, offset);\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nclass ECBDecryptor extends BlockCipherModeAlgorithm {\n    /**\n     * Processes the data block at offset.\n     *\n     * \\@example\n     *\n     *     mode.processBlock(data.words, offset);\n     * @param {?} words The data words to operate on.\n     * @param {?} offset The offset where the block starts.\n     *\n     * @return {?}\n     */\n    processBlock(words, offset) {\n        this._cipher.decryptBlock(words, offset);\n    }\n}\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n/**\n * Cipher Block Chaining mode.\n * @abstract\n */\nclass ECB extends BlockCipherMode {\n}\nECB.Encryptor = ECBEncryptor;\nECB.Decryptor = ECBDecryptor;\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\nconst /** @type {?} */ lib = {\n    BlockCipher: BlockCipher,\n    WordArray: WordArray,\n    CipherParams: CipherParams,\n    Hasher: Hasher,\n    SerializableCipher: SerializableCipher,\n    PasswordBasedCipher: PasswordBasedCipher\n};\nconst /** @type {?} */ algo = {\n    AES: AES,\n    SHA256: SHA256\n};\nconst /** @type {?} */ enc = {\n    Utf8: Utf8,\n    Hex: Hex\n};\nconst /** @type {?} */ pad = {\n    NoPadding: NoPadding,\n    PKCS7: PKCS7,\n    ZeroPadding: ZeroPadding,\n};\nconst /** @type {?} */ mode = {\n    CBC: CBC,\n    ECB: ECB\n};\n// HELPERS /////////////////////////////////////////////////////////////////////////////////////////\nconst /** @type {?} */ AES$1 = lib.BlockCipher._createHelper(algo.AES);\nconst /** @type {?} */ SHA256$1 = lib.Hasher._createHelper(algo.SHA256);\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\n/**\n * @fileoverview added by tsickle\n * @suppress {checkTypes} checked by tsc\n */\n\nexport {Base64, lib, algo, enc, pad, mode, AES$1 as AES, SHA256$1 as SHA256, AES as ɵl, SHA256 as ɵm, Hex as ɵp, Latin1 as ɵo, Utf8 as ɵn, Base as ɵg, BlockCipher as ɵa, BufferedBlockAlgorithm as ɵc, Cipher as ɵb, CipherParams as ɵf, Hasher as ɵi, PasswordBasedCipher as ɵk, SerializableCipher as ɵj, WordArray as ɵe, BlockCipherMode as ɵt, CBC as ɵs, ECB as ɵu, NoPadding as ɵq, PKCS7 as ɵr };\n//# sourceMappingURL=crypto-ts.js.map\n"]}