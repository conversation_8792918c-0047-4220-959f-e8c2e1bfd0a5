{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts"], "names": ["GeneralItemType", "_decorator", "Component", "Label", "Sprite", "Layout", "color", "GeneralCampType", "GeneralHeadLogic", "ccclass", "property", "General<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setData", "cfg", "updateItem", "_cfg", "name<PERSON><PERSON><PERSON>", "string", "name", "spritePic", "getComponent", "setHeadId", "cfgId", "showStar", "star", "costLabel", "cost", "camp", "Han", "camp<PERSON>abel", "<PERSON><PERSON>", "<PERSON>", "Shu", "<PERSON>", "arm<PERSON>abel", "armstr", "arms", "star_lv", "childen", "starLayout", "node", "children", "i", "length", "active", "str", "indexOf"], "mappings": ";;;sHAQaA,e;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AAG9CC,MAAAA,e,iBAAAA,e;;AACFC,MAAAA,gB;;;;;;;OAHD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;AAM9B;iCACaD,e,GAAN,MAAMA,eAAN,CAAsB,E;;sBAAhBA,e,iBACoB,C;;sBADpBA,e,oBAEuB,C;;sBAFvBA,e,sBAGyB,C;;sBAHzBA,e,oBAIuB,C;;sBAJvBA,e,mBAKsB,C;;yBAKdW,kB,WADpBF,OAAO,CAAC,oBAAD,C,UAIHC,QAAQ,CAACP,KAAD,C,UAIRO,QAAQ,CAACN,MAAD,C,UAGRM,QAAQ,CAACP,KAAD,C,UAIRO,QAAQ,CAACL,MAAD,C,UAIRK,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACP,KAAD,C,oCAtBb,MACqBQ,kBADrB,SACgDT,SADhD,CAC0D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,wCAwBjC,IAxBiC;AAAA;;AA0B/CU,QAAAA,OAAO,CAACC,GAAD,EAAyB;AACnC,eAAKC,UAAL,CAAgBD,GAAhB;AACH;;AAESC,QAAAA,UAAU,CAACD,GAAD,EAAwB;AACxC;AACA,eAAKE,IAAL,GAAYF,GAAZ;AACA,eAAKG,SAAL,CAAeC,MAAf,GAAwB,KAAKF,IAAL,CAAUG,IAAlC;AACA,eAAKC,SAAL,CAAeC,YAAf;AAAA;AAAA,oDAA8CC,SAA9C,CAAwD,KAAKN,IAAL,CAAUO,KAAlE;AACA,eAAKC,QAAL,CAAc,KAAKR,IAAL,CAAUS,IAAxB,EAA8B,CAA9B;;AAEA,cAAG,KAAKC,SAAR,EAAkB;AACd,iBAAKA,SAAL,CAAeR,MAAf,GAAwB,KAAKF,IAAL,CAAUW,IAAV,GAAiB,EAAzC;AACH;;AAED,cAAG,KAAKX,IAAL,CAAUY,IAAV,IAAkB;AAAA;AAAA,kDAAgBC,GAArC,EAAyC;AACrC,iBAAKC,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH,WAFD,MAEM,IAAG,KAAKF,IAAL,CAAUY,IAAV,IAAkB;AAAA;AAAA,kDAAgBG,GAArC,EAAyC;AAC3C,iBAAKD,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH,WAFK,MAEA,IAAG,KAAKF,IAAL,CAAUY,IAAV,IAAkB;AAAA;AAAA,kDAAgBI,GAArC,EAAyC;AAC3C,iBAAKF,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH,WAFK,MAEA,IAAG,KAAKF,IAAL,CAAUY,IAAV,IAAkB;AAAA;AAAA,kDAAgBK,GAArC,EAAyC;AAC3C,iBAAKH,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH,WAFK,MAEA,IAAG,KAAKF,IAAL,CAAUY,IAAV,IAAkB;AAAA;AAAA,kDAAgBM,EAArC,EAAwC;AAC1C,iBAAKJ,SAAL,CAAeZ,MAAf,GAAwB,GAAxB;AACH;;AAED,eAAKiB,QAAL,CAAcjB,MAAd,GAAuB,KAAKkB,MAAL,CAAY,KAAKpB,IAAL,CAAUqB,IAAtB,CAAvB;AAEH;;AAGSb,QAAAA,QAAQ,CAACC,IAAD,EAAiBa,OAAjB,EAAyC;AAAA,cAAxCb,IAAwC;AAAxCA,YAAAA,IAAwC,GAA1B,CAA0B;AAAA;;AAAA,cAAxBa,OAAwB;AAAxBA,YAAAA,OAAwB,GAAP,CAAO;AAAA;;AACvD,cAAIC,OAAO,GAAG,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBC,QAAnC;;AACA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAcA,CAAC,GAACJ,OAAO,CAACK,MAAxB,EAA+BD,CAAC,EAAhC,EAAmC;AAC/B,gBAAGA,CAAC,GAAGlB,IAAP,EAAY;AACRc,cAAAA,OAAO,CAACI,CAAD,CAAP,CAAWE,MAAX,GAAoB,IAApB;;AACA,kBAAGF,CAAC,GAAGL,OAAP,EAAe;AACXC,gBAAAA,OAAO,CAACI,CAAD,CAAP,CAAWtB,YAAX,CAAwBhB,MAAxB,EAAgCE,KAAhC,GAAwCA,KAAK,CAAC,GAAD,EAAK,CAAL,EAAO,CAAP,CAA7C;AACH,eAFD,MAEK;AACDgC,gBAAAA,OAAO,CAACI,CAAD,CAAP,CAAWtB,YAAX,CAAwBhB,MAAxB,EAAgCE,KAAhC,GAAwCA,KAAK,CAAC,GAAD,EAAK,GAAL,EAAS,GAAT,CAA7C;AACH;AACJ,aAPD,MAOK;AACDgC,cAAAA,OAAO,CAACI,CAAD,CAAP,CAAWE,MAAX,GAAoB,KAApB;AACH;AACJ;AACJ;;AAEST,QAAAA,MAAM,CAACC,IAAD,EAAwB;AACpC;AAEA,cAAIS,GAAG,GAAG,EAAV;;AACA,cAAGT,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBV,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CV,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AAC9DD,YAAAA,GAAG,IAAI,GAAP;AACH,WAFD,MAEM,IAAGT,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBV,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CV,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AACpED,YAAAA,GAAG,IAAI,GAAP;AACH,WAFK,MAEA,IAAGT,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAjB,IAAsBV,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAvC,IAA4CV,IAAI,CAACU,OAAL,CAAa,CAAb,KAAiB,CAAhE,EAAkE;AACpED,YAAAA,GAAG,IAAI,GAAP;AACH;;AACD,iBAAOA,GAAP;AACH;;AAtFqD,O;;;;;iBAInC,I;;;;;;;iBAIA,I;;;;;;;iBAGA,I;;;;;;;iBAIC,I;;;;;;;iBAID,I;;;;;;;iBAGD,I", "sourcesContent": ["import { _decorator, Component, Label, Sprite, Layout, color } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { GeneralCampType, GeneralConfig } from \"../../general/GeneralProxy\";\nimport GeneralHeadLogic from \"./GeneralHeadLogic\";\n\n\n// /**军队命令*/\nexport class GeneralItemType {\n    static GeneralInfo: number = 0;//武将详情\n    static GeneralDispose: number = 1;//武将上阵\n    static GeneralConScript: number = 2;//武将征兵\n    static GeneralNoThing: number = 3;//无用\n    static GeneralSelect: number = 4;//选择\n}\n\n\n@ccclass('GeneralRosterLogic')\nexport default class GeneralRosterLogic extends Component {\n\n\n    @property(Label)\n    nameLabel: Label = null;\n\n\n    @property(Sprite)\n    spritePic:Sprite = null;\n\n    @property(Label)\n    costLabel: Label = null;\n\n    \n    @property(Layout)\n    starLayout:Layout = null;\n\n    \n    @property(Label)\n    campLabel: Label = null;\n\n    @property(Label)\n    armLabel: Label = null;\n\n    _cfg:GeneralConfig = null;\n\n    public setData(cfg:GeneralConfig): void{\n        this.updateItem(cfg);\n    }\n\n    protected updateItem(cfg:GeneralConfig):void{\n        // console.log(\"updateItem\");\n        this._cfg = cfg;\n        this.nameLabel.string = this._cfg.name \n        this.spritePic.getComponent(GeneralHeadLogic).setHeadId(this._cfg.cfgId);\n        this.showStar(this._cfg.star, 0);\n        \n        if(this.costLabel){\n            this.costLabel.string = this._cfg.cost + \"\";\n        }\n\n        if(this._cfg.camp == GeneralCampType.Han){\n            this.campLabel.string = \"汉\";\n        }else if(this._cfg.camp == GeneralCampType.Qun){\n            this.campLabel.string = \"群\";\n        }else if(this._cfg.camp == GeneralCampType.Wei){\n            this.campLabel.string = \"魏\";\n        }else if(this._cfg.camp == GeneralCampType.Shu){\n            this.campLabel.string = \"蜀\";\n        }else if(this._cfg.camp == GeneralCampType.Wu){\n            this.campLabel.string = \"吴\";\n        }\n\n        this.armLabel.string = this.armstr(this._cfg.arms);\n        \n    }\n\n\n    protected showStar(star:number = 3,star_lv:number = 0):void{\n        var childen = this.starLayout.node.children;\n        for(var i = 0;i<childen.length;i++){\n            if(i < star){\n                childen[i].active = true;\n                if(i < star_lv){\n                    childen[i].getComponent(Sprite).color = color(255,0,0);\n                }else{\n                    childen[i].getComponent(Sprite).color = color(255,255,255);\n                }\n            }else{\n                childen[i].active = false; \n            }\n        }\n    }\n\n    protected armstr(arms:number []): string{\n        // console.log(\"armstr:\", arms);\n\n        var str = \"\"\n        if(arms.indexOf(1)>=0 || arms.indexOf(4)>=0 || arms.indexOf(7)>=0){\n            str += \"步\"\n        }else if(arms.indexOf(2)>=0 || arms.indexOf(5)>=0 || arms.indexOf(8)>=0){\n            str += \"弓\"\n        }else if(arms.indexOf(3)>=0 || arms.indexOf(6)>=0 || arms.indexOf(9)>=0){\n            str += \"骑\"\n        }\n        return str;\n    }\n\n\n}\n"]}