{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts"], "names": ["_decorator", "view", "ResolutionPolicy", "sys", "UITransform", "Widget", "screen", "GameConfig", "ccclass", "property", "ScreenAdapter", "DESIGN_WIDTH", "designWidth", "DESIGN_HEIGHT", "designHeight", "instance", "_instance", "init", "_isInitialized", "enableDebugInfo", "console", "log", "windowSize", "screenRatio", "width", "height", "toFixed", "policy", "designRatio", "FIXED_WIDTH", "FIXED_HEIGHT", "SHOW_ALL", "setDesignResolutionSize", "visibleSize", "getVisibleSize", "visible<PERSON><PERSON>in", "getVisibleOrigin", "x", "y", "warn", "scaleX", "scaleY", "error", "newVisibleSize", "newVisibleOrigin", "newScaleX", "newScaleY", "getAdaptInfo", "designSize", "getDesignResolutionSize", "frameSize", "adaptToSafeArea", "node", "alignTop", "alignBottom", "widget", "getComponent", "safeArea", "getSafeAreaRect", "getFrameSize", "topOffset", "top", "isAlignTop", "bottomOffset", "bottom", "isAlignBottom", "updateAlignment", "isPointInNode", "worldPos", "uiTransform", "localPos", "convertToNodeSpaceAR", "size", "contentSize", "anchorPoint", "minX", "maxX", "minY", "maxY", "getDeviceType", "ratio", "isMobile", "printDebugInfo", "info"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,G,OAAAA,G;AAAaC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,M,OAAAA,M;;AACvFC,MAAAA,U,iBAAAA,U;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;AAE9B;AACA;AACA;AACA;;+BAEaU,a,WADZF,OAAO,CAAC,eAAD,C,mCAAR,MACaE,aADb,CAC2B;AAAA;AAAA,kDAgBW,KAhBX;AAAA;;AAEvB;AAC8B,mBAAZC,YAAY,GAAW;AACrC,iBAAO;AAAA;AAAA,wCAAWL,MAAX,CAAkBM,WAAzB;AACH;;AAE8B,mBAAbC,aAAa,GAAW;AACtC,iBAAO;AAAA;AAAA,wCAAWP,MAAX,CAAkBQ,YAAzB;AACH,SATsB,CAWvB;;;AAO0B,mBAARC,QAAQ,GAAkB;AACxC,cAAI,CAAC,KAAKC,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,IAAIN,aAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKM,SAAZ;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,IAAI,GAAS;AAChB,cAAI,KAAKC,cAAT,EAAyB;AACrB;AACH;;AAED,cAAI;AAAA;AAAA,wCAAWZ,MAAX,CAAkBa,eAAtB,EAAuC;AACnCC,YAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ;AACH,WAPe,CAShB;;;AACA,cAAMC,UAAU,GAAGhB,MAAM,CAACgB,UAA1B;AACA,cAAMC,WAAW,GAAGD,UAAU,CAACE,KAAX,GAAmBF,UAAU,CAACG,MAAlD;AAEAL,UAAAA,OAAO,CAACC,GAAR,gDAAqCC,UAAU,CAACE,KAAhD,SAAyDF,UAAU,CAACG,MAApE,wBAAmFF,WAAW,CAACG,OAAZ,CAAoB,CAApB,CAAnF,EAbgB,CAehB;;AACA,cAAId,WAAW,GAAGF,aAAa,CAACC,YAAhC;AACA,cAAIG,YAAY,GAAGJ,aAAa,CAACG,aAAjC;AACA,cAAIc,MAAJ,CAlBgB,CAoBhB;;AACA,cAAMC,WAAW,GAAGhB,WAAW,GAAGE,YAAlC,CArBgB,CAqBgC;;AAEhDM,UAAAA,OAAO,CAACC,GAAR,gDAAqCO,WAAW,CAACF,OAAZ,CAAoB,CAApB,CAArC,oCAAsEH,WAAW,CAACG,OAAZ,CAAoB,CAApB,CAAtE,EAvBgB,CAyBhB;;AACA,cAAIH,WAAW,GAAGK,WAAW,GAAG,GAAhC,EAAqC;AACjC;AACAD,YAAAA,MAAM,GAAGzB,gBAAgB,CAAC2B,WAA1B;AACAT,YAAAA,OAAO,CAACC,GAAR,CAAY,sCAAZ;AACH,WAJD,MAIO,IAAIE,WAAW,GAAGK,WAAW,GAAG,GAAhC,EAAqC;AACxC;AACAD,YAAAA,MAAM,GAAGzB,gBAAgB,CAAC4B,YAA1B;AACAV,YAAAA,OAAO,CAACC,GAAR,CAAY,uCAAZ;AACH,WAJM,MAIA;AACH;AACAM,YAAAA,MAAM,GAAGzB,gBAAgB,CAAC6B,QAA1B;AACAX,YAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACH,WAtCe,CAwChB;;;AACApB,UAAAA,IAAI,CAAC+B,uBAAL,CAA6BpB,WAA7B,EAA0CE,YAA1C,EAAwDa,MAAxD,EAzCgB,CA2ChB;;AACA,cAAMM,WAAW,GAAGhC,IAAI,CAACiC,cAAL,EAApB;AACA,cAAMC,aAAa,GAAGlC,IAAI,CAACmC,gBAAL,EAAtB;AAEAhB,UAAAA,OAAO,CAACC,GAAR,sDAAsCT,WAAtC,SAAqDE,YAArD;AACAM,UAAAA,OAAO,CAACC,GAAR,gDAAqCY,WAAW,CAACT,KAAjD,SAA0DS,WAAW,CAACR,MAAtE;AACAL,UAAAA,OAAO,CAACC,GAAR,iDAAsCc,aAAa,CAACE,CAApD,UAA0DF,aAAa,CAACG,CAAxE,QAjDgB,CAmDhB;;AACA,cAAIH,aAAa,CAACE,CAAd,KAAoB,CAApB,IAAyBF,aAAa,CAACG,CAAd,KAAoB,CAAjD,EAAoD;AAChDlB,YAAAA,OAAO,CAACmB,IAAR;AACH,WAtDe,CAwDhB;;;AACA,cAAMC,MAAM,GAAGP,WAAW,CAACT,KAAZ,GAAoBZ,WAAnC;AACA,cAAM6B,MAAM,GAAGR,WAAW,CAACR,MAAZ,GAAqBX,YAApC;AAEAM,UAAAA,OAAO,CAACC,GAAR,8DAAyCmB,MAAM,CAACd,OAAP,CAAe,CAAf,CAAzC,YAAiEe,MAAM,CAACf,OAAP,CAAe,CAAf,CAAjE,EA5DgB,CA8DhB;;AACA,cAAIe,MAAM,GAAG,GAAT,IAAgBD,MAAM,GAAG,GAA7B,EAAkC;AAC9BpB,YAAAA,OAAO,CAACsB,KAAR,gGAA+CD,MAAM,CAACf,OAAP,CAAe,CAAf,CAA/C,YAAuEc,MAAM,CAACd,OAAP,CAAe,CAAf,CAAvE;AACAN,YAAAA,OAAO,CAACsB,KAAR,yGAF8B,CAI9B;;AACAtB,YAAAA,OAAO,CAACC,GAAR;AACApB,YAAAA,IAAI,CAAC+B,uBAAL,CAA6BpB,WAA7B,EAA0CE,YAA1C,EAAwDZ,gBAAgB,CAAC6B,QAAzE,EAN8B,CAQ9B;;AACA,gBAAMY,cAAc,GAAG1C,IAAI,CAACiC,cAAL,EAAvB;AACA,gBAAMU,gBAAgB,GAAG3C,IAAI,CAACmC,gBAAL,EAAzB;AACA,gBAAMS,SAAS,GAAGF,cAAc,CAACnB,KAAf,GAAuBZ,WAAzC;AACA,gBAAMkC,SAAS,GAAGH,cAAc,CAAClB,MAAf,GAAwBX,YAA1C;AAEAM,YAAAA,OAAO,CAACC,GAAR;AACAD,YAAAA,OAAO,CAACC,GAAR,gDAAqCsB,cAAc,CAACnB,KAAf,CAAqBE,OAArB,CAA6B,CAA7B,CAArC,SAAwEiB,cAAc,CAAClB,MAAf,CAAsBC,OAAtB,CAA8B,CAA9B,CAAxE;AACAN,YAAAA,OAAO,CAACC,GAAR,iDAAsCuB,gBAAgB,CAACP,CAAjB,CAAmBX,OAAnB,CAA2B,CAA3B,CAAtC,UAAwEkB,gBAAgB,CAACN,CAAjB,CAAmBZ,OAAnB,CAA2B,CAA3B,CAAxE;AACAN,YAAAA,OAAO,CAACC,GAAR,kDAAuCwB,SAAS,CAACnB,OAAV,CAAkB,CAAlB,CAAvC,YAAkEoB,SAAS,CAACpB,OAAV,CAAkB,CAAlB,CAAlE;AACH;;AAED,eAAKR,cAAL,GAAsB,IAAtB;AACH;AAED;AACJ;AACA;;;AACW6B,QAAAA,YAAY,GAAQ;AACvB,cAAMzB,UAAU,GAAGhB,MAAM,CAACgB,UAA1B;AACA,cAAMW,WAAW,GAAGhC,IAAI,CAACiC,cAAL,EAApB;AACA,cAAMC,aAAa,GAAGlC,IAAI,CAACmC,gBAAL,EAAtB;AACA,cAAMY,UAAU,GAAG/C,IAAI,CAACgD,uBAAL,EAAnB;AAEA,iBAAO;AACHC,YAAAA,SAAS,EAAE5B,UADR;AAEHW,YAAAA,WAAW,EAAEA,WAFV;AAGHE,YAAAA,aAAa,EAAEA,aAHZ;AAIHa,YAAAA,UAAU,EAAEA,UAJT;AAKHR,YAAAA,MAAM,EAAEP,WAAW,CAACT,KAAZ,GAAoBwB,UAAU,CAACxB,KALpC;AAMHiB,YAAAA,MAAM,EAAER,WAAW,CAACR,MAAZ,GAAqBuB,UAAU,CAACvB;AANrC,WAAP;AAQH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACW0B,QAAAA,eAAe,CAACC,IAAD,EAAYC,QAAZ,EAAuCC,WAAvC,EAA2E;AAAA,cAA/DD,QAA+D;AAA/DA,YAAAA,QAA+D,GAA3C,KAA2C;AAAA;;AAAA,cAApCC,WAAoC;AAApCA,YAAAA,WAAoC,GAAb,KAAa;AAAA;;AAC7F,cAAI,CAACF,IAAL,EAAW;AAEX,cAAMG,MAAM,GAAGH,IAAI,CAACI,YAAL,CAAkBnD,MAAlB,CAAf;;AACA,cAAI,CAACkD,MAAL,EAAa;AACTnC,YAAAA,OAAO,CAACmB,IAAR,CAAa,uCAAb;AACA;AACH,WAP4F,CAS7F;;;AACA,cAAMkB,QAAQ,GAAGtD,GAAG,CAACuD,eAAJ,EAAjB;AACA,cAAMR,SAAS,GAAGjD,IAAI,CAAC0D,YAAL,EAAlB;AACA,cAAM1B,WAAW,GAAGhC,IAAI,CAACiC,cAAL,EAApB;;AAEA,cAAImB,QAAQ,IAAII,QAAQ,CAACnB,CAAT,GAAa,CAA7B,EAAgC;AAC5B;AACA,gBAAMsB,SAAS,GAAIH,QAAQ,CAACnB,CAAT,GAAaY,SAAS,CAACzB,MAAxB,GAAkCQ,WAAW,CAACR,MAAhE;AACA8B,YAAAA,MAAM,CAACM,GAAP,GAAaD,SAAb;AACAL,YAAAA,MAAM,CAACO,UAAP,GAAoB,IAApB;AACH;;AAED,cAAIR,WAAW,IAAKG,QAAQ,CAACnB,CAAT,GAAamB,QAAQ,CAAChC,MAAvB,GAAiCyB,SAAS,CAACzB,MAA9D,EAAsE;AAClE;AACA,gBAAMsC,YAAY,GAAI,CAACb,SAAS,CAACzB,MAAV,GAAmBgC,QAAQ,CAACnB,CAA5B,GAAgCmB,QAAQ,CAAChC,MAA1C,IAAoDyB,SAAS,CAACzB,MAA/D,GAAyEQ,WAAW,CAACR,MAA1G;AACA8B,YAAAA,MAAM,CAACS,MAAP,GAAgBD,YAAhB;AACAR,YAAAA,MAAM,CAACU,aAAP,GAAuB,IAAvB;AACH;;AAEDV,UAAAA,MAAM,CAACW,eAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,aAAa,CAACf,IAAD,EAAYgB,QAAZ,EAAqC;AACrD,cAAI,CAAChB,IAAL,EAAW,OAAO,KAAP;AAEX,cAAMiB,WAAW,GAAGjB,IAAI,CAACI,YAAL,CAAkBpD,WAAlB,CAApB;AACA,cAAI,CAACiE,WAAL,EAAkB,OAAO,KAAP,CAJmC,CAMrD;;AACA,cAAMC,QAAQ,GAAGD,WAAW,CAACE,oBAAZ,CAAiCH,QAAjC,CAAjB,CAPqD,CASrD;;AACA,cAAMI,IAAI,GAAGH,WAAW,CAACI,WAAzB;AACA,cAAMC,WAAW,GAAGL,WAAW,CAACK,WAAhC;AAEA,cAAMC,IAAI,GAAG,CAACH,IAAI,CAAChD,KAAN,GAAckD,WAAW,CAACrC,CAAvC;AACA,cAAMuC,IAAI,GAAGJ,IAAI,CAAChD,KAAL,IAAc,IAAIkD,WAAW,CAACrC,CAA9B,CAAb;AACA,cAAMwC,IAAI,GAAG,CAACL,IAAI,CAAC/C,MAAN,GAAeiD,WAAW,CAACpC,CAAxC;AACA,cAAMwC,IAAI,GAAGN,IAAI,CAAC/C,MAAL,IAAe,IAAIiD,WAAW,CAACpC,CAA/B,CAAb;AAEA,iBAAOgC,QAAQ,CAACjC,CAAT,IAAcsC,IAAd,IAAsBL,QAAQ,CAACjC,CAAT,IAAcuC,IAApC,IACAN,QAAQ,CAAChC,CAAT,IAAcuC,IADd,IACsBP,QAAQ,CAAChC,CAAT,IAAcwC,IAD3C;AAEH;AAED;AACJ;AACA;;;AACWC,QAAAA,aAAa,GAAW;AAC3B,cAAMzD,UAAU,GAAGhB,MAAM,CAACgB,UAA1B;AACA,cAAM0D,KAAK,GAAG1D,UAAU,CAACE,KAAX,GAAmBF,UAAU,CAACG,MAA5C;;AAEA,cAAItB,GAAG,CAAC8E,QAAR,EAAkB;AACd,gBAAID,KAAK,GAAG,GAAZ,EAAiB;AACb,qBAAO,eAAP,CADa,CACY;AAC5B,aAFD,MAEO,IAAIA,KAAK,GAAG,IAAZ,EAAkB;AACrB,qBAAO,eAAP,CADqB,CACI;AAC5B,aAFM,MAEA;AACH,qBAAO,aAAP,CADG,CACsB;AAC5B;AACJ,WARD,MAQO;AACH,mBAAO,SAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACWE,QAAAA,cAAc,GAAS;AAC1B,cAAMC,IAAI,GAAG,KAAKpC,YAAL,EAAb;AACA3B,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,gCAAqB,KAAK0D,aAAL,EAArB;AACA3D,UAAAA,OAAO,CAACC,GAAR,gCAAqB8D,IAAI,CAACjC,SAAL,CAAe1B,KAApC,SAA6C2D,IAAI,CAACjC,SAAL,CAAezB,MAA5D;AACAL,UAAAA,OAAO,CAACC,GAAR,sCAAsB8D,IAAI,CAACnC,UAAL,CAAgBxB,KAAtC,SAA+C2D,IAAI,CAACnC,UAAL,CAAgBvB,MAA/D;AACAL,UAAAA,OAAO,CAACC,GAAR,gCAAqB8D,IAAI,CAAClD,WAAL,CAAiBT,KAAtC,SAA+C2D,IAAI,CAAClD,WAAL,CAAiBR,MAAhE;AACAL,UAAAA,OAAO,CAACC,GAAR,iCAAsB8D,IAAI,CAAChD,aAAL,CAAmBE,CAAzC,UAA+C8C,IAAI,CAAChD,aAAL,CAAmBG,CAAlE;AACAlB,UAAAA,OAAO,CAACC,GAAR,kCAAuB8D,IAAI,CAAC3C,MAAL,CAAYd,OAAZ,CAAoB,CAApB,CAAvB,YAAoDyD,IAAI,CAAC1C,MAAL,CAAYf,OAAZ,CAAoB,CAApB,CAApD;;AAEA,cAAIvB,GAAG,CAAC8E,QAAR,EAAkB;AACd,gBAAMxB,QAAQ,GAAGtD,GAAG,CAACuD,eAAJ,EAAjB;AACAtC,YAAAA,OAAO,CAACC,GAAR,kCAAuBoC,QAAQ,CAACpB,CAAhC,YAAwCoB,QAAQ,CAACnB,CAAjD,YAAyDmB,QAAQ,CAACjC,KAAlE,YAA8EiC,QAAQ,CAAChC,MAAvF;AACH;;AACDL,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACH;;AA3OsB,O,wCAYY,IAAI,E,yCACJ,IAAI,E,yCAEG,I", "sourcesContent": ["import { _decorator, Component, view, ResolutionPolicy, sys, Canvas, UITransform, Widget, Vec3, screen } from 'cc';\nimport { GameConfig } from '../config/GameConfig';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 屏幕适配管理器\n * 统一处理不同设备的分辨率适配问题\n */\n@ccclass('ScreenAdapter')\nexport class ScreenAdapter {\n    \n    // 设计分辨率（从配置文件读取）\n    public static get DESIGN_WIDTH(): number {\n        return GameConfig.screen.designWidth;\n    }\n\n    public static get DESIGN_HEIGHT(): number {\n        return GameConfig.screen.designHeight;\n    }\n    \n    // 最小和最大宽高比\n    public static readonly MIN_RATIO = 9 / 21;  // 最窄屏幕比例\n    public static readonly MAX_RATIO = 9 / 16;  // 最宽屏幕比例\n    \n    private static _instance: ScreenAdapter = null;\n    private _isInitialized: boolean = false;\n    \n    public static get instance(): ScreenAdapter {\n        if (!this._instance) {\n            this._instance = new ScreenAdapter();\n        }\n        return this._instance;\n    }\n    \n    /**\n     * 初始化屏幕适配\n     */\n    public init(): void {\n        if (this._isInitialized) {\n            return;\n        }\n\n        if (GameConfig.screen.enableDebugInfo) {\n            console.log('[ScreenAdapter] 开始初始化屏幕适配');\n        }\n\n        // 获取屏幕实际尺寸（使用新API）\n        const windowSize = screen.windowSize;\n        const screenRatio = windowSize.width / windowSize.height;\n\n        console.log(`[ScreenAdapter] 屏幕尺寸: ${windowSize.width}x${windowSize.height}, 比例: ${screenRatio.toFixed(3)}`);\n\n        // 对于竖屏游戏，使用更保守的适配策略\n        let designWidth = ScreenAdapter.DESIGN_WIDTH;\n        let designHeight = ScreenAdapter.DESIGN_HEIGHT;\n        let policy: ResolutionPolicy;\n\n        // 计算设计分辨率比例\n        const designRatio = designWidth / designHeight; // 1080/1920 = 0.5625\n\n        console.log(`[ScreenAdapter] 设计比例: ${designRatio.toFixed(3)}, 屏幕比例: ${screenRatio.toFixed(3)}`);\n\n        // 对于竖屏游戏，优先使用SHOW_ALL策略确保UI不变形\n        if (screenRatio > designRatio * 1.5) {\n            // 屏幕太宽（如桌面环境），使用FIXED_WIDTH避免UI过度拉伸\n            policy = ResolutionPolicy.FIXED_WIDTH;\n            console.log('[ScreenAdapter] 屏幕过宽，使用FIXED_WIDTH策略');\n        } else if (screenRatio < designRatio * 0.8) {\n            // 屏幕太窄，使用FIXED_HEIGHT\n            policy = ResolutionPolicy.FIXED_HEIGHT;\n            console.log('[ScreenAdapter] 屏幕过窄，使用FIXED_HEIGHT策略');\n        } else {\n            // 正常比例，使用SHOW_ALL保证完整显示\n            policy = ResolutionPolicy.SHOW_ALL;\n            console.log('[ScreenAdapter] 使用SHOW_ALL策略保证完整显示');\n        }\n\n        // 设置设计分辨率和适配策略\n        view.setDesignResolutionSize(designWidth, designHeight, policy);\n\n        // 输出最终的可视区域信息\n        const visibleSize = view.getVisibleSize();\n        const visibleOrigin = view.getVisibleOrigin();\n\n        console.log(`[ScreenAdapter] 设计分辨率: ${designWidth}x${designHeight}`);\n        console.log(`[ScreenAdapter] 可视区域: ${visibleSize.width}x${visibleSize.height}`);\n        console.log(`[ScreenAdapter] 可视原点: (${visibleOrigin.x}, ${visibleOrigin.y})`);\n\n        // 检查可视原点偏移\n        if (visibleOrigin.x !== 0 || visibleOrigin.y !== 0) {\n            console.warn(`[ScreenAdapter] 警告：可视原点不为(0,0)，这可能导致点击偏移！`);\n        }\n\n        // 检查缩放比例和可视原点\n        const scaleX = visibleSize.width / designWidth;\n        const scaleY = visibleSize.height / designHeight;\n\n        console.log(`[ScreenAdapter] 实际缩放比例: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);\n\n        // 检查是否有严重的缩放异常（比如Y轴被压缩到0.3）\n        if (scaleY < 0.5 || scaleX > 4.0) {\n            console.error(`[ScreenAdapter] 严重错误：缩放比例异常！Y=${scaleY.toFixed(3)}, X=${scaleX.toFixed(3)}`);\n            console.error(`[ScreenAdapter] 这会导致严重的点击偏移问题！`);\n\n            // 强制使用SHOW_ALL策略重新设置\n            console.log(`[ScreenAdapter] 强制重置为SHOW_ALL策略`);\n            view.setDesignResolutionSize(designWidth, designHeight, ResolutionPolicy.SHOW_ALL);\n\n            // 重新获取信息\n            const newVisibleSize = view.getVisibleSize();\n            const newVisibleOrigin = view.getVisibleOrigin();\n            const newScaleX = newVisibleSize.width / designWidth;\n            const newScaleY = newVisibleSize.height / designHeight;\n\n            console.log(`[ScreenAdapter] 重置后信息:`);\n            console.log(`[ScreenAdapter] 可视区域: ${newVisibleSize.width.toFixed(1)}x${newVisibleSize.height.toFixed(1)}`);\n            console.log(`[ScreenAdapter] 可视原点: (${newVisibleOrigin.x.toFixed(1)}, ${newVisibleOrigin.y.toFixed(1)})`);\n            console.log(`[ScreenAdapter] 缩放比例: X=${newScaleX.toFixed(3)}, Y=${newScaleY.toFixed(3)}`);\n        }\n\n        this._isInitialized = true;\n    }\n    \n    /**\n     * 获取屏幕适配信息\n     */\n    public getAdaptInfo(): any {\n        const windowSize = screen.windowSize;\n        const visibleSize = view.getVisibleSize();\n        const visibleOrigin = view.getVisibleOrigin();\n        const designSize = view.getDesignResolutionSize();\n\n        return {\n            frameSize: windowSize,\n            visibleSize: visibleSize,\n            visibleOrigin: visibleOrigin,\n            designSize: designSize,\n            scaleX: visibleSize.width / designSize.width,\n            scaleY: visibleSize.height / designSize.height\n        };\n    }\n    \n    /**\n     * 适配UI节点到安全区域\n     * @param node 需要适配的节点\n     * @param alignTop 是否对齐到顶部安全区域\n     * @param alignBottom 是否对齐到底部安全区域\n     */\n    public adaptToSafeArea(node: any, alignTop: boolean = false, alignBottom: boolean = false): void {\n        if (!node) return;\n        \n        const widget = node.getComponent(Widget);\n        if (!widget) {\n            console.warn('[ScreenAdapter] 节点没有Widget组件，无法适配安全区域');\n            return;\n        }\n        \n        // 获取安全区域信息\n        const safeArea = sys.getSafeAreaRect();\n        const frameSize = view.getFrameSize();\n        const visibleSize = view.getVisibleSize();\n        \n        if (alignTop && safeArea.y > 0) {\n            // 适配顶部安全区域（如刘海屏）\n            const topOffset = (safeArea.y / frameSize.height) * visibleSize.height;\n            widget.top = topOffset;\n            widget.isAlignTop = true;\n        }\n        \n        if (alignBottom && (safeArea.y + safeArea.height) < frameSize.height) {\n            // 适配底部安全区域（如虚拟按键）\n            const bottomOffset = ((frameSize.height - safeArea.y - safeArea.height) / frameSize.height) * visibleSize.height;\n            widget.bottom = bottomOffset;\n            widget.isAlignBottom = true;\n        }\n        \n        widget.updateAlignment();\n    }\n    \n    /**\n     * 检查点击位置是否在节点范围内\n     * @param node 目标节点\n     * @param worldPos 世界坐标点击位置\n     */\n    public isPointInNode(node: any, worldPos: Vec3): boolean {\n        if (!node) return false;\n        \n        const uiTransform = node.getComponent(UITransform);\n        if (!uiTransform) return false;\n        \n        // 将世界坐标转换为节点本地坐标\n        const localPos = uiTransform.convertToNodeSpaceAR(worldPos);\n        \n        // 检查是否在节点范围内\n        const size = uiTransform.contentSize;\n        const anchorPoint = uiTransform.anchorPoint;\n        \n        const minX = -size.width * anchorPoint.x;\n        const maxX = size.width * (1 - anchorPoint.x);\n        const minY = -size.height * anchorPoint.y;\n        const maxY = size.height * (1 - anchorPoint.y);\n        \n        return localPos.x >= minX && localPos.x <= maxX && \n               localPos.y >= minY && localPos.y <= maxY;\n    }\n    \n    /**\n     * 获取当前设备类型\n     */\n    public getDeviceType(): string {\n        const windowSize = screen.windowSize;\n        const ratio = windowSize.width / windowSize.height;\n        \n        if (sys.isMobile) {\n            if (ratio < 0.6) {\n                return 'mobile_narrow';  // 窄屏手机\n            } else if (ratio < 0.75) {\n                return 'mobile_normal';  // 普通手机\n            } else {\n                return 'mobile_wide';    // 宽屏手机\n            }\n        } else {\n            return 'desktop';\n        }\n    }\n    \n    /**\n     * 打印调试信息\n     */\n    public printDebugInfo(): void {\n        const info = this.getAdaptInfo();\n        console.log('=== 屏幕适配调试信息 ===');\n        console.log(`设备类型: ${this.getDeviceType()}`);\n        console.log(`窗口尺寸: ${info.frameSize.width}x${info.frameSize.height}`);\n        console.log(`设计分辨率: ${info.designSize.width}x${info.designSize.height}`);\n        console.log(`可视区域: ${info.visibleSize.width}x${info.visibleSize.height}`);\n        console.log(`可视原点: (${info.visibleOrigin.x}, ${info.visibleOrigin.y})`);\n        console.log(`缩放比例: X=${info.scaleX.toFixed(3)}, Y=${info.scaleY.toFixed(3)}`);\n        \n        if (sys.isMobile) {\n            const safeArea = sys.getSafeAreaRect();\n            console.log(`安全区域: x=${safeArea.x}, y=${safeArea.y}, w=${safeArea.width}, h=${safeArea.height}`);\n        }\n        console.log('========================');\n    }\n}\n"]}