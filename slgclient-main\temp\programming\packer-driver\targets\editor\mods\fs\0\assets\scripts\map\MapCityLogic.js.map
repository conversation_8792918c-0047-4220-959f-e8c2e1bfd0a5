{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts"], "names": ["_decorator", "Vec2", "Vec3", "MapBaseLayerLogic", "CityLogic", "MapUtil", "EventMgr", "LogicEvent", "ccclass", "property", "MapCityLogic", "onLoad", "on", "updateCitys", "onUpdateCitys", "updateCity", "onUpdateCity", "onDestroy", "targetOff", "areaIndex", "addIds", "removeIds", "updateIds", "_itemMap", "has", "i", "length", "addItem", "_cmd", "cityProxy", "getCity", "removeItem", "updateItem", "city", "getAreaIdByCellPoint", "x", "y", "setItemData", "item", "data", "cityData", "position", "mapCellToPixelPoint", "setPosition", "getComponent", "setCityData", "getIdByData", "cityId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG1BC,MAAAA,iB;;AACAC,MAAAA,S;;AACAC,MAAAA,O;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAPH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAWTU,Y,WAFpBF,OAAO,CAAC,cAAD,C,gBAAR,MAEqBE,YAFrB;AAAA;AAAA,kDAE4D;AAE9CC,QAAAA,MAAM,GAAS;AACrB,gBAAMA,MAAN;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,UAAvB,EAAmC,KAAKC,YAAxC,EAAsD,IAAtD;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,gBAAMD,SAAN;AACH;;AAESH,QAAAA,aAAa,CAACK,SAAD,EAAoBC,MAApB,EAAsCC,SAAtC,EAA2DC,SAA3D,EAAsF;AAEzG,cAAI,KAAKC,QAAL,CAAcC,GAAd,CAAkBL,SAAlB,CAAJ,EAAkC;AAC9B,iBAAK,IAAIM,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGL,MAAM,CAACM,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;AAC5C,mBAAKE,OAAL,CAAaR,SAAb,EAAwB,KAAKS,IAAL,CAAUC,SAAV,CAAoBC,OAApB,CAA4BV,MAAM,CAACK,CAAD,CAAlC,CAAxB;AACH;;AACD,iBAAK,IAAIA,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGJ,SAAS,CAACK,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,mBAAKM,UAAL,CAAgBZ,SAAhB,EAA2BE,SAAS,CAACI,CAAD,CAApC;AACH;;AACD,iBAAK,IAAIA,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGH,SAAS,CAACI,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,mBAAKO,UAAL,CAAgBb,SAAhB,EAA2B,KAAKS,IAAL,CAAUC,SAAV,CAAoBC,OAApB,CAA4BR,SAAS,CAACG,CAAD,CAArC,CAA3B;AACH;AACJ;AACJ;;AAEST,QAAAA,YAAY,CAACiB,IAAD,EAAwB;AAC1C,cAAId,SAAgB,GAAG;AAAA;AAAA,kCAAQe,oBAAR,CAA6BD,IAAI,CAACE,CAAlC,EAAqCF,IAAI,CAACG,CAA1C,CAAvB;;AACA,cAAI,KAAKb,QAAL,CAAcC,GAAd,CAAkBL,SAAlB,CAAJ,EAAkC;AAC9B,iBAAKQ,OAAL,CAAaR,SAAb,EAAwBc,IAAxB;AACH;AACJ;;AAEMI,QAAAA,WAAW,CAACC,IAAD,EAAaC,IAAb,EAA8B;AAC5C,cAAIC,QAAqB,GAAGD,IAA5B;AACA,cAAIE,QAAc,GAAG;AAAA;AAAA,kCAAQC,mBAAR,CAA4B,IAAIzC,IAAJ,CAASuC,QAAQ,CAACL,CAAlB,EAAqBK,QAAQ,CAACJ,CAA9B,CAA5B,CAArB;AACAE,UAAAA,IAAI,CAACK,WAAL,CAAiB,IAAIzC,IAAJ,CAASuC,QAAQ,CAACN,CAAlB,EAAqBM,QAAQ,CAACL,CAA9B,EAAiC,CAAjC,CAAjB;AACAE,UAAAA,IAAI,CAACM,YAAL;AAAA;AAAA,sCAA6BC,WAA7B,CAAyCL,QAAzC;AACH;;AAEMM,QAAAA,WAAW,CAACP,IAAD,EAAoB;AAClC,iBAAQA,IAAD,CAAsBQ,MAA7B;AACH;;AA5CuD,O", "sourcesContent": ["import { _decorator, Node, Vec2, Vec3 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport MapBaseLayerLogic from \"./MapBaseLayerLogic\";\nimport CityLogic from \"./entries/CityLogic\";\nimport MapUtil from \"./MapUtil\";\nimport { MapCityData } from \"./MapCityProxy\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('MapCityLogic')\n\nexport default class MapCityLogic extends MapBaseLayerLogic {\n\n    protected onLoad(): void {\n        super.onLoad();\n        EventMgr.on(LogicEvent.updateCitys, this.onUpdateCitys, this);\n        EventMgr.on(LogicEvent.updateCity, this.onUpdateCity, this);\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        super.onDestroy();\n    }\n\n    protected onUpdateCitys(areaIndex: number, addIds: number[], removeIds: number[], updateIds: number[]): void {\n    \n        if (this._itemMap.has(areaIndex)) {\n            for (let i: number = 0; i < addIds.length; i++) {\n                this.addItem(areaIndex, this._cmd.cityProxy.getCity(addIds[i]));\n            }\n            for (let i: number = 0; i < removeIds.length; i++) {\n                this.removeItem(areaIndex, removeIds[i]);\n            }\n            for (let i: number = 0; i < updateIds.length; i++) {\n                this.updateItem(areaIndex, this._cmd.cityProxy.getCity(updateIds[i]));\n            }\n        }\n    }\n\n    protected onUpdateCity(city:MapCityData):void {\n        let areaIndex:number = MapUtil.getAreaIdByCellPoint(city.x, city.y);\n        if (this._itemMap.has(areaIndex)) {\n            this.addItem(areaIndex, city);\n        }\n    }\n\n    public setItemData(item: Node, data: any): void {\n        let cityData: MapCityData = data as MapCityData;\n        let position: Vec2 = MapUtil.mapCellToPixelPoint(new Vec2(cityData.x, cityData.y));\n        item.setPosition(new Vec3(position.x, position.y, 0));\n        item.getComponent(CityLogic).setCityData(cityData);\n    }\n\n    public getIdByData(data: any): number {\n        return (data as MapCityData).cityId;\n    }\n}\n"]}