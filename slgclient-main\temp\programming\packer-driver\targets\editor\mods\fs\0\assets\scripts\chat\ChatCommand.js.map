{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts"], "names": ["ChatCommand", "NetManager", "ServerConfig", "ChatProxy", "EventMgr", "LogicEvent", "getInstance", "_instance", "destory", "onDestory", "constructor", "on", "chat_history", "onChatHistory", "chat_push", "onChat", "data", "console", "log", "code", "msg", "type", "_proxy", "updateWorldChat", "updateUnionChat", "emit", "updateChatHistory", "updateWorldChatList", "msgs", "updateUnionChatList", "targetOff", "clearData", "proxy", "chat", "sendData", "name", "chat_chat", "send", "join", "id", "chat_join", "exit", "chat_exit", "chatHistory"], "mappings": ";;;yFAQqBA,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANZC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,S;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;yBAEYL,W,GAAN,MAAMA,WAAN,CAAkB;AAC7B;AAEyB,eAAXM,WAAW,GAAgB;AACrC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIP,WAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKO,SAAZ;AACH,SAR4B,CAW7B;;;AAGqB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SArB4B,CAuB7B;;;AAEAG,QAAAA,WAAW,GAAG;AAAA,0CAbe;AAAA;AAAA,uCAaf;;AACV;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,YAAzB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,SAAzB,EAAoC,KAAKC,MAAzC,EAAiD,IAAjD;AACH;;AAESA,QAAAA,MAAM,CAACC,IAAD,EAAe;AAC3BC,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAsBF,IAAtB;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAGH,IAAI,CAACI,GAAL,CAASC,IAAT,IAAiB,CAApB,EAAsB;AAClB,mBAAKC,MAAL,CAAYC,eAAZ,CAA4BP,IAAI,CAACI,GAAjC;AACH,aAFD,MAEM,IAAIJ,IAAI,CAACI,GAAL,CAASC,IAAT,IAAiB,CAArB,EAAuB;AACzB,mBAAKC,MAAL,CAAYE,eAAZ,CAA4BR,IAAI,CAACI,GAAjC;AACH;;AACD;AAAA;AAAA,sCAASK,IAAT,CAAc;AAAA;AAAA,0CAAWC,iBAAzB;AACH;AACJ;;AAGSb,QAAAA,aAAa,CAACG,IAAD,EAAe;AAClCC,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA6BF,IAA7B;;AACA,cAAIA,IAAI,CAACG,IAAL,IAAa,CAAjB,EAAoB;AAChB,gBAAGH,IAAI,CAACI,GAAL,CAASC,IAAT,IAAiB,CAApB,EAAsB;AAClB,mBAAKC,MAAL,CAAYK,mBAAZ,CAAgCX,IAAI,CAACI,GAAL,CAASQ,IAAzC;AACH,aAFD,MAEM,IAAGZ,IAAI,CAACI,GAAL,CAASC,IAAT,IAAiB,CAApB,EAAsB;AACxB,mBAAKC,MAAL,CAAYO,mBAAZ,CAAgCb,IAAI,CAACI,GAAL,CAASQ,IAAzC;AACH;;AACD;AAAA;AAAA,sCAASH,IAAT,CAAc;AAAA;AAAA,0CAAWC,iBAAzB;AACH;AACJ;;AAEMjB,QAAAA,SAAS,GAAS;AACrB;AAAA;AAAA,oCAASqB,SAAT,CAAmB,IAAnB;AACH;;AAEMC,QAAAA,SAAS,GAAS;AACrB,eAAKT,MAAL,CAAYS,SAAZ;AACH;;AAEe,YAALC,KAAK,GAAc;AAC1B,iBAAO,KAAKV,MAAZ;AACH;;AAGMW,QAAAA,IAAI,CAACb,GAAD,EAAYC,IAAW,GAAG,CAA1B,EAAiC;AACxC,cAAIa,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAaC,SADH;AAEhBhB,YAAAA,GAAG,EAAE;AACDA,cAAAA,GAAG,EAACA,GADH;AAEDC,cAAAA,IAAI,EAACA;AAFJ;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWf,WAAX,GAAyB+B,IAAzB,CAA8BH,QAA9B;AACH;;AAEMI,QAAAA,IAAI,CAACjB,IAAD,EAAakB,EAAb,EAA4B;AACnC,cAAIL,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAaK,SADH;AAEhBpB,YAAAA,GAAG,EAAE;AACDC,cAAAA,IAAI,EAACA,IADJ;AAEDkB,cAAAA,EAAE,EAACA;AAFF;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWjC,WAAX,GAAyB+B,IAAzB,CAA8BH,QAA9B;AACH;;AAEMO,QAAAA,IAAI,CAACpB,IAAD,EAAakB,EAAb,EAA4B;AACnC,cAAIL,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAaO,SADH;AAEhBtB,YAAAA,GAAG,EAAE;AACDC,cAAAA,IAAI,EAACA,IADJ;AAEDkB,cAAAA,EAAE,EAACA;AAFF;AAFW,WAApB;AAOA;AAAA;AAAA,wCAAWjC,WAAX,GAAyB+B,IAAzB,CAA8BH,QAA9B;AACH;;AAEMS,QAAAA,WAAW,CAACtB,IAAD,EAAkB;AAChC,cAAIa,QAAa,GAAG;AAChBC,YAAAA,IAAI,EAAE;AAAA;AAAA,8CAAavB,YADH;AAEhBQ,YAAAA,GAAG,EAAE;AACDC,cAAAA,IAAI,EAACA;AADJ;AAFW,WAApB;AAMA;AAAA;AAAA,wCAAWf,WAAX,GAAyB+B,IAAzB,CAA8BH,QAA9B;AACH;;AA7G4B,O;;sBAAZlC,W", "sourcesContent": ["import { _decorator } from 'cc';\n\nimport { NetManager } from \"../network/socket/NetManager\";\nimport { ServerConfig } from \"../config/ServerConfig\";\nimport ChatProxy from \"./ChatProxy\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\nexport default class ChatCommand {\n    //单例\n    protected static _instance: ChatCommand;\n    public static getInstance(): ChatCommand {\n        if (this._instance == null) {\n            this._instance = new ChatCommand();\n        }\n        return this._instance;\n    }\n\n\n    //数据model\n    protected _proxy:ChatProxy = new ChatProxy();\n\n    public static destory(): boolean {\n        if (this._instance) {\n            this._instance.onDestory();\n            this._instance = null;\n            return true;\n        }\n        return false;\n    }\n\n    //数据model\n\n    constructor() {\n        EventMgr.on(ServerConfig.chat_history, this.onChatHistory, this)\n        EventMgr.on(ServerConfig.chat_push, this.onChat, this)\n    }\n\n    protected onChat(data:any):void{\n        console.log(\"onChat:\",data)\n        if (data.code == 0) {\n            if(data.msg.type == 0){\n                this._proxy.updateWorldChat(data.msg);\n            }else if (data.msg.type == 1){\n                this._proxy.updateUnionChat(data.msg);\n            }\n            EventMgr.emit(LogicEvent.updateChatHistory);\n        }\n    }\n\n\n    protected onChatHistory(data:any):void{\n        console.log(\"onChatHistory:\",data)\n        if (data.code == 0) {\n            if(data.msg.type == 0){\n                this._proxy.updateWorldChatList(data.msg.msgs);\n            }else if(data.msg.type == 1){\n                this._proxy.updateUnionChatList(data.msg.msgs);\n            }\n            EventMgr.emit(LogicEvent.updateChatHistory);\n        }\n    }\n\n    public onDestory(): void {\n        EventMgr.targetOff(this);\n    }\n\n    public clearData(): void {\n        this._proxy.clearData();\n    }\n\n    public get proxy(): ChatProxy {\n        return this._proxy;\n    }\n\n\n    public chat(msg:string,type:number = 0):void{\n        let sendData: any = {\n            name: ServerConfig.chat_chat,\n            msg: {\n                msg:msg,\n                type:type,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public join(type:number,id:number):void{\n        let sendData: any = {\n            name: ServerConfig.chat_join,\n            msg: {\n                type:type,\n                id:id,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public exit(type:number,id:number):void{\n        let sendData: any = {\n            name: ServerConfig.chat_exit,\n            msg: {\n                type:type,\n                id:id,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    public chatHistory(type:number):void{\n        let sendData: any = {\n            name: ServerConfig.chat_history,\n            msg: {\n                type:type,\n            }\n        };\n        NetManager.getInstance().send(sendData);\n    }\n\n    \n}\n"]}