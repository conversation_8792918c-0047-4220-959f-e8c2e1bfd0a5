{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts"], "names": ["Skill", "SkillProxy", "Map", "initSkillConfig", "cfgs", "_skillConfs", "_skillCfgMap", "clear", "i", "length", "_name", "console", "log", "_skillOutLine", "json", "push", "set", "cfgId", "skillConfs", "outLine", "getSkillCfg", "has", "get", "getSkill", "_skills", "updateSkills", "skills", "for<PERSON>ach", "skill", "Array", "from", "values"], "mappings": ";;;8BAGaA,K,EAMQC,U;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBANRD,K,GAAN,MAAMA,KAAN,CAAY;AAAA;AAAA,sCACH,CADG;;AAAA,yCAEC,CAFD;;AAAA,4CAGM,EAHN;AAAA;;AAAA,O;;yBAMEC,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,gDACkB,IAAIC,GAAJ,EADlB;;AAAA,+CAES,EAFT;;AAAA;;AAAA,2CAIY,IAAIA,GAAJ,EAJZ;AAAA;;AAKrBC,QAAAA,eAAe,CAACC,IAAD,EAAoB;AACtC,eAAKC,WAAL,GAAmB,EAAnB;;AACA,eAAKC,YAAL,CAAkBC,KAAlB;;AAEA,eAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGJ,IAAI,CAACK,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAE1C,gBAAIJ,IAAI,CAACI,CAAD,CAAJ,CAAQE,KAAR,IAAiB,eAArB,EAAsC;AAClCC,cAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACA,mBAAKC,aAAL,GAAqBT,IAAI,CAACI,CAAD,CAAJ,CAAQM,IAA7B;AACH,aAHD,MAGO;AACH,mBAAKT,WAAL,CAAiBU,IAAjB,CAAsBX,IAAI,CAACI,CAAD,CAAJ,CAAQM,IAA9B;;AACA,mBAAKR,YAAL,CAAkBU,GAAlB,CAAsBZ,IAAI,CAACI,CAAD,CAAJ,CAAQM,IAAR,CAAaG,KAAnC,EAA0Cb,IAAI,CAACI,CAAD,CAAJ,CAAQM,IAAlD;AACH;AACJ;AACJ;;AACoB,YAAVI,UAAU,GAAgB;AACjC,iBAAO,KAAKb,WAAZ;AACH;;AACiB,YAAPc,OAAO,GAAiB;AAC/B,iBAAO,KAAKN,aAAZ;AACH;;AACMO,QAAAA,WAAW,CAACH,KAAD,EAAyB;AACvC,cAAG,KAAKX,YAAL,CAAkBe,GAAlB,CAAsBJ,KAAtB,CAAH,EAAgC;AAC5B,mBAAO,KAAKX,YAAL,CAAkBgB,GAAlB,CAAsBL,KAAtB,CAAP;AACH,WAFD,MAEK;AACD,mBAAO,IAAP;AACH;AACJ;;AACMM,QAAAA,QAAQ,CAACN,KAAD,EAAqB;AAChC,cAAG,KAAKO,OAAL,CAAaH,GAAb,CAAiBJ,KAAjB,CAAH,EAA2B;AACvB,mBAAO,KAAKO,OAAL,CAAaF,GAAb,CAAiBL,KAAjB,CAAP;AACH,WAFD,MAEK;AACD,mBAAO,IAAP;AACH;AACJ;;AACMQ,QAAAA,YAAY,CAACC,MAAD,EAAkB;AAEjCA,UAAAA,MAAM,CAACC,OAAP,CAAeC,KAAK,IAAI;AACpB,iBAAKJ,OAAL,CAAaR,GAAb,CAAiBY,KAAK,CAACX,KAAvB,EAA8BW,KAA9B;AACH,WAFD;AAIH;;AACgB,YAANF,MAAM,GAAW;AACxB,iBAAOG,KAAK,CAACC,IAAN,CAAW,KAAKN,OAAL,CAAaO,MAAb,EAAX,CAAP;AACH;;AAjD2B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { SkillConf, SkillOutline } from \"../config/skill/Skill\";\n\nexport class Skill {\n    id:number = 0;\n    cfgId: number = 0;\n    generals: number[] = [];\n}\n\nexport default class SkillProxy {\n    private _skillCfgMap:Map<number, SkillConf> = new Map<number, SkillConf>();\n    protected _skillConfs: SkillConf[] = [];\n    protected _skillOutLine: SkillOutline;\n    protected _skills: Map<number, Skill> = new Map<number, Skill>();\n    public initSkillConfig(cfgs: any[]): void {\n        this._skillConfs = [];\n        this._skillCfgMap.clear();\n\n        for (let i: number = 0; i < cfgs.length; i++) {\n\n            if (cfgs[i]._name == \"skill_outline\") {\n                console.log(\"skill_outline\");\n                this._skillOutLine = cfgs[i].json;\n            } else {\n                this._skillConfs.push(cfgs[i].json);\n                this._skillCfgMap.set(cfgs[i].json.cfgId, cfgs[i].json);\n            }\n        }\n    }\n    public get skillConfs(): SkillConf[] {\n        return this._skillConfs;\n    }\n    public get outLine(): SkillOutline {\n        return this._skillOutLine;\n    }\n    public getSkillCfg(cfgId:number): SkillConf{\n        if(this._skillCfgMap.has(cfgId)){\n            return this._skillCfgMap.get(cfgId);\n        }else{\n            return null;\n        }\n    }\n    public getSkill(cfgId:number): Skill{\n        if(this._skills.has(cfgId)){\n            return this._skills.get(cfgId);\n        }else{\n            return null;\n        }\n    }\n    public updateSkills(skills: Skill[]) {\n\n        skills.forEach(skill => {\n            this._skills.set(skill.cfgId, skill);\n        });\n\n    }\n    public get skills():Skill[] {\n        return Array.from(this._skills.values())\n    }\n}\n"]}