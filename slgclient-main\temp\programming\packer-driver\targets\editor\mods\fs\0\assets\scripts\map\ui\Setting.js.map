{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts"], "names": ["_decorator", "Component", "Toggle", "AudioManager", "ccclass", "property", "Setting", "onLoad", "isMusic", "instance", "getConfiguration", "music", "isChecked", "isSound", "sound", "onClickClose", "playClick", "node", "active", "onClickMusic", "openMusic", "closeMusic", "onClickSound", "openSound", "closeSound"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;;AACvBC,MAAAA,Y,iBAAAA,Y;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAIjBM,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAACH,MAAD,C,UAGRG,QAAQ,CAACH,MAAD,C,oCANb,MACaI,OADb,SAC6BL,SAD7B,CACuC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAQnCM,QAAAA,MAAM,GAAE;AACJ,cAAIC,OAAO,GAAG;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,gBAAtB,CAAuC,IAAvC,CAAd;AACA,eAAKC,KAAL,CAAWC,SAAX,GAAuBJ,OAAvB;AAEA,cAAIK,OAAO,GAAG;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,gBAAtB,CAAuC,KAAvC,CAAd;AACA,eAAKI,KAAL,CAAWF,SAAX,GAAuBC,OAAvB;AACH;;AAEDE,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,4CAAaN,QAAb,CAAsBO,SAAtB;AACA,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAEDC,QAAAA,YAAY,GAAE;AACV;AAAA;AAAA,4CAAaV,QAAb,CAAsBO,SAAtB;;AAEA,cAAG,KAAKL,KAAL,CAAWC,SAAd,EAAwB;AACpB;AAAA;AAAA,8CAAaH,QAAb,CAAsBW,SAAtB;AACH,WAFD,MAEK;AACD;AAAA;AAAA,8CAAaX,QAAb,CAAsBY,UAAtB;AACH;AAEJ;;AAEDC,QAAAA,YAAY,GAAE;AACV;AAAA;AAAA,4CAAab,QAAb,CAAsBO,SAAtB;;AACA,cAAG,KAAKF,KAAL,CAAWF,SAAd,EAAwB;AACpB;AAAA;AAAA,8CAAaH,QAAb,CAAsBc,SAAtB;AACH,WAFD,MAEK;AACD;AAAA;AAAA,8CAAad,QAAb,CAAsBe,UAAtB;AACH;AACJ;;AAvCkC,O;;;;;iBAGV,I;;;;;;;iBAGA,I", "sourcesContent": ["\nimport { _decorator, Component, Toggle } from 'cc';\nimport { AudioManager } from '../../common/AudioManager';\nconst { ccclass, property } = _decorator;\n\n\n@ccclass('Setting')\nexport class Setting extends Component {\n    \n    @property(Toggle)\n    protected music:Toggle = null;\n    \n    @property(Toggle)\n    protected sound:Toggle = null;\n\n    onLoad(){\n        let isMusic = AudioManager.instance.getConfiguration(true);\n        this.music.isChecked = isMusic;\n\n        let isSound = AudioManager.instance.getConfiguration(false);\n        this.sound.isChecked = isSound;\n    }\n\n    onClickClose() {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n    }\n\n    onClickMusic(){\n        AudioManager.instance.playClick();\n\n        if(this.music.isChecked){\n            AudioManager.instance.openMusic();\n        }else{\n            AudioManager.instance.closeMusic();\n        }\n        \n    }\n\n    onClickSound(){\n        AudioManager.instance.playClick();\n        if(this.sound.isChecked){\n            AudioManager.instance.openSound();\n        }else{\n            AudioManager.instance.closeSound();\n        }\n    }\n}\n\n"]}