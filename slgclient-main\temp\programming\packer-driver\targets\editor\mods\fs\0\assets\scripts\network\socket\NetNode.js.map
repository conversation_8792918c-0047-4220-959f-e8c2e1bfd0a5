{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts"], "names": ["NetNode", "RequestObject", "NetEvent", "NetTimer", "WebSock", "EventMgr", "NetTipsType", "NetNodeState", "NetNodeType", "recvMessage", "Closed", "Array", "init", "console", "log", "_socket", "initSocket", "_timer", "initTimer", "_invokePool", "connect", "options", "_state", "Connecting", "updateNetTips", "_connectOptions", "autoReconnect", "changeConect", "closeSocket", "_autoReconnect", "_autoReconnectMax", "onConnected", "event", "onJsonMessage", "msg", "onMessage", "onError", "onClosed", "onGetKey", "on", "ServerHandShake", "onChecked", "Working", "emit", "ServerCheckLogin", "ServerTimeOut", "onTimeOut", "i", "_requests", "length", "req", "name", "rspName", "seq", "splice", "destroyInvoke", "tipsType", "isShow", "Requesting", "ReConnecting", "clearTimer", "resetHearbeatTimer", "sended", "socketSend", "cannelMsgTimer", "otherData", "ServerRequestSucess", "restReq", "tryConnet", "isAutoReconnect", "close", "_reconnectTimer", "setTimeout", "_reconnetTimeOut", "code", "reason", "_seqId", "send", "send_data", "force", "data", "createInvoke", "json", "p", "Promise", "resolve", "reject", "self", "ok", "rsp", "obj", "off", "sendPack", "push", "Checking", "error", "startTime", "Date", "getTime", "packAndSend", "schedule", "_receiveTime", "getHearbeat", "ctime", "cancel", "_keepAliveTimer", "clearInterval", "setInterval", "_heartTime", "clearTimeout", "destroy", "rejectReconnect", "shift", "invoke"], "mappings": ";;;oFAqCaA,O;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AApCJC,MAAAA,a,iBAAAA,a;AAAeC,MAAAA,Q,iBAAAA,Q;;AACfC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;iBAGGC,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;SAAAA,W,2BAAAA,W;;iBAMAC,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;SAAAA,Y,4BAAAA,Y;;iBAQAC,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;SAAAA,W,2BAAAA,W;;AAMNC,MAAAA,W,GAAc,a;;yBAUPT,O,GAAN,MAAMA,OAAN,CAAc;AAAA;AAAA,mDAC8B,IAD9B;;AAAA,kDAEkB,CAFlB;;AAAA,qDAGqB,CAHrB;;AAAA,0CAIgBO,YAAY,CAACG,MAJ7B;;AAAA,2CAKY,IALZ;;AAAA,0CAMW,IANX;;AAAA,mDASgB,IAThB;;AAAA,mDAUgB,IAVhB;;AAAA,8CAWc,KAAG,IAXjB;;AAAA,gDAYgB,KAAG,IAZnB;;AAAA,oDAaoB,IAAE,IAbtB;;AAAA,6CAcsBC,KAAK,EAd3B;;AAAA,6CAea,OAfb;;AAAA,0CAgBU,CAhBV;;AAAA,+CAiBW,EAjBX;AAAA;;AAmBjB;AACOC,QAAAA,IAAI,GAAG;AACVC,UAAAA,OAAO,CAACC,GAAR,CAAa,qBAAb;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,mCAAf;AACA,eAAKC,UAAL;AACA,eAAKC,MAAL,GAAc;AAAA;AAAA,qCAAd;AACA,eAAKC,SAAL;AACA,eAAKC,WAAL,GAAmB,EAAnB;AAEH;;AAEMC,QAAAA,OAAO,CAACC,OAAD,EAAsC;AAChDR,UAAAA,OAAO,CAACC,GAAR,CAAa,yBAAb,EAAsCO,OAAtC;;AACA,cAAI,KAAKN,OAAL,IAAgB,KAAKO,MAAL,IAAef,YAAY,CAACG,MAAhD,EAAwD;AACpD,iBAAKY,MAAL,GAAcf,YAAY,CAACgB,UAA3B;;AACA,gBAAI,CAAC,KAAKR,OAAL,CAAaK,OAAb,CAAqBC,OAArB,CAAL,EAAoC;AAChC,mBAAKG,aAAL,CAAmBlB,WAAW,CAACiB,UAA/B,EAA2C,KAA3C;AACA,qBAAO,KAAP;AACH;;AAED,gBAAI,KAAKE,eAAL,IAAwB,IAA5B,EAAkC;AAC9BJ,cAAAA,OAAO,CAACK,aAAR,GAAwBL,OAAO,CAACK,aAAhC;AACH;;AACD,iBAAKD,eAAL,GAAuBJ,OAAvB;AACA,iBAAKG,aAAL,CAAmBlB,WAAW,CAACiB,UAA/B,EAA2C,IAA3C;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAGD;AACJ;AACA;AACA;;;AACWI,QAAAA,YAAY,CAACN,OAAD,EAA4B;AAC3C,cAAGA,OAAO,IAAI,KAAKI,eAAnB,EAAmC;AAC/B;AACH;;AAED,cAAG,KAAKH,MAAL,IAAef,YAAY,CAACG,MAA/B,EAAsC;AAClC,iBAAKkB,WAAL;AACH;;AACD,eAAKR,OAAL,CAAaC,OAAb;AACH;;AAESL,QAAAA,UAAU,GAAG;AACnB,eAAKa,cAAL,GAAsB,KAAKC,iBAA3B;;AACA,eAAKf,OAAL,CAAagB,WAAb,GAA4BC,KAAD,IAAW;AAAE,iBAAKD,WAAL,CAAiBC,KAAjB;AAAyB,WAAjE;;AACA,eAAKjB,OAAL,CAAakB,aAAb,GAA8BC,GAAD,IAAS;AAAE,iBAAKC,SAAL,CAAeD,GAAf;AAAqB,WAA7D;;AACA,eAAKnB,OAAL,CAAaqB,OAAb,GAAwBJ,KAAD,IAAW;AAAE,iBAAKI,OAAL,CAAaJ,KAAb;AAAqB,WAAzD;;AACA,eAAKjB,OAAL,CAAasB,QAAb,GAAyBL,KAAD,IAAW;AAAE,iBAAKK,QAAL,CAAcL,KAAd;AAAsB,WAA3D;;AACA,eAAKjB,OAAL,CAAauB,QAAb,GAAwB,MAAM;AAAE,iBAAKA,QAAL;AAAiB,WAAjD;;AAEA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,oCAASC,eAArB,EAAsC,KAAKC,SAA3C,EAAsD,IAAtD;AACH;;AAGSH,QAAAA,QAAQ,GAAE;AAChB,eAAKhB,MAAL,GAAcf,YAAY,CAACmC,OAA3B,CADgB,CAGhB;AAEA;AACA;AACA;;AAEA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,oCAASC,gBAAvB;AAEH;;AAGS1B,QAAAA,SAAS,GAAE;AACjB,eAAKD,MAAL,CAAYL,IAAZ;;AAEA;AAAA;AAAA,oCAAS2B,EAAT,CAAY;AAAA;AAAA,oCAASM,aAArB,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACH;;AAESA,QAAAA,SAAS,CAACZ,GAAD,EAAS;AACxBrB,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAiCoB,GAAjC,EADwB,CAExB;;AACA,eAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,SAAL,CAAeC,MAAnC,EAA0CF,CAAC,EAA3C,EAA+C;AAC3C,gBAAIG,GAAG,GAAG,KAAKF,SAAL,CAAeD,CAAf,CAAV;;AACA,gBAAGb,GAAG,CAACiB,IAAJ,IAAYD,GAAG,CAACE,OAAhB,IAA2BlB,GAAG,CAACmB,GAAJ,IAAWH,GAAG,CAACG,GAA7C,EAAiD;AAC7CxC,cAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BoC,GAA/B;;AACA,mBAAKF,SAAL,CAAeM,MAAf,CAAsBP,CAAtB,EAAyB,CAAzB;;AACA,mBAAKQ,aAAL,CAAmBL,GAAnB;AACAH,cAAAA,CAAC;AAED;AAAA;AAAA,wCAASJ,IAAT,CAAclC,WAAd,EAA2ByB,GAA3B,EAAgCgB,GAAhC;AACH;AACJ;AAEJ;;AAES1B,QAAAA,aAAa,CAACgC,QAAD,EAAwBC,MAAxB,EAAyC;AAC5D,cAAID,QAAQ,IAAIlD,WAAW,CAACoD,UAA5B,EAAwC,CACpC;AAEH,WAHD,MAGO,IAAIF,QAAQ,IAAIlD,WAAW,CAACiB,UAA5B,EAAwC,CAE9C,CAFM,MAEA,IAAIiC,QAAQ,IAAIlD,WAAW,CAACqD,YAA5B,EAA0C,CAEhD;AACJ,SA3HgB,CA6HjB;;;AACU5B,QAAAA,WAAW,CAACC,KAAD,EAAQ;AACzBnB,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ;AACA,eAAKe,cAAL,GAAsB,KAAKC,iBAA3B;AAEA,eAAK8B,UAAL,GAJyB,CAKzB;;AACA,eAAKC,kBAAL,GANyB,CAQzB;AACH,SAvIgB,CAyIjB;;;AACUpB,QAAAA,SAAS,GAAG;AAClB5B,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EADkB,CAGlB;;AACA,eAAKU,aAAL,CAAmBlB,WAAW,CAACiB,UAA/B,EAA2C,KAA3C;AACA,eAAKC,aAAL,CAAmBlB,WAAW,CAACqD,YAA/B,EAA6C,KAA7C;;AAEA,cAAI,KAAKX,SAAL,CAAeC,MAAf,GAAwB,CAA5B,EAA+B;AAC3B,iBAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,SAAL,CAAeC,MAAnC,EAA0CF,CAAC,EAA3C,EAA+C;AAC3C,kBAAIG,GAAG,GAAG,KAAKF,SAAL,CAAeD,CAAf,CAAV;;AACA,kBAAGG,GAAG,CAACY,MAAJ,IAAc,KAAjB,EAAuB;AACnB,qBAAKC,UAAL,CAAgBb,GAAhB;AACH;AAEJ,aAP0B,CAQ3B;;AACH;AACJ,SA3JgB,CA6JjB;;;AACUf,QAAAA,SAAS,CAACD,GAAD,EAAY;AAC3B;AAEA,cAAGA,GAAH,EAAO;AAEF;AACD;AACA,gBAAGA,GAAG,CAACmB,GAAJ,IAAW,CAAd,EAAgB;AACZ;AAAA;AAAA,wCAASV,IAAT,CAAcT,GAAG,CAACiB,IAAlB,EAAwBjB,GAAxB,EADY,CAEZ;AACH,aAHD,MAGK;AACD,mBAAK8B,cAAL,CAAoB9B,GAApB;;AAEA,mBAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,SAAL,CAAeC,MAAnC,EAA0CF,CAAC,EAA3C,EAA+C;AAC3C,oBAAIG,GAAG,GAAG,KAAKF,SAAL,CAAeD,CAAf,CAAV;;AACA,oBAAGb,GAAG,CAACiB,IAAJ,IAAYD,GAAG,CAACE,OAAhB,IAA2BlB,GAAG,CAACmB,GAAJ,IAAWH,GAAG,CAACG,GAA1C,IAAiDH,GAAG,CAACY,MAAJ,IAAc,IAAlE,EAAuE;AAEnE;AACA,uBAAKd,SAAL,CAAeM,MAAf,CAAsBP,CAAtB,EAAyB,CAAzB;;AACAA,kBAAAA,CAAC;AAED;AAAA;AAAA,4CAASJ,IAAT,CAAclC,WAAd,EAA2ByB,GAA3B,EAAgCgB,GAAhC;AACA;AAAA;AAAA,4CAASP,IAAT,CAAcT,GAAG,CAACiB,IAAlB,EAAwBjB,GAAxB,EAA6BgB,GAAG,CAACe,SAAjC;AACA,uBAAKV,aAAL,CAAmBL,GAAnB;AACA;AAAA;AAAA,4CAASP,IAAT,CAAc;AAAA;AAAA,4CAASuB,mBAAvB,EAA2ChC,GAA3C;AACH;AACJ;AAEJ;AAEJ;AACJ;;AAESE,QAAAA,OAAO,CAACJ,KAAD,EAAQ;AACrBnB,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAuBkB,KAAvB,EADqB,CAGrB;;AACA,eAAK4B,UAAL;AACA,eAAKO,OAAL;AACA,eAAKC,SAAL;AACH;;AAES/B,QAAAA,QAAQ,CAACL,KAAD,EAAQ;AACtBnB,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAwBkB,KAAxB,EADsB,CAGtB;;AACA,eAAK4B,UAAL;AACA,eAAKQ,SAAL;AACH;;AAESD,QAAAA,OAAO,GAAE;AACf,eAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,SAAL,CAAeC,MAAnC,EAA0CF,CAAC,EAA3C,EAA+C;AAC3C,gBAAIG,GAAG,GAAG,KAAKF,SAAL,CAAeD,CAAf,CAAV;AACAG,YAAAA,GAAG,CAACY,MAAJ,GAAa,KAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACWM,QAAAA,SAAS,GAAE;AACdvD,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAwB,KAAKe,cAA7B;;AACA,cAAI,KAAKwC,eAAL,EAAJ,EAA4B;AACxB,iBAAK7C,aAAL,CAAmBlB,WAAW,CAACqD,YAA/B,EAA6C,IAA7C;;AAGA,iBAAK5C,OAAL,CAAauD,KAAb;;AACA,iBAAKhD,MAAL,GAAcf,YAAY,CAACG,MAA3B;AAEA,iBAAK6D,eAAL,GAAuBC,UAAU,CAAC,MAAM;AACpC3D,cAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACA,mBAAKM,OAAL,CAAa,KAAKK,eAAlB;;AACA,kBAAI,KAAKI,cAAL,GAAsB,CAA1B,EAA6B;AACzB,qBAAKA,cAAL,IAAuB,CAAvB;AACH;AAGJ,aARgC,EAQ9B,KAAK4C,gBARyB,CAAjC;AASH,WAhBD,MAgBO;AACH,iBAAKnD,MAAL,GAAcf,YAAY,CAACG,MAA3B;AACH;AAEJ,SAhPgB,CAkPjB;;;AACOkB,QAAAA,WAAW,CAAC8C,IAAD,EAAgBC,MAAhB,EAAiC;AAC/C,eAAKf,UAAL;AACA,eAAKZ,SAAL,CAAeC,MAAf,GAAwB,CAAxB;AACA,eAAK2B,MAAL,GAAc,CAAd;AACA,eAAK/C,cAAL,GAAsB,CAAtB;;AAEA,cAAI,KAAKd,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAauD,KAAb,CAAmBI,IAAnB,EAAyBC,MAAzB;AACH,WAFD,MAEO;AACH,iBAAKrD,MAAL,GAAcf,YAAY,CAACG,MAA3B;AACH;AACJ;;AAKMmE,QAAAA,IAAI,CAACC,SAAD,EAAeb,SAAf,EAA6Bc,KAAc,GAAG,KAA9C,EAAkE;AAEzE,cAAIC,IAAI,GAAG,KAAKC,YAAL,EAAX;AACAD,UAAAA,IAAI,CAACE,IAAL,GAAYJ,SAAZ;AACAE,UAAAA,IAAI,CAAC5B,OAAL,GAAe0B,SAAS,CAAC3B,IAAzB;AACA6B,UAAAA,IAAI,CAACf,SAAL,GAAiBA,SAAjB;AAEA,cAAIkB,CAAC,GAAG,IAAIC,OAAJ,CAAY,UAASC,OAAT,EAAkBC,MAAlB,EAAyB;AACzC,gBAAIC,IAAI,GAAG,IAAX;;AACA,gBAAIC,EAAE,GAAG,CAACC,GAAD,EAAMvC,GAAN,KAAY;AACjB,kBAAG8B,IAAI,IAAI9B,GAAX,EAAe;AACX,oBAAIwC,GAAG,GAAG;AACNxC,kBAAAA,GAAG,EAAE8B,IAAI,CAACE,IADJ;AAENO,kBAAAA,GAAG,EAAEA;AAFC,iBAAV;AAIA;AAAA;AAAA,0CAASE,GAAT,CAAalF,WAAb,EAA0B+E,EAA1B,EAA8BD,IAA9B,EALW,CAMX;;AACAF,gBAAAA,OAAO,CAACK,GAAD,CAAP;AACH;AACJ,aAVD;;AAYA;AAAA;AAAA,sCAASnD,EAAT,CAAY9B,WAAZ,EAAyB+E,EAAzB,EAA6BD,IAA7B;AAEH,WAhBO,CAAR;AAkBA,eAAKK,QAAL,CAAcZ,IAAd,EAAmBD,KAAnB;AACA,iBAAOI,CAAP;AACH,SA9RgB,CAgSjB;;;AACOS,QAAAA,QAAQ,CAACF,GAAD,EAAqBX,KAAc,GAAG,KAAtC,EAAsD;AACjE,cAAI,KAAKzD,MAAL,IAAef,YAAY,CAACmC,OAA5B,IAAuCqC,KAA3C,EAAkD;AAC9C,iBAAKhB,UAAL,CAAgB2B,GAAhB;;AACA,iBAAK1C,SAAL,CAAe6C,IAAf,CAAoBH,GAApB;AACH,WAHD,MAKK,IAAI,KAAKpE,MAAL,IAAef,YAAY,CAACuF,QAA5B,IACL,KAAKxE,MAAL,IAAef,YAAY,CAACgB,UAD3B,EACuC;AACxC,iBAAKyB,SAAL,CAAe6C,IAAf,CAAoBH,GAApB;AACH,WAHI,MAKA,IAAG,KAAKpE,MAAL,IAAef,YAAY,CAACG,MAA/B,EAAsC;AACvC,iBAAKU,OAAL,CAAa,KAAKK,eAAlB;;AACA,iBAAKuB,SAAL,CAAe6C,IAAf,CAAoBH,GAApB;AACH,WAHI,MAKA;AACD7E,YAAAA,OAAO,CAACkF,KAAR,CAAc,6CAA6C,KAAKzE,MAAhE;AACH;;AACD,iBAAO,KAAP;AACH;AAGD;AACJ;AACA;AACA;;;AACWyC,QAAAA,UAAU,CAAC2B,GAAD,EAAmB;AAChCA,UAAAA,GAAG,CAACrC,GAAJ,GAAUqC,GAAG,CAACR,IAAJ,CAAS7B,GAAT,GAAe,KAAKuB,MAA9B;AACAc,UAAAA,GAAG,CAACM,SAAJ,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,EAAhB;;AACA,eAAKnF,OAAL,CAAaoF,WAAb,CAAyBT,GAAG,CAACR,IAA7B;;AACA,eAAKN,MAAL,IAAa,CAAb;AACAc,UAAAA,GAAG,CAAC5B,MAAJ,GAAa,IAAb;;AACA,eAAK7C,MAAL,CAAYmF,QAAZ,CAAqBV,GAAG,CAACR,IAAzB,EAA8B,KAAKmB,YAAnC;AACH;AAGD;AACJ;AACA;;;AACWC,QAAAA,WAAW,GAAE;AAChB,cAAIZ,GAAG,GAAG,KAAKT,YAAL,EAAV,CADgB,CACc;;AAC9BS,UAAAA,GAAG,CAACR,IAAJ,GAAW;AAAC/B,YAAAA,IAAI,EAAC,WAAN;AAAkBjB,YAAAA,GAAG,EAAC;AAACqE,cAAAA,KAAK,EAAC,IAAIN,IAAJ,GAAWC,OAAX;AAAP,aAAtB;AAAmD7C,YAAAA,GAAG,EAAC;AAAvD,WAAX;AACAqC,UAAAA,GAAG,CAACtC,OAAJ,GAAc,WAAd;AACAsC,UAAAA,GAAG,CAACrC,GAAJ,GAAU,CAAV;AACA,iBAAOqC,GAAP;AAEH;AAGD;;;AACU1B,QAAAA,cAAc,CAACgB,IAAQ,GAAG,IAAZ,EAAkB;AACtC,eAAK/D,MAAL,CAAYuF,MAAZ,CAAmBxB,IAAnB;AACH;;AAESnB,QAAAA,kBAAkB,GAAG;AAC3B,cAAI,KAAK4C,eAAL,KAAyB,IAA7B,EAAmC;AAC/BC,YAAAA,aAAa,CAAC,KAAKD,eAAN,CAAb;AACH;;AAED,eAAKA,eAAL,GAAuBE,WAAW,CAAC,MAAM;AACrC,iBAAKf,QAAL,CAAc,KAAKU,WAAL,EAAd;AACH,WAFiC,EAE/B,KAAKM,UAF0B,CAAlC;AAGH;;AAEShD,QAAAA,UAAU,GAAG;AACnB,cAAI,KAAK6C,eAAL,KAAyB,IAA7B,EAAmC;AAC/BI,YAAAA,YAAY,CAAC,KAAKJ,eAAN,CAAZ;AACH;;AACD,cAAI,KAAKlC,eAAL,KAAyB,IAA7B,EAAmC;AAC/BsC,YAAAA,YAAY,CAAC,KAAKtC,eAAN,CAAZ;AACH;;AACD,eAAKtD,MAAL,CAAY6F,OAAZ;AACH;;AAEMzC,QAAAA,eAAe,GAAG;AACrB,iBAAO,KAAKxC,cAAL,IAAuB,CAA9B;AACH;;AAEMkF,QAAAA,eAAe,GAAG;AACrB,eAAKlF,cAAL,GAAsB,CAAtB;AACA,eAAK+B,UAAL;AACH;;AAOSqB,QAAAA,YAAY,GAAgB;AAClC;AACA,cAAI,KAAK9D,WAAL,CAAiB8B,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAO,KAAK9B,WAAL,CAAiB6F,KAAjB,EAAP;AACH;;AACD,iBAAO;AAAA;AAAA,+CAAP;AACH;;AAESzD,QAAAA,aAAa,CAAC0D,MAAD,EAA4B;AAC/CA,UAAAA,MAAM,CAACH,OAAP;;AACA,eAAK3F,WAAL,CAAiB0E,IAAjB,CAAsBoB,MAAtB,EAF+C,CAG/C;;AACH;;AAtYgB,O", "sourcesContent": ["\nimport { RequestObject, NetEvent } from \"./NetInterface\";\nimport { NetTimer } from \"./NetTimer\";\nimport { WebSock } from \"./WebSock\";\nimport { EventMgr } from \"../../utils/EventMgr\";\n\n\nexport enum NetTipsType {\n    Connecting,\n    ReConnecting,\n    Requesting,\n}\n\nexport enum NetNodeState {\n    Closed,                     // 已关闭\n    Connecting,                 // 连接中\n    Checking,                   // 验证中\n    Working,                    // 可传输数据\n}\n\n\nexport enum NetNodeType {\n    BaseServer,                     //主要服务器\n    ChatServer,                     //聊天服务器\n}\n \n\nconst recvMessage = \"recvMessage\";\n\nexport interface NetConnectOptions {\n    host?: string,              // 地址\n    port?: number,              // 端口\n    url?: string,               // url，与地址+端口二选一\n    autoReconnect?: number,     // -1 永久重连，0不自动重连，其他正整数为自动重试次数\n    type?:NetNodeType,          //服务器类型\n}\n\nexport class NetNode {\n    protected _connectOptions: NetConnectOptions = null;\n    protected _autoReconnect: number = 0;\n    protected _autoReconnectMax: number = 3;\n    protected _state: NetNodeState = NetNodeState.Closed;                   // 节点当前状态\n    protected _socket: WebSock = null;                                      // Socket对象（可能是原生socket、websocket、wx.socket...)\n    protected _timer:NetTimer = null;\n\n\n    protected _keepAliveTimer: any = null;                                  // 心跳定时器\n    protected _reconnectTimer: any = null;                                  // 重连定时器\n    protected _heartTime: number = 10*1000;                                 // 心跳间隔\n    protected _receiveTime: number = 10*1000;                               // 多久没收到数据断开\n    protected _reconnetTimeOut: number = 2*1000;                            // 重连间隔\n    protected _requests: RequestObject[] = Array<RequestObject>();          // 请求列表\n    protected _maxSeqId :number = 1000000;\n    protected _seqId :number = 1;\n    protected _invokePool:any = [];\n\n    /********************** 网络相关处理 *********************/\n    public init() {\n        console.log(`NetNode init socket`);\n        this._socket = new WebSock();\n        this.initSocket();\n        this._timer = new NetTimer();\n        this.initTimer();\n        this._invokePool = [];\n        \n    }\n\n    public connect(options: NetConnectOptions): boolean {\n        console.log(`NetNode connect socket:`,options);\n        if (this._socket && this._state == NetNodeState.Closed) {\n            this._state = NetNodeState.Connecting;\n            if (!this._socket.connect(options)) {\n                this.updateNetTips(NetTipsType.Connecting, false);\n                return false;\n            }\n\n            if (this._connectOptions == null) {\n                options.autoReconnect = options.autoReconnect;\n            }\n            this._connectOptions = options;\n            this.updateNetTips(NetTipsType.Connecting, true);\n            return true;\n        }\n        return false;\n    }\n\n\n    /**\n     * 更换线路\n     * @param options \n     */\n    public changeConect(options: NetConnectOptions){\n        if(options == this._connectOptions){\n            return;\n        }\n\n        if(this._state != NetNodeState.Closed){\n            this.closeSocket();\n        }\n        this.connect(options);\n    }\n\n    protected initSocket() {\n        this._autoReconnect = this._autoReconnectMax;\n        this._socket.onConnected = (event) => { this.onConnected(event) };\n        this._socket.onJsonMessage = (msg) => { this.onMessage(msg) };\n        this._socket.onError = (event) => { this.onError(event) };\n        this._socket.onClosed = (event) => { this.onClosed(event) };\n        this._socket.onGetKey = () => { this.onGetKey() };\n\n        EventMgr.on(NetEvent.ServerHandShake, this.onChecked, this);\n    }\n\n\n    protected onGetKey(){\n        this._state = NetNodeState.Working;\n\n        // if(this._connectOptions.type == NetNodeType.BaseServer){\n            \n        // }else{\n        //     this.onChecked();\n        // }\n\n        EventMgr.emit(NetEvent.ServerCheckLogin);\n        \n    }\n\n\n    protected initTimer(){\n        this._timer.init();\n        \n        EventMgr.on(NetEvent.ServerTimeOut, this.onTimeOut, this);\n    }\n\n    protected onTimeOut(msg:any){\n        console.log(\"NetNode onTimeOut!\",msg)\n        //超时删除 请求队列\n        for (var i = 0; i < this._requests.length;i++) {\n            let req = this._requests[i];\n            if(msg.name == req.rspName && msg.seq == req.seq){\n                console.log(\"NetNode remove:\", req)\n                this._requests.splice(i, 1);\n                this.destroyInvoke(req);\n                i--;\n\n                EventMgr.emit(recvMessage, msg, req);\n            }       \n        }\n\n    }\n\n    protected updateNetTips(tipsType: NetTipsType, isShow: boolean) {\n        if (tipsType == NetTipsType.Requesting) {\n            // EventMgr.emit(NetEvent.ServerRequesting, isShow);\n\n        } else if (tipsType == NetTipsType.Connecting) {\n\n        } else if (tipsType == NetTipsType.ReConnecting) {\n\n        }\n    }\n\n    // 网络连接成功\n    protected onConnected(event) {\n        console.log(\"NetNode onConnected!\")\n        this._autoReconnect = this._autoReconnectMax;\n\n        this.clearTimer();\n        // 启动心跳\n        this.resetHearbeatTimer();\n\n        // EventMgr.emit(NetEvent.ServerConnected);\n    }\n\n    // 连接验证成功，进入工作状态\n    protected onChecked() {\n        console.log(\"NetNode onChecked!\")\n        \n        // 关闭连接或重连中的状态显示\n        this.updateNetTips(NetTipsType.Connecting, false);\n        this.updateNetTips(NetTipsType.ReConnecting, false);\n\n        if (this._requests.length > 0) {\n            for (var i = 0; i < this._requests.length;i++) {\n                let req = this._requests[i];\n                if(req.sended == false){\n                    this.socketSend(req);\n                }\n\n            }\n            // 如果还有等待返回的请求，启动网络请求层\n        }\n    }\n\n    // 接收到一个完整的消息包\n    protected onMessage(msg): void {\n        // console.log(`NetNode onMessage msg ` ,msg);\n        \n        if(msg){\n\n             // 接受到数据，重新定时收数据计时器\n            //推送\n            if(msg.seq == 0){\n                EventMgr.emit(msg.name, msg);\n                // console.log(\"all_push:\",msg.name, msg);\n            }else{\n                this.cannelMsgTimer(msg);\n\n                for (var i = 0; i < this._requests.length;i++) {\n                    let req = this._requests[i];\n                    if(msg.name == req.rspName && msg.seq == req.seq && req.sended == true){\n                        \n                        // console.log(\"req:\", req);\n                        this._requests.splice(i, 1);\n                        i--;\n               \n                        EventMgr.emit(recvMessage, msg, req);\n                        EventMgr.emit(msg.name, msg, req.otherData);\n                        this.destroyInvoke(req);\n                        EventMgr.emit(NetEvent.ServerRequestSucess,msg);\n                    }       \n                }\n\n            }\n           \n        }\n    }\n\n    protected onError(event) {\n        console.log(\"onError:\",event);\n\n        //出错后清空定时器 那后断开服务 尝试链接\n        this.clearTimer();\n        this.restReq();\n        this.tryConnet();\n    }\n\n    protected onClosed(event) {\n        console.log(\"onClosed:\",event);\n\n        //出错后\n        this.clearTimer();\n        this.tryConnet();\n    }\n\n    protected restReq(){\n        for (var i = 0; i < this._requests.length;i++) {\n            let req = this._requests[i];\n            req.sended = false;            \n        }\n    }\n\n    /**\n     * 重连\n     */\n    public tryConnet(){\n        console.log(\"tryConnet\",this._autoReconnect)\n        if (this.isAutoReconnect()) {\n            this.updateNetTips(NetTipsType.ReConnecting, true);\n\n            \n            this._socket.close();\n            this._state = NetNodeState.Closed;\n\n            this._reconnectTimer = setTimeout(() => {\n                console.log(\"NetNode tryConnet!\")\n                this.connect(this._connectOptions);\n                if (this._autoReconnect > 0) {\n                    this._autoReconnect -= 1;\n                }\n\n\n            }, this._reconnetTimeOut);\n        } else {\n            this._state = NetNodeState.Closed;\n        }\n\n    }\n\n    // 只是关闭Socket套接字（仍然重用缓存与当前状态）\n    public closeSocket(code?: number, reason?: string) {\n        this.clearTimer();\n        this._requests.length = 0;\n        this._seqId = 1;\n        this._autoReconnect = 0;\n\n        if (this._socket) {\n            this._socket.close(code, reason);\n        } else {\n            this._state = NetNodeState.Closed;\n        }\n    }\n\n\n\n\n    public send(send_data:any,otherData:any,force: boolean = false) :Promise<any>{\n   \n        let data = this.createInvoke();\n        data.json = send_data;\n        data.rspName = send_data.name;\n        data.otherData = otherData;\n  \n        let p = new Promise(function(resolve, reject){\n            let self = this;\n            let ok = (rsp, req)=>{\n                if(data == req){\n                    let obj = {\n                        req: data.json,\n                        rsp: rsp\n                    };\n                    EventMgr.off(recvMessage, ok, self);\n                    // console.log(\"ok\");\n                    resolve(obj);\n                }\n            }\n          \n            EventMgr.on(recvMessage, ok, self);\n      \n        });\n         \n        this.sendPack(data,force);\n        return p;\n    }\n\n    // 发起请求，如果当前处于重连中，进入缓存列表等待重连完成后发送\n    public sendPack(obj: RequestObject, force: boolean = false) :boolean {\n        if (this._state == NetNodeState.Working || force) {\n            this.socketSend(obj);\n            this._requests.push(obj);\n        } \n        \n        else if (this._state == NetNodeState.Checking ||\n            this._state == NetNodeState.Connecting) {\n            this._requests.push(obj);\n        } \n        \n        else if(this._state == NetNodeState.Closed){\n            this.connect(this._connectOptions);\n            this._requests.push(obj);\n        }\n        \n        else {\n            console.error(\"NetNode request error! current state is \" + this._state);\n        }\n        return false;\n    }\n\n\n    /**\n     * 打包发送\n     * @param obj \n     */\n    public socketSend(obj:RequestObject){\n        obj.seq = obj.json.seq = this._seqId;\n        obj.startTime = new Date().getTime()\n        this._socket.packAndSend(obj.json);\n        this._seqId+=1;\n        obj.sended = true;\n        this._timer.schedule(obj.json,this._receiveTime);\n    }\n\n\n    /**\n     * 心跳\n     */\n    public getHearbeat(){\n        var obj = this.createInvoke();//new RequestObject();\n        obj.json = {name:\"heartbeat\",msg:{ctime:new Date().getTime()},seq:0};\n        obj.rspName = \"heartbeat\";\n        obj.seq = 0;\n        return obj;\n        \n    }\n    \n\n    /********************** 心跳、超时相关处理 *********************/\n    protected cannelMsgTimer(data:any = null) {\n        this._timer.cancel(data);\n    }\n\n    protected resetHearbeatTimer() {\n        if (this._keepAliveTimer !== null) {\n            clearInterval(this._keepAliveTimer);\n        }\n\n        this._keepAliveTimer = setInterval(() => {\n            this.sendPack(this.getHearbeat());\n        }, this._heartTime);\n    }\n\n    protected clearTimer() {\n        if (this._keepAliveTimer !== null) {\n            clearTimeout(this._keepAliveTimer);\n        }\n        if (this._reconnectTimer !== null) {\n            clearTimeout(this._reconnectTimer);\n        }\n        this._timer.destroy();\n    }\n\n    public isAutoReconnect() {\n        return this._autoReconnect != 0;\n    }\n\n    public rejectReconnect() {\n        this._autoReconnect = 0;\n        this.clearTimer();\n    }\n\n\n\n\n\n\n    protected createInvoke():RequestObject{\n        // console.log(\"createInvoke_invokePool :\",this._invokePool.length)\n        if (this._invokePool.length > 0) {\n            return this._invokePool.shift();\n        }\n        return new RequestObject();\n    }\n\n    protected destroyInvoke(invoke:RequestObject):void {\n        invoke.destroy();\n        this._invokePool.push(invoke);\n        // console.log(\"destroyInvoke_invokePool :\",this._invokePool.length)\n    }\n}\n"]}