System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _decorator, Component, tween, UIOpacity, _dec, _class, _crd, ccclass, property, PanelOut;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "33c14fX9dBEbo67kq4aZweX", "PanelOut", undefined);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PanelOut", PanelOut = (_dec = ccclass('PanelOut'), _dec(_class = class PanelOut extends Component {
        onEnable() {
          console.log("PanelOut onEnable");
          this.node.getComponent(UIOpacity).opacity = 125;
          let lt = tween(this.node.getComponent(UIOpacity)).to(0.2, {
            opacity: 255
          }, {
            easing: 'sineOut'
          });
          lt.start();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=PanelOut.js.map