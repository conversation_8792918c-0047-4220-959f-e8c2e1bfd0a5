{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts"], "names": ["_decorator", "Component", "Label", "Prefab", "Node", "ScrollView", "instantiate", "GeneralCommand", "GeneralItemLogic", "GeneralItemType", "EventMgr", "AudioManager", "LogicEvent", "ListLogic", "ccclass", "property", "GeneralComposeLogic", "onLoad", "_generalNode", "generalItemPrefab", "parent", "generalItemParent", "onEnable", "on", "openGeneralSelect", "selectItem", "updataView", "onDisable", "targetOff", "cfg", "curData", "index", "_gIdsArr", "indexOf", "id", "splice", "push", "setData", "cfgData", "_currData", "_cfgData", "com", "getComponent", "updateItem", "nameLab", "string", "name", "updateGeneral", "list", "getInstance", "proxy", "getComposeGenerals", "cfgId", "listTemp", "concat", "for<PERSON>ach", "item", "type", "GeneralSelect", "comp", "scrollView", "node", "composeNode", "active", "length", "star_lv", "star", "onCompose", "instance", "playClick", "composeGeneral"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;;AAG1DC,MAAAA,c;;AAEAC,MAAAA,gB;AAAoBC,MAAAA,e,iBAAAA,e;;AAClBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,S;;;;;;;OARD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;yBAWTgB,mB,WADpBF,OAAO,CAAC,qBAAD,C,UAGHC,QAAQ,CAACb,KAAD,C,UAGRa,QAAQ,CAACZ,MAAD,C,UAGRY,QAAQ,CAACX,IAAD,C,UAIRW,QAAQ,CAACV,UAAD,C,UAGRU,QAAQ,CAACX,IAAD,C,oCAhBb,MACqBY,mBADrB,SACkDf,SADlD,CAC4D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,6CAkBxB,IAlBwB;;AAAA,4CAmBvB,IAnBuB;;AAAA,gDAqB5B,IArB4B;;AAAA,4CAsB5B,EAtB4B;AAAA;;AAwB9CgB,QAAAA,MAAM,GAAO;AACnB,eAAKC,YAAL,GAAoBZ,WAAW,CAAC,KAAKa,iBAAN,CAA/B;AACA,eAAKD,YAAL,CAAkBE,MAAlB,GAA2B,KAAKC,iBAAhC;AACH;;AAESC,QAAAA,QAAQ,GAAO;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,iBAAvB,EAA0C,KAAKC,UAA/C,EAA2D,IAA3D;AACA,eAAKC,UAAL;AACH;;AAESC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOH,QAAAA,UAAU,CAACI,GAAD,EAASC,OAAT,EAA0B;AACxC,cAAIC,KAAK,GAAG,KAAKC,QAAL,CAAcC,OAAd,CAAsBH,OAAO,CAACI,EAA9B,CAAZ;;AACA,cAAGH,KAAK,IAAI,CAAZ,EAAc;AAEV,iBAAKC,QAAL,CAAcG,MAAd,CAAqBJ,KAArB,EAA2B,CAA3B;AACH,WAHD,MAGK;AACD,iBAAKC,QAAL,CAAcI,IAAd,CAAmBN,OAAO,CAACI,EAA3B;AACH;;AACD,eAAKR,UAAL;AAEH;;AAEMW,QAAAA,OAAO,CAACC,OAAD,EAAaR,OAAb,EAA8B;AACxC,eAAKS,SAAL,GAAiBT,OAAjB;AACA,eAAKU,QAAL,GAAgBF,OAAhB;AACA,eAAKN,QAAL,GAAgB,EAAhB;;AACA,cAAIS,GAAG,GAAG,KAAKvB,YAAL,CAAkBwB,YAAlB;AAAA;AAAA,mDAAV;;AACA,cAAGD,GAAH,EAAO;AACHA,YAAAA,GAAG,CAACE,UAAJ,CAAe,KAAKJ,SAApB;AACH;;AAED,eAAKK,OAAL,CAAaC,MAAb,GAAsB,KAAKL,QAAL,CAAcM,IAApC;AAEA,eAAKC,aAAL;AACA,eAAKrB,UAAL;AACH;;AAGSqB,QAAAA,aAAa,GAAO;AAC1B,cAAIC,IAAU,GAAG;AAAA;AAAA,gDAAeC,WAAf,GAA6BC,KAA7B,CAAmCC,kBAAnC,CAAsD,KAAKX,QAAL,CAAcY,KAApE,EAA0E,KAAKb,SAAL,CAAeL,EAAzF,CAAjB;AACA,cAAImB,QAAQ,GAAGL,IAAI,CAACM,MAAL,EAAf;AAGAD,UAAAA,QAAQ,CAACE,OAAT,CAAiBC,IAAI,IAAI;AACrBA,YAAAA,IAAI,CAACC,IAAL,GAAY;AAAA;AAAA,oDAAgBC,aAA5B;AACH,WAFD;AAIA,cAAIC,IAAI,GAAG,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBnB,YAArB;AAAA;AAAA,qCAAX;AACAiB,UAAAA,IAAI,CAACtB,OAAL,CAAagB,QAAb;AAEH;;AAGO3B,QAAAA,UAAU,GAAO;AACrB,eAAKoC,WAAL,CAAiBC,MAAjB,GAA4B,KAAK/B,QAAL,CAAcgC,MAAd,GAAuB,CAAxB,IAA+B,KAAKzB,SAAL,CAAe0B,OAAf,GAAyB,KAAKzB,QAAL,CAAc0B,IAAjG;AACH;;AAGSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,gDAAepB,WAAf,GAA6BqB,cAA7B,CAA4C,KAAK/B,SAAL,CAAeL,EAA3D,EAA8D,KAAKF,QAAnE;AACH;;AAzFuD,O;;;;;iBAGvC,I;;;;;;;iBAGW,I;;;;;;;iBAGF,I;;;;;;;iBAIF,I;;;;;;;iBAGJ,I", "sourcesContent": ["import { _decorator, Component, Label, Prefab, Node, ScrollView, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport { GeneralConfig, GeneralData } from \"../../general/GeneralProxy\";\nimport GeneralItemLogic, { GeneralItemType } from \"./GeneralItemLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\nimport ListLogic from '../../utils/ListLogic';\n\n@ccclass('GeneralComposeLogic')\nexport default class GeneralComposeLogic  extends Component {\n\n    @property(Label)\n    nameLab: Label = null;\n\n    @property(Prefab)\n    generalItemPrefab: Prefab = null;\n\n    @property(Node)\n    generalItemParent: Node = null;\n\n\n    @property(ScrollView)\n    scrollView:ScrollView = null;\n\n    @property(Node)\n    composeNode: Node = null;\n\n    private _currData:GeneralData = null;\n    private _cfgData:GeneralConfig = null;\n\n    private _generalNode:Node = null;\n    private _gIdsArr:number[] = [];\n\n    protected onLoad():void{\n        this._generalNode = instantiate(this.generalItemPrefab);\n        this._generalNode.parent = this.generalItemParent;\n    }\n\n    protected onEnable():void{\n        EventMgr.on(LogicEvent.openGeneralSelect, this.selectItem, this); \n        this.updataView();\n    }\n\n    protected onDisable():void{\n        EventMgr.targetOff(this);\n    }\n\n    private selectItem(cfg:any,curData:any):void{\n        var index = this._gIdsArr.indexOf(curData.id);\n        if(index >= 0){\n\n            this._gIdsArr.splice(index,1)\n        }else{\n            this._gIdsArr.push(curData.id);\n        }\n        this.updataView();\n\n    }\n\n    public setData(cfgData:any,curData:any):void{\n        this._currData = curData;\n        this._cfgData = cfgData;\n        this._gIdsArr = [];\n        var com = this._generalNode.getComponent(GeneralItemLogic);\n        if(com){\n            com.updateItem(this._currData);\n        }\n\n        this.nameLab.string = this._cfgData.name;\n        \n        this.updateGeneral();\n        this.updataView();\n    }\n\n\n    protected updateGeneral():void{\n        let list:any[] = GeneralCommand.getInstance().proxy.getComposeGenerals(this._cfgData.cfgId,this._currData.id);\n        let listTemp = list.concat();\n\n\n        listTemp.forEach(item => {\n            item.type = GeneralItemType.GeneralSelect;\n        })\n\n        var comp = this.scrollView.node.getComponent(ListLogic);\n        comp.setData(listTemp);\n        \n    }\n\n\n    private updataView():void{\n        this.composeNode.active = ((this._gIdsArr.length > 0) && (this._currData.star_lv < this._cfgData.star));\n    }\n\n\n    protected onCompose(): void {\n        AudioManager.instance.playClick();\n        GeneralCommand.getInstance().composeGeneral(this._currData.id,this._gIdsArr);\n    }\n\n}\n"]}