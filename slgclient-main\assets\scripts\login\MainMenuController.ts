import { _decorator, Component, Node, Button, Prefab, instantiate } from 'cc';
import { AudioManager } from '../common/AudioManager';

const { ccclass, property } = _decorator;

/**
 * 主菜单控制器
 * 控制主菜单的两个按钮：登录游戏、离开游戏
 */
@ccclass('MainMenuController')
export class MainMenuController extends Component {
    
    @property(Button)
    loginButton: Button = null!;
    
    @property(Button)
    exitButton: Button = null!;
    
    @property(Prefab)
    loginDialogPrefab: Prefab = null!;
    
    @property(Prefab)
    exitDialogPrefab: Prefab = null!;
    
    @property(Node)
    dialogContainer: Node = null!;
    
    private currentDialog: Node = null;

    onLoad() {
        console.log('[MainMenuController] 主菜单控制器加载');
        this.setupEventListeners();
    }

    start() {
        console.log('[MainMenuController] 主菜单控制器启动');
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        if (this.loginButton) {
            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
            console.log('[MainMenuController] 登录按钮事件监听器已设置');
        }

        if (this.exitButton) {
            this.exitButton.node.on(Button.EventType.CLICK, this.onExitButtonClick, this);
            console.log('[MainMenuController] 离开按钮事件监听器已设置');
        }
    }

    /**
     * 登录游戏按钮点击事件
     */
    private onLoginButtonClick(): void {
        console.log('[MainMenuController] 登录游戏按钮被点击');
        
        // 播放点击音效
        AudioManager.instance.playClick();
        
        // 显示登录对话框
        this.showLoginDialog();
    }

    /**
     * 离开游戏按钮点击事件
     */
    private onExitButtonClick(): void {
        console.log('[MainMenuController] 离开游戏按钮被点击');
        
        // 播放点击音效
        AudioManager.instance.playClick();
        
        // 显示离开游戏对话框
        this.showExitDialog();
    }

    /**
     * 显示登录对话框
     */
    private showLoginDialog(): void {
        if (!this.loginDialogPrefab) {
            console.error('[MainMenuController] 登录对话框预制体未设置');
            return;
        }

        // 关闭当前对话框
        this.closeCurrentDialog();

        // 创建登录对话框
        this.currentDialog = instantiate(this.loginDialogPrefab);
        this.currentDialog.setParent(this.dialogContainer);
        this.currentDialog.setPosition(0, 0, 0);

        console.log('[MainMenuController] 登录对话框已显示');
    }

    /**
     * 显示离开游戏对话框
     */
    private showExitDialog(): void {
        if (!this.exitDialogPrefab) {
            console.error('[MainMenuController] 离开游戏对话框预制体未设置');
            return;
        }

        // 关闭当前对话框
        this.closeCurrentDialog();

        // 创建离开游戏对话框
        this.currentDialog = instantiate(this.exitDialogPrefab);
        this.currentDialog.setParent(this.dialogContainer);
        this.currentDialog.setPosition(0, 0, 0);

        console.log('[MainMenuController] 离开游戏对话框已显示');
    }

    /**
     * 关闭当前对话框
     */
    public closeCurrentDialog(): void {
        if (this.currentDialog) {
            this.currentDialog.destroy();
            this.currentDialog = null;
            console.log('[MainMenuController] 当前对话框已关闭');
        }
    }

    onDestroy() {
        // 清理事件监听器
        if (this.loginButton) {
            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);
        }
        if (this.exitButton) {
            this.exitButton.node.off(Button.EventType.CLICK, this.onExitButtonClick, this);
        }
        console.log('[MainMenuController] 主菜单控制器销毁');
    }
}
