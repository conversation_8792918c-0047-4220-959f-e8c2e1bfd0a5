System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, view, ResolutionPolicy, sys, UITransform, Widget, screen, GameConfig, _dec, _class, _class2, _temp, _crd, ccclass, property, ScreenAdapter;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../config/GameConfig", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      view = _cc.view;
      ResolutionPolicy = _cc.ResolutionPolicy;
      sys = _cc.sys;
      UITransform = _cc.UITransform;
      Widget = _cc.Widget;
      screen = _cc.screen;
    }, function (_unresolved_2) {
      GameConfig = _unresolved_2.GameConfig;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9b4ef9eqyVNl7EoAwYDY1SK", "ScreenAdapter", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 屏幕适配管理器
       * 统一处理不同设备的分辨率适配问题
       */

      _export("ScreenAdapter", ScreenAdapter = (_dec = ccclass('ScreenAdapter'), _dec(_class = (_temp = _class2 = class ScreenAdapter {
        constructor() {
          _defineProperty(this, "_isInitialized", false);
        }

        // 设计分辨率（从配置文件读取）
        static get DESIGN_WIDTH() {
          return (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.designWidth;
        }

        static get DESIGN_HEIGHT() {
          return (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.designHeight;
        } // 最小和最大宽高比


        static get instance() {
          if (!this._instance) {
            this._instance = new ScreenAdapter();
          }

          return this._instance;
        }
        /**
         * 初始化屏幕适配
         */


        init() {
          if (this._isInitialized) {
            return;
          }

          if ((_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.enableDebugInfo) {
            console.log('[ScreenAdapter] 开始初始化屏幕适配');
          } // 获取屏幕实际尺寸（使用新API）


          var windowSize = screen.windowSize;
          var screenRatio = windowSize.width / windowSize.height;
          console.log("[ScreenAdapter] \u5C4F\u5E55\u5C3A\u5BF8: " + windowSize.width + "x" + windowSize.height + ", \u6BD4\u4F8B: " + screenRatio.toFixed(3)); // 对于竖屏游戏，使用更保守的适配策略

          var designWidth = ScreenAdapter.DESIGN_WIDTH;
          var designHeight = ScreenAdapter.DESIGN_HEIGHT;
          var policy; // 计算设计分辨率比例

          var designRatio = designWidth / designHeight; // 1080/1920 = 0.5625

          console.log("[ScreenAdapter] \u8BBE\u8BA1\u6BD4\u4F8B: " + designRatio.toFixed(3) + ", \u5C4F\u5E55\u6BD4\u4F8B: " + screenRatio.toFixed(3)); // 对于竖屏游戏，优先使用SHOW_ALL策略确保UI不变形

          if (screenRatio > designRatio * 1.5) {
            // 屏幕太宽（如桌面环境），使用FIXED_WIDTH避免UI过度拉伸
            policy = ResolutionPolicy.FIXED_WIDTH;
            console.log('[ScreenAdapter] 屏幕过宽，使用FIXED_WIDTH策略');
          } else if (screenRatio < designRatio * 0.8) {
            // 屏幕太窄，使用FIXED_HEIGHT
            policy = ResolutionPolicy.FIXED_HEIGHT;
            console.log('[ScreenAdapter] 屏幕过窄，使用FIXED_HEIGHT策略');
          } else {
            // 正常比例，使用SHOW_ALL保证完整显示
            policy = ResolutionPolicy.SHOW_ALL;
            console.log('[ScreenAdapter] 使用SHOW_ALL策略保证完整显示');
          } // 设置设计分辨率和适配策略


          view.setDesignResolutionSize(designWidth, designHeight, policy); // 输出最终的可视区域信息

          var visibleSize = view.getVisibleSize();
          var visibleOrigin = view.getVisibleOrigin();
          console.log("[ScreenAdapter] \u8BBE\u8BA1\u5206\u8FA8\u7387: " + designWidth + "x" + designHeight);
          console.log("[ScreenAdapter] \u53EF\u89C6\u533A\u57DF: " + visibleSize.width + "x" + visibleSize.height);
          console.log("[ScreenAdapter] \u53EF\u89C6\u539F\u70B9: (" + visibleOrigin.x + ", " + visibleOrigin.y + ")"); // 检查可视原点偏移

          if (visibleOrigin.x !== 0 || visibleOrigin.y !== 0) {
            console.warn("[ScreenAdapter] \u8B66\u544A\uFF1A\u53EF\u89C6\u539F\u70B9\u4E0D\u4E3A(0,0)\uFF0C\u8FD9\u53EF\u80FD\u5BFC\u81F4\u70B9\u51FB\u504F\u79FB\uFF01");
          } // 检查缩放比例和可视原点


          var scaleX = visibleSize.width / designWidth;
          var scaleY = visibleSize.height / designHeight;
          console.log("[ScreenAdapter] \u5B9E\u9645\u7F29\u653E\u6BD4\u4F8B: X=" + scaleX.toFixed(3) + ", Y=" + scaleY.toFixed(3)); // 检查是否有严重的缩放异常（比如Y轴被压缩到0.3）

          if (scaleY < 0.5 || scaleX > 4.0) {
            console.error("[ScreenAdapter] \u4E25\u91CD\u9519\u8BEF\uFF1A\u7F29\u653E\u6BD4\u4F8B\u5F02\u5E38\uFF01Y=" + scaleY.toFixed(3) + ", X=" + scaleX.toFixed(3));
            console.error("[ScreenAdapter] \u8FD9\u4F1A\u5BFC\u81F4\u4E25\u91CD\u7684\u70B9\u51FB\u504F\u79FB\u95EE\u9898\uFF01"); // 强制使用SHOW_ALL策略重新设置

            console.log("[ScreenAdapter] \u5F3A\u5236\u91CD\u7F6E\u4E3ASHOW_ALL\u7B56\u7565");
            view.setDesignResolutionSize(designWidth, designHeight, ResolutionPolicy.SHOW_ALL); // 重新获取信息

            var newVisibleSize = view.getVisibleSize();
            var newVisibleOrigin = view.getVisibleOrigin();
            var newScaleX = newVisibleSize.width / designWidth;
            var newScaleY = newVisibleSize.height / designHeight;
            console.log("[ScreenAdapter] \u91CD\u7F6E\u540E\u4FE1\u606F:");
            console.log("[ScreenAdapter] \u53EF\u89C6\u533A\u57DF: " + newVisibleSize.width.toFixed(1) + "x" + newVisibleSize.height.toFixed(1));
            console.log("[ScreenAdapter] \u53EF\u89C6\u539F\u70B9: (" + newVisibleOrigin.x.toFixed(1) + ", " + newVisibleOrigin.y.toFixed(1) + ")");
            console.log("[ScreenAdapter] \u7F29\u653E\u6BD4\u4F8B: X=" + newScaleX.toFixed(3) + ", Y=" + newScaleY.toFixed(3));
          }

          this._isInitialized = true;
        }
        /**
         * 获取屏幕适配信息
         */


        getAdaptInfo() {
          var windowSize = screen.windowSize;
          var visibleSize = view.getVisibleSize();
          var visibleOrigin = view.getVisibleOrigin();
          var designSize = view.getDesignResolutionSize();
          return {
            frameSize: windowSize,
            visibleSize: visibleSize,
            visibleOrigin: visibleOrigin,
            designSize: designSize,
            scaleX: visibleSize.width / designSize.width,
            scaleY: visibleSize.height / designSize.height
          };
        }
        /**
         * 适配UI节点到安全区域
         * @param node 需要适配的节点
         * @param alignTop 是否对齐到顶部安全区域
         * @param alignBottom 是否对齐到底部安全区域
         */


        adaptToSafeArea(node, alignTop, alignBottom) {
          if (alignTop === void 0) {
            alignTop = false;
          }

          if (alignBottom === void 0) {
            alignBottom = false;
          }

          if (!node) return;
          var widget = node.getComponent(Widget);

          if (!widget) {
            console.warn('[ScreenAdapter] 节点没有Widget组件，无法适配安全区域');
            return;
          } // 获取安全区域信息


          var safeArea = sys.getSafeAreaRect();
          var frameSize = view.getFrameSize();
          var visibleSize = view.getVisibleSize();

          if (alignTop && safeArea.y > 0) {
            // 适配顶部安全区域（如刘海屏）
            var topOffset = safeArea.y / frameSize.height * visibleSize.height;
            widget.top = topOffset;
            widget.isAlignTop = true;
          }

          if (alignBottom && safeArea.y + safeArea.height < frameSize.height) {
            // 适配底部安全区域（如虚拟按键）
            var bottomOffset = (frameSize.height - safeArea.y - safeArea.height) / frameSize.height * visibleSize.height;
            widget.bottom = bottomOffset;
            widget.isAlignBottom = true;
          }

          widget.updateAlignment();
        }
        /**
         * 检查点击位置是否在节点范围内
         * @param node 目标节点
         * @param worldPos 世界坐标点击位置
         */


        isPointInNode(node, worldPos) {
          if (!node) return false;
          var uiTransform = node.getComponent(UITransform);
          if (!uiTransform) return false; // 将世界坐标转换为节点本地坐标

          var localPos = uiTransform.convertToNodeSpaceAR(worldPos); // 检查是否在节点范围内

          var size = uiTransform.contentSize;
          var anchorPoint = uiTransform.anchorPoint;
          var minX = -size.width * anchorPoint.x;
          var maxX = size.width * (1 - anchorPoint.x);
          var minY = -size.height * anchorPoint.y;
          var maxY = size.height * (1 - anchorPoint.y);
          return localPos.x >= minX && localPos.x <= maxX && localPos.y >= minY && localPos.y <= maxY;
        }
        /**
         * 获取当前设备类型
         */


        getDeviceType() {
          var windowSize = screen.windowSize;
          var ratio = windowSize.width / windowSize.height;

          if (sys.isMobile) {
            if (ratio < 0.6) {
              return 'mobile_narrow'; // 窄屏手机
            } else if (ratio < 0.75) {
              return 'mobile_normal'; // 普通手机
            } else {
              return 'mobile_wide'; // 宽屏手机
            }
          } else {
            return 'desktop';
          }
        }
        /**
         * 打印调试信息
         */


        printDebugInfo() {
          var info = this.getAdaptInfo();
          console.log('=== 屏幕适配调试信息 ===');
          console.log("\u8BBE\u5907\u7C7B\u578B: " + this.getDeviceType());
          console.log("\u7A97\u53E3\u5C3A\u5BF8: " + info.frameSize.width + "x" + info.frameSize.height);
          console.log("\u8BBE\u8BA1\u5206\u8FA8\u7387: " + info.designSize.width + "x" + info.designSize.height);
          console.log("\u53EF\u89C6\u533A\u57DF: " + info.visibleSize.width + "x" + info.visibleSize.height);
          console.log("\u53EF\u89C6\u539F\u70B9: (" + info.visibleOrigin.x + ", " + info.visibleOrigin.y + ")");
          console.log("\u7F29\u653E\u6BD4\u4F8B: X=" + info.scaleX.toFixed(3) + ", Y=" + info.scaleY.toFixed(3));

          if (sys.isMobile) {
            var safeArea = sys.getSafeAreaRect();
            console.log("\u5B89\u5168\u533A\u57DF: x=" + safeArea.x + ", y=" + safeArea.y + ", w=" + safeArea.width + ", h=" + safeArea.height);
          }

          console.log('========================');
        }

      }, _defineProperty(_class2, "MIN_RATIO", 9 / 21), _defineProperty(_class2, "MAX_RATIO", 9 / 16), _defineProperty(_class2, "_instance", null), _temp)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ScreenAdapter.js.map