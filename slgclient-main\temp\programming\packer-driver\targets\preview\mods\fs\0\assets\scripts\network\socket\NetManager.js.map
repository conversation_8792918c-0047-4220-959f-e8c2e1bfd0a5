{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"], "names": ["NetManager", "NetNode", "getInstance", "_instance", "constructor", "_netNode", "init", "connect", "options", "send", "send_data", "otherData", "force", "seq", "undefined", "close", "code", "reason", "closeSocket", "changeConnect", "changeConect", "tryConnet"], "mappings": ";;;uCAGaA,U;;;;;;;;;;;;;;;;;;;;AAFJC,MAAAA,O,iBAAAA,O;;;;;;;4BAEID,U,GAAN,MAAMA,UAAN,CAAiB;AAGK,eAAXE,WAAW,GAAe;AACpC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AAC5B,iBAAKA,SAAL,GAAiB,IAAIH,UAAJ,EAAjB;AACC;;AACD,iBAAO,KAAKG,SAAZ;AACH;;AACDC,QAAAA,WAAW,GAAE;AAAA,4CAPiB,IAOjB;;AACT,eAAKC,QAAL,GAAgB;AAAA;AAAA,mCAAhB;;AACA,eAAKA,QAAL,CAAcC,IAAd;AACH;;AAEMC,QAAAA,OAAO,CAACC,OAAD,EAAkC;AAC5C,eAAKH,QAAL,CAAcE,OAAd,CAAsBC,OAAtB;AACH;;AAEMC,QAAAA,IAAI,CAACC,SAAD,EAAiBC,SAAjB,EAAoCC,KAApC,EAAyE;AAAA,cAAxDD,SAAwD;AAAxDA,YAAAA,SAAwD,GAAxC,EAAwC;AAAA;;AAAA,cAArCC,KAAqC;AAArCA,YAAAA,KAAqC,GAApB,KAAoB;AAAA;;AAChF,cAAGF,SAAS,CAACG,GAAV,IAAiBC,SAApB,EAA8B;AAC1BJ,YAAAA,SAAS,CAACG,GAAV,GAAgB,CAAhB;AACH;;AAED,iBAAO,KAAKR,QAAL,CAAcI,IAAd,CAAmBC,SAAnB,EAA6BC,SAA7B,EAAuCC,KAAvC,CAAP;AACH;;AAEMG,QAAAA,KAAK,CAACC,IAAD,EAAgBC,MAAhB,EAAsC;AAC9C,eAAKZ,QAAL,CAAca,WAAd,CAA0BF,IAA1B,EAAgCC,MAAhC;AACH;;AAEME,QAAAA,aAAa,CAACX,OAAD,EAAkC;AAClD,eAAKH,QAAL,CAAce,YAAd,CAA2BZ,OAA3B;AACH;;AACMa,QAAAA,SAAS,GAAO;AACnB,eAAKhB,QAAL,CAAcgB,SAAd;AACH;;AAnCmB,O;;sBAAXrB,U,eAC8B,I", "sourcesContent": ["import { _decorator } from 'cc';\nimport { NetNode, NetConnectOptions } from \"./NetNode\";\n\nexport class NetManager {\n    private static _instance: NetManager = null;\n    protected _netNode: NetNode = null;\n    public static getInstance(): NetManager {\n        if (this._instance == null) {\n        this._instance = new NetManager();\n        }\n        return this._instance;\n    }\n    constructor(){\n        this._netNode = new NetNode();\n        this._netNode.init();\n    }\n    \n    public connect(options: NetConnectOptions) :void{\n        this._netNode.connect(options);\n    }\n\n    public send(send_data: any, otherData:any = {},force: boolean = false) :Promise<any>{\n        if(send_data.seq == undefined){\n            send_data.seq = 0;\n        }\n        \n        return this._netNode.send(send_data,otherData,force);\n    }\n\n    public close(code?: number, reason?: string):void {\n        this._netNode.closeSocket(code, reason);\n    }\n\n    public changeConnect(options: NetConnectOptions):void {\n        this._netNode.changeConect(options);\n    }\n    public tryConnet():void{\n        this._netNode.tryConnet();\n    }\n}\n"]}