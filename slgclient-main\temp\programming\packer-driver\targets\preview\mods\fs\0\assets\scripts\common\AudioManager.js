System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, AudioClip, assert, clamp01, warn, resources, LocalCache, AudioManager, _crd;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfLocalCache(extras) {
    _reporterNs.report("LocalCache", "../utils/LocalCache", _context.meta, extras);
  }

  _export("AudioManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      AudioClip = _cc.AudioClip;
      assert = _cc.assert;
      clamp01 = _cc.clamp01;
      warn = _cc.warn;
      resources = _cc.resources;
    }, function (_unresolved_2) {
      LocalCache = _unresolved_2.LocalCache;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d489elJnGRJt5zCOsweSAlP", "AudioManager", undefined);

      _export("AudioManager", AudioManager = class AudioManager {
        constructor() {
          _defineProperty(this, "soundVolume", 1);
        }

        static get instance() {
          if (this._instance) {
            return this._instance;
          }

          this._instance = new AudioManager();
          return this._instance;
        }

        // init AudioManager in GameRoot.
        init(audioSource) {
          this.soundVolume = this.getConfiguration(false) ? 1 : 0;
          AudioManager._audioSource = audioSource;

          if (this.getConfiguration(true)) {
            this.openMusic();
          } else {
            this.closeMusic();
          }
        }

        getConfiguration(isMusic) {
          var state;

          if (isMusic) {
            state = (_crd && LocalCache === void 0 ? (_reportPossibleCrUseOfLocalCache({
              error: Error()
            }), LocalCache) : LocalCache).getMusic();
          } else {
            state = (_crd && LocalCache === void 0 ? (_reportPossibleCrUseOfLocalCache({
              error: Error()
            }), LocalCache) : LocalCache).getSound();
          }

          return state === undefined || state ? true : false;
        }
        /**
         * 播放音乐
         * @param {String} name 音乐名称可通过constants.AUDIO_MUSIC 获取
         * @param {Boolean} loop 是否循环播放
         */


        playMusic(loop) {
          var audioSource = AudioManager._audioSource;
          assert(audioSource, 'AudioManager not inited!');
          audioSource.loop = loop;

          if (!audioSource.playing) {
            audioSource.play();
          }
        }
        /**
         * 播放音效
         * @param {String} name 音效名称可通过constants.AUDIO_SOUND 获取
         */


        playSound(name) {
          var audioSource = AudioManager._audioSource;
          assert(audioSource, 'AudioManager not inited!');
          var path = '/audio/sound/';
          resources.load(path + name, AudioClip, (err, clip) => {
            if (err) {
              warn('load audioClip failed: ', err);
              return;
            }

            audioSource.playOneShot(clip, this.soundVolume);
          });
        }

        playClick() {
          this.playSound("click");
        }

        setMusicVolume(flag) {
          console.log("setMusicVolume:", flag);
          var audioSource = AudioManager._audioSource;
          assert(audioSource, 'AudioManager not inited!');
          flag = clamp01(flag);
          audioSource.volume = flag;
        }

        setSoundVolume(flag) {
          this.soundVolume = flag;
        }

        openMusic() {
          this.setMusicVolume(0.2);
          this.playMusic(true);
          (_crd && LocalCache === void 0 ? (_reportPossibleCrUseOfLocalCache({
            error: Error()
          }), LocalCache) : LocalCache).setMusic(true);
        }

        closeMusic() {
          AudioManager._audioSource.stop();

          (_crd && LocalCache === void 0 ? (_reportPossibleCrUseOfLocalCache({
            error: Error()
          }), LocalCache) : LocalCache).setMusic(false);
        }

        openSound() {
          this.setSoundVolume(1);
          (_crd && LocalCache === void 0 ? (_reportPossibleCrUseOfLocalCache({
            error: Error()
          }), LocalCache) : LocalCache).setSound(true);
        }

        closeSound() {
          this.setSoundVolume(0);
          (_crd && LocalCache === void 0 ? (_reportPossibleCrUseOfLocalCache({
            error: Error()
          }), LocalCache) : LocalCache).setSound(false);
        }

      });

      _defineProperty(AudioManager, "_instance", void 0);

      _defineProperty(AudioManager, "_audioSource", void 0);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=AudioManager.js.map