{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts"], "names": ["HttpConfig", "register", "name", "url"], "mappings": ";;;;;;;;;;;;;;4BAIMA,U,GAAa;AACfC,QAAAA,QAAQ,EAAE;AACNC,UAAAA,IAAI,EAAE,UADA;AAENC,UAAAA,GAAG,EAAE;AAFC;AADK,O", "sourcesContent": ["// /**http接口配置*/\n// //账号注册\n\nimport { _decorator } from 'cc';\nconst HttpConfig = {\n    register: {\n        name: \"register\",\n        url: \"/account/register\"\n    },\n}\nexport { HttpConfig };\n"]}