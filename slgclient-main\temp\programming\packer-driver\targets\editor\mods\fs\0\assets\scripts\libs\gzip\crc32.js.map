{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts"], "names": ["makeTable", "c", "n", "k", "poly", "table", "strToArr", "str", "Array", "prototype", "map", "call", "charCodeAt", "crcDirect", "arr", "crc", "i", "j", "l", "temp", "length", "crcTable", "append", "crc32", "val", "direct", "ret", "toString"], "mappings": ";;;;;AACuB;AAEvB,WAASA,SAAT,GAAqB;AACpB,QAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV;;AAEA,SAAKD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,GAAhB,EAAqBA,CAAC,IAAI,CAA1B,EAA6B;AAC5BD,MAAAA,CAAC,GAAGC,CAAJ;;AACA,WAAKC,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,CAAhB,EAAmBA,CAAC,IAAI,CAAxB,EAA2B;AAC1B,YAAIF,CAAC,GAAG,CAAR,EAAW;AACVA,UAAAA,CAAC,GAAGG,IAAI,GAAIH,CAAC,KAAK,CAAlB;AACA,SAFD,MAEO;AACNA,UAAAA,CAAC,GAAGA,CAAC,KAAK,CAAV;AACA;AACD;;AACDI,MAAAA,KAAK,CAACH,CAAD,CAAL,GAAWD,CAAC,KAAK,CAAjB;AACA;AACD;;AAED,WAASK,QAAT,CAAkBC,GAAlB,EAAuB;AACtB;AACA,WAAOC,KAAK,CAACC,SAAN,CAAgBC,GAAhB,CAAoBC,IAApB,CAAyBJ,GAAzB,EAA8B,UAAUN,CAAV,EAAa;AACjD,aAAOA,CAAC,CAACW,UAAF,CAAa,CAAb,CAAP;AACA,KAFM,CAAP;AAGA;;AAED,WAASC,SAAT,CAAmBC,GAAnB,EAAwB;AACvB,QAAIC,GAAG,GAAG,CAAC,CAAX;AAAA,QAAc;AACbC,IAAAA,CADD;AAAA,QACIC,CADJ;AAAA,QACOC,CADP;AAAA,QACUC,IADV;;AAGA,SAAKH,CAAC,GAAG,CAAJ,EAAOE,CAAC,GAAGJ,GAAG,CAACM,MAApB,EAA4BJ,CAAC,GAAGE,CAAhC,EAAmCF,CAAC,IAAI,CAAxC,EAA2C;AAC1CG,MAAAA,IAAI,GAAG,CAACJ,GAAG,GAAGD,GAAG,CAACE,CAAD,CAAV,IAAiB,IAAxB,CAD0C,CAG1C;;AACA,WAAKC,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,CAAhB,EAAmBA,CAAC,IAAI,CAAxB,EAA2B;AAC1B,YAAI,CAACE,IAAI,GAAG,CAAR,MAAe,CAAnB,EAAsB;AACrBA,UAAAA,IAAI,GAAIA,IAAI,KAAK,CAAV,GAAef,IAAtB;AACA,SAFD,MAEO;AACNe,UAAAA,IAAI,GAAIA,IAAI,KAAK,CAAjB;AACA;AACD;;AACDJ,MAAAA,GAAG,GAAIA,GAAG,KAAK,CAAT,GAAcI,IAApB;AACA,KAhBsB,CAkBvB;;;AACA,WAAOJ,GAAG,GAAG,CAAC,CAAd;AACA;;AAGD,WAASM,QAAT,CAAkBP,GAAlB,EAAuBQ,MAAvB,EAA+B;AAC9B,QAAIP,GAAJ,EAASC,CAAT,EAAYE,CAAZ,CAD8B,CAG9B;AACA;;AACA,QAAI,OAAOG,QAAQ,CAACN,GAAhB,KAAwB,WAAxB,IAAuC,CAACO,MAAxC,IAAkD,CAACR,GAAvD,EAA4D;AAC3DO,MAAAA,QAAQ,CAACN,GAAT,GAAe,IAAI,CAAC,CAApB;;AAEA,UAAI,CAACD,GAAL,EAAU;AACT;AACA;AACD,KAX6B,CAa9B;;;AACAC,IAAAA,GAAG,GAAGM,QAAQ,CAACN,GAAf;;AAEA,SAAKC,CAAC,GAAG,CAAJ,EAAOE,CAAC,GAAGJ,GAAG,CAACM,MAApB,EAA4BJ,CAAC,GAAGE,CAAhC,EAAmCF,CAAC,IAAI,CAAxC,EAA2C;AAC1CD,MAAAA,GAAG,GAAIA,GAAG,KAAK,CAAT,GAAcV,KAAK,CAAC,CAACU,GAAG,GAAGD,GAAG,CAACE,CAAD,CAAV,IAAiB,IAAlB,CAAzB;AACA;;AAEDK,IAAAA,QAAQ,CAACN,GAAT,GAAeA,GAAf;AAEA,WAAOA,GAAG,GAAG,CAAC,CAAd;AACA;;AAIM,WAASQ,KAAT,CAAeC,GAAf,EAAoBC,MAApB,EAA4B;AAClC,QAAID,GAAG,GAAI,OAAOA,GAAP,KAAe,QAAhB,GAA4BlB,QAAQ,CAACkB,GAAD,CAApC,GAA4CA,GAAtD;AAAA,QACCE,GAAG,GAAGD,MAAM,GAAGZ,SAAS,CAACW,GAAD,CAAZ,GAAoBH,QAAQ,CAACG,GAAD,CADzC,CADkC,CAIlC;;AACA,WAAO,CAACE,GAAG,KAAK,CAAT,EAAYC,QAAZ,CAAqB,EAArB,CAAP;AACA;;mBANeJ,K;;;;;;;;;;;AA5EZlB,MAAAA,K,GAAQ,E;AACRD,MAAAA,I,GAAO,U;AAyEXJ,MAAAA,SAAS;AAQR", "sourcesContent": ["var table = [];\nvar poly = 0xEDB88320; // reverse polynomial\n\nfunction makeTable() {\n\tvar c, n, k;\n\n\tfor (n = 0; n < 256; n += 1) {\n\t\tc = n;\n\t\tfor (k = 0; k < 8; k += 1) {\n\t\t\tif (c & 1) {\n\t\t\t\tc = poly ^ (c >>> 1);\n\t\t\t} else {\n\t\t\t\tc = c >>> 1;\n\t\t\t}\n\t\t}\n\t\ttable[n] = c >>> 0;\n\t}\n}\n\nfunction strToArr(str) {\n\t// sweet hack to turn string into a 'byte' array\n\treturn Array.prototype.map.call(str, function (c) {\n\t\treturn c.charCodeAt(0);\n\t});\n}\n\nfunction crcDirect(arr) {\n\tvar crc = -1, // initial contents of LFBSR\n\t\ti, j, l, temp;\n\n\tfor (i = 0, l = arr.length; i < l; i += 1) {\n\t\ttemp = (crc ^ arr[i]) & 0xff;\n\n\t\t// read 8 bits one at a time\n\t\tfor (j = 0; j < 8; j += 1) {\n\t\t\tif ((temp & 1) === 1) {\n\t\t\t\ttemp = (temp >>> 1) ^ poly;\n\t\t\t} else {\n\t\t\t\ttemp = (temp >>> 1);\n\t\t\t}\n\t\t}\n\t\tcrc = (crc >>> 8) ^ temp;\n\t}\n\n\t// flip bits\n\treturn crc ^ -1;\n}\n\n\nfunction crcTable(arr, append) {\n\tvar crc, i, l;\n\n\t// if we're in append mode, don't reset crc\n\t// if arr is null or undefined, reset table and return\n\tif (typeof crcTable.crc === 'undefined' || !append || !arr) {\n\t\tcrcTable.crc = 0 ^ -1;\n\n\t\tif (!arr) {\n\t\t\treturn;\n\t\t}\n\t}\n\n\t// store in temp variable for minor speed gain\n\tcrc = crcTable.crc;\n\n\tfor (i = 0, l = arr.length; i < l; i += 1) {\n\t\tcrc = (crc >>> 8) ^ table[(crc ^ arr[i]) & 0xff];\n\t}\n\n\tcrcTable.crc = crc;\n\n\treturn crc ^ -1;\n}\n\nmakeTable();\n\nexport function crc32(val, direct) {\n\tvar val = (typeof val === 'string') ? strToArr(val) : val,\n\t\tret = direct ? crcDirect(val) : crcTable(val);\n\n\t// convert to 2's complement hex\n\treturn (ret >>> 0).toString(16);\n};\n"]}