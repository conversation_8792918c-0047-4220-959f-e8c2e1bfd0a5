{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts"], "names": ["_decorator", "Component", "Sprite", "SpriteAtlas", "MapResType", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onLoad", "onDestroy", "setResourceData", "data", "_data", "type", "WOOD", "level", "spr", "spriteFrame", "resourceAtlas1", "getSpriteFrame", "resourceAtlas2", "IRON", "STONE", "GRAIN", "SYS_FORTRESS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAGpBC,MAAAA,U,iBAAAA,U;;;;;;;OAFd;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;yBAKTO,Q,WADpBF,OAAO,CAAC,UAAD,C,UAEHC,QAAQ,CAACJ,MAAD,C,UAERI,QAAQ,CAACH,WAAD,C,UAGRG,QAAQ,CAACH,WAAD,C,oCAPb,MACqBI,QADrB,SACsCN,SADtC,CACgD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCASd,IATc;AAAA;;AAWlCO,QAAAA,MAAM,GAAS,CAExB;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AAEMC,QAAAA,eAAe,CAACC,IAAD,EAAyB;AAC3C,eAAKC,KAAL,GAAaD,IAAb;;AAGA,cAAIA,IAAI,CAACE,IAAL,IAAa;AAAA;AAAA,wCAAWC,IAA5B,EAAkC;AAC9B;AACA,gBAAGH,IAAI,CAACI,KAAL,IAAc,CAAjB,EAAmB;AACf,mBAAKC,GAAL,CAASC,WAAT,GAAuB,KAAKC,cAAL,CAAoBC,cAApB,CAAmC,iBAAnC,CAAvB;AACH,aAFD,MAEM,IAAGR,IAAI,CAACI,KAAL,IAAc,CAAjB,EAAmB;AACrB,mBAAKC,GAAL,CAASC,WAAT,GAAuB,KAAKC,cAAL,CAAoBC,cAApB,CAAmC,iBAAnC,CAAvB;AACH,aAFK,MAED;AACD,mBAAKH,GAAL,CAASC,WAAT,GAAuB,KAAKG,cAAL,CAAoBD,cAApB,CAAmC,aAAaR,IAAI,CAACI,KAAL,GAAW,CAAxB,CAAnC,CAAvB;AACH;AAEJ,WAVD,MAUO,IAAIJ,IAAI,CAACE,IAAL,IAAa;AAAA;AAAA,wCAAWQ,IAA5B,EAAkC;AACrC;AACA,gBAAGV,IAAI,CAACI,KAAL,IAAc,CAAjB,EAAmB;AACf,mBAAKC,GAAL,CAASC,WAAT,GAAuB,KAAKC,cAAL,CAAoBC,cAApB,CAAmC,iBAAnC,CAAvB;AACH,aAFD,MAEM,IAAGR,IAAI,CAACI,KAAL,IAAc,CAAjB,EAAmB;AACrB,mBAAKC,GAAL,CAASC,WAAT,GAAuB,KAAKC,cAAL,CAAoBC,cAApB,CAAmC,iBAAnC,CAAvB;AACH,aAFK,MAED;AACD,mBAAKH,GAAL,CAASC,WAAT,GAAuB,KAAKG,cAAL,CAAoBD,cAApB,CAAmC,aAAaR,IAAI,CAACI,KAAL,GAAW,CAAxB,CAAnC,CAAvB;AACH;AAEJ,WAVM,MAUA,IAAIJ,IAAI,CAACE,IAAL,IAAa;AAAA;AAAA,wCAAWS,KAA5B,EAAmC;AACtC;AACA,gBAAGX,IAAI,CAACI,KAAL,IAAc,CAAjB,EAAmB;AACf,mBAAKC,GAAL,CAASC,WAAT,GAAuB,KAAKC,cAAL,CAAoBC,cAApB,CAAmC,iBAAnC,CAAvB;AACH,aAFD,MAEM,IAAGR,IAAI,CAACI,KAAL,IAAc,CAAjB,EAAmB;AACrB,mBAAKC,GAAL,CAASC,WAAT,GAAuB,KAAKC,cAAL,CAAoBC,cAApB,CAAmC,iBAAnC,CAAvB;AACH,aAFK,MAED;AACD,mBAAKH,GAAL,CAASC,WAAT,GAAuB,KAAKG,cAAL,CAAoBD,cAApB,CAAmC,aAAaR,IAAI,CAACI,KAAL,GAAW,CAAxB,CAAnC,CAAvB;AACH;AACJ,WATM,MASA,IAAIJ,IAAI,CAACE,IAAL,IAAa;AAAA;AAAA,wCAAWU,KAA5B,EAAmC;AACtC;AACA,gBAAGZ,IAAI,CAACI,KAAL,IAAc,CAAjB,EAAmB;AACf,mBAAKC,GAAL,CAASC,WAAT,GAAuB,KAAKC,cAAL,CAAoBC,cAApB,CAAmC,iBAAnC,CAAvB;AACH,aAFD,MAEM,IAAGR,IAAI,CAACI,KAAL,IAAc,CAAjB,EAAmB;AACrB,mBAAKC,GAAL,CAASC,WAAT,GAAuB,KAAKC,cAAL,CAAoBC,cAApB,CAAmC,iBAAnC,CAAvB;AACH,aAFK,MAED;AACD,mBAAKH,GAAL,CAASC,WAAT,GAAuB,KAAKG,cAAL,CAAoBD,cAApB,CAAmC,aAAaR,IAAI,CAACI,KAAL,GAAW,CAAxB,CAAnC,CAAvB;AACH;AACJ,WATM,MASA,IAAIJ,IAAI,CAACE,IAAL,IAAa;AAAA;AAAA,wCAAWW,YAA5B,EAA0C;AAC7C;AACA,iBAAKR,GAAL,CAASC,WAAT,GAAuB,KAAKG,cAAL,CAAoBD,cAApB,CAAmC,cAAnC,CAAvB;AACH,WAHM,MAGD;AACF,iBAAKH,GAAL,CAASC,WAAT,GAAuB,IAAvB;AACH;AAEJ;;AApE2C,O;;;;;iBAE9B,I;;;;;;;iBAEgB,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Sprite, SpriteAtlas } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport {MapResData, MapResType } from \"../MapProxy\";\n\n@ccclass('ResLogic')\nexport default class ResLogic extends Component {\n    @property(Sprite)\n    spr: Sprite = null;\n    @property(SpriteAtlas)\n    resourceAtlas1: SpriteAtlas = null;\n\n    @property(SpriteAtlas)\n    resourceAtlas2: SpriteAtlas = null;\n\n    protected _data: MapResData = null;\n\n    protected onLoad(): void {\n\n    }\n\n    protected onDestroy(): void {\n\n    }\n\n    public setResourceData(data: MapResData): void {\n        this._data = data;\n        \n        \n        if (data.type == MapResType.WOOD) {\n            //木头\n            if(data.level == 1){\n                this.spr.spriteFrame = this.resourceAtlas1.getSpriteFrame(\"land_ground_1_1\");\n            }else if(data.level == 2){\n                this.spr.spriteFrame = this.resourceAtlas1.getSpriteFrame(\"land_ground_2_1\");\n            }else{\n                this.spr.spriteFrame = this.resourceAtlas2.getSpriteFrame(\"land_2_\" + (data.level-2));\n            }\n           \n        } else if (data.type == MapResType.IRON) {\n            //铁\n            if(data.level == 1){\n                this.spr.spriteFrame = this.resourceAtlas1.getSpriteFrame(\"land_ground_1_1\");\n            }else if(data.level == 2){\n                this.spr.spriteFrame = this.resourceAtlas1.getSpriteFrame(\"land_ground_2_1\");\n            }else{\n                this.spr.spriteFrame = this.resourceAtlas2.getSpriteFrame(\"land_4_\" + (data.level-2));\n            }\n            \n        } else if (data.type == MapResType.STONE) {\n            //石头\n            if(data.level == 1){\n                this.spr.spriteFrame = this.resourceAtlas1.getSpriteFrame(\"land_ground_1_1\");\n            }else if(data.level == 2){\n                this.spr.spriteFrame = this.resourceAtlas1.getSpriteFrame(\"land_ground_2_1\");\n            }else{\n                this.spr.spriteFrame = this.resourceAtlas2.getSpriteFrame(\"land_2_\" + (data.level-2));\n            }\n        } else if (data.type == MapResType.GRAIN) {\n            //田\n            if(data.level == 1){\n                this.spr.spriteFrame = this.resourceAtlas1.getSpriteFrame(\"land_ground_1_1\");\n            }else if(data.level == 2){\n                this.spr.spriteFrame = this.resourceAtlas1.getSpriteFrame(\"land_ground_2_1\");\n            }else{\n                this.spr.spriteFrame = this.resourceAtlas2.getSpriteFrame(\"land_1_\" + (data.level-2));\n            }\n        } else if (data.type == MapResType.SYS_FORTRESS) {\n            //系统要塞\n            this.spr.spriteFrame = this.resourceAtlas2.getSpriteFrame(\"sys_fortress\");\n        }else {\n            this.spr.spriteFrame = null;\n        }\n       \n    }\n}\n"]}