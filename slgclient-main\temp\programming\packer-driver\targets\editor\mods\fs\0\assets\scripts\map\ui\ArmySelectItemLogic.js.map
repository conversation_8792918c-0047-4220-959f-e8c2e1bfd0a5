{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts"], "names": ["_decorator", "Component", "Node", "Label", "Sprite", "ProgressBar", "ArmyCmd", "GeneralCommand", "ArmyCommand", "GeneralHeadLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "ArmySelectItemLogic", "onLoad", "on", "updateArmy", "onUpdateArmy", "tipNode", "active", "onDestroy", "targetOff", "_data", "armyData", "id", "setArmyData", "_cmd", "_toX", "_toY", "onClickItem", "instance", "playClick", "getInstance", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emit", "closeArmyAelectUi", "console", "log", "updateItem", "generals", "commonCfg", "proxy", "getCommonCfg", "getArmyGenerals", "firstGeneralCfg", "getGeneralCfg", "cfgId", "power", "getArmyPhysicalPowerByGenerals", "curSoldierCnt", "getArmyCurSoldierCnt", "totalSoldierCnt", "getArmyTotalSoldierCntByGenerals", "cmd", "Conscript", "labelTip", "string", "recovery_physical_power", "soldiers", "state", "Reclaim", "headIcon", "getComponent", "setHeadId", "labelLv", "level", "labelName", "name", "labelState", "getArmyStateDes", "labelSoldierCnt", "progressSoldier", "progress", "sencondGeneralCfg", "labelVice1", "thirdGeneralCfg", "labelVice2", "data", "x", "y"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAG5CC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,c;;AACAC,MAAAA,W;;AAEAC,MAAAA,gB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OATH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;yBAYTe,mB,WADpBF,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACV,MAAD,C,UAERU,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACT,WAAD,C,oCAtBb,MACqBU,mBADrB,SACiDd,SADjD,CAC2D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAwB3B,IAxB2B;;AAAA,wCAyB9B,CAzB8B;;AAAA,wCA0B9B,CA1B8B;;AAAA,wCA2B9B,CA3B8B;AAAA;;AA6B7Ce,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,UAAvB,EAAmC,KAAKC,YAAxC,EAAsD,IAAtD;AACA,eAAKC,OAAL,CAAaC,MAAb,GAAsB,KAAtB;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,eAAKC,KAAL,GAAa,IAAb;AACH;;AAESL,QAAAA,YAAY,CAACM,QAAD,EAA2B;AAC7C,cAAIA,QAAQ,CAACC,EAAT,IAAe,KAAKF,KAAL,CAAWE,EAA9B,EAAkC;AAC9B,iBAAKC,WAAL,CAAiBF,QAAjB,EAA2B,KAAKG,IAAhC,EAAsC,KAAKC,IAA3C,EAAiD,KAAKC,IAAtD;AACH;AACJ;;AAESC,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKb,OAAL,CAAaC,MAAb,IAAuB,KAA3B,EAAkC;AAC9B;AAAA;AAAA,4CAAYa,WAAZ,GAA0BC,iBAA1B,CAA4C,KAAKX,KAAL,CAAWE,EAAvD,EAA2D,KAAKE,IAAhE,EAAsE,KAAKC,IAA3E,EAAiF,KAAKC,IAAtF;AACA;AAAA;AAAA,sCAASM,IAAT,CAAc;AAAA;AAAA,0CAAWC,iBAAzB;AACH,WAHD,MAGO;AACHC,YAAAA,OAAO,CAACC,GAAR,CAAY,KAAZ;AACH;AACJ;;AAESC,QAAAA,UAAU,GAAS;AACzB,cAAI,KAAKhB,KAAL,IAAc,KAAKA,KAAL,CAAWiB,QAAX,CAAoB,CAApB,KAA0B,CAA5C,EAA+C;AAC3CH,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,KAAKf,KAA/B;AACA,gBAAIkB,SAA8B,GAAG;AAAA;AAAA,kDAAeR,WAAf,GAA6BS,KAA7B,CAAmCC,YAAnC,EAArC;AACA,gBAAIH,QAAuB,GAAG;AAAA;AAAA,4CAAYP,WAAZ,GAA0BW,eAA1B,CAA0C,KAAKrB,KAA/C,CAA9B;AACA,gBAAIsB,eAA8B,GAAG;AAAA;AAAA,kDAAeZ,WAAf,GAA6BS,KAA7B,CAAmCI,aAAnC,CAAiDN,QAAQ,CAAC,CAAD,CAAR,CAAYO,KAA7D,CAArC;AACA,gBAAIC,KAAa,GAAG;AAAA;AAAA,4CAAYf,WAAZ,GAA0BgB,8BAA1B,CAAyDT,QAAzD,CAApB;AACA,gBAAIU,aAAqB,GAAG;AAAA;AAAA,4CAAYjB,WAAZ,GAA0BkB,oBAA1B,CAA+C,KAAK5B,KAApD,CAA5B;AACA,gBAAI6B,eAAuB,GAAG;AAAA;AAAA,4CAAYnB,WAAZ,GAA0BoB,gCAA1B,CAA2Db,QAA3D,CAA9B;;AAEA,gBAAI,KAAKjB,KAAL,CAAW+B,GAAX,IAAkB;AAAA;AAAA,oCAAQC,SAA9B,EAAyC;AACrC;AACA,mBAAKpC,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACA,mBAAKoC,QAAL,CAAcC,MAAd,GAAuB,QAAvB;AACH,aAJD,MAIM,IAAIT,KAAK,GAAGP,SAAS,CAACiB,uBAAtB,EAA+C;AACjD;AACA,mBAAKvC,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACA,mBAAKoC,QAAL,CAAcC,MAAd,GAAuB,MAAvB;AACH,aAJK,MAIC,IAAI,KAAKlC,KAAL,CAAWoC,QAAX,CAAoB,CAApB,KAA0B,CAA9B,EAAiC;AACpC;AACA,mBAAKxC,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACA,mBAAKoC,QAAL,CAAcC,MAAd,GAAuB,QAAvB;AACH,aAJM,MAIA,IAAI,KAAKlC,KAAL,CAAWqC,KAAX,GAAmB,CAAvB,EAA0B;AAC7B;AACA,mBAAKzC,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACA,mBAAKoC,QAAL,CAAcC,MAAd,GAAuB,QAAvB;AACH,aAJM,MAIA,IAAI,KAAKlC,KAAL,CAAW+B,GAAX,IAAkB;AAAA;AAAA,oCAAQO,OAA9B,EAAuC;AAC1C;AACA,mBAAK1C,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACA,mBAAKoC,QAAL,CAAcC,MAAd,GAAuB,QAAvB;AACH,aAJM,MAIA;AACH,mBAAKtC,OAAL,CAAaC,MAAb,GAAsB,KAAtB;AACH;;AAED,iBAAK0C,QAAL,CAAcC,YAAd;AAAA;AAAA,sDAA6CC,SAA7C,CAAuDxB,QAAQ,CAAC,CAAD,CAAR,CAAYO,KAAnE;AACA,iBAAKkB,OAAL,CAAaR,MAAb,GAAsBjB,QAAQ,CAAC,CAAD,CAAR,CAAY0B,KAAZ,GAAoB,EAA1C;AACA,iBAAKC,SAAL,CAAeV,MAAf,GAAwBZ,eAAe,CAACuB,IAAxC;AACA,iBAAKC,UAAL,CAAgBZ,MAAhB,GAAyB;AAAA;AAAA,4CAAYxB,WAAZ,GAA0BqC,eAA1B,CAA0C,KAAK/C,KAA/C,CAAzB;AACA,iBAAKgD,eAAL,CAAqBd,MAArB,GAA8BP,aAAa,GAAG,GAAhB,GAAsBE,eAApD;AACA,iBAAKoB,eAAL,CAAqBC,QAArB,GAAgCvB,aAAa,GAAGE,eAAhD;;AAEA,gBAAIZ,QAAQ,CAAC,CAAD,CAAZ,EAAiB;AACb,kBAAIkC,iBAAgC,GAAG;AAAA;AAAA,oDAAezC,WAAf,GAA6BS,KAA7B,CAAmCI,aAAnC,CAAiDN,QAAQ,CAAC,CAAD,CAAR,CAAYO,KAA7D,CAAvC;AACA,mBAAK4B,UAAL,CAAgBlB,MAAhB,GAAyBiB,iBAAiB,CAACN,IAA3C;AACH,aAHD,MAGO;AACH,mBAAKO,UAAL,CAAgBlB,MAAhB,GAAyB,EAAzB;AACH;;AAED,gBAAIjB,QAAQ,CAAC,CAAD,CAAZ,EAAiB;AACb,kBAAIoC,eAA8B,GAAG;AAAA;AAAA,oDAAe3C,WAAf,GAA6BS,KAA7B,CAAmCI,aAAnC,CAAiDN,QAAQ,CAAC,CAAD,CAAR,CAAYO,KAA7D,CAArC;AACA,mBAAK8B,UAAL,CAAgBpB,MAAhB,GAAyBmB,eAAe,CAACR,IAAzC;AACH,aAHD,MAGO;AACH,mBAAKS,UAAL,CAAgBpB,MAAhB,GAAyB,EAAzB;AACH;AACJ;AACJ;;AAEM/B,QAAAA,WAAW,CAACoD,IAAD,EAAiBxB,GAAjB,EAA8ByB,CAA9B,EAAyCC,CAAzC,EAA0D;AACxE,eAAKzD,KAAL,GAAauD,IAAb;AACA,eAAKnD,IAAL,GAAY2B,GAAZ;AACA,eAAK1B,IAAL,GAAYmD,CAAZ;AACA,eAAKlD,IAAL,GAAYmD,CAAZ,CAJwE,CAKxE;;AACA,eAAKzC,UAAL;AACH;;AAvHsD,O;;;;;iBAEvC,I;;;;;;;iBAEE,I;;;;;;;iBAEC,I;;;;;;;iBAEF,I;;;;;;;iBAEE,I;;;;;;;iBAEC,I;;;;;;;iBAEC,I;;;;;;;iBAEI,I;;;;;;;iBAEL,I;;;;;;;iBAEA,I;;;;;;;iBAEW,I", "sourcesContent": ["import { _decorator, Component, Node, Label, Sprite, ProgressBar } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { ArmyCmd, ArmyData } from \"../../general/ArmyProxy\";\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport { GeneralCommonConfig, GeneralConfig, GeneralData } from \"../../general/GeneralProxy\";\nimport GeneralHeadLogic from \"./GeneralHeadLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('ArmySelectItemLogic')\nexport default class ArmySelectItemLogic extends Component {\n    @property(Node)\n    tipNode: Node = null;\n    @property(Label)\n    labelTip: Label = null;\n    @property(Sprite)\n    headIcon: Sprite = null;\n    @property(Label)\n    labelLv: Label = null;\n    @property(Label)\n    labelName: Label = null;\n    @property(Label)\n    labelState: Label = null;\n    @property(Label)\n    labelMorale: Label = null;\n    @property(Label)\n    labelSoldierCnt: Label = null;\n    @property(Label)\n    labelVice1: Label = null;\n    @property(Label)\n    labelVice2: Label = null;\n    @property(ProgressBar)\n    progressSoldier: ProgressBar = null;\n\n    protected _data: ArmyData = null;\n    protected _cmd: number = 0;\n    protected _toX: number = 0;\n    protected _toY: number = 0;\n\n    protected onLoad(): void {\n        EventMgr.on(LogicEvent.updateArmy, this.onUpdateArmy, this);\n        this.tipNode.active = false;\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._data = null;\n    }\n\n    protected onUpdateArmy(armyData: ArmyData): void {\n        if (armyData.id == this._data.id) {\n            this.setArmyData(armyData, this._cmd, this._toX, this._toY);\n        }\n    }\n\n    protected onClickItem(): void {\n        AudioManager.instance.playClick();\n        if (this.tipNode.active == false) {\n            ArmyCommand.getInstance().generalAssignArmy(this._data.id, this._cmd, this._toX, this._toY);\n            EventMgr.emit(LogicEvent.closeArmyAelectUi);\n        } else {\n            console.log(\"军队忙\");\n        }\n    }\n\n    protected updateItem(): void {\n        if (this._data && this._data.generals[0] != 0) {\n            console.log(\"updateItem\", this._data);\n            let commonCfg: GeneralCommonConfig = GeneralCommand.getInstance().proxy.getCommonCfg();\n            let generals: GeneralData[] = ArmyCommand.getInstance().getArmyGenerals(this._data);\n            let firstGeneralCfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(generals[0].cfgId);\n            let power: number = ArmyCommand.getInstance().getArmyPhysicalPowerByGenerals(generals);\n            let curSoldierCnt: number = ArmyCommand.getInstance().getArmyCurSoldierCnt(this._data);\n            let totalSoldierCnt: number = ArmyCommand.getInstance().getArmyTotalSoldierCntByGenerals(generals);\n\n            if (this._data.cmd == ArmyCmd.Conscript) {\n                //体力不足\n                this.tipNode.active = true;\n                this.labelTip.string = \"征兵中...\";\n            }else if (power < commonCfg.recovery_physical_power) {\n                //体力不足\n                this.tipNode.active = true;\n                this.labelTip.string = \"体力不足\";\n            } else if (this._data.soldiers[0] <= 0) {\n                //兵力不足\n                this.tipNode.active = true;\n                this.labelTip.string = \"主将兵力不足\";\n            } else if (this._data.state > 0) {\n                //行军中\n                this.tipNode.active = true;\n                this.labelTip.string = \"行军中...\";\n            } else if (this._data.cmd == ArmyCmd.Reclaim) {\n                //屯田中\n                this.tipNode.active = true;\n                this.labelTip.string = \"屯田中...\";\n            } else {\n                this.tipNode.active = false;\n            }\n\n            this.headIcon.getComponent(GeneralHeadLogic).setHeadId(generals[0].cfgId);\n            this.labelLv.string = generals[0].level + \"\";\n            this.labelName.string = firstGeneralCfg.name;\n            this.labelState.string = ArmyCommand.getInstance().getArmyStateDes(this._data);\n            this.labelSoldierCnt.string = curSoldierCnt + \"/\" + totalSoldierCnt;\n            this.progressSoldier.progress = curSoldierCnt / totalSoldierCnt;\n\n            if (generals[1]) {\n                let sencondGeneralCfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(generals[1].cfgId);\n                this.labelVice1.string = sencondGeneralCfg.name;\n            } else {\n                this.labelVice1.string = \"\";\n            }\n\n            if (generals[2]) {\n                let thirdGeneralCfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(generals[2].cfgId);\n                this.labelVice2.string = thirdGeneralCfg.name;\n            } else {\n                this.labelVice2.string = \"\";\n            }\n        }\n    }\n\n    public setArmyData(data: ArmyData, cmd: number, x: number, y: number): void {\n        this._data = data;\n        this._cmd = cmd;\n        this._toX = x;\n        this._toY = y;\n        // console.log(\"setArmyData\", arguments);\n        this.updateItem();\n    }\n}\n"]}