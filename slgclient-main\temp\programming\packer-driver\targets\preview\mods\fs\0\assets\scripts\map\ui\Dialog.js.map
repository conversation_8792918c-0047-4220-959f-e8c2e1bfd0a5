{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts"], "names": ["DialogType", "_decorator", "Component", "Label", "<PERSON><PERSON>", "AudioManager", "ccclass", "property", "Dialog", "close", "closeCB", "node", "active", "show", "text", "type", "string", "<PERSON><PERSON><PERSON>", "confirmBtn", "cancelBtn", "OnlyCancel", "OnlyConfirm", "setClose", "cb", "setConfirmCB", "confirmCB", "setCancelCB", "cancelCB", "clickConfirm", "instance", "playClick", "clickCancel"], "mappings": ";;;kFAKaA,U;;;;;;;;;;;;;;;;;;;;;AAJJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAC9BC,MAAAA,Y,iBAAAA,Y;;;;;;;OACH;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBN,U;;4BAEfD,U,GAAN,MAAMA,UAAN,CAAiB,E;;sBAAXA,U,aACsB,C;;sBADtBA,U,gBAEyB,C;;sBAFzBA,U,iBAG0B,C;;yBAIlBQ,M,WADpBF,OAAO,CAAC,QAAD,C,UAGHC,QAAQ,CAACJ,KAAD,C,UAGRI,QAAQ,CAACH,MAAD,C,UAGRG,QAAQ,CAACH,MAAD,C,oCATb,MACqBI,MADrB,SACoCN,SADpC,CAC8C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,2CAWZ,IAXY;;AAAA,6CAYV,IAZU;;AAAA,4CAaX,IAbW;AAAA;;AAgBhCO,QAAAA,KAAK,GAAS;AACpB,cAAI,KAAKC,OAAT,EAAiB;AACb,iBAAKA,OAAL;AACH;;AACD,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAEMC,QAAAA,IAAI,CAACC,IAAD,EAAeC,IAAf,EAAmC;AAI3C,eAAKD,IAAL,CAAUE,MAAV,GAAmBF,IAAnB;;AACA,cAAGC,IAAI,IAAIf,UAAU,CAACiB,OAAtB,EAA8B;AACzB,iBAAKC,UAAL,CAAgBP,IAAhB,CAAqBC,MAArB,GAA8B,IAA9B;AACA,iBAAKO,SAAL,CAAeR,IAAf,CAAoBC,MAApB,GAA6B,IAA7B;AACJ,WAHD,MAGM,IAAGG,IAAI,IAAIf,UAAU,CAACoB,UAAtB,EAAiC;AAClC,iBAAKF,UAAL,CAAgBP,IAAhB,CAAqBC,MAArB,GAA8B,KAA9B;AACA,iBAAKO,SAAL,CAAeR,IAAf,CAAoBC,MAApB,GAA6B,IAA7B;AACJ,WAHK,MAGA,IAAGG,IAAI,IAAIf,UAAU,CAACqB,WAAtB,EAAkC;AACnC,iBAAKH,UAAL,CAAgBP,IAAhB,CAAqBC,MAArB,GAA8B,IAA9B;AACA,iBAAKO,SAAL,CAAeR,IAAf,CAAoBC,MAApB,GAA6B,KAA7B;AACH;AACJ;;AAEMU,QAAAA,QAAQ,CAACC,EAAD,EAAc;AACzB,eAAKb,OAAL,GAAea,EAAf;AACH;;AAEMC,QAAAA,YAAY,CAACD,EAAD,EAAe;AAC9B,eAAKE,SAAL,GAAiBF,EAAjB;AACH;;AAEMG,QAAAA,WAAW,CAACH,EAAD,EAAe;AAC7B,eAAKI,QAAL,GAAgBJ,EAAhB;AACH;;AAESK,QAAAA,YAAY,GAAE;AACpB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKL,SAAR,EAAkB;AACd,iBAAKA,SAAL;AACH;;AAED,eAAKhB,KAAL;AACH;;AAESsB,QAAAA,WAAW,GAAE;AACnB;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKH,QAAR,EAAiB;AACb,iBAAKA,QAAL;AACH;;AACD,eAAKlB,KAAL;AACH;;AAnEyC,O;;;;;iBAG5B,I;;;;;;;iBAGO,I;;;;;;;iBAGD,I", "sourcesContent": ["\nimport { _decorator, Component, Label, But<PERSON>, Node, tween, Vec3 } from 'cc';\nimport { AudioManager } from '../../common/AudioManager';\nconst {ccclass, property} = _decorator;\n\nexport class DialogType {\n    public static Default:number = 0;\n    public static OnlyCancel:number = 1;\n    public static OnlyConfirm:number = 2;\n}\n\n@ccclass('Dialog')\nexport default class Dialog extends Component {\n\n    @property(Label)\n    text: Label = null;\n\n    @property(Button)\n    confirmBtn: Button = null;\n\n    @property(Button)\n    cancelBtn: Button = null;\n\n    protected closeCB: Function = null;\n    protected confirmCB: Function = null;\n    protected cancelCB: Function = null;\n\n\n    protected close(): void {\n        if (this.closeCB){\n            this.closeCB()\n        }\n        this.node.active = false;\n    }\n\n    public show(text: string, type: number): void {\n\n        \n\n       this.text.string = text;\n       if(type == DialogType.Default){\n            this.confirmBtn.node.active = true;\n            this.cancelBtn.node.active = true;\n       }else if(type == DialogType.OnlyCancel){\n            this.confirmBtn.node.active = false;\n            this.cancelBtn.node.active = true;\n       }else if(type == DialogType.OnlyConfirm){\n            this.confirmBtn.node.active = true;\n            this.cancelBtn.node.active = false;\n        }\n    }\n\n    public setClose(cb :Function){\n        this.closeCB = cb;\n    }\n\n    public setConfirmCB(cb :Function) {\n        this.confirmCB = cb;\n    }\n\n    public setCancelCB(cb :Function) {\n        this.cancelCB = cb;\n    }\n\n    protected clickConfirm(){\n        AudioManager.instance.playClick();\n        if(this.confirmCB){\n            this.confirmCB();\n        }\n\n        this.close();\n    }\n\n    protected clickCancel(){\n        AudioManager.instance.playClick();\n        if(this.cancelCB){\n            this.cancelCB();\n        }\n        this.close();\n    }\n\n}\n"]}