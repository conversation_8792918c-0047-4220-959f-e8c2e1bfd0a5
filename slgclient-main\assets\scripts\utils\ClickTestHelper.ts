import { _decorator, Component, Node, EventTouch, UITransform, Vec3, Label, view, sys, director, screen } from 'cc';
import { FixedScreenAdapter } from './FixedScreenAdapter';

const { ccclass, property } = _decorator;

/**
 * 点击测试助手
 * 用于测试和调试点击功能是否正常
 */
@ccclass('ClickTestHelper')
export class ClickTestHelper extends Component {
    
    @property(Label)
    debugLabel: Label = null;
    
    private _touchCount: number = 0;
    
    protected onLoad(): void {
        // 监听全局触摸事件
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        
        // 创建调试信息显示
        this.createDebugDisplay();
        
        // 定期更新调试信息
        this.schedule(this.updateDebugInfo, 1.0);
    }
    
    protected onDestroy(): void {
        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.unschedule(this.updateDebugInfo);
    }
    
    /**
     * 创建调试信息显示
     */
    private createDebugDisplay(): void {
        if (!this.debugLabel) {
            // 创建调试标签节点
            const debugNode = new Node('DebugLabel');
            debugNode.setParent(this.node);
            
            // 添加UITransform组件
            const uiTransform = debugNode.addComponent(UITransform);
            uiTransform.setContentSize(400, 200);
            
            // 添加Label组件
            this.debugLabel = debugNode.addComponent(Label);
            this.debugLabel.fontSize = 20;
            this.debugLabel.lineHeight = 25;
            this.debugLabel.string = '点击测试助手已启动';
            
            // 设置位置（左上角）
            const visibleSize = view.getVisibleSize();
            debugNode.setPosition(-visibleSize.width/2 + 200, visibleSize.height/2 - 100, 0);
        }
    }
    
    /**
     * 触摸开始事件
     */
    private onTouchStart(event: EventTouch): void {
        this._touchCount++;
        const touchPos = event.getUILocation();
        const worldPos = new Vec3(touchPos.x, touchPos.y, 0);

        // 获取更多调试信息
        const adaptInfo = FixedScreenAdapter.instance ? FixedScreenAdapter.instance.getAdaptInfo() : null;

        console.log(`[ClickTest] 触摸开始 #${this._touchCount}:`);
        console.log(`  UI坐标: (${touchPos.x.toFixed(1)}, ${touchPos.y.toFixed(1)})`);
        if (adaptInfo) {
            console.log(`  窗口尺寸: ${adaptInfo.windowSize.width}x${adaptInfo.windowSize.height}`);
            console.log(`  可视区域: ${adaptInfo.visibleSize.width.toFixed(1)}x${adaptInfo.visibleSize.height.toFixed(1)}`);
            console.log(`  可视原点: (${adaptInfo.visibleOrigin.x.toFixed(1)}, ${adaptInfo.visibleOrigin.y.toFixed(1)})`);
            console.log(`  缩放比例: X=${adaptInfo.scaleX.toFixed(3)}, Y=${adaptInfo.scaleY.toFixed(3)}`);
        }
        console.log(`  设计分辨率: ${adaptInfo.designSize.width}x${adaptInfo.designSize.height}`);
        console.log(`  可视区域: ${adaptInfo.visibleSize.width.toFixed(0)}x${adaptInfo.visibleSize.height.toFixed(0)}`);
        console.log(`  缩放比例: X=${adaptInfo.scaleX.toFixed(3)}, Y=${adaptInfo.scaleY.toFixed(3)}`);

        // 检查点击的UI元素
        this.checkClickedElement(worldPos);

        // 更新调试信息
        this.updateTouchDebugInfo(touchPos, '开始');
    }
    
    /**
     * 触摸移动事件
     */
    private onTouchMove(event: EventTouch): void {
        const touchPos = event.getUILocation();
        // 只在移动距离较大时记录
        const delta = event.getDelta();
        if (Math.abs(delta.x) > 5 || Math.abs(delta.y) > 5) {
            // 获取屏幕适配信息
            const canvas = director.root!.mainWindow!;
            const designSize = screen.designResolution;
            const windowSize = screen.windowSize;

            console.log(`[ClickTest] 触摸移动: UI坐标(${touchPos.x.toFixed(1)}, ${touchPos.y.toFixed(1)})`);
            console.log(`[ClickTest] 屏幕适配: 设计分辨率${designSize.width}x${designSize.height}, 窗口大小${windowSize.width}x${windowSize.height}`);
        }
    }
    
    /**
     * 触摸结束事件
     */
    private onTouchEnd(event: EventTouch): void {
        const touchPos = event.getUILocation();
        console.log(`[ClickTest] 触摸结束: UI坐标(${touchPos.x.toFixed(1)}, ${touchPos.y.toFixed(1)})`);
        
        // 更新调试信息
        this.updateTouchDebugInfo(touchPos, '结束');
    }
    
    /**
     * 检查点击的UI元素
     */
    private checkClickedElement(worldPos: Vec3): void {
        // 这里可以添加具体的UI元素检测逻辑
        // 例如检查是否点击了按钮、输入框等
        console.log(`[ClickTest] 检查世界坐标(${worldPos.x.toFixed(1)}, ${worldPos.y.toFixed(1)})处的UI元素`);
    }
    
    /**
     * 更新触摸调试信息
     */
    private updateTouchDebugInfo(touchPos: any, action: string): void {
        if (!this.debugLabel) return;
        
        const adaptInfo = FixedScreenAdapter.instance ? FixedScreenAdapter.instance.getAdaptInfo() : null;
        const deviceType = sys.isMobile ? 'Mobile' : 'Desktop';
        
        const debugText = `点击测试助手
设备类型: ${deviceType}
触摸次数: ${this._touchCount}
最后${action}: (${touchPos.x.toFixed(1)}, ${touchPos.y.toFixed(1)})
${adaptInfo ? `窗口尺寸: ${adaptInfo.windowSize.width}x${adaptInfo.windowSize.height}
可视区域: ${adaptInfo.visibleSize.width.toFixed(0)}x${adaptInfo.visibleSize.height.toFixed(0)}
缩放比例: X=${adaptInfo.scaleX.toFixed(3)}, Y=${adaptInfo.scaleY.toFixed(3)}` : '适配器未初始化'}`;
        
        this.debugLabel.string = debugText;
    }
    
    /**
     * 定期更新调试信息
     */
    private updateDebugInfo(): void {
        if (!this.debugLabel) return;
        
        const adaptInfo = FixedScreenAdapter.instance ? FixedScreenAdapter.instance.getAdaptInfo() : null;
        const deviceType = sys.isMobile ? 'Mobile' : 'Desktop';
        
        let safeAreaInfo = '';
        if (sys.isMobile) {
            const safeArea = sys.getSafeAreaRect();
            safeAreaInfo = `\n安全区域: ${safeArea.width.toFixed(0)}x${safeArea.height.toFixed(0)}`;
        }
        
        const debugText = `点击测试助手
设备类型: ${deviceType}
触摸次数: ${this._touchCount}
${adaptInfo ? `窗口尺寸: ${adaptInfo.windowSize.width}x${adaptInfo.windowSize.height}
可视区域: ${adaptInfo.visibleSize.width.toFixed(0)}x${adaptInfo.visibleSize.height.toFixed(0)}
缩放比例: X=${adaptInfo.scaleX.toFixed(3)}, Y=${adaptInfo.scaleY.toFixed(3)}` : '适配器未初始化'}${safeAreaInfo}
状态: 等待点击...`;
        
        this.debugLabel.string = debugText;
    }
    
    /**
     * 手动触发适配信息打印
     */
    public printAdaptInfo(): void {
        if (FixedScreenAdapter.instance) {
            FixedScreenAdapter.instance.printDebugInfo();
        }
        console.log('=== 点击测试统计 ===');
        console.log(`总触摸次数: ${this._touchCount}`);
        console.log('==================');
    }
}
