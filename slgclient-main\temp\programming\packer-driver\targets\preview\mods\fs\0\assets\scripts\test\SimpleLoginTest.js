System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Node, Button, Label, EditBox, Canvas, UITransform, Color, Sprite, view, screen, SimpleLoginUI, _dec, _class, _temp, _crd, ccclass, property, SimpleLoginTest;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfSimpleLoginUI(extras) {
    _reporterNs.report("SimpleLoginUI", "../login/SimpleLoginUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Button = _cc.Button;
      Label = _cc.Label;
      EditBox = _cc.EditBox;
      Canvas = _cc.Canvas;
      UITransform = _cc.UITransform;
      Color = _cc.Color;
      Sprite = _cc.Sprite;
      view = _cc.view;
      screen = _cc.screen;
    }, function (_unresolved_2) {
      SimpleLoginUI = _unresolved_2.SimpleLoginUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1fde2YxwX5JqonOBdQRM8/R", "SimpleLoginTest", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 简单登录测试组件
       * 用于测试点击响应是否正常
       */

      _export("SimpleLoginTest", SimpleLoginTest = (_dec = ccclass('SimpleLoginTest'), _dec(_class = (_temp = class SimpleLoginTest extends Component {
        constructor() {
          super(...arguments);

          _defineProperty(this, "testButton", null);

          _defineProperty(this, "testLabel", null);

          _defineProperty(this, "clickCount", 0);
        }

        onLoad() {
          console.log('[SimpleLoginTest] 开始创建简单登录测试界面'); // 创建测试UI

          this.createTestUI(); // 打印调试信息

          this.printDebugInfo();
        }
        /**
         * 创建测试UI
         */


        createTestUI() {
          // 确保当前节点有Canvas组件
          var canvas = this.getComponent(Canvas);

          if (!canvas) {
            canvas = this.addComponent(Canvas);
          } // 设置Canvas


          canvas.alignCanvasWithScreen = false;
          this.node.setPosition(0, 0, 0); // 设置UITransform

          var uiTransform = this.getComponent(UITransform);

          if (uiTransform) {
            var windowSize = screen.windowSize;
            uiTransform.setContentSize(windowSize.width, windowSize.height);
            uiTransform.setAnchorPoint(0.5, 0.5);
            console.log("[SimpleLoginTest] Canvas\u5C3A\u5BF8\u8BBE\u7F6E\u4E3A: " + windowSize.width + "x" + windowSize.height);
          } // 创建背景


          this.createBackground(); // 创建测试按钮

          this.createTestButton(); // 创建状态标签

          this.createStatusLabel(); // 添加切换到实际登录UI的按钮

          this.addSwitchToLoginUIButton();
          console.log('[SimpleLoginTest] 测试UI创建完成');
        }
        /**
         * 创建背景
         */


        createBackground() {
          var bgNode = new Node('Background');
          bgNode.setParent(this.node);
          var bgTransform = bgNode.addComponent(UITransform);
          var windowSize = screen.windowSize;
          bgTransform.setContentSize(windowSize.width, windowSize.height);
          bgTransform.setAnchorPoint(0.5, 0.5);
          var bgSprite = bgNode.addComponent(Sprite);
          bgSprite.color = new Color(50, 50, 50, 255); // 深灰色背景

          bgNode.setPosition(0, 0, 0);
          console.log('[SimpleLoginTest] 背景创建完成');
        }
        /**
         * 创建测试按钮
         */


        createTestButton() {
          var buttonNode = new Node('TestButton');
          buttonNode.setParent(this.node); // 设置按钮尺寸和位置

          var buttonTransform = buttonNode.addComponent(UITransform);
          buttonTransform.setContentSize(200, 60);
          buttonTransform.setAnchorPoint(0.5, 0.5);
          buttonNode.setPosition(0, 0, 0); // 屏幕中央
          // 添加按钮背景

          var buttonSprite = buttonNode.addComponent(Sprite);
          buttonSprite.color = new Color(0, 150, 255, 255); // 蓝色按钮
          // 添加按钮组件

          this.testButton = buttonNode.addComponent(Button);
          this.testButton.transition = Button.Transition.COLOR;
          this.testButton.normalColor = new Color(0, 150, 255, 255);
          this.testButton.pressedColor = new Color(0, 100, 200, 255);
          this.testButton.hoverColor = new Color(50, 180, 255, 255);
          this.testButton.disabledColor = new Color(100, 100, 100, 255); // 添加按钮文字

          var labelNode = new Node('Label');
          labelNode.setParent(buttonNode);
          var labelTransform = labelNode.addComponent(UITransform);
          labelTransform.setContentSize(200, 60);
          labelTransform.setAnchorPoint(0.5, 0.5);
          labelNode.setPosition(0, 0, 0);
          var label = labelNode.addComponent(Label);
          label.string = "\u70B9\u51FB\u6D4B\u8BD5 (" + this.clickCount + ")";
          label.fontSize = 24;
          label.color = new Color(255, 255, 255, 255); // 绑定点击事件

          this.testButton.node.on(Button.EventType.CLICK, this.onTestButtonClick, this);
          console.log('[SimpleLoginTest] 测试按钮创建完成，位置: (0, 0, 0)');
        }
        /**
         * 创建状态标签
         */


        createStatusLabel() {
          var labelNode = new Node('StatusLabel');
          labelNode.setParent(this.node);
          var labelTransform = labelNode.addComponent(UITransform);
          labelTransform.setContentSize(400, 100);
          labelTransform.setAnchorPoint(0.5, 0.5);
          labelNode.setPosition(0, -150, 0); // 按钮下方

          this.testLabel = labelNode.addComponent(Label);
          this.testLabel.string = '等待点击测试...';
          this.testLabel.fontSize = 18;
          this.testLabel.color = new Color(255, 255, 255, 255);
          console.log('[SimpleLoginTest] 状态标签创建完成');
        }
        /**
         * 测试按钮点击事件
         */


        onTestButtonClick() {
          this.clickCount++;
          console.log("[SimpleLoginTest] \u6309\u94AE\u88AB\u70B9\u51FB\uFF01\u70B9\u51FB\u6B21\u6570: " + this.clickCount); // 更新按钮文字

          var buttonLabel = this.testButton.node.getComponentInChildren(Label);

          if (buttonLabel) {
            buttonLabel.string = "\u70B9\u51FB\u6D4B\u8BD5 (" + this.clickCount + ")";
          } // 更新状态标签


          if (this.testLabel) {
            this.testLabel.string = "\u2705 \u70B9\u51FB\u6210\u529F\uFF01\u603B\u8BA1: " + this.clickCount + " \u6B21\n\u70B9\u51FB\u54CD\u5E94\u6B63\u5E38\uFF0CUI\u9002\u914D\u5DE5\u4F5C\u6B63\u5E38";
            this.testLabel.color = new Color(0, 255, 0, 255); // 绿色表示成功
          } // 打印详细的点击信息


          this.printClickInfo();
        }
        /**
         * 打印点击信息
         */


        printClickInfo() {
          var windowSize = screen.windowSize;
          var visibleSize = view.getVisibleSize();
          var visibleOrigin = view.getVisibleOrigin();
          var designSize = view.getDesignResolutionSize();
          console.log('=== 点击测试成功信息 ===');
          console.log("\u70B9\u51FB\u6B21\u6570: " + this.clickCount);
          console.log("\u7A97\u53E3\u5C3A\u5BF8: " + windowSize.width + "x" + windowSize.height);
          console.log("\u8BBE\u8BA1\u5206\u8FA8\u7387: " + designSize.width + "x" + designSize.height);
          console.log("\u53EF\u89C6\u533A\u57DF: " + visibleSize.width.toFixed(1) + "x" + visibleSize.height.toFixed(1));
          console.log("\u53EF\u89C6\u539F\u70B9: (" + visibleOrigin.x.toFixed(1) + ", " + visibleOrigin.y.toFixed(1) + ")");
          console.log('========================');
        }
        /**
         * 打印调试信息
         */


        printDebugInfo() {
          var windowSize = screen.windowSize;
          var visibleSize = view.getVisibleSize();
          var visibleOrigin = view.getVisibleOrigin();
          var designSize = view.getDesignResolutionSize();
          console.log('=== 简单登录测试调试信息 ===');
          console.log("\u7A97\u53E3\u5C3A\u5BF8: " + windowSize.width + "x" + windowSize.height);
          console.log("\u8BBE\u8BA1\u5206\u8FA8\u7387: " + designSize.width + "x" + designSize.height);
          console.log("\u53EF\u89C6\u533A\u57DF: " + visibleSize.width.toFixed(1) + "x" + visibleSize.height.toFixed(1));
          console.log("\u53EF\u89C6\u539F\u70B9: (" + visibleOrigin.x.toFixed(1) + ", " + visibleOrigin.y.toFixed(1) + ")"); // 检查Canvas设置

          var canvas = this.getComponent(Canvas);

          if (canvas) {
            console.log("Canvas alignCanvasWithScreen: " + canvas.alignCanvasWithScreen);
          }

          console.log('============================');
        }
        /**
         * 添加一个简单的输入框测试
         */


        addInputTest() {
          var inputNode = new Node('TestInput');
          inputNode.setParent(this.node);
          var inputTransform = inputNode.addComponent(UITransform);
          inputTransform.setContentSize(300, 50);
          inputTransform.setAnchorPoint(0.5, 0.5);
          inputNode.setPosition(0, 100, 0); // 按钮上方
          // 添加白色背景

          var inputSprite = inputNode.addComponent(Sprite);
          inputSprite.color = new Color(255, 255, 255, 255); // 添加黑色边框

          var borderNode = new Node('Border');
          borderNode.setParent(inputNode);
          var borderTransform = borderNode.addComponent(UITransform);
          borderTransform.setContentSize(304, 54);
          borderTransform.setAnchorPoint(0.5, 0.5);
          borderNode.setPosition(0, 0, -1);
          var borderSprite = borderNode.addComponent(Sprite);
          borderSprite.color = new Color(0, 0, 0, 255);
          var editBox = inputNode.addComponent(EditBox);
          editBox.string = '';
          editBox.placeholder = '点击这里输入测试文字';
          editBox.fontSize = 18;
          editBox.fontColor = new Color(0, 0, 0, 255); // 黑色文字

          editBox.placeholderFontColor = new Color(128, 128, 128, 255); // 灰色占位符
          // 添加输入框标签

          var labelNode = new Node('InputLabel');
          labelNode.setParent(this.node);
          var labelTransform = labelNode.addComponent(UITransform);
          labelTransform.setContentSize(300, 30);
          labelTransform.setAnchorPoint(0.5, 0.5);
          labelNode.setPosition(0, 140, 0);
          var label = labelNode.addComponent(Label);
          label.string = '输入框测试：';
          label.fontSize = 16;
          label.color = new Color(255, 255, 255, 255);
          console.log('[SimpleLoginTest] 输入框测试添加完成');
        }
        /**
         * 添加切换到实际登录UI的按钮
         */


        addSwitchToLoginUIButton() {
          var buttonNode = new Node('SwitchToLoginButton');
          buttonNode.setParent(this.node);
          var buttonTransform = buttonNode.addComponent(UITransform);
          buttonTransform.setContentSize(200, 50);
          buttonTransform.setAnchorPoint(0.5, 0.5);
          buttonNode.setPosition(0, -200, 0); // 按钮背景

          var buttonSprite = buttonNode.addComponent(Sprite);
          buttonSprite.color = new Color(0, 200, 0, 255); // 绿色
          // 按钮组件

          var button = buttonNode.addComponent(Button);
          button.transition = Button.Transition.COLOR;
          button.normalColor = new Color(0, 200, 0, 255);
          button.pressedColor = new Color(0, 150, 0, 255);
          button.hoverColor = new Color(50, 220, 50, 255); // 按钮文字

          var labelNode = new Node('Label');
          labelNode.setParent(buttonNode);
          var labelTransform = labelNode.addComponent(UITransform);
          labelTransform.setContentSize(200, 50);
          labelTransform.setAnchorPoint(0.5, 0.5);
          labelNode.setPosition(0, 0, 0);
          var label = labelNode.addComponent(Label);
          label.string = '切换到登录界面';
          label.fontSize = 18;
          label.color = new Color(255, 255, 255, 255); // 绑定点击事件

          button.node.on(Button.EventType.CLICK, this.createActualLoginUI, this);
          console.log('[SimpleLoginTest] 切换按钮添加完成');
        }
        /**
         * 创建实际的登录UI（替代测试UI）
         */


        createActualLoginUI() {
          console.log('[SimpleLoginTest] 开始创建实际登录UI'); // 清除当前的测试UI

          this.node.removeAllChildren(); // 创建登录UI节点

          var loginUINode = new Node('LoginUI');
          loginUINode.setParent(this.node); // 添加SimpleLoginUI组件

          var loginUI = loginUINode.addComponent(_crd && SimpleLoginUI === void 0 ? (_reportPossibleCrUseOfSimpleLoginUI({
            error: Error()
          }), SimpleLoginUI) : SimpleLoginUI);
          console.log('[SimpleLoginTest] 实际登录UI创建完成');
        }

      }, _temp)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=SimpleLoginTest.js.map