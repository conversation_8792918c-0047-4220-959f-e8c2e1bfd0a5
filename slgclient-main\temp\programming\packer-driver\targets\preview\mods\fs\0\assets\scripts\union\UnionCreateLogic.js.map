{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts"], "names": ["_decorator", "Component", "EditBox", "UnionCommand", "EventMgr", "AudioManager", "createName", "LogicEvent", "ccclass", "property", "UnionCreateLogic", "onLoad", "on", "createUnionSuccess", "onUnCreateOk", "editName", "string", "getRandomName", "onCreate", "instance", "playClick", "getInstance", "unionCreate", "onRandomName", "name", "onDestroy", "targetOff", "node", "active", "onClickClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;;AAGzBC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBASTU,gB,WADpBF,OAAO,CAAC,kBAAD,C,UAEHC,QAAQ,CAACP,OAAD,C,oCAFb,MACqBQ,gBADrB,SAC8CT,SAD9C,CACwD;AAAA;AAAA;;AAAA;AAAA;;AAG1CU,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,kBAAvB,EAA2C,KAAKC,YAAhD,EAA6D,IAA7D;AACA,eAAKC,QAAL,CAAcC,MAAd,GAAuB,KAAKC,aAAL,EAAvB;AACH;;AAESC,QAAAA,QAAQ,GAAG;AACjB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,WAA3B,CAAuC,KAAKP,QAAL,CAAcC,MAArD;AACH;;AAESO,QAAAA,YAAY,GAAO;AACzB;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;AACA,eAAKL,QAAL,CAAcC,MAAd,GAAuB,KAAKC,aAAL,EAAvB;AACH;;AACSA,QAAAA,aAAa,GAAS;AAC5B,cAAIO,IAAI,GAAG;AAAA;AAAA,wCAAW,KAAX,CAAX;AACA,iBAAOA,IAAP;AACF;;AAEQC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESZ,QAAAA,YAAY,GAAE;AACpB,eAAKa,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B,eAAKF,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaT,QAAb,CAAsBC,SAAtB;AACH;;AAjCmD,O;;;;;iBAEzB,I", "sourcesContent": ["\nimport { _decorator, Component, EditBox } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport UnionCommand from \"./UnionCommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { AudioManager } from '../common/AudioManager';\nimport { createName } from '../libs/NameDict';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('UnionCreateLogic')\nexport default class UnionCreateLogic extends Component {\n    @property(EditBox)\n    editName: EditBox | null = null;\n    protected onLoad():void{\n        EventMgr.on(LogicEvent.createUnionSuccess, this.onUnCreateOk,this)\n        this.editName.string = this.getRandomName();\n    }\n\n    protected onCreate() {\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionCreate(this.editName.string);\n    }\n\n    protected onRandomName():void{\n        AudioManager.instance.playClick();\n        this.editName.string = this.getRandomName();\n    }\n    protected getRandomName():string{\n        let name = createName(\"boy\");\n        return name\n     }\n\n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onUnCreateOk(){\n        this.node.active = false;\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n}\n"]}