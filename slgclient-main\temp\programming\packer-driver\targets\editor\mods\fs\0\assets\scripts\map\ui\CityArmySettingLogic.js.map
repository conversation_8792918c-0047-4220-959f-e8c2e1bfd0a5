{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts"], "names": ["_decorator", "Component", "Label", "Node", "Prefab", "EditBox", "Slide<PERSON>", "instantiate", "ArmyCmd", "GeneralCommand", "ArmyCommand", "GeneralCampType", "MapUICommand", "MapCommand", "CityGeneralItemLogic", "LoginCommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "CityArmySettingLogic", "onLoad", "initView", "additionTipNode", "active", "additionTouchNode", "on", "EventType", "TOUCH_START", "onShowAdditionTip", "TOUCH_END", "onHideAdditionTip", "TOUCH_CANCEL", "onDestroy", "_gengeralLogics", "length", "onEnable", "_soldiers", "_totalSoldiers", "_curConscripts", "_conTimes", "_conCnts", "updateArmy", "onUpdateArmy", "chosedGeneral", "onChooseGeneral", "updateCityAddition", "onUpdateAddition", "onDisable", "targetOff", "_data", "_addition", "_cityData", "i", "_generalCnt", "item", "generalPrefab", "parent", "<PERSON><PERSON><PERSON><PERSON>", "comp", "getComponent", "index", "push", "labelAddition", "string", "clearSoldiers", "armyData", "cityId", "_cityId", "order", "_order", "setData", "cfgData", "curData", "position", "getInstance", "generalDispose", "id", "Number", "updateView", "generalData", "generalCfg", "isUnlock", "totalCost", "soldierCnt", "totalSoldierCnt", "_isUnlock", "vanguardCnt", "generals", "proxy", "getMyGeneral", "getGeneralCfg", "cfgId", "cost", "soldiers", "level", "conTimes", "conCnts", "tipNodes", "editBoxs", "node", "sliders", "totalValue", "cmd", "Conscript", "can", "enabled", "progress", "Idle", "setCurConscriptForIndex", "curValue", "Math", "max", "min", "parseInt", "labelId", "labelCost", "getMyCityCost", "labelSoldierCnt", "getArmyGenerals", "speed", "getArmySpeed", "labelSpeed", "toFixed", "destroy", "getArmy<PERSON><PERSON>roy", "labelAtkCity", "camp", "getArmyCamp", "Han", "han", "labelAdditionTip", "<PERSON><PERSON>", "qun", "<PERSON>", "wei", "Shu", "shu", "<PERSON>", "wu", "updateResCost", "cnt", "maxCnt", "percent", "getTotalConscriptCnt", "totalCnt", "myRoleRes", "getRoleResData", "baseCost", "getConscriptBaseCost", "str", "cost_gold", "gold", "cost_wood", "wood", "cost_iron", "iron", "cost_grain", "grain", "labelResCost", "onChangeEditBox", "editBox", "indexOf", "onChangeSlider", "slider", "floor", "onClickQuick", "instance", "playClick", "onClickSure", "generalConscript", "onClickPrev", "armyCnt", "onClickNext", "onClickClose", "cityProxy", "getMyCityById", "getArmyByOrder", "getMyCityAddition", "facility", "getMyFacilitys", "qryCityFacilities"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAG7DC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,c;;AACAC,MAAAA,W;;AACEC,MAAAA,e,iBAAAA,e;;AACFC,MAAAA,Y;;AACAC,MAAAA,U;;AAGAC,MAAAA,oB;;AACAC,MAAAA,Y;;AAEEC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;OAfH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBpB,U;;yBAkBTqB,oB,WADpBF,OAAO,CAAC,sBAAD,C,UAEHC,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAACjB,IAAD,C,WAERiB,QAAQ,CAACjB,IAAD,C,WAERiB,QAAQ,CAAClB,KAAD,C,WAERkB,QAAQ,CAACjB,IAAD,C,WAERiB,QAAQ,CAAChB,MAAD,C,WAERgB,QAAQ,CAAC,CAACf,OAAD,CAAD,C,WAERe,QAAQ,CAAC,CAACd,MAAD,CAAD,C,WAERc,QAAQ,CAAC,CAACjB,IAAD,CAAD,C,oCA9Bb,MACqBkB,oBADrB,SACkDpB,SADlD,CAC4D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,+CAgCxB,CAhCwB;;AAAA,0CAiC7B,CAjC6B;;AAAA,2CAkC5B,CAlC4B;;AAAA,6CAmCrB,IAnCqB;;AAAA,6CAoCzB,KApCyB;;AAAA,yCAqC5B,IArC4B;;AAAA,6CAsCpB,IAtCoB;;AAAA,mDAuCJ,EAvCI;;AAAA,6CAwCxB,IAxCwB;;AAAA,kDAyCnB,IAzCmB;;AAAA,kDA0CnB,IA1CmB;;AAAA,6CA4CxB,IA5CwB;;AAAA,4CA6CzB,IA7CyB;AAAA;;AA+C9CqB,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL;AACA,eAAKC,eAAL,CAAqBC,MAArB,GAA8B,KAA9B;AACA,eAAKC,iBAAL,CAAuBC,EAAvB,CAA0BxB,IAAI,CAACyB,SAAL,CAAeC,WAAzC,EAAsD,KAAKC,iBAA3D,EAA8E,IAA9E;AACA,eAAKJ,iBAAL,CAAuBC,EAAvB,CAA0BxB,IAAI,CAACyB,SAAL,CAAeG,SAAzC,EAAoD,KAAKC,iBAAzD,EAA4E,IAA5E;AACA,eAAKN,iBAAL,CAAuBC,EAAvB,CAA0BxB,IAAI,CAACyB,SAAL,CAAeK,YAAzC,EAAuD,KAAKD,iBAA5D,EAA+E,IAA/E;AACH;;AAESE,QAAAA,SAAS,GAAS;AACxB,eAAKC,eAAL,CAAqBC,MAArB,GAA8B,CAA9B;AACH;;AAESC,QAAAA,QAAQ,GAAS;AACvB,eAAKC,SAAL,GAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAjB;AACA,eAAKC,cAAL,GAAsB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAtB;AACA,eAAKC,cAAL,GAAsB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAtB;AACA,eAAKC,SAAL,GAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAjB;AACA,eAAKC,QAAL,GAAgB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAhB;AAEA;AAAA;AAAA,oCAASf,EAAT,CAAY;AAAA;AAAA,wCAAWgB,UAAvB,EAAmC,KAAKC,YAAxC,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASjB,EAAT,CAAY;AAAA;AAAA,wCAAWkB,aAAvB,EAAsC,KAAKC,eAA3C,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAASnB,EAAT,CAAY;AAAA;AAAA,wCAAWoB,kBAAvB,EAA2C,KAAKC,gBAAhD,EAAkE,IAAlE;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKC,SAAL,GAAiB,IAAjB;AACA,eAAKC,SAAL,GAAiB,IAAjB;AACH;;AAES9B,QAAAA,QAAQ,GAAS;AACvB,eAAK,IAAI+B,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKC,WAAjC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,gBAAIE,IAAU,GAAGjD,WAAW,CAAC,KAAKkD,aAAN,CAA5B;AACAD,YAAAA,IAAI,CAACE,MAAL,GAAc,KAAKC,YAAnB;AACA,gBAAIC,IAA0B,GAAGJ,IAAI,CAACK,YAAL;AAAA;AAAA,6DAAjC;AACAD,YAAAA,IAAI,CAACE,KAAL,GAAaR,CAAb;;AACA,iBAAKnB,eAAL,CAAqB4B,IAArB,CAA0BH,IAA1B;AACH;AACJ;;AAES9B,QAAAA,iBAAiB,GAAS;AAChC,cAAI,KAAKqB,KAAL,IAAc,KAAKa,aAAL,CAAmBC,MAAnB,IAA6B,GAA/C,EAAoD;AAChD,iBAAKzC,eAAL,CAAqBC,MAArB,GAA8B,IAA9B;AACH;AACJ;;AAESO,QAAAA,iBAAiB,GAAS;AAChC,eAAKR,eAAL,CAAqBC,MAArB,GAA8B,KAA9B;AACH;;AAESyC,QAAAA,aAAa,GAAS;AAC5B,eAAK,IAAIZ,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKC,WAAjC,EAA8CD,CAAC,EAA/C,EAAmD;AAC/C,iBAAKhB,SAAL,CAAegB,CAAf,IAAoB,CAApB;AACA,iBAAKf,cAAL,CAAoBe,CAApB,IAAyB,CAAzB;AACH;AACJ;;AAESV,QAAAA,YAAY,CAACuB,QAAD,EAA2B;AAC7C,cAAIA,QAAQ,CAACC,MAAT,IAAmB,KAAKC,OAAxB,IAAmCF,QAAQ,CAACG,KAAT,IAAkB,KAAKC,MAA9D,EAAsE;AAClE,iBAAKC,OAAL,CAAa,KAAKH,OAAlB,EAA2B,KAAKE,MAAhC;AACH;AACJ;;AAESvB,QAAAA,gBAAgB,CAACoB,MAAD,EAAuB;AAC7C,cAAI,KAAKC,OAAL,IAAgBD,MAApB,EAA4B;AACxB,iBAAKI,OAAL,CAAa,KAAKH,OAAlB,EAA2B,KAAKE,MAAhC;AACH;AACJ;;AAESzB,QAAAA,eAAe,CAAC2B,OAAD,EAAeC,OAAf,EAA6BC,QAA7B,EAAkD;AACvE;AAAA;AAAA,0CAAYC,WAAZ,GAA0BC,cAA1B,CAAyC,KAAKxB,SAAL,CAAee,MAAxD,EAAgEM,OAAO,CAACI,EAAxE,EAA4E,KAAKP,MAAjF,EAAyFQ,MAAM,CAACJ,QAAD,CAA/F,EAA2G,KAAKtB,SAAhH;AACH;;AAES2B,QAAAA,UAAU,GAAS;AACzB,cAAIpB,IAA0B,GAAG,IAAjC;AACA,cAAIqB,WAAwB,GAAG,IAA/B;AACA,cAAIC,UAAyB,GAAG,IAAhC;AACA,cAAIC,QAAiB,GAAG,KAAxB;AACA,cAAIC,SAAiB,GAAG,CAAxB;AACA,cAAIC,UAAkB,GAAG,CAAzB;AACA,cAAIC,eAAuB,GAAG,CAA9B;AACA,eAAKpB,aAAL;;AACA,cAAI,KAAKqB,SAAT,EAAoB;AAChB,iBAAK,IAAIjC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKnB,eAAL,CAAqBC,MAAjD,EAAyDkB,CAAC,EAA1D,EAA8D;AAC1DM,cAAAA,IAAI,GAAG,KAAKzB,eAAL,CAAqBmB,CAArB,CAAP;AACA2B,cAAAA,WAAW,GAAG,IAAd;AACAE,cAAAA,QAAQ,GAAG,IAAX;;AACA,kBAAI7B,CAAC,IAAI,CAAT,EAAY;AACR;AACA6B,gBAAAA,QAAQ,GAAG,KAAK/B,SAAL,CAAeoC,WAAf,IAA8B,KAAKjB,MAA9C;AACH;;AACD,kBAAI,KAAKpB,KAAL,IAAc,KAAKA,KAAL,CAAWsC,QAAX,CAAoBnC,CAApB,IAAyB,CAA3C,EAA8C;AAC1C2B,gBAAAA,WAAW,GAAG;AAAA;AAAA,sDAAeL,WAAf,GAA6Bc,KAA7B,CAAmCC,YAAnC,CAAgD,KAAKxC,KAAL,CAAWsC,QAAX,CAAoBnC,CAApB,CAAhD,CAAd;AACA4B,gBAAAA,UAAU,GAAG;AAAA;AAAA,sDAAeN,WAAf,GAA6Bc,KAA7B,CAAmCE,aAAnC,CAAiDX,WAAW,CAACY,KAA7D,CAAb;AACAT,gBAAAA,SAAS,IAAIF,UAAU,CAACY,IAAxB;AACA,qBAAKxD,SAAL,CAAegB,CAAf,IAAoB,KAAKH,KAAL,CAAW4C,QAAX,CAAoBzC,CAApB,CAApB;AACA,qBAAKf,cAAL,CAAoBe,CAApB,IAAyB2B,WAAW,CAACe,KAAZ,GAAoB,GAApB,GAA0B,KAAK5C,SAAL,CAAeiC,UAAlE;AACAA,gBAAAA,UAAU,IAAI,KAAK/C,SAAL,CAAegB,CAAf,CAAd;AACAgC,gBAAAA,eAAe,IAAI,KAAK/C,cAAL,CAAoBe,CAApB,CAAnB;AACA,qBAAKb,SAAL,CAAea,CAAf,IAAoB,KAAKH,KAAL,CAAW8C,QAAX,CAAoB3C,CAApB,CAApB;AACA,qBAAKZ,QAAL,CAAcY,CAAd,IAAmB,KAAKH,KAAL,CAAW+C,OAAX,CAAmB5C,CAAnB,CAAnB;AAEH;;AACD,kBAAI6B,QAAQ,IAAI,KAAZ,IAAqBF,WAAW,IAAI,IAAxC,EAA8C;AAC1C,qBAAKkB,QAAL,CAAc7C,CAAd,EAAiB7B,MAAjB,GAA0B,IAA1B;AACA,qBAAK2E,QAAL,CAAc9C,CAAd,EAAiB+C,IAAjB,CAAsB5E,MAAtB,GAA+B,KAA/B;AACA,qBAAK6E,OAAL,CAAahD,CAAb,EAAgB+C,IAAhB,CAAqB5E,MAArB,GAA8B,KAA9B;AACH,eAJD,MAIO;AACH,qBAAK0E,QAAL,CAAc7C,CAAd,EAAiB7B,MAAjB,GAA0B,KAA1B;AACA,qBAAK2E,QAAL,CAAc9C,CAAd,EAAiB+C,IAAjB,CAAsB5E,MAAtB,GAA+B,IAA/B;AACA,qBAAK6E,OAAL,CAAahD,CAAb,EAAgB+C,IAAhB,CAAqB5E,MAArB,GAA8B,IAA9B;AAEA,oBAAI8E,UAAkB,GAAG,KAAKhE,cAAL,CAAoBe,CAApB,IAAyB,KAAKhB,SAAL,CAAegB,CAAf,CAAlD;;AACA,oBAAG,KAAKH,KAAL,IAAc,KAAKA,KAAL,CAAWqD,GAAX,IAAkB;AAAA;AAAA,wCAAQC,SAA3C,EAAqD;AACjD,sBAAIC,GAAG,GAAG,KAAKhE,QAAL,CAAcY,CAAd,KAAoB,CAA9B;AACA,uBAAKgD,OAAL,CAAahD,CAAb,EAAgBqD,OAAhB,GAA0BD,GAA1B;AACA,uBAAKN,QAAL,CAAc9C,CAAd,EAAiBW,MAAjB,GAA0B,GAA1B;AACA,uBAAKqC,OAAL,CAAahD,CAAb,EAAgBsD,QAAhB,GAA2B,CAA3B;AACA,uBAAKR,QAAL,CAAc9C,CAAd,EAAiBqD,OAAjB,GAA2BD,GAA3B;AACH,iBAND,MAMM,IAAG,KAAKvD,KAAL,IAAc,KAAKA,KAAL,CAAWqD,GAAX,GAAiB;AAAA;AAAA,wCAAQK,IAAvC,IAA+CN,UAAU,IAAI,CAAhE,EAAmE;AACrE;AACA,uBAAKH,QAAL,CAAc9C,CAAd,EAAiBW,MAAjB,GAA0B,GAA1B;AACA,uBAAKqC,OAAL,CAAahD,CAAb,EAAgBsD,QAAhB,GAA2B,CAA3B;AACA,uBAAKR,QAAL,CAAc9C,CAAd,EAAiBqD,OAAjB,GAA2B,KAA3B;AACA,uBAAKL,OAAL,CAAahD,CAAb,EAAgBqD,OAAhB,GAA0B,KAA1B;AACA,uBAAKG,uBAAL,CAA6BxD,CAA7B,EAAgC,CAAhC;AACH,iBAPK,MAOC;AACH,uBAAK8C,QAAL,CAAc9C,CAAd,EAAiBqD,OAAjB,GAA2B,IAA3B;AACA,uBAAKL,OAAL,CAAahD,CAAb,EAAgBqD,OAAhB,GAA0B,IAA1B;AACA,sBAAII,QAAgB,GAAG,CAAvB;;AACA,sBAAI,KAAKX,QAAL,CAAc9C,CAAd,EAAiBW,MAAjB,IAA2B,EAA/B,EAAmC;AAC/B8C,oBAAAA,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASC,QAAQ,CAAC,KAAKf,QAAL,CAAc9C,CAAd,EAAiBW,MAAlB,CAAjB,EAA4CsC,UAA5C,CAAZ,CAAX;AACH;;AACD,uBAAKO,uBAAL,CAA6BxD,CAA7B,EAAgCyD,QAAhC;AACH;AAEJ;;AACDnD,cAAAA,IAAI,CAACY,OAAL,CAAa,KAAKH,OAAlB,EAA2B,KAAKE,MAAhC,EAAwCU,WAAxC,EAAqD,KAAK3C,SAAL,CAAegB,CAAf,CAArD,EAAwE,KAAKf,cAAL,CAAoBe,CAApB,CAAxE,EAAgG,KAAKZ,QAAL,CAAcY,CAAd,CAAhG,EAAkH,KAAKb,SAAL,CAAea,CAAf,CAAlH,EAAqI6B,QAArI;AACH;AACJ;;AACD,eAAKiC,OAAL,CAAanD,MAAb,GAAsB,OAAO,KAAKM,MAAlC;AACA,eAAK8C,SAAL,CAAepD,MAAf,GAAwBmB,SAAS,GAAG,GAAZ,GAAkB;AAAA;AAAA,4CAAaR,WAAb,GAA2Bc,KAA3B,CAAiC4B,aAAjC,CAA+C,KAAKjD,OAApD,CAA1C;AACA,eAAKkD,eAAL,CAAqBtD,MAArB,GAA8BoB,UAAU,GAAG,GAAb,GAAmBC,eAAjD;AACA,eAAKtB,aAAL,CAAmBC,MAAnB,GAA4B,GAA5B;;AACA,cAAI,KAAKd,KAAT,EAAgB;AACZ,gBAAIsC,QAAuB,GAAG;AAAA;AAAA,4CAAYb,WAAZ,GAA0B4C,eAA1B,CAA0C,KAAKrE,KAA/C,CAA9B;AACA,gBAAIsE,KAAa,GAAG;AAAA;AAAA,4CAAY7C,WAAZ,GAA0B8C,YAA1B,CAAuCjC,QAAvC,CAApB;AACA,iBAAKkC,UAAL,CAAgB1D,MAAhB,GAAyBwD,KAAK,CAACG,OAAN,CAAc,CAAd,CAAzB;AAEA,gBAAIC,OAAe,GAAG;AAAA;AAAA,4CAAYjD,WAAZ,GAA0BkD,cAA1B,CAAyCrC,QAAzC,CAAtB;AACA,iBAAKsC,YAAL,CAAkB9D,MAAlB,GAA2B4D,OAAO,CAACD,OAAR,CAAgB,CAAhB,CAA3B;AAEA,gBAAII,IAAY,GAAG;AAAA;AAAA,4CAAYpD,WAAZ,GAA0BqD,WAA1B,CAAsCxC,QAAtC,CAAnB;;AACA,gBAAIuC,IAAI,GAAG,CAAX,EAAc;AACV,kBAAIA,IAAI,IAAI;AAAA;AAAA,sDAAgBE,GAAxB,IAA+B,KAAK9E,SAAL,CAAe+E,GAAf,GAAqB,CAAxD,EAA2D;AACvD,qBAAKC,gBAAL,CAAsBnE,MAAtB,GAA+B,WAAW,KAAKb,SAAL,CAAe+E,GAAzD;AACA,qBAAKnE,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B;AACH,eAHD,MAGO,IAAI+D,IAAI,IAAI;AAAA;AAAA,sDAAgBK,GAAxB,IAA+B,KAAKjF,SAAL,CAAe+E,GAAf,GAAqB,CAAxD,EAA2D;AAC9D,qBAAKC,gBAAL,CAAsBnE,MAAtB,GAA+B,WAAW,KAAKb,SAAL,CAAekF,GAAzD;AACA,qBAAKtE,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B;AACH,eAHM,MAGA,IAAI+D,IAAI,IAAI;AAAA;AAAA,sDAAgBO,GAAxB,IAA+B,KAAKnF,SAAL,CAAe+E,GAAf,GAAqB,CAAxD,EAA2D;AAC9D,qBAAKC,gBAAL,CAAsBnE,MAAtB,GAA+B,WAAW,KAAKb,SAAL,CAAeoF,GAAzD;AACA,qBAAKxE,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B;AACH,eAHM,MAGA,IAAI+D,IAAI,IAAI;AAAA;AAAA,sDAAgBS,GAAxB,IAA+B,KAAKrF,SAAL,CAAe+E,GAAf,GAAqB,CAAxD,EAA2D;AAC9D,qBAAKC,gBAAL,CAAsBnE,MAAtB,GAA+B,WAAW,KAAKb,SAAL,CAAesF,GAAzD;AACA,qBAAK1E,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B;AACH,eAHM,MAGA,IAAI+D,IAAI,IAAI;AAAA;AAAA,sDAAgBW,EAAxB,IAA8B,KAAKvF,SAAL,CAAe+E,GAAf,GAAqB,CAAvD,EAA0D;AAC7D,qBAAKC,gBAAL,CAAsBnE,MAAtB,GAA+B,WAAW,KAAKb,SAAL,CAAewF,EAAzD;AACA,qBAAK5E,aAAL,CAAmBC,MAAnB,GAA4B,IAA5B;AACH;AACJ;AACJ,WA3BD,MA2BO;AACH,iBAAK0D,UAAL,CAAgB1D,MAAhB,GAAyB,GAAzB;AACH;;AAED,eAAK4E,aAAL;AACH;;AAES/B,QAAAA,uBAAuB,CAAChD,KAAD,EAAgBgF,GAAW,GAAG,CAA9B,EAAuC;AACpE,cAAIC,MAAc,GAAG,KAAKxG,cAAL,CAAoBuB,KAApB,IAA6B,KAAKxB,SAAL,CAAewB,KAAf,CAAlD;AACAgF,UAAAA,GAAG,GAAG9B,IAAI,CAACE,GAAL,CAAS4B,GAAT,EAAcC,MAAd,CAAN;AACA,cAAIC,OAAe,GAAGF,GAAG,GAAGC,MAA5B;AACA,eAAK3C,QAAL,CAActC,KAAd,EAAqBG,MAArB,GAA8B6E,GAAG,GAAG,EAApC;AACA,eAAKxC,OAAL,CAAaxC,KAAb,EAAoB8C,QAApB,GAA+BoC,OAA/B;AACA,eAAKxG,cAAL,CAAoBsB,KAApB,IAA6BgF,GAA7B;AACH;;AAESG,QAAAA,oBAAoB,GAAW;AACrC,cAAIC,QAAgB,GAAG,CAAvB;;AACA,eAAK,IAAI5F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKd,cAAL,CAAoBJ,MAAxC,EAAgDkB,CAAC,EAAjD,EAAqD;AACjD4F,YAAAA,QAAQ,IAAI,KAAK1G,cAAL,CAAoBc,CAApB,CAAZ;AACH;;AACD,iBAAO4F,QAAP;AACH;;AAESL,QAAAA,aAAa,GAAS;AAC5B,cAAIK,QAAgB,GAAG,KAAKD,oBAAL,EAAvB;;AACA,cAAIC,QAAQ,GAAG,CAAf,EAAkB;AACd,gBAAIC,SAAS,GAAG;AAAA;AAAA,8CAAavE,WAAb,GAA2Bc,KAA3B,CAAiC0D,cAAjC,EAAhB;AACA,gBAAIC,QAAmB,GAAG;AAAA;AAAA,8CAAazE,WAAb,GAA2Bc,KAA3B,CAAiC4D,oBAAjC,EAA1B;AACA,gBAAIC,GAAW,GAAG,UAAU,KAAV,GAAkBL,QAAQ,GAAGG,QAAQ,CAACG,SAAtC,GAAkD,GAAlD,GAAwDL,SAAS,CAACM,IAApF;AACAF,YAAAA,GAAG,IAAI,SAASL,QAAQ,GAAGG,QAAQ,CAACK,SAA7B,GAAyC,GAAzC,GAA+CP,SAAS,CAACQ,IAAhE;AACAJ,YAAAA,GAAG,IAAI,SAASL,QAAQ,GAAGG,QAAQ,CAACO,SAA7B,GAAyC,GAAzC,GAA+CT,SAAS,CAACU,IAAhE;AACAN,YAAAA,GAAG,IAAI,SAASL,QAAQ,GAAGG,QAAQ,CAACS,UAA7B,GAA0C,GAA1C,GAAgDX,SAAS,CAACY,KAAjE;AACA,iBAAKC,YAAL,CAAkB/F,MAAlB,GAA2BsF,GAA3B;AACH,WARD,MAQO;AACH,iBAAKS,YAAL,CAAkB/F,MAAlB,GAA2B,EAA3B;AACH;AACJ;;AAESgG,QAAAA,eAAe,CAACC,OAAD,EAAyB;AAC9C,cAAIpG,KAAa,GAAG,KAAKsC,QAAL,CAAc+D,OAAd,CAAsBD,OAAtB,CAApB;;AACA,cAAIpG,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAKgD,uBAAL,CAA6BhD,KAA7B,EAAoCqD,QAAQ,CAAC,KAAKf,QAAL,CAActC,KAAd,EAAqBG,MAAtB,CAA5C;AACA,iBAAK4E,aAAL;AACH;AACJ;;AAESuB,QAAAA,cAAc,CAACC,MAAD,EAAuB;AAC3C,cAAIvG,KAAa,GAAG,KAAKwC,OAAL,CAAa6D,OAAb,CAAqBE,MAArB,CAApB;;AACA,cAAIvG,KAAK,IAAI,CAAb,EAAgB;AACZ,gBAAIiF,MAAc,GAAG,KAAKxG,cAAL,CAAoBuB,KAApB,IAA6B,KAAKxB,SAAL,CAAewB,KAAf,CAAlD;AACA,iBAAKgD,uBAAL,CAA6BhD,KAA7B,EAAoCkD,IAAI,CAACsD,KAAL,CAAW,KAAKhE,OAAL,CAAaxC,KAAb,EAAoB8C,QAApB,GAA+BmC,MAA1C,CAApC;AACA,iBAAKF,aAAL;AACH;AACJ;;AAES0B,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKtH,KAAL,IAAc,KAAKA,KAAL,CAAWqD,GAAX,IAAkB;AAAA;AAAA,kCAAQK,IAAxC,IAAgD,KAAK1D,KAAL,CAAWqD,GAAX,IAAkB;AAAA;AAAA,kCAAQC,SAA9E,EAAyF;AACrF,iBAAK,IAAInD,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKf,cAAL,CAAoBH,MAAhD,EAAwDkB,CAAC,EAAzD,EAA6D;AACzD,kBAAG,KAAKZ,QAAL,CAAcY,CAAd,IAAmB,CAAtB,EAAwB;AACpB;AACA,qBAAKwD,uBAAL,CAA6BxD,CAA7B,EAAgC,CAAhC;AACH,eAHD,MAGK;AACD,oBAAIyF,MAAc,GAAG,KAAKxG,cAAL,CAAoBe,CAApB,IAAyB,KAAKhB,SAAL,CAAegB,CAAf,CAA9C;;AACA,oBAAIyF,MAAM,GAAG,CAAb,EAAgB;AACZ,uBAAKjC,uBAAL,CAA6BxD,CAA7B,EAAgCyF,MAAhC;AACH;AACJ;AACJ;;AACD,iBAAKF,aAAL;AACH;AAEJ;;AAES6B,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKtH,KAAL,IAAc,KAAKA,KAAL,CAAWqD,GAAX,IAAkB;AAAA;AAAA,kCAAQK,IAAxC,IAAgD,KAAK1D,KAAL,CAAWqD,GAAX,IAAkB;AAAA;AAAA,kCAAQC,SAA9E,EAAyF;AACrF,gBAAIyC,QAAgB,GAAG,KAAKD,oBAAL,EAAvB;;AACA,gBAAIC,QAAQ,GAAG,CAAf,EAAkB;AACd;AAAA;AAAA,8CAAYtE,WAAZ,GAA0B+F,gBAA1B,CAA2C,KAAKxH,KAAL,CAAW2B,EAAtD,EAA0D,KAAKtC,cAA/D,EAA+E,IAA/E;AACH;AACJ;AACJ;;AAESoI,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;AACA,eAAKjI,cAAL,GAAsB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAtB;;AACA,cAAI,KAAK+B,MAAL,IAAe,CAAnB,EAAsB;AAClB;AACA,iBAAKC,OAAL,CAAa,KAAKH,OAAlB,EAA2B,KAAKjB,SAAL,CAAeyH,OAA1C;AACH,WAHD,MAGO;AACH,iBAAKrG,OAAL,CAAa,KAAKH,OAAlB,EAA2B,KAAKE,MAAL,GAAc,CAAzC;AACH;AACJ;;AAESuG,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaN,QAAb,CAAsBC,SAAtB;AACA,eAAKjI,cAAL,GAAsB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAtB;;AACA,cAAI,KAAK+B,MAAL,IAAe,KAAKnB,SAAL,CAAeyH,OAAlC,EAA2C;AACvC;AACA,iBAAKrG,OAAL,CAAa,KAAKH,OAAlB,EAA2B,CAA3B;AACH,WAHD,MAGO;AACH,iBAAKG,OAAL,CAAa,KAAKH,OAAlB,EAA2B,KAAKE,MAAL,GAAc,CAAzC;AACH;AACJ;;AAESwG,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaP,QAAb,CAAsBC,SAAtB;AACA,eAAKpE,IAAL,CAAU5E,MAAV,GAAmB,KAAnB;AACH;;AAEM+C,QAAAA,OAAO,CAACJ,MAAD,EAAiBE,KAAa,GAAG,CAAjC,EAA0C;AACpD,eAAK9B,cAAL,GAAsB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAtB;AACA,eAAK6B,OAAL,GAAeD,MAAf;AACA,eAAKG,MAAL,GAAcD,KAAd;AACA,eAAKjB,SAAL,GAAiB;AAAA;AAAA,wCAAWuB,WAAX,GAAyBoG,SAAzB,CAAmCC,aAAnC,CAAiD,KAAK5G,OAAtD,CAAjB;AACA,eAAKlB,KAAL,GAAa;AAAA;AAAA,0CAAYyB,WAAZ,GAA0Bc,KAA1B,CAAgCwF,cAAhC,CAA+C5G,KAA/C,EAAsDF,MAAtD,CAAb;AACA,eAAKhB,SAAL,GAAiB;AAAA;AAAA,4CAAawB,WAAb,GAA2Bc,KAA3B,CAAiCyF,iBAAjC,CAAmD/G,MAAnD,CAAjB;AACA,eAAKmB,SAAL,GAAiB,KAAKnC,SAAL,CAAeyH,OAAf,IAA0B,KAAKtG,MAAhD;AACA,cAAI6G,QAA+B,GAAG;AAAA;AAAA,4CAAaxG,WAAb,GAA2Bc,KAA3B,CAAiC2F,cAAjC,CAAgD,KAAKhH,OAArD,CAAtC;;AACA,cAAI+G,QAAQ,IAAI,IAAhB,EAAsB;AAClB;AAAA;AAAA,8CAAaxG,WAAb,GAA2B0G,iBAA3B,CAA6C,KAAKjH,OAAlD;AACH;;AACD,eAAKW,UAAL;AACH;;AAxVuD,O;;;;;iBAEvC,I;;;;;;;iBAEE,I;;;;;;;iBAEM,I;;;;;;;iBAEL,I;;;;;;;iBAEE,I;;;;;;;iBAEC,I;;;;;;;iBAEG,I;;;;;;;iBAEA,I;;;;;;;iBAEF,I;;;;;;;iBAEF,I;;;;;;;iBAED,I;;;;;;;iBAEG,I;;;;;;;iBAEF,E;;;;;;;iBAEF,E;;;;;;;iBAED,E", "sourcesContent": ["import { _decorator, Component, Label, Node, Prefab, EditBox, Slider, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { ArmyCmd, ArmyData } from \"../../general/ArmyProxy\";\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport { GeneralCampType, GeneralConfig, GeneralData } from \"../../general/GeneralProxy\";\nimport MapUICommand from \"./MapUICommand\";\nimport MapCommand from \"../MapCommand\";\nimport { MapCityData } from \"../MapCityProxy\";\nimport { CityAddition, CityAdditionType, Facility } from \"./MapUIProxy\";\nimport CityGeneralItemLogic from \"./CityGeneralItemLogic\";\nimport LoginCommand from \"../../login/LoginCommand\";\nimport { Conscript } from \"../../config/Basci\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('CityArmySettingLogic')\nexport default class CityArmySettingLogic extends Component {\n    @property(Label)\n    labelId: Label = null;\n    @property(Label)\n    labelCost: Label = null;\n    @property(Label)\n    labelSoldierCnt: Label = null;\n    @property(Label)\n    labelSpeed: Label = null;\n    @property(Label)\n    labelAtkCity: Label = null;\n    @property(Label)\n    labelAddition: Label = null;\n    @property(Label)\n    labelAdditionTip: Label = null;\n    @property(Node)\n    additionTouchNode: Node = null;\n    @property(Node)\n    additionTipNode: Node = null;\n    @property(Label)\n    labelResCost: Label = null;\n    @property(Node)\n    generalLayer: Node = null;\n    @property(Prefab)\n    generalPrefab: Prefab = null;\n    @property([EditBox])\n    editBoxs: EditBox[] = [];\n    @property([Slider])\n    sliders: Slider[] = [];\n    @property([Node])\n    tipNodes: Node[] = [];\n\n    protected _generalCnt: number = 3;\n    protected _order: number = 0;\n    protected _cityId: number = 0;\n    protected _cityData: MapCityData = null;\n    protected _isUnlock: boolean = false;\n    protected _data: ArmyData = null;\n    protected _addition: CityAddition = null;\n    protected _gengeralLogics: CityGeneralItemLogic[] = [];\n    protected _soldiers: number[] = null;\n    protected _totalSoldiers: number[] = null;\n    protected _curConscripts: number[] = null;//当前征兵数\n\n    protected _conTimes: number[] = null;\n    protected _conCnts: number[] = null;\n\n    protected onLoad(): void {\n        this.initView();\n        this.additionTipNode.active = false;\n        this.additionTouchNode.on(Node.EventType.TOUCH_START, this.onShowAdditionTip, this);\n        this.additionTouchNode.on(Node.EventType.TOUCH_END, this.onHideAdditionTip, this);\n        this.additionTouchNode.on(Node.EventType.TOUCH_CANCEL, this.onHideAdditionTip, this);\n    }\n\n    protected onDestroy(): void {\n        this._gengeralLogics.length = 0;\n    }\n\n    protected onEnable(): void {\n        this._soldiers = [0, 0, 0];\n        this._totalSoldiers = [0, 0, 0];\n        this._curConscripts = [0, 0, 0];\n        this._conTimes = [0, 0, 0];\n        this._conCnts = [0, 0, 0];\n\n        EventMgr.on(LogicEvent.updateArmy, this.onUpdateArmy, this);\n        EventMgr.on(LogicEvent.chosedGeneral, this.onChooseGeneral, this);\n        EventMgr.on(LogicEvent.updateCityAddition, this.onUpdateAddition, this);\n    }\n\n    protected onDisable(): void {\n        EventMgr.targetOff(this);\n        this._data = null;\n        this._addition = null;\n        this._cityData = null;\n    }\n\n    protected initView(): void {\n        for (let i: number = 0; i < this._generalCnt; i++) {\n            let item: Node = instantiate(this.generalPrefab);\n            item.parent = this.generalLayer;\n            let comp: CityGeneralItemLogic = item.getComponent(CityGeneralItemLogic);\n            comp.index = i;\n            this._gengeralLogics.push(comp);\n        }\n    }\n\n    protected onShowAdditionTip(): void {\n        if (this._data && this.labelAddition.string != \"无\") {\n            this.additionTipNode.active = true;\n        }\n    }\n\n    protected onHideAdditionTip(): void {\n        this.additionTipNode.active = false;\n    }\n\n    protected clearSoldiers(): void {\n        for (let i: number = 0; i < this._generalCnt; i++) {\n            this._soldiers[i] = 0;\n            this._totalSoldiers[i] = 0;\n        }\n    }\n\n    protected onUpdateArmy(armyData: ArmyData): void {\n        if (armyData.cityId == this._cityId && armyData.order == this._order) {\n            this.setData(this._cityId, this._order);\n        }\n    }\n\n    protected onUpdateAddition(cityId: number): void {\n        if (this._cityId == cityId) {\n            this.setData(this._cityId, this._order);\n        }\n    }\n\n    protected onChooseGeneral(cfgData: any, curData: any, position: any): void {\n        ArmyCommand.getInstance().generalDispose(this._cityData.cityId, curData.id, this._order, Number(position), this._cityData);\n    }\n\n    protected updateView(): void {\n        let comp: CityGeneralItemLogic = null;\n        let generalData: GeneralData = null;\n        let generalCfg: GeneralConfig = null;\n        let isUnlock: boolean = false;\n        let totalCost: number = 0;\n        let soldierCnt: number = 0;\n        let totalSoldierCnt: number = 0;\n        this.clearSoldiers();\n        if (this._isUnlock) {\n            for (let i: number = 0; i < this._gengeralLogics.length; i++) {\n                comp = this._gengeralLogics[i];\n                generalData = null;\n                isUnlock = true;\n                if (i == 2) {\n                    //只有第二个副将才需要判断是否解锁\n                    isUnlock = this._addition.vanguardCnt >= this._order;\n                }\n                if (this._data && this._data.generals[i] > 0) {\n                    generalData = GeneralCommand.getInstance().proxy.getMyGeneral(this._data.generals[i]);\n                    generalCfg = GeneralCommand.getInstance().proxy.getGeneralCfg(generalData.cfgId);\n                    totalCost += generalCfg.cost;\n                    this._soldiers[i] = this._data.soldiers[i];\n                    this._totalSoldiers[i] = generalData.level * 100 + this._addition.soldierCnt;\n                    soldierCnt += this._soldiers[i];\n                    totalSoldierCnt += this._totalSoldiers[i];\n                    this._conTimes[i] = this._data.conTimes[i];\n                    this._conCnts[i] = this._data.conCnts[i];\n\n                }\n                if (isUnlock == false || generalData == null) {\n                    this.tipNodes[i].active = true;\n                    this.editBoxs[i].node.active = false;\n                    this.sliders[i].node.active = false;\n                } else {\n                    this.tipNodes[i].active = false;\n                    this.editBoxs[i].node.active = true;\n                    this.sliders[i].node.active = true;\n\n                    let totalValue: number = this._totalSoldiers[i] - this._soldiers[i];\n                    if(this._data && this._data.cmd == ArmyCmd.Conscript){\n                        var can = this._conCnts[i] == 0\n                        this.sliders[i].enabled = can;\n                        this.editBoxs[i].string = \"0\";\n                        this.sliders[i].progress = 0;\n                        this.editBoxs[i].enabled = can;\n                    }else if(this._data && this._data.cmd > ArmyCmd.Idle || totalValue <= 0) {\n                        //不可征兵\n                        this.editBoxs[i].string = \"0\";\n                        this.sliders[i].progress = 0;\n                        this.editBoxs[i].enabled = false;\n                        this.sliders[i].enabled = false;\n                        this.setCurConscriptForIndex(i, 0);\n                    } else {\n                        this.editBoxs[i].enabled = true;\n                        this.sliders[i].enabled = true;\n                        let curValue: number = 0;\n                        if (this.editBoxs[i].string != \"\") {\n                            curValue = Math.max(0, Math.min(parseInt(this.editBoxs[i].string), totalValue));\n                        }\n                        this.setCurConscriptForIndex(i, curValue);\n                    }\n\n                }\n                comp.setData(this._cityId, this._order, generalData, this._soldiers[i], this._totalSoldiers[i], this._conCnts[i], this._conTimes[i], isUnlock);\n            }\n        }\n        this.labelId.string = \"部队\" + this._order;\n        this.labelCost.string = totalCost + \"/\" + MapUICommand.getInstance().proxy.getMyCityCost(this._cityId);\n        this.labelSoldierCnt.string = soldierCnt + \"/\" + totalSoldierCnt;\n        this.labelAddition.string = \"无\";\n        if (this._data) {\n            let generals: GeneralData[] = ArmyCommand.getInstance().getArmyGenerals(this._data);\n            let speed: number = ArmyCommand.getInstance().getArmySpeed(generals);\n            this.labelSpeed.string = speed.toFixed(2);\n\n            let destroy: number = ArmyCommand.getInstance().getArmyDestroy(generals);\n            this.labelAtkCity.string = destroy.toFixed(2);\n\n            let camp: number = ArmyCommand.getInstance().getArmyCamp(generals);\n            if (camp > 0) {\n                if (camp == GeneralCampType.Han && this._addition.han > 0) {\n                    this.labelAdditionTip.string = \"汉阵营加成：\" + this._addition.han;\n                    this.labelAddition.string = \"阵营\";\n                } else if (camp == GeneralCampType.Qun && this._addition.han > 0) {\n                    this.labelAdditionTip.string = \"群阵营加成：\" + this._addition.qun;\n                    this.labelAddition.string = \"阵营\";\n                } else if (camp == GeneralCampType.Wei && this._addition.han > 0) {\n                    this.labelAdditionTip.string = \"魏阵营加成：\" + this._addition.wei;\n                    this.labelAddition.string = \"阵营\";\n                } else if (camp == GeneralCampType.Shu && this._addition.han > 0) {\n                    this.labelAdditionTip.string = \"蜀阵营加成：\" + this._addition.shu;\n                    this.labelAddition.string = \"阵营\";\n                } else if (camp == GeneralCampType.Wu && this._addition.han > 0) {\n                    this.labelAdditionTip.string = \"吴阵营加成：\" + this._addition.wu;\n                    this.labelAddition.string = \"阵营\";\n                }\n            }\n        } else {\n            this.labelSpeed.string = \"0\";\n        }\n\n        this.updateResCost();\n    }\n\n    protected setCurConscriptForIndex(index: number, cnt: number = 0): void {\n        let maxCnt: number = this._totalSoldiers[index] - this._soldiers[index];\n        cnt = Math.min(cnt, maxCnt);\n        let percent: number = cnt / maxCnt;\n        this.editBoxs[index].string = cnt + \"\";\n        this.sliders[index].progress = percent;\n        this._curConscripts[index] = cnt;\n    }\n\n    protected getTotalConscriptCnt(): number {\n        let totalCnt: number = 0;\n        for (let i = 0; i < this._curConscripts.length; i++) {\n            totalCnt += this._curConscripts[i];\n        }\n        return totalCnt;\n    }\n\n    protected updateResCost(): void {\n        let totalCnt: number = this.getTotalConscriptCnt();\n        if (totalCnt > 0) {\n            var myRoleRes = LoginCommand.getInstance().proxy.getRoleResData();\n            var baseCost: Conscript = MapUICommand.getInstance().proxy.getConscriptBaseCost();\n            let str: string = \"消耗:  \" + \"金币:\" + totalCnt * baseCost.cost_gold + \"/\" + myRoleRes.gold;\n            str += \" 木材:\" + totalCnt * baseCost.cost_wood + \"/\" + myRoleRes.wood;\n            str += \" 金属:\" + totalCnt * baseCost.cost_iron + \"/\" + myRoleRes.iron;\n            str += \" 谷物:\" + totalCnt * baseCost.cost_grain + \"/\" + myRoleRes.grain;\n            this.labelResCost.string = str;\n        } else {\n            this.labelResCost.string = \"\";\n        }\n    }\n\n    protected onChangeEditBox(editBox: EditBox): void {\n        let index: number = this.editBoxs.indexOf(editBox);\n        if (index >= 0) {\n            this.setCurConscriptForIndex(index, parseInt(this.editBoxs[index].string));\n            this.updateResCost();\n        }\n    }\n\n    protected onChangeSlider(slider: Slider): void {\n        let index: number = this.sliders.indexOf(slider);\n        if (index >= 0) {\n            let maxCnt: number = this._totalSoldiers[index] - this._soldiers[index];\n            this.setCurConscriptForIndex(index, Math.floor(this.sliders[index].progress * maxCnt));\n            this.updateResCost();\n        }\n    }\n\n    protected onClickQuick(): void {\n        AudioManager.instance.playClick();\n        if (this._data && this._data.cmd == ArmyCmd.Idle || this._data.cmd == ArmyCmd.Conscript) {\n            for (let i: number = 0; i < this._totalSoldiers.length; i++) {\n                if(this._conCnts[i] > 0){\n                    //正在征兵的不能重复征兵\n                    this.setCurConscriptForIndex(i, 0);\n                }else{\n                    let maxCnt: number = this._totalSoldiers[i] - this._soldiers[i];\n                    if (maxCnt > 0) {\n                        this.setCurConscriptForIndex(i, maxCnt);\n                    }\n                }\n            }\n            this.updateResCost();\n        }\n\n    }\n\n    protected onClickSure(): void {\n        AudioManager.instance.playClick();\n        if (this._data && this._data.cmd == ArmyCmd.Idle || this._data.cmd == ArmyCmd.Conscript) {\n            let totalCnt: number = this.getTotalConscriptCnt();\n            if (totalCnt > 0) {\n                ArmyCommand.getInstance().generalConscript(this._data.id, this._curConscripts, null);\n            }\n        }\n    }\n\n    protected onClickPrev(): void {\n        AudioManager.instance.playClick();\n        this._curConscripts = [0, 0, 0];\n        if (this._order == 1) {\n            //第一个就跳到最后一个\n            this.setData(this._cityId, this._addition.armyCnt);\n        } else {\n            this.setData(this._cityId, this._order - 1);\n        }\n    }\n\n    protected onClickNext(): void {\n        AudioManager.instance.playClick();\n        this._curConscripts = [0, 0, 0];\n        if (this._order == this._addition.armyCnt) {\n            //最后一个就跳到第一个\n            this.setData(this._cityId, 1);\n        } else {\n            this.setData(this._cityId, this._order + 1);\n        }\n    }\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n    }\n\n    public setData(cityId: number, order: number = 1): void {\n        this._curConscripts = [0, 0, 0];\n        this._cityId = cityId;\n        this._order = order;\n        this._cityData = MapCommand.getInstance().cityProxy.getMyCityById(this._cityId);\n        this._data = ArmyCommand.getInstance().proxy.getArmyByOrder(order, cityId);\n        this._addition = MapUICommand.getInstance().proxy.getMyCityAddition(cityId);\n        this._isUnlock = this._addition.armyCnt >= this._order;\n        let facility: Map<number, Facility> = MapUICommand.getInstance().proxy.getMyFacilitys(this._cityId);\n        if (facility == null) {\n            MapUICommand.getInstance().qryCityFacilities(this._cityId);\n        }\n        this.updateView();\n    }\n}\n"]}