{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"], "names": ["MapResConfig", "MapResType", "MapResData", "MapTagPos", "MapAreaData", "MapProxy", "Vec2", "view", "MapUtil", "EventMgr", "LogicEvent", "checkAndUpdateQryTime", "nowTime", "Date", "now", "qryStartTime", "MAX_TIME", "equals", "other", "id", "fuzzyEquals", "variance", "x", "y", "Map", "initData", "_mapAreaDatas", "length", "areaCount", "clearData", "_curCenterPoint", "_curCenterAreaId", "qryAreaIds", "setNationMapConfig", "configList", "_mapResConfigs", "clear", "i", "cfg", "type", "level", "name", "wood", "<PERSON>", "iron", "stone", "grain", "durable", "defender", "set", "set<PERSON><PERSON><PERSON><PERSON>", "time", "warFree", "getWarFree", "initMapResConfig", "jsonData", "w", "list", "_mapResDatas", "_mapSysCityResDatas", "data", "Math", "floor", "push", "SYS_CITY", "getSysCityResData", "index", "resData", "dis", "abs", "setCurCenterPoint", "point", "pixelPoint", "areaPoint", "getAreaPointByCellPoint", "areaId", "getIdByAreaPoint", "emit", "mapEenterChange", "areaData", "getMapAreaData", "oldIds", "newIds", "get9GridVaildAreaIds", "addIds", "removeIds", "firstAreaIds", "otherAreaIds", "temp", "clone", "leftTopPixelPoint", "add", "getVisibleSize", "width", "height", "leftDownPixelPoint", "rightTopPixelPoint", "rightDownPixelPoint", "getVaildAreaIdsByPixelPoints", "indexOf", "qryIndexs", "concat", "mapShowAreaChange", "getCurCenterPoint", "getCurCenterAreaId", "undefined", "getAreaPointById", "startCellPoint", "getStartCellPointByAreaPoint", "startCellX", "startCellY", "endCellX", "areaCellSize", "endCellY", "len", "getResYieldDesList", "getResData", "getResConfig", "key", "has", "get", "hasResDatas", "hasResConfig", "size", "updateMapPosTags", "posTag", "_mapPosTags", "for<PERSON>ach", "tag", "removeMapPosTag", "tags", "addMapPosTag", "ok", "isPosTag", "ret", "getPosTags"], "mappings": ";;;yEAMaA,Y,EAaAC,U,EAYAC,U,EAUAC,S,EAOAC,W,EAwCQC,Q;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxFeC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,I,OAAAA,I;;AACzCC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;8BAGIV,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,wCACP,CADO;;AAAA,yCAEN,CAFM;;AAAA,wCAGP,EAHO;;AAAA,wCAIP,CAJO;;AAAA,wCAKP,CALO;;AAAA,yCAMN,CANM;;AAAA,yCAON,CAPM;;AAAA,2CAQJ,CARI;;AAAA,4CASH,CATG;AAAA;;AAAA,O;AAY1B;;;4BACaC,U,GAAN,MAAMA,UAAN,CAAiB,E;AAWxB;;;sBAXaA,U,kBACqB,E;;sBADrBA,U,cAEiB,E;;sBAFjBA,U,UAGa,E;;sBAHbA,U,UAIa,E;;sBAJbA,U,WAKc,E;;sBALdA,U,WAMc,E;;sBANdA,U,cAOiB,E;;4BAKjBC,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,sCACP,CADO;;AAAA,wCAEL,CAFK;;AAAA,yCAGJ,CAHI;;AAAA,qCAIR,CAJQ;;AAAA,qCAKR,CALQ;AAAA;;AAAA,O;;2BAUXC,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,qCACP,CADO;;AAAA,qCAEP,CAFO;;AAAA,wCAGJ,EAHI;AAAA;;AAAA,O;AAMvB;;;6BACaC,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA,sCAER,CAFQ;;AAAA,qCAGT,CAHS;;AAAA,qCAIT,CAJS;;AAAA,8CAKA,CALA;;AAAA,8CAMA,CANA;;AAAA,4CAOF,CAPE;;AAAA,4CAQF,CARE;;AAAA,uCASP,CATO;;AAAA,gDAUE,CAVF;AAAA;;AAYdO,QAAAA,qBAAqB,GAAY;AACpC,cAAIC,OAAe,GAAGC,IAAI,CAACC,GAAL,EAAtB;;AACA,cAAIF,OAAO,GAAG,KAAKG,YAAf,IAA+BX,WAAW,CAACY,QAA/C,EAAyD;AACrD,iBAAKD,YAAL,GAAoBH,OAApB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEMK,QAAAA,MAAM,CAACC,KAAD,EAA8B;AACvC,cAAIA,KAAK,IAAI,IAAb,EAAmB;AACf,mBAAO,KAAP;AACH;;AACD,iBAAO,KAAKC,EAAL,IAAWD,KAAK,CAACC,EAAxB;AACH;;AAEMC,QAAAA,WAAW,CAACF,KAAD,EAAqBG,QAArB,EAAgD;AAC9D,cAAIH,KAAK,IAAI,IAAb,EAAmB;AACf,mBAAO,KAAP;AACH;;AACD,cAAI,KAAKI,CAAL,GAASD,QAAT,IAAqBH,KAAK,CAACI,CAA3B,IAAgCJ,KAAK,CAACI,CAAN,IAAW,KAAKA,CAAL,GAASD,QAAxD,EAAkE;AAC9D,gBAAI,KAAKE,CAAL,GAASF,QAAT,IAAqBH,KAAK,CAACK,CAA3B,IAAgCL,KAAK,CAACK,CAAN,IAAW,KAAKA,CAAL,GAASF,QAAxD,EACI,OAAO,IAAP;AACP;;AACD,iBAAO,KAAP;AACH;;AArCoB,O;;sBAAZjB,W,cACiB,K;;yBAuCTC,Q,GAAN,MAAMA,QAAN,CAAe;AAAA;AAAA,2CACF,CADE;;AAAA,iDAEY,IAFZ;;AAAA,mDAIQ,IAJR;;AAAA,oDAMW,CAAC,CANZ;;AAAA,iDAOe,EAPf;;AAAA,gDAQa,EARb;;AAAA,uDASoB,EATpB;;AAAA,+CAUW,EAVX;;AAAA,8CAaI,EAbJ;;AAAA,kDAe4B,IAAImB,GAAJ,EAf5B;AAAA;;AAiB1B;AACOC,QAAAA,QAAQ,GAAS;AACpB,eAAKC,aAAL,CAAmBC,MAAnB,GAA4B;AAAA;AAAA,kCAAQC,SAApC;AACH;;AAEMC,QAAAA,SAAS,GAAS;AACrB,eAAKC,eAAL,GAAuB,IAAvB;AACA,eAAKC,gBAAL,GAAwB,CAAC,CAAzB;AACA,eAAKL,aAAL,CAAmBC,MAAnB,GAA4B,CAA5B;AACA,eAAKK,UAAL,CAAgBL,MAAhB,GAAyB,CAAzB;AACH;AAED;;;AACOM,QAAAA,kBAAkB,CAACC,UAAD,EAA0B;AAC/C,eAAKC,cAAL,CAAoBC,KAApB;;AACA,eAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGH,UAAU,CAACP,MAAvC,EAA+CU,CAAC,EAAhD,EAAoD;AAChD,gBAAIC,GAAiB,GAAG,IAAItC,YAAJ,EAAxB;AACAsC,YAAAA,GAAG,CAACC,IAAJ,GAAWL,UAAU,CAACG,CAAD,CAAV,CAAcE,IAAzB;AACAD,YAAAA,GAAG,CAACE,KAAJ,GAAYN,UAAU,CAACG,CAAD,CAAV,CAAcG,KAA1B;AACAF,YAAAA,GAAG,CAACG,IAAJ,GAAWP,UAAU,CAACG,CAAD,CAAV,CAAcI,IAAzB;AACAH,YAAAA,GAAG,CAACI,IAAJ,GAAWR,UAAU,CAACG,CAAD,CAAV,CAAcM,IAAzB;AACAL,YAAAA,GAAG,CAACM,IAAJ,GAAWV,UAAU,CAACG,CAAD,CAAV,CAAcO,IAAzB;AACAN,YAAAA,GAAG,CAACO,KAAJ,GAAYX,UAAU,CAACG,CAAD,CAAV,CAAcQ,KAA1B;AACAP,YAAAA,GAAG,CAACQ,KAAJ,GAAYZ,UAAU,CAACG,CAAD,CAAV,CAAcS,KAA1B;AACAR,YAAAA,GAAG,CAACS,OAAJ,GAAcb,UAAU,CAACG,CAAD,CAAV,CAAcU,OAA5B;AACAT,YAAAA,GAAG,CAACU,QAAJ,GAAed,UAAU,CAACG,CAAD,CAAV,CAAcW,QAA7B;;AACA,iBAAKb,cAAL,CAAoBc,GAApB,CAAwBf,UAAU,CAACG,CAAD,CAAV,CAAcE,IAAd,GAAqB,GAArB,GAA2BD,GAAG,CAACE,KAAvD,EAA8DF,GAA9D;AACH;AACJ;;AAEMY,QAAAA,UAAU,CAACC,IAAD,EAAO;AACpB,eAAKC,OAAL,GAAeD,IAAf;AACH;;AAEME,QAAAA,UAAU,GAAU;AACvB,iBAAO,KAAKD,OAAL,GAAa,IAApB;AACH;;AAEME,QAAAA,gBAAgB,CAACC,QAAD,EAAsB;AACzC,cAAIC,CAAS,GAAGD,QAAQ,CAACC,CAAzB;AACA,cAAIC,IAA0B,GAAGF,QAAQ,CAACE,IAA1C;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKC,mBAAL,GAA2B,EAA3B;;AACA,eAAK,IAAItB,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGkB,QAAQ,CAACE,IAAT,CAAc9B,MAA1C,EAAkDU,CAAC,EAAnD,EAAuD;AACnD,gBAAIuB,IAAgB,GAAG,IAAI1D,UAAJ,EAAvB;AACA0D,YAAAA,IAAI,CAACzC,EAAL,GAAUkB,CAAV;AACAuB,YAAAA,IAAI,CAACrB,IAAL,GAAYkB,IAAI,CAACpB,CAAD,CAAJ,CAAQ,CAAR,CAAZ;AACAuB,YAAAA,IAAI,CAACpB,KAAL,GAAaiB,IAAI,CAACpB,CAAD,CAAJ,CAAQ,CAAR,CAAb;AACAuB,YAAAA,IAAI,CAACtC,CAAL,GAASe,CAAC,GAAGmB,CAAb;AACAI,YAAAA,IAAI,CAACrC,CAAL,GAASsC,IAAI,CAACC,KAAL,CAAWzB,CAAC,GAAGmB,CAAf,CAAT;;AACA,iBAAKE,YAAL,CAAkBK,IAAlB,CAAuBH,IAAvB;;AAEA,gBAAGA,IAAI,CAACrB,IAAL,IAAatC,UAAU,CAAC+D,QAA3B,EAAoC;AAChC,mBAAKL,mBAAL,CAAyBI,IAAzB,CAA8BH,IAA9B;AACH;AACJ;AACJ;;AAGMK,QAAAA,iBAAiB,CAAC3C,CAAD,EAAIC,CAAJ,EAAkB;AACtC,eAAK,IAAI2C,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAKP,mBAAL,CAAyBhC,MAArD,EAA6DuC,KAAK,EAAlE,EAAsE;AAElE,gBAAIC,OAAO,GAAG,KAAKR,mBAAL,CAAyBO,KAAzB,CAAd;AACA,gBAAI1B,KAAK,GAAG2B,OAAO,CAAC3B,KAApB;AACA,gBAAI4B,GAAG,GAAG,CAAV;;AACA,gBAAG5B,KAAK,IAAI,CAAZ,EAAc;AACV4B,cAAAA,GAAG,GAAG,CAAN;AACH,aAFD,MAEM,IAAG5B,KAAK,IAAI,CAAZ,EAAc;AAChB4B,cAAAA,GAAG,GAAG,CAAN;AACH,aAFK,MAEA;AACFA,cAAAA,GAAG,GAAG,CAAN;AACH;;AAED,gBAAIA,GAAG,IAAIP,IAAI,CAACQ,GAAL,CAAS/C,CAAC,GAAC6C,OAAO,CAAC7C,CAAnB,CAAP,IAAgC8C,GAAG,IAAIP,IAAI,CAACQ,GAAL,CAAS9C,CAAC,GAAC4C,OAAO,CAAC5C,CAAnB,CAA3C,EAAiE;AAC7D,qBAAO4C,OAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;AAID;;;AACOG,QAAAA,iBAAiB,CAACC,KAAD,EAAcC,UAAd,EAAyC;AAC7D,cAAI,KAAK1C,eAAL,IAAwB,IAAxB,IACG,KAAKA,eAAL,CAAqBR,CAArB,IAA0BiD,KAAK,CAACjD,CADnC,IAEG,KAAKQ,eAAL,CAAqBP,CAArB,IAA0BgD,KAAK,CAAChD,CAFvC,EAE0C;AACtC,iBAAKO,eAAL,GAAuByC,KAAvB;AACA,gBAAIE,SAAe,GAAG;AAAA;AAAA,oCAAQC,uBAAR,CAAgCH,KAAK,CAACjD,CAAtC,EAAyCiD,KAAK,CAAChD,CAA/C,CAAtB;AACA,gBAAIoD,MAAc,GAAG;AAAA;AAAA,oCAAQC,gBAAR,CAAyBH,SAAS,CAACnD,CAAnC,EAAsCmD,SAAS,CAAClD,CAAhD,CAArB;AAEA;AAAA;AAAA,sCAASsD,IAAT,CAAc;AAAA;AAAA,0CAAWC,eAAzB,EAA0C,KAAKhD,eAA/C;;AACA,gBAAI,KAAKC,gBAAL,IAAyB,CAAC,CAA1B,IAA+B,KAAKA,gBAAL,IAAyB4C,MAA5D,EAAoE;AAChE;AACA,kBAAII,QAAqB,GAAG,KAAKC,cAAL,CAAoBL,MAApB,CAA5B;AACA,kBAAIM,MAAgB,GAAG,IAAvB;AACA,kBAAIC,MAAgB,GAAG;AAAA;AAAA,sCAAQC,oBAAR,CAA6BJ,QAAQ,CAAC5D,EAAtC,CAAvB;AACA,kBAAIiE,MAAgB,GAAG,EAAvB;AACA,kBAAIC,SAAmB,GAAG,EAA1B;AACA,kBAAIC,YAAsB,GAAG,IAA7B;AACA,kBAAIC,YAAsB,GAAG,EAA7B;;AACA,kBAAI,KAAKxD,gBAAL,IAAyB,CAAC,CAA1B,IACG,KAAKiD,cAAL,CAAoB,KAAKjD,gBAAzB,EAA2CX,WAA3C,CAAuD2D,QAAvD,EAAiE,CAAjE,KAAuE,KAD9E,EACqF;AACjF;AACAE,gBAAAA,MAAM,GAAG,EAAT;AACAG,gBAAAA,MAAM,GAAGF,MAAT,CAHiF,CAIjF;;AACA,oBAAIM,IAAI,GAAGhB,UAAU,CAACiB,KAAX,EAAX;AACA,oBAAIC,iBAAuB,GAAGF,IAAI,CAACG,GAAL,CAAS,IAAIrF,IAAJ,CAAS,CAACC,IAAI,CAACqF,cAAL,GAAsBC,KAAvB,GAA+B,GAAxC,EAA6CtF,IAAI,CAACqF,cAAL,GAAsBE,MAAtB,GAA+B,GAA5E,CAAT,CAA9B;AACAN,gBAAAA,IAAI,GAAGhB,UAAU,CAACiB,KAAX,EAAP;AAEA,oBAAIM,kBAAwB,GAAGP,IAAI,CAACG,GAAL,CAAS,IAAIrF,IAAJ,CAAS,CAACC,IAAI,CAACqF,cAAL,GAAsBC,KAAvB,GAA+B,GAAxC,EAA6C,CAACtF,IAAI,CAACqF,cAAL,GAAsBE,MAAvB,GAAgC,GAA7E,CAAT,CAA/B;AACAN,gBAAAA,IAAI,GAAGhB,UAAU,CAACiB,KAAX,EAAP;AAEA,oBAAIO,kBAAwB,GAAGR,IAAI,CAACG,GAAL,CAAS,IAAIrF,IAAJ,CAASC,IAAI,CAACqF,cAAL,GAAsBC,KAAtB,GAA8B,GAAvC,EAA4CtF,IAAI,CAACqF,cAAL,GAAsBE,MAAtB,GAA+B,GAA3E,CAAT,CAA/B;AACAN,gBAAAA,IAAI,GAAGhB,UAAU,CAACiB,KAAX,EAAP;AAEA,oBAAIQ,mBAAyB,GAAGT,IAAI,CAACG,GAAL,CAAS,IAAIrF,IAAJ,CAASC,IAAI,CAACqF,cAAL,GAAsBC,KAAtB,GAA8B,GAAvC,EAA4C,CAACtF,IAAI,CAACqF,cAAL,GAAsBE,MAAvB,GAAgC,GAA5E,CAAT,CAAhC;AACAN,gBAAAA,IAAI,GAAGhB,UAAU,CAACiB,KAAX,EAAP;AAEAH,gBAAAA,YAAY,GAAG;AAAA;AAAA,wCAAQY,4BAAR,CAAqCV,IAArC,EAA2CE,iBAA3C,EAA8DK,kBAA9D,EAAkFC,kBAAlF,EAAsGC,mBAAtG,CAAf;AACH,eApBD,MAoBO;AACHhB,gBAAAA,MAAM,GAAG;AAAA;AAAA,wCAAQE,oBAAR,CAA6B,KAAKpD,gBAAlC,CAAT;;AACA,qBAAK,IAAIM,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG6C,MAAM,CAACvD,MAAnC,EAA2CU,CAAC,EAA5C,EAAgD;AAC5C,sBAAI4C,MAAM,CAACkB,OAAP,CAAejB,MAAM,CAAC7C,CAAD,CAArB,KAA6B,CAAC,CAAlC,EAAqC;AACjC+C,oBAAAA,MAAM,CAACrB,IAAP,CAAYmB,MAAM,CAAC7C,CAAD,CAAlB;AACH;AACJ;;AACD,qBAAK,IAAIA,EAAS,GAAG,CAArB,EAAwBA,EAAC,GAAG4C,MAAM,CAACtD,MAAnC,EAA2CU,EAAC,EAA5C,EAAgD;AAC5C,sBAAI6C,MAAM,CAACiB,OAAP,CAAelB,MAAM,CAAC5C,EAAD,CAArB,KAA6B,CAAC,CAAlC,EAAqC;AACjCgD,oBAAAA,SAAS,CAACtB,IAAV,CAAekB,MAAM,CAAC5C,EAAD,CAArB;AACH;AACJ,iBAXE,CAYH;;;AACA,oBAAI+C,MAAM,CAACe,OAAP,CAAepB,QAAQ,CAAC5D,EAAxB,CAAJ,EAAiC;AAC7BmE,kBAAAA,YAAY,GAAG,CAACP,QAAQ,CAAC5D,EAAV,CAAf;AACH;AACJ;;AAED,kBAAImE,YAAY,IAAIA,YAAY,CAAC3D,MAAb,GAAsB,CAA1C,EAA6C;AACzC,qBAAK,IAAIU,GAAS,GAAG,CAArB,EAAwBA,GAAC,GAAG+C,MAAM,CAACzD,MAAnC,EAA2CU,GAAC,EAA5C,EAAgD;AAC5C,sBAAIiD,YAAY,CAACa,OAAb,CAAqBf,MAAM,CAAC/C,GAAD,CAA3B,KAAmC,CAAC,CAAxC,EAA2C;AACvCkD,oBAAAA,YAAY,CAACxB,IAAb,CAAkBqB,MAAM,CAAC/C,GAAD,CAAxB;AACH;AACJ;AACJ,eAND,MAMO;AACHkD,gBAAAA,YAAY,GAAGH,MAAf;AACH;;AAED,kBAAIgB,SAAmB,GAAG,IAA1B;;AACA,kBAAId,YAAY,IAAIA,YAAY,CAAC3D,MAAb,GAAsB,CAA1C,EAA6C;AACzCyE,gBAAAA,SAAS,GAAGd,YAAY,CAACe,MAAb,CAAoBd,YAApB,CAAZ;AACH,eAFD,MAEO;AACHa,gBAAAA,SAAS,GAAGb,YAAZ;AACH;;AACD,mBAAKvD,UAAL,GAAkB,KAAKA,UAAL,CAAgBqE,MAAhB,CAAuBD,SAAvB,CAAlB,CA/DgE,CAgEhE;;AAEA,mBAAKrE,gBAAL,GAAwB4C,MAAxB;AACA;AAAA;AAAA,wCAASE,IAAT,CAAc;AAAA;AAAA,4CAAWyB,iBAAzB,EAA4C/B,KAA5C,EAAmD,KAAKxC,gBAAxD,EAA0EqD,MAA1E,EAAkFC,SAAlF;AACH;;AACD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEMkB,QAAAA,iBAAiB,GAAQ;AAC5B,iBAAO,KAAKzE,eAAZ;AACH;;AAEM0E,QAAAA,kBAAkB,GAAW;AAChC,iBAAO,KAAKzE,gBAAZ;AACH;AAED;;;AACOiD,QAAAA,cAAc,CAAC7D,EAAD,EAA0B;AAC3C,cAAI,KAAKO,aAAL,CAAmBP,EAAnB,KAA0BsF,SAA9B,EAAyC;AACrC,gBAAI7C,IAAiB,GAAG,IAAIxD,WAAJ,EAAxB;AACAwD,YAAAA,IAAI,CAACzC,EAAL,GAAUA,EAAV;AACA,gBAAIoD,KAAW,GAAG;AAAA;AAAA,oCAAQmC,gBAAR,CAAyBvF,EAAzB,CAAlB;AACA,gBAAIwF,cAAoB,GAAG;AAAA;AAAA,oCAAQC,4BAAR,CAAqCrC,KAAK,CAACjD,CAA3C,EAA8CiD,KAAK,CAAChD,CAApD,CAA3B;AACAqC,YAAAA,IAAI,CAACtC,CAAL,GAASiD,KAAK,CAACjD,CAAf;AACAsC,YAAAA,IAAI,CAACrC,CAAL,GAASgD,KAAK,CAAChD,CAAf;AACAqC,YAAAA,IAAI,CAACiD,UAAL,GAAkBF,cAAc,CAACrF,CAAjC;AACAsC,YAAAA,IAAI,CAACkD,UAAL,GAAkBH,cAAc,CAACpF,CAAjC;AACAqC,YAAAA,IAAI,CAACmD,QAAL,GAAgBJ,cAAc,CAACrF,CAAf,GAAmB;AAAA;AAAA,oCAAQ0F,YAAR,CAAqBnB,KAAxD;AACAjC,YAAAA,IAAI,CAACqD,QAAL,GAAgBN,cAAc,CAACpF,CAAf,GAAmB;AAAA;AAAA,oCAAQyF,YAAR,CAAqBnB,KAAxD;AACAjC,YAAAA,IAAI,CAACsD,GAAL,GAAW;AAAA;AAAA,oCAAQF,YAAR,CAAqBnB,KAAhC;AACA,iBAAKnE,aAAL,CAAmBP,EAAnB,IAAyByC,IAAzB;AACA,mBAAOA,IAAP;AACH;;AACD,iBAAO,KAAKlC,aAAL,CAAmBP,EAAnB,CAAP;AACH;AAED;;;AACOgG,QAAAA,kBAAkB,CAAC7E,GAAD,EAA8B;AACnD,cAAImB,IAAc,GAAG,EAArB;;AACA,cAAInB,GAAG,CAACQ,KAAJ,GAAY,CAAhB,EAAmB;AACfW,YAAAA,IAAI,CAACM,IAAL,CAAU,SAASzB,GAAG,CAACQ,KAAb,GAAqB,KAA/B;AACH;;AACD,cAAIR,GAAG,CAACI,IAAJ,GAAW,CAAf,EAAkB;AACde,YAAAA,IAAI,CAACM,IAAL,CAAU,SAASzB,GAAG,CAACI,IAAb,GAAoB,KAA9B;AACH;;AACD,cAAIJ,GAAG,CAACM,IAAJ,GAAW,CAAf,EAAkB;AACda,YAAAA,IAAI,CAACM,IAAL,CAAU,SAASzB,GAAG,CAACM,IAAb,GAAoB,KAA9B;AACH;;AACD,cAAIN,GAAG,CAACO,KAAJ,GAAY,CAAhB,EAAmB;AACfY,YAAAA,IAAI,CAACM,IAAL,CAAU,SAASzB,GAAG,CAACO,KAAb,GAAqB,KAA/B;AACH;;AACD,iBAAOY,IAAP;AACH;;AAEM2D,QAAAA,UAAU,CAACjG,EAAD,EAAyB;AACtC,iBAAO,KAAKuC,YAAL,CAAkBvC,EAAlB,CAAP;AACH;AAED;;;AACOkG,QAAAA,YAAY,CAAC9E,IAAD,EAAeC,KAAf,EAA4C;AAC3D,cAAI8E,GAAW,GAAG/E,IAAI,GAAG,GAAP,GAAaC,KAA/B;;AACA,cAAI,KAAKL,cAAL,CAAoBoF,GAApB,CAAwBD,GAAxB,CAAJ,EAAkC;AAC9B,mBAAO,KAAKnF,cAAL,CAAoBqF,GAApB,CAAwBF,GAAxB,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;;AAEMG,QAAAA,WAAW,GAAY;AAC1B,iBAAO,KAAK/D,YAAL,CAAkB/B,MAAlB,GAA2B,CAAlC;AACH;;AAEM+F,QAAAA,YAAY,GAAY;AAC3B,iBAAO,KAAKvF,cAAL,CAAoBwF,IAApB,GAA2B,CAAlC;AACH;;AAEMC,QAAAA,gBAAgB,CAACC,MAAD,EAAc;AACjC,eAAKC,WAAL,GAAmB,EAAnB;AACAD,UAAAA,MAAM,CAACE,OAAP,CAAenE,IAAI,IAAI;AACnB,gBAAIoE,GAAG,GAAG,IAAI7H,SAAJ,EAAV;AACA6H,YAAAA,GAAG,CAAC1G,CAAJ,GAAQsC,IAAI,CAACtC,CAAb;AACA0G,YAAAA,GAAG,CAACzG,CAAJ,GAAQqC,IAAI,CAACrC,CAAb;AACAyG,YAAAA,GAAG,CAACvF,IAAJ,GAAWmB,IAAI,CAACnB,IAAhB;;AAEA,iBAAKqF,WAAL,CAAiB/D,IAAjB,CAAsBiE,GAAtB;AACH,WAPD;AAQH;;AAEMC,QAAAA,eAAe,CAAC3G,CAAD,EAAYC,CAAZ,EAAsB;AACxC,cAAI2G,IAAiB,GAAG,EAAxB;;AACA,eAAKJ,WAAL,CAAiBC,OAAjB,CAAyBC,GAAG,IAAI;AAC5B,gBAAGA,GAAG,CAAC1G,CAAJ,IAASA,CAAT,IAAcC,CAAC,IAAIyG,GAAG,CAACzG,CAA1B,EAA4B;AACxB2G,cAAAA,IAAI,CAACnE,IAAL,CAAUiE,GAAV;AACH;AACJ,WAJD;;AAMA,eAAKF,WAAL,GAAmBI,IAAnB;AACH;;AAEMC,QAAAA,YAAY,CAAC7G,CAAD,EAAYC,CAAZ,EAAsBkB,IAAtB,EAAmC;AAClD,cAAIuF,GAAG,GAAG,IAAI7H,SAAJ,EAAV;AACA6H,UAAAA,GAAG,CAAC1G,CAAJ,GAAQA,CAAR;AACA0G,UAAAA,GAAG,CAACzG,CAAJ,GAAQA,CAAR;AACAyG,UAAAA,GAAG,CAACvF,IAAJ,GAAWA,IAAX;AAEA,cAAI2F,EAAE,GAAG,IAAT;;AACA,eAAKN,WAAL,CAAiBC,OAAjB,CAAyBC,GAAG,IAAI;AAC5B,gBAAIA,GAAG,CAAC1G,CAAJ,IAASA,CAAT,IAAc0G,GAAG,CAACzG,CAAJ,IAASA,CAA3B,EAA6B;AACzB6G,cAAAA,EAAE,GAAG,KAAL;AACH;AACJ,WAJD;;AAMA,cAAIA,EAAJ,EAAO;AACH,iBAAKN,WAAL,CAAiB/D,IAAjB,CAAsBiE,GAAtB;AACH;AAEJ;;AAEMK,QAAAA,QAAQ,CAAC/G,CAAD,EAAYC,CAAZ,EAA8B;AACzC,cAAI+G,GAAG,GAAG,KAAV;;AACA,eAAK,IAAIpE,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAK4D,WAAL,CAAiBnG,MAA7C,EAAqDuC,KAAK,EAA1D,EAA8D;AAC1D,gBAAM8D,GAAG,GAAG,KAAKF,WAAL,CAAiB5D,KAAjB,CAAZ;;AACA,gBAAI8D,GAAG,CAAC1G,CAAJ,IAASA,CAAT,IAAc0G,GAAG,CAACzG,CAAJ,IAASA,CAA3B,EAA6B;AACzB+G,cAAAA,GAAG,GAAG,IAAN;AACA;AACH;AACJ;;AAED,iBAAOA,GAAP;AACH;;AAEMC,QAAAA,UAAU,GAAe;AAC5B,iBAAO,KAAKT,WAAZ;AACH;;AAnTyB,O", "sourcesContent": ["import { _decorator, TiledMapAsset, Vec2, game, view } from 'cc';\nimport MapUtil from \"./MapUtil\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\n\nexport class MapResConfig {\n    type: number = 0;\n    level: number = 0;\n    name: string = \"\";\n    wood: number = 0;\n    iron: number = 0;\n    stone: number = 0;\n    grain: number = 0;\n    durable: number = 0;\n    defender: number = 0;\n}\n\n/**地图资源类型*/\nexport class MapResType {\n    static SYS_FORTRESS: number = 50;   //系统要塞\n    static SYS_CITY: number = 51;\n    static WOOD: number = 52;\n    static IRON: number = 53;\n    static STONE: number = 54;\n    static GRAIN: number = 55;\n    static FORTRESS: number = 56; //要塞\n    \n}\n\n/**地图资源数据*/\nexport class MapResData {\n    id: number = 0;\n    type: number = 0;\n    level: number = 0;\n    x: number = 0;\n    y: number = 0;\n}\n\n\n\nexport class MapTagPos {\n    x: number = 0;\n    y: number = 0;\n    name: string = \"\";\n}\n\n/**地图区域数据*/\nexport class MapAreaData {\n    static MAX_TIME: number = 10000;\n    id: number = 0;\n    x: number = 0;\n    y: number = 0;\n    startCellX: number = 0;\n    startCellY: number = 0;\n    endCellX: number = 0;\n    endCellY: number = 0;\n    len: number = 0;\n    qryStartTime: number = 0;\n\n    public checkAndUpdateQryTime(): boolean {\n        let nowTime: number = Date.now();\n        if (nowTime - this.qryStartTime >= MapAreaData.MAX_TIME) {\n            this.qryStartTime = nowTime;\n            return true\n        }\n        return false;\n    }\n\n    public equals(other: MapAreaData): boolean {\n        if (other == null) {\n            return false;\n        }\n        return this.id == other.id;\n    }\n\n    public fuzzyEquals(other: MapAreaData, variance: number): boolean {\n        if (other == null) {\n            return false;\n        }\n        if (this.x - variance <= other.x && other.x <= this.x + variance) {\n            if (this.y - variance <= other.y && other.y <= this.y + variance)\n                return true;\n        }\n        return false;\n    }\n}\n\nexport default class MapProxy {\n    public warFree:number = 0; //免战时间\n    public tiledMapAsset: TiledMapAsset = null;\n    //当前地图中心点\n    protected _curCenterPoint: Vec2 = null;\n    //当前展示区域\n    protected _curCenterAreaId: number = -1;\n    protected _mapAreaDatas: MapAreaData[] = [];\n    protected _mapResDatas: MapResData[] = [];\n    protected _mapSysCityResDatas: MapResData[] = [];\n    protected _mapPosTags: MapTagPos[] = [];\n\n    //地图请求列表\n    public qryAreaIds: number[] = [];\n    //地图基础配置数据\n    protected _mapResConfigs: Map<string, MapResConfig> = new Map<string, MapResConfig>();\n\n    // 初始化地图配置\n    public initData(): void {\n        this._mapAreaDatas.length = MapUtil.areaCount;\n    }\n\n    public clearData(): void {\n        this._curCenterPoint = null;\n        this._curCenterAreaId = -1;\n        this._mapAreaDatas.length = 0;\n        this.qryAreaIds.length = 0;\n    }\n\n    /**地图建筑基础配置信息*/\n    public setNationMapConfig(configList: any[]): void {\n        this._mapResConfigs.clear();\n        for (let i: number = 0; i < configList.length; i++) {\n            let cfg: MapResConfig = new MapResConfig();\n            cfg.type = configList[i].type;\n            cfg.level = configList[i].level;\n            cfg.name = configList[i].name;\n            cfg.wood = configList[i].Wood;\n            cfg.iron = configList[i].iron;\n            cfg.stone = configList[i].stone;\n            cfg.grain = configList[i].grain;\n            cfg.durable = configList[i].durable;\n            cfg.defender = configList[i].defender;\n            this._mapResConfigs.set(configList[i].type + \"_\" + cfg.level, cfg);\n        }\n    }\n\n    public setWarFree(time) {\n        this.warFree = time;\n    }\n\n    public getWarFree(): number{\n        return this.warFree*1000\n    }\n\n    public initMapResConfig(jsonData: any): void {\n        let w: number = jsonData.w;\n        let list: Array<Array<number>> = jsonData.list;\n        this._mapResDatas = [];\n        this._mapSysCityResDatas = [];\n        for (let i: number = 0; i < jsonData.list.length; i++) {\n            let data: MapResData = new MapResData();\n            data.id = i;\n            data.type = list[i][0];\n            data.level = list[i][1];\n            data.x = i % w;\n            data.y = Math.floor(i / w);\n            this._mapResDatas.push(data);\n\n            if(data.type == MapResType.SYS_CITY){\n                this._mapSysCityResDatas.push(data)\n            }\n        }\n    }\n\n\n    public getSysCityResData(x, y): MapResData{\n        for (let index = 0; index < this._mapSysCityResDatas.length; index++) {\n            \n            var resData = this._mapSysCityResDatas[index];\n            var level = resData.level;\n            var dis = 0;\n            if(level >= 8){\n                dis = 3;\n            }else if(level >= 5){\n                dis = 2;\n            }else {\n                dis = 1;\n            }\n\n            if( dis >= Math.abs(x-resData.x) && dis >= Math.abs(y-resData.y)){\n                return resData;\n            }\n        }\n        return null;\n    }\n\n\n\n    /**设置地图当前中心点的信息*/\n    public setCurCenterPoint(point: Vec2, pixelPoint: Vec2): boolean {\n        if (this._curCenterPoint == null\n            || this._curCenterPoint.x != point.x\n            || this._curCenterPoint.y != point.y) {\n            this._curCenterPoint = point;\n            let areaPoint: Vec2 = MapUtil.getAreaPointByCellPoint(point.x, point.y);\n            let areaId: number = MapUtil.getIdByAreaPoint(areaPoint.x, areaPoint.y);\n\n            EventMgr.emit(LogicEvent.mapEenterChange, this._curCenterPoint);\n            if (this._curCenterAreaId == -1 || this._curCenterAreaId != areaId) {\n                //展示区域变化\n                let areaData: MapAreaData = this.getMapAreaData(areaId);\n                let oldIds: number[] = null;\n                let newIds: number[] = MapUtil.get9GridVaildAreaIds(areaData.id);\n                let addIds: number[] = [];\n                let removeIds: number[] = [];\n                let firstAreaIds: number[] = null;\n                let otherAreaIds: number[] = [];\n                if (this._curCenterAreaId == -1\n                    || this.getMapAreaData(this._curCenterAreaId).fuzzyEquals(areaData, 3) == false) {\n                    //全量刷新\n                    oldIds = [];\n                    addIds = newIds;\n                    //计算四个角所在的区域 用于判断需要优先请求的区域\n                    let temp = pixelPoint.clone();\n                    let leftTopPixelPoint: Vec2 = temp.add(new Vec2(-view.getVisibleSize().width * 0.5, view.getVisibleSize().height * 0.5));\n                    temp = pixelPoint.clone();\n\n                    let leftDownPixelPoint: Vec2 = temp.add(new Vec2(-view.getVisibleSize().width * 0.5, -view.getVisibleSize().height * 0.5));\n                    temp = pixelPoint.clone();\n\n                    let rightTopPixelPoint: Vec2 = temp.add(new Vec2(view.getVisibleSize().width * 0.5, view.getVisibleSize().height * 0.5));\n                    temp = pixelPoint.clone();\n\n                    let rightDownPixelPoint: Vec2 = temp.add(new Vec2(view.getVisibleSize().width * 0.5, -view.getVisibleSize().height * 0.5));\n                    temp = pixelPoint.clone();\n\n                    firstAreaIds = MapUtil.getVaildAreaIdsByPixelPoints(temp, leftTopPixelPoint, leftDownPixelPoint, rightTopPixelPoint, rightDownPixelPoint);\n                } else {\n                    oldIds = MapUtil.get9GridVaildAreaIds(this._curCenterAreaId);\n                    for (let i: number = 0; i < newIds.length; i++) {\n                        if (oldIds.indexOf(newIds[i]) == -1) {\n                            addIds.push(newIds[i]);\n                        }\n                    }\n                    for (let i: number = 0; i < oldIds.length; i++) {\n                        if (newIds.indexOf(oldIds[i]) == -1) {\n                            removeIds.push(oldIds[i]);\n                        }\n                    }\n                    //其他情况优先请求中心区域\n                    if (addIds.indexOf(areaData.id)) {\n                        firstAreaIds = [areaData.id];\n                    }\n                }\n\n                if (firstAreaIds && firstAreaIds.length > 0) {\n                    for (let i: number = 0; i < addIds.length; i++) {\n                        if (firstAreaIds.indexOf(addIds[i]) == -1) {\n                            otherAreaIds.push(addIds[i]);\n                        }\n                    }\n                } else {\n                    otherAreaIds = addIds;\n                }\n\n                let qryIndexs: number[] = null;\n                if (firstAreaIds && firstAreaIds.length > 0) {\n                    qryIndexs = firstAreaIds.concat(otherAreaIds);\n                } else {\n                    qryIndexs = otherAreaIds;\n                }\n                this.qryAreaIds = this.qryAreaIds.concat(qryIndexs);\n                // this.qryAreaIds = [18];\n\n                this._curCenterAreaId = areaId;\n                EventMgr.emit(LogicEvent.mapShowAreaChange, point, this._curCenterAreaId, addIds, removeIds);\n            }\n            return true;\n        }\n        return false;\n    }\n\n    public getCurCenterPoint():Vec2 {\n        return this._curCenterPoint;\n    }\n\n    public getCurCenterAreaId(): number {\n        return this._curCenterAreaId;\n    }\n\n    /**获取地图区域数据*/\n    public getMapAreaData(id: number): MapAreaData {\n        if (this._mapAreaDatas[id] == undefined) {\n            let data: MapAreaData = new MapAreaData();\n            data.id = id;\n            let point: Vec2 = MapUtil.getAreaPointById(id);\n            let startCellPoint: Vec2 = MapUtil.getStartCellPointByAreaPoint(point.x, point.y);\n            data.x = point.x;\n            data.y = point.y;\n            data.startCellX = startCellPoint.x;\n            data.startCellY = startCellPoint.y;\n            data.endCellX = startCellPoint.x + MapUtil.areaCellSize.width;\n            data.endCellY = startCellPoint.y + MapUtil.areaCellSize.width;\n            data.len = MapUtil.areaCellSize.width;\n            this._mapAreaDatas[id] = data;\n            return data;\n        }\n        return this._mapAreaDatas[id];\n    }\n\n    /*获取产量描述**/\n    public getResYieldDesList(cfg: MapResConfig): string[] {\n        let list: string[] = [];\n        if (cfg.grain > 0) {\n            list.push(\"粮食 +\" + cfg.grain + \"/小时\");\n        }\n        if (cfg.wood > 0) {\n            list.push(\"木材 +\" + cfg.wood + \"/小时\");\n        }\n        if (cfg.iron > 0) {\n            list.push(\"铁矿 +\" + cfg.iron + \"/小时\");\n        }\n        if (cfg.stone > 0) {\n            list.push(\"石料 +\" + cfg.stone + \"/小时\");\n        }\n        return list;\n    }\n\n    public getResData(id: number): MapResData {\n        return this._mapResDatas[id];\n    }\n\n    /**根据类型获取配置数据*/\n    public getResConfig(type: number, level: number): MapResConfig {\n        let key: string = type + \"_\" + level;\n        if (this._mapResConfigs.has(key)) {\n            return this._mapResConfigs.get(key);\n        }\n        return null;\n    }\n\n    public hasResDatas(): boolean {\n        return this._mapResDatas.length > 0;\n    }\n\n    public hasResConfig(): boolean {\n        return this._mapResConfigs.size > 0;\n    }\n\n    public updateMapPosTags(posTag: any) {\n        this._mapPosTags = [];\n        posTag.forEach(data => {\n            var tag = new MapTagPos();\n            tag.x = data.x;\n            tag.y = data.y;\n            tag.name = data.name;\n\n            this._mapPosTags.push(tag);\n        });\n    }\n\n    public removeMapPosTag(x: number, y:number) {\n        var tags: MapTagPos[] = [];\n        this._mapPosTags.forEach(tag => {\n            if(tag.x != x || y != tag.y){\n                tags.push(tag);\n            }\n        });\n\n        this._mapPosTags = tags;\n    }\n\n    public addMapPosTag(x: number, y:number, name:string) {\n        var tag = new MapTagPos();\n        tag.x = x;\n        tag.y = y;\n        tag.name = name;\n\n        var ok = true;\n        this._mapPosTags.forEach(tag => {\n            if (tag.x == x && tag.y == y){\n                ok = false;\n            }\n        });\n\n        if (ok){\n            this._mapPosTags.push(tag);\n        }\n       \n    }\n\n    public isPosTag(x: number, y:number):boolean {\n        var ret = false;\n        for (let index = 0; index < this._mapPosTags.length; index++) {\n            const tag = this._mapPosTags[index];\n            if (tag.x == x && tag.y == y){\n                ret = true;\n                break;\n            }\n        }\n       \n        return ret;\n    }\n\n    public getPosTags() :MapTagPos[]{\n        return this._mapPosTags;\n    }\n}\n"]}