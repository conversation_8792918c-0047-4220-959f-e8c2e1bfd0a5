{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts"], "names": ["_decorator", "Component", "Sprite", "SpriteAtlas", "MapCommand", "EventMgr", "LogicEvent", "ccclass", "property", "ResBuildLogic", "onLoad", "onDestroy", "onEnable", "on", "unionChange", "onUnionChange", "onDisable", "_data", "targetOff", "rid", "unionId", "parentId", "updateUI", "setBuildData", "data", "upSpr", "spriteFrame", "downSpr", "getInstance", "buildProxy", "myId", "resourceAtlas", "getSpriteFrame", "myUnionId", "myParentId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAIjCC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBASTS,a,WADpBF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACN,MAAD,C,UAERM,QAAQ,CAACN,MAAD,C,UAERM,QAAQ,CAACL,WAAD,C,oCANb,MACqBM,aADrB,SAC2CR,SAD3C,CACqD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAOjB,IAPiB;AAAA;;AAQvCS,QAAAA,MAAM,GAAS,CAExB;;AACSC,QAAAA,SAAS,GAAS,CAE3B;;AACSC,QAAAA,QAAQ,GAAQ;AACtB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AACH;;AACSC,QAAAA,SAAS,GAAQ;AACvB,eAAKC,KAAL,GAAa,IAAb;AACA;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACSH,QAAAA,aAAa,CAACI,GAAD,EAAcC,OAAd,EAA+BC,QAA/B,EAAuD;AAC1E,cAAI,KAAKJ,KAAL,CAAWE,GAAX,IAAkBA,GAAtB,EAA2B;AAC3B,iBAAKF,KAAL,CAAWG,OAAX,GAAqBA,OAArB;AACA,iBAAKH,KAAL,CAAWI,QAAX,GAAsBA,QAAtB;AACC;;AACD,eAAKC,QAAL;AACF;;AAEMC,QAAAA,YAAY,CAACC,IAAD,EAA2B;AAC3C,eAAKP,KAAL,GAAaO,IAAb;AACA,eAAKF,QAAL;AACF;;AACKA,QAAAA,QAAQ,GAAS;AAEpB,cAAI,KAAKL,KAAT,EAAgB;AAChB,gBAAG,CAAC,KAAKA,KAAL,CAAWE,GAAf,EAAmB;AACnB,mBAAKM,KAAL,CAAWC,WAAX,GAAyB,IAAzB;AACA,mBAAKC,OAAL,CAAaD,WAAb,GAA2B,IAA3B;AACC,aAHD,MAGM,IAAI,KAAKT,KAAL,CAAWE,GAAX,IAAkB;AAAA;AAAA,0CAAWS,WAAX,GAAyBC,UAAzB,CAAoCC,IAA1D,EAAgE;AACtE,mBAAKL,KAAL,CAAWC,WAAX,GAAyB,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,UAAlC,CAAzB;AACA,mBAAKL,OAAL,CAAaD,WAAb,GAA2B,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,UAAlC,CAA3B;AACC,aAHK,MAGC,IAAI,KAAKf,KAAL,CAAWG,OAAX,GAAqB,CAArB,IAA0B,KAAKH,KAAL,CAAWG,OAAX,IAAsB;AAAA;AAAA,0CAAWQ,WAAX,GAAyBC,UAAzB,CAAoCI,SAAxF,EAAmG;AAC1G,mBAAKR,KAAL,CAAWC,WAAX,GAAyB,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,WAAlC,CAAzB;AACA,mBAAKL,OAAL,CAAaD,WAAb,GAA2B,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,WAAlC,CAA3B;AACC,aAHM,MAGD,IAAI,KAAKf,KAAL,CAAWG,OAAX,GAAqB,CAArB,IAA0B,KAAKH,KAAL,CAAWG,OAAX,IAAsB;AAAA;AAAA,0CAAWQ,WAAX,GAAyBC,UAAzB,CAAoCK,UAAxF,EAAoG;AAC1G,mBAAKT,KAAL,CAAWC,WAAX,GAAyB,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAAzB;AACA,mBAAKL,OAAL,CAAaD,WAAb,GAA2B,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAA3B;AACC,aAHK,MAGC,IAAI,KAAKf,KAAL,CAAWI,QAAX,GAAsB,CAAtB,IAA2B,KAAKJ,KAAL,CAAWI,QAAX,IAAuB;AAAA;AAAA,0CAAWO,WAAX,GAAyBC,UAAzB,CAAoCI,SAA1F,EAAqG;AAC5G,mBAAKR,KAAL,CAAWC,WAAX,GAAyB,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAAzB;AACA,mBAAKL,OAAL,CAAaD,WAAb,GAA2B,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAA3B;AACC,aAHM,MAGD;AACN,mBAAKP,KAAL,CAAWC,WAAX,GAAyB,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,SAAlC,CAAzB;AACA,mBAAKL,OAAL,CAAaD,WAAb,GAA2B,KAAKK,aAAL,CAAmBC,cAAnB,CAAkC,SAAlC,CAA3B;AACC;AACA;AACJ;;AAxDgD,O;;;;;iBAE1B,I;;;;;;;iBAEE,I;;;;;;;iBAEW,I", "sourcesContent": ["import { _decorator, Component, Sprite, SpriteAtlas } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { MapBuildData } from \"../MapBuildProxy\";\nimport MapCommand from \"../MapCommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n\n@ccclass('ResBuildLogic')\nexport default class ResBuildLogic extends Component {\n    @property(Sprite)\n    upSpr: Sprite | null = null;\n    @property(Sprite)\n    downSpr: Sprite | null = null;\n    @property(SpriteAtlas)\n    resourceAtlas: SpriteAtlas | null = null;\n    protected _data: MapBuildData = null;\n    protected onLoad(): void {\n\n    }\n    protected onDestroy(): void {\n\n    }\n    protected onEnable():void {\n        EventMgr.on(LogicEvent.unionChange, this.onUnionChange, this);\n    }\n    protected onDisable():void {\n        this._data = null;\n        EventMgr.targetOff(this);\n    }\n    protected onUnionChange(rid: number, unionId: number, parentId: number): void {\n        if (this._data.rid == rid ){\n        this._data.unionId = unionId;\n        this._data.parentId = parentId;\n        }\n        this.updateUI();\n     }\n \n     public setBuildData(data: MapBuildData): void {\n        this._data = data;\n        this.updateUI();\n     }\n    public updateUI(): void {\n\n        if (this._data) {\n        if(!this._data.rid){\n        this.upSpr.spriteFrame = null;\n        this.downSpr.spriteFrame = null;\n        }else if (this._data.rid == MapCommand.getInstance().buildProxy.myId) {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"blue_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"blue_1_3\");\n        } else if (this._data.unionId > 0 && this._data.unionId == MapCommand.getInstance().buildProxy.myUnionId) {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"green_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"green_1_3\");\n        }else if (this._data.unionId > 0 && this._data.unionId == MapCommand.getInstance().buildProxy.myParentId) {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"purple_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"purple_1_3\");\n        } else if (this._data.parentId > 0 && this._data.parentId == MapCommand.getInstance().buildProxy.myUnionId) {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"yellow_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"yellow_1_3\");\n        }else {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"red_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"red_1_3\");\n        }\n        }\n    }\n}\n"]}