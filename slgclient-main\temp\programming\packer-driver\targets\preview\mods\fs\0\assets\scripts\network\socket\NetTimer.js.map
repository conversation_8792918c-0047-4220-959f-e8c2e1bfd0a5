{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts"], "names": ["NetTimerData", "NetTimer", "NetEvent", "EventMgr", "init", "_tokens", "Map", "_tokenId", "schedule", "data", "delay", "self", "token", "id", "setTimeout", "handleTimeout", "timerData", "name", "seq", "timeId", "set", "get", "emit", "ServerTimeOut", "delete", "cancel", "<PERSON><PERSON><PERSON>", "clearTimeout", "return_key", "for<PERSON>ach", "value", "key", "destroy", "clear"], "mappings": ";;;kDAMaA,Y,EAMAC,Q;;;;;;;;;;;;;;;;;;;;;;;AAVJC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;8BAGIH,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,wCACD,EADC;;AAAA,uCAEF,CAFE;;AAAA,0CAGC,CAHD;AAAA;;AAAA,O;;0BAMbC,Q,GAAN,MAAMA,QAAN,CAAe;AAAA;AAAA,2CACI,IADJ;;AAAA,4CAEQ,CAFR;AAAA;;AAGXG,QAAAA,IAAI,GAAE;AACT,eAAKC,OAAL,GAAe,IAAIC,GAAJ,EAAf;AACA,eAAKC,QAAL,GAAgB,CAAhB;AACH;;AAEMC,QAAAA,QAAQ,CAACC,IAAD,EAAUC,KAAV,EAAgC;AAAA,cAAtBA,KAAsB;AAAtBA,YAAAA,KAAsB,GAAP,CAAO;AAAA;;AAE3C,cAAIC,IAAI,GAAG,IAAX;AACA,cAAIC,KAAK,GAAG,KAAKL,QAAL,EAAZ;AACA,cAAIM,EAAE,GAAGC,UAAU,CAAC,YAAW;AAAEH,YAAAA,IAAI,CAACI,aAAL,CAAmBH,KAAnB;AAA4B,WAA1C,EAA4CF,KAA5C,CAAnB;AAEA,cAAIM,SAAS,GAAG,IAAIhB,YAAJ,EAAhB;AACAgB,UAAAA,SAAS,CAACC,IAAV,GAAiBR,IAAI,CAACQ,IAAtB;AACAD,UAAAA,SAAS,CAACE,GAAV,GAAgBT,IAAI,CAACS,GAArB;AACAF,UAAAA,SAAS,CAACG,MAAV,GAAmBN,EAAnB,CAT2C,CAW3C;;AACA,eAAKR,OAAL,CAAae,GAAb,CAAiBR,KAAjB,EAAuBI,SAAvB;AACH;;AAGOD,QAAAA,aAAa,CAACF,EAAD,EAAoB;AAAA,cAAnBA,EAAmB;AAAnBA,YAAAA,EAAmB,GAAP,CAAO;AAAA;;AACrC,cAAIJ,IAAI,GAAG,KAAKJ,OAAL,CAAagB,GAAb,CAAiBR,EAAjB,CAAX;;AACA,cAAGJ,IAAH,EAAQ;AACJ;AAAA;AAAA,sCAASa,IAAT,CAAc;AAAA;AAAA,sCAASC,aAAvB,EAAsCd,IAAtC;;AACA,iBAAKJ,OAAL,CAAamB,MAAb,CAAoBX,EAApB;AACH;AACJ;;AAGMY,QAAAA,MAAM,CAAChB,IAAD,EAAe;AACxB,cAAGA,IAAI,IAAI,IAAX,EAAgB;AACZ;AACH;;AACD,cAAII,EAAE,GAAG,CAAC,CAAV;;AACA,cAAG,OAAOJ,IAAP,IAAc,QAAjB,EAA0B;AACtBI,YAAAA,EAAE,GAAG,KAAKa,MAAL,CAAYjB,IAAZ,CAAL;AACH,WAFD,MAEK;AACDI,YAAAA,EAAE,GAAGJ,IAAL;AACH,WATuB,CAWxB;;;AACA,cAAGI,EAAE,IAAI,CAAT,EAAW;AACP,iBAAKR,OAAL,CAAamB,MAAb,CAAoBX,EAApB;;AACAc,YAAAA,YAAY,CAACd,EAAD,CAAZ,CAFO,CAGP;AACH;AAEJ;;AAIOa,QAAAA,MAAM,CAACjB,IAAD,EAAiB;AAC3B,cAAImB,UAAU,GAAG,CAAC,CAAlB;;AACA,eAAKvB,OAAL,CAAawB,OAAb,CAAqB,CAACC,KAAD,EAASC,GAAT,KAAgB;AACjC,gBAAGD,KAAK,CAACb,IAAN,IAAcR,IAAI,CAACQ,IAAnB,IAA2Ba,KAAK,CAACZ,GAAN,IAAaT,IAAI,CAACS,GAAhD,EAAoD;AAChDU,cAAAA,UAAU,GAAGG,GAAb;AACH;AACJ,WAJD;;AAMA,iBAAOH,UAAP;AACH;;AAGMI,QAAAA,OAAO,GAAO;AACjB,cAAIrB,IAAI,GAAG,IAAX;;AACA,eAAKN,OAAL,CAAawB,OAAb,CAAqB,UAASE,GAAT,EAAcD,KAAd,EAAoB;AACrCnB,YAAAA,IAAI,CAACc,MAAL,CAAYM,GAAZ;AACH,WAFD;;AAGA,eAAK1B,OAAL,CAAa4B,KAAb;AACH;;AAzEiB,O", "sourcesContent": ["\nimport { _decorator } from 'cc';\nimport { NetEvent } from \"./NetInterface\";\nimport { EventMgr } from '../../utils/EventMgr';\n\n\nexport class NetTimerData {\n    public name:string = \"\";\n    public seq:number = 0;\n    public timeId:number = 0;\n}\n\nexport class NetTimer {\n    private _tokens:any = null;\n    private _tokenId:number = 0;\n    public init(){\n        this._tokens = new Map();\n        this._tokenId = 0;\n    }\n\n    public schedule(data:any,delay:number = 0):void{\n        \n        var self = this;\n        var token = this._tokenId++;\n        var id = setTimeout(function() { self.handleTimeout(token); }, delay);\n\n        var timerData = new NetTimerData();\n        timerData.name = data.name;\n        timerData.seq = data.seq;\n        timerData.timeId = id;\n\n        // console.log(\"NetTimer token size:\",this._tokens.size,token)\n        this._tokens.set(token,timerData);\n    }\n\n\n    private handleTimeout(id:number = 0):void{\n        var data = this._tokens.get(id);\n        if(data){\n            EventMgr.emit(NetEvent.ServerTimeOut, data);\n            this._tokens.delete(id);\n        }\n    }\n\n\n    public cancel(data:any):void{\n        if(data == null){\n            return\n        }\n        var id = -1;\n        if(typeof(data)=='object'){\n            id = this.getKey(data);\n        }else{\n            id = data;\n        }\n\n        // console.log(\"NetTimer token id:\",id)\n        if(id >= 0){\n            this._tokens.delete(id);\n            clearTimeout(id);\n            // console.log(\"NetTimer token cancel:\",this._tokens.size)\n        }\n\n    }\n\n\n\n    private getKey(data:any):number{\n        var return_key = -1;\n        this._tokens.forEach((value , key) =>{\n            if(value.name == data.name && value.seq == data.seq){\n                return_key = key;\n            }    \n        });\n\n        return return_key;\n    }\n\n\n    public destroy():void{\n        var self = this;\n        this._tokens.forEach(function(key, value){\n            self.cancel(key);\n        });\n        this._tokens.clear();\n    }\n\n}\n"]}