{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts"], "names": ["_decorator", "Component", "Camera", "Node", "Vec2", "UITransform", "Vec3", "view", "MapCommand", "MapUtil", "EventMgr", "LogicEvent", "ccclass", "property", "MapLogic", "onLoad", "console", "log", "_cmd", "getInstance", "_mapCamera", "node", "parent", "getChildByName", "getComponent", "_orthoHeight", "orthoHeight", "on", "openCityAbout", "closeCityAbout", "onDestroy", "targetOff", "setTiledMap", "tiledMap", "_tiledMap", "enableCulling", "updateCulling", "uit", "_maxMapX", "width", "getVisibleSize", "_maxMapY", "height", "EventType", "MOUSE_WHEEL", "onMouseWheel", "TOUCH_START", "onTouchBegan", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "TOUCH_CANCEL", "onTouchCancel", "data", "_maxZoomRatio", "_minZoomRatio", "event", "scrollY", "getScrollY", "changeRatio", "Number", "_changeZoomRadix", "toFixed", "newZoomRatio", "Math", "min", "max", "_isTouch", "delta", "<PERSON><PERSON><PERSON><PERSON>", "x", "y", "_isMove", "pixelPoint", "position", "setPosition", "z", "setCenterMapCellPoint", "mapPixelToCellPoint", "touchLocation", "touch", "getUILocation", "touchLocation1", "viewPointToWorldPoint", "mapPoint", "worldPixelToMapCellPoint", "clickCenterPoint", "mapCellToPixelPoint", "emit", "touchMap", "moveMap", "point", "canvasNode", "cuit", "cameraWorldX", "anchorX", "cameraWorldY", "anchorY", "worldToMapPixelPoint", "pixelX", "pixelY", "scrollToMapPoint", "temp", "toCameraPoint", "pos", "clone", "proxy", "setCurCenterPoint", "TRANSFORM_CHANGED", "scheduleOnce", "curCameraPoint", "positionX", "positionY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAqBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAmBC,MAAAA,W,OAAAA,W;AAAqCC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG/GC,MAAAA,U;;AACAC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;yBAQTc,Q,WADpBF,OAAO,CAAC,UAAD,C,yBAAR,MACqBE,QADrB,SACsCb,SADtC,CACgD;AAAA;AAAA;;AAAA;;AAAA,6CAEZ,IAFY;;AAAA,8CAGb,IAHa;;AAAA,4CAId,KAJc;;AAAA,2CAKf,KALe;;AAAA,iDAOV,CAPU;;AAAA,iDAQV,GARU;;AAAA,oDASP,GATO;;AAAA,gDAUZ,GAVY;;AAAA,4CAaf,CAbe;;AAAA,4CAcf,CAde;;AAAA,iDAgBZ,IAhBY;;AAAA,gDAiBb,IAjBa;AAAA;;AAoBlCc,QAAAA,MAAM,GAAS;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ;AACA,eAAKC,IAAL,GAAY;AAAA;AAAA,wCAAWC,WAAX,EAAZ;AACA,eAAKC,UAAL,GAAkB,KAAKC,IAAL,CAAUC,MAAV,CAAiBC,cAAjB,CAAgC,YAAhC,EAA8CC,YAA9C,CAA2DtB,MAA3D,CAAlB;AAEA,eAAKuB,YAAL,GAAoB,KAAKL,UAAL,CAAgBM,WAApC;AAEA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,aAAvB,EAAsC,KAAKA,aAA3C,EAA0D,IAA1D;AACA;AAAA;AAAA,oCAASD,EAAT,CAAY;AAAA;AAAA,wCAAWE,cAAvB,EAAuC,KAAKA,cAA5C,EAA4D,IAA5D;AAEH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AAEA,eAAKb,IAAL,GAAY,IAAZ;AACH;;AAEMc,QAAAA,WAAW,CAACC,QAAD,EAA2B;AACzC,eAAKC,SAAL,GAAiBD,QAAjB;AACA,eAAKC,SAAL,CAAeC,aAAf,GAA+B,IAA/B;AAEA,eAAKC,aAAL;;AAEA,cAAIC,GAAG,GAAG,KAAKH,SAAL,CAAeb,IAAf,CAAoBG,YAApB,CAAiCnB,WAAjC,CAAV;;AACA,eAAKiC,QAAL,GAAgB,CAACD,GAAG,CAACE,KAAJ,GAAYhC,IAAI,CAACiC,cAAL,GAAsBD,KAAnC,IAA4C,GAA5D;AACA,eAAKE,QAAL,GAAgB,CAACJ,GAAG,CAACK,MAAJ,GAAanC,IAAI,CAACiC,cAAL,GAAsBE,MAApC,IAA8C,GAA9D;;AACA,eAAKR,SAAL,CAAeb,IAAf,CAAoBM,EAApB,CAAuBxB,IAAI,CAACwC,SAAL,CAAeC,WAAtC,EAAmD,KAAKC,YAAxD,EAAsE,IAAtE;;AACA,eAAKX,SAAL,CAAeb,IAAf,CAAoBM,EAApB,CAAuBxB,IAAI,CAACwC,SAAL,CAAeG,WAAtC,EAAmD,KAAKC,YAAxD,EAAsE,IAAtE;;AACA,eAAKb,SAAL,CAAeb,IAAf,CAAoBM,EAApB,CAAuBxB,IAAI,CAACwC,SAAL,CAAeK,UAAtC,EAAkD,KAAKC,WAAvD,EAAoE,IAApE;;AACA,eAAKf,SAAL,CAAeb,IAAf,CAAoBM,EAApB,CAAuBxB,IAAI,CAACwC,SAAL,CAAeO,SAAtC,EAAiD,KAAKC,UAAtD,EAAkE,IAAlE;;AACA,eAAKjB,SAAL,CAAeb,IAAf,CAAoBM,EAApB,CAAuBxB,IAAI,CAACwC,SAAL,CAAeS,YAAtC,EAAoD,KAAKC,aAAzD,EAAwE,IAAxE;AACH;;AAESzB,QAAAA,aAAa,CAAC0B,IAAD,EAAkB;AACrC,eAAKlC,UAAL,CAAgBM,WAAhB,GAA8B,KAAKD,YAAL,GAAoB,KAAK8B,aAAvD;AACH;;AAES1B,QAAAA,cAAc,GAAS;AAC7B,eAAKT,UAAL,CAAgBM,WAAhB,GAA8B,KAAKD,YAAL,GAAoB,KAAK+B,aAAvD;AACH;;AAESX,QAAAA,YAAY,CAACY,KAAD,EAA0B;AAC5CzC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ;AAEA,cAAIyC,OAAe,GAAGD,KAAK,CAACE,UAAN,EAAtB;AACA,cAAIC,WAAmB,GAAGC,MAAM,CAAC,CAACH,OAAO,GAAG,KAAKI,gBAAhB,EAAkCC,OAAlC,CAA0C,CAA1C,CAAD,CAAhC;AACA,cAAIC,YAAoB,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKV,aAAd,EAA6BS,IAAI,CAACE,GAAL,CAAS,KAAKZ,aAAd,EAA6B,KAAKnC,UAAL,CAAgBM,WAAhB,GAA4B,KAAKD,YAAjC,GAAgDmC,WAA7E,CAA7B,CAA3B;AAEA5C,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6B+C,YAA7B;AACA,eAAK5C,UAAL,CAAgBM,WAAhB,GAA8B,KAAKD,YAAL,GAAoBuC,YAAlD;AACH;;AAESf,QAAAA,WAAW,CAACQ,KAAD,EAA0B;AAC3C,cAAI,KAAKW,QAAT,EAAmB;AACf,gBAAIC,KAAW,GAAGZ,KAAK,CAACa,QAAN,EAAlB;;AACA,gBAAID,KAAK,CAACE,CAAN,IAAW,CAAX,IAAgBF,KAAK,CAACG,CAAN,IAAW,CAA/B,EAAkC;AAC9B,mBAAKC,OAAL,GAAe,IAAf;AACA,kBAAIC,UAAgB,GAAG,IAAItE,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAAvB;AACAsE,cAAAA,UAAU,CAACH,CAAX,GAAe,KAAKnD,UAAL,CAAgBC,IAAhB,CAAqBsD,QAArB,CAA8BJ,CAA9B,GAAkCF,KAAK,CAACE,CAAvD;AACAG,cAAAA,UAAU,CAACF,CAAX,GAAe,KAAKpD,UAAL,CAAgBC,IAAhB,CAAqBsD,QAArB,CAA8BH,CAA9B,GAAkCH,KAAK,CAACG,CAAvD;AACAE,cAAAA,UAAU,CAACH,CAAX,GAAeN,IAAI,CAACC,GAAL,CAAS,KAAK5B,QAAd,EAAwB2B,IAAI,CAACE,GAAL,CAAS,CAAC,KAAK7B,QAAf,EAAyBoC,UAAU,CAACH,CAApC,CAAxB,CAAf;AACAG,cAAAA,UAAU,CAACF,CAAX,GAAeP,IAAI,CAACC,GAAL,CAAS,KAAKzB,QAAd,EAAwBwB,IAAI,CAACE,GAAL,CAAS,CAAC,KAAK1B,QAAf,EAAyBiC,UAAU,CAACF,CAApC,CAAxB,CAAf;;AACA,mBAAKpD,UAAL,CAAgBC,IAAhB,CAAqBuD,WAArB,CAAiC,IAAItE,IAAJ,CAASoE,UAAU,CAACH,CAApB,EAAuBG,UAAU,CAACF,CAAlC,EAAqC,KAAKpD,UAAL,CAAgBC,IAAhB,CAAqBsD,QAArB,CAA8BE,CAAnE,CAAjC;;AACA,mBAAKC,qBAAL,CAA2B;AAAA;AAAA,sCAAQC,mBAAR,CAA4BL,UAA5B,CAA3B,EAAoEA,UAApE;AACA,mBAAKtC,aAAL;AACH;AACJ;AACJ;;AAESW,QAAAA,YAAY,CAACU,KAAD,EAA0B;AAC5C,eAAKW,QAAL,GAAgB,IAAhB;AACA,eAAKK,OAAL,GAAe,KAAf;AACH;;AAEStB,QAAAA,UAAU,CAACM,KAAD,EAA0B;AAC1C,eAAKW,QAAL,GAAgB,KAAhB;;AACA,cAAI,KAAKK,OAAL,IAAgB,KAApB,EAA2B;AACvB,gBAAIO,aAAmB,GAAGvB,KAAK,CAACwB,KAAN,CAAYC,aAAZ,EAA1B;AACA,gBAAIC,cAAc,GAAG,KAAKC,qBAAL,CAA2BJ,aAA3B,CAArB;AACA,gBAAIK,QAAc,GAAG;AAAA;AAAA,oCAAQC,wBAAR,CAAiCH,cAAjC,CAArB;AACA,gBAAII,gBAAsB,GAAG;AAAA;AAAA,oCAAQC,mBAAR,CAA4BH,QAA5B,CAA7B,CAJuB,CAKvB;AACA;;AAEA;AAAA;AAAA,sCAASI,IAAT,CAAc;AAAA;AAAA,0CAAWC,QAAzB,EAAmCL,QAAnC,EAA6CE,gBAA7C;AACH,WATD,MASO;AACH;AAAA;AAAA,sCAASE,IAAT,CAAc;AAAA;AAAA,0CAAWE,OAAzB;AACH;;AACD,eAAKlB,OAAL,GAAe,KAAf;AACH;;AAESpB,QAAAA,aAAa,CAACI,KAAD,EAA0B;AAC7C,eAAKW,QAAL,GAAgB,KAAhB;AACA,eAAKK,OAAL,GAAe,KAAf;AACH,SAnH2C,CAqH5C;;;AACUW,QAAAA,qBAAqB,CAACQ,KAAD,EAAoB;AAC/C;AAEA,cAAIC,UAAgB,GAAG,KAAKxE,IAAL,CAAUC,MAAjC;AACA,cAAIwE,IAAI,GAAGD,UAAU,CAACrE,YAAX,CAAwBnB,WAAxB,CAAX;;AACA,cAAIgC,GAAG,GAAG,KAAKH,SAAL,CAAeb,IAAf,CAAoBG,YAApB,CAAiCnB,WAAjC,CAAV;;AAGA,cAAI0F,YAAoB,GAAG1D,GAAG,CAACE,KAAJ,GAAYF,GAAG,CAAC2D,OAAhB,GAA0BzF,IAAI,CAACiC,cAAL,GAAsBD,KAAtB,GAA8BuD,IAAI,CAACE,OAA7D,GAAuE,KAAK5E,UAAL,CAAgBC,IAAhB,CAAqBsD,QAArB,CAA8BJ,CAAhI;;AACA,cAAI0B,YAAoB,GAAG5D,GAAG,CAACK,MAAJ,GAAaL,GAAG,CAAC6D,OAAjB,GAA2B3F,IAAI,CAACiC,cAAL,GAAsBE,MAAtB,GAA+BoD,IAAI,CAACI,OAA/D,GAAyE,KAAK9E,UAAL,CAAgBC,IAAhB,CAAqBsD,QAArB,CAA8BH,CAAlI;;AAEA,iBAAO,IAAIpE,IAAJ,CAASwF,KAAK,CAACrB,CAAN,GAAUwB,YAAnB,EAAiCH,KAAK,CAACpB,CAAN,GAAUyB,YAA3C,CAAP;AACH,SAlI2C,CAsI5C;;;AACUE,QAAAA,oBAAoB,CAACP,KAAD,EAAoB;AAC9C,cAAIvD,GAAG,GAAG,KAAKH,SAAL,CAAeb,IAAf,CAAoBG,YAApB,CAAiCnB,WAAjC,CAAV;;AACA,cAAI+F,MAAc,GAAGR,KAAK,CAACrB,CAAN,GAAUlC,GAAG,CAACE,KAAJ,GAAYF,GAAG,CAAC2D,OAA/C;AACA,cAAIK,MAAc,GAAGT,KAAK,CAACpB,CAAN,GAAUnC,GAAG,CAACK,MAAJ,GAAaL,GAAG,CAAC6D,OAAhD;AACA,iBAAO,IAAI9F,IAAJ,CAASgG,MAAT,EAAiBC,MAAjB,CAAP;AACH;;AAEMC,QAAAA,gBAAgB,CAACV,KAAD,EAAoB;AACvC,cAAIW,IAAI,GAAG,KAAKC,aAAL,CAAmBZ,KAAnB,CAAX;;AACA,cAAIa,GAAG,GAAG,KAAKrF,UAAL,CAAgBC,IAAhB,CAAqBsD,QAArB,CAA8B+B,KAA9B,EAAV;;AACA,cAAIhC,UAAgB,GAAG;AAAA;AAAA,kCAAQc,mBAAR,CAA4BI,KAA5B,CAAvB;AAEAa,UAAAA,GAAG,CAAClC,CAAJ,GAAQgC,IAAI,CAAChC,CAAb;AACAkC,UAAAA,GAAG,CAACjC,CAAJ,GAAQ+B,IAAI,CAAC/B,CAAb;AACA,eAAKpD,UAAL,CAAgBC,IAAhB,CAAqBsD,QAArB,GAAgC8B,GAAhC;AACA,eAAK3B,qBAAL,CAA2Bc,KAA3B,EAAkClB,UAAlC;AACA,eAAKtC,aAAL;AAEH;;AAES0C,QAAAA,qBAAqB,CAACc,KAAD,EAAclB,UAAd,EAAsC;AACjE,eAAKxD,IAAL,CAAUyF,KAAV,CAAgBC,iBAAhB,CAAkChB,KAAlC,EAAyClB,UAAzC;AACH;;AAEOtC,QAAAA,aAAa,GAAG;AACpB,cAAG,KAAKF,SAAR,EAAkB;AACd;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA,iBAAKA,SAAL,CAAeb,IAAf,CAAoBoE,IAApB,CAAyBtF,IAAI,CAACwC,SAAL,CAAekE,iBAAxC;;AACA,iBAAKC,YAAL,CAAkB,MAAI;AAClB,mBAAK5E,SAAL,CAAeb,IAAf,CAAoBoE,IAApB,CAAyBtF,IAAI,CAACwC,SAAL,CAAekE,iBAAxC;AACH,aAFD;AAIH;AAEH;;AAGKE,QAAAA,cAAc,GAAQ;AACzB,cAAIN,GAAG,GAAG,KAAKrF,UAAL,CAAgBC,IAAhB,CAAqBsD,QAA/B;AACA,iBAAO,IAAIvE,IAAJ,CAASqG,GAAG,CAAClC,CAAb,EAAgBkC,GAAG,CAACjC,CAApB,CAAP;AACH;;AAGMgC,QAAAA,aAAa,CAACZ,KAAD,EAAc;AAC9B,cAAIlB,UAAgB,GAAG;AAAA;AAAA,kCAAQc,mBAAR,CAA4BI,KAA5B,CAAvB;AACA,cAAIoB,SAAiB,GAAG/C,IAAI,CAACC,GAAL,CAAS,KAAK5B,QAAd,EAAwB2B,IAAI,CAACE,GAAL,CAAS,CAAC,KAAK7B,QAAf,EAAyBoC,UAAU,CAACH,CAApC,CAAxB,CAAxB;AACA,cAAI0C,SAAiB,GAAGhD,IAAI,CAACC,GAAL,CAAS,KAAKzB,QAAd,EAAwBwB,IAAI,CAACE,GAAL,CAAS,CAAC,KAAK1B,QAAf,EAAyBiC,UAAU,CAACF,CAApC,CAAxB,CAAxB;AACA,iBAAO,IAAIpE,IAAJ,CAAS4G,SAAT,EAAoBC,SAApB,CAAP;AACH;;AAnM2C,O", "sourcesContent": ["import { _decorator, Component, TiledMap, Camera, Node, Vec2, Event, game, UITransform, EventMouse, EventTouch, Vec3, view } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport MapCommand from \"./MapCommand\";\nimport MapUtil from \"./MapUtil\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('MapLogic')\nexport default class MapLogic extends Component {\n    protected _cmd: MapCommand;\n    protected _tiledMap: TiledMap = null;\n    protected _mapCamera: Camera = null;\n    protected _isTouch: boolean = false;\n    protected _isMove: boolean = false;\n    //地图相机缩放倍率边界\n    protected _minZoomRatio: number = 1;\n    protected _maxZoomRatio: number = 0.8;\n    protected _changeZoomRadix: number = 200;\n    protected _orthoHeight:number = 360;\n\n    //地图相机移动边界\n    protected _maxMapX: number = 1;\n    protected _maxMapY: number = 1;\n\n    protected _touchAniNode: Node = null;\n    protected _centerPoint: Vec2 = null;\n\n    \n    protected onLoad(): void {\n        console.log(\"MapLogic onLoad\");\n        this._cmd = MapCommand.getInstance();\n        this._mapCamera = this.node.parent.getChildByName(\"Map Camera\").getComponent(Camera);\n   \n        this._orthoHeight = this._mapCamera.orthoHeight;\n\n        EventMgr.on(LogicEvent.openCityAbout, this.openCityAbout, this);\n        EventMgr.on(LogicEvent.closeCityAbout, this.closeCityAbout, this);\n\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n\n        this._cmd = null;\n    }\n\n    public setTiledMap(tiledMap: TiledMap): void {\n        this._tiledMap = tiledMap;\n        this._tiledMap.enableCulling = true;\n\n        this.updateCulling();\n        \n        var uit = this._tiledMap.node.getComponent(UITransform);\n        this._maxMapX = (uit.width - view.getVisibleSize().width) * 0.5;\n        this._maxMapY = (uit.height - view.getVisibleSize().height) * 0.5;\n        this._tiledMap.node.on(Node.EventType.MOUSE_WHEEL, this.onMouseWheel, this);\n        this._tiledMap.node.on(Node.EventType.TOUCH_START, this.onTouchBegan, this);\n        this._tiledMap.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);\n        this._tiledMap.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);\n        this._tiledMap.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);\n    }\n\n    protected openCityAbout(data: any): void {\n        this._mapCamera.orthoHeight = this._orthoHeight * this._maxZoomRatio;\n    }\n\n    protected closeCityAbout(): void {\n        this._mapCamera.orthoHeight = this._orthoHeight * this._minZoomRatio;\n    }\n\n    protected onMouseWheel(event: EventMouse): void {\n        console.log(\"onMouseWheel\");\n\n        let scrollY: number = event.getScrollY();\n        let changeRatio: number = Number((scrollY / this._changeZoomRadix).toFixed(1));\n        let newZoomRatio: number = Math.min(this._minZoomRatio, Math.max(this._maxZoomRatio, this._mapCamera.orthoHeight/this._orthoHeight + changeRatio));\n\n        console.log(\"onMouseWheel:\", newZoomRatio);\n        this._mapCamera.orthoHeight = this._orthoHeight * newZoomRatio;\n    }\n\n    protected onTouchMove(event: EventTouch): void {\n        if (this._isTouch) {\n            let delta: Vec2 = event.getDelta();\n            if (delta.x != 0 || delta.y != 0) {\n                this._isMove = true;\n                let pixelPoint: Vec2 = new Vec2(0, 0);\n                pixelPoint.x = this._mapCamera.node.position.x - delta.x;\n                pixelPoint.y = this._mapCamera.node.position.y - delta.y;\n                pixelPoint.x = Math.min(this._maxMapX, Math.max(-this._maxMapX, pixelPoint.x));\n                pixelPoint.y = Math.min(this._maxMapY, Math.max(-this._maxMapY, pixelPoint.y));\n                this._mapCamera.node.setPosition(new Vec3(pixelPoint.x, pixelPoint.y, this._mapCamera.node.position.z));\n                this.setCenterMapCellPoint(MapUtil.mapPixelToCellPoint(pixelPoint), pixelPoint);\n                this.updateCulling();\n            }\n        }\n    }\n\n    protected onTouchBegan(event: EventTouch): void {\n        this._isTouch = true;\n        this._isMove = false;\n    }\n\n    protected onTouchEnd(event: EventTouch): void {\n        this._isTouch = false;\n        if (this._isMove == false) {\n            let touchLocation: Vec2 = event.touch.getUILocation();\n            let touchLocation1 = this.viewPointToWorldPoint(touchLocation);\n            let mapPoint: Vec2 = MapUtil.worldPixelToMapCellPoint(touchLocation1);\n            let clickCenterPoint: Vec2 = MapUtil.mapCellToPixelPoint(mapPoint);\n            //派发事件\n            // console.log(\"onTouchEnd:\", touchLocation1, clickCenterPoint);\n\n            EventMgr.emit(LogicEvent.touchMap, mapPoint, clickCenterPoint);\n        } else {\n            EventMgr.emit(LogicEvent.moveMap);\n        }\n        this._isMove = false;\n    }\n\n    protected onTouchCancel(event: EventTouch): void {\n        this._isTouch = false;\n        this._isMove = false;\n    }\n\n    //界面坐标转世界坐标\n    protected viewPointToWorldPoint(point: Vec2): Vec2 {\n        // console.log(\"viewPointToWorldPoint in\", point.x, point.y);\n\n        let canvasNode: Node = this.node.parent;\n        let cuit = canvasNode.getComponent(UITransform);\n        let uit = this._tiledMap.node.getComponent(UITransform);\n\n\n        let cameraWorldX: number = uit.width * uit.anchorX - view.getVisibleSize().width * cuit.anchorX + this._mapCamera.node.position.x;\n        let cameraWorldY: number = uit.height * uit.anchorY - view.getVisibleSize().height * cuit.anchorY + this._mapCamera.node.position.y;\n\n        return new Vec2(point.x + cameraWorldX, point.y + cameraWorldY);\n    }\n\n\n\n    //世界坐标转化为相对地图的像素坐标\n    protected worldToMapPixelPoint(point: Vec2): Vec2 {\n        var uit = this._tiledMap.node.getComponent(UITransform);\n        let pixelX: number = point.x - uit.width * uit.anchorX;\n        let pixelY: number = point.y - uit.height * uit.anchorY;\n        return new Vec2(pixelX, pixelY);\n    }\n\n    public scrollToMapPoint(point: Vec2): void {\n        let temp = this.toCameraPoint(point);\n        let pos = this._mapCamera.node.position.clone();\n        let pixelPoint: Vec2 = MapUtil.mapCellToPixelPoint(point);\n\n        pos.x = temp.x;\n        pos.y = temp.y;\n        this._mapCamera.node.position = pos;\n        this.setCenterMapCellPoint(point, pixelPoint);\n        this.updateCulling();\n\n    }\n\n    protected setCenterMapCellPoint(point: Vec2, pixelPoint: Vec2): void {\n        this._cmd.proxy.setCurCenterPoint(point, pixelPoint);\n    }\n\n    private updateCulling() {\n        if(this._tiledMap){\n            // let layers = this._tiledMap.getLayers();\n            // for (let index = 0; index < layers.length; index++) {\n            //     const l = layers[index];\n            //     l.updateCulling();\n            // }\n\n            // this.scheduleOnce(()=>{\n            //     for (let index = 0; index < layers.length; index++) {\n            //         const l = layers[index];\n            //         l.updateCulling();\n            //     }\n            // })\n\n            this._tiledMap.node.emit(Node.EventType.TRANSFORM_CHANGED);\n            this.scheduleOnce(()=>{\n                this._tiledMap.node.emit(Node.EventType.TRANSFORM_CHANGED);\n            })\n    \n        }\n\n     }\n\n \n    public curCameraPoint():Vec2 {\n        let pos = this._mapCamera.node.position;\n        return new Vec2(pos.x, pos.y);\n    }\n\n\n    public toCameraPoint(point: Vec2) {\n        let pixelPoint: Vec2 = MapUtil.mapCellToPixelPoint(point);\n        let positionX: number = Math.min(this._maxMapX, Math.max(-this._maxMapX, pixelPoint.x));\n        let positionY: number = Math.min(this._maxMapY, Math.max(-this._maxMapY, pixelPoint.y));\n        return new Vec2(positionX, positionY);\n    }\n}\n"]}