{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts"], "names": ["trigger", "triggerType", "effect", "effectType", "target", "targetType", "SkillOutline", "SkillLevel", "SkillConf", "SkillEffectType"], "mappings": ";;;iBAIaA,O,EAKAC,W,EAKAC,M,EAMAC,U,EAKAC,M,EAKAC,U,EAKAC,Y,EAMAC,U,EAMAC,S;;;;;;;;;;;;;;;;;;;;;;;;;;yBA3CAR,O,GAAN,MAAMA,OAAN,CAAc;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;6BAKRC,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;wBAKZC,M,GAAN,MAAMA,MAAN,CAAa;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;4BAMPC,U,GAAN,MAAMA,UAAN,CAAkB;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;wBAKZC,M,GAAN,MAAMA,MAAN,CAAa;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;4BAKPC,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;8BAKXC,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;4BAMbC,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA;;AAAA;;AAAA;AAAA,UAGI;;;AAHJ,O;;2BAMXC,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;iBAaXC,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;SAAAA,e,+BAAAA,e", "sourcesContent": ["// //技能大纲\n// //技能配置\n\nimport { _decorator } from 'cc';\nexport class trigger {\n\ttype: number\n\tdes: string\n}\n\nexport class triggerType {\n\tdes: string\n\tlist:trigger[] \n}\n\nexport class effect {\n\ttype: number\n\tdes: string\n\tisRate: boolean\n}\n\nexport class effectType  {\n\tdes: string\n\tlist:effect[] \n}\n\nexport class target {\n\ttype: number\n\tdes: string\n}\n\nexport class targetType {\n\tdes: string\n\tlist:target[] \n}\n\nexport class SkillOutline {\n\ttrigger_type: triggerType \n\teffect_type: effectType\n\ttarget_type: targetType\n}\n\nexport class SkillLevel {\n    probability: number     //发动概率\n    effect_value:number[]   //效果值\n    effect_round: number[]  //效果持续回合数\n}\n\nexport class SkillConf {\n\tcfgId: number\n\tname: string\n    des: string\n    trigger: number //发起类型\n    target: number  //目标类型\n    limit: number   //可以被武将装备上限\n    arms:number[]   //可以装备的兵种\n    include_effect: number[] //技能包括的效果\n    levels:SkillLevel[]\n}\n\n\nexport enum SkillEffectType {\n\tHurtRate = 1, //伤害率\n\tForce,\n\tDefense,\n\tStrategy,\n\tSpeed,\n\tDestroy,\n}"]}