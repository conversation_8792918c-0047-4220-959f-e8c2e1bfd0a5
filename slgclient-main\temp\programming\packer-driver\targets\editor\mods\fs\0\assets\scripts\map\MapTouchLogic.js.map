{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts"], "names": ["_decorator", "Component", "Prefab", "Node", "Vec2", "instantiate", "Vec3", "MapClickUILogic", "MapCommand", "MapUtil", "EventMgr", "LogicEvent", "ccclass", "property", "MapTouchLogic", "onLoad", "_cmd", "getInstance", "on", "touchMap", "onTouchMap", "moveMap", "onMoveMap", "onDestroy", "targetOff", "_clickUINode", "mapPoint", "clickPixelPoint", "console", "log", "x", "y", "removeClickUINode", "isVaildCellPoint", "cellId", "getIdByCellPoint", "cityData", "cityProxy", "getCity", "mapCellToPixelPoint", "showClickUINode", "buildData", "buildProxy", "getBuild", "isSysCity", "resData", "proxy", "getResData", "type", "temp", "getSysCityResData", "data", "pos", "clickUIPrefab", "parent", "touch", "setPosition", "getComponent", "setCellData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAK1DC,MAAAA,e;;AACAC,MAAAA,U;;AAEAC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OATH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;yBAYTc,a,WADpBF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACX,MAAD,C,UAGRW,QAAQ,CAACV,IAAD,C,oCALb,MACqBW,aADrB,SAC2Cb,SAD3C,CACqD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,gDAQlB,IARkB;AAAA;;AAUvCc,QAAAA,MAAM,GAAS;AACrB,eAAKC,IAAL,GAAY;AAAA;AAAA,wCAAWC,WAAX,EAAZ;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,QAAvB,EAAiC,KAAKC,UAAtC,EAAkD,IAAlD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,OAAvB,EAAgC,KAAKC,SAArC,EAAgD,IAAhD;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,eAAKR,IAAL,GAAY,IAAZ;AACA,eAAKS,YAAL,GAAoB,IAApB;AACH;;AAESL,QAAAA,UAAU,CAACM,QAAD,EAAiBC,eAAjB,EAA8C;AAC9DC,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAWH,QAAQ,CAACI,CAApB,GAAwB,GAAxB,GAA8BJ,QAAQ,CAACK,CAAvC,GAA2C,GAAvD;AACA,eAAKC,iBAAL;;AACA,cAAI;AAAA;AAAA,kCAAQC,gBAAR,CAAyBP,QAAzB,KAAsC,KAA1C,EAAiD;AAC7CE,YAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACA;AACH;;AAED,cAAIK,MAAc,GAAG;AAAA;AAAA,kCAAQC,gBAAR,CAAyBT,QAAQ,CAACI,CAAlC,EAAqCJ,QAAQ,CAACK,CAA9C,CAArB;;AACA,cAAIK,QAAqB,GAAG,KAAKpB,IAAL,CAAUqB,SAAV,CAAoBC,OAApB,CAA4BJ,MAA5B,CAA5B;;AAAgE;;AAChE,cAAIE,QAAQ,IAAI,IAAhB,EAAsB;AAClB;AACAT,YAAAA,eAAe,GAAG;AAAA;AAAA,oCAAQY,mBAAR,CAA4B,IAAInC,IAAJ,CAASgC,QAAQ,CAACN,CAAlB,EAAqBM,QAAQ,CAACL,CAA9B,CAA5B,CAAlB;AACA,iBAAKS,eAAL,CAAqBJ,QAArB,EAA+BT,eAA/B;AACA;AACH;;AAED,cAAIc,SAAuB,GAAG,KAAKzB,IAAL,CAAU0B,UAAV,CAAqBC,QAArB,CAA8BT,MAA9B,CAA9B;;AACA,cAAIO,SAAS,IAAI,IAAjB,EAAuB;AACnB,gBAAGA,SAAS,CAACG,SAAV,MAAyB,KAA5B,EAAkC;AAC9B;AACAhB,cAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBY,SAAxB;AACA,mBAAKD,eAAL,CAAqBC,SAArB,EAAgCd,eAAhC;AACA;AACH;AAEJ;;AAED,cAAIkB,OAAmB,GAAG,KAAK7B,IAAL,CAAU8B,KAAV,CAAgBC,UAAhB,CAA2Bb,MAA3B,CAA1B;;AACA,cAAIW,OAAO,CAACG,IAAR,GAAe,CAAnB,EAAsB;AAClB,gBAAIC,IAAI,GAAG;AAAA;AAAA,0CAAWhC,WAAX,GAAyB6B,KAAzB,CAA+BI,iBAA/B,CAAiDL,OAAO,CAACf,CAAzD,EAA4De,OAAO,CAACd,CAApE,CAAX;;AACA,gBAAIkB,IAAJ,EAAS;AACLtB,cAAAA,eAAe,GAAG;AAAA;AAAA,sCAAQY,mBAAR,CAA4B,IAAInC,IAAJ,CAAS6C,IAAI,CAACnB,CAAd,EAAiBmB,IAAI,CAAClB,CAAtB,CAA5B,CAAlB;AACA,kBAAIG,MAAc,GAAG;AAAA;AAAA,sCAAQC,gBAAR,CAAyBc,IAAI,CAACnB,CAA9B,EAAiCmB,IAAI,CAAClB,CAAtC,CAArB;;AACA,kBAAIU,SAAuB,GAAG,KAAKzB,IAAL,CAAU0B,UAAV,CAAqBC,QAArB,CAA8BT,MAA9B,CAA9B;;AACA,kBAAGO,SAAH,EAAa;AACT,qBAAKD,eAAL,CAAqBC,SAArB,EAAgCd,eAAhC;AACH,eAFD,MAEK;AACD,qBAAKa,eAAL,CAAqBS,IAArB,EAA2BtB,eAA3B;AACH;;AACDC,cAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsBoB,IAAtB;AACH,aAVD,MAUK;AACD,mBAAKT,eAAL,CAAqBK,OAArB,EAA8BlB,eAA9B;AACAC,cAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsBgB,OAAtB;AACH;AAEJ,WAjBD,MAiBO;AACHjB,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACH;AACJ;;AAESP,QAAAA,SAAS,GAAS;AACxB,eAAKU,iBAAL;AACH;;AAEMQ,QAAAA,eAAe,CAACW,IAAD,EAAYC,GAAZ,EAA6B;AAC/C,cAAI,KAAK3B,YAAL,IAAqB,IAAzB,EAA+B;AAC3B,iBAAKA,YAAL,GAAoBpB,WAAW,CAAC,KAAKgD,aAAN,CAA/B;AAEH;;AACD,eAAK5B,YAAL,CAAkB6B,MAAlB,GAA2B,KAAKC,KAAhC;;AACA,eAAK9B,YAAL,CAAkB+B,WAAlB,CAA8B,IAAIlD,IAAJ,CAAS8C,GAAG,CAACtB,CAAb,EAAgBsB,GAAG,CAACrB,CAApB,EAAuB,CAAvB,CAA9B;;AACA,eAAKN,YAAL,CAAkBgC,YAAlB;AAAA;AAAA,kDAAgDC,WAAhD,CAA4DP,IAA5D,EAAkEC,GAAlE;AACH;;AAEMpB,QAAAA,iBAAiB,GAAS;AAC7B,cAAI,KAAKP,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkB6B,MAAlB,GAA2B,IAA3B;AACH;AACJ;;AA3FgD,O;;;;;iBAEzB,I;;;;;;;iBAGX,I", "sourcesContent": ["import { _decorator, Component, Prefab, Node, Vec2, instantiate, Vec3 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { MapBuildData } from \"./MapBuildProxy\";\nimport { MapCityData } from \"./MapCityProxy\";\nimport MapClickUILogic from \"./MapClickUILogic\";\nimport MapCommand from \"./MapCommand\";\nimport { MapResData } from \"./MapProxy\";\nimport MapUtil from \"./MapUtil\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('MapTouchLogic')\nexport default class MapTouchLogic extends Component {\n    @property(Prefab)\n    clickUIPrefab: Prefab = null;\n\n    @property(Node)\n    touch:Node = null;\n\n    protected _cmd: MapCommand;\n    protected _clickUINode: Node = null;\n\n    protected onLoad(): void {\n        this._cmd = MapCommand.getInstance();\n        EventMgr.on(LogicEvent.touchMap, this.onTouchMap, this);\n        EventMgr.on(LogicEvent.moveMap, this.onMoveMap, this);\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._cmd = null;\n        this._clickUINode = null;\n    }\n\n    protected onTouchMap(mapPoint: Vec2, clickPixelPoint: Vec2): void {\n        console.log(\"点击区域 (\" + mapPoint.x + \",\" + mapPoint.y + \")\");\n        this.removeClickUINode();\n        if (MapUtil.isVaildCellPoint(mapPoint) == false) {\n            console.log(\"点击到无效区域\");\n            return;\n        }\n\n        let cellId: number = MapUtil.getIdByCellPoint(mapPoint.x, mapPoint.y);\n        let cityData: MapCityData = this._cmd.cityProxy.getCity(cellId);;\n        if (cityData != null) {\n            //代表点击的是城市\n            clickPixelPoint = MapUtil.mapCellToPixelPoint(new Vec2(cityData.x, cityData.y));\n            this.showClickUINode(cityData, clickPixelPoint);\n            return;\n        }\n\n        let buildData: MapBuildData = this._cmd.buildProxy.getBuild(cellId);\n        if (buildData != null) {\n            if(buildData.isSysCity() == false){\n                //代表点击被占领的区域\n                console.log(\"点击被占领的区域\", buildData);\n                this.showClickUINode(buildData, clickPixelPoint);\n                return;\n            }\n\n        }\n\n        let resData: MapResData = this._cmd.proxy.getResData(cellId);\n        if (resData.type > 0) {\n            var temp = MapCommand.getInstance().proxy.getSysCityResData(resData.x, resData.y);\n            if (temp){\n                clickPixelPoint = MapUtil.mapCellToPixelPoint(new Vec2(temp.x, temp.y));\n                let cellId: number = MapUtil.getIdByCellPoint(temp.x, temp.y);\n                let buildData: MapBuildData = this._cmd.buildProxy.getBuild(cellId);\n                if(buildData){\n                    this.showClickUINode(buildData, clickPixelPoint);\n                }else{\n                    this.showClickUINode(temp, clickPixelPoint);\n                }\n                console.log(\"点击野外城池\", temp);\n            }else{\n                this.showClickUINode(resData, clickPixelPoint);\n                console.log(\"点击野外区域\", resData);\n            }\n           \n        } else {\n            console.log(\"点击山脉河流区域\");\n        }\n    }\n\n    protected onMoveMap(): void {\n        this.removeClickUINode();\n    }\n\n    public showClickUINode(data: any, pos: Vec2): void {\n        if (this._clickUINode == null) {\n            this._clickUINode = instantiate(this.clickUIPrefab);\n\n        }\n        this._clickUINode.parent = this.touch;\n        this._clickUINode.setPosition(new Vec3(pos.x, pos.y, 0));\n        this._clickUINode.getComponent(MapClickUILogic).setCellData(data, pos);\n    }\n\n    public removeClickUINode(): void {\n        if (this._clickUINode) {\n            this._clickUINode.parent = null;\n        }\n    }\n}\n"]}