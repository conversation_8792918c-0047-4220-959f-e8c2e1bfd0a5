import { _decorator, Component, Node, EditBox, Button, Label, Color, Toggle } from 'cc';
import { AudioManager } from '../common/AudioManager';
import { EventMgr } from '../utils/EventMgr';
import { LogicEvent } from '../common/LogicEvent';

const { ccclass, property } = _decorator;

/**
 * 登录对话框控制器
 * 处理手机号登录、验证码获取等功能
 */
@ccclass('LoginDialogController')
export class LoginDialogController extends Component {
    
    @property(EditBox)
    phoneInput: EditBox = null!;
    
    @property(EditBox)
    codeInput: EditBox = null!;
    
    @property(Button)
    getCodeButton: Button = null!;
    
    @property(Button)
    loginButton: Button = null!;
    
    @property(Node)
    maskNode: Node = null!;
    
    @property(Toggle)
    agreementToggle: Toggle = null!;
    
    @property(Label)
    phoneStatusLabel: Label = null!;
    
    @property(Label)
    codeStatusLabel: Label = null!;
    
    private codeCountdown: number = 0;
    private countdownTimer: number = 0;

    onLoad() {
        console.log('[LoginDialogController] 登录对话框控制器加载');
        this.initializeDialog();
        this.setupEventListeners();
    }

    start() {
        console.log('[LoginDialogController] 登录对话框控制器启动');
    }

    /**
     * 初始化对话框
     */
    private initializeDialog(): void {
        // 隐藏提示标签
        if (this.phoneStatusLabel) {
            this.phoneStatusLabel.node.active = false;
        }
        if (this.codeStatusLabel) {
            this.codeStatusLabel.node.active = false;
        }
        
        // 设置输入框限制
        if (this.phoneInput) {
            this.phoneInput.maxLength = 11;
        }
        if (this.codeInput) {
            this.codeInput.maxLength = 6;
        }
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        if (this.getCodeButton) {
            this.getCodeButton.node.on(Button.EventType.CLICK, this.onGetCodeButtonClick, this);
        }

        if (this.loginButton) {
            this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
        }

        // 设置遮罩点击事件（点击对话框外区域关闭）
        if (this.maskNode) {
            this.maskNode.on(Node.EventType.TOUCH_END, this.onMaskClick, this);
        }

        if (this.phoneInput) {
            this.phoneInput.node.on('text-changed', this.onPhoneInputChanged, this);
        }

        if (this.codeInput) {
            this.codeInput.node.on('text-changed', this.onCodeInputChanged, this);
        }
    }

    /**
     * 手机号输入变化事件
     */
    private onPhoneInputChanged(): void {
        const phone = this.phoneInput.string;
        
        // 只允许输入数字
        const numericPhone = phone.replace(/[^0-9]/g, '');
        if (numericPhone !== phone) {
            this.phoneInput.string = numericPhone;
        }
        
        // 隐藏手机号状态提示
        if (this.phoneStatusLabel) {
            this.phoneStatusLabel.node.active = false;
        }
    }

    /**
     * 验证码输入变化事件
     */
    private onCodeInputChanged(): void {
        const code = this.codeInput.string;
        
        // 只允许输入数字
        const numericCode = code.replace(/[^0-9]/g, '');
        if (numericCode !== code) {
            this.codeInput.string = numericCode;
        }
        
        // 隐藏验证码状态提示
        if (this.codeStatusLabel) {
            this.codeStatusLabel.node.active = false;
        }
    }

    /**
     * 获取验证码按钮点击事件
     */
    private onGetCodeButtonClick(): void {
        console.log('[LoginDialogController] 获取验证码按钮被点击');
        
        AudioManager.instance.playClick();
        
        const phone = this.phoneInput.string.trim();
        
        // 验证手机号
        if (!this.validatePhone(phone)) {
            return;
        }
        
        // 开始倒计时
        this.startCodeCountdown();
        
        // 这里应该调用获取验证码的API
        console.log(`[LoginDialogController] 向手机号 ${phone} 发送验证码`);
        EventMgr.emit(LogicEvent.showToast, `验证码已发送到 ${phone}`);
    }

    /**
     * 登录按钮点击事件
     */
    private onLoginButtonClick(): void {
        console.log('[LoginDialogController] 登录按钮被点击');
        
        AudioManager.instance.playClick();
        
        const phone = this.phoneInput.string.trim();
        const code = this.codeInput.string.trim();
        
        // 验证输入
        if (!this.validatePhone(phone) || !this.validateCode(code) || !this.validateAgreement()) {
            return;
        }
        
        // 执行登录
        this.performLogin(phone, code);
    }

    /**
     * 遮罩点击事件（点击对话框外区域关闭）
     */
    private onMaskClick(): void {
        console.log('[LoginDialogController] 点击对话框外区域，关闭对话框');

        AudioManager.instance.playClick();
        this.node.destroy();
    }

    /**
     * 验证手机号
     */
    private validatePhone(phone: string): boolean {
        if (!phone) {
            this.showPhoneStatus('请输入手机号', new Color(255, 100, 100, 255));
            return false;
        }
        
        if (phone.length !== 11) {
            this.showPhoneStatus('手机号必须是11位数字', new Color(255, 100, 100, 255));
            return false;
        }
        
        if (!/^1[3-9]\d{9}$/.test(phone)) {
            this.showPhoneStatus('请输入正确的手机号格式', new Color(255, 100, 100, 255));
            return false;
        }
        
        return true;
    }

    /**
     * 验证验证码
     */
    private validateCode(code: string): boolean {
        if (!code) {
            this.showCodeStatus('请输入验证码', new Color(255, 100, 100, 255));
            return false;
        }
        
        if (code.length !== 6) {
            this.showCodeStatus('验证码必须是6位数字', new Color(255, 100, 100, 255));
            return false;
        }
        
        return true;
    }

    /**
     * 验证协议勾选
     */
    private validateAgreement(): boolean {
        if (!this.agreementToggle || !this.agreementToggle.isChecked) {
            EventMgr.emit(LogicEvent.showToast, '请先同意用户协议');
            return false;
        }
        return true;
    }

    /**
     * 执行登录
     */
    private performLogin(phone: string, code: string): void {
        console.log(`[LoginDialogController] 开始登录: 手机号=${phone}, 验证码=${code}`);
        
        // 这里应该调用登录API
        // 模拟登录成功
        EventMgr.emit(LogicEvent.showToast, '登录成功！');
        
        // 关闭对话框
        this.scheduleOnce(() => {
            this.node.destroy();
        }, 1);
    }

    /**
     * 显示手机号状态
     */
    private showPhoneStatus(message: string, color: Color): void {
        if (this.phoneStatusLabel) {
            this.phoneStatusLabel.string = message;
            this.phoneStatusLabel.color = color;
            this.phoneStatusLabel.node.active = true;
        }
    }

    /**
     * 显示验证码状态
     */
    private showCodeStatus(message: string, color: Color): void {
        if (this.codeStatusLabel) {
            this.codeStatusLabel.string = message;
            this.codeStatusLabel.color = color;
            this.codeStatusLabel.node.active = true;
        }
    }

    /**
     * 开始验证码倒计时
     */
    private startCodeCountdown(): void {
        this.codeCountdown = 60;
        this.updateCodeButtonText();
        
        this.countdownTimer = setInterval(() => {
            this.codeCountdown--;
            this.updateCodeButtonText();
            
            if (this.codeCountdown <= 0) {
                this.stopCodeCountdown();
            }
        }, 1000);
    }

    /**
     * 停止验证码倒计时
     */
    private stopCodeCountdown(): void {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = 0;
        }
        this.codeCountdown = 0;
        this.updateCodeButtonText();
    }

    /**
     * 更新获取验证码按钮文本
     */
    private updateCodeButtonText(): void {
        if (!this.getCodeButton) return;
        
        const label = this.getCodeButton.node.getComponentInChildren(Label);
        if (label) {
            if (this.codeCountdown > 0) {
                label.string = `${this.codeCountdown}秒后重试`;
                this.getCodeButton.interactable = false;
            } else {
                label.string = '获取验证码';
                this.getCodeButton.interactable = true;
            }
        }
    }

    onDestroy() {
        // 清理倒计时
        this.stopCodeCountdown();

        // 清理事件监听器
        if (this.getCodeButton) {
            this.getCodeButton.node.off(Button.EventType.CLICK, this.onGetCodeButtonClick, this);
        }
        if (this.loginButton) {
            this.loginButton.node.off(Button.EventType.CLICK, this.onLoginButtonClick, this);
        }
        if (this.maskNode) {
            this.maskNode.off(Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        if (this.phoneInput) {
            this.phoneInput.node.off('text-changed', this.onPhoneInputChanged, this);
        }
        if (this.codeInput) {
            this.codeInput.node.off('text-changed', this.onCodeInputChanged, this);
        }

        console.log('[LoginDialogController] 登录对话框控制器销毁');
    }
}
