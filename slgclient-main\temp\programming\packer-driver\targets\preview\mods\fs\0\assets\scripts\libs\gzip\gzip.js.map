{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts"], "names": ["putByte", "n", "arr", "push", "putShort", "putLong", "putString", "s", "i", "len", "length", "charCodeAt", "readByte", "shift", "readShort", "readLong", "n1", "n2", "Math", "pow", "readString", "char<PERSON><PERSON>", "String", "fromCharCode", "join", "readBytes", "ret", "zip", "data", "options", "flags", "level", "crc", "out", "DEFAULT_LEVEL", "Array", "prototype", "map", "call", "char", "ID1", "ID2", "compressionMethods", "name", "possibleFlags", "timestamp", "parseInt", "Date", "now", "osMap", "os", "substring", "lastIndexOf", "deflate", "for<PERSON>ach", "byte", "unzip", "slice", "t", "compressionMethod", "mtime", "xFlags", "key", "size", "res", "Object", "keys", "some", "inflate", "splice", "crc32"], "mappings": ";;;;;AA0CA,WAASA,OAAT,CAAiBC,CAAjB,EAAoBC,GAApB,EAAyB;AACxBA,IAAAA,GAAG,CAACC,IAAJ,CAASF,CAAC,GAAG,IAAb;AACA,G,CAED;;;AACA,WAASG,QAAT,CAAkBH,CAAlB,EAAqBC,GAArB,EAA0B;AACzBA,IAAAA,GAAG,CAACC,IAAJ,CAASF,CAAC,GAAG,IAAb;AACAC,IAAAA,GAAG,CAACC,IAAJ,CAASF,CAAC,KAAK,CAAf;AACA,G,CAED;;;AACA,WAASI,OAAT,CAAiBJ,CAAjB,EAAoBC,GAApB,EAAyB;AACxBE,IAAAA,QAAQ,CAACH,CAAC,GAAG,MAAL,EAAaC,GAAb,CAAR;AACAE,IAAAA,QAAQ,CAACH,CAAC,KAAK,EAAP,EAAWC,GAAX,CAAR;AACA;;AAED,WAASI,SAAT,CAAmBC,CAAnB,EAAsBL,GAAtB,EAA2B;AAC1B,QAAIM,CAAJ;AAAA,QAAOC,GAAG,GAAGF,CAAC,CAACG,MAAf;;AACA,SAAKF,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGC,GAAhB,EAAqBD,CAAC,IAAI,CAA1B,EAA6B;AAC5BR,MAAAA,OAAO,CAACO,CAAC,CAACI,UAAF,CAAaH,CAAb,CAAD,EAAkBN,GAAlB,CAAP;AACA;AACD;;AAED,WAASU,QAAT,CAAkBV,GAAlB,EAAuB;AACtB,WAAOA,GAAG,CAACW,KAAJ,EAAP;AACA;;AAED,WAASC,SAAT,CAAmBZ,GAAnB,EAAwB;AACvB,WAAOA,GAAG,CAACW,KAAJ,KAAeX,GAAG,CAACW,KAAJ,MAAe,CAArC;AACA;;AAED,WAASE,QAAT,CAAkBb,GAAlB,EAAuB;AACtB,QAAIc,EAAE,GAAGF,SAAS,CAACZ,GAAD,CAAlB;AAAA,QACCe,EAAE,GAAGH,SAAS,CAACZ,GAAD,CADf,CADsB,CAItB;AACA;AACA;;AACA,QAAIe,EAAE,GAAG,KAAT,EAAgB;AACfA,MAAAA,EAAE,IAAI,KAAN;AAEA,aAAO,CAAEA,EAAE,IAAI,EAAP,GAAaD,EAAd,IAAoB,QAAQE,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,EAAZ,CAAnC;AACA;;AAED,WAAQF,EAAE,IAAI,EAAP,GAAaD,EAApB;AACA;;AAED,WAASI,UAAT,CAAoBlB,GAApB,EAAyB;AACxB,QAAImB,OAAO,GAAG,EAAd,CADwB,CAGxB;;AACA,WAAOnB,GAAG,CAAC,CAAD,CAAH,KAAW,CAAlB,EAAqB;AACpBmB,MAAAA,OAAO,CAAClB,IAAR,CAAamB,MAAM,CAACC,YAAP,CAAoBrB,GAAG,CAACW,KAAJ,EAApB,CAAb;AACA,KANuB,CAQxB;;;AACAX,IAAAA,GAAG,CAACW,KAAJ,GATwB,CAWxB;;AACA,WAAOQ,OAAO,CAACG,IAAR,CAAa,EAAb,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASC,SAAT,CAAmBvB,GAAnB,EAAwBD,CAAxB,EAA2B;AAC1B,QAAIO,CAAJ;AAAA,QAAOkB,GAAG,GAAG,EAAb;;AACA,SAAKlB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGP,CAAhB,EAAmBO,CAAC,IAAI,CAAxB,EAA2B;AAC1BkB,MAAAA,GAAG,CAACvB,IAAJ,CAASD,GAAG,CAACW,KAAJ,EAAT;AACA;;AAED,WAAOa,GAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;;;AACO,WAASC,GAAT,CAAaC,IAAb,EAAmBC,OAAnB,EAA4B;AAClC,QAAIC,KAAK,GAAG,CAAZ;AAAA,QACCC,KADD;AAAA,QAECC,GAFD;AAAA,QAEMC,GAAG,GAAG,EAFZ;;AAIA,QAAI,CAACJ,OAAL,EAAc;AACbA,MAAAA,OAAO,GAAG,EAAV;AACA;;AACDE,IAAAA,KAAK,GAAGF,OAAO,CAACE,KAAR,IAAiBG,aAAzB;;AAEA,QAAI,OAAON,IAAP,KAAgB,QAApB,EAA8B;AAC7BA,MAAAA,IAAI,GAAGO,KAAK,CAACC,SAAN,CAAgBC,GAAhB,CAAoBC,IAApB,CAAyBV,IAAzB,EAA+B,UAAUW,IAAV,EAAgB;AACrD,eAAOA,IAAI,CAAC5B,UAAL,CAAgB,CAAhB,CAAP;AACA,OAFM,CAAP;AAGA,KAdiC,CAgBlC;;;AACAX,IAAAA,OAAO,CAACwC,GAAD,EAAMP,GAAN,CAAP;AACAjC,IAAAA,OAAO,CAACyC,GAAD,EAAMR,GAAN,CAAP;AAEAjC,IAAAA,OAAO,CAAC0C,kBAAkB,CAAC,SAAD,CAAnB,EAAgCT,GAAhC,CAAP;;AAEA,QAAIJ,OAAO,CAACc,IAAZ,EAAkB;AACjBb,MAAAA,KAAK,IAAIc,aAAa,CAAC,OAAD,CAAtB;AACA;;AAED5C,IAAAA,OAAO,CAAC8B,KAAD,EAAQG,GAAR,CAAP;AACA5B,IAAAA,OAAO,CAACwB,OAAO,CAACgB,SAAR,IAAqBC,QAAQ,CAACC,IAAI,CAACC,GAAL,KAAa,IAAd,EAAoB,EAApB,CAA9B,EAAuDf,GAAvD,CAAP,CA3BkC,CA6BlC;;AACA,QAAIF,KAAK,KAAK,CAAd,EAAiB;AAChB;AACA/B,MAAAA,OAAO,CAAC,CAAD,EAAIiC,GAAJ,CAAP;AACA,KAHD,MAGO,IAAIF,KAAK,KAAK,CAAd,EAAiB;AACvB;AACA/B,MAAAA,OAAO,CAAC,CAAD,EAAIiC,GAAJ,CAAP;AACA,KAHM,MAGA;AACNjC,MAAAA,OAAO,CAAC,CAAD,EAAIiC,GAAJ,CAAP;AACA,KAtCiC,CAwClC;;;AACAjC,IAAAA,OAAO,CAACiD,KAAK,CAACC,EAAD,CAAN,EAAYjB,GAAZ,CAAP;;AAEA,QAAIJ,OAAO,CAACc,IAAZ,EAAkB;AACjB;AACArC,MAAAA,SAAS,CAACuB,OAAO,CAACc,IAAR,CAAaQ,SAAb,CAAuBtB,OAAO,CAACc,IAAR,CAAaS,WAAb,CAAyB,GAAzB,IAAgC,CAAvD,CAAD,EAA4DnB,GAA5D,CAAT,CAFiB,CAIjB;;AACAjC,MAAAA,OAAO,CAAC,CAAD,EAAIiC,GAAJ,CAAP;AACA;;AAEDoB,IAAAA,OAAO,CAACA,OAAR,CAAgBzB,IAAhB,EAAsBG,KAAtB,EAA6BuB,OAA7B,CAAqC,UAAUC,IAAV,EAAgB;AACpDvD,MAAAA,OAAO,CAACuD,IAAD,EAAOtB,GAAP,CAAP;AACA,KAFD;AAIA5B,IAAAA,OAAO,CAACyC,QAAQ,CAAC;AAAA;AAAA,wBAAMlB,IAAN,CAAD,EAAc,EAAd,CAAT,EAA4BK,GAA5B,CAAP;AACA5B,IAAAA,OAAO,CAACuB,IAAI,CAAClB,MAAN,EAAcuB,GAAd,CAAP;AAEA,WAAOA,GAAP;AACA;;AAEM,WAASuB,KAAT,CAAe5B,IAAf,EAAqB;AAC5B;AACC,QAAI1B,GAAG,GAAGiC,KAAK,CAACC,SAAN,CAAgBqB,KAAhB,CAAsBnB,IAAtB,CAA2BV,IAA3B,EAAiC,CAAjC,CAAV;AAAA,QACA8B,CADA;AAAA,QAEAC,iBAFA;AAAA,QAGA7B,KAHA;AAAA,QAIA8B,KAJA;AAAA,QAKAC,MALA;AAAA,QAMAC,GANA;AAAA,QAOAZ,EAPA;AAAA,QAQAlB,GARA;AAAA,QASA+B,IATA;AAAA,QAUAC,GAVA,CAF2B,CAc3B;;AACA,QAAIpD,QAAQ,CAACV,GAAD,CAAR,KAAkBsC,GAAlB,IAAyB5B,QAAQ,CAACV,GAAD,CAAR,KAAkBuC,GAA/C,EAAoD;AACnD,YAAM,iBAAN;AACA;;AAEDiB,IAAAA,CAAC,GAAG9C,QAAQ,CAACV,GAAD,CAAZ;AACAwD,IAAAA,CAAC,GAAGO,MAAM,CAACC,IAAP,CAAYxB,kBAAZ,EAAgCyB,IAAhC,CAAqC,UAAUL,GAAV,EAAe;AACvDH,MAAAA,iBAAiB,GAAGG,GAApB;AACA,aAAOpB,kBAAkB,CAACoB,GAAD,CAAlB,KAA4BJ,CAAnC;AACA,KAHG,CAAJ;;AAKA,QAAI,CAACA,CAAL,EAAQ;AACP,YAAM,gCAAN;AACA;;AAED5B,IAAAA,KAAK,GAAGlB,QAAQ,CAACV,GAAD,CAAhB;AACA0D,IAAAA,KAAK,GAAG7C,QAAQ,CAACb,GAAD,CAAhB;AACA2D,IAAAA,MAAM,GAAGjD,QAAQ,CAACV,GAAD,CAAjB;AACAwD,IAAAA,CAAC,GAAG9C,QAAQ,CAACV,GAAD,CAAZ;AACA+D,IAAAA,MAAM,CAACC,IAAP,CAAYjB,KAAZ,EAAmBkB,IAAnB,CAAwB,UAAUL,GAAV,EAAe;AACtC,UAAIb,KAAK,CAACa,GAAD,CAAL,KAAeJ,CAAnB,EAAsB;AACrBR,QAAAA,EAAE,GAAGY,GAAL;AACA,eAAO,IAAP;AACA;AACD,KALD,EAjC2B,CAwC3B;;AACA,QAAIhC,KAAK,GAAGc,aAAa,CAAC,QAAD,CAAzB,EAAqC;AACpCc,MAAAA,CAAC,GAAG5C,SAAS,CAACZ,GAAD,CAAb;AACAuB,MAAAA,SAAS,CAACvB,GAAD,EAAMwD,CAAN,CAAT;AACA,KA5C0B,CA8C3B;;;AACA,QAAI5B,KAAK,GAAGc,aAAa,CAAC,OAAD,CAAzB,EAAoC;AACnCxB,MAAAA,UAAU,CAAClB,GAAD,CAAV;AACA,KAjD0B,CAmD3B;;;AACA,QAAI4B,KAAK,GAAGc,aAAa,CAAC,UAAD,CAAzB,EAAuC;AACtCxB,MAAAA,UAAU,CAAClB,GAAD,CAAV;AACA,KAtD0B,CAwD3B;;;AACA,QAAI4B,KAAK,GAAGc,aAAa,CAAC,OAAD,CAAzB,EAAoC;AACnC9B,MAAAA,SAAS,CAACZ,GAAD,CAAT;AACA;;AAED,QAAIyD,iBAAiB,KAAK,SAA1B,EAAqC;AACpC;AACA;AACAK,MAAAA,GAAG,GAAGX,OAAO,CAACe,OAAR,CAAgBlE,GAAG,CAACmE,MAAJ,CAAW,CAAX,EAAcnE,GAAG,CAACQ,MAAJ,GAAa,CAA3B,CAAhB,CAAN;AACA;;AAED,QAAIoB,KAAK,GAAGc,aAAa,CAAC,OAAD,CAAzB,EAAoC;AACnCoB,MAAAA,GAAG,GAAG7B,KAAK,CAACC,SAAN,CAAgBC,GAAhB,CAAoBC,IAApB,CAAyB0B,GAAzB,EAA8B,UAAUT,IAAV,EAAgB;AACnD,eAAOjC,MAAM,CAACC,YAAP,CAAoBgC,IAApB,CAAP;AACA,OAFK,EAEH/B,IAFG,CAEE,EAFF,CAAN;AAGA;;AAEDQ,IAAAA,GAAG,GAAGjB,QAAQ,CAACb,GAAD,CAAR,KAAkB,CAAxB;;AACA,QAAI8B,GAAG,KAAKc,QAAQ,CAAC;AAAA;AAAA,wBAAMkB,GAAN,CAAD,EAAa,EAAb,CAApB,EAAsC;AACrC,YAAM,yBAAN;AACA;;AAEDD,IAAAA,IAAI,GAAGhD,QAAQ,CAACb,GAAD,CAAf;;AACA,QAAI6D,IAAI,KAAKC,GAAG,CAACtD,MAAjB,EAAyB;AACxB,YAAM,uCAAN;AACA;;AAED,WAAOsD,GAAP;AACA;;;;;;;SAjJerC,G;WA6DA6B;;;;;;;;;AAxLPc,MAAAA,K,iBAAAA,K;;AACGjB,MAAAA,O;;;;;;;AAEZ;AACIb,MAAAA,G,GAAM,I;AACVC,MAAAA,G,GAAM,I;AACNC,MAAAA,kB,GAAqB;AACpB,mBAAW;AADS,O;AAGrBE,MAAAA,a,GAAgB;AACf,iBAAS,IADM;AAEf,iBAAS,IAFM;AAGf,kBAAU,IAHK;AAIf,iBAAS,IAJM;AAKf,oBAAY;AALG,O;AAOhBK,MAAAA,K,GAAQ;AACP,eAAO,CADA;AACG;AACV,iBAAS,CAFF;AAEK;AACZ,eAAO,CAHA;AAGG;AACV,gBAAQ,CAJD;AAII;AACX,kBAAU,CALH;AAKM;AACb,iBAAS,CANF;AAMK;AACZ,gBAAQ,CAPD;AAOI;AACX,qBAAa,CARN;AAQS;AAChB,oBAAY,CATL;AASQ;AACf,gBAAQ,CAVD;AAUI;AACX,mBAAW,EAXJ;AAWQ;AACf,gBAAQ,EAZD;AAYK;AACZ,gBAAQ,EAbD;AAaK;AACZ,iBAAS,EAdF;AAcM;AACb,gBAAQ,EAfD;AAeK;AACZ,eAAO,EAhBA;AAgBI;AACX,gBAAQ,EAjBD;AAiBK;AACZ,kBAAU,EAlBH;AAkBO;AACd,iBAAS,EAnBF,CAmBK;;AAnBL,O;AAqBRC,MAAAA,E,GAAK,M;AACLhB,MAAAA,a,GAAgB,C", "sourcesContent": ["\n\nimport { crc32 } from \"./crc32\";\nimport * as deflate from \"./deflate-js\";\n\n// magic numbers marking this file as GZIP\nvar ID1 = 0x1F,\nID2 = 0x8B,\ncompressionMethods = {\n\t'deflate': 8\n},\npossibleFlags = {\n\t'FTEXT': 0x01,\n\t'FHCRC': 0x02,\n\t'FEXTRA': 0x04,\n\t'FNAME': 0x08,\n\t'FCOMMENT': 0x10\n},\nosMap = {\n\t'fat': 0, // FAT file system (DOS, OS/2, NT) + PKZIPW 2.50 VFAT, NTFS\n\t'amiga': 1, // Amiga\n\t'vmz': 2, // VMS (VAX or Alpha AXP)\n\t'unix': 3, // Unix\n\t'vm/cms': 4, // VM/CMS\n\t'atari': 5, // Atari\n\t'hpfs': 6, // HPFS file system (OS/2, NT 3.x)\n\t'macintosh': 7, // Macintosh\n\t'z-system': 8, // Z-System\n\t'cplm': 9, // CP/M\n\t'tops-20': 10, // TOPS-20\n\t'ntfs': 11, // NTFS file system (NT)\n\t'qdos': 12, // SMS/QDOS\n\t'acorn': 13, // Acorn RISC OS\n\t'vfat': 14, // VFAT file system (Win95, NT)\n\t'vms': 15, // MVS (code also taken for PRIMOS)\n\t'beos': 16, // BeOS (BeBox or PowerMac)\n\t'tandem': 17, // Tandem/NSK\n\t'theos': 18 // THEOS\n},\nos = 'unix',\nDEFAULT_LEVEL = 6;\n\nfunction putByte(n, arr) {\n\tarr.push(n & 0xFF);\n}\n\n// LSB first\nfunction putShort(n, arr) {\n\tarr.push(n & 0xFF);\n\tarr.push(n >>> 8);\n}\n\n// LSB first\nfunction putLong(n, arr) {\n\tputShort(n & 0xffff, arr);\n\tputShort(n >>> 16, arr);\n}\n\nfunction putString(s, arr) {\n\tvar i, len = s.length;\n\tfor (i = 0; i < len; i += 1) {\n\t\tputByte(s.charCodeAt(i), arr);\n\t}\n}\n\nfunction readByte(arr) {\n\treturn arr.shift();\n}\n\nfunction readShort(arr) {\n\treturn arr.shift() | (arr.shift() << 8);\n}\n\nfunction readLong(arr) {\n\tvar n1 = readShort(arr),\n\t\tn2 = readShort(arr);\n\n\t// JavaScript can't handle bits in the position 32\n\t// we'll emulate this by removing the left-most bit (if it exists)\n\t// and add it back in via multiplication, which does work\n\tif (n2 > 32768) {\n\t\tn2 -= 32768;\n\n\t\treturn ((n2 << 16) | n1) + 32768 * Math.pow(2, 16);\n\t}\n\n\treturn (n2 << 16) | n1;\n}\n\nfunction readString(arr) {\n\tvar charArr = [];\n\n\t// turn all bytes into chars until the terminating null\n\twhile (arr[0] !== 0) {\n\t\tcharArr.push(String.fromCharCode(arr.shift()));\n\t}\n\n\t// throw away terminating null\n\tarr.shift();\n\n\t// join all characters into a cohesive string\n\treturn charArr.join('');\n}\n\n/*\n* Reads n number of bytes and return as an array.\n*\n* @param arr- Array of bytes to read from\n* @param n- Number of bytes to read\n*/\nfunction readBytes(arr, n) {\n\tvar i, ret = [];\n\tfor (i = 0; i < n; i += 1) {\n\t\tret.push(arr.shift());\n\t}\n\n\treturn ret;\n}\n\n/*\n* ZIPs a file in GZIP format. The format is as given by the spec, found at:\n* http://www.gzip.org/zlib/rfc-gzip.html\n*\n* Omitted parts in this implementation:\n*/\nexport function zip(data, options) {\n\tvar flags = 0,\n\t\tlevel,\n\t\tcrc, out = [];\n\n\tif (!options) {\n\t\toptions = {};\n\t}\n\tlevel = options.level || DEFAULT_LEVEL;\n\n\tif (typeof data === 'string') {\n\t\tdata = Array.prototype.map.call(data, function (char) {\n\t\t\treturn char.charCodeAt(0);\n\t\t});\n\t}\n\n\t// magic number marking this file as GZIP\n\tputByte(ID1, out);\n\tputByte(ID2, out);\n\n\tputByte(compressionMethods['deflate'], out);\n\n\tif (options.name) {\n\t\tflags |= possibleFlags['FNAME'];\n\t}\n\n\tputByte(flags, out);\n\tputLong(options.timestamp || parseInt(Date.now() / 1000, 10), out);\n\n\t// put deflate args (extra flags)\n\tif (level === 1) {\n\t\t// fastest algorithm\n\t\tputByte(4, out);\n\t} else if (level === 9) {\n\t\t// maximum compression (fastest algorithm)\n\t\tputByte(2, out);\n\t} else {\n\t\tputByte(0, out);\n\t}\n\n\t// OS identifier\n\tputByte(osMap[os], out);\n\n\tif (options.name) {\n\t\t// ignore the directory part\n\t\tputString(options.name.substring(options.name.lastIndexOf('/') + 1), out);\n\n\t\t// terminating null\n\t\tputByte(0, out);\n\t}\n\n\tdeflate.deflate(data, level).forEach(function (byte) {\n\t\tputByte(byte, out);\n\t});\n\n\tputLong(parseInt(crc32(data), 16), out);\n\tputLong(data.length, out);\n\n\treturn out;\n}\n\nexport function unzip(data) {\n// start with a copy of the array\n\tvar arr = Array.prototype.slice.call(data, 0),\n\tt,\n\tcompressionMethod,\n\tflags,\n\tmtime,\n\txFlags,\n\tkey,\n\tos,\n\tcrc,\n\tsize,\n\tres;\n\n\t// check the first two bytes for the magic numbers\n\tif (readByte(arr) !== ID1 || readByte(arr) !== ID2) {\n\t\tthrow 'Not a GZIP file';\n\t}\n\n\tt = readByte(arr);\n\tt = Object.keys(compressionMethods).some(function (key) {\n\t\tcompressionMethod = key;\n\t\treturn compressionMethods[key] === t;\n\t});\n\n\tif (!t) {\n\t\tthrow 'Unsupported compression method';\n\t}\n\n\tflags = readByte(arr);\n\tmtime = readLong(arr);\n\txFlags = readByte(arr);\n\tt = readByte(arr);\n\tObject.keys(osMap).some(function (key) {\n\t\tif (osMap[key] === t) {\n\t\t\tos = key;\n\t\t\treturn true;\n\t\t}\n\t});\n\n\t// just throw away the bytes for now\n\tif (flags & possibleFlags['FEXTRA']) {\n\t\tt = readShort(arr);\n\t\treadBytes(arr, t);\n\t}\n\n\t// just throw away for now\n\tif (flags & possibleFlags['FNAME']) {\n\t\treadString(arr);\n\t}\n\n\t// just throw away for now\n\tif (flags & possibleFlags['FCOMMENT']) {\n\t\treadString(arr);\n\t}\n\n\t// just throw away for now\n\tif (flags & possibleFlags['FHCRC']) {\n\t\treadShort(arr);\n\t}\n\n\tif (compressionMethod === 'deflate') {\n\t\t// give deflate everything but the last 8 bytes\n\t\t// the last 8 bytes are for the CRC32 checksum and filesize\n\t\tres = deflate.inflate(arr.splice(0, arr.length - 8));\n\t}\n\n\tif (flags & possibleFlags['FTEXT']) {\n\t\tres = Array.prototype.map.call(res, function (byte) {\n\t\t\treturn String.fromCharCode(byte);\n\t\t}).join('');\n\t}\n\n\tcrc = readLong(arr) >>> 0;\n\tif (crc !== parseInt(crc32(res), 16)) {\n\t\tthrow 'Checksum does not match';\n\t}\n\n\tsize = readLong(arr);\n\tif (size !== res.length) {\n\t\tthrow 'Size of decompressed file not correct';\n\t}\n\n\treturn res;\n}\n\n\n"]}