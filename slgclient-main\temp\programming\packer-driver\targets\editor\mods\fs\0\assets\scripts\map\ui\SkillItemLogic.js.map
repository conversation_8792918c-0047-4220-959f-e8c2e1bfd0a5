{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts"], "names": ["_decorator", "Component", "Label", "Node", "SpriteFrame", "SkillCommand", "SkillIconLogic", "ccclass", "property", "SkillItemLogic", "onEnable", "updateItem", "skill", "conf", "getInstance", "proxy", "getSkillCfg", "cfgId", "_skill", "nameLab", "string", "name", "icon", "getComponent", "setData", "limitLab", "generals", "length", "limit"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAEtCC,MAAAA,Y;;AAEAC,MAAAA,c;;;;;;;OAHD;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBR,U;;yBAMPS,c,WADpBF,OAAO,CAAC,gBAAD,C,UAIHC,QAAQ,CAACN,KAAD,C,UAIRM,QAAQ,CAACN,KAAD,C,UAGRM,QAAQ,CAACL,IAAD,C,UAGRK,QAAQ,CAAC,CAACJ,WAAD,CAAD,C,oCAdb,MACqBK,cADrB,SAC4CR,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,0CAgBlC,IAhBkC;AAAA;;AAkBxCS,QAAAA,QAAQ,GAAO,CAExB;;AAESC,QAAAA,UAAU,CAACC,KAAD,EAAkB;AAElC,cAAIC,IAAI,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,WAAjC,CAA6CJ,KAAK,CAACK,KAAnD,CAAX;AACA,eAAKC,MAAL,GAAcN,KAAd;AACA,eAAKO,OAAL,CAAaC,MAAb,GAAsBP,IAAI,CAACQ,IAA3B;AAEA,eAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,gDAAuCC,OAAvC,CAA+CZ,KAA/C,EAAsD,IAAtD;AAEA,eAAKa,QAAL,CAAcL,MAAd,GAAuB,KAAKF,MAAL,CAAYQ,QAAZ,CAAqBC,MAArB,GAA8B,GAA9B,GAAoCd,IAAI,CAACe,KAAhE;AACH;;AA/BiD,O;;;;;iBAIjC,I;;;;;;;iBAIC,I;;;;;;;iBAGN,I;;;;;;;iBAGQ,E", "sourcesContent": ["import { _decorator, Component, Label, Node, SpriteFrame } from 'cc';\nconst {ccclass, property} = _decorator;\nimport SkillCommand from \"../../skill/SkillCommand\";\nimport { Skill } from \"../../skill/SkillProxy\";\nimport SkillIconLogic from \"./SkillIconLogic\";\n\n@ccclass('SkillItemLogic')\nexport default class SkillItemLogic extends Component {\n\n   \n    @property(Label)\n    nameLab: Label = null;\n\n       \n    @property(Label)\n    limitLab: Label = null;\n\n    @property(Node)\n    icon:Node = null;\n\n    @property([SpriteFrame])\n    sps:SpriteFrame[] = [];\n\n    _skill: Skill = null;\n\n    protected onEnable():void{\n        \n    }\n\n    protected updateItem(skill:Skill):void{\n   \n        var conf = SkillCommand.getInstance().proxy.getSkillCfg(skill.cfgId);\n        this._skill = skill;\n        this.nameLab.string = conf.name;\n\n        this.icon.getComponent(SkillIconLogic).setData(skill, null);\n\n        this.limitLab.string = this._skill.generals.length + \"/\" + conf.limit;\n    }\n\n}\n"]}