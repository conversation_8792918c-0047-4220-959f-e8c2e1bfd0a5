{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts"], "names": ["RequestObject", "NetEvent", "destroy", "json", "rspName", "autoReconnect", "seq", "sended", "otherData", "startTime"], "mappings": ";;;iBAGaA,a,EAqCAC,Q;;;;;;;;;;;;;;;;;;+BArCAD,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,wCACF,IADE;;AAAA,2CAEE,EAFF;;AAAA,iDAGQ,CAHR;;AAAA,uCAIH,CAJG;;AAAA,0CAKC,KALD;;AAAA,6CAMA,EANA;;AAAA,6CAOG,CAPH;AAAA;;AAShBE,QAAAA,OAAO,GAAO;AACjB,eAAKC,IAAL,GAAY,IAAZ;AACA,eAAKC,OAAL,GAAe,EAAf;AACA,eAAKC,aAAL,GAAqB,CAArB;AACA,eAAKC,GAAL,GAAW,CAAX;AACA,eAAKC,MAAL,GAAc,KAAd;AACA,eAAKC,SAAL,GAAiB,EAAjB;AACA,eAAKC,SAAL,GAAiB,CAAjB;AACH;;AAjBsB,O,GAsB3B;;;AAcA;0BACaR,Q,GAAN,MAAMA,QAAN,CAAe,E;;sBAATA,Q,mBAC4B,e;;sBAD5BA,Q,qBAE8B,iB;;sBAF9BA,Q,qBAG8B,iB;;sBAH9BA,Q,sBAI+B,kB;;sBAJ/BA,Q,sBAK+B,kB;;sBAL/BA,Q,yBAMkC,qB", "sourcesContent": ["\nexport type NetData = (string | ArrayBufferLike | Blob | ArrayBufferView);\n\nexport class RequestObject {\n    public json: any   = null;           // 请求的json\n    public rspName: string = \"\";       // 接口名\n    public autoReconnect: number = 0;  // -1 永久重连，0不自动重连，其他正整数为自动重试次数\n    public seq:number = 0;             // 消息的序号\n    public sended:boolean = false;        // 是否发送\n    public otherData:any = {};\n    public startTime:number = 0\n\n    public destroy():void{\n        this.json = null;\n        this.rspName = \"\";\n        this.autoReconnect = 0;\n        this.seq = 0;\n        this.sended = false;\n        this.otherData = {};\n        this.startTime = 0;\n    }\n}\n\n\n\n// Socket接口\nexport interface ISocket {\n    onConnected: (event) => void;           // 连接回调\n    onMessage: (msg: NetData) => void;      // 消息回调\n    onJsonMessage: (msg: NetData) => void;      // 消息回调\n    onError: (event) => void;               // 错误回调\n    onClosed: (event) => void;              // 关闭回调\n    \n    connect(options: any);                  // 连接接口\n    send(buffer: NetData);                  // 数据发送接口\n    close(code?: number, reason?: string);  // 关闭接口\n}\n\n\n// 请求对象\nexport class NetEvent {\n    public static ServerTimeOut:string = \"ServerTimeOut\";\n    public static ServerConnected:string = \"ServerConnected\";\n    public static ServerHandShake:string = \"ServerHandShake\";\n    public static ServerCheckLogin:string = \"ServerCheckLogin\";\n    public static ServerRequesting:string = \"ServerRequesting\";\n    public static ServerRequestSucess:string = \"ServerRequestSucess\";\n}\n"]}