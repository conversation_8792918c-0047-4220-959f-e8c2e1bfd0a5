{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts"], "names": ["_decorator", "Component", "Node", "Label", "Sprite", "ArmyCmd", "GeneralCommand", "ArmyCommand", "MapUICommand", "GeneralHeadLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "CityArmyItemLogic", "onLoad", "on", "updateArmy", "onUpdateArmy", "tipNode", "active", "onDestroy", "targetOff", "_data", "armyData", "id", "setArmyData", "_cityId", "onClickItem", "instance", "playClick", "maskNode", "_isOut", "emit", "openArmySetting", "order", "updateItem", "_isOpened", "generals", "infoNode", "getInstance", "getArmyGenerals", "firstGeneralCfg", "proxy", "getGeneralCfg", "cfgId", "curSoldierCnt", "getArmyCurSoldierCnt", "totalSoldierCnt", "getArmyTotalSoldierCntByGenerals", "cmd", "Reclaim", "labelState", "string", "Conscript", "labelId", "headIcon", "getComponent", "setHeadId", "labelLv", "level", "labelName", "name", "labelSoldierCnt", "sencondGeneralCfg", "labelVice1", "thirdGeneralCfg", "labelVice2", "labelTip", "isOpenedArmy", "bool", "isOut", "desName", "getFacilityCfgByType", "cityId", "data"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAGpCC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,c;;AACAC,MAAAA,W;;AAEAC,MAAAA,Y;;AACAC,MAAAA,gB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAVH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;yBAaTe,iB,WADpBF,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACV,MAAD,C,UAERU,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACX,KAAD,C,oCA1Bb,MACqBY,iBADrB,SAC+Cd,SAD/C,CACyD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCA4B9B,CA5B8B;;AAAA,2CA6BzB,CA7ByB;;AAAA,yCA8BzB,IA9ByB;;AAAA,6CA+BtB,IA/BsB;;AAAA,0CAgCzB,IAhCyB;AAAA;;AAkC3Ce,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,UAAvB,EAAmC,KAAKC,YAAxC,EAAsD,IAAtD;AACA,eAAKC,OAAL,CAAaC,MAAb,GAAsB,KAAtB;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA,eAAKC,KAAL,GAAa,IAAb;AACH;;AAESL,QAAAA,YAAY,CAACM,QAAD,EAA2B;AAC7C,cAAI,KAAKD,KAAL,IAAcC,QAAQ,CAACC,EAAT,IAAe,KAAKF,KAAL,CAAWE,EAA5C,EAAgD;AAC5C,iBAAKC,WAAL,CAAiB,KAAKC,OAAtB,EAA+BH,QAA/B;AACH;AACJ;;AAESI,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKC,QAAL,CAAcX,MAAd,IAAwB,KAA5B,EAAmC;AAC/B,gBAAG,KAAKY,MAAR,EAAe;AACX,kBAAG,KAAKT,KAAR,EAAc;AACV;AAAA;AAAA,0CAASU,IAAT,CAAc;AAAA;AAAA,8CAAWC,eAAzB,EAA0C,KAAKP,OAA/C,EAAwD,KAAKJ,KAAL,CAAWY,KAAnE;AACH;AACJ,aAJD,MAIK;AACD;AAAA;AAAA,wCAASF,IAAT,CAAc;AAAA;AAAA,4CAAWC,eAAzB,EAA0C,KAAKP,OAA/C,EAAwD,KAAKQ,KAA7D;AACH;AACJ;AACJ;;AAESC,QAAAA,UAAU,GAAS;AAEzB;AAEA,cAAG,KAAKC,SAAL,IAAkB,KAArB,EAA2B;AACvB;AACH;;AAED,cAAI,KAAKd,KAAL,IAAc,KAAKA,KAAL,CAAWe,QAAX,CAAoB,CAApB,KAA0B,CAA5C,EAA+C;AAC3C;AACA,iBAAKnB,OAAL,CAAaC,MAAb,GAAsB,KAAtB;AACA,iBAAKmB,QAAL,CAAcnB,MAAd,GAAuB,IAAvB;AACA,gBAAIkB,QAAuB,GAAG;AAAA;AAAA,4CAAYE,WAAZ,GAA0BC,eAA1B,CAA0C,KAAKlB,KAA/C,CAA9B;AACA,gBAAImB,eAA8B,GAAG;AAAA;AAAA,kDAAeF,WAAf,GAA6BG,KAA7B,CAAmCC,aAAnC,CAAiDN,QAAQ,CAAC,CAAD,CAAR,CAAYO,KAA7D,CAArC;AACA,gBAAIC,aAAqB,GAAG;AAAA;AAAA,4CAAYN,WAAZ,GAA0BO,oBAA1B,CAA+C,KAAKxB,KAApD,CAA5B;AACA,gBAAIyB,eAAuB,GAAG;AAAA;AAAA,4CAAYR,WAAZ,GAA0BS,gCAA1B,CAA2DX,QAA3D,CAA9B;;AACA,gBAAI,KAAKf,KAAL,CAAW2B,GAAX,IAAkB;AAAA;AAAA,oCAAQC,OAA9B,EAAuC;AACnC;AACA,mBAAKC,UAAL,CAAgBC,MAAhB,GAAyB,QAAzB;AACH,aAHD,MAGO,IAAG,KAAK9B,KAAL,CAAW2B,GAAX,IAAkB;AAAA;AAAA,oCAAQI,SAA7B,EAAuC;AAC1C,mBAAKF,UAAL,CAAgBC,MAAhB,GAAyB,QAAzB;AACH,aAFM,MAEA,IAAI,KAAK9B,KAAL,CAAW2B,GAAX,GAAiB,CAArB,EAAwB;AAC3B,mBAAKE,UAAL,CAAgBC,MAAhB,GAAyB,UAAzB;AACH,aAFM,MAEA;AACH,mBAAKD,UAAL,CAAgBC,MAAhB,GAAyB,EAAzB;AACH;;AACD,iBAAKE,OAAL,CAAaF,MAAb,GAAsB,KAAKlB,KAAL,GAAa,EAAnC;AACA,iBAAKqB,QAAL,CAAcC,YAAd;AAAA;AAAA,sDAA6CC,SAA7C,CAAuDpB,QAAQ,CAAC,CAAD,CAAR,CAAYO,KAAnE;AACA,iBAAKc,OAAL,CAAaN,MAAb,GAAsBf,QAAQ,CAAC,CAAD,CAAR,CAAYsB,KAAZ,GAAoB,EAA1C;AACA,iBAAKC,SAAL,CAAeR,MAAf,GAAwBX,eAAe,CAACoB,IAAxC;AACA,iBAAKC,eAAL,CAAqBV,MAArB,GAA8BP,aAAa,GAAG,GAAhB,GAAsBE,eAApD,CAtB2C,CAuB3C;;AAEA,gBAAIV,QAAQ,CAAC,CAAD,CAAZ,EAAiB;AACb,kBAAI0B,iBAAgC,GAAG;AAAA;AAAA,oDAAexB,WAAf,GAA6BG,KAA7B,CAAmCC,aAAnC,CAAiDN,QAAQ,CAAC,CAAD,CAAR,CAAYO,KAA7D,CAAvC;AACA,mBAAKoB,UAAL,CAAgBZ,MAAhB,GAAyBW,iBAAiB,CAACF,IAA3C;AACH,aAHD,MAGO;AACH,mBAAKG,UAAL,CAAgBZ,MAAhB,GAAyB,GAAzB;AACH;;AAED,gBAAIf,QAAQ,CAAC,CAAD,CAAZ,EAAiB;AACb,kBAAI4B,eAA8B,GAAG;AAAA;AAAA,oDAAe1B,WAAf,GAA6BG,KAA7B,CAAmCC,aAAnC,CAAiDN,QAAQ,CAAC,CAAD,CAAR,CAAYO,KAA7D,CAArC;AACA,mBAAKsB,UAAL,CAAgBd,MAAhB,GAAyBa,eAAe,CAACJ,IAAzC;AACH,aAHD,MAGO;AACH,mBAAKK,UAAL,CAAgBd,MAAhB,GAAyB,GAAzB;AACH;AACJ,WAtCD,MAsCO;AACH,gBAAG,KAAKrB,MAAR,EAAe;AACX,mBAAKb,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACA,mBAAKmB,QAAL,CAAcnB,MAAd,GAAuB,KAAvB;AACA,mBAAKgD,QAAL,CAAcf,MAAd,GAAuB,MAAvB;AACH,aAJD,MAIK;AACD,mBAAKlC,OAAL,CAAaC,MAAb,GAAsB,IAAtB;AACA,mBAAKmB,QAAL,CAAcnB,MAAd,GAAuB,KAAvB;AACA,mBAAKgD,QAAL,CAAcf,MAAd,GAAuB,QAAvB;AACH;AACJ;AACJ;;AAEMgB,QAAAA,YAAY,CAACC,IAAD,EAAgBC,KAAhB,EAAsC;AACrD,eAAKlC,SAAL,GAAiBiC,IAAjB;AACA,eAAK/B,QAAL,CAAcnB,MAAd,GAAuB,KAAvB;AACA,eAAKW,QAAL,CAAcX,MAAd,GAAuB,CAAC,KAAKiB,SAA7B;AACA,eAAKlB,OAAL,CAAaC,MAAb,GAAsB,CAAC,KAAKiB,SAA5B;AACA,eAAKL,MAAL,GAAcuC,KAAd;;AACA,cAAI,KAAKlC,SAAL,IAAkB,KAAtB,EAA6B;AACzB,gBAAI,KAAKL,MAAT,EAAgB;AACZ,mBAAKoC,QAAL,CAAcf,MAAd,GAAuB,QAAQ,KAAKlB,KAAb,GAAqB,IAA5C;AACH,aAFD,MAEK;AACD,kBAAIqC,OAAe,GAAG;AAAA;AAAA,gDAAahC,WAAb,GAA2BG,KAA3B,CAAiC8B,oBAAjC,CAAsD,EAAtD,EAA0DX,IAAhF;AACA,mBAAKM,QAAL,CAAcf,MAAd,GAAuBmB,OAAO,GAAG,KAAV,GAAkB,KAAKrC,KAAvB,GAA+B,IAAtD;AACH;AACJ;AACJ;;AAEMT,QAAAA,WAAW,CAACgD,MAAD,EAAiBC,IAAjB,EAAuC;AACrD,eAAKhD,OAAL,GAAe+C,MAAf;AACA,eAAKnD,KAAL,GAAaoD,IAAb;AACA,eAAKvC,UAAL;AACH;;AA9IoD,O;;;;;iBAEpC,I;;;;;;;iBAEA,I;;;;;;;iBAED,I;;;;;;;iBAEE,I;;;;;;;iBAEC,I;;;;;;;iBAEF,I;;;;;;;iBAEG,I;;;;;;;iBAEH,I;;;;;;;iBAEE,I;;;;;;;iBAEA,I;;;;;;;iBAEM,I;;;;;;;iBAEL,I;;;;;;;iBAEA,I", "sourcesContent": ["import { _decorator, Component, Node, Label, Sprite } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { ArmyCmd, ArmyData } from \"../../general/ArmyProxy\";\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport { GeneralConfig, GeneralData } from \"../../general/GeneralProxy\";\nimport MapUICommand from \"./MapUICommand\";\nimport GeneralHeadLogic from \"./GeneralHeadLogic\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('CityArmyItemLogic')\nexport default class CityArmyItemLogic extends Component {\n    @property(Node)\n    infoNode: Node = null;\n    @property(Node)\n    maskNode: Node = null;\n    @property(Node)\n    tipNode: Node = null;\n    @property(Label)\n    labelTip: Label = null;\n    @property(Sprite)\n    headIcon: Sprite = null;\n    @property(Label)\n    labelId: Label = null;\n    @property(Label)\n    labelState: Label = null;\n    @property(Label)\n    labelLv: Label = null;\n    @property(Label)\n    labelName: Label = null;\n    @property(Label)\n    labelArms: Label = null;\n    @property(Label)\n    labelSoldierCnt: Label = null;\n    @property(Label)\n    labelVice1: Label = null;\n    @property(Label)\n    labelVice2: Label = null;\n\n    public order: number = 0;\n    protected _cityId: number = 0;\n    protected _data: ArmyData = null;\n    protected _isOpened: boolean = true;\n    protected _isOut: boolean = true;\n\n    protected onLoad(): void {\n        EventMgr.on(LogicEvent.updateArmy, this.onUpdateArmy, this);\n        this.tipNode.active = false;\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._data = null;\n    }\n\n    protected onUpdateArmy(armyData: ArmyData): void {\n        if (this._data && armyData.id == this._data.id) {\n            this.setArmyData(this._cityId, armyData);\n        }\n    }\n\n    protected onClickItem(): void {\n        AudioManager.instance.playClick();\n        if (this.maskNode.active == false) {\n            if(this._isOut){\n                if(this._data){\n                    EventMgr.emit(LogicEvent.openArmySetting, this._cityId, this._data.order);\n                }\n            }else{\n                EventMgr.emit(LogicEvent.openArmySetting, this._cityId, this.order);\n            }\n        }\n    }\n\n    protected updateItem(): void {\n     \n        // console.log(\"cityarmyitem:\", this._data);\n        \n        if(this._isOpened == false){\n            return\n        }\n\n        if (this._data && this._data.generals[0] != 0) {\n            //有数据 并且配置了第一个将\n            this.tipNode.active = false;\n            this.infoNode.active = true;\n            let generals: GeneralData[] = ArmyCommand.getInstance().getArmyGenerals(this._data);\n            let firstGeneralCfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(generals[0].cfgId);\n            let curSoldierCnt: number = ArmyCommand.getInstance().getArmyCurSoldierCnt(this._data);\n            let totalSoldierCnt: number = ArmyCommand.getInstance().getArmyTotalSoldierCntByGenerals(generals);\n            if (this._data.cmd == ArmyCmd.Reclaim) {\n                //屯田中\n                this.labelState.string = \"屯田中...\";\n            } else if(this._data.cmd == ArmyCmd.Conscript){\n                this.labelState.string = \"征兵中...\";\n            } else if (this._data.cmd > 0) {\n                this.labelState.string = \"队伍外派中...\";\n            } else {\n                this.labelState.string = \"\";\n            }\n            this.labelId.string = this.order + \"\";\n            this.headIcon.getComponent(GeneralHeadLogic).setHeadId(generals[0].cfgId);\n            this.labelLv.string = generals[0].level + \"\";\n            this.labelName.string = firstGeneralCfg.name;\n            this.labelSoldierCnt.string = curSoldierCnt + \"/\" + totalSoldierCnt;\n            // this.labelArms.string = \"\";\n\n            if (generals[1]) {\n                let sencondGeneralCfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(generals[1].cfgId);\n                this.labelVice1.string = sencondGeneralCfg.name;\n            } else {\n                this.labelVice1.string = \"无\";\n            }\n\n            if (generals[2]) {\n                let thirdGeneralCfg: GeneralConfig = GeneralCommand.getInstance().proxy.getGeneralCfg(generals[2].cfgId);\n                this.labelVice2.string = thirdGeneralCfg.name;\n            } else {\n                this.labelVice2.string = \"无\";\n            }\n        } else {\n            if(this._isOut){\n                this.tipNode.active = true;\n                this.infoNode.active = false;\n                this.labelTip.string = \"暂无队伍\";\n            }else{\n                this.tipNode.active = true;\n                this.infoNode.active = false;\n                this.labelTip.string = \"点击编制队伍\";\n            }\n        }\n    }\n\n    public isOpenedArmy(bool: boolean, isOut: boolean): void {\n        this._isOpened = bool;\n        this.infoNode.active = false;\n        this.maskNode.active = !this._isOpened;\n        this.tipNode.active = !this._isOpened;\n        this._isOut = isOut;\n        if (this._isOpened == false) {\n            if (this._isOut){\n                this.labelTip.string = \" 等级\" + this.order + \"开启\";\n            }else{\n                let desName: string = MapUICommand.getInstance().proxy.getFacilityCfgByType(13).name;\n                this.labelTip.string = desName + \" 等级\" + this.order + \"开启\";\n            }\n        }\n    }\n\n    public setArmyData(cityId: number, data: ArmyData): void {\n        this._cityId = cityId;\n        this._data = data;\n        this.updateItem();\n    }\n}\n"]}