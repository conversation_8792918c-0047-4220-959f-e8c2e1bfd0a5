{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts"], "names": ["_decorator", "Component", "EditBox", "Label", "Node", "Prefab", "ToggleContainer", "instantiate", "JsonAsset", "SpriteFrame", "LoaderManager", "LoadData", "LoadDataType", "GeneralCommand", "General<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "JSB", "ccclass", "property", "GeneralTool", "onLoad", "tipsLab", "string", "opNode", "active", "dataList", "push", "DIR", "FILE", "getInstance", "startLoadList", "error", "paths", "datas", "console", "log", "proxy", "initGeneralConfig", "json", "initGeneralTex", "loadFinish", "_isLoading", "cfgs", "getGeneralAllCfg", "_cfgs", "Array", "from", "values", "sort", "sortStar", "probability", "index", "length", "e", "star", "Math", "floor", "random", "show", "_curIndex", "idx", "_generalNode", "g", "<PERSON><PERSON><PERSON><PERSON>", "parent", "generalParentNode", "cfg", "getComponent", "setData", "nameEditBox", "name", "xjEditBox", "wlEditBox", "force", "fyEditBox", "defense", "mlEditBox", "strategy", "sdEditBox", "speed", "gcEditBox", "destroy", "wlAddEditBox", "force_grow", "fyAddEditBox", "defense_grow", "mlAddEditBox", "strategy_grow", "sdAddEditBox", "speed_grow", "gcAddEditBox", "costEditBox", "cost", "toggleCampGroup", "toggleItems", "camp", "isChecked", "arms", "toggleArmGroup", "refresh", "xj", "parseInt", "Number", "destroy_grow", "items", "item", "items2", "onClickMake", "outDirEditBox", "path", "jsb", "fileUtils", "isDirectoryExist", "obj", "Object", "title", "list", "str", "JSON", "stringify", "writeStringToFile", "onClickPre", "onClickNext", "a", "b", "cfgId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,e,OAAAA,e;AAAiBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;;AAGhGC,MAAAA,a;AAAiBC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Y,iBAAAA,Y;;AAC3BC,MAAAA,c;;AAEAC,MAAAA,kB;;AACEC,MAAAA,G,UAAAA,G;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;yBASTkB,W,WADpBF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAACf,OAAD,C,UAGRe,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACb,IAAD,C,UAIRa,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAACZ,MAAD,C,UAGRY,QAAQ,CAACX,eAAD,C,UAGRW,QAAQ,CAACX,eAAD,C,UAIRW,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,WAGRe,QAAQ,CAACf,OAAD,C,oCA9Db,MACqBgB,WADrB,SACyCjB,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,gDAiE1B,IAjE0B;;AAAA,yCAmEZ,EAnEY;;AAAA,8CAqExB,IArEwB;;AAAA,6CAuEzB,CAvEyB;AAAA;;AAyErCkB,QAAAA,MAAM,GAAS;AAErB,eAAKC,OAAL,CAAaC,MAAb,GAAsB,QAAtB;AACA,eAAKC,MAAL,CAAYC,MAAZ,GAAqB,KAArB;AAEA,cAAIC,QAAoB,GAAG,EAA3B;AACAA,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,wBAAb,EAAuC;AAAA;AAAA,4CAAaC,GAApD,EAAyDlB,SAAzD,CAAd;AACAgB,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,cAAb,EAA6B;AAAA;AAAA,4CAAaC,GAA1C,EAA+CjB,WAA/C,CAAd;AACAe,UAAAA,QAAQ,CAACC,IAAT,CAAc;AAAA;AAAA,oCAAa,gBAAb,EAA+B;AAAA;AAAA,4CAAaE,IAA5C,EAAkDnB,SAAlD,CAAd;AAEA;AAAA;AAAA,8CAAcoB,WAAd,GAA4BC,aAA5B,CAA0CL,QAA1C,EAAoD,IAApD,EACI,CAACM,KAAD,EAAeC,KAAf,EAAgCC,KAAhC,KAAiD;AAC7C,gBAAIF,KAAK,IAAI,IAAb,EAAmB;AACfG,cAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA;AACH;;AACDD,YAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BH,KAA5B,EAAmCC,KAAnC;AAEA;AAAA;AAAA,kDAAeJ,WAAf,GAA6BO,KAA7B,CAAmCC,iBAAnC,CAAqDJ,KAAK,CAAC,CAAD,CAA1D,EAA+DA,KAAK,CAAC,CAAD,CAAN,CAAwBK,IAAtF;AACA;AAAA;AAAA,kDAAeT,WAAf,GAA6BO,KAA7B,CAAmCG,cAAnC,CAAkDN,KAAK,CAAC,CAAD,CAAvD;AAEA,iBAAKO,UAAL;AACH,WAZL,EAaI,IAbJ;AAiBH;;AAESA,QAAAA,UAAU,GAAQ;AACxB,eAAKjB,MAAL,CAAYC,MAAZ,GAAqB,IAArB;AACA,eAAKH,OAAL,CAAaC,MAAb,GAAsB,EAAtB;AACA,eAAKmB,UAAL,GAAkB,KAAlB;AAEA,cAAIC,IAAI,GAAG;AAAA;AAAA,gDAAeb,WAAf,GAA6BO,KAA7B,CAAmCO,gBAAnC,EAAX;AACA,eAAKC,KAAL,GAAaC,KAAK,CAACC,IAAN,CAAWJ,IAAI,CAACK,MAAL,EAAX,CAAb;;AACA,eAAKH,KAAL,CAAWI,IAAX,CAAgB,KAAKC,QAArB;;AAGA,cAAIC,WAAmB,GAAG,GAA1B;;AACA,eAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAKP,KAAL,CAAWQ,MAAvC,EAA+CD,KAAK,EAApD,EAAwD;AACpD,gBAAIE,CAAC,GAAG,KAAKT,KAAL,CAAWO,KAAX,CAAR;;AACA,gBAAIE,CAAC,CAACC,IAAF,IAAU,CAAd,EAAgB;AACZJ,cAAAA,WAAW,GAAGK,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,EAA3B,IAAiC,CAA/C;AACH,aAFD,MAEM,IAAGJ,CAAC,CAACC,IAAF,IAAU,CAAb,EAAe;AACjBJ,cAAAA,WAAW,GAAGK,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,EAA3B,IAAiC,EAA/C;AACH,aAFK,MAEA,IAAGJ,CAAC,CAACC,IAAF,IAAU,CAAb,EAAe;AACjBJ,cAAAA,WAAW,GAAGK,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,GAA3B,IAAkC,GAAhD;AACH,aAFK,MAEA,IAAGJ,CAAC,CAACC,IAAF,IAAU,CAAb,EAAe;AACjBJ,cAAAA,WAAW,GAAGK,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,GAA3B,IAAkC,GAAhD;AACH,aAFK,MAEA,IAAGJ,CAAC,CAACC,IAAF,IAAU,CAAb,EAAe;AACjBJ,cAAAA,WAAW,GAAGK,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,GAA3B,IAAkC,GAAhD;AACH;;AACDJ,YAAAA,CAAC,CAACH,WAAF,GAAgBA,WAAhB;AACH;;AAGD,eAAKQ,IAAL,CAAU,KAAKC,SAAf;AACH;;AAESD,QAAAA,IAAI,CAACE,GAAD,EAAkB;AAE5B,cAAG,KAAKhB,KAAL,CAAWQ,MAAX,GAAoB,CAAvB,EAAyB;AACrB,gBAAGQ,GAAG,GAAG,CAAT,EAAW;AACPA,cAAAA,GAAG,GAAG,KAAKhB,KAAL,CAAWQ,MAAX,GAAkB,CAAxB;AACH,aAFD,MAEM,IAAGQ,GAAG,IAAI,KAAKhB,KAAL,CAAWQ,MAArB,EAA4B;AAC9BQ,cAAAA,GAAG,GAAG,CAAN;AACH;;AAED,gBAAG,KAAKC,YAAL,IAAqB,IAAxB,EAA6B;AACzB,kBAAIC,CAAC,GAAGtD,WAAW,CAAC,KAAKuD,aAAN,CAAnB;AACAD,cAAAA,CAAC,CAACE,MAAF,GAAW,KAAKC,iBAAhB;AACA,mBAAKJ,YAAL,GAAoBC,CAApB;AACH;;AAED,gBAAII,GAAG,GAAG,KAAKtB,KAAL,CAAWgB,GAAX,CAAV;;AACA,iBAAKC,YAAL,CAAkBM,YAAlB;AAAA;AAAA,0DAAmDC,OAAnD,CAA2DF,GAA3D;;AAEA,iBAAKG,WAAL,CAAiB/C,MAAjB,GAA0B4C,GAAG,CAACI,IAA9B;AACA,iBAAKC,SAAL,CAAejD,MAAf,GAAwB4C,GAAG,CAACZ,IAAJ,GAAW,EAAnC;AAEA,iBAAKkB,SAAL,CAAelD,MAAf,GAAyB4C,GAAG,CAACO,KAAJ,GAAY,GAAb,GAAoB,EAA5C;AACA,iBAAKC,SAAL,CAAepD,MAAf,GAAyB4C,GAAG,CAACS,OAAJ,GAAc,GAAf,GAAsB,EAA9C;AACA,iBAAKC,SAAL,CAAetD,MAAf,GAAyB4C,GAAG,CAACW,QAAJ,GAAe,GAAhB,GAAuB,EAA/C;AACA,iBAAKC,SAAL,CAAexD,MAAf,GAAyB4C,GAAG,CAACa,KAAJ,GAAY,GAAb,GAAoB,EAA5C;AACA,iBAAKC,SAAL,CAAe1D,MAAf,GAAyB4C,GAAG,CAACe,OAAJ,GAAc,GAAf,GAAsB,EAA9C;AAEA,iBAAKC,YAAL,CAAkB5D,MAAlB,GAA4B4C,GAAG,CAACiB,UAAJ,GAAiB,GAAlB,GAAyB,EAApD;AACA,iBAAKC,YAAL,CAAkB9D,MAAlB,GAA4B4C,GAAG,CAACmB,YAAJ,GAAmB,GAApB,GAA2B,EAAtD;AACA,iBAAKC,YAAL,CAAkBhE,MAAlB,GAA4B4C,GAAG,CAACqB,aAAJ,GAAoB,GAArB,GAA4B,EAAvD;AACA,iBAAKC,YAAL,CAAkBlE,MAAlB,GAA4B4C,GAAG,CAACuB,UAAJ,GAAiB,GAAlB,GAAyB,EAApD;AACA,iBAAKC,YAAL,CAAkBpE,MAAlB,GAA4B4C,GAAG,CAACmB,YAAJ,GAAmB,GAApB,GAA2B,EAAtD;AAEA,iBAAKM,WAAL,CAAiBrE,MAAjB,GAA0B4C,GAAG,CAAC0B,IAAJ,GAAW,EAArC;AACA,iBAAKC,eAAL,CAAqBC,WAArB,CAAiC5B,GAAG,CAAC6B,IAAJ,GAAS,CAA1C,EAA6CC,SAA7C,GAAyD,IAAzD;AAEA9D,YAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB+B,GAAzB;;AACA,gBAAGA,GAAG,CAAC+B,IAAJ,CAAS,CAAT,KAAe,CAAlB,EAAoB;AAChB,mBAAKC,cAAL,CAAoBJ,WAApB,CAAgC,CAAhC,EAAmCE,SAAnC,GAA+C,IAA/C;AACH,aAFD,MAEM,IAAG9B,GAAG,CAAC+B,IAAJ,CAAS,CAAT,KAAe,CAAlB,EAAoB;AACtB,mBAAKC,cAAL,CAAoBJ,WAApB,CAAgC,CAAhC,EAAmCE,SAAnC,GAA+C,IAA/C;AACH,aAFK,MAEA;AACF,mBAAKE,cAAL,CAAoBJ,WAApB,CAAgC,CAAhC,EAAmCE,SAAnC,GAA+C,IAA/C;AACH;AAEJ;AACJ;;AAESG,QAAAA,OAAO,GAAS;AACtBjE,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EADsB,CAGtB;;AACA,eAAKS,KAAL,CAAW,KAAKe,SAAhB,EAA2BW,IAA3B,GAAkC,KAAKD,WAAL,CAAiB/C,MAAnD;AAEA,cAAI8E,EAAE,GAAGC,QAAQ,CAAC,KAAK9B,SAAL,CAAejD,MAAhB,CAAjB;;AACA,cAAG,IAAI8E,EAAJ,IAAUA,EAAE,IAAI,CAAnB,EAAqB;AACjB,iBAAKxD,KAAL,CAAW,KAAKe,SAAhB,EAA2BL,IAA3B,GAAkC8C,EAAlC;AACH;;AAED,eAAKxD,KAAL,CAAW,KAAKe,SAAhB,EAA2Bc,KAA3B,GAAmC4B,QAAQ,CAAC,KAAK7B,SAAL,CAAelD,MAAhB,CAAR,GAAgC,GAAnE;AACA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2BkB,QAA3B,GAAsCwB,QAAQ,CAAC,KAAKzB,SAAL,CAAetD,MAAhB,CAAR,GAAgC,GAAtE;AACA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2BgB,OAA3B,GAAqC0B,QAAQ,CAAC,KAAK3B,SAAL,CAAepD,MAAhB,CAAR,GAAgC,GAArE;AACA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2BoB,KAA3B,GAAmCsB,QAAQ,CAAC,KAAKvB,SAAL,CAAexD,MAAhB,CAAR,GAAgC,GAAnE;AACA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2BsB,OAA3B,GAAqCoB,QAAQ,CAAC,KAAKrB,SAAL,CAAe1D,MAAhB,CAAR,GAAgC,GAArE;AAEA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2BwB,UAA3B,GAAwCmB,MAAM,CAAC,KAAKpB,YAAL,CAAkB5D,MAAnB,CAAN,GAAiC,GAAzE;AACA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2B4B,aAA3B,GAA2Ce,MAAM,CAAC,KAAKhB,YAAL,CAAkBhE,MAAnB,CAAN,GAAiC,GAA5E;AACA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2B0B,YAA3B,GAA0CiB,MAAM,CAAC,KAAKlB,YAAL,CAAkB9D,MAAnB,CAAN,GAAiC,GAA3E;AACA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2B8B,UAA3B,GAAwCa,MAAM,CAAC,KAAKd,YAAL,CAAkBlE,MAAnB,CAAN,GAAiC,GAAzE;AACA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2B4C,YAA3B,GAA0CD,MAAM,CAAC,KAAKZ,YAAL,CAAkBpE,MAAnB,CAAN,GAAiC,GAA3E;AAEA,eAAKsB,KAAL,CAAW,KAAKe,SAAhB,EAA2BiC,IAA3B,GAAkCS,QAAQ,CAAC,KAAKV,WAAL,CAAiBrE,MAAlB,CAA1C;AAEA,cAAIkF,KAAK,GAAG,KAAKX,eAAL,CAAqBC,WAAjC;;AACA,eAAK,IAAI3C,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGqD,KAAK,CAACpD,MAAlC,EAA0CD,KAAK,EAA/C,EAAmD;AAC/C,gBAAIsD,IAAI,GAAGD,KAAK,CAACrD,KAAD,CAAhB;;AACA,gBAAGsD,IAAI,CAACT,SAAR,EAAkB;AACd,mBAAKpD,KAAL,CAAW,KAAKe,SAAhB,EAA2BoC,IAA3B,GAAkC5C,KAAK,GAAC,CAAxC;AACH;AACJ;;AAED,cAAIuD,MAAM,GAAG,KAAKR,cAAL,CAAoBJ,WAAjC;;AACA,eAAK,IAAI3C,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGuD,MAAM,CAACtD,MAAnC,EAA2CD,KAAK,EAAhD,EAAoD;AAChD,gBAAIsD,IAAI,GAAGC,MAAM,CAACvD,KAAD,CAAjB;;AACA,gBAAGsD,IAAI,CAACT,SAAR,EAAkB;AACd,kBAAG7C,KAAK,IAAI,CAAZ,EAAc;AACV,qBAAKP,KAAL,CAAW,KAAKe,SAAhB,EAA2BsC,IAA3B,GAAkC,CAAC,CAAD,EAAG,CAAH,EAAK,CAAL,CAAlC;AACH,eAFD,MAEM,IAAG9C,KAAK,IAAI,CAAZ,EAAc;AAChB,qBAAKP,KAAL,CAAW,KAAKe,SAAhB,EAA2BsC,IAA3B,GAAkC,CAAC,CAAD,EAAG,CAAH,EAAK,CAAL,CAAlC;AACH,eAFK,MAED;AACD,qBAAKrD,KAAL,CAAW,KAAKe,SAAhB,EAA2BsC,IAA3B,GAAkC,CAAC,CAAD,EAAG,CAAH,EAAK,CAAL,CAAlC;AACH;AACJ;AACJ;AACJ;;AAESU,QAAAA,WAAW,GAAS;AAE1B,cAAG,KAAKlE,UAAR,EAAmB;AACf;AACH;;AAED,eAAK0D,OAAL;;AAEA,cAAI,KAAKS,aAAL,CAAmBtF,MAAnB,IAA6B,EAAjC,EAAoC;AAChC,iBAAKD,OAAL,CAAaC,MAAb,GAAsB,WAAtB;AACA;AACH;;AAED,cAAI,CAACN,GAAL,EAAU;AACN,iBAAKK,OAAL,CAAaC,MAAb,GAAsB,mBAAtB;AACA;AACH;;AAED,cAAIuF,IAAI,GAAG,KAAKD,aAAL,CAAmBtF,MAA9B;;AACA,cAAGwF,GAAG,CAACC,SAAJ,CAAcC,gBAAd,CAA+BH,IAA/B,KAAwC,KAA3C,EAAiD;AAC7C,iBAAKxF,OAAL,CAAaC,MAAb,GAAsB,OAAtB;AACA;AACH;;AAED,cAAI2F,GAAG,GAAGC,MAAM,EAAhB;AACAD,UAAAA,GAAG,CAACE,KAAJ,GAAY,MAAZ;AACAF,UAAAA,GAAG,CAACG,IAAJ,GAAW,KAAKxE,KAAhB;AAEA,cAAIyE,GAAG,GAAGC,IAAI,CAACC,SAAL,CAAeN,GAAf,EAAoB,IAApB,EAA0B,IAA1B,CAAV;AACAH,UAAAA,GAAG,CAACC,SAAJ,CAAcS,iBAAd,CAAgCH,GAAhC,EAAqCR,IAAI,GAAG,eAA5C;AAEA,eAAKxF,OAAL,CAAaC,MAAb,GAAsB,MAAtB;AACH;;AAGSmG,QAAAA,UAAU,GAAS;AACzB,cAAG,KAAKhF,UAAR,EAAmB;AACf;AACH;;AAED,eAAK0D,OAAL;AAEA,eAAKxC,SAAL,IAAgB,CAAhB;AACA,eAAKD,IAAL,CAAU,KAAKC,SAAf;AACH;;AAES+D,QAAAA,WAAW,GAAS;AAC1B,cAAG,KAAKjF,UAAR,EAAmB;AACf;AACH;;AAED,eAAK0D,OAAL;AACA,eAAKxC,SAAL,IAAgB,CAAhB;AACA,eAAKD,IAAL,CAAU,KAAKC,SAAf;AACH;;AAGSV,QAAAA,QAAQ,CAAC0E,CAAD,EAAmBC,CAAnB,EAA6C;AAE3D,cAAGD,CAAC,CAACrE,IAAF,GAASsE,CAAC,CAACtE,IAAd,EAAmB;AACf,mBAAO,CAAP;AACH,WAFD,MAEM,IAAGqE,CAAC,CAACrE,IAAF,IAAUsE,CAAC,CAACtE,IAAf,EAAoB;AACtB,mBAAOqE,CAAC,CAACE,KAAF,GAAUD,CAAC,CAACC,KAAnB;AACH,WAFK,MAED;AACD,mBAAO,CAAC,CAAR;AACH;AACJ;;AAvS8C,O;;;;;iBAGtB,I;;;;;;;iBAGR,I;;;;;;;iBAGS,I;;;;;;;iBAIX,I;;;;;;;iBAGS,I;;;;;;;iBAGW,I;;;;;;;iBAGD,I;;;;;;;iBAIX,I;;;;;;;iBAGF,I;;;;;;;iBAGA,I;;;;;;;iBAGA,I;;;;;;;iBAGA,I;;;;;;;iBAGA,I;;;;;;;iBAGA,I;;;;;;;iBAGE,I;;;;;;;iBAGC,I;;;;;;;iBAGA,I;;;;;;;iBAGA,I;;;;;;;iBAGA,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, EditBox, Label, Node, Prefab, ToggleContainer, instantiate, JsonAsset, SpriteFrame } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport LoaderManager, { LoadData, LoadDataType } from \"../core/LoaderManager\";\nimport GeneralCommand from \"../general/GeneralCommand\";\nimport { GeneralConfig } from \"../general/GeneralProxy\";\nimport GeneralRosterLogic from \"../map/ui/GeneralRosterLogic\";\nimport { JSB } from 'cc/env';\n\n@ccclass('GeneralTool')\nexport default class GeneralTool extends Component {\n\n    @property(EditBox)\n    outDirEditBox: EditBox = null;\n\n    @property(Label)\n    tipsLab: Label = null;\n\n    @property(Node)\n    generalParentNode: Node = null;\n    \n    \n    @property(Node)\n    opNode: Node = null;\n\n    @property(Prefab)\n    generalRoster: Prefab = null;\n\n    @property(ToggleContainer)\n    toggleCampGroup: ToggleContainer = null;\n\n    @property(ToggleContainer)\n    toggleArmGroup: ToggleContainer = null;\n\n\n    @property(EditBox)\n    nameEditBox: EditBox = null;\n\n    @property(EditBox)\n    xjEditBox: EditBox = null;\n\n    @property(EditBox)\n    wlEditBox: EditBox = null;\n\n    @property(EditBox)\n    mlEditBox: EditBox = null;\n\n    @property(EditBox)\n    fyEditBox: EditBox = null;\n\n    @property(EditBox)\n    gcEditBox: EditBox = null;\n\n    @property(EditBox)\n    sdEditBox: EditBox = null;\n\n    @property(EditBox)\n    costEditBox: EditBox = null;\n\n    @property(EditBox)\n    wlAddEditBox: EditBox = null;\n\n    @property(EditBox)\n    mlAddEditBox: EditBox = null;\n\n    @property(EditBox)\n    fyAddEditBox: EditBox = null;\n\n    @property(EditBox)\n    gcAddEditBox: EditBox = null;\n\n    @property(EditBox)\n    sdAddEditBox: EditBox = null;\n\n\n    _generalNode: Node = null;\n\n    protected _cfgs: GeneralConfig[] = [];\n\n    protected _isLoading = true;\n\n    protected _curIndex = 0;\n\n    protected onLoad(): void {\n        \n        this.tipsLab.string = \"加载中...\";\n        this.opNode.active = false;\n\n        let dataList: LoadData[] = [];\n        dataList.push(new LoadData(\"./config/json/general/\", LoadDataType.DIR, JsonAsset));\n        dataList.push(new LoadData(\"./generalpic\", LoadDataType.DIR, SpriteFrame));\n        dataList.push(new LoadData(\"./config/basic\", LoadDataType.FILE, JsonAsset));\n    \n        LoaderManager.getInstance().startLoadList(dataList, null,\n            (error: Error, paths: string[], datas: any[]) => {\n                if (error != null) {\n                    console.log(\"加载配置文件失败\");\n                    return;\n                }\n                console.log(\"loadComplete\", paths, datas);\n \n                GeneralCommand.getInstance().proxy.initGeneralConfig(datas[0],(datas[2] as JsonAsset).json);\n                GeneralCommand.getInstance().proxy.initGeneralTex(datas[1]);\n\n                this.loadFinish();\n            },\n            this\n        );\n\n        \n    }\n\n    protected loadFinish(): void{\n        this.opNode.active = true;\n        this.tipsLab.string = \"\";\n        this._isLoading = false;\n\n        let cfgs = GeneralCommand.getInstance().proxy.getGeneralAllCfg();\n        this._cfgs = Array.from(cfgs.values());\n        this._cfgs.sort(this.sortStar);\n\n        \n        var probability: number = 100;\n        for (let index = 0; index < this._cfgs.length; index++) {\n            var e = this._cfgs[index];\n            if (e.star == 5){\n                probability = Math.floor(Math.random() * 20) + 5;\n            }else if(e.star == 4){\n                probability = Math.floor(Math.random() * 30) + 20;\n            }else if(e.star == 3){\n                probability = Math.floor(Math.random() * 200) + 300;\n            }else if(e.star == 2){\n                probability = Math.floor(Math.random() * 200) + 400;\n            }else if(e.star == 1){\n                probability = Math.floor(Math.random() * 200) + 500;\n            }\n            e.probability = probability;\n        }\n        \n\n        this.show(this._curIndex);\n    }\n\n    protected show(idx:number):void {\n       \n        if(this._cfgs.length > 0){\n            if(idx < 0){\n                idx = this._cfgs.length-1\n            }else if(idx >= this._cfgs.length){\n                idx = 0\n            }\n\n            if(this._generalNode == null){\n                var g = instantiate(this.generalRoster);\n                g.parent = this.generalParentNode;\n                this._generalNode = g;\n            }\n\n            var cfg = this._cfgs[idx];\n            this._generalNode.getComponent(GeneralRosterLogic).setData(cfg);\n\n            this.nameEditBox.string = cfg.name;\n            this.xjEditBox.string = cfg.star + \"\";\n\n            this.wlEditBox.string = (cfg.force / 100) + \"\";\n            this.fyEditBox.string = (cfg.defense / 100) + \"\";\n            this.mlEditBox.string = (cfg.strategy / 100) + \"\";\n            this.sdEditBox.string = (cfg.speed / 100) + \"\";\n            this.gcEditBox.string = (cfg.destroy / 100) + \"\";\n\n            this.wlAddEditBox.string = (cfg.force_grow / 100) + \"\";\n            this.fyAddEditBox.string = (cfg.defense_grow / 100) + \"\";\n            this.mlAddEditBox.string = (cfg.strategy_grow / 100) + \"\";\n            this.sdAddEditBox.string = (cfg.speed_grow / 100) + \"\";\n            this.gcAddEditBox.string = (cfg.defense_grow / 100) + \"\";\n\n            this.costEditBox.string = cfg.cost + \"\";\n            this.toggleCampGroup.toggleItems[cfg.camp-1].isChecked = true;\n\n            console.log(\"cfg.arms:\", cfg);\n            if(cfg.arms[0] == 1){\n                this.toggleArmGroup.toggleItems[0].isChecked = true;\n            }else if(cfg.arms[0] == 2){\n                this.toggleArmGroup.toggleItems[1].isChecked = true;\n            }else {\n                this.toggleArmGroup.toggleItems[2].isChecked = true;\n            }\n            \n        }\n    }\n\n    protected refresh(): void {\n        console.log(\"refresh\");\n\n        //刷新\n        this._cfgs[this._curIndex].name = this.nameEditBox.string;\n\n        var xj = parseInt(this.xjEditBox.string);\n        if(0 < xj && xj <= 5){\n            this._cfgs[this._curIndex].star = xj;\n        } \n\n        this._cfgs[this._curIndex].force = parseInt(this.wlEditBox.string)*100;\n        this._cfgs[this._curIndex].strategy = parseInt(this.mlEditBox.string)*100;\n        this._cfgs[this._curIndex].defense = parseInt(this.fyEditBox.string)*100;\n        this._cfgs[this._curIndex].speed = parseInt(this.sdEditBox.string)*100;\n        this._cfgs[this._curIndex].destroy = parseInt(this.gcEditBox.string)*100;\n\n        this._cfgs[this._curIndex].force_grow = Number(this.wlAddEditBox.string)*100;\n        this._cfgs[this._curIndex].strategy_grow = Number(this.mlAddEditBox.string)*100;\n        this._cfgs[this._curIndex].defense_grow = Number(this.fyAddEditBox.string)*100;\n        this._cfgs[this._curIndex].speed_grow = Number(this.sdAddEditBox.string)*100;\n        this._cfgs[this._curIndex].destroy_grow = Number(this.gcAddEditBox.string)*100;\n\n        this._cfgs[this._curIndex].cost = parseInt(this.costEditBox.string);\n\n        var items = this.toggleCampGroup.toggleItems;\n        for (let index = 0; index < items.length; index++) {\n            let item = items[index];\n            if(item.isChecked){\n                this._cfgs[this._curIndex].camp = index+1;\n            }\n        }\n\n        var items2 = this.toggleArmGroup.toggleItems;\n        for (let index = 0; index < items2.length; index++) {\n            let item = items2[index];\n            if(item.isChecked){\n                if(index == 0){\n                    this._cfgs[this._curIndex].arms = [1,4,7];\n                }else if(index == 1){\n                    this._cfgs[this._curIndex].arms = [2,5,8];\n                }else{\n                    this._cfgs[this._curIndex].arms = [3,6,9];\n                }\n            }\n        }\n    }\n    \n    protected onClickMake(): void {\n        \n        if(this._isLoading){\n            return\n        }\n\n        this.refresh();\n\n        if (this.outDirEditBox.string == \"\"){\n            this.tipsLab.string = \"请输入生成输出目录\";\n            return\n        }\n\n        if (!JSB) {\n            this.tipsLab.string = \"请使用 Windows 模拟器运行\";\n            return\n        }\n\n        var path = this.outDirEditBox.string;\n        if(jsb.fileUtils.isDirectoryExist(path) == false){\n            this.tipsLab.string = \"目录不存在\";\n            return\n        }\n\n        var obj = Object();\n        obj.title = \"武将配置\";\n        obj.list = this._cfgs\n       \n        var str = JSON.stringify(obj, null, \"\\t\");\n        jsb.fileUtils.writeStringToFile(str, path + \"/general.json\");\n        \n        this.tipsLab.string = \"保存成功\";\n    }\n\n    \n    protected onClickPre(): void {\n        if(this._isLoading){\n            return\n        }\n\n        this.refresh();\n\n        this._curIndex-=1;\n        this.show(this._curIndex);\n    }\n\n    protected onClickNext(): void {\n        if(this._isLoading){\n            return\n        }\n\n        this.refresh();\n        this._curIndex+=1;\n        this.show(this._curIndex);\n    }\n\n\n    protected sortStar(a: GeneralConfig, b: GeneralConfig): number {\n\n        if(a.star < b.star){\n            return 1;\n        }else if(a.star == b.star){\n            return a.cfgId - b.cfgId;\n        }else{\n            return -1;\n        }\n    }\n\n}\n"]}