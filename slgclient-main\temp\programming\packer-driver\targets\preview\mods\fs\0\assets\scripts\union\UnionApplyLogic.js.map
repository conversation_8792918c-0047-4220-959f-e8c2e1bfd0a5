{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "UnionCommand", "MapCommand", "EventMgr", "ListLogic", "LogicEvent", "ccclass", "property", "UnionApplyLogic", "onLoad", "on", "updateUnionApply", "updateApply", "verifyUnionSuccess", "getApply", "onDestroy", "targetOff", "data", "comp", "<PERSON><PERSON>iew", "node", "getComponent", "setData", "city", "getInstance", "cityProxy", "getMyMainCity", "unionData", "proxy", "getUnion", "unionId", "<PERSON><PERSON><PERSON><PERSON>", "rid", "unionApplyList", "id", "onEnable", "console", "log"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;;AAGzBC,MAAAA,Y;;AAGAC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;AACEC,MAAAA,U,iBAAAA,U;;;;;;;OARH;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBT,U;;yBAWPU,e,WADpBF,OAAO,CAAC,iBAAD,C,UAEHC,QAAQ,CAACP,UAAD,C,oCAFb,MACqBQ,eADrB,SAC6CT,SAD7C,CACuD;AAAA;AAAA;;AAAA;AAAA;;AAGzCU,QAAAA,MAAM,GAAO;AACnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,gBAAvB,EAAwC,KAAKC,WAA7C,EAAyD,IAAzD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,kBAAvB,EAA0C,KAAKC,QAA/C,EAAwD,IAAxD;AACH;;AAESC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACSJ,QAAAA,WAAW,CAACK,IAAD,EAAY;AAC7B,cAAIC,IAAI,GAAG,KAAKC,SAAL,CAAeC,IAAf,CAAoBC,YAApB;AAAA;AAAA,qCAAX;AACAH,UAAAA,IAAI,CAACI,OAAL,CAAaL,IAAI,GAACA,IAAD,GAAM,EAAvB;AACH;;AACSH,QAAAA,QAAQ,GAAO;AACrB,cAAIS,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIC,SAAe,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BI,KAA3B,CAAiCC,QAAjC,CAA0CN,IAAI,CAACO,OAA/C,CAAtB;;AACA,cAAGH,SAAS,CAACI,OAAV,CAAkBR,IAAI,CAACS,GAAvB,CAAH,EAA+B;AAC3B;AAAA;AAAA,8CAAaR,WAAb,GAA2BS,cAA3B,CAA0CN,SAAS,CAACO,EAApD;AACH;AACJ;;AACSC,QAAAA,QAAQ,GAAO;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA,eAAKvB,QAAL;AACH;;AAzBkD,O;;;;;iBAErB,I", "sourcesContent": ["// // Learn TypeScript:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html\n// // Learn Attribute:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html\n// // Learn life-cycle callbacks:\n// //  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html\n\nimport { _decorator, Component, ScrollView } from 'cc';\nconst {ccclass, property} = _decorator;\n\nimport UnionCommand from \"./UnionCommand\";\nimport { Union } from \"./UnionProxy\";\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport ListLogic from '../utils/ListLogic';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('UnionApplyLogic')\nexport default class UnionApplyLogic extends Component {\n    @property(ScrollView)\n    applyView:ScrollView | null = null;\n    protected onLoad():void{\n        EventMgr.on(LogicEvent.updateUnionApply,this.updateApply,this);\n        EventMgr.on(LogicEvent.verifyUnionSuccess,this.getApply,this);\n    }\n    \n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n    protected updateApply(data:any[]){\n        var comp = this.applyView.node.getComponent(ListLogic);\n        comp.setData(data?data:[]);\n    }\n    protected getApply():void{\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let unionData:Union = UnionCommand.getInstance().proxy.getUnion(city.unionId);\n        if(unionData.isMajor(city.rid)){\n            UnionCommand.getInstance().unionApplyList(unionData.id);\n        }\n    }\n    protected onEnable():void{\n        console.log(\"getApply\");\n        this.getApply()\n    }\n}\n\n"]}