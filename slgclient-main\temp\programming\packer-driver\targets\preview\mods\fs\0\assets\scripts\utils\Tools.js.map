{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts"], "names": ["Tools", "LocalCache", "getUUID", "uuid_str", "cache", "getUuid", "chars", "split", "uuid", "radix", "length", "i", "Math", "random", "join", "setUuid", "getCodeStr", "code", "str", "codeObj", "console", "log", "numberToShow", "num", "floor"], "mappings": ";;;0CAGaA,K;;;;;;;;;;;;;;AAFJC,MAAAA,U,iBAAAA,U;;;;;;;uBAEID,K,GAAN,MAAMA,KAAN,CAAW;AACO,eAAPE,OAAO,GAAS;AAC1B,cAAIC,QAAQ,GAAG,EAAf;AACA,cAAIC,KAAK,GAAG;AAAA;AAAA,wCAAWC,OAAX,EAAZ;;AACA,cAAGD,KAAK,IAAI,EAAZ,EAAe;AACX,gBAAIE,KAAK,GAAG,iEAAiEC,KAAjE,CAAuE,EAAvE,CAAZ;AACA,gBAAIC,IAAI,GAAG,EAAX;AACA,gBAAIC,KAAK,GAAG,KAAKH,KAAK,CAACI,MAAvB;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB;AAA6BH,cAAAA,IAAI,CAACG,CAAD,CAAJ,GAAUL,KAAK,CAAC,IAAIM,IAAI,CAACC,MAAL,KAAgBJ,KAArB,CAAf;AAA7B;;AACAD,YAAAA,IAAI,CAAC,CAAD,CAAJ,GAAUA,IAAI,CAAC,EAAD,CAAJ,GAAWA,IAAI,CAAC,EAAD,CAAJ,GAAWA,IAAI,CAAC,EAAD,CAAJ,GAAW,GAA3C;AACAL,YAAAA,QAAQ,GAAGK,IAAI,CAACM,IAAL,CAAU,EAAV,CAAX;AACA;AAAA;AAAA,0CAAWC,OAAX,CAAmBZ,QAAnB;AACH,WARD,MAQK;AACDA,YAAAA,QAAQ,GAAGC,KAAX;AACH;;AAED,iBAAOD,QAAP;AACH;;AAIuB,eAAVa,UAAU,CAACC,IAAD,EAAwB;AAAA,cAAvBA,IAAuB;AAAvBA,YAAAA,IAAuB,GAAT,CAAS;AAAA;;AAC5C,cAAIC,GAAG,GAAG,EAAV;AAEA,cAAIC,OAAO,GAAG,EAAd;AACAA,UAAAA,OAAO,CAAC,CAAC,CAAF,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,CAAC,CAAF,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,CAAC,CAAF,CAAP,GAAc,UAAd;AACAA,UAAAA,OAAO,CAAC,CAAC,CAAF,CAAP,GAAc,UAAd;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,IAAb;AAEAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,MAAb;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,OAAb;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,OAAb;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,OAAb;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,OAAb;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,WAAb;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,YAAb;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,UAAb;AACAA,UAAAA,OAAO,CAAC,CAAD,CAAP,GAAa,OAAb;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,OAAd;AAEAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,OAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AAEAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AAEAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,OAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,OAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,OAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AAEAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,OAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,KAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,KAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,UAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,aAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AAEAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,OAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AAEAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,MAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,SAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;AACAA,UAAAA,OAAO,CAAC,EAAD,CAAP,GAAc,QAAd;;AAGA,cAAIA,OAAO,CAACF,IAAD,CAAP,IAAiB,IAArB,EAA0B;AACtBC,YAAAA,GAAG,GAAG,QAAQD,IAAd;AACH,WAFD,MAEK;AACDC,YAAAA,GAAG,GAAGC,OAAO,CAACF,IAAD,CAAb;AACH;;AAEDG,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BH,GAA3B;AACA,iBAAOA,GAAP;AACH;;AAEyB,eAAZI,YAAY,CAACC,GAAD,EAAuB;AAAA,cAAtBA,GAAsB;AAAtBA,YAAAA,GAAsB,GAAT,CAAS;AAAA;;AAC7C,cAAIA,GAAG,IAAI,SAAX,EAAqB;AACjB,mBAAOX,IAAI,CAACY,KAAL,CAAWD,GAAG,GAAC,SAAf,IAA4B,GAAnC;AACH,WAFD,MAGK,IAAIA,GAAG,IAAI,KAAX,EAAiB;AAClB,mBAAOX,IAAI,CAACY,KAAL,CAAWD,GAAG,GAAC,KAAf,IAAwB,GAA/B;AACH,WAFI,MAEA;AACD,mBAAOA,GAAG,GAAG,EAAb;AACH;AACJ;;AA5Ha,O", "sourcesContent": ["\nimport { LocalCache } from \"./LocalCache\";\n\nexport class Tools{\n    public static getUUID():string{\n        let uuid_str = '';\n        let cache = LocalCache.getUuid();\n        if(cache == \"\"){\n            var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');\n            var uuid = [];\n            let radix = 16 | chars.length;\n            for (let i = 0; i < 36; i++) uuid[i] = chars[0 | Math.random() * radix];\n            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';\n            uuid_str = uuid.join('');\n            LocalCache.setUuid(uuid_str);\n        }else{\n            uuid_str = cache;\n        }\n\n        return uuid_str\n    }\n\n\n\n    public static getCodeStr(code:number = 0):string{\n        let str = \"\";\n\n        var codeObj = {};\n        codeObj[-4] = \"代理连接失败\";\n        codeObj[-3] = \"代理错误\";\n        codeObj[-2] = \"链接没有找到用户\";\n        codeObj[-1] = \"链接没有找到角色\";\n        codeObj[0] = \"成功\";\n\n        codeObj[1] = \"参数有误\";\n        codeObj[2] = \"数据库异常\";\n        codeObj[3] = \"用户已存在\";\n        codeObj[4] = \"密码不正确\";\n        codeObj[5] = \"用户不存在\";\n        codeObj[6] = \"session无效\";\n        codeObj[7] = \"Hardware错误\";\n        codeObj[8] = \"已经创建过角色了\";\n        codeObj[9] = \"角色不存在\";\n        codeObj[10] = \"城市不存在\";\n\n        codeObj[11] = \"城市不是自己的\";\n        codeObj[12] = \"升级失败\";\n        codeObj[13] = \"武将不存在\";\n        codeObj[14] = \"武将不是自己的\";\n        codeObj[15] = \"军队不是自己的\";\n        codeObj[16] = \"资源不足\";\n        codeObj[17] = \"超过带兵限制\";\n        codeObj[18] = \"军队再忙\";\n        codeObj[19] = \"将领再忙\";\n        codeObj[20] = \"不能放弃\";\n\n        codeObj[21] = \"领地不是自己的\";\n        codeObj[22] = \"军队没有主将\";\n        codeObj[23] = \"不可到达\";\n        codeObj[24] = \"体力不足\";\n        codeObj[25] = \"政令不足\";\n        codeObj[26] = \"金币不足\";\n        codeObj[27] = \"重复上阵\";\n        codeObj[28] = \"cost不足\";\n        codeObj[29] = \"没有该合成武将\";\n        codeObj[30] = \"合成武将非同名\";\n\n        codeObj[31] = \"统帅不足\";\n        codeObj[32] = \"升级失败\";\n        codeObj[33] = \"升级到最大星级\";\n        codeObj[34] = \"联盟创建失败\";\n        codeObj[35] = \"联盟不存在\";\n        codeObj[36] = \"权限不足\";\n        codeObj[37] = \"已经有联盟\";\n        codeObj[38] = \"不允许退出\";\n        codeObj[39] = \"内容太长\";\n        codeObj[40] = \"不属于该联盟\";\n\n        codeObj[41] = \"用户已满\";\n        codeObj[42] = \"已经申请过了\";\n        codeObj[43] = \"不能驻守\";\n        codeObj[44] = \"不能占领\";\n        codeObj[45] = \"没有募兵所\";\n        codeObj[46] = \"免战中\";\n        codeObj[47] = \"征兵中\";\n        codeObj[48] = \"领地已经在放弃了\";\n        codeObj[49] = \"不能再新建建筑在领地上\";\n        codeObj[50] = \"不能调兵\";\n\n        codeObj[51] = \"坑位已满\";\n        codeObj[52] = \"队伍在城外\";\n        codeObj[53] = \"不能升级建筑\";\n        codeObj[54] = \"不能拆除建筑\";\n        codeObj[55] = \"超过征收次数\";\n        codeObj[56] = \"cd内不能操作\";\n        codeObj[57] = \"武将超过上限了\";\n        codeObj[58] = \"没有集市\";\n        codeObj[59] = \"超过了收藏上限\";\n\n        codeObj[60] = \"超过了技能上限\";\n        codeObj[61] = \"装备技能失败\";\n        codeObj[62] = \"取下技能失败\";\n        codeObj[63] = \"兵种不符\";\n        codeObj[64] = \"该位置没有技能\";\n        codeObj[65] = \"技能等级已满\";\n        codeObj[66] = \"昵称已经存在\";\n       \n\n        if (codeObj[code] == null){\n            str = \"错误:\" + code;\n        }else{\n            str = codeObj[code]\n        }\n\n        console.log(\"getCodeStr:\", str)\n        return str;\n    }\n\n    public static numberToShow(num:number = 0):string{\n        if (num >= 100000000){\n            return Math.floor(num/100000000) + \"亿\"\n        }\n        else if (num >= 10000){\n            return Math.floor(num/10000) + \"万\"\n        }else{\n            return num + \"\"\n        }\n    }\n\n}\n"]}