System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _decorator, Component, Can<PERSON>, director, _dec, _class, _crd, ccclass, property, CanvasFixer;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Canvas = _cc.Canvas;
      director = _cc.director;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c5f46twwdxFzZLy7/3UgtGy", "CanvasFixer", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * Canvas修复器
       * 在运行时修复所有Canvas的alignCanvasWithScreen设置
       */

      _export("CanvasFixer", CanvasFixer = (_dec = ccclass('CanvasFixer'), _dec(_class = class CanvasFixer extends Component {
        onLoad() {
          // 修复当前场景的Canvas
          this.fixCurrentSceneCanvas(); // 监听场景切换，修复新场景的Canvas

          director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);
        }

        onDestroy() {
          director.off(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);
        }
        /**
         * 场景启动后的回调
         */


        onSceneLaunched(scene) {
          this.scheduleOnce(() => {
            this.fixSceneCanvas(scene);
          }, 0);
        }
        /**
         * 修复当前场景的Canvas
         */


        fixCurrentSceneCanvas() {
          const scene = director.getScene();

          if (scene) {
            this.fixSceneCanvas(scene);
          }
        }
        /**
         * 修复指定场景的Canvas
         */


        fixSceneCanvas(scene) {
          // 查找场景中的所有Canvas组件
          const canvasComponents = scene.getComponentsInChildren(Canvas);
          canvasComponents.forEach((canvas, index) => {
            console.log(`[CanvasFixer] 检查Canvas[${index}]: alignCanvasWithScreen=${canvas.alignCanvasWithScreen}`); // 强制设置为false，无论当前值是什么

            canvas.alignCanvasWithScreen = false; // 确保Canvas位置为原点

            canvas.node.setPosition(0, 0, 0);
            console.log(`[CanvasFixer] 修复Canvas[${index}]: alignCanvasWithScreen=false, position=(0,0,0)`); // 不再自动添加CanvasAdapter，避免冲突
          });
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=CanvasFixer.js.map