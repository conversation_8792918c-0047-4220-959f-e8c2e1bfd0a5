{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "Node", "instantiate", "UnionCommand", "MapCommand", "UnionMemberItemOpLogic", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "UnionMemberLogic", "onLoad", "on", "updateUnionMember", "updateMember", "kickUnionSuccess", "getMember", "unionAppoint", "unionAbdicate", "clickUnionMemberItem", "onClickItem", "onDestroy", "targetOff", "click", "instance", "playClick", "_op", "active", "menberData", "node", "opNode", "parent", "getComponent", "setData", "data", "city", "getInstance", "cityProxy", "getMyMainCity", "unionData", "proxy", "getUnion", "unionId", "comp", "<PERSON><PERSON><PERSON><PERSON>", "list", "getMemberList", "id", "concat", "updateBtn", "<PERSON><PERSON><PERSON><PERSON>", "rid", "exitButton", "disMissButton", "unionMember", "onEnable", "dismiss", "unionDismiss", "exit", "unionExit"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAG3CC,MAAAA,Y;;AAGAC,MAAAA,U;;AACAC,MAAAA,sB;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OATH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAYTa,gB,WADpBF,OAAO,CAAC,kBAAD,C,UAGHC,QAAQ,CAACV,UAAD,C,UAGRU,QAAQ,CAACT,IAAD,C,UAGRS,QAAQ,CAACT,IAAD,C,UAGRS,QAAQ,CAACT,IAAD,C,oCAZb,MACqBU,gBADrB,SAC8CZ,SAD9C,CACwD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,uCAc9B,IAd8B;AAAA;;AAgB1Ca,QAAAA,MAAM,GAAO;AAEnB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,iBAAvB,EAAyC,KAAKC,YAA9C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,gBAAvB,EAAwC,KAAKC,SAA7C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWK,YAAvB,EAAoC,KAAKD,SAAzC,EAAmD,IAAnD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWM,aAAvB,EAAqC,KAAKF,SAA1C,EAAoD,IAApD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,wCAAWO,oBAAvB,EAA4C,KAAKC,WAAjD,EAA6D,IAA7D;AAGH;;AAESC,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,KAAK,GAAO;AAClB;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AACA,cAAG,KAAKC,GAAL,IAAY,IAAf,EAAoB;AAChB,iBAAKA,GAAL,CAASC,MAAT,GAAkB,KAAlB;AACH;AACJ;;AAESP,QAAAA,WAAW,CAACQ,UAAD,EAAiB;AAClC;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;;AACA,cAAI,KAAKC,GAAL,IAAY,IAAhB,EAAqB;AACjB,gBAAIG,IAAI,GAAG5B,WAAW,CAAC,KAAK6B,MAAN,CAAtB;AACAD,YAAAA,IAAI,CAACE,MAAL,GAAc,KAAKF,IAAnB;AACA,iBAAKH,GAAL,GAAWG,IAAX;AACH;;AACD,eAAKH,GAAL,CAASC,MAAT,GAAkB,IAAlB;;AACA,eAAKD,GAAL,CAASM,YAAT;AAAA;AAAA,gEAA8CC,OAA9C,CAAsDL,UAAtD;AACH;;AAESd,QAAAA,YAAY,CAACoB,IAAD,EAAY;AAE9B,cAAIC,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIC,SAAe,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BI,KAA3B,CAAiCC,QAAjC,CAA0CN,IAAI,CAACO,OAA/C,CAAtB;AAEA,cAAIC,IAAI,GAAG,KAAKC,UAAL,CAAgBf,IAAhB,CAAqBG,YAArB,CAAkC,WAAlC,CAAX;AACA,cAAIa,IAAa,GAAG;AAAA;AAAA,4CAAaT,WAAb,GAA2BI,KAA3B,CAAiCM,aAAjC,CAA+CP,SAAS,CAACQ,EAAzD,EAA6DC,MAA7D,EAApB;AAEAL,UAAAA,IAAI,CAACV,OAAL,CAAaY,IAAb;AAEA,eAAKI,SAAL;AACH;;AAESA,QAAAA,SAAS,GAAE;AACjB,cAAId,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIC,SAAe,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BI,KAA3B,CAAiCC,QAAjC,CAA0CN,IAAI,CAACO,OAA/C,CAAtB;;AACA,cAAGH,SAAS,CAACW,WAAV,GAAwBC,GAAxB,IAA+BhB,IAAI,CAACgB,GAAvC,EAA2C;AACvC,iBAAKC,UAAL,CAAgBzB,MAAhB,GAAyB,KAAzB;AACA,iBAAK0B,aAAL,CAAmB1B,MAAnB,GAA4B,IAA5B;AACH,WAHD,MAGK;AACD,iBAAKyB,UAAL,CAAgBzB,MAAhB,GAAyB,IAAzB;AACA,iBAAK0B,aAAL,CAAmB1B,MAAnB,GAA4B,KAA5B;AACH;AACJ;;AAESX,QAAAA,SAAS,GAAO;AACtB,cAAImB,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,cAAIC,SAAe,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BI,KAA3B,CAAiCC,QAAjC,CAA0CN,IAAI,CAACO,OAA/C,CAAtB;AACA;AAAA;AAAA,4CAAaN,WAAb,GAA2BkB,WAA3B,CAAuCf,SAAS,CAACQ,EAAjD;AACH;;AAESQ,QAAAA,QAAQ,GAAO;AACrB,eAAKN,SAAL;AACA,eAAKjC,SAAL;AACH;;AAESwC,QAAAA,OAAO,GAAO;AACpB;AAAA;AAAA,4CAAahC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaW,WAAb,GAA2BqB,YAA3B;AACH;;AAESC,QAAAA,IAAI,GAAO;AACjB;AAAA;AAAA,4CAAalC,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,4CAAaW,WAAb,GAA2BuB,SAA3B;AACH;;AA7FmD,O;;;;;iBAG5B,I;;;;;;;iBAGF,I;;;;;;;iBAGH,I;;;;;;;iBAGJ,I", "sourcesContent": ["import { _decorator, Component, ScrollView, Node, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport UnionCommand from \"./UnionCommand\";\nimport { Member, Union } from \"./UnionProxy\";\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport UnionMemberItemOpLogic from \"./UnionMemberItemOpLogic\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('UnionMemberLogic')\nexport default class UnionMemberLogic extends Component {\n\n    @property(ScrollView)\n    memberView:ScrollView = null;\n\n    @property(Node)\n    disMissButton: Node = null;\n\n    @property(Node)\n    exitButton: Node = null;\n\n    @property(Node)\n    opNode: Node = null;\n\n    protected _op: Node = null;\n\n    protected onLoad():void{\n\n        EventMgr.on(LogicEvent.updateUnionMember,this.updateMember,this);\n        EventMgr.on(LogicEvent.kickUnionSuccess,this.getMember,this);\n        EventMgr.on(LogicEvent.unionAppoint,this.getMember,this);\n        EventMgr.on(LogicEvent.unionAbdicate,this.getMember,this);\n        EventMgr.on(LogicEvent.clickUnionMemberItem,this.onClickItem,this);\n        \n\n    }\n\n    protected onDestroy():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected click():void{\n        AudioManager.instance.playClick();\n        if(this._op != null){\n            this._op.active = false;\n        }\n    }\n\n    protected onClickItem(menberData):void{\n        AudioManager.instance.playClick();\n        if (this._op == null){\n            var node = instantiate(this.opNode);\n            node.parent = this.node;\n            this._op = node;\n        }\n        this._op.active = true;\n        this._op.getComponent(UnionMemberItemOpLogic).setData(menberData);\n    }\n\n    protected updateMember(data:any[]){\n\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let unionData:Union = UnionCommand.getInstance().proxy.getUnion(city.unionId);\n\n        var comp = this.memberView.node.getComponent(\"ListLogic\");\n        var list:Member[] = UnionCommand.getInstance().proxy.getMemberList(unionData.id).concat();\n\n        comp.setData(list);\n\n        this.updateBtn();\n    }\n\n    protected updateBtn(){\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let unionData:Union = UnionCommand.getInstance().proxy.getUnion(city.unionId);\n        if(unionData.getChairman().rid == city.rid){\n            this.exitButton.active = false;\n            this.disMissButton.active = true;\n        }else{\n            this.exitButton.active = true;\n            this.disMissButton.active = false;\n        }\n    }\n\n    protected getMember():void{\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        let unionData:Union = UnionCommand.getInstance().proxy.getUnion(city.unionId);\n        UnionCommand.getInstance().unionMember(unionData.id);\n    }\n\n    protected onEnable():void{\n        this.updateBtn();\n        this.getMember();\n    }\n\n    protected dismiss():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionDismiss();\n    }\n\n    protected exit():void{\n        AudioManager.instance.playClick();\n        UnionCommand.getInstance().unionExit();\n    }\n\n\n}\n"]}