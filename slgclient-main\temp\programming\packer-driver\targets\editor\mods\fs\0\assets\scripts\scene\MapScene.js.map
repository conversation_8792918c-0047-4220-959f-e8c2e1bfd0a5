{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts"], "names": ["_decorator", "Component", "Node", "Vec2", "TiledMap", "MapResBuildLogic", "MapBuildTipsLogic", "MapCityLogic", "MapCommand", "MapLogic", "MapResType", "MapResLogic", "MapUtil", "MapFacilityBuildLogic", "MapBuildTagLogic", "MapSysCityLogic", "EventMgr", "LogicEvent", "CoreEvent", "ccclass", "property", "MapScene", "onLoad", "_cmd", "getInstance", "tiledMap", "map<PERSON>ayer", "addComponent", "tmxAsset", "proxy", "tiledMapAsset", "initMapConfig", "initData", "on", "mapShowAreaChange", "onMapShowAreaChange", "scrollToMap", "onScrollToMap", "scheduleOnce", "myCity", "cityProxy", "getMyMainCity", "node", "getComponent", "setTiledMap", "scrollToMapPoint", "x", "y", "onTimer", "schedule", "emit", "loadComplete", "onDestroy", "targetOff", "clearData", "qryAreaIds", "length", "qryIndex", "shift", "qryData", "getMapAreaData", "checkAndUpdateQryTime", "qryNationMapScanBlock", "nowTime", "Date", "now", "_lastUpPosTime", "point", "getCurCenterPoint", "_centerX", "_centerY", "upPosition", "centerPoint", "centerAreaId", "addIds", "removeIds", "resLogic", "buildResLogic", "buildFacilityLogic", "tagLogic", "buildTipsLogic", "cityLogic", "sysCityLogic", "udpateShowAreas", "i", "areaData", "startCellX", "endCellX", "startCellY", "endCellY", "cellId", "getIdByCellPoint", "getResData", "addItem", "type", "SYS_CITY", "buildProxy", "getBuild", "build", "SYS_FORTRESS", "console", "log", "FORTRESS", "getCity", "old", "curCameraPoint", "cur", "toCameraPoint", "beforeScrollToMap"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;;AAGrCC,MAAAA,gB;;AACAC,MAAAA,iB;;AACAC,MAAAA,Y;;AAEAC,MAAAA,U;;AACAC,MAAAA,Q;;AACeC,MAAAA,U,iBAAAA,U;;AACfC,MAAAA,W;;AACAC,MAAAA,O;;AACAC,MAAAA,qB;;AACAC,MAAAA,gB;;AACAC,MAAAA,e;;AACEC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,S,kBAAAA,S;;;;;;;OAhBH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBpB,U;;yBAmBTqB,Q,WADpBF,OAAO,CAAC,UAAD,C,UAEHC,QAAQ,CAAClB,IAAD,C,oCAFb,MACqBmB,QADrB,SACsCpB,SADtC,CACgD;AAAA;AAAA;;AAAA;;AAAA,wCAIf,IAJe;;AAAA,4CAKf,CALe;;AAAA,4CAMf,CANe;;AAAA,kDAOT,CAPS;AAAA;;AASlCqB,QAAAA,MAAM,GAAS;AACrB,eAAKC,IAAL,GAAY;AAAA;AAAA,wCAAWC,WAAX,EAAZ,CADqB,CAIrB;;AACA,cAAIC,QAAkB,GAAG,KAAKC,QAAL,CAAcC,YAAd,CAA2BvB,QAA3B,CAAzB;AACAqB,UAAAA,QAAQ,CAACG,QAAT,GAAoB,KAAKL,IAAL,CAAUM,KAAV,CAAgBC,aAApC;AAEA;AAAA;AAAA,kCAAQC,aAAR,CAAsBN,QAAtB;;AACA,eAAKF,IAAL,CAAUS,QAAV;;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,iBAAvB,EAA0C,KAAKC,mBAA/C,EAAoE,IAApE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AAEA,eAAKC,YAAL,CAAkB,MAAM;AACpB,gBAAIC,MAAmB,GAAG,KAAKhB,IAAL,CAAUiB,SAAV,CAAoBC,aAApB,EAA1B;;AACA,iBAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,sCAAiCC,WAAjC,CAA6CnB,QAA7C;AACA,iBAAKiB,IAAL,CAAUC,YAAV;AAAA;AAAA,sCAAiCE,gBAAjC,CAAkD,IAAI1C,IAAJ,CAASoC,MAAM,CAACO,CAAhB,EAAmBP,MAAM,CAACQ,CAA1B,CAAlD;AACA,iBAAKC,OAAL,GAJoB,CAIL;AAClB,WALD,EAKG,GALH;AAOA,eAAKC,QAAL,CAAc,KAAKD,OAAnB,EAA4B,GAA5B;AAEA,eAAKV,YAAL,CAAkB,MAAI;AAClB;AAAA;AAAA,sCAASY,IAAT,CAAc;AAAA;AAAA,wCAAUC,YAAxB;AACH,WAFD,EAEG,GAFH;AAGH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;;AACA,eAAK9B,IAAL,CAAUM,KAAV,CAAgByB,SAAhB;;AACA,eAAK/B,IAAL,GAAY,IAAZ;AACH;;AAESyB,QAAAA,OAAO,GAAS;AAEtB,cAAI,KAAKzB,IAAL,CAAUM,KAAV,CAAgB0B,UAAhB,IAA8B,KAAKhC,IAAL,CAAUM,KAAV,CAAgB0B,UAAhB,CAA2BC,MAA3B,GAAoC,CAAtE,EAAyE;AACrE,gBAAIC,QAAgB,GAAG,KAAKlC,IAAL,CAAUM,KAAV,CAAgB0B,UAAhB,CAA2BG,KAA3B,EAAvB;;AACA,gBAAIC,OAAoB,GAAG,KAAKpC,IAAL,CAAUM,KAAV,CAAgB+B,cAAhB,CAA+BH,QAA/B,CAA3B;;AACA,gBAAIE,OAAO,CAACE,qBAAR,EAAJ,EAAqC;AACjC,mBAAKtC,IAAL,CAAUuC,qBAAV,CAAgCH,OAAhC;AACH;AACJ;;AACD,cAAII,OAAe,GAAGC,IAAI,CAACC,GAAL,EAAtB;;AACA,cAAIF,OAAO,GAAG,KAAKG,cAAf,GAAgC,IAApC,EAA0C;AACtC,iBAAKA,cAAL,GAAsBH,OAAtB,CADsC,CAEtC;;AACA,gBAAII,KAAW,GAAG;AAAA;AAAA,0CAAW3C,WAAX,GAAyBK,KAAzB,CAA+BuC,iBAA/B,EAAlB;;AACA,gBAAID,KAAK,IAAI,IAAT,KAAkB,KAAKE,QAAL,IAAiBF,KAAK,CAACrB,CAAvB,IAA4B,KAAKwB,QAAL,IAAiBH,KAAK,CAACpB,CAArE,CAAJ,EAA6E;AACzE,mBAAKsB,QAAL,GAAgBF,KAAK,CAACrB,CAAtB;AACA,mBAAKwB,QAAL,GAAgBH,KAAK,CAACpB,CAAtB;AACA;AAAA;AAAA,4CAAWvB,WAAX,GAAyB+C,UAAzB,CAAoCJ,KAAK,CAACrB,CAA1C,EAA6CqB,KAAK,CAACpB,CAAnD;AACH;AACJ;AACJ;;AAESZ,QAAAA,mBAAmB,CAACqC,WAAD,EAAoBC,YAApB,EAA0CC,MAA1C,EAA4DC,SAA5D,EAAuF;AAGhH,cAAIC,QAAqB,GAAG,KAAKlC,IAAL,CAAUC,YAAV;AAAA;AAAA,yCAA5B;AACA,cAAIkC,aAA+B,GAAG,KAAKnC,IAAL,CAAUC,YAAV;AAAA;AAAA,mDAAtC;AACA,cAAImC,kBAAyC,GAAG,KAAKpC,IAAL,CAAUC,YAAV;AAAA;AAAA,6DAAhD;AACA,cAAIoC,QAA0B,GAAG,KAAKrC,IAAL,CAAUC,YAAV;AAAA;AAAA,mDAAjC;AACA,cAAIqC,cAAiC,GAAG,KAAKtC,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAAxC;AACA,cAAIsC,SAAuB,GAAG,KAAKvC,IAAL,CAAUC,YAAV;AAAA;AAAA,2CAA9B;AACA,cAAIuC,YAA6B,GAAG,KAAKxC,IAAL,CAAUC,YAAV;AAAA;AAAA,iDAApC,CATgH,CAWhH;;AACAiC,UAAAA,QAAQ,CAACO,eAAT,CAAyBT,MAAzB,EAAiCC,SAAjC;AACAE,UAAAA,aAAa,CAACM,eAAd,CAA8BT,MAA9B,EAAsCC,SAAtC;AACAG,UAAAA,kBAAkB,CAACK,eAAnB,CAAmCT,MAAnC,EAA2CC,SAA3C;AACAI,UAAAA,QAAQ,CAACI,eAAT,CAAyBT,MAAzB,EAAiCC,SAAjC;AACAK,UAAAA,cAAc,CAACG,eAAf,CAA+BT,MAA/B,EAAuCC,SAAvC;AACAM,UAAAA,SAAS,CAACE,eAAV,CAA0BT,MAA1B,EAAkCC,SAAlC;AACAO,UAAAA,YAAY,CAACC,eAAb,CAA6BT,MAA7B,EAAqCC,SAArC,EAlBgH,CAoBhH;;AACA,eAAK,IAAIS,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGV,MAAM,CAAClB,MAAnC,EAA2C4B,CAAC,EAA5C,EAAgD;AAC5C,gBAAIC,QAAqB,GAAG,KAAK9D,IAAL,CAAUM,KAAV,CAAgB+B,cAAhB,CAA+Bc,MAAM,CAACU,CAAD,CAArC,CAA5B,CAD4C,CAE5C;;;AACA,iBAAK,IAAItC,CAAS,GAAGuC,QAAQ,CAACC,UAA9B,EAA0CxC,CAAC,GAAGuC,QAAQ,CAACE,QAAvD,EAAiEzC,CAAC,EAAlE,EAAsE;AAClE,mBAAK,IAAIC,CAAS,GAAGsC,QAAQ,CAACG,UAA9B,EAA0CzC,CAAC,GAAGsC,QAAQ,CAACI,QAAvD,EAAiE1C,CAAC,EAAlE,EAAsE;AAClE,oBAAI2C,MAAc,GAAG;AAAA;AAAA,wCAAQC,gBAAR,CAAyB7C,CAAzB,EAA4BC,CAA5B,CAArB,CADkE,CAElE;;AACA,oBAAI,KAAKxB,IAAL,CAAUM,KAAV,CAAgB+D,UAAhB,CAA2BF,MAA3B,CAAJ,EAAwC;AACpCd,kBAAAA,QAAQ,CAACiB,OAAT,CAAiBnB,MAAM,CAACU,CAAD,CAAvB,EAA4B,KAAK7D,IAAL,CAAUM,KAAV,CAAgB+D,UAAhB,CAA2BF,MAA3B,CAA5B;AACH;;AAED,oBAAI,KAAKnE,IAAL,CAAUM,KAAV,CAAgB+D,UAAhB,CAA2BF,MAA3B,EAAmCI,IAAnC,IAA2C;AAAA;AAAA,8CAAWC,QAA1D,EAAoE;AAChEb,kBAAAA,YAAY,CAACW,OAAb,CAAqBnB,MAAM,CAACU,CAAD,CAA3B,EAAgC,KAAK7D,IAAL,CAAUM,KAAV,CAAgB+D,UAAhB,CAA2BF,MAA3B,CAAhC;AACH,iBATiE,CAWlE;;;AACA,oBAAI,KAAKnE,IAAL,CAAUyE,UAAV,CAAqBC,QAArB,CAA8BP,MAA9B,KAAyC,IAA7C,EAAmD;AAC/C,sBAAIQ,KAAK,GAAG,KAAK3E,IAAL,CAAUyE,UAAV,CAAqBC,QAArB,CAA8BP,MAA9B,CAAZ;;AACA,sBAAGQ,KAAK,CAACJ,IAAN,IAAc;AAAA;AAAA,gDAAWC,QAA5B,EAAqC;AACjC;AACAb,oBAAAA,YAAY,CAACW,OAAb,CAAqBnB,MAAM,CAACU,CAAD,CAA3B,EAAgCc,KAAhC;AACH,mBAHD,MAGM,IAAGA,KAAK,CAACJ,IAAN,IAAc;AAAA;AAAA,gDAAWK,YAA5B,EAAyC;AAC3CC,oBAAAA,OAAO,CAACC,GAAR,CAAY,yBAAZ;AACAzB,oBAAAA,QAAQ,CAACiB,OAAT,CAAiBnB,MAAM,CAACU,CAAD,CAAvB,EAA4Bc,KAA5B;AACH,mBAHK,MAGA;AACFrB,oBAAAA,aAAa,CAACgB,OAAd,CAAsBnB,MAAM,CAACU,CAAD,CAA5B,EAAiCc,KAAjC;AACH;AACJ;;AAED,oBAAI,KAAK3E,IAAL,CAAUyE,UAAV,CAAqBC,QAArB,CAA8BP,MAA9B,KAAyC,IAA7C,EAAmD;AAC/CZ,kBAAAA,kBAAkB,CAACe,OAAnB,CAA2BnB,MAAM,CAACU,CAAD,CAAjC,EAAsC,KAAK7D,IAAL,CAAUyE,UAAV,CAAqBC,QAArB,CAA8BP,MAA9B,CAAtC;AACH,iBA3BiE,CA6BlE;;;AACA,oBAAI,KAAKnE,IAAL,CAAUM,KAAV,CAAgB+D,UAAhB,CAA2BF,MAA3B,EAAmCI,IAAnC,IAA2C;AAAA;AAAA,8CAAWQ,QAA1D,EAAoE;AAEhEvB,kBAAAA,QAAQ,CAACc,OAAT,CAAiBnB,MAAM,CAACU,CAAD,CAAvB,EAA4B,KAAK7D,IAAL,CAAUM,KAAV,CAAgB+D,UAAhB,CAA2BF,MAA3B,CAA5B;AACH;;AAED,oBAAI,KAAKnE,IAAL,CAAUyE,UAAV,CAAqBC,QAArB,CAA8BP,MAA9B,KAAyC,IAA7C,EAAmD;AAC/CV,kBAAAA,cAAc,CAACa,OAAf,CAAuBnB,MAAM,CAACU,CAAD,CAA7B,EAAkC,KAAK7D,IAAL,CAAUyE,UAAV,CAAqBC,QAArB,CAA8BP,MAA9B,CAAlC;AACH,iBArCiE,CAuClE;;;AACA,oBAAI,KAAKnE,IAAL,CAAUiB,SAAV,CAAoB+D,OAApB,CAA4Bb,MAA5B,KAAuC,IAA3C,EAAiD;AAC7CT,kBAAAA,SAAS,CAACY,OAAV,CAAkBnB,MAAM,CAACU,CAAD,CAAxB,EAA6B,KAAK7D,IAAL,CAAUiB,SAAV,CAAoB+D,OAApB,CAA4Bb,MAA5B,CAA7B;AACH;AAEJ;AACJ;AACJ;AACJ;;AAESrD,QAAAA,aAAa,CAACS,CAAD,EAAYC,CAAZ,EAA6B;AAChD,cAAIyD,GAAG,GAAG,KAAK9D,IAAL,CAAUC,YAAV;AAAA;AAAA,oCAAiC8D,cAAjC,EAAV;AACA,cAAIC,GAAG,GAAG,KAAKhE,IAAL,CAAUC,YAAV;AAAA;AAAA,oCAAiCgE,aAAjC,CAA+C,IAAIxG,IAAJ,CAAS2C,CAAT,EAAYC,CAAZ,CAA/C,CAAV;AAEA;AAAA;AAAA,oCAASG,IAAT,CAAc;AAAA;AAAA,wCAAW0D,iBAAzB,EAA4CF,GAAG,CAAC5D,CAAhD,EAAmD4D,GAAG,CAAC3D,CAAvD,EAA0DyD,GAAG,CAAC1D,CAA9D,EAAiE0D,GAAG,CAACzD,CAArE;AACA,eAAKL,IAAL,CAAUC,YAAV;AAAA;AAAA,oCAAiCE,gBAAjC,CAAkD,IAAI1C,IAAJ,CAAS2C,CAAT,EAAYC,CAAZ,CAAlD;AACH;;AAhJ2C,O;;;;;iBAE3B,I", "sourcesContent": ["import { _decorator, Component, Node, Vec2, TiledMap } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport MapResBuildLogic from \"../map/MapResBuildLogic\";\nimport MapBuildTipsLogic from \"../map/MapBuildTipsLogic\";\nimport MapCityLogic from \"../map/MapCityLogic\";\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\nimport MapLogic from \"../map/MapLogic\";\nimport { MapAreaData, MapResType } from \"../map/MapProxy\";\nimport MapResLogic from \"../map/MapResLogic\";\nimport MapUtil from \"../map/MapUtil\";\nimport MapFacilityBuildLogic from \"../map/MapFacilityBuildLogic\";\nimport MapBuildTagLogic from \"../map/MapBuildTagLogic\";\nimport MapSysCityLogic from \"../map/MapSysCityLogic\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { LogicEvent } from '../common/LogicEvent';\nimport { CoreEvent } from '../core/coreEvent';\n\n@ccclass('MapScene')\nexport default class MapScene extends Component {\n    @property(Node)\n    mapLayer: Node = null;\n\n    protected _cmd: MapCommand = null;\n    protected _centerX: number = 0;\n    protected _centerY: number = 0;\n    protected _lastUpPosTime: number = 0;\n\n    protected onLoad(): void {\n        this._cmd = MapCommand.getInstance();\n        \n\n        //初始化地图\n        let tiledMap: TiledMap = this.mapLayer.addComponent(TiledMap);\n        tiledMap.tmxAsset = this._cmd.proxy.tiledMapAsset;\n        \n        MapUtil.initMapConfig(tiledMap);\n        this._cmd.initData();\n        EventMgr.on(LogicEvent.mapShowAreaChange, this.onMapShowAreaChange, this);\n        EventMgr.on(LogicEvent.scrollToMap, this.onScrollToMap, this);\n        \n        this.scheduleOnce(() => {\n            let myCity: MapCityData = this._cmd.cityProxy.getMyMainCity();\n            this.node.getComponent(MapLogic).setTiledMap(tiledMap);\n            this.node.getComponent(MapLogic).scrollToMapPoint(new Vec2(myCity.x, myCity.y));\n            this.onTimer();//立即执行一次\n        }, 0.1);\n\n        this.schedule(this.onTimer, 0.2);\n\n        this.scheduleOnce(()=>{\n            EventMgr.emit(CoreEvent.loadComplete);\n        }, 0.6);\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        this._cmd.proxy.clearData();\n        this._cmd = null;\n    }\n\n    protected onTimer(): void {\n        \n        if (this._cmd.proxy.qryAreaIds && this._cmd.proxy.qryAreaIds.length > 0) {\n            let qryIndex: number = this._cmd.proxy.qryAreaIds.shift();\n            let qryData: MapAreaData = this._cmd.proxy.getMapAreaData(qryIndex);\n            if (qryData.checkAndUpdateQryTime()) {\n                this._cmd.qryNationMapScanBlock(qryData);\n            }\n        }\n        let nowTime: number = Date.now();\n        if (nowTime - this._lastUpPosTime > 1000) {\n            this._lastUpPosTime = nowTime;\n            //间隔一秒检测中心点是否改变\n            let point: Vec2 = MapCommand.getInstance().proxy.getCurCenterPoint();\n            if (point != null && (this._centerX != point.x || this._centerY != point.y)) {\n                this._centerX = point.x;\n                this._centerY = point.y;\n                MapCommand.getInstance().upPosition(point.x, point.y);\n            }\n        }\n    }\n\n    protected onMapShowAreaChange(centerPoint: Vec2, centerAreaId: number, addIds: number[], removeIds: number[]): void {\n        \n    \n        let resLogic: MapResLogic = this.node.getComponent(MapResLogic);\n        let buildResLogic: MapResBuildLogic = this.node.getComponent(MapResBuildLogic);\n        let buildFacilityLogic: MapFacilityBuildLogic = this.node.getComponent(MapFacilityBuildLogic);\n        let tagLogic: MapBuildTagLogic = this.node.getComponent(MapBuildTagLogic);\n        let buildTipsLogic: MapBuildTipsLogic = this.node.getComponent(MapBuildTipsLogic);\n        let cityLogic: MapCityLogic = this.node.getComponent(MapCityLogic);\n        let sysCityLogic: MapSysCityLogic = this.node.getComponent(MapSysCityLogic);\n\n        //更新展示区域\n        resLogic.udpateShowAreas(addIds, removeIds);\n        buildResLogic.udpateShowAreas(addIds, removeIds);\n        buildFacilityLogic.udpateShowAreas(addIds, removeIds);\n        tagLogic.udpateShowAreas(addIds, removeIds);\n        buildTipsLogic.udpateShowAreas(addIds, removeIds);\n        cityLogic.udpateShowAreas(addIds, removeIds);\n        sysCityLogic.udpateShowAreas(addIds, removeIds);\n\n        //更新区域内的具体节点\n        for (let i: number = 0; i < addIds.length; i++) {\n            let areaData: MapAreaData = this._cmd.proxy.getMapAreaData(addIds[i]);\n            // console.log(\"areaData\", areaData);\n            for (let x: number = areaData.startCellX; x < areaData.endCellX; x++) {\n                for (let y: number = areaData.startCellY; y < areaData.endCellY; y++) {\n                    let cellId: number = MapUtil.getIdByCellPoint(x, y);\n                    //资源\n                    if (this._cmd.proxy.getResData(cellId)) {\n                        resLogic.addItem(addIds[i], this._cmd.proxy.getResData(cellId));\n                    }\n\n                    if (this._cmd.proxy.getResData(cellId).type == MapResType.SYS_CITY) {\n                        sysCityLogic.addItem(addIds[i], this._cmd.proxy.getResData(cellId));\n                    }\n\n                    //建筑\n                    if (this._cmd.buildProxy.getBuild(cellId) != null) {\n                        var build = this._cmd.buildProxy.getBuild(cellId);\n                        if(build.type == MapResType.SYS_CITY){\n                            //系统城池\n                            sysCityLogic.addItem(addIds[i], build);\n                        }else if(build.type == MapResType.SYS_FORTRESS){\n                            console.log(\"MapResType.SYS_FORTRESS\");\n                            resLogic.addItem(addIds[i], build);\n                        } else{\n                            buildResLogic.addItem(addIds[i], build);\n                        }\n                    }\n\n                    if (this._cmd.buildProxy.getBuild(cellId) != null) {\n                        buildFacilityLogic.addItem(addIds[i], this._cmd.buildProxy.getBuild(cellId));\n                    }\n\n                    //标记\n                    if (this._cmd.proxy.getResData(cellId).type <= MapResType.FORTRESS) {\n            \n                        tagLogic.addItem(addIds[i], this._cmd.proxy.getResData(cellId));\n                    }\n\n                    if (this._cmd.buildProxy.getBuild(cellId) != null) {\n                        buildTipsLogic.addItem(addIds[i], this._cmd.buildProxy.getBuild(cellId));\n                    }\n\n                    //城池\n                    if (this._cmd.cityProxy.getCity(cellId) != null) {\n                        cityLogic.addItem(addIds[i], this._cmd.cityProxy.getCity(cellId));\n                    }\n\n                }\n            }\n        }\n    }\n\n    protected onScrollToMap(x: number, y: number): void {\n        let old = this.node.getComponent(MapLogic).curCameraPoint();\n        let cur = this.node.getComponent(MapLogic).toCameraPoint(new Vec2(x, y));\n        \n        EventMgr.emit(LogicEvent.beforeScrollToMap, cur.x, cur.y, old.x, old.y);\n        this.node.getComponent(MapLogic).scrollToMapPoint(new Vec2(x, y));\n    }\n}\n"]}