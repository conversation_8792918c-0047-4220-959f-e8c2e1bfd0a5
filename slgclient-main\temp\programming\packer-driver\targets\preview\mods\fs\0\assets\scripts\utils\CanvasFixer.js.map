{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "director", "ccclass", "property", "CanvasFixer", "onLoad", "fixCurrentSceneCanvas", "on", "EVENT_AFTER_SCENE_LAUNCH", "onSceneLaunched", "onDestroy", "off", "scene", "scheduleOnce", "fixSceneCanvas", "getScene", "canvasComponents", "getComponentsInChildren", "for<PERSON>ach", "canvas", "index", "console", "log", "alignCanvasWithScreen", "node", "setPosition"], "mappings": ";;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;;;;;;;OAElC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;AAE9B;AACA;AACA;AACA;;6BAEaM,W,WADZF,OAAO,CAAC,aAAD,C,gBAAR,MACaE,WADb,SACiCL,SADjC,CAC2C;AAE7BM,QAAAA,MAAM,GAAS;AACrB;AACA,eAAKC,qBAAL,GAFqB,CAIrB;;AACAL,UAAAA,QAAQ,CAACM,EAAT,CAAYN,QAAQ,CAACO,wBAArB,EAA+C,KAAKC,eAApD,EAAqE,IAArE;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxBT,UAAAA,QAAQ,CAACU,GAAT,CAAaV,QAAQ,CAACO,wBAAtB,EAAgD,KAAKC,eAArD,EAAsE,IAAtE;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,eAAe,CAACG,KAAD,EAAqB;AACxC,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAKC,cAAL,CAAoBF,KAApB;AACH,WAFD,EAEG,CAFH;AAGH;AAED;AACJ;AACA;;;AACYN,QAAAA,qBAAqB,GAAS;AAClC,cAAMM,KAAK,GAAGX,QAAQ,CAACc,QAAT,EAAd;;AACA,cAAIH,KAAJ,EAAW;AACP,iBAAKE,cAAL,CAAoBF,KAApB;AACH;AACJ;AAED;AACJ;AACA;;;AACYE,QAAAA,cAAc,CAACF,KAAD,EAAqB;AACvC;AACA,cAAMI,gBAAgB,GAAGJ,KAAK,CAACK,uBAAN,CAA8BjB,MAA9B,CAAzB;AAEAgB,UAAAA,gBAAgB,CAACE,OAAjB,CAAyB,CAACC,MAAD,EAASC,KAAT,KAAmB;AACxCC,YAAAA,OAAO,CAACC,GAAR,uCAAsCF,KAAtC,iCAAuED,MAAM,CAACI,qBAA9E,EADwC,CAGxC;;AACAJ,YAAAA,MAAM,CAACI,qBAAP,GAA+B,KAA/B,CAJwC,CAMxC;;AACAJ,YAAAA,MAAM,CAACK,IAAP,CAAYC,WAAZ,CAAwB,CAAxB,EAA2B,CAA3B,EAA8B,CAA9B;AAEAJ,YAAAA,OAAO,CAACC,GAAR,uCAAsCF,KAAtC,uDATwC,CAWxC;AACH,WAZD;AAaH;;AArDsC,O", "sourcesContent": ["import { _decorator, Component, Can<PERSON>, director, Scene } from 'cc';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * Canvas修复器\n * 在运行时修复所有Canvas的alignCanvasWithScreen设置\n */\n@ccclass('CanvasFixer')\nexport class CanvasFixer extends Component {\n    \n    protected onLoad(): void {\n        // 修复当前场景的Canvas\n        this.fixCurrentSceneCanvas();\n        \n        // 监听场景切换，修复新场景的Canvas\n        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);\n    }\n    \n    protected onDestroy(): void {\n        director.off(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);\n    }\n    \n    /**\n     * 场景启动后的回调\n     */\n    private onSceneLaunched(scene: Scene): void {\n        this.scheduleOnce(() => {\n            this.fixSceneCanvas(scene);\n        }, 0);\n    }\n    \n    /**\n     * 修复当前场景的Canvas\n     */\n    private fixCurrentSceneCanvas(): void {\n        const scene = director.getScene();\n        if (scene) {\n            this.fixSceneCanvas(scene);\n        }\n    }\n    \n    /**\n     * 修复指定场景的Canvas\n     */\n    private fixSceneCanvas(scene: Scene): void {\n        // 查找场景中的所有Canvas组件\n        const canvasComponents = scene.getComponentsInChildren(Canvas);\n        \n        canvasComponents.forEach((canvas, index) => {\n            console.log(`[CanvasFixer] 检查Canvas[${index}]: alignCanvasWithScreen=${canvas.alignCanvasWithScreen}`);\n\n            // 强制设置为false，无论当前值是什么\n            canvas.alignCanvasWithScreen = false;\n\n            // 确保Canvas位置为原点\n            canvas.node.setPosition(0, 0, 0);\n\n            console.log(`[CanvasFixer] 修复Canvas[${index}]: alignCanvasWithScreen=false, position=(0,0,0)`);\n\n            // 不再自动添加CanvasAdapter，避免冲突\n        });\n    }\n}\n"]}