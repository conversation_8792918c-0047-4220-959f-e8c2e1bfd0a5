{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON>", "view", "sys", "director", "screen", "ResolutionPolicy", "GameConfig", "ccclass", "property", "FixedScreenAdapter", "instance", "_instance", "onLoad", "console", "log", "onDestroy", "init", "windowSize", "width", "height", "designWidth", "baseWidth", "designHeight", "baseHeight", "setDesignResolutionSize", "EXACT_FIT", "fixAllCanvas", "on", "EVENT_AFTER_SCENE_LAUNCH", "onSceneLaunched", "setupResizeListener", "scheduleOnce", "printFinalInfo", "scene", "getScene", "warn", "canvasComponents", "getComponentsInChildren", "length", "for<PERSON>ach", "canvas", "index", "oldAlign", "alignCanvasWithScreen", "oldPos", "node", "position", "clone", "setPosition", "x", "y", "z", "uiTransform", "getComponent", "oldSize", "setContentSize", "setAnchorPoint", "resizeTimer", "handleResize", "clearTimeout", "setTimeout", "window", "addEventListener", "isMobile", "enableDebugInfo", "visibleSize", "getVisibleSize", "visible<PERSON><PERSON>in", "getVisibleOrigin", "designSize", "getDesignResolutionSize", "toFixed", "scaleX", "scaleY", "isIdeal", "Math", "abs", "getAdaptInfo", "printDebugInfo", "forceReadapt"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,Q,OAAAA,Q;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,gB,OAAAA,gB;;AACnEC,MAAAA,U,iBAAAA,U;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;AAE9B;AACA;AACA;AACA;AACA;;oCAEaW,kB,WADZF,OAAO,CAAC,oBAAD,C,mCAAR,MACaE,kBADb,SACwCV,SADxC,CACkD;AAIpB,mBAARW,QAAQ,GAAuB;AAC7C,iBAAOD,kBAAkB,CAACE,SAA1B;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrBH,UAAAA,kBAAkB,CAACE,SAAnB,GAA+B,IAA/B;AACAE,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB,cAAIN,kBAAkB,CAACE,SAAnB,KAAiC,IAArC,EAA2C;AACvCF,YAAAA,kBAAkB,CAACE,SAAnB,GAA+B,IAA/B;AACH;AACJ;AAED;AACJ;AACA;;;AACWK,QAAAA,IAAI,GAAS;AAChBH,UAAAA,OAAO,CAACC,GAAR,CAAY,mCAAZ,EADgB,CAGhB;;AACA,gBAAMG,UAAU,GAAGb,MAAM,CAACa,UAA1B;AACAJ,UAAAA,OAAO,CAACC,GAAR,CAAa,8BAA6BG,UAAU,CAACC,KAAM,IAAGD,UAAU,CAACE,MAAO,EAAhF,EALgB,CAOhB;;AACA,gBAAMC,WAAW,GAAG;AAAA;AAAA,wCAAWhB,MAAX,CAAkBiB,SAAtC;AACA,gBAAMC,YAAY,GAAG;AAAA;AAAA,wCAAWlB,MAAX,CAAkBmB,UAAvC,CATgB,CAWhB;;AACAtB,UAAAA,IAAI,CAACuB,uBAAL,CAA6BP,UAAU,CAACC,KAAxC,EAA+CD,UAAU,CAACE,MAA1D,EAAkEd,gBAAgB,CAACoB,SAAnF;AAEAZ,UAAAA,OAAO,CAACC,GAAR,CAAa,sCAAqCG,UAAU,CAACC,KAAM,IAAGD,UAAU,CAACE,MAAO,cAAxF,EAdgB,CAgBhB;;AACA,eAAKO,YAAL,GAjBgB,CAmBhB;;AACAvB,UAAAA,QAAQ,CAACwB,EAAT,CAAYxB,QAAQ,CAACyB,wBAArB,EAA+C,KAAKC,eAApD,EAAqE,IAArE,EApBgB,CAsBhB;;AACA,eAAKC,mBAAL,GAvBgB,CAyBhB;;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAKC,cAAL;AACH,WAFD,EAEG,GAFH;AAIAnB,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ;AACH;AAED;AACJ;AACA;;;AACYe,QAAAA,eAAe,CAACI,KAAD,EAAqB;AACxC,eAAKF,YAAL,CAAkB,MAAM;AACpBlB,YAAAA,OAAO,CAACC,GAAR,CAAY,uCAAZ;AACA,iBAAKY,YAAL;AACH,WAHD,EAGG,CAHH;AAIH;AAED;AACJ;AACA;;;AACYA,QAAAA,YAAY,GAAS;AACzB,gBAAMO,KAAK,GAAG9B,QAAQ,CAAC+B,QAAT,EAAd;;AACA,cAAI,CAACD,KAAL,EAAY;AACRpB,YAAAA,OAAO,CAACsB,IAAR,CAAa,+BAAb;AACA;AACH;;AAED,gBAAMC,gBAAgB,GAAGH,KAAK,CAACI,uBAAN,CAA8BrC,MAA9B,CAAzB;AACAa,UAAAA,OAAO,CAACC,GAAR,CAAa,2BAA0BsB,gBAAgB,CAACE,MAAO,YAA/D;AAEAF,UAAAA,gBAAgB,CAACG,OAAjB,CAAyB,CAACC,MAAD,EAASC,KAAT,KAAmB;AACxC5B,YAAAA,OAAO,CAACC,GAAR,CAAa,iCAAgC2B,KAAM,IAAnD,EADwC,CAGxC;;AACA,kBAAMC,QAAQ,GAAGF,MAAM,CAACG,qBAAxB;AACAH,YAAAA,MAAM,CAACG,qBAAP,GAA+B,KAA/B;AACA9B,YAAAA,OAAO,CAACC,GAAR,CAAa,8BAA6B4B,QAAS,UAAnD,EANwC,CAQxC;;AACA,kBAAME,MAAM,GAAGJ,MAAM,CAACK,IAAP,CAAYC,QAAZ,CAAqBC,KAArB,EAAf;AACAP,YAAAA,MAAM,CAACK,IAAP,CAAYG,WAAZ,CAAwB,CAAxB,EAA2B,CAA3B,EAA8B,CAA9B;AACAnC,YAAAA,OAAO,CAACC,GAAR,CAAa,kBAAiB8B,MAAM,CAACK,CAAE,KAAIL,MAAM,CAACM,CAAE,KAAIN,MAAM,CAACO,CAAE,eAAjE,EAXwC,CAaxC;;AACA,kBAAMC,WAAW,GAAGZ,MAAM,CAACa,YAAP,CAAoB,aAApB,CAApB;;AACA,gBAAID,WAAJ,EAAiB;AACb,oBAAMnC,UAAU,GAAGb,MAAM,CAACa,UAA1B;AACA,oBAAMqC,OAAO,GAAG;AAAEpC,gBAAAA,KAAK,EAAEkC,WAAW,CAAClC,KAArB;AAA4BC,gBAAAA,MAAM,EAAEiC,WAAW,CAACjC;AAAhD,eAAhB;AACAiC,cAAAA,WAAW,CAACG,cAAZ,CAA2BtC,UAAU,CAACC,KAAtC,EAA6CD,UAAU,CAACE,MAAxD;AACAiC,cAAAA,WAAW,CAACI,cAAZ,CAA2B,GAA3B,EAAgC,GAAhC;AACA3C,cAAAA,OAAO,CAACC,GAAR,CAAa,WAAUwC,OAAO,CAACpC,KAAM,IAAGoC,OAAO,CAACnC,MAAO,MAAKF,UAAU,CAACC,KAAM,IAAGD,UAAU,CAACE,MAAO,EAAlG;AACAN,cAAAA,OAAO,CAACC,GAAR,CAAa,oBAAb;AACH;;AAEDD,YAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8B2B,KAAM,QAAjD;AACH,WAzBD;AA0BH;AAED;AACJ;AACA;;;AACYX,QAAAA,mBAAmB,GAAS;AAChC,cAAI2B,WAAgB,GAAG,IAAvB;;AAEA,gBAAMC,YAAY,GAAG,MAAM;AACvB,gBAAID,WAAJ,EAAiB;AACbE,cAAAA,YAAY,CAACF,WAAD,CAAZ;AACH;;AACDA,YAAAA,WAAW,GAAGG,UAAU,CAAC,MAAM;AAC3B/C,cAAAA,OAAO,CAACC,GAAR,CAAY,wCAAZ;AACA,mBAAKE,IAAL,GAF2B,CAEd;AAChB,aAHuB,EAGrB,GAHqB,CAAxB;AAIH,WARD,CAHgC,CAahC;;;AACA6C,UAAAA,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkCJ,YAAlC,EAdgC,CAgBhC;;AACA,cAAIxD,GAAG,CAAC6D,QAAR,EAAkB;AACdF,YAAAA,MAAM,CAACC,gBAAP,CAAwB,mBAAxB,EAA6C,MAAM;AAC/CF,cAAAA,UAAU,CAAC,MAAM;AACb/C,gBAAAA,OAAO,CAACC,GAAR,CAAY,wCAAZ;AACA,qBAAKE,IAAL;AACH,eAHS,EAGP,GAHO,CAAV;AAIH,aALD;AAMH;;AAEDH,UAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ;AACH;AAED;AACJ;AACA;;;AACYkB,QAAAA,cAAc,GAAS;AAC3B,cAAI,CAAC;AAAA;AAAA,wCAAW5B,MAAX,CAAkB4D,eAAvB,EAAwC;AAExC,gBAAM/C,UAAU,GAAGb,MAAM,CAACa,UAA1B;AACA,gBAAMgD,WAAW,GAAGhE,IAAI,CAACiE,cAAL,EAApB;AACA,gBAAMC,aAAa,GAAGlE,IAAI,CAACmE,gBAAL,EAAtB;AACA,gBAAMC,UAAU,GAAGpE,IAAI,CAACqE,uBAAL,EAAnB;AAEAzD,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQG,UAAU,CAACC,KAAM,IAAGD,UAAU,CAACE,MAAO,EAA3D;AACAN,UAAAA,OAAO,CAACC,GAAR,CAAa,UAASuD,UAAU,CAACnD,KAAM,IAAGmD,UAAU,CAAClD,MAAO,EAA5D;AACAN,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQmD,WAAW,CAAC/C,KAAZ,CAAkBqD,OAAlB,CAA0B,CAA1B,CAA6B,IAAGN,WAAW,CAAC9C,MAAZ,CAAmBoD,OAAnB,CAA2B,CAA3B,CAA8B,EAAnF;AACA1D,UAAAA,OAAO,CAACC,GAAR,CAAa,UAASqD,aAAa,CAAClB,CAAd,CAAgBsB,OAAhB,CAAwB,CAAxB,CAA2B,KAAIJ,aAAa,CAACjB,CAAd,CAAgBqB,OAAhB,CAAwB,CAAxB,CAA2B,GAAhF,EAZ2B,CAc3B;;AACA,gBAAMC,MAAM,GAAGP,WAAW,CAAC/C,KAAZ,GAAoBmD,UAAU,CAACnD,KAA9C;AACA,gBAAMuD,MAAM,GAAGR,WAAW,CAAC9C,MAAZ,GAAqBkD,UAAU,CAAClD,MAA/C;AACAN,UAAAA,OAAO,CAACC,GAAR,CAAa,WAAU0D,MAAM,CAACD,OAAP,CAAe,CAAf,CAAkB,OAAME,MAAM,CAACF,OAAP,CAAe,CAAf,CAAkB,EAAjE,EAjB2B,CAmB3B;;AACA,gBAAMG,OAAO,GAAGC,IAAI,CAACC,GAAL,CAASJ,MAAM,GAAG,GAAlB,IAAyB,IAAzB,IACDG,IAAI,CAACC,GAAL,CAASH,MAAM,GAAG,GAAlB,IAAyB,IADxB,IAEDE,IAAI,CAACC,GAAL,CAAST,aAAa,CAAClB,CAAvB,IAA4B,CAF3B,IAGD0B,IAAI,CAACC,GAAL,CAAST,aAAa,CAACjB,CAAvB,IAA4B,CAH3C;;AAKA,cAAIwB,OAAJ,EAAa;AACT7D,YAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACH,WAFD,MAEO;AACHD,YAAAA,OAAO,CAACsB,IAAR,CAAa,oBAAb;;AACA,gBAAIwC,IAAI,CAACC,GAAL,CAAST,aAAa,CAAClB,CAAvB,IAA4B,CAA5B,IAAiC0B,IAAI,CAACC,GAAL,CAAST,aAAa,CAACjB,CAAvB,IAA4B,CAAjE,EAAoE;AAChErC,cAAAA,OAAO,CAACsB,IAAR,CAAc,gBAAegC,aAAa,CAAClB,CAAd,CAAgBsB,OAAhB,CAAwB,CAAxB,CAA2B,KAAIJ,aAAa,CAACjB,CAAd,CAAgBqB,OAAhB,CAAwB,CAAxB,CAA2B,GAAvF;AACH;;AACD,gBAAII,IAAI,CAACC,GAAL,CAASJ,MAAM,GAAG,GAAlB,IAAyB,IAAzB,IAAiCG,IAAI,CAACC,GAAL,CAASH,MAAM,GAAG,GAAlB,IAAyB,IAA9D,EAAoE;AAChE5D,cAAAA,OAAO,CAACsB,IAAR,CAAc,iBAAgBqC,MAAM,CAACD,OAAP,CAAe,CAAf,CAAkB,OAAME,MAAM,CAACF,OAAP,CAAe,CAAf,CAAkB,EAAxE;AACH;AACJ;;AAED1D,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;AACH;AAED;AACJ;AACA;;;AACW+D,QAAAA,YAAY,GAAQ;AACvB,gBAAM5D,UAAU,GAAGb,MAAM,CAACa,UAA1B;AACA,gBAAMgD,WAAW,GAAGhE,IAAI,CAACiE,cAAL,EAApB;AACA,gBAAMC,aAAa,GAAGlE,IAAI,CAACmE,gBAAL,EAAtB;AACA,gBAAMC,UAAU,GAAGpE,IAAI,CAACqE,uBAAL,EAAnB;AAEA,iBAAO;AACHrD,YAAAA,UADG;AAEHoD,YAAAA,UAFG;AAGHJ,YAAAA,WAHG;AAIHE,YAAAA,aAJG;AAKHK,YAAAA,MAAM,EAAEP,WAAW,CAAC/C,KAAZ,GAAoBmD,UAAU,CAACnD,KALpC;AAMHuD,YAAAA,MAAM,EAAER,WAAW,CAAC9C,MAAZ,GAAqBkD,UAAU,CAAClD;AANrC,WAAP;AAQH;AAED;AACJ;AACA;;;AACW2D,QAAAA,cAAc,GAAS;AAC1B,eAAK9C,cAAL;AACH;AAED;AACJ;AACA;;;AACW+C,QAAAA,YAAY,GAAS;AACxBlE,UAAAA,OAAO,CAACC,GAAR,CAAY,6BAAZ;AACA,eAAKE,IAAL;AACH;;AArN6C,O,wCAEC,I", "sourcesContent": ["import { _decorator, Component, Canvas, view, sys, director, Scene, screen, ResolutionPolicy } from 'cc';\nimport { GameConfig } from '../config/GameConfig';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 固定屏幕适配器\n * 专门解决点击偏移问题的最简单方案\n * 使用固定分辨率 + 强制Canvas设置\n */\n@ccclass('FixedScreenAdapter')\nexport class FixedScreenAdapter extends Component {\n    \n    private static _instance: FixedScreenAdapter = null;\n    \n    public static get instance(): FixedScreenAdapter {\n        return FixedScreenAdapter._instance;\n    }\n    \n    protected onLoad(): void {\n        FixedScreenAdapter._instance = this;\n        console.log('[FixedScreenAdapter] 固定屏幕适配器初始化');\n    }\n    \n    protected onDestroy(): void {\n        if (FixedScreenAdapter._instance === this) {\n            FixedScreenAdapter._instance = null;\n        }\n    }\n    \n    /**\n     * 初始化屏幕适配\n     */\n    public init(): void {\n        console.log('[FixedScreenAdapter] 开始初始化屏幕适配...');\n        \n        // 获取屏幕信息\n        const windowSize = screen.windowSize;\n        console.log(`[FixedScreenAdapter] 窗口尺寸: ${windowSize.width}x${windowSize.height}`);\n        \n        // 使用固定的设计分辨率，不进行任何缩放适配\n        const designWidth = GameConfig.screen.baseWidth;\n        const designHeight = GameConfig.screen.baseHeight;\n        \n        // 关键：使用EXACT_FIT策略，强制匹配屏幕尺寸\n        view.setDesignResolutionSize(windowSize.width, windowSize.height, ResolutionPolicy.EXACT_FIT);\n        \n        console.log(`[FixedScreenAdapter] 设计分辨率设置为窗口尺寸: ${windowSize.width}x${windowSize.height} (EXACT_FIT)`);\n        \n        // 立即修复所有Canvas\n        this.fixAllCanvas();\n        \n        // 监听场景切换\n        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);\n        \n        // 监听屏幕尺寸变化\n        this.setupResizeListener();\n        \n        // 延迟打印最终信息\n        this.scheduleOnce(() => {\n            this.printFinalInfo();\n        }, 0.2);\n        \n        console.log('[FixedScreenAdapter] 屏幕适配初始化完成');\n    }\n    \n    /**\n     * 场景启动后的回调\n     */\n    private onSceneLaunched(scene: Scene): void {\n        this.scheduleOnce(() => {\n            console.log('[FixedScreenAdapter] 新场景启动，修复Canvas设置');\n            this.fixAllCanvas();\n        }, 0);\n    }\n    \n    /**\n     * 修复所有Canvas设置\n     */\n    private fixAllCanvas(): void {\n        const scene = director.getScene();\n        if (!scene) {\n            console.warn('[FixedScreenAdapter] 无法获取当前场景');\n            return;\n        }\n        \n        const canvasComponents = scene.getComponentsInChildren(Canvas);\n        console.log(`[FixedScreenAdapter] 找到 ${canvasComponents.length} 个Canvas组件`);\n        \n        canvasComponents.forEach((canvas, index) => {\n            console.log(`[FixedScreenAdapter] 修复Canvas[${index}]:`);\n            \n            // 最关键的设置：禁用自动对齐屏幕\n            const oldAlign = canvas.alignCanvasWithScreen;\n            canvas.alignCanvasWithScreen = false;\n            console.log(`  - alignCanvasWithScreen: ${oldAlign} → false`);\n            \n            // 确保Canvas位置为原点\n            const oldPos = canvas.node.position.clone();\n            canvas.node.setPosition(0, 0, 0);\n            console.log(`  - position: (${oldPos.x}, ${oldPos.y}, ${oldPos.z}) → (0, 0, 0)`);\n            \n            // 获取UITransform并设置尺寸\n            const uiTransform = canvas.getComponent('UITransform');\n            if (uiTransform) {\n                const windowSize = screen.windowSize;\n                const oldSize = { width: uiTransform.width, height: uiTransform.height };\n                uiTransform.setContentSize(windowSize.width, windowSize.height);\n                uiTransform.setAnchorPoint(0.5, 0.5);\n                console.log(`  - 尺寸: ${oldSize.width}x${oldSize.height} → ${windowSize.width}x${windowSize.height}`);\n                console.log(`  - 锚点: (0.5, 0.5)`);\n            }\n            \n            console.log(`[FixedScreenAdapter] Canvas[${index}] 修复完成`);\n        });\n    }\n    \n    /**\n     * 设置屏幕尺寸变化监听\n     */\n    private setupResizeListener(): void {\n        let resizeTimer: any = null;\n        \n        const handleResize = () => {\n            if (resizeTimer) {\n                clearTimeout(resizeTimer);\n            }\n            resizeTimer = setTimeout(() => {\n                console.log('[FixedScreenAdapter] 检测到窗口尺寸变化，重新适配...');\n                this.init(); // 重新初始化\n            }, 100);\n        };\n        \n        // 监听窗口尺寸变化\n        window.addEventListener('resize', handleResize);\n        \n        // 监听屏幕方向变化（移动设备）\n        if (sys.isMobile) {\n            window.addEventListener('orientationchange', () => {\n                setTimeout(() => {\n                    console.log('[FixedScreenAdapter] 检测到屏幕方向变化，重新适配...');\n                    this.init();\n                }, 200);\n            });\n        }\n        \n        console.log('[FixedScreenAdapter] 屏幕尺寸变化监听已设置');\n    }\n    \n    /**\n     * 打印最终适配信息\n     */\n    private printFinalInfo(): void {\n        if (!GameConfig.screen.enableDebugInfo) return;\n        \n        const windowSize = screen.windowSize;\n        const visibleSize = view.getVisibleSize();\n        const visibleOrigin = view.getVisibleOrigin();\n        const designSize = view.getDesignResolutionSize();\n        \n        console.log('=== 固定屏幕适配最终信息 ===');\n        console.log(`窗口尺寸: ${windowSize.width}x${windowSize.height}`);\n        console.log(`设计分辨率: ${designSize.width}x${designSize.height}`);\n        console.log(`可视区域: ${visibleSize.width.toFixed(1)}x${visibleSize.height.toFixed(1)}`);\n        console.log(`可视原点: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);\n        \n        // 计算缩放比例\n        const scaleX = visibleSize.width / designSize.width;\n        const scaleY = visibleSize.height / designSize.height;\n        console.log(`缩放比例: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);\n        \n        // 理想状态检查\n        const isIdeal = Math.abs(scaleX - 1.0) < 0.01 && \n                       Math.abs(scaleY - 1.0) < 0.01 && \n                       Math.abs(visibleOrigin.x) < 1 && \n                       Math.abs(visibleOrigin.y) < 1;\n        \n        if (isIdeal) {\n            console.log('✅ 适配状态理想，点击应该正常工作');\n        } else {\n            console.warn('⚠️ 适配状态异常，可能存在点击偏移');\n            if (Math.abs(visibleOrigin.x) > 1 || Math.abs(visibleOrigin.y) > 1) {\n                console.warn(`  - 可视原点偏移: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);\n            }\n            if (Math.abs(scaleX - 1.0) > 0.01 || Math.abs(scaleY - 1.0) > 0.01) {\n                console.warn(`  - 缩放比例异常: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);\n            }\n        }\n        \n        console.log('============================');\n    }\n    \n    /**\n     * 获取当前适配信息\n     */\n    public getAdaptInfo(): any {\n        const windowSize = screen.windowSize;\n        const visibleSize = view.getVisibleSize();\n        const visibleOrigin = view.getVisibleOrigin();\n        const designSize = view.getDesignResolutionSize();\n        \n        return {\n            windowSize,\n            designSize,\n            visibleSize,\n            visibleOrigin,\n            scaleX: visibleSize.width / designSize.width,\n            scaleY: visibleSize.height / designSize.height\n        };\n    }\n    \n    /**\n     * 手动触发适配信息打印\n     */\n    public printDebugInfo(): void {\n        this.printFinalInfo();\n    }\n    \n    /**\n     * 强制重新适配\n     */\n    public forceReadapt(): void {\n        console.log('[FixedScreenAdapter] 强制重新适配');\n        this.init();\n    }\n}\n"]}