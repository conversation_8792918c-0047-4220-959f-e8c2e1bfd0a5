{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"], "names": ["ArmyCmd", "ArmyData", "ArmyProxy", "DateUtil", "createFromServer", "serverData", "armyData", "data", "id", "cityId", "order", "generals", "soldiers", "conTimes", "con_times", "conCnts", "con_cnts", "state", "cmd", "Return", "fromX", "to_x", "fromY", "to_y", "toX", "from_x", "toY", "from_y", "Idle", "Conscript", "x", "y", "startTime", "start", "endTime", "end", "isGenConEnd", "index", "length", "conTime", "isAfterServerTime", "Map", "clearData", "_armys", "clear", "getArmyList", "has", "get", "updateArmys", "datas", "list", "Array", "_maxArmyCnt", "set", "i", "updateArmy", "updateArmysNoCity", "push", "getArmyById", "getArmyByOrder", "console", "log", "getAllArmys", "for<PERSON>ach", "concat", "getArmysByPos", "armys", "army"], "mappings": ";;;wCAGaA,O,EAWAC,Q,EA4EQC,S;;;;;;;;;;;;;;;;;;;;AA1FdC,MAAAA,Q;;;;;;;AAEP;yBACaH,O,GAAN,MAAMA,OAAN,CAAc,E;AAUrB;;;sBAVaA,O,UACa,C;;sBADbA,O,YAEe,C;;sBAFfA,O,cAGiB,C;;sBAHjBA,O,aAIgB,C;;sBAJhBA,O,YAKe,C;;sBALfA,O,eAMkB,C;;sBANlBA,O,cAOiB,C;;0BAIjBC,Q,GAAN,MAAMA,QAAN,CAAe;AAAA;AAAA,sCACL,CADK;;AAAA,0CAED,CAFC;;AAAA,yCAGF,CAHE;;AAAA,4CAIG,EAJH;;AAAA,4CAKG,EALH;;AAAA,4CAMG,EANH;;AAAA,2CAOE,EAPF;;AAAA,uCAQJ,CARI;;AAAA,yCASF,CATE;;AAAA,yCAUF,CAVE;;AAAA,yCAWF,CAXE;;AAAA,uCAYJ,CAZI;;AAAA,uCAaJ,CAbI;;AAAA,6CAcE,CAdF;;AAAA,2CAeA,CAfA;;AAAA,qCAgBN,CAhBM;;AAAA,qCAiBN,CAjBM;AAAA;;AAmBK,eAAhBG,gBAAgB,CAACC,UAAD,EAAkBC,QAAlB,EAAuD;AAAA,cAArCA,QAAqC;AAArCA,YAAAA,QAAqC,GAAhB,IAAgB;AAAA;;AAC1E,cAAIC,IAAc,GAAGD,QAArB;;AACA,cAAIA,QAAQ,IAAI,IAAhB,EAAsB;AAClBC,YAAAA,IAAI,GAAG,IAAIN,QAAJ,EAAP;AACH;;AACDM,UAAAA,IAAI,CAACC,EAAL,GAAUH,UAAU,CAACG,EAArB;AACAD,UAAAA,IAAI,CAACE,MAAL,GAAcJ,UAAU,CAACI,MAAzB;AACAF,UAAAA,IAAI,CAACG,KAAL,GAAaL,UAAU,CAACK,KAAxB;AACAH,UAAAA,IAAI,CAACI,QAAL,GAAgBN,UAAU,CAACM,QAA3B;AACAJ,UAAAA,IAAI,CAACK,QAAL,GAAgBP,UAAU,CAACO,QAA3B;AAEAL,UAAAA,IAAI,CAACM,QAAL,GAAgBR,UAAU,CAACS,SAA3B;AACAP,UAAAA,IAAI,CAACQ,OAAL,GAAeV,UAAU,CAACW,QAA1B;AAEAT,UAAAA,IAAI,CAACU,KAAL,GAAaZ,UAAU,CAACY,KAAxB;AACAV,UAAAA,IAAI,CAACW,GAAL,GAAWb,UAAU,CAACa,GAAtB;;AACA,cAAIX,IAAI,CAACW,GAAL,IAAYlB,OAAO,CAACmB,MAAxB,EAAgC;AAC5B;AACAZ,YAAAA,IAAI,CAACa,KAAL,GAAaf,UAAU,CAACgB,IAAxB;AACAd,YAAAA,IAAI,CAACe,KAAL,GAAajB,UAAU,CAACkB,IAAxB;AACAhB,YAAAA,IAAI,CAACiB,GAAL,GAAWnB,UAAU,CAACoB,MAAtB;AACAlB,YAAAA,IAAI,CAACmB,GAAL,GAAWrB,UAAU,CAACsB,MAAtB;AACH,WAND,MAMO;AACHpB,YAAAA,IAAI,CAACa,KAAL,GAAaf,UAAU,CAACoB,MAAxB;AACAlB,YAAAA,IAAI,CAACe,KAAL,GAAajB,UAAU,CAACsB,MAAxB;AACApB,YAAAA,IAAI,CAACiB,GAAL,GAAWnB,UAAU,CAACgB,IAAtB;AACAd,YAAAA,IAAI,CAACmB,GAAL,GAAWrB,UAAU,CAACkB,IAAtB;AACH;;AACD,cAAIhB,IAAI,CAACW,GAAL,IAAYlB,OAAO,CAAC4B,IAApB,IAA4BrB,IAAI,CAACW,GAAL,IAAYlB,OAAO,CAAC6B,SAApD,EAA+D;AAC3D;AACAtB,YAAAA,IAAI,CAACuB,CAAL,GAASvB,IAAI,CAACa,KAAd;AACAb,YAAAA,IAAI,CAACwB,CAAL,GAASxB,IAAI,CAACe,KAAd;AACH,WAJD,MAIO;AACHf,YAAAA,IAAI,CAACuB,CAAL,GAASvB,IAAI,CAACiB,GAAd;AACAjB,YAAAA,IAAI,CAACwB,CAAL,GAASxB,IAAI,CAACmB,GAAd;AACH;;AAEDnB,UAAAA,IAAI,CAACyB,SAAL,GAAiB3B,UAAU,CAAC4B,KAAX,GAAmB,IAApC;AACA1B,UAAAA,IAAI,CAAC2B,OAAL,GAAe7B,UAAU,CAAC8B,GAAX,GAAiB,IAAhC;AAEA,iBAAO5B,IAAP;AACH;;AAEM6B,QAAAA,WAAW,GAAW;AACzB,eAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAKxB,QAAL,CAAcyB,MAA1C,EAAkDD,KAAK,EAAvD,EAA2D;AACvD,gBAAME,OAAO,GAAG,KAAK1B,QAAL,CAAcwB,KAAd,CAAhB;;AACA,gBAAIE,OAAO,IAAI,CAAf,EAAkB;AACd;AACH;;AACD,gBAAI;AAAA;AAAA,sCAASC,iBAAT,CAA2BD,OAAO,GAAC,IAAnC,CAAJ,EAA6C;AACzC,qBAAO,IAAP;AACH;AACJ;;AACD,iBAAO,KAAP;AACH;;AAzEiB,O;;yBA4EDrC,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,+CACK,CADL;;AAAA,0CAGiB,IAAIuC,GAAJ,EAHjB;AAAA;;AAKpBC,QAAAA,SAAS,GAAS;AACrB,eAAKC,MAAL,CAAYC,KAAZ;AACH;;AAEMC,QAAAA,WAAW,CAACpC,MAAD,EAA6B;AAC3C,cAAI,KAAKkC,MAAL,CAAYG,GAAZ,CAAgBrC,MAAhB,KAA2B,KAA/B,EAAsC;AAClC,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAKkC,MAAL,CAAYI,GAAZ,CAAgBtC,MAAhB,CAAP;AACH;;AAEMuC,QAAAA,WAAW,CAACvC,MAAD,EAAiBwC,KAAjB,EAA2C;AACzD,cAAIC,IAAgB,GAAG,KAAKL,WAAL,CAAiBpC,MAAjB,CAAvB;;AACA,cAAIyC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIC,KAAJ,CAAU,KAAKC,WAAf,CAAP;;AACA,iBAAKT,MAAL,CAAYU,GAAZ,CAAgB5C,MAAhB,EAAwByC,IAAxB;AACH;;AACD,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,KAAK,CAACX,MAA1B,EAAkCgB,CAAC,EAAnC,EAAuC;AACnC,gBAAIhD,QAAkB,GAAGL,QAAQ,CAACG,gBAAT,CAA0B6C,KAAK,CAACK,CAAD,CAA/B,CAAzB;AACAJ,YAAAA,IAAI,CAAC5C,QAAQ,CAACI,KAAT,GAAiB,CAAlB,CAAJ,GAA2BJ,QAA3B;AACH;;AACD,iBAAO4C,IAAP;AACH;;AAEMK,QAAAA,UAAU,CAAC9C,MAAD,EAAiBF,IAAjB,EAAsC;AACnD,cAAI2C,IAAgB,GAAG,KAAKL,WAAL,CAAiBpC,MAAjB,CAAvB;;AACA,cAAIyC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIC,KAAJ,CAAU,KAAKC,WAAf,CAAP;;AACA,iBAAKT,MAAL,CAAYU,GAAZ,CAAgB5C,MAAhB,EAAwByC,IAAxB;AACH;;AACD,cAAI5C,QAAkB,GAAG4C,IAAI,CAAC3C,IAAI,CAACG,KAAL,GAAa,CAAd,CAA7B;AACAwC,UAAAA,IAAI,CAAC3C,IAAI,CAACG,KAAL,GAAa,CAAd,CAAJ,GAAuBT,QAAQ,CAACG,gBAAT,CAA0BG,IAA1B,EAAgCD,QAAhC,CAAvB;AACA,iBAAO4C,IAAI,CAAC3C,IAAI,CAACG,KAAL,GAAa,CAAd,CAAX;AACH;;AAGM8C,QAAAA,iBAAiB,CAACP,KAAD,EAAyB;AAC7C,cAAIC,IAAgB,GAAG,EAAvB;;AACA,eAAI,IAAII,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGL,KAAK,CAACX,MAAzB,EAAiCgB,CAAC,EAAlC,EAAqC;AACjC,gBAAIhD,QAAkB,GAAGL,QAAQ,CAACG,gBAAT,CAA0B6C,KAAK,CAACK,CAAD,CAA/B,CAAzB;AACA,iBAAKC,UAAL,CAAgBjD,QAAQ,CAACG,MAAzB,EAAgCH,QAAhC;AACA4C,YAAAA,IAAI,CAACO,IAAL,CAAUnD,QAAV;AACH;;AAED,iBAAO4C,IAAP;AACH;AAED;;;AACOQ,QAAAA,WAAW,CAAClD,EAAD,EAAaC,MAAb,EAAuC;AACrD,cAAIyC,IAAgB,GAAG,KAAKL,WAAL,CAAiBpC,MAAjB,CAAvB;;AACA,cAAIyC,IAAJ,EAAU;AACN,iBAAK,IAAII,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGJ,IAAI,CAACZ,MAAjC,EAAyCgB,CAAC,EAA1C,EAA8C;AAC1C,kBAAIJ,IAAI,CAACI,CAAD,CAAJ,IAAWJ,IAAI,CAACI,CAAD,CAAJ,CAAQ9C,EAAR,IAAcA,EAA7B,EAAiC;AAC7B,uBAAO0C,IAAI,CAACI,CAAD,CAAX;AACH;AACJ;AACJ;;AACD,iBAAO,IAAP;AACH;AAED;;;AACOK,QAAAA,cAAc,CAACjD,KAAD,EAAgBD,MAAhB,EAA0C;AAC3D,cAAIyC,IAAgB,GAAG,KAAKL,WAAL,CAAiBpC,MAAjB,CAAvB;AACAmD,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BnD,KAA9B,EAAqCD,MAArC,EAA6CyC,IAA7C;;AACA,cAAIA,IAAJ,EAAU;AACN,mBAAOA,IAAI,CAACxC,KAAK,GAAG,CAAT,CAAX;AACH;;AACD,iBAAO,IAAP;AACH;;AAEMoD,QAAAA,WAAW,GAAe;AAC7B,cAAIZ,IAAe,GAAG,EAAtB;;AACA,eAAKP,MAAL,CAAYoB,OAAZ,CAAqBd,KAAD,IAAsB;AACtCC,YAAAA,IAAI,GAAGA,IAAI,CAACc,MAAL,CAAYf,KAAZ,CAAP;AACH,WAFD;;AAGA,iBAAOC,IAAP;AACH;;AAEMe,QAAAA,aAAa,CAACnC,CAAD,EAAYC,CAAZ,EAAuB;AACvC,cAAImB,IAAe,GAAG,EAAtB;;AACA,eAAKP,MAAL,CAAYoB,OAAZ,CAAqBG,KAAD,IAAsB;AACtCA,YAAAA,KAAK,CAACH,OAAN,CAAcI,IAAI,IAAI;AAClB,kBAAIA,IAAI,CAAC/C,KAAL,IAAcU,CAAd,IAAmBqC,IAAI,CAAC7C,KAAL,IAAcS,CAArC,EAAuC;AACnCmB,gBAAAA,IAAI,CAACO,IAAL,CAAUU,IAAV;AACH;AACJ,aAJD;AAKH,WAND;;AAOA,iBAAOjB,IAAP;AACH;;AA7F0B,O", "sourcesContent": ["import DateUtil from \"../utils/DateUtil\";\n\n/**军队命令*/\nexport class ArmyCmd {\n    static Idle: number = 0;//空闲\n    static Attack: number = 1;//攻击\n    static Garrison: number = 2;//驻军\n    static Reclaim: number = 3;//屯田\n    static Return: number = 4;//撤退\n    static Conscript: number = 5;//征兵\n    static Transfer: number = 6;//调动\n}\n\n/**军队数据*/\nexport class ArmyData {\n    id: number = 0;\n    cityId: number = 0;\n    order: number = 0;\n    generals: number[] = [];\n    soldiers: number[] = [];\n    conTimes: number[] = [];\n    conCnts: number[] = [];\n    cmd: number = 0;\n    state: number = 0;\n    fromX: number = 0;\n    fromY: number = 0;\n    toX: number = 0;\n    toY: number = 0;\n    startTime: number = 0;\n    endTime: number = 0;\n    x: number = 0;\n    y: number = 0;\n\n    static createFromServer(serverData: any, armyData: ArmyData = null): ArmyData {\n        let data: ArmyData = armyData;\n        if (armyData == null) {\n            data = new ArmyData();\n        }\n        data.id = serverData.id;\n        data.cityId = serverData.cityId;\n        data.order = serverData.order;\n        data.generals = serverData.generals;\n        data.soldiers = serverData.soldiers;\n\n        data.conTimes = serverData.con_times;\n        data.conCnts = serverData.con_cnts;\n\n        data.state = serverData.state;\n        data.cmd = serverData.cmd;\n        if (data.cmd == ArmyCmd.Return) {\n            //返回的时候 坐标是反的\n            data.fromX = serverData.to_x;\n            data.fromY = serverData.to_y;\n            data.toX = serverData.from_x;\n            data.toY = serverData.from_y;\n        } else {\n            data.fromX = serverData.from_x;\n            data.fromY = serverData.from_y;\n            data.toX = serverData.to_x;\n            data.toY = serverData.to_y;\n        }\n        if (data.cmd == ArmyCmd.Idle || data.cmd == ArmyCmd.Conscript) {\n            //代表是停留在城池中\n            data.x = data.fromX;\n            data.y = data.fromY;\n        } else {\n            data.x = data.toX;\n            data.y = data.toY;\n        }\n\n        data.startTime = serverData.start * 1000;\n        data.endTime = serverData.end * 1000;\n\n        return data;\n    }\n\n    public isGenConEnd():boolean {\n        for (let index = 0; index < this.conTimes.length; index++) {\n            const conTime = this.conTimes[index];\n            if (conTime == 0) {\n                continue\n            }\n            if (DateUtil.isAfterServerTime(conTime*1000)){\n                return true;\n            }\n        }\n        return false\n    }\n}\n\nexport default class ArmyProxy {\n    protected _maxArmyCnt: number = 5;//一个城池最大军队数量\n    //武将基础配置数据\n    protected _armys: Map<number, ArmyData[]> = new Map<number, ArmyData[]>();\n\n    public clearData(): void {\n        this._armys.clear();\n    }\n\n    public getArmyList(cityId: number): ArmyData[] {\n        if (this._armys.has(cityId) == false) {\n            return null;\n        }\n        return this._armys.get(cityId);\n    }\n\n    public updateArmys(cityId: number, datas: any[]): ArmyData[] {\n        let list: ArmyData[] = this.getArmyList(cityId);\n        if (list == null) {\n            list = new Array(this._maxArmyCnt);\n            this._armys.set(cityId, list);\n        }\n        for (var i = 0; i < datas.length; i++) {\n            let armyData: ArmyData = ArmyData.createFromServer(datas[i]);\n            list[armyData.order - 1] = armyData;\n        }\n        return list;\n    }\n\n    public updateArmy(cityId: number, data: any): ArmyData {\n        let list: ArmyData[] = this.getArmyList(cityId);\n        if (list == null) {\n            list = new Array(this._maxArmyCnt);\n            this._armys.set(cityId, list);\n        }\n        let armyData: ArmyData = list[data.order - 1];\n        list[data.order - 1] = ArmyData.createFromServer(data, armyData);\n        return list[data.order - 1];\n    }\n\n\n    public updateArmysNoCity(datas: any): ArmyData[] {\n        let list: ArmyData[] = [];\n        for(var i = 0; i < datas.length ;i++){\n            let armyData: ArmyData = ArmyData.createFromServer(datas[i]);\n            this.updateArmy(armyData.cityId,armyData);\n            list.push(armyData)\n        }\n\n        return list;\n    }\n\n    /**根据id获取军队*/\n    public getArmyById(id: number, cityId: number): ArmyData {\n        let list: ArmyData[] = this.getArmyList(cityId);\n        if (list) {\n            for (let i: number = 0; i < list.length; i++) {\n                if (list[i] && list[i].id == id) {\n                    return list[i];\n                }\n            }\n        }\n        return null;\n    }\n\n    /**根据位置获取军队*/\n    public getArmyByOrder(order: number, cityId: number): ArmyData {\n        let list: ArmyData[] = this.getArmyList(cityId);\n        console.log(\"getArmyByOrder\", order, cityId, list);\n        if (list) {\n            return list[order - 1];\n        }\n        return null;\n    }\n\n    public getAllArmys(): ArmyData[] {\n        let list:ArmyData[] = [];\n        this._armys.forEach((datas:ArmyData[]) => {\n            list = list.concat(datas);\n        })\n        return list;\n    }\n\n    public getArmysByPos(x: number, y: number) {\n        let list:ArmyData[] = [];\n        this._armys.forEach((armys:ArmyData[]) => {\n            armys.forEach(army => {\n                if (army.fromX == x && army.fromY == y){\n                    list.push(army);\n                }\n            });\n        })\n        return list;\n    }\n}\n"]}