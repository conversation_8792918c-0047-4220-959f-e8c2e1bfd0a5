System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Canvas, view, sys, UITransform, director, screen, GameConfig, _dec, _class, _class2, _temp, _crd, ccclass, property, ResponsiveAdapter;

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _reportPossibleCrUseOfGameConfig(extras) {
    _reporterNs.report("GameConfig", "../config/GameConfig", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Canvas = _cc.Canvas;
      view = _cc.view;
      sys = _cc.sys;
      UITransform = _cc.UITransform;
      director = _cc.director;
      screen = _cc.screen;
    }, function (_unresolved_2) {
      GameConfig = _unresolved_2.GameConfig;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "fb21fmP+6xF7YE7YbAusKAu", "ResponsiveAdapter", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 响应式UI适配器
       * 使用响应式设计理念，根据屏幕尺寸动态调整UI元素
       * 解决固定分辨率适配导致的点击偏移问题
       */

      _export("ResponsiveAdapter", ResponsiveAdapter = (_dec = ccclass('ResponsiveAdapter'), _dec(_class = (_temp = _class2 = class ResponsiveAdapter extends Component {
        constructor() {
          super(...arguments);

          _defineProperty(this, "_screenWidth", 0);

          _defineProperty(this, "_screenHeight", 0);

          _defineProperty(this, "_screenRatio", 0);

          _defineProperty(this, "_scale", 1.0);

          _defineProperty(this, "_baseWidth", (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.baseWidth);

          _defineProperty(this, "_baseHeight", (_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.baseHeight);

          _defineProperty(this, "_baseRatio", this._baseWidth / this._baseHeight);
        }

        static get instance() {
          return ResponsiveAdapter._instance;
        }

        onLoad() {
          ResponsiveAdapter._instance = this;
          console.log('[ResponsiveAdapter] 响应式适配器初始化');
        }

        onDestroy() {
          if (ResponsiveAdapter._instance === this) {
            ResponsiveAdapter._instance = null;
          }
        }
        /**
         * 初始化响应式适配
         */


        init() {
          if (!(_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.enableResponsiveDesign) {
            console.log('[ResponsiveAdapter] 响应式设计已禁用，跳过初始化');
            return;
          }

          console.log('[ResponsiveAdapter] 开始初始化响应式适配...'); // 获取屏幕信息

          this.updateScreenInfo(); // 设置Canvas为响应式模式

          this.setupResponsiveCanvas(); // 监听屏幕尺寸变化

          this.setupScreenResizeListener();
          console.log('[ResponsiveAdapter] 响应式适配初始化完成');
          this.printDebugInfo();
        }
        /**
         * 更新屏幕信息
         */


        updateScreenInfo() {
          // 使用新的API获取屏幕尺寸
          var windowSize = screen.windowSize;
          this._screenWidth = windowSize.width;
          this._screenHeight = windowSize.height;
          this._screenRatio = this._screenWidth / this._screenHeight; // 计算缩放比例（基于较小的维度）

          var scaleX = this._screenWidth / this._baseWidth;
          var scaleY = this._screenHeight / this._baseHeight;
          this._scale = Math.min(scaleX, scaleY); // 限制缩放范围

          this._scale = Math.max((_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.minScale, Math.min((_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.maxScale, this._scale));
          console.log("[ResponsiveAdapter] \u5C4F\u5E55\u4FE1\u606F\u66F4\u65B0:");
          console.log("[ResponsiveAdapter] \u5C4F\u5E55\u5C3A\u5BF8: " + this._screenWidth + "x" + this._screenHeight);
          console.log("[ResponsiveAdapter] \u5C4F\u5E55\u6BD4\u4F8B: " + this._screenRatio.toFixed(3));
          console.log("[ResponsiveAdapter] \u57FA\u51C6\u6BD4\u4F8B: " + this._baseRatio.toFixed(3));
          console.log("[ResponsiveAdapter] \u8BA1\u7B97\u7F29\u653E: " + this._scale.toFixed(3));
        }
        /**
         * 设置Canvas为响应式模式
         */


        setupResponsiveCanvas() {
          // 使用SHOW_ALL策略，确保内容完整显示且不变形
          view.setDesignResolutionSize(this._baseWidth, this._baseHeight, 2); // 2 = SHOW_ALL

          console.log("[ResponsiveAdapter] \u8BBE\u7F6E\u8BBE\u8BA1\u5206\u8FA8\u7387: " + this._baseWidth + "x" + this._baseHeight + " (SHOW_ALL)"); // 确保所有Canvas都使用正确的设置

          var scene = director.getScene();

          if (scene) {
            this.adaptSceneCanvas(scene);
          } // 监听场景切换


          director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);
        }
        /**
         * 场景启动后的回调
         */


        onSceneLaunched(scene) {
          this.scheduleOnce(() => {
            this.adaptSceneCanvas(scene);
          }, 0);
        }
        /**
         * 适配场景中的Canvas
         */


        adaptSceneCanvas(scene) {
          var canvasComponents = scene.getComponentsInChildren(Canvas);
          canvasComponents.forEach((canvas, index) => {
            console.log("[ResponsiveAdapter] \u9002\u914DCanvas[" + index + "]"); // 禁用自动对齐屏幕，避免坐标系统混乱

            canvas.alignCanvasWithScreen = false; // 设置Canvas位置为原点

            canvas.node.setPosition(0, 0, 0); // 设置Canvas尺寸为设计分辨率

            var uiTransform = canvas.getComponent(UITransform);

            if (uiTransform) {
              uiTransform.setContentSize(this._baseWidth, this._baseHeight);
              uiTransform.setAnchorPoint(0.5, 0.5);
            }

            console.log("[ResponsiveAdapter] Canvas[" + index + "] \u8BBE\u7F6E\u5B8C\u6210: \u5C3A\u5BF8=" + this._baseWidth + "x" + this._baseHeight + ", \u4F4D\u7F6E=(0,0,0)");
          });
        } // 移除复杂的UI元素适配逻辑
        // 使用SHOW_ALL策略让Cocos Creator自动处理适配

        /**
         * 设置屏幕尺寸变化监听
         */


        setupScreenResizeListener() {
          // 监听屏幕方向变化
          if (sys.isMobile) {
            window.addEventListener('orientationchange', () => {
              this.scheduleOnce(() => {
                console.log('[ResponsiveAdapter] 检测到屏幕方向变化');
                this.handleScreenResize();
              }, 0.2); // 增加延迟确保方向变化完成
            });
          } // 监听窗口尺寸变化（桌面环境）


          window.addEventListener('resize', () => {
            this.scheduleOnce(() => {
              console.log('[ResponsiveAdapter] 检测到窗口尺寸变化');
              this.handleScreenResize();
            }, 0.1);
          });
          console.log('[ResponsiveAdapter] 屏幕尺寸变化监听已设置');
        }
        /**
         * 处理屏幕尺寸变化
         */


        handleScreenResize() {
          var oldWidth = this._screenWidth;
          var oldHeight = this._screenHeight; // 更新屏幕信息

          this.updateScreenInfo(); // 检查尺寸是否真的发生了变化

          if (oldWidth !== this._screenWidth || oldHeight !== this._screenHeight) {
            console.log("[ResponsiveAdapter] \u5C4F\u5E55\u5C3A\u5BF8\u53D8\u5316: " + oldWidth + "x" + oldHeight + " \u2192 " + this._screenWidth + "x" + this._screenHeight); // 重新设置Canvas

            this.setupResponsiveCanvas();
            console.log('[ResponsiveAdapter] 屏幕尺寸变化适配完成');
            this.printDebugInfo();
          } else {
            console.log('[ResponsiveAdapter] 屏幕尺寸未发生实际变化，跳过重新适配');
          }
        }
        /**
         * 获取当前缩放比例
         */


        getScale() {
          return this._scale;
        }
        /**
         * 获取屏幕信息
         */


        getScreenInfo() {
          return {
            width: this._screenWidth,
            height: this._screenHeight,
            ratio: this._screenRatio,
            scale: this._scale
          };
        }
        /**
         * 打印调试信息
         */


        printDebugInfo() {
          if (!(_crd && GameConfig === void 0 ? (_reportPossibleCrUseOfGameConfig({
            error: Error()
          }), GameConfig) : GameConfig).screen.enableDebugInfo) return;
          console.log('=== 响应式适配调试信息 ===');
          console.log("\u5C4F\u5E55\u5C3A\u5BF8: " + this._screenWidth + "x" + this._screenHeight);
          console.log("\u57FA\u51C6\u5C3A\u5BF8: " + this._baseWidth + "x" + this._baseHeight);
          console.log("\u5C4F\u5E55\u6BD4\u4F8B: " + this._screenRatio.toFixed(3));
          console.log("\u57FA\u51C6\u6BD4\u4F8B: " + this._baseRatio.toFixed(3));
          console.log("\u7F29\u653E\u6BD4\u4F8B: " + this._scale.toFixed(3));
          console.log("\u8BBE\u8BA1\u5206\u8FA8\u7387: " + this._screenWidth + "x" + this._screenHeight + " (\u539F\u751F)");
          console.log('========================');
        }

      }, _defineProperty(_class2, "_instance", null), _temp)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ResponsiveAdapter.js.map