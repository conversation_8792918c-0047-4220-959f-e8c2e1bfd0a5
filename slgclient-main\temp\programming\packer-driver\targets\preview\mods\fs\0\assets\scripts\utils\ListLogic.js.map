{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"], "names": ["_decorator", "Component", "ScrollView", "Prefab", "Node", "NodePool", "EventHandler", "UITransform", "instantiate", "CCBoolean", "CCString", "Vec3", "ccclass", "property", "ListLogic", "onLoad", "_updateTimer", "_curIndex", "_newOffset", "_initContentPos", "_maxRowColSize", "_itemWidth", "_itemHeight", "itemPrefab", "data", "getComponent", "width", "scale", "height", "itemNode", "active", "isHorizontal", "scrollView", "content", "anchorX", "anchorY", "_isUpdateList", "_itemPool", "_items", "updateList", "onDestroy", "clear", "length", "_datas", "update", "dt", "updateInterval", "curOffset", "position", "x", "y", "Math", "max", "min", "_maxOffset", "setCurOffset", "_curO<PERSON>et", "isVirtual", "startIndex", "floor", "spaceColumn", "columnCount", "setStartIndex", "spaceRow", "index", "_startIndex", "suit", "i", "item", "index1", "iuit", "pos", "clone", "_row", "_toY", "_col", "_toX", "itemIdx", "setPosition", "updateItems", "updateItemCount", "count", "_itemCount", "children", "slice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "off", "EventType", "TOUCH_END", "onItemClick", "put", "createItem", "on", "<PERSON><PERSON><PERSON><PERSON>", "push", "console", "log", "hideItems", "stopAutoScroll", "rowCount", "showCount", "dataLen", "uit", "parent", "cuit", "autoColumnCount", "ceil", "getMaxScrollOffset", "isItemChange", "abs", "updateItem", "selectItem", "comp", "itemLogicScriptName", "getItem", "isSelected", "size", "get", "acitve", "setIndex", "oldItem", "newItem", "event", "target", "itemClickEvents", "for<PERSON>ach", "handler", "emit", "bind", "setData", "scrollOffset", "undefined", "isNaN", "scrollToIndex"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;;;;;;;OAC3H;AAACC,QAAAA,OAAD;AAAUC,QAAAA;AAAV,O,GAAsBb,U;;yBAGPc,S,WADpBF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACX,UAAD,C,UAGRW,QAAQ,CAACV,MAAD,C,UAIRU,QAAQ,CAACT,IAAD,C,UAGRS,QAAQ,CAACH,QAAD,C,UAIRG,QAAQ,CAACJ,SAAD,C,UAQRI,QAAQ,CAACJ,SAAD,C,UAiBRI,QAAQ,CAAC,CAACP,YAAD,CAAD,C,UAKRO,QAAQ,CAACJ,SAAD,C,oCA/Cb,MACqBK,SADrB,SACuCb,SADvC,CACiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,8CAiDjB,CAjDiB;;AAAA,8CAkDjB,CAlDiB;;AAAA,+CAmDhB,CAnDgB;;AAAA,8CAoDjB,CApDiB;;AAAA,gDAqDf,CArDe;;AAAA,6CAsDlB,CAtDkB;;AAAA,8CAuDjB,CAvDiB;;AAAA,mDAwDZ,CAxDY;;AAAA,kDAyDb,CAzDa;;AAAA,8CA0DjB,CA1DiB;;AAAA,+CA2DhB,CA3DgB;;AAAA,iDA4Db,KA5Da;;AAAA,6CA6DhB,IA7DgB;;AAAA,0CA8DxB,EA9DwB;;AAAA,0CA+DxB,IA/DwB;AAAA;;AAiEnCc,QAAAA,MAAM,GAAO;AACnB,eAAKC,YAAL,GAAoB,CAApB,CADmB,CACG;;AACtB,eAAKC,SAAL,GAAiB,CAAC,CAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKC,cAAL,GAAsB,CAAtB,CALmB,CAKK;;AACxB,eAAKC,UAAL,GAAkB,KAAKC,WAAL,GAAmB,CAArC;;AACA,cAAI,KAAKC,UAAT,EAAqB;AAEjB,iBAAKF,UAAL,GAAkB,KAAKE,UAAL,CAAgBC,IAAhB,CAAqBC,YAArB,CAAkClB,WAAlC,EAA+CmB,KAA/C,GAAuD,KAAKC,KAA9E,CAFiB,CAEmE;;AACpF,iBAAKL,WAAL,GAAmB,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBC,YAArB,CAAkClB,WAAlC,EAA+CqB,MAA/C,GAAwD,KAAKD,KAAhF,CAHiB,CAGqE;AACzF,WAJD,MAIO,IAAI,KAAKE,QAAT,EAAmB;AACtB,iBAAKA,QAAL,CAAcC,MAAd,GAAuB,KAAvB;AACA,iBAAKT,UAAL,GAAkB,KAAKQ,QAAL,CAAcJ,YAAd,CAA2BlB,WAA3B,EAAwCmB,KAAxC,GAAgD,KAAKC,KAAvE,CAFsB,CAEuD;;AAC7E,iBAAKL,WAAL,GAAmB,KAAKO,QAAL,CAAcJ,YAAd,CAA2BlB,WAA3B,EAAwCqB,MAAxC,GAAiD,KAAKD,KAAzE,CAHsB,CAGyD;AAClF;;AAED,cAAI,KAAKI,YAAT,EAAuB;AACnB,iBAAKC,UAAL,CAAgBC,OAAhB,CAAwBR,YAAxB,CAAqClB,WAArC,EAAkD2B,OAAlD,GAA4D,CAA5D;AACH,WAFD,MAEO;AACH,iBAAKF,UAAL,CAAgBC,OAAhB,CAAwBR,YAAxB,CAAqClB,WAArC,EAAkD4B,OAAlD,GAA4D,CAA5D;AACH;;AAED,eAAKC,aAAL,GAAqB,KAArB,CAvBmB,CAuBQ;;AAC3B,eAAKC,SAAL,GAAiB,IAAIhC,QAAJ,EAAjB,CAxBmB,CAwBa;;AAChC,eAAKiC,MAAL,GAAc,EAAd,CAzBmB,CAyBF;;AACjB,eAAKC,UAAL;AACH;;AAMSC,QAAAA,SAAS,GAAQ;AACvB,eAAKH,SAAL,CAAeI,KAAf;;AACA,eAAKH,MAAL,CAAYI,MAAZ,GAAqB,CAArB;AACA,eAAKC,MAAL,GAAc,IAAd;AACH;;AAGSC,QAAAA,MAAM,CAAEC,EAAF,EAAW;AACvB,eAAK7B,YAAL,IAAqB6B,EAArB;;AACA,cAAI,KAAK7B,YAAL,GAAoB,KAAK8B,cAA7B,EAA6C;AACzC,mBADyC,CAClC;AACV;;AACD,eAAK9B,YAAL,GAAoB,CAApB,CALuB,CAMvB;AACA;AACA;;AACA,cAAI,KAAKoB,aAAT,EAAwB;AACpB,mBADoB,CACb;AACV;;AACD,cAAIW,SAAS,GAAG,CAAhB;;AAEA,cAAI,KAAKhB,YAAT,EAAuB;AACnBgB,YAAAA,SAAS,GAAG,KAAK5B,eAAL,GAAuB,KAAKa,UAAL,CAAgBC,OAAhB,CAAwBe,QAAxB,CAAiCC,CAApE;AACH,WAFD,MAEO;AACHF,YAAAA,SAAS,GAAG,KAAKf,UAAL,CAAgBC,OAAhB,CAAwBe,QAAxB,CAAiCE,CAAjC,GAAqC,KAAK/B,eAAtD;AACH;;AACD4B,UAAAA,SAAS,GAAGI,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAASN,SAAT,EAAoB,KAAKO,UAAzB,CAAT,EAA+C,CAA/C,CAAZ;AACA,eAAKC,YAAL,CAAkBR,SAAlB;AAEH;;AAKSQ,QAAAA,YAAY,CAACR,SAAD,EAAiB;AAEnC,cAAI,KAAKJ,MAAL,IAAe,IAAf,IAAuB,KAAKA,MAAL,CAAYD,MAAZ,IAAsB,CAAjD,EAAoD;AAChD,mBADgD,CACzC;AACV;;AACD,cAAI,KAAKJ,MAAL,IAAe,IAAf,IAAuB,KAAKA,MAAL,CAAYI,MAAZ,IAAsB,CAAjD,EAAoD;AAChD,mBADgD,CACzC;AACV;;AACD,cAAI,KAAKc,UAAL,IAAmBT,SAAvB,EAAkC;AAC9B;AACA,iBAAKS,UAAL,GAAkBT,SAAlB;;AACA,gBAAI,KAAKU,SAAT,EAAoB;AAChB,kBAAI,KAAK1B,YAAT,EAAuB;AACnB,oBAAI2B,UAAU,GAAGP,IAAI,CAACQ,KAAL,CAAW,KAAKH,UAAL,IAAmB,KAAKnC,UAAL,GAAkB,KAAKuC,WAA1C,CAAX,IAAqE,KAAKC,WAA3F;AACA,qBAAKC,aAAL,CAAmBJ,UAAnB;AACH,eAHD,MAGO;AACH,oBAAIA,UAAU,GAAGP,IAAI,CAACQ,KAAL,CAAW,KAAKH,UAAL,IAAmB,KAAKlC,WAAL,GAAmB,KAAKyC,QAA3C,CAAX,IAAmE,KAAKF,WAAzF;AACA,qBAAKC,aAAL,CAAmBJ,UAAnB;AACH;AACJ,aARD,MAQO;AACH,mBAAKI,aAAL,CAAmB,CAAnB,EADG,CACmB;AACzB,aAb6B,CAc9B;;AACH;AACJ;;AAKSA,QAAAA,aAAa,CAACE,KAAD,EAAQ;AAC3B,cAAI,KAAKC,WAAL,IAAoBD,KAApB,IAA6B,KAAK1B,MAAL,CAAYI,MAAZ,GAAqB,CAAtD,EAAyD;AACrD;AACA,iBAAKuB,WAAL,GAAmBD,KAAnB;AACA,gBAAIE,IAAI,GAAG,KAAKlC,UAAL,CAAgBC,OAAhB,CAAwBR,YAAxB,CAAqClB,WAArC,CAAX;;AACA,iBAAK,IAAI4D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7B,MAAL,CAAYI,MAAhC,EAAwCyB,CAAC,EAAzC,EAA6C;AACzC,kBAAIC,IAAS,GAAG,KAAK9B,MAAL,CAAY6B,CAAZ,CAAhB;AACA,kBAAIE,MAAM,GAAG,KAAKJ,WAAL,GAAmBE,CAAhC;AACA,kBAAIG,IAAI,GAAGF,IAAI,CAAC3C,YAAL,CAAkBlB,WAAlB,CAAX;AACA,kBAAIgE,GAAG,GAAGH,IAAI,CAACpB,QAAL,CAAcwB,KAAd,EAAV;;AAEA,kBAAI,KAAKzC,YAAT,EAAuB;AACnB,oBAAI0C,IAAI,GAAGN,CAAC,GAAG,KAAKN,WAApB;;AACA,oBAAIa,IAAI,GAAGD,IAAI,IAAI,KAAKnD,WAAL,GAAmB,KAAKyC,QAA5B,CAAJ,GAA4CO,IAAI,CAACnC,OAAL,GAAe,KAAKb,WAAhE,GAA8E4C,IAAI,CAACtC,MAAL,GAAcsC,IAAI,CAAC/B,OAA5G;;AACAoC,gBAAAA,GAAG,CAACrB,CAAJ,GAAQ,CAACwB,IAAD,GAAQ,CAACR,IAAI,CAACtC,MAAL,GAAc,KAAKR,cAApB,IAAsC,CAAtD;AACAmD,gBAAAA,GAAG,CAACtB,CAAJ,GAAQE,IAAI,CAACQ,KAAL,CAAWU,MAAM,GAAG,KAAKR,WAAzB,KAAyC,KAAKxC,UAAL,GAAkB,KAAKuC,WAAhE,IAA+E,KAAKA,WAApF,GAAkG,CAAC,IAAIU,IAAI,CAACpC,OAAV,IAAqB,KAAKb,UAApI;AACH,eALD,MAKO;AACH,oBAAIsD,IAAI,GAAGR,CAAC,GAAG,KAAKN,WAApB;;AACA,oBAAIe,IAAI,GAAGD,IAAI,IAAI,KAAKtD,UAAL,GAAkB,KAAKuC,WAA3B,CAAJ,GAA8CU,IAAI,CAACpC,OAAL,GAAe,KAAKb,UAAlE,GAA+E6C,IAAI,CAACxC,KAAL,GAAawC,IAAI,CAAChC,OAA5G;;AACAqC,gBAAAA,GAAG,CAACtB,CAAJ,GAAQ2B,IAAI,GAAG,CAACV,IAAI,CAACxC,KAAL,GAAa,KAAKN,cAAnB,IAAqC,CAApD;AACAmD,gBAAAA,GAAG,CAACrB,CAAJ,GAAQ,CAACC,IAAI,CAACQ,KAAL,CAAWU,MAAM,GAAG,KAAKR,WAAzB,CAAD,IAA0C,KAAKvC,WAAL,GAAmB,KAAKyC,QAAlE,IAA8E,KAAKA,QAAnF,GAA8F,CAAC,IAAIO,IAAI,CAACnC,OAAV,IAAqB,KAAKb,WAAhI;AACH;;AACD8C,cAAAA,IAAI,CAACS,OAAL,GAAeR,MAAf;AACAD,cAAAA,IAAI,CAACU,WAAL,CAAiBP,GAAjB,EAlByC,CAmBzC;AACH;;AAED,iBAAKQ,WAAL;AACH;AACJ;AAIG;;;AACOC,QAAAA,eAAe,CAACC,KAAD,EAAgB;AACtC,cAAI,KAAKC,UAAL,IAAmBD,KAAvB,EAA8B;AAC1B,iBAAKC,UAAL,GAAkBD,KAAlB,CAD0B,CAE1B;;AACA,gBAAIE,QAAQ,GAAG,KAAKnD,UAAL,CAAgBC,OAAhB,CAAwBkD,QAAxB,CAAiCC,KAAjC,EAAf;AACA,iBAAKpD,UAAL,CAAgBC,OAAhB,CAAwBoD,iBAAxB;;AACA,iBAAK,IAAIlB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgB,QAAQ,CAACzC,MAA7B,EAAqCyB,CAAC,EAAtC,EAA0C;AACtC,kBAAIC,IAAI,GAAGe,QAAQ,CAAChB,CAAD,CAAnB;;AACA,kBAAIC,IAAI,CAACkB,OAAT,EAAkB;AACdlB,gBAAAA,IAAI,CAACmB,GAAL,CAASnF,IAAI,CAACoF,SAAL,CAAeC,SAAxB,EAAmC,KAAKC,WAAxC,EAAqD,IAArD;;AACA,qBAAKrD,SAAL,CAAesD,GAAf,CAAmBvB,IAAnB,EAFc,CAEW;;AAC5B;AACJ;;AACD,iBAAK9B,MAAL,CAAYI,MAAZ,GAAqB,CAArB;;AACA,iBAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKe,UAAzB,EAAqCf,CAAC,EAAtC,EAA0C;AACtC,kBAAIC,KAAI,GAAG,KAAKwB,UAAL,EAAX;;AACAxB,cAAAA,KAAI,CAACtC,MAAL,GAAc,KAAd;AACAsC,cAAAA,KAAI,CAACS,OAAL,GAAeV,CAAf,CAHsC,CAGrB;;AACjBC,cAAAA,KAAI,CAACyB,EAAL,CAAQzF,IAAI,CAACoF,SAAL,CAAeC,SAAvB,EAAkC,KAAKC,WAAvC,EAAoD,IAApD;;AACA,mBAAK1D,UAAL,CAAgBC,OAAhB,CAAwB6D,QAAxB,CAAiC1B,KAAjC;;AACA,mBAAK9B,MAAL,CAAYyD,IAAZ,CAAiB3B,KAAjB;AACH;;AACD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAKG;AACR;AACA;;;AACc7B,QAAAA,UAAU,GAAQ;AACxB,cAAI,KAAKI,MAAL,IAAe,IAAf,IAAuB,KAAKL,MAAL,IAAe,IAAtC,IAA8C,KAAKD,SAAL,IAAkB,IAApE,EAA0E;AACtE;AACH,WAHuB,CAIxB;;;AACA,cAAI,KAAKhB,UAAL,IAAmB,CAAnB,IAAwB,KAAKC,WAAL,IAAoB,CAAhD,EAAmD;AAC/C0E,YAAAA,OAAO,CAACC,GAAR,CAAY,sCAAZ;AACA;AACH;;AACD,cAAI,KAAKtD,MAAL,CAAYD,MAAZ,IAAsB,CAA1B,EAA6B;AACzB,iBAAKc,UAAL,GAAkB,KAAKS,WAAL,GAAmB,CAAC,CAAtC,CADyB,CACe;;AACxC,iBAAKiC,SAAL;AACA;AACH;;AACD,eAAK9D,aAAL,GAAqB,IAArB;AACA,eAAKJ,UAAL,CAAgBmE,cAAhB,GAfwB,CAeS;;AACjC,cAAIC,QAAQ,GAAG,CAAf;AACA,cAAIC,SAAS,GAAG,CAAhB;AACA,cAAIC,OAAO,GAAG,KAAK3D,MAAL,CAAYD,MAA1B;AACA,cAAI6D,GAAG,GAAG,KAAKvE,UAAL,CAAgBC,OAAhB,CAAwBuE,MAAxB,CAA+B/E,YAA/B,CAA4ClB,WAA5C,CAAV;AACA,cAAIkG,IAAI,GAAG,KAAKzE,UAAL,CAAgBC,OAAhB,CAAwBR,YAAxB,CAAqClB,WAArC,CAAX;;AACA,cAAI,KAAKwB,YAAT,EAAuB;AACnB,gBAAI,KAAK2E,eAAT,EAA0B;AACtB;AACA,mBAAK7C,WAAL,GAAmBV,IAAI,CAACQ,KAAL,CAAW4C,GAAG,CAAC3E,MAAJ,GAAa,KAAKN,WAA7B,CAAnB;AACH;;AACD,gBAAI,KAAKuC,WAAL,GAAmB,CAAvB,EAA0B;AACtB,mBAAKA,WAAL,GAAmB,CAAnB;AACH;;AACD,iBAAKzC,cAAL,GAAsB,KAAKyC,WAAL,IAAoB,KAAKvC,WAAL,GAAmB,KAAKyC,QAA5C,IAAwD,KAAKA,QAAnF;AACAqC,YAAAA,QAAQ,GAAGjD,IAAI,CAACwD,IAAL,CAAUJ,GAAG,CAAC7E,KAAJ,IAAa,KAAKL,UAAL,GAAkB,KAAKuC,WAApC,CAAV,IAA8D,CAAzE;;AACA,gBAAI,KAAKH,SAAT,EAAoB;AAChB4C,cAAAA,SAAS,GAAGD,QAAQ,GAAG,KAAKvC,WAA5B;AACH,aAFD,MAEO;AACHwC,cAAAA,SAAS,GAAGC,OAAZ;AACH;;AACDG,YAAAA,IAAI,CAAC/E,KAAL,GAAayB,IAAI,CAACwD,IAAL,CAAUL,OAAO,GAAG,KAAKzC,WAAzB,KAAyC,KAAKxC,UAAL,GAAkB,KAAKuC,WAAhE,CAAb;AACA,iBAAKN,UAAL,GAAkB,KAAKtB,UAAL,CAAgB4E,kBAAhB,GAAqC3D,CAAvD;AACA,iBAAK9B,eAAL,GAAuBoF,GAAG,CAAC7E,KAAJ,IAAa,IAAI6E,GAAG,CAACrE,OAArB,CAAvB;AACH,WAlBD,MAkBO;AACH,gBAAI,KAAKwE,eAAT,EAA0B;AACtB;AACA,mBAAK7C,WAAL,GAAmBV,IAAI,CAACQ,KAAL,CAAW4C,GAAG,CAAC7E,KAAJ,GAAY,KAAKL,UAA5B,CAAnB;AACH;;AACD,gBAAI,KAAKwC,WAAL,GAAmB,CAAvB,EAA0B;AACtB,mBAAKA,WAAL,GAAmB,CAAnB;AACH;;AACD,iBAAKzC,cAAL,GAAsB,KAAKyC,WAAL,IAAoB,KAAKxC,UAAL,GAAkB,KAAKuC,WAA3C,IAA0D,KAAKA,WAArF;AACAwC,YAAAA,QAAQ,GAAGjD,IAAI,CAACwD,IAAL,CAAUJ,GAAG,CAAC3E,MAAJ,IAAc,KAAKN,WAAL,GAAmB,KAAKyC,QAAtC,CAAV,IAA6D,CAAxE;;AACA,gBAAI,KAAKN,SAAT,EAAoB;AAChB4C,cAAAA,SAAS,GAAGD,QAAQ,GAAG,KAAKvC,WAA5B;AACH,aAFD,MAEO;AACHwC,cAAAA,SAAS,GAAGC,OAAZ;AACH;;AACDG,YAAAA,IAAI,CAAC7E,MAAL,GAAcuB,IAAI,CAACwD,IAAL,CAAUL,OAAO,GAAG,KAAKzC,WAAzB,KAAyC,KAAKvC,WAAL,GAAmB,KAAKyC,QAAjE,CAAd;AACA,iBAAKT,UAAL,GAAkB,KAAKtB,UAAL,CAAgB4E,kBAAhB,GAAqC1D,CAAvD;AACA,iBAAK/B,eAAL,GAAuBoF,GAAG,CAAC3E,MAAJ,IAAc,IAAI2E,GAAG,CAACpE,OAAtB,CAAvB;AACH;;AAED,cAAI0E,YAAY,GAAG,KAAK7B,eAAL,CAAqBqB,SAArB,CAAnB;AACA,eAAKnF,UAAL,GAAkBiC,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAAS,KAAKnC,UAAd,EAA0B,KAAKoC,UAA/B,CAAT,EAAqD,CAArD,CAAlB;;AAGA,cAAKuD,YAAY,IAAI,KAAK3F,UAAL,IAAmB,KAAKsC,UAA7C,EAA0D;AACtD,gBAAIe,GAAG,GAAG,KAAKvC,UAAL,CAAgBC,OAAhB,CAAwBe,QAAxB,CAAiCwB,KAAjC,EAAV;AACA,iBAAKhB,UAAL,GAAkB,KAAKtC,UAAvB;;AACA,gBAAI,KAAKa,YAAT,EAAuB;AACnBwC,cAAAA,GAAG,CAACtB,CAAJ,GAAQ,CAACE,IAAI,CAAC2D,GAAL,CAAS,KAAK3F,eAAL,GAAuB,KAAKD,UAArC,CAAT;AACH,aAFD,MAEO;AACHqD,cAAAA,GAAG,CAACrB,CAAJ,GAAQC,IAAI,CAAC2D,GAAL,CAAS,KAAK3F,eAAL,GAAuB,KAAKD,UAArC,CAAR;AACH;;AACD,iBAAKsC,UAAL,GAAkB,CAAC,CAAnB,CARsD,CAQjC;;AACrB,iBAAKS,WAAL,GAAmB,CAAC,CAApB,CATsD,CAShC;;AACtB,iBAAKV,YAAL,CAAkB,KAAKrC,UAAvB;AACA,iBAAKc,UAAL,CAAgBC,OAAhB,CAAwB6C,WAAxB,CAAoCP,GAApC;AACH,WAZD,MAYO;AACH,iBAAKQ,WAAL;AACH;;AACD,eAAK3C,aAAL,GAAqB,KAArB,CA9EwB,CA+ExB;AAEA;AACH,SAtT4C,CA4TzC;;;AACM2C,QAAAA,WAAW,GAAQ;AAEzB,eAAK,IAAIZ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7B,MAAL,CAAYI,MAAhC,EAAwCyB,CAAC,EAAzC,EAA6C;AACzC,gBAAIC,IAAI,GAAG,KAAK9B,MAAL,CAAY6B,CAAZ,CAAX,CADyC,CAEzC;;AACAC,YAAAA,IAAI,CAACtC,MAAL,GAAcsC,IAAI,CAACS,OAAL,GAAe,KAAKlC,MAAL,CAAYD,MAAzC;;AACA,gBAAI0B,IAAI,CAACtC,MAAT,EAAiB;AACb,mBAAKiF,UAAL,CAAgB3C,IAAhB,EAAsBA,IAAI,CAACS,OAA3B;AACA,mBAAKmC,UAAL,CAAgB5C,IAAhB,EAAsBA,IAAI,CAACS,OAAL,IAAgB,KAAK5D,SAA3C;AACH,aAPwC,CAQrC;;AACP;AACJ;;AAGSiF,QAAAA,SAAS,GAAQ;AACvB,eAAK,IAAI/B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7B,MAAL,CAAYI,MAAhC,EAAwCyB,CAAC,EAAzC,EAA6C;AACzC,iBAAK7B,MAAL,CAAY6B,CAAZ,EAAerC,MAAf,GAAwB,KAAxB;AACH;AACJ;;AAGSiF,QAAAA,UAAU,CAAC3C,IAAD,EAAOJ,KAAP,EAAmB;AACnC,cAAIiD,IAAI,GAAG,IAAX;;AACA,cAAI,KAAKC,mBAAT,EAA8B;AAC1BD,YAAAA,IAAI,GAAG7C,IAAI,CAAC3C,YAAL,CAAkB,KAAKyF,mBAAvB,CAAP;;AACA,gBAAID,IAAI,IAAIA,IAAI,CAACF,UAAjB,EAA6B;AACzBE,cAAAA,IAAI,CAACF,UAAL,CAAgB,KAAKpE,MAAL,CAAYqB,KAAZ,CAAhB,EAAoCA,KAApC;AACH;AACJ;AACJ;AAGD;AACJ;AACA;;;AACcmD,QAAAA,OAAO,CAACnD,KAAD,EAAW;AACxB,cAAII,IAAI,GAAG,IAAX;;AACA,cAAI,KAAK9B,MAAT,EAAiB;AACb,iBAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7B,MAAL,CAAYI,MAAhC,EAAwCyB,CAAC,EAAzC,EAA6C;AACzC,kBAAI,KAAK7B,MAAL,CAAY6B,CAAZ,EAAeU,OAAf,IAA0Bb,KAA9B,EAAqC;AACjCI,gBAAAA,IAAI,GAAG,KAAK9B,MAAL,CAAY6B,CAAZ,CAAP;AACA;AACH;AACJ;AACJ;;AACD,iBAAOC,IAAP;AACH;AAGD;AACJ;AACA;;;AACc4C,QAAAA,UAAU,CAAC5C,IAAD,EAAOgD,UAAP,EAAwB;AACxC,cAAIH,IAAI,GAAG,IAAX;;AACA,cAAI,KAAKC,mBAAT,EAA8B;AAC1BD,YAAAA,IAAI,GAAG7C,IAAI,CAAC3C,YAAL,CAAkB,KAAKyF,mBAAvB,CAAP;;AACA,gBAAID,IAAI,IAAIA,IAAI,CAACG,UAAjB,EAA6B;AACzBH,cAAAA,IAAI,CAACG,UAAL,CAAgBA,UAAhB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACcxB,QAAAA,UAAU,GAAO;AACvB,cAAIxB,IAAI,GAAG,IAAX;;AACA,cAAI,KAAK/B,SAAL,CAAegF,IAAf,KAAwB,CAA5B,EAA+B;AAAE;AAC7BjD,YAAAA,IAAI,GAAG,KAAK/B,SAAL,CAAeiF,GAAf,EAAP;AACH,WAFD,MAEO,IAAI,KAAK/F,UAAT,EAAqB;AAAE;AAC1B6C,YAAAA,IAAI,GAAG5D,WAAW,CAAC,KAAKe,UAAN,CAAlB;AAEH,WAHM,MAGA,IAAI,KAAKM,QAAT,EAAmB;AAAE;AACxBuC,YAAAA,IAAI,GAAG5D,WAAW,CAAC,KAAKqB,QAAN,CAAlB;AAEH;;AACDuC,UAAAA,IAAI,CAACzC,KAAL,GAAa,IAAIhB,IAAJ,CAAS,KAAKgB,KAAd,EAAqB,KAAKA,KAA1B,EAAiC,KAAKA,KAAtC,CAAb;AACAyC,UAAAA,IAAI,CAACmD,MAAL,GAAc,IAAd;AACAnD,UAAAA,IAAI,CAACyB,EAAL,CAAQzF,IAAI,CAACoF,SAAL,CAAeC,SAAvB,EAAkC,KAAKC,WAAvC,EAAoD,IAApD;AACA,iBAAOtB,IAAP;AACH;;AAESoD,QAAAA,QAAQ,CAACxD,KAAD,EAAa;AAC3B,cAAI,KAAK/C,SAAL,IAAkB+C,KAAtB,EAA6B;AACzB,gBAAI,KAAK/C,SAAL,IAAkB,CAAlB,IAAuB,KAAKA,SAAL,GAAiB,KAAK0B,MAAL,CAAYD,MAAxD,EAAgE;AAC5D,kBAAI+E,OAAO,GAAG,KAAKN,OAAL,CAAa,KAAKlG,SAAlB,CAAd;;AACA,kBAAIwG,OAAJ,EAAa;AACT,qBAAKT,UAAL,CAAgBS,OAAhB,EAAyB,KAAzB;AACH;AACJ;;AACD,gBAAIC,OAAO,GAAG,KAAKP,OAAL,CAAanD,KAAb,CAAd;;AACA,gBAAI0D,OAAJ,EAAa;AACT,mBAAKV,UAAL,CAAgBU,OAAhB,EAAyB,IAAzB;AACH;;AACD,iBAAKzG,SAAL,GAAiB+C,KAAjB;AACH;AACJ;AAED;AACJ;AACA;;;AACc0B,QAAAA,WAAW,CAACiC,KAAD,EAAa;AAC9B,cAAI3D,KAAK,GAAG2D,KAAK,CAACC,MAAN,CAAa/C,OAAzB;AACA,eAAK2C,QAAL,CAAcxD,KAAd;AACA,eAAK6D,eAAL,CAAqBC,OAArB,CAA6B,UAAUC,OAAV,EAAmB;AAC5CA,YAAAA,OAAO,CAACC,IAAR,CAAa,CAAC,KAAKrF,MAAL,CAAYqB,KAAZ,CAAD,EAAqBA,KAArB,EAA4B2D,KAAK,CAACC,MAAlC,CAAb;AACH,WAF4B,CAE3BK,IAF2B,CAEtB,IAFsB,CAA7B;AAGH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,OAAO,CAAC1G,IAAD,EAAO2G,YAAP,EAA8B;AACxC,eAAKxF,MAAL,GAAcnB,IAAd;;AACA,cAAI2G,YAAY,IAAI,IAAhB,IAAwBA,YAAY,IAAIC,SAAxC,IAAqD,CAACC,KAAK,CAACF,YAAD,CAA/D,EAA+E;AAC3E,iBAAKjH,UAAL,GAAkB,KAAKsC,UAAL,GAAkB2E,YAApC;AACH,WAFD,MAEO;AACH,iBAAKjH,UAAL,GAAkB,CAAlB;AACH,WANuC,CAOxC;;;AACA,eAAKqB,UAAL;AACH;;AAES+F,QAAAA,aAAa,CAACtE,KAAD,EAAa;AAChC,cAAI,KAAKrB,MAAL,IAAe,IAAf,IAAuB,KAAKL,MAAL,IAAe,IAAtC,IAA8C,KAAKD,SAAL,IAAkB,IAApE,EAA0E;AACtE;AACH;;AACD,cAAI,KAAKD,aAAT,EAAwB;AACpB,mBADoB,CACb;AACV;;AACD,cAAI4B,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAI,KAAKrB,MAAL,CAAYD,MAAtC,EAA8C;AAC1C,mBAD0C,CACnC;AACV;;AACD,cAAIK,SAAS,GAAG,CAAhB;;AACA,cAAI,KAAKhB,YAAT,EAAuB;AACnBgB,YAAAA,SAAS,GAAGI,IAAI,CAACwD,IAAL,CAAU3C,KAAK,GAAG,KAAKH,WAAvB,KAAuC,KAAKxC,UAAL,GAAkB,KAAKuC,WAA9D,CAAZ;AACH,WAFD,MAEO;AACHb,YAAAA,SAAS,GAAGI,IAAI,CAACwD,IAAL,CAAU3C,KAAK,GAAG,KAAKH,WAAvB,KAAuC,KAAKvC,WAAL,GAAmB,KAAKyC,QAA/D,CAAZ;AACH;;AACDhB,UAAAA,SAAS,GAAGI,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAASN,SAAT,EAAoB,KAAKO,UAAzB,CAAT,EAA+C,CAA/C,CAAZ;AACA,eAAKC,YAAL,CAAkBR,SAAlB;AACH;;AA5c4C,O;;;;;iBAGpB,I;;;;;;;iBAGJ,I;;;;;;;iBAIJ,I;;;;;;;iBAGa,E;;;;;;;iBAIN,K;;sFAGvBlC,Q;;;;;iBACa,C;;;;;;;iBAIa,K;;sFAG1BA,Q;;;;;iBACa,C;;mFAEbA,Q;;;;;iBACU,C;;0FAGVA,Q;;;;;iBACgB,G;;iFAEhBA,Q;;;;;iBACO,C;;;;;;;iBAG0B,E;;;;;;;iBAKb,K", "sourcesContent": ["import { _decorator, Component, ScrollView, Prefab, Node, NodePool, EventHandler, UITransform, instantiate, CCBoolean, CCString, Vec3 } from 'cc';\nconst {ccclass, property} = _decorator;\n\n@ccclass('ListLogic')\nexport default class ListLogic extends Component {\n\n    @property(ScrollView)\n    scrollView: ScrollView = null;\n\n    @property(Prefab)\n    itemPrefab: Prefab = null;\n\n\n    @property(Node)\n    itemNode: Node = null;\n\n    @property(CCString)\n    itemLogicScriptName:string  = \"\";\n\n\n    @property(CCBoolean)\n    isHorizontal:boolean  = false;\n\n\n    @property\n    columnCount = 1;\n\n\n    @property(CCBoolean)\n    autoColumnCount:boolean  = false;\n\n\n    @property\n    spaceColumn = 1;\n\n    @property\n    spaceRow = 1;\n\n\n    @property\n    updateInterval = 0.1;\n\n    @property\n    scale = 1;\n\n    @property([EventHandler])\n    itemClickEvents:EventHandler[]  = [];\n\n\n    \n    @property(CCBoolean)\n    isVirtual:boolean  = false;\n\n    private _curOffset:number = 0;\n    private _maxOffset:number = 0;\n    private _startIndex:number = 0;\n    private _itemCount:number = 0;\n    private _updateTimer:number = 0;\n    private _curIndex:number = 0;\n    private _newOffset:number = 0;\n    private _initContentPos:number = 0;\n    private _maxRowColSize:number = 0;\n    private _itemWidth:number = 0;\n    private _itemHeight:number = 0;\n    private _isUpdateList:boolean = false;\n    private _itemPool:NodePool = null;\n    private _items:any = [];\n    private _datas:any = null;\n\n    protected onLoad():void{\n        this._updateTimer = 0;//上次更新间隔时间\n        this._curIndex = -1;\n        this._newOffset = 0;\n        this._initContentPos = 0;\n        this._maxRowColSize = 0;//当前一行或者一列可以显示的最大宽度或者高度\n        this._itemWidth = this._itemHeight = 0;\n        if (this.itemPrefab) {\n            \n            this._itemWidth = this.itemPrefab.data.getComponent(UITransform).width * this.scale;//item宽度\n            this._itemHeight = this.itemPrefab.data.getComponent(UITransform).height * this.scale;//item高度\n        } else if (this.itemNode) {\n            this.itemNode.active = false;\n            this._itemWidth = this.itemNode.getComponent(UITransform).width * this.scale;//item宽度\n            this._itemHeight = this.itemNode.getComponent(UITransform).height * this.scale;//item高度\n        }\n\n        if (this.isHorizontal) {\n            this.scrollView.content.getComponent(UITransform).anchorX = 0;\n        } else {\n            this.scrollView.content.getComponent(UITransform).anchorY = 1;\n        }\n\n        this._isUpdateList = false;//是否正在更新列表\n        this._itemPool = new NodePool();//item缓存对象池\n        this._items = [];//item列表\n        this.updateList();\n    }\n\n\n\n\n\n    protected onDestroy():void {\n        this._itemPool.clear();\n        this._items.length = 0;\n        this._datas = null;\n    }\n\n\n    protected update (dt):void {\n        this._updateTimer += dt;\n        if (this._updateTimer < this.updateInterval) {\n            return;//更新间隔太短\n        }\n        this._updateTimer = 0;\n        // if (this.isVirtual == false) {\n        //     return;//非虚拟列表 不需要刷新位置和数据\n        // }\n        if (this._isUpdateList) {\n            return;//正在重新构建列表的时候 是不刷新的\n        }\n        let curOffset = 0;\n       \n        if (this.isHorizontal) {\n            curOffset = this._initContentPos - this.scrollView.content.position.x;\n        } else {\n            curOffset = this.scrollView.content.position.y - this._initContentPos;\n        }\n        curOffset = Math.max(Math.min(curOffset, this._maxOffset), 0);\n        this.setCurOffset(curOffset);\n       \n    }\n\n\n\n\n    protected setCurOffset(curOffset):void {\n    \n        if (this._datas == null || this._datas.length == 0) {\n            return;//没有数据不执行刷新\n        }\n        if (this._items == null || this._items.length == 0) {\n            return;//没有显示对象也不执行刷新\n        }\n        if (this._curOffset != curOffset) {\n            // console.log(\"setCurOffset\", this._curOffset, curOffset);\n            this._curOffset = curOffset;\n            if (this.isVirtual) {\n                if (this.isHorizontal) {\n                    var startIndex = Math.floor(this._curOffset / (this._itemWidth + this.spaceColumn)) * this.columnCount;\n                    this.setStartIndex(startIndex);\n                } else {\n                    var startIndex = Math.floor(this._curOffset / (this._itemHeight + this.spaceRow)) * this.columnCount;\n                    this.setStartIndex(startIndex);\n                }\n            } else {\n                this.setStartIndex(0);//非虚拟列表startIndex不变\n            }\n            //console.log(\"updatelist11 y\", this.scrollView.content.y);\n        }\n    }\n\n    \n\n\n    protected setStartIndex(index) {\n        if (this._startIndex != index && this._items.length > 0) {\n            //console.log(\"setStartIndex\", this._startIndex, index);\n            this._startIndex = index;\n            let suit = this.scrollView.content.getComponent(UITransform);\n            for (var i = 0; i < this._items.length; i++) {\n                var item:Node = this._items[i];\n                var index1 = this._startIndex + i;\n                let iuit = item.getComponent(UITransform);\n                let pos = item.position.clone();\n\n                if (this.isHorizontal) {\n                    let _row = i % this.columnCount;\n                    let _toY = _row * (this._itemHeight + this.spaceRow) + iuit.anchorY * this._itemHeight - suit.height * suit.anchorY;\n                    pos.y = -_toY - (suit.height - this._maxRowColSize) / 2;\n                    pos.x = Math.floor(index1 / this.columnCount) * (this._itemWidth + this.spaceColumn) + this.spaceColumn + (1 - iuit.anchorX) * this._itemWidth;\n                } else {\n                    let _col = i % this.columnCount;\n                    let _toX = _col * (this._itemWidth + this.spaceColumn) + iuit.anchorX * this._itemWidth - suit.width * suit.anchorX;\n                    pos.x = _toX + (suit.width - this._maxRowColSize) / 2;\n                    pos.y = -Math.floor(index1 / this.columnCount) * (this._itemHeight + this.spaceRow) - this.spaceRow - (1 - iuit.anchorY) * this._itemHeight;\n                }\n                item.itemIdx = index1;\n                item.setPosition(pos);\n                //console.log(\"update item position x: \" + item.x + \", y: \" + item.y);\n            }\n\n            this.updateItems();\n        }\n    }\n\n\n    \n        /**设置item实例数量*/\n    protected  updateItemCount(count):boolean {\n        if (this._itemCount != count) {\n            this._itemCount = count;\n            //清空列表\n            var children = this.scrollView.content.children.slice();\n            this.scrollView.content.removeAllChildren();\n            for (var i = 0; i < children.length; i++) {\n                let item = children[i];\n                if (item.isValid) {\n                    item.off(Node.EventType.TOUCH_END, this.onItemClick, this);\n                    this._itemPool.put(item);//加入对象池\n                }\n            }\n            this._items.length = 0;\n            for (var i = 0; i < this._itemCount; i++) {\n                let item = this.createItem();\n                item.active = false;\n                item.itemIdx = i;//在item上纪录当前下标\n                item.on(Node.EventType.TOUCH_END, this.onItemClick, this);\n                this.scrollView.content.addChild(item);\n                this._items.push(item);\n            }\n            return true;\n        }\n        return false;\n    }\n\n\n\n\n        /**\n     * 更新列表\n     */\n    protected updateList():void {\n        if (this._datas == null || this._items == null || this._itemPool == null) {\n            return;\n        }\n        //计算布局\n        if (this._itemWidth <= 0 || this._itemHeight <= 0) {\n            console.log(\"the list item has no width or height\");\n            return;\n        }\n        if (this._datas.length <= 0) {\n            this._curOffset = this._startIndex = -1;//重置纪录\n            this.hideItems();\n            return;\n        }\n        this._isUpdateList = true;\n        this.scrollView.stopAutoScroll();//更新时 停止滚动\n        var rowCount = 1;\n        var showCount = 1;\n        var dataLen = this._datas.length;\n        let uit = this.scrollView.content.parent.getComponent(UITransform);\n        let cuit = this.scrollView.content.getComponent(UITransform);\n        if (this.isHorizontal) {\n            if (this.autoColumnCount) {\n                //自动排列\n                this.columnCount = Math.floor(uit.height / this._itemHeight);\n            }\n            if (this.columnCount < 1) {\n                this.columnCount = 1;\n            }\n            this._maxRowColSize = this.columnCount * (this._itemHeight + this.spaceRow) - this.spaceRow;\n            rowCount = Math.ceil(uit.width / (this._itemWidth + this.spaceColumn)) + 1;\n            if (this.isVirtual) {\n                showCount = rowCount * this.columnCount;\n            } else {\n                showCount = dataLen;\n            }\n            cuit.width = Math.ceil(dataLen / this.columnCount) * (this._itemWidth + this.spaceColumn);\n            this._maxOffset = this.scrollView.getMaxScrollOffset().x;\n            this._initContentPos = uit.width * (0 - uit.anchorX);\n        } else {\n            if (this.autoColumnCount) {\n                //自动排列\n                this.columnCount = Math.floor(uit.width / this._itemWidth);\n            }\n            if (this.columnCount < 1) {\n                this.columnCount = 1;\n            }\n            this._maxRowColSize = this.columnCount * (this._itemWidth + this.spaceColumn) - this.spaceColumn;\n            rowCount = Math.ceil(uit.height / (this._itemHeight + this.spaceRow)) + 1;\n            if (this.isVirtual) {\n                showCount = rowCount * this.columnCount;\n            } else {\n                showCount = dataLen;\n            }\n            cuit.height = Math.ceil(dataLen / this.columnCount) * (this._itemHeight + this.spaceRow);\n            this._maxOffset = this.scrollView.getMaxScrollOffset().y;\n            this._initContentPos = uit.height * (1 - uit.anchorY);\n        }\n\n        var isItemChange = this.updateItemCount(showCount);\n        this._newOffset = Math.max(Math.min(this._newOffset, this._maxOffset), 0);\n       \n\n        if ((isItemChange || this._newOffset != this._curOffset)) {\n            let pos = this.scrollView.content.position.clone();\n            this._curOffset = this._newOffset;\n            if (this.isHorizontal) {\n                pos.x = -Math.abs(this._initContentPos - this._newOffset);\n            } else {\n                pos.y = Math.abs(this._initContentPos + this._newOffset);\n            }\n            this._curOffset = -1;//重置纪录\n            this._startIndex = -1;//重置纪录\n            this.setCurOffset(this._newOffset);\n            this.scrollView.content.setPosition(pos);\n        } else {\n            this.updateItems();\n        }\n        this._isUpdateList = false;\n        //console.log(\"updatelist y\", this.scrollView.content.y);\n\n        // console.log(\"this.scrollView:\", this.scrollView);\n    }\n\n\n    \n\n\n        //刷新所有item数据\n    protected updateItems():void {\n        \n        for (var i = 0; i < this._items.length; i++) {\n            var item = this._items[i];\n            // console.log(\"updateItems:\", item, item.itemIdx, this._datas.length, item.itemIdx < this._datas.length)\n            item.active = item.itemIdx < this._datas.length;\n            if (item.active) {\n                this.updateItem(item, item.itemIdx);\n                this.selectItem(item, item.itemIdx == this._curIndex);\n            }\n                //console.log(\"update item i: \" + item.itemIdx + \", active: \" + item.active);\n        }\n    }\n\n\n    protected hideItems():void {\n        for (var i = 0; i < this._items.length; i++) {\n            this._items[i].active = false;\n        }\n    }\n\n\n    protected updateItem(item, index):void {\n        var comp = null;\n        if (this.itemLogicScriptName) {\n            comp = item.getComponent(this.itemLogicScriptName);\n            if (comp && comp.updateItem) {\n                comp.updateItem(this._datas[index], index);\n            }\n        }\n    }\n\n\n    /**\n     * 根据下标获取item对象\n     */\n    protected getItem(index):any{\n        var item = null;\n        if (this._items) {\n            for (var i = 0; i < this._items.length; i++) {\n                if (this._items[i].itemIdx == index) {\n                    item = this._items[i];\n                    break;\n                }\n            }\n        }\n        return item;\n    }\n\n\n    /**\n     * 选中item\n     */\n    protected selectItem(item, isSelected):void {\n        var comp = null;\n        if (this.itemLogicScriptName) {\n            comp = item.getComponent(this.itemLogicScriptName);\n            if (comp && comp.isSelected) {\n                comp.isSelected(isSelected);\n            }\n        }\n    }\n\n    /**\n     * 创建item\n     */\n    protected createItem():any {\n        var item = null;\n        if (this._itemPool.size() > 0) { // 通过 size 接口判断对象池中是否有空闲的对象\n            item = this._itemPool.get();\n        } else if (this.itemPrefab) { // 如果没有空闲对象，也就是对象池中备用对象不够时，我们就用 instantiate 重新创建\n            item = instantiate(this.itemPrefab);\n            \n        } else if (this.itemNode) { // 如果没有空闲对象，也就是对象池中备用对象不够时，我们就用 instantiate 重新创建\n            item = instantiate(this.itemNode);\n            \n        }\n        item.scale = new Vec3(this.scale, this.scale, this.scale);\n        item.acitve = true;\n        item.on(Node.EventType.TOUCH_END, this.onItemClick, this);\n        return item;\n    }\n\n    protected setIndex(index):void {\n        if (this._curIndex != index) {\n            if (this._curIndex >= 0 && this._curIndex < this._datas.length) {\n                var oldItem = this.getItem(this._curIndex);\n                if (oldItem) {\n                    this.selectItem(oldItem, false);\n                }\n            }\n            var newItem = this.getItem(index);\n            if (newItem) {\n                this.selectItem(newItem, true);\n            }\n            this._curIndex = index;\n        }\n    }\n\n    /**\n     * item点击回调\n     */\n    protected onItemClick(event):void {\n        var index = event.target.itemIdx;\n        this.setIndex(index);\n        this.itemClickEvents.forEach(function (handler) {\n            handler.emit([this._datas[index], index, event.target]);\n        }.bind(this));\n    }\n\n    /**\n     * 设置列表数据\n     * scrollOffset 没有传值代表刷新到初始位置 其他整数代表刷新到当前位置的相对偏移量\n     */\n    public setData(data, scrollOffset?:any):void{\n        this._datas = data;\n        if (scrollOffset != null && scrollOffset != undefined && !isNaN(scrollOffset)) {\n            this._newOffset = this._curOffset + scrollOffset;\n        } else {\n            this._newOffset = 0;\n        }\n        // console.log(\"list logiv setData\", data, scrollOffset, this._newOffset);\n        this.updateList();\n    }\n\n    protected scrollToIndex(index):void {\n        if (this._datas == null || this._items == null || this._itemPool == null) {\n            return;\n        }\n        if (this._isUpdateList) {\n            return;//正在重新构建列表的时候 是不刷新的\n        }\n        if (index < 0 || index >= this._datas.length) {\n            return;//数据不合法\n        }\n        var curOffset = 0;\n        if (this.isHorizontal) {\n            curOffset = Math.ceil(index / this.columnCount) * (this._itemWidth + this.spaceColumn);\n        } else {\n            curOffset = Math.ceil(index / this.columnCount) * (this._itemHeight + this.spaceRow);\n        }\n        curOffset = Math.max(Math.min(curOffset, this._maxOffset), 0);\n        this.setCurOffset(curOffset);\n    }\n}\n"]}