{"cce:/internal/x/cc": {"mTimestamp": 5263.704700000002, "chunkId": "quick-pack:/cce/internal/x/cc.js", "imports": [{"value": "cce:/internal/x/cc-fu/base", "rewritten": "__unresolved_0", "loc": {"start": {"line": 1, "column": 14}, "end": {"line": 1, "column": 42}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 14}, "end": {"line": 2, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/gfx-webgl2", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 14}, "end": {"line": 3, "column": 48}}}, {"value": "cce:/internal/x/cc-fu/2d", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 14}, "end": {"line": 4, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/ui", "rewritten": "__unresolved_4", "loc": {"start": {"line": 5, "column": 14}, "end": {"line": 5, "column": 40}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-box2d", "rewritten": "__unresolved_5", "loc": {"start": {"line": 6, "column": 14}, "end": {"line": 6, "column": 54}}}, {"value": "cce:/internal/x/cc-fu/physics-2d-framework", "rewritten": "__unresolved_6", "loc": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 58}}}, {"value": "cce:/internal/x/cc-fu/intersection-2d", "rewritten": "__unresolved_7", "loc": {"start": {"line": 8, "column": 14}, "end": {"line": 8, "column": 53}}}, {"value": "cce:/internal/x/cc-fu/profiler", "rewritten": "__unresolved_8", "loc": {"start": {"line": 9, "column": 14}, "end": {"line": 9, "column": 46}}}, {"value": "cce:/internal/x/cc-fu/particle-2d", "rewritten": "__unresolved_9", "loc": {"start": {"line": 10, "column": 14}, "end": {"line": 10, "column": 49}}}, {"value": "cce:/internal/x/cc-fu/audio", "rewritten": "__unresolved_10", "loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/video", "rewritten": "__unresolved_11", "loc": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/webview", "rewritten": "__unresolved_12", "loc": {"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 45}}}, {"value": "cce:/internal/x/cc-fu/tween", "rewritten": "__unresolved_13", "loc": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/tiled-map", "rewritten": "__unresolved_14", "loc": {"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 47}}}, {"value": "cce:/internal/x/cc-fu/spine", "rewritten": "__unresolved_15", "loc": {"start": {"line": 16, "column": 14}, "end": {"line": 16, "column": 43}}}, {"value": "cce:/internal/x/cc-fu/dragon-bones", "rewritten": "__unresolved_16", "loc": {"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 50}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"specifierOrURL": "cce:/internal/x/cc-fu/base", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/gfx-webgl2", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/2d", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/ui", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-box2d", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/physics-2d-framework", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/intersection-2d", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/profiler", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/particle-2d", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/audio", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/video", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/webview", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/tween", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/tiled-map", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/spine", "isExternal": true}, {"specifierOrURL": "cce:/internal/x/cc-fu/dragon-bones", "isExternal": true}]}, "cce:/internal/x/prerequisite-imports": {"mTimestamp": 15465.366699999984, "chunkId": "quick-pack:/cce/internal/x/prerequisite-imports.js", "imports": [{"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts", "rewritten": "__unresolved_0", "loc": {"start": {"line": 4, "column": 7}, "end": {"line": 4, "column": 66}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 7}, "end": {"line": 5, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 7}, "end": {"line": 6, "column": 80}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 7}, "end": {"line": 7, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 7}, "end": {"line": 8, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 7}, "end": {"line": 9, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts", "rewritten": "__unresolved_7", "loc": {"start": {"line": 11, "column": 7}, "end": {"line": 11, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts", "rewritten": "__unresolved_8", "loc": {"start": {"line": 12, "column": 7}, "end": {"line": 12, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts", "rewritten": "__unresolved_9", "loc": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts", "rewritten": "__unresolved_10", "loc": {"start": {"line": 14, "column": 7}, "end": {"line": 14, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts", "rewritten": "__unresolved_11", "loc": {"start": {"line": 15, "column": 7}, "end": {"line": 15, "column": 77}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts", "rewritten": "__unresolved_12", "loc": {"start": {"line": 16, "column": 7}, "end": {"line": 16, "column": 74}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts", "rewritten": "__unresolved_13", "loc": {"start": {"line": 17, "column": 7}, "end": {"line": 17, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts", "rewritten": "__unresolved_14", "loc": {"start": {"line": 18, "column": 7}, "end": {"line": 18, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts", "rewritten": "__unresolved_15", "loc": {"start": {"line": 19, "column": 7}, "end": {"line": 19, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts", "rewritten": "__unresolved_16", "loc": {"start": {"line": 20, "column": 7}, "end": {"line": 20, "column": 80}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts", "rewritten": "__unresolved_17", "loc": {"start": {"line": 21, "column": 7}, "end": {"line": 21, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts", "rewritten": "__unresolved_18", "loc": {"start": {"line": 22, "column": 7}, "end": {"line": 22, "column": 80}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts", "rewritten": "__unresolved_19", "loc": {"start": {"line": 23, "column": 7}, "end": {"line": 23, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts", "rewritten": "__unresolved_20", "loc": {"start": {"line": 24, "column": 7}, "end": {"line": 24, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts", "rewritten": "__unresolved_21", "loc": {"start": {"line": 25, "column": 7}, "end": {"line": 25, "column": 84}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts", "rewritten": "__unresolved_22", "loc": {"start": {"line": 26, "column": 7}, "end": {"line": 26, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts", "rewritten": "__unresolved_23", "loc": {"start": {"line": 27, "column": 7}, "end": {"line": 27, "column": 75}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts", "rewritten": "__unresolved_24", "loc": {"start": {"line": 28, "column": 7}, "end": {"line": 28, "column": 74}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts", "rewritten": "__unresolved_25", "loc": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": 80}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts", "rewritten": "__unresolved_26", "loc": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 77}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts", "rewritten": "__unresolved_27", "loc": {"start": {"line": 31, "column": 7}, "end": {"line": 31, "column": 77}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts", "rewritten": "__unresolved_28", "loc": {"start": {"line": 32, "column": 7}, "end": {"line": 32, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts", "rewritten": "__unresolved_29", "loc": {"start": {"line": 33, "column": 7}, "end": {"line": 33, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts", "rewritten": "__unresolved_30", "loc": {"start": {"line": 34, "column": 7}, "end": {"line": 34, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts", "rewritten": "__unresolved_31", "loc": {"start": {"line": 35, "column": 7}, "end": {"line": 35, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts", "rewritten": "__unresolved_32", "loc": {"start": {"line": 36, "column": 7}, "end": {"line": 36, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts", "rewritten": "__unresolved_33", "loc": {"start": {"line": 37, "column": 7}, "end": {"line": 37, "column": 88}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts", "rewritten": "__unresolved_34", "loc": {"start": {"line": 38, "column": 7}, "end": {"line": 38, "column": 80}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts", "rewritten": "__unresolved_35", "loc": {"start": {"line": 39, "column": 7}, "end": {"line": 39, "column": 89}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts", "rewritten": "__unresolved_36", "loc": {"start": {"line": 40, "column": 7}, "end": {"line": 40, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts", "rewritten": "__unresolved_37", "loc": {"start": {"line": 41, "column": 7}, "end": {"line": 41, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts", "rewritten": "__unresolved_38", "loc": {"start": {"line": 42, "column": 7}, "end": {"line": 42, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts", "rewritten": "__unresolved_39", "loc": {"start": {"line": 43, "column": 7}, "end": {"line": 43, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts", "rewritten": "__unresolved_40", "loc": {"start": {"line": 44, "column": 7}, "end": {"line": 44, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts", "rewritten": "__unresolved_41", "loc": {"start": {"line": 45, "column": 7}, "end": {"line": 45, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts", "rewritten": "__unresolved_42", "loc": {"start": {"line": 46, "column": 7}, "end": {"line": 46, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts", "rewritten": "__unresolved_43", "loc": {"start": {"line": 47, "column": 7}, "end": {"line": 47, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts", "rewritten": "__unresolved_44", "loc": {"start": {"line": 48, "column": 7}, "end": {"line": 48, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts", "rewritten": "__unresolved_45", "loc": {"start": {"line": 49, "column": 7}, "end": {"line": 49, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts", "rewritten": "__unresolved_46", "loc": {"start": {"line": 50, "column": 7}, "end": {"line": 50, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts", "rewritten": "__unresolved_47", "loc": {"start": {"line": 51, "column": 7}, "end": {"line": 51, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts", "rewritten": "__unresolved_48", "loc": {"start": {"line": 52, "column": 7}, "end": {"line": 52, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts", "rewritten": "__unresolved_49", "loc": {"start": {"line": 53, "column": 7}, "end": {"line": 53, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts", "rewritten": "__unresolved_50", "loc": {"start": {"line": 54, "column": 7}, "end": {"line": 54, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts", "rewritten": "__unresolved_51", "loc": {"start": {"line": 55, "column": 7}, "end": {"line": 55, "column": 74}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts", "rewritten": "__unresolved_52", "loc": {"start": {"line": 56, "column": 7}, "end": {"line": 56, "column": 74}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts", "rewritten": "__unresolved_53", "loc": {"start": {"line": 57, "column": 7}, "end": {"line": 57, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts", "rewritten": "__unresolved_54", "loc": {"start": {"line": 58, "column": 7}, "end": {"line": 58, "column": 77}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts", "rewritten": "__unresolved_55", "loc": {"start": {"line": 59, "column": 7}, "end": {"line": 59, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts", "rewritten": "__unresolved_56", "loc": {"start": {"line": 60, "column": 7}, "end": {"line": 60, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts", "rewritten": "__unresolved_57", "loc": {"start": {"line": 61, "column": 7}, "end": {"line": 61, "column": 73}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts", "rewritten": "__unresolved_58", "loc": {"start": {"line": 62, "column": 7}, "end": {"line": 62, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts", "rewritten": "__unresolved_59", "loc": {"start": {"line": 63, "column": 7}, "end": {"line": 63, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts", "rewritten": "__unresolved_60", "loc": {"start": {"line": 64, "column": 7}, "end": {"line": 64, "column": 88}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts", "rewritten": "__unresolved_61", "loc": {"start": {"line": 65, "column": 7}, "end": {"line": 65, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts", "rewritten": "__unresolved_62", "loc": {"start": {"line": 66, "column": 7}, "end": {"line": 66, "column": 92}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts", "rewritten": "__unresolved_63", "loc": {"start": {"line": 67, "column": 7}, "end": {"line": 67, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts", "rewritten": "__unresolved_64", "loc": {"start": {"line": 68, "column": 7}, "end": {"line": 68, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts", "rewritten": "__unresolved_65", "loc": {"start": {"line": 69, "column": 7}, "end": {"line": 69, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts", "rewritten": "__unresolved_66", "loc": {"start": {"line": 70, "column": 7}, "end": {"line": 70, "column": 88}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts", "rewritten": "__unresolved_67", "loc": {"start": {"line": 71, "column": 7}, "end": {"line": 71, "column": 88}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts", "rewritten": "__unresolved_68", "loc": {"start": {"line": 72, "column": 7}, "end": {"line": 72, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts", "rewritten": "__unresolved_69", "loc": {"start": {"line": 73, "column": 7}, "end": {"line": 73, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts", "rewritten": "__unresolved_70", "loc": {"start": {"line": 74, "column": 7}, "end": {"line": 74, "column": 89}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts", "rewritten": "__unresolved_71", "loc": {"start": {"line": 75, "column": 7}, "end": {"line": 75, "column": 89}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts", "rewritten": "__unresolved_72", "loc": {"start": {"line": 76, "column": 7}, "end": {"line": 76, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts", "rewritten": "__unresolved_73", "loc": {"start": {"line": 77, "column": 7}, "end": {"line": 77, "column": 75}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts", "rewritten": "__unresolved_74", "loc": {"start": {"line": 78, "column": 7}, "end": {"line": 78, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts", "rewritten": "__unresolved_75", "loc": {"start": {"line": 79, "column": 7}, "end": {"line": 79, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts", "rewritten": "__unresolved_76", "loc": {"start": {"line": 80, "column": 7}, "end": {"line": 80, "column": 94}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts", "rewritten": "__unresolved_77", "loc": {"start": {"line": 81, "column": 7}, "end": {"line": 81, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts", "rewritten": "__unresolved_78", "loc": {"start": {"line": 82, "column": 7}, "end": {"line": 82, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts", "rewritten": "__unresolved_79", "loc": {"start": {"line": 83, "column": 7}, "end": {"line": 83, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts", "rewritten": "__unresolved_80", "loc": {"start": {"line": 84, "column": 7}, "end": {"line": 84, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts", "rewritten": "__unresolved_81", "loc": {"start": {"line": 85, "column": 7}, "end": {"line": 85, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts", "rewritten": "__unresolved_82", "loc": {"start": {"line": 86, "column": 7}, "end": {"line": 86, "column": 88}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts", "rewritten": "__unresolved_83", "loc": {"start": {"line": 87, "column": 7}, "end": {"line": 87, "column": 88}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts", "rewritten": "__unresolved_84", "loc": {"start": {"line": 88, "column": 7}, "end": {"line": 88, "column": 84}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts", "rewritten": "__unresolved_85", "loc": {"start": {"line": 89, "column": 7}, "end": {"line": 89, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts", "rewritten": "__unresolved_86", "loc": {"start": {"line": 90, "column": 7}, "end": {"line": 90, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts", "rewritten": "__unresolved_87", "loc": {"start": {"line": 91, "column": 7}, "end": {"line": 91, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts", "rewritten": "__unresolved_88", "loc": {"start": {"line": 92, "column": 7}, "end": {"line": 92, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts", "rewritten": "__unresolved_89", "loc": {"start": {"line": 93, "column": 7}, "end": {"line": 93, "column": 91}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts", "rewritten": "__unresolved_90", "loc": {"start": {"line": 94, "column": 7}, "end": {"line": 94, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts", "rewritten": "__unresolved_91", "loc": {"start": {"line": 95, "column": 7}, "end": {"line": 95, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts", "rewritten": "__unresolved_92", "loc": {"start": {"line": 96, "column": 7}, "end": {"line": 96, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts", "rewritten": "__unresolved_93", "loc": {"start": {"line": 97, "column": 7}, "end": {"line": 97, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts", "rewritten": "__unresolved_94", "loc": {"start": {"line": 98, "column": 7}, "end": {"line": 98, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts", "rewritten": "__unresolved_95", "loc": {"start": {"line": 99, "column": 7}, "end": {"line": 99, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts", "rewritten": "__unresolved_96", "loc": {"start": {"line": 100, "column": 7}, "end": {"line": 100, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts", "rewritten": "__unresolved_97", "loc": {"start": {"line": 101, "column": 7}, "end": {"line": 101, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts", "rewritten": "__unresolved_98", "loc": {"start": {"line": 102, "column": 7}, "end": {"line": 102, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts", "rewritten": "__unresolved_99", "loc": {"start": {"line": 103, "column": 7}, "end": {"line": 103, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts", "rewritten": "__unresolved_100", "loc": {"start": {"line": 104, "column": 7}, "end": {"line": 104, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts", "rewritten": "__unresolved_101", "loc": {"start": {"line": 105, "column": 7}, "end": {"line": 105, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts", "rewritten": "__unresolved_102", "loc": {"start": {"line": 106, "column": 7}, "end": {"line": 106, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts", "rewritten": "__unresolved_103", "loc": {"start": {"line": 107, "column": 7}, "end": {"line": 107, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts", "rewritten": "__unresolved_104", "loc": {"start": {"line": 108, "column": 7}, "end": {"line": 108, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts", "rewritten": "__unresolved_105", "loc": {"start": {"line": 109, "column": 7}, "end": {"line": 109, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts", "rewritten": "__unresolved_106", "loc": {"start": {"line": 110, "column": 7}, "end": {"line": 110, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts", "rewritten": "__unresolved_107", "loc": {"start": {"line": 111, "column": 7}, "end": {"line": 111, "column": 90}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts", "rewritten": "__unresolved_108", "loc": {"start": {"line": 112, "column": 7}, "end": {"line": 112, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts", "rewritten": "__unresolved_109", "loc": {"start": {"line": 113, "column": 7}, "end": {"line": 113, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts", "rewritten": "__unresolved_110", "loc": {"start": {"line": 114, "column": 7}, "end": {"line": 114, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts", "rewritten": "__unresolved_111", "loc": {"start": {"line": 115, "column": 7}, "end": {"line": 115, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts", "rewritten": "__unresolved_112", "loc": {"start": {"line": 116, "column": 7}, "end": {"line": 116, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts", "rewritten": "__unresolved_113", "loc": {"start": {"line": 117, "column": 7}, "end": {"line": 117, "column": 89}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts", "rewritten": "__unresolved_114", "loc": {"start": {"line": 118, "column": 7}, "end": {"line": 118, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts", "rewritten": "__unresolved_115", "loc": {"start": {"line": 119, "column": 7}, "end": {"line": 119, "column": 84}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts", "rewritten": "__unresolved_116", "loc": {"start": {"line": 120, "column": 7}, "end": {"line": 120, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts", "rewritten": "__unresolved_117", "loc": {"start": {"line": 121, "column": 7}, "end": {"line": 121, "column": 84}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts", "rewritten": "__unresolved_118", "loc": {"start": {"line": 122, "column": 7}, "end": {"line": 122, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts", "rewritten": "__unresolved_119", "loc": {"start": {"line": 123, "column": 7}, "end": {"line": 123, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts", "rewritten": "__unresolved_120", "loc": {"start": {"line": 124, "column": 7}, "end": {"line": 124, "column": 80}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts", "rewritten": "__unresolved_121", "loc": {"start": {"line": 125, "column": 7}, "end": {"line": 125, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts", "rewritten": "__unresolved_122", "loc": {"start": {"line": 126, "column": 7}, "end": {"line": 126, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts", "rewritten": "__unresolved_123", "loc": {"start": {"line": 127, "column": 7}, "end": {"line": 127, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts", "rewritten": "__unresolved_124", "loc": {"start": {"line": 128, "column": 7}, "end": {"line": 128, "column": 75}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts", "rewritten": "__unresolved_125", "loc": {"start": {"line": 129, "column": 7}, "end": {"line": 129, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts", "rewritten": "__unresolved_126", "loc": {"start": {"line": 130, "column": 7}, "end": {"line": 130, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts", "rewritten": "__unresolved_127", "loc": {"start": {"line": 131, "column": 7}, "end": {"line": 131, "column": 80}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts", "rewritten": "__unresolved_128", "loc": {"start": {"line": 132, "column": 7}, "end": {"line": 132, "column": 84}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts", "rewritten": "__unresolved_129", "loc": {"start": {"line": 133, "column": 7}, "end": {"line": 133, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts", "rewritten": "__unresolved_130", "loc": {"start": {"line": 134, "column": 7}, "end": {"line": 134, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts", "rewritten": "__unresolved_131", "loc": {"start": {"line": 135, "column": 7}, "end": {"line": 135, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts", "rewritten": "__unresolved_132", "loc": {"start": {"line": 136, "column": 7}, "end": {"line": 136, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts", "rewritten": "__unresolved_133", "loc": {"start": {"line": 137, "column": 7}, "end": {"line": 137, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts", "rewritten": "__unresolved_134", "loc": {"start": {"line": 138, "column": 7}, "end": {"line": 138, "column": 82}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts", "rewritten": "__unresolved_135", "loc": {"start": {"line": 139, "column": 7}, "end": {"line": 139, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts", "rewritten": "__unresolved_136", "loc": {"start": {"line": 140, "column": 7}, "end": {"line": 140, "column": 90}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts", "rewritten": "__unresolved_137", "loc": {"start": {"line": 141, "column": 7}, "end": {"line": 141, "column": 84}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts", "rewritten": "__unresolved_138", "loc": {"start": {"line": 142, "column": 7}, "end": {"line": 142, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts", "rewritten": "__unresolved_139", "loc": {"start": {"line": 143, "column": 7}, "end": {"line": 143, "column": 75}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts", "rewritten": "__unresolved_140", "loc": {"start": {"line": 144, "column": 7}, "end": {"line": 144, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts", "rewritten": "__unresolved_141", "loc": {"start": {"line": 145, "column": 7}, "end": {"line": 145, "column": 79}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts", "rewritten": "__unresolved_142", "loc": {"start": {"line": 146, "column": 7}, "end": {"line": 146, "column": 83}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts", "rewritten": "__unresolved_143", "loc": {"start": {"line": 147, "column": 7}, "end": {"line": 147, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts", "rewritten": "__unresolved_144", "loc": {"start": {"line": 148, "column": 7}, "end": {"line": 148, "column": 76}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts", "rewritten": "__unresolved_145", "loc": {"start": {"line": 149, "column": 7}, "end": {"line": 149, "column": 86}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts", "rewritten": "__unresolved_146", "loc": {"start": {"line": 150, "column": 7}, "end": {"line": 150, "column": 77}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts", "rewritten": "__unresolved_147", "loc": {"start": {"line": 151, "column": 7}, "end": {"line": 151, "column": 78}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts", "rewritten": "__unresolved_148", "loc": {"start": {"line": 152, "column": 7}, "end": {"line": 152, "column": 73}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts", "rewritten": "__unresolved_149", "loc": {"start": {"line": 153, "column": 7}, "end": {"line": 153, "column": 85}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts", "rewritten": "__unresolved_150", "loc": {"start": {"line": 154, "column": 7}, "end": {"line": 154, "column": 81}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts", "rewritten": "__unresolved_151", "loc": {"start": {"line": 155, "column": 7}, "end": {"line": 155, "column": 87}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts", "rewritten": "__unresolved_152", "loc": {"start": {"line": 156, "column": 7}, "end": {"line": 156, "column": 73}}}, {"value": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts", "rewritten": "__unresolved_153", "loc": {"start": {"line": 157, "column": 7}, "end": {"line": 157, "column": 73}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts"}]}, "cce:/internal/code-quality/cr.mjs": {"mTimestamp": 1754004257013, "chunkId": "quick-pack:/cce/internal/code-quality/cr.js", "imports": [], "modLoMetadata": {"type": "esm"}, "resolvedImports": []}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/core/coreEvent.js", "imports": [], "modLoMetadata": {"type": "esm"}, "resolvedImports": []}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/chat/ChatItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": 40}}}, {"value": "cc", "loc": {"start": {"line": 4, "column": 45}, "end": {"line": 4, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts": {"mTimestamp": 1753985829431.5972, "chunkId": "assets/scripts/Main.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 141}, "end": {"line": 1, "column": 145}}}, {"value": "./config/GameConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 48}}}, {"value": "./core/LoaderManager", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 54}, "end": {"line": 5, "column": 76}}}, {"value": "./general/ArmyCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 47}}}, {"value": "./general/GeneralCommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 53}}}, {"value": "./login/LoginCommand", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 47}}}, {"value": "./map/MapCommand", "rewritten": "__unresolved_6", "loc": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 41}}}, {"value": "./map/ui/MapUICommand", "rewritten": "__unresolved_7", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 48}}}, {"value": "./network/http/HttpManager", "rewritten": "__unresolved_8", "loc": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 56}}}, {"value": "./network/socket/NetInterface", "rewritten": "__unresolved_9", "loc": {"start": {"line": 12, "column": 25}, "end": {"line": 12, "column": 56}}}, {"value": "./network/socket/NetManager", "rewritten": "__unresolved_10", "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 56}}}, {"value": "./network/socket/NetNode", "rewritten": "__unresolved_11", "loc": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 54}}}, {"value": "./skill/SkillCommand", "rewritten": "__unresolved_12", "loc": {"start": {"line": 15, "column": 25}, "end": {"line": 15, "column": 47}}}, {"value": "./utils/Toast", "rewritten": "__unresolved_13", "loc": {"start": {"line": 16, "column": 18}, "end": {"line": 16, "column": 33}}}, {"value": "./utils/Tools", "rewritten": "__unresolved_14", "loc": {"start": {"line": 17, "column": 22}, "end": {"line": 17, "column": 37}}}, {"value": "./utils/EventMgr", "rewritten": "__unresolved_15", "loc": {"start": {"line": 18, "column": 25}, "end": {"line": 18, "column": 43}}}, {"value": "./common/AudioManager", "rewritten": "__unresolved_16", "loc": {"start": {"line": 19, "column": 29}, "end": {"line": 19, "column": 52}}}, {"value": "./common/LogicEvent", "rewritten": "__unresolved_17", "loc": {"start": {"line": 20, "column": 27}, "end": {"line": 20, "column": 48}}}, {"value": "./utils/FixedScreenAdapter", "rewritten": "__unresolved_18", "loc": {"start": {"line": 21, "column": 35}, "end": {"line": 21, "column": 63}}}, {"value": "./utils/ClickTestHelper", "rewritten": "__unresolved_19", "loc": {"start": {"line": 22, "column": 32}, "end": {"line": 22, "column": 57}}}, {"value": "./test/SimpleLoginTest", "rewritten": "__unresolved_20", "loc": {"start": {"line": 23, "column": 32}, "end": {"line": 23, "column": 56}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/chat/ChatCommand.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../network/socket/NetManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 57}}}, {"value": "../config/ServerConfig", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 53}}}, {"value": "./ChatProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 35}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/chat/ChatLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 3, "column": 59}, "end": {"line": 3, "column": 63}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 42}}}, {"value": "./ChatCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 39}}}, {"value": "../utils/ListLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 42}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 44}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 12, "column": 29}, "end": {"line": 12, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/common/AudioManager.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 85}, "end": {"line": 1, "column": 89}}}, {"value": "../utils/LocalCache", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 48}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/chat/ChatProxy.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/cloud/CloudAni.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 76}, "end": {"line": 2, "column": 80}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/common/BgLogic.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 4, "column": 85}, "end": {"line": 4, "column": 89}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/common/LoadingLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 51}, "end": {"line": 1, "column": 55}}}, {"value": "../core/coreEvent", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 26}, "end": {"line": 2, "column": 45}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 44}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/common/DialogOut.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 51}, "end": {"line": 2, "column": 55}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/common/PanelOut.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 62}, "end": {"line": 2, "column": 66}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts": {"mTimestamp": 1753985337372.9514, "chunkId": "assets/scripts/common/LogicEvent.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/config/Basci.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts": {"mTimestamp": 1753946751586.3, "chunkId": "assets/scripts/config/GameConfig.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/config/skill/Skill.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/core/CoreEvent.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/config/ServerConfig.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/config/HttpConfig.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts": {"mTimestamp": 1753985276032.9175, "chunkId": "assets/scripts/general/ArmyCommand.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../config/ServerConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}, {"value": "../network/socket/NetManager", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 57}}}, {"value": "./ArmyProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 45}, "end": {"line": 8, "column": 58}}}, {"value": "./GeneralCommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 45}}}, {"value": "./GeneralProxy", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 63}, "end": {"line": 10, "column": 79}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_7", "loc": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/core/LoaderManager.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 44}}}, {"value": "./coreEvent", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 26}, "end": {"line": 3, "column": 39}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/general/GeneralCommand.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../config/ServerConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 1, "column": 29}, "end": {"line": 1, "column": 53}}}, {"value": "../network/socket/NetManager", "rewritten": "__unresolved_2", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 57}}}, {"value": "./GeneralProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 41}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/NameDict.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/convert.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/general/ArmyProxy.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 1, "column": 21}, "end": {"line": 1, "column": 40}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/general/GeneralProxy.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/gzip/crc32.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/crypto/md5.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/gzip/deflate-js.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "./rawinflate", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": 36}}}, {"value": "./rawdeflate", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 36}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/login/CreateLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 55}, "end": {"line": 1, "column": 59}}}, {"value": "../config/ServerConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 53}}}, {"value": "./LoginCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 41}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 44}}}, {"value": "../libs/NameDict", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 45}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 53}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/gzip/gzip.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "./crc32", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 31}}}, {"value": "./deflate-js", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 39}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts": {"mTimestamp": 1753978853393.9707, "chunkId": "assets/scripts/login/ExitDialogController.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 65}, "end": {"line": 1, "column": 69}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/crypto/crypto.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/gzip/rawinflate.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts": {"mTimestamp": 1753951408807.1829, "chunkId": "assets/scripts/login/LoginFormController.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 75}, "end": {"line": 1, "column": 79}}}, {"value": "./LoginCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 41}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 49}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 53}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts": {"mTimestamp": 1753978790428.5842, "chunkId": "assets/scripts/login/LoginDialogController.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 83}, "end": {"line": 1, "column": 87}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/login/LoginLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 54}, "end": {"line": 1, "column": 58}}}, {"value": "../utils/LocalCache", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 48}}}, {"value": "./LoginCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 41}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 44}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/login/LoginCommand.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../config/HttpConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": 49}}}, {"value": "../config/ServerConfig", "rewritten": "__unresolved_2", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}, {"value": "../network/http/HttpManager", "rewritten": "__unresolved_3", "loc": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": 57}}}, {"value": "../network/socket/NetManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 57}}}, {"value": "../utils/Tools", "rewritten": "__unresolved_5", "loc": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 38}}}, {"value": "./LoginProxy", "rewritten": "__unresolved_6", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 37}}}, {"value": "../network/socket/NetInterface", "rewritten": "__unresolved_7", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 57}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_8", "loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 42}}}, {"value": "../utils/LocalCache", "rewritten": "__unresolved_9", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 48}}}, {"value": "../utils/DateUtil", "rewritten": "__unresolved_10", "loc": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 40}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_11", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 44}}}, {"value": "../libs/crypto/md5", "rewritten": "__unresolved_12", "loc": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 40}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_13", "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/libs/gzip/rawdeflate.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/login/LoginProxy.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapArmyLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 75}, "end": {"line": 1, "column": 79}}}, {"value": "../general/ArmyCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 48}}}, {"value": "../general/ArmyProxy", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 34}, "end": {"line": 5, "column": 56}}}, {"value": "./entries/ArmyLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 22}, "end": {"line": 6, "column": 43}}}, {"value": "./MapCommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 37}}}, {"value": "./MapUtil", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_6", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_7", "loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts": {"mTimestamp": 1753959540986.7239, "chunkId": "assets/scripts/login/MainMenuController.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 73}, "end": {"line": 1, "column": 77}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapBuildTagLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./entries/BuildTagLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": 51}}}, {"value": "./MapBaseLayerLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": 51}}}, {"value": "./MapUtil", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 31}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts": {"mTimestamp": 1753950257113.0906, "chunkId": "assets/scripts/login/SimpleLoginUI.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 112}, "end": {"line": 1, "column": 116}}}, {"value": "./LoginCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 41}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapBaseLayerLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 75}, "end": {"line": 1, "column": 79}}}, {"value": "./MapCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 37}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapBuildTipsLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./entries/BuildTipsLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 53}}}, {"value": "./MapBaseLayerLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": 51}}}, {"value": "./MapUtil", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapBuildProxy.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": 40}}}, {"value": "./MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 37}}}, {"value": "./MapProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": 38}}}, {"value": "./MapUtil", "rewritten": "__unresolved_4", "loc": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_5", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapCityLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./MapBaseLayerLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": 51}}}, {"value": "./entries/CityLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 43}}}, {"value": "./MapUtil", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 20}, "end": {"line": 6, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapCityProxy.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "./MapUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 20}, "end": {"line": 2, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapClickUILogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 130}, "end": {"line": 1, "column": 134}}}, {"value": "../general/ArmyProxy", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 46}}}, {"value": "../utils/DateUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 40}}}, {"value": "./MapBuildProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 46}}}, {"value": "./MapCityProxy", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 44}}}, {"value": "./MapCommand", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 37}}}, {"value": "./MapProxy", "rewritten": "__unresolved_6", "loc": {"start": {"line": 9, "column": 53}, "end": {"line": 9, "column": 65}}}, {"value": "./ui/MapUICommand", "rewritten": "__unresolved_7", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 44}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_8", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 44}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_9", "loc": {"start": {"line": 12, "column": 29}, "end": {"line": 12, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_10", "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts": {"mTimestamp": 1753985405044.4814, "chunkId": "assets/scripts/map/MapCommand.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../config/ServerConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}, {"value": "../general/GeneralCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 54}}}, {"value": "../network/socket/NetManager", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": 57}}}, {"value": "../utils/DateUtil", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 21}, "end": {"line": 7, "column": 40}}}, {"value": "./MapBuildProxy", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 8, "column": 61}}}, {"value": "./MapCityProxy", "rewritten": "__unresolved_6", "loc": {"start": {"line": 9, "column": 42}, "end": {"line": 9, "column": 58}}}, {"value": "./MapProxy", "rewritten": "__unresolved_7", "loc": {"start": {"line": 10, "column": 38}, "end": {"line": 10, "column": 50}}}, {"value": "./MapUtil", "rewritten": "__unresolved_8", "loc": {"start": {"line": 11, "column": 20}, "end": {"line": 11, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_9", "loc": {"start": {"line": 14, "column": 25}, "end": {"line": 14, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_10", "loc": {"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapFacilityBuildLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./entries/FacilityBuildLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 31}, "end": {"line": 4, "column": 61}}}, {"value": "./MapBaseLayerLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": 51}}}, {"value": "./MapUtil", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 130}, "end": {"line": 1, "column": 134}}}, {"value": "./MapCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 37}}}, {"value": "./MapUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapResBuildLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./entries/ResBuildLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": 51}}}, {"value": "./MapBaseLayerLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": 51}}}, {"value": "./MapUtil", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapProxy.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 60}, "end": {"line": 1, "column": 64}}}, {"value": "./MapUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 20}, "end": {"line": 2, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapResLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./entries/ResLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 41}}}, {"value": "./MapBaseLayerLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": 51}}}, {"value": "./MapUtil", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 31}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapSysCityLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "./MapBaseLayerLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": 51}}}, {"value": "./MapUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 31}}}, {"value": "./entries/SysCityLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 49}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/entries/ArmyLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": 43}}}, {"value": "../MapUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 20}, "end": {"line": 3, "column": 32}}}, {"value": "cc", "loc": {"start": {"line": 4, "column": 57}, "end": {"line": 4, "column": 61}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapUtil.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 84}, "end": {"line": 1, "column": 88}}}, {"value": "./MapCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 37}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/MapTouchLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 77}, "end": {"line": 1, "column": 81}}}, {"value": "./MapClickUILogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 6, "column": 28}, "end": {"line": 6, "column": 47}}}, {"value": "./MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 37}}}, {"value": "./MapUtil", "rewritten": "__unresolved_3", "loc": {"start": {"line": 9, "column": 20}, "end": {"line": 9, "column": 31}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/entries/BuildTipsLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 43}}}, {"value": "../MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 38}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 47}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/entries/BuildTagLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "../MapCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": 38}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 47}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_3", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/entries/CityLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 72}, "end": {"line": 1, "column": 76}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 43}}}, {"value": "../MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 38}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 47}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/entries/FacilityBuildLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 72}, "end": {"line": 1, "column": 76}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 43}}}, {"value": "../MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 38}}}, {"value": "../MapProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 66}, "end": {"line": 7, "column": 79}}}, {"value": "../MapUtil", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 32}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/entries/ResBuildLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "../MapCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 38}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 47}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/ArmySelectItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 72}, "end": {"line": 1, "column": 76}}}, {"value": "../../general/ArmyProxy", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 59}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 57}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 51}}}, {"value": "./GeneralHeadLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 49}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_7", "loc": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/entries/ResLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "../MapProxy", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 38}, "end": {"line": 4, "column": 51}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/entries/SysCityLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 71}, "end": {"line": 1, "column": 75}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 43}}}, {"value": "../MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 38}}}, {"value": "../MapProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": 40}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 47}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/ArmySelectNodeLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 65}, "end": {"line": 1, "column": 69}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 51}}}, {"value": "../MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 38}}}, {"value": "./ArmySelectItemLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 32}, "end": {"line": 8, "column": 55}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/CityAboutLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 65}, "end": {"line": 1, "column": 69}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 51}}}, {"value": "./CityArmyItemLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 51}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 11, "column": 29}, "end": {"line": 11, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/CityArmySettingLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 89}, "end": {"line": 1, "column": 93}}}, {"value": "../../general/ArmyProxy", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 59}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 57}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 51}}}, {"value": "../../general/GeneralProxy", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 60}, "end": {"line": 7, "column": 88}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 41}}}, {"value": "../MapCommand", "rewritten": "__unresolved_6", "loc": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 38}}}, {"value": "./CityGeneralItemLogic", "rewritten": "__unresolved_7", "loc": {"start": {"line": 12, "column": 33}, "end": {"line": 12, "column": 57}}}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_8", "loc": {"start": {"line": 13, "column": 25}, "end": {"line": 13, "column": 51}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_9", "loc": {"start": {"line": 15, "column": 25}, "end": {"line": 15, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_10", "loc": {"start": {"line": 16, "column": 29}, "end": {"line": 16, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_11", "loc": {"start": {"line": 17, "column": 27}, "end": {"line": 17, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/CityArmyItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "../../general/ArmyProxy", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 59}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 57}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 51}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 41}}}, {"value": "./GeneralHeadLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 49}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_7", "loc": {"start": {"line": 11, "column": 29}, "end": {"line": 11, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_8", "loc": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/CollectLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 8, "column": 53}, "end": {"line": 8, "column": 57}}}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 51}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 43}}}, {"value": "../../utils/Tools", "rewritten": "__unresolved_3", "loc": {"start": {"line": 13, "column": 22}, "end": {"line": 13, "column": 41}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 14, "column": 25}, "end": {"line": 14, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_5", "loc": {"start": {"line": 15, "column": 25}, "end": {"line": 15, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_6", "loc": {"start": {"line": 16, "column": 29}, "end": {"line": 16, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_7", "loc": {"start": {"line": 17, "column": 27}, "end": {"line": 17, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/Dialog.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 72}, "end": {"line": 2, "column": 76}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 56}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/CityGeneralItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 72}, "end": {"line": 1, "column": 76}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 51}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": 57}}}, {"value": "../../general/GeneralProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 60}, "end": {"line": 7, "column": 88}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 21}, "end": {"line": 8, "column": 43}}}, {"value": "./GeneralHeadLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 49}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_7", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_8", "loc": {"start": {"line": 12, "column": 29}, "end": {"line": 12, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_9", "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/DrawLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 45}, "end": {"line": 2, "column": 49}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 57}}}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 51}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/DrawRLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 73}, "end": {"line": 1, "column": 77}}}, {"value": "./GeneralItemLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 50}, "end": {"line": 4, "column": 70}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 56}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/FacilityAdditionItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 51}, "end": {"line": 1, "column": 55}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 41}}}, {"value": "./MapUIProxy", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 80}, "end": {"line": 5, "column": 94}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/FacilityDesLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 113}, "end": {"line": 1, "column": 117}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 56}}}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 51}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 43}}}, {"value": "./FacilityAdditionItemLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 38}, "end": {"line": 7, "column": 67}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 41}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/FacilityItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 51}, "end": {"line": 1, "column": 55}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 43}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/FacilityListLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 63}, "end": {"line": 1, "column": 67}}}, {"value": "./FacilityDesLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 49}}}, {"value": "./FacilityItemLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": 51}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/FortressAbout.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 80}, "end": {"line": 1, "column": 84}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 51}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 43}}}, {"value": "../MapCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 38}}}, {"value": "../MapProxy", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 40}}}, {"value": "./CityArmyItemLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 51}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_7", "loc": {"start": {"line": 12, "column": 29}, "end": {"line": 12, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_8", "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralAddPrLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 80}, "end": {"line": 2, "column": 84}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 56}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": 57}}}, {"value": "../../general/GeneralProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 43}, "end": {"line": 7, "column": 71}}}, {"value": "./GeneralItemLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 50}, "end": {"line": 8, "column": 70}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralComposeLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 84}, "end": {"line": 1, "column": 88}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 57}}}, {"value": "./GeneralItemLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 50}, "end": {"line": 6, "column": 70}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 52}}}, {"value": "../../utils/ListLogic", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 45}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralConvertLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 3, "column": 96}, "end": {"line": 3, "column": 100}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": 57}}}, {"value": "./GeneralItemLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 50}, "end": {"line": 7, "column": 70}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 52}}}, {"value": "../../utils/ListLogic", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 45}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralDesLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 92}, "end": {"line": 1, "column": 96}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 57}}}, {"value": "../../general/GeneralProxy", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 55}}}, {"value": "../../skill/SkillCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 51}}}, {"value": "./GeneralItemLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 50}, "end": {"line": 7, "column": 70}}}, {"value": "./SkillIconLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 45}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_6", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_7", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_8", "loc": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralHeadLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 70}, "end": {"line": 1, "column": 74}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 57}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralInfoLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 82}, "end": {"line": 2, "column": 86}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 52}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 47}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 74}, "end": {"line": 2, "column": 78}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 57}}}, {"value": "../../general/GeneralProxy", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 45}, "end": {"line": 6, "column": 73}}}, {"value": "./GeneralHeadLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 49}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralListLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 57}, "end": {"line": 2, "column": 61}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 57}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 47}}}, {"value": "../../utils/ListLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": 45}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralRosterListLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 50}, "end": {"line": 2, "column": 54}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 52}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 57}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 47}}}, {"value": "../../utils/ListLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 45}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/GeneralRosterLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 68}, "end": {"line": 1, "column": 72}}}, {"value": "../../general/GeneralProxy", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 47}, "end": {"line": 4, "column": 75}}}, {"value": "./GeneralHeadLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/MapUICommand.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../../config/ServerConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 56}}}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 51}}}, {"value": "../../network/socket/NetManager", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 60}}}, {"value": "../MapCommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 38}}}, {"value": "./MapUIProxy", "rewritten": "__unresolved_5", "loc": {"start": {"line": 7, "column": 51}, "end": {"line": 7, "column": 65}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_6", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 47}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_7", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/MapUILogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 99}, "end": {"line": 1, "column": 103}}}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 51}}}, {"value": "./ArmySelectNodeLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 32}, "end": {"line": 5, "column": 55}}}, {"value": "./CityArmySettingLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 33}, "end": {"line": 6, "column": 57}}}, {"value": "./FacilityListLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 30}, "end": {"line": 7, "column": 51}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 41}}}, {"value": "./Dialog", "rewritten": "__unresolved_6", "loc": {"start": {"line": 9, "column": 35}, "end": {"line": 9, "column": 45}}}, {"value": "../../union/UnionCommand", "rewritten": "__unresolved_7", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 51}}}, {"value": "../MapCommand", "rewritten": "__unresolved_8", "loc": {"start": {"line": 11, "column": 23}, "end": {"line": 11, "column": 38}}}, {"value": "./FortressAbout", "rewritten": "__unresolved_9", "loc": {"start": {"line": 12, "column": 26}, "end": {"line": 12, "column": 43}}}, {"value": "./CityAboutLogic", "rewritten": "__unresolved_10", "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 45}}}, {"value": "./GeneralListLogic", "rewritten": "__unresolved_11", "loc": {"start": {"line": 14, "column": 29}, "end": {"line": 14, "column": 49}}}, {"value": "./TransformLogic", "rewritten": "__unresolved_12", "loc": {"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 45}}}, {"value": "../../utils/Tools", "rewritten": "__unresolved_13", "loc": {"start": {"line": 16, "column": 22}, "end": {"line": 16, "column": 41}}}, {"value": "./GeneralInfoLogic", "rewritten": "__unresolved_14", "loc": {"start": {"line": 17, "column": 29}, "end": {"line": 17, "column": 49}}}, {"value": "./WarReportLogic", "rewritten": "__unresolved_15", "loc": {"start": {"line": 18, "column": 27}, "end": {"line": 18, "column": 45}}}, {"value": "./DrawRLogic", "rewritten": "__unresolved_16", "loc": {"start": {"line": 19, "column": 23}, "end": {"line": 19, "column": 37}}}, {"value": "./<PERSON>llLogic", "rewritten": "__unresolved_17", "loc": {"start": {"line": 21, "column": 23}, "end": {"line": 21, "column": 37}}}, {"value": "./SkillInfoLogic", "rewritten": "__unresolved_18", "loc": {"start": {"line": 23, "column": 27}, "end": {"line": 23, "column": 45}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_19", "loc": {"start": {"line": 24, "column": 25}, "end": {"line": 24, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_20", "loc": {"start": {"line": 25, "column": 29}, "end": {"line": 25, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_21", "loc": {"start": {"line": 27, "column": 27}, "end": {"line": 27, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/RightCityItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/RightArmyItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 72}, "end": {"line": 1, "column": 76}}}, {"value": "../../general/ArmyProxy", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 59}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 57}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 24}, "end": {"line": 6, "column": 51}}}, {"value": "../MapCommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 38}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 43}}}, {"value": "./GeneralHeadLogic", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 29}, "end": {"line": 11, "column": 49}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_7", "loc": {"start": {"line": 12, "column": 25}, "end": {"line": 12, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_8", "loc": {"start": {"line": 13, "column": 29}, "end": {"line": 13, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_9", "loc": {"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/MapUIProxy.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 51}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 43}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 41}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/RightInfoNodeLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 85}, "end": {"line": 1, "column": 89}}}, {"value": "../../general/ArmyCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 51}}}, {"value": "../MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 38}}}, {"value": "./RightArmyItemLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 31}, "end": {"line": 7, "column": 53}}}, {"value": "./RightCityItemLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 31}, "end": {"line": 9, "column": 53}}}, {"value": "./RightTagItemLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 51}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_7", "loc": {"start": {"line": 12, "column": 29}, "end": {"line": 12, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_8", "loc": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/RightTagItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/Setting.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 46}, "end": {"line": 2, "column": 50}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 56}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/SkillInfoLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 57}}}, {"value": "../../skill/SkillCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 51}}}, {"value": "./SkillIconLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 45}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 11, "column": 29}, "end": {"line": 11, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/SkillIconLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 66}, "end": {"line": 1, "column": 70}}}, {"value": "../../skill/SkillCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 51}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/SkillLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "../../skill/SkillCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 51}}}, {"value": "../../skill/SkillProxy", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 22}, "end": {"line": 6, "column": 46}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 56}}}, {"value": "../../utils/ListLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 45}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/SkillItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 64}, "end": {"line": 1, "column": 68}}}, {"value": "../../skill/SkillCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 51}}}, {"value": "./SkillIconLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 45}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/UnionButtonLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "../../union/UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 51}}}, {"value": "../MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 38}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/TransformLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 75}, "end": {"line": 2, "column": 79}}}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 51}}}, {"value": "../MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 38}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 29}, "end": {"line": 8, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/WarReportDesLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 69}, "end": {"line": 1, "column": 73}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 56}}}, {"value": "./WarReportDesItemLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 34}, "end": {"line": 6, "column": 59}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/WarReportDesItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 80}, "end": {"line": 1, "column": 84}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 52}}}, {"value": "../../config/skill/Skill", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 32}, "end": {"line": 4, "column": 58}}}, {"value": "../../general/GeneralCommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 57}}}, {"value": "../../skill/SkillCommand", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 51}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 47}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/WarButtonLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 44}, "end": {"line": 1, "column": 48}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/WarReportLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 8, "column": 77}, "end": {"line": 8, "column": 81}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 41}}}, {"value": "./WarReportDesLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 13, "column": 30}, "end": {"line": 13, "column": 51}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 14, "column": 25}, "end": {"line": 14, "column": 47}}}, {"value": "../../utils/ListLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 45}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 16, "column": 29}, "end": {"line": 16, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 17, "column": 27}, "end": {"line": 17, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/SmallMapLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 59}, "end": {"line": 1, "column": 63}}}, {"value": "../MapCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 38}}}, {"value": "../MapUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 32}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 47}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/network/http/HttpInvoke.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../socket/NetInterface", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 49}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 47}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/map/ui/WarReportItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 51}, "end": {"line": 1, "column": 55}}}, {"value": "../../login/LoginCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 51}}}, {"value": "../../utils/DateUtil", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 43}}}, {"value": "./MapUICommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 47}}}, {"value": "./GeneralItemLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 49}}}, {"value": "../../common/AudioManager", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 29}, "end": {"line": 11, "column": 56}}}, {"value": "../../common/LogicEvent", "rewritten": "__unresolved_7", "loc": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 52}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/network/http/HttpManager.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "./HttpInvoke", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 42}, "end": {"line": 2, "column": 56}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/network/socket/NetManager.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "./NetNode", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 43}, "end": {"line": 2, "column": 54}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/network/socket/NetNode.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "./NetInterface", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 40}, "end": {"line": 2, "column": 56}}}, {"value": "./NetTimer", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 37}}}, {"value": "./WebSock", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 35}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 47}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/network/socket/NetTimer.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "./NetInterface", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 41}}}, {"value": "../../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 47}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/network/socket/NetInterface.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/scene/LoginScene.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 65}, "end": {"line": 1, "column": 69}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 49}}}, {"value": "../login/LoginCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 48}}}, {"value": "../network/socket/NetInterface", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 57}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_5", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 44}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/scene/MapScene.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 60}, "end": {"line": 1, "column": 64}}}, {"value": "../map/MapResBuildLogic", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 54}}}, {"value": "../map/MapBuildTipsLogic", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 30}, "end": {"line": 5, "column": 56}}}, {"value": "../map/MapCityLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 46}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 42}}}, {"value": "../map/MapLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 38}}}, {"value": "../map/MapProxy", "rewritten": "__unresolved_6", "loc": {"start": {"line": 10, "column": 40}, "end": {"line": 10, "column": 57}}}, {"value": "../map/MapResLogic", "rewritten": "__unresolved_7", "loc": {"start": {"line": 11, "column": 24}, "end": {"line": 11, "column": 44}}}, {"value": "../map/MapUtil", "rewritten": "__unresolved_8", "loc": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 36}}}, {"value": "../map/MapFacilityBuildLogic", "rewritten": "__unresolved_9", "loc": {"start": {"line": 13, "column": 34}, "end": {"line": 13, "column": 64}}}, {"value": "../map/MapBuildTagLogic", "rewritten": "__unresolved_10", "loc": {"start": {"line": 14, "column": 29}, "end": {"line": 14, "column": 54}}}, {"value": "../map/MapSysCityLogic", "rewritten": "__unresolved_11", "loc": {"start": {"line": 15, "column": 28}, "end": {"line": 15, "column": 52}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_12", "loc": {"start": {"line": 16, "column": 25}, "end": {"line": 16, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_13", "loc": {"start": {"line": 17, "column": 27}, "end": {"line": 17, "column": 49}}}, {"value": "../core/coreEvent", "rewritten": "__unresolved_14", "loc": {"start": {"line": 18, "column": 26}, "end": {"line": 18, "column": 45}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/network/socket/WebSock.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../../libs/crypto/crypto", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 51}}}, {"value": "../../libs/gzip/gzip", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 44}}}, {"value": "../../libs/convert", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 24}, "end": {"line": 4, "column": 44}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/skill/SkillProxy.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionApplyItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 41}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/skill/SkillCommand.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../config/ServerConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}, {"value": "../network/socket/NetManager", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 57}}}, {"value": "./SkillProxy", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 37}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/tools/GeneralTool.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 122}, "end": {"line": 1, "column": 126}}}, {"value": "../core/LoaderManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 54}, "end": {"line": 4, "column": 77}}}, {"value": "../general/GeneralCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 54}}}, {"value": "../map/ui/GeneralRosterLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 31}, "end": {"line": 7, "column": 61}}}, {"value": "cc/env", "loc": {"start": {"line": 8, "column": 20}, "end": {"line": 8, "column": 28}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts"}, {"specifierOrURL": "cc/env", "isExternal": true}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts": {"mTimestamp": 1753949235889.0151, "chunkId": "assets/scripts/test/SimpleLoginTest.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 142}, "end": {"line": 1, "column": 146}}}, {"value": "../login/SimpleLoginUI", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 30}, "end": {"line": 2, "column": 54}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/tools/MapTool.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 70}, "end": {"line": 1, "column": 74}}}, {"value": "../map/MapProxy", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 44}}}, {"value": "cc/env", "loc": {"start": {"line": 5, "column": 20}, "end": {"line": 5, "column": 28}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts"}, {"specifierOrURL": "cc/env", "isExternal": true}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionCommand.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../network/socket/NetManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 57}}}, {"value": "./UnionProxy", "rewritten": "__unresolved_2", "loc": {"start": {"line": 3, "column": 34}, "end": {"line": 3, "column": 48}}}, {"value": "../config/ServerConfig", "rewritten": "__unresolved_3", "loc": {"start": {"line": 4, "column": 29}, "end": {"line": 4, "column": 53}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_4", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 42}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_5", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 51}, "end": {"line": 1, "column": 55}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 3, "column": 41}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 44}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_3", "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_4", "loc": {"start": {"line": 7, "column": 27}, "end": {"line": 7, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionCreateLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 47}, "end": {"line": 2, "column": 51}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": 41}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 44}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": 53}}}, {"value": "../libs/NameDict", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 45}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionApplyLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 8, "column": 50}, "end": {"line": 8, "column": 54}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 11, "column": 25}, "end": {"line": 11, "column": 41}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 14, "column": 23}, "end": {"line": 14, "column": 42}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 15, "column": 25}, "end": {"line": 15, "column": 44}}}, {"value": "../utils/ListLogic", "rewritten": "__unresolved_4", "loc": {"start": {"line": 16, "column": 22}, "end": {"line": 16, "column": 42}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 17, "column": 27}, "end": {"line": 17, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionLobbyLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 50}, "end": {"line": 1, "column": 54}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 41}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 44}}}, {"value": "../utils/ListLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 42}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_4", "loc": {"start": {"line": 8, "column": 27}, "end": {"line": 8, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionLogLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 3, "column": 50}, "end": {"line": 3, "column": 54}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 6, "column": 25}, "end": {"line": 6, "column": 41}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 42}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 44}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_4", "loc": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 49}}}, {"value": "../utils/ListLogic", "rewritten": "__unresolved_5", "loc": {"start": {"line": 12, "column": 22}, "end": {"line": 12, "column": 42}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionLogItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 45}, "end": {"line": 2, "column": 49}}}, {"value": "../utils/DateUtil", "rewritten": "__unresolved_1", "loc": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 40}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionMainLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 68}, "end": {"line": 1, "column": 72}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 41}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 42}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 44}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 29}, "end": {"line": 9, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 2, "column": 51}, "end": {"line": 2, "column": 55}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_2", "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 49}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 42}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 44}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionMemItemLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 8, "column": 45}, "end": {"line": 8, "column": 49}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 12, "column": 25}, "end": {"line": 12, "column": 41}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_2", "loc": {"start": {"line": 14, "column": 25}, "end": {"line": 14, "column": 44}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_3", "loc": {"start": {"line": 15, "column": 29}, "end": {"line": 15, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_4", "loc": {"start": {"line": 16, "column": 27}, "end": {"line": 16, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionMemberItemOpLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 52}, "end": {"line": 1, "column": 56}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": 53}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 42}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_3", "loc": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 41}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/utils/BgScale.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 92}, "end": {"line": 1, "column": 96}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionProxy.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "../login/LoginCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": 48}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 42}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts": {"mTimestamp": 1753942910933.2456, "chunkId": "assets/scripts/utils/CanvasAdapter.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 80}, "end": {"line": 1, "column": 84}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/union/UnionMemberLogic.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 69}, "end": {"line": 1, "column": 73}}}, {"value": "./UnionCommand", "rewritten": "__unresolved_1", "loc": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": 41}}}, {"value": "../map/MapCommand", "rewritten": "__unresolved_2", "loc": {"start": {"line": 7, "column": 23}, "end": {"line": 7, "column": 42}}}, {"value": "./UnionMemberItemOpLogic", "rewritten": "__unresolved_3", "loc": {"start": {"line": 8, "column": 35}, "end": {"line": 8, "column": 61}}}, {"value": "../utils/EventMgr", "rewritten": "__unresolved_4", "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 9, "column": 44}}}, {"value": "../common/AudioManager", "rewritten": "__unresolved_5", "loc": {"start": {"line": 10, "column": 29}, "end": {"line": 10, "column": 53}}}, {"value": "../common/LogicEvent", "rewritten": "__unresolved_6", "loc": {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts": {"mTimestamp": 1753942893358.623, "chunkId": "assets/scripts/utils/CanvasFixer.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 63}, "end": {"line": 1, "column": 67}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts": {"mTimestamp": 1753950197001.538, "chunkId": "assets/scripts/utils/ClickTestHelper.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 111}, "end": {"line": 1, "column": 115}}}, {"value": "./FixedScreenAdapter", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 35}, "end": {"line": 2, "column": 57}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/utils/DateUtil.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/utils/EventMgr.js", "imports": [{"value": "cc"}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts": {"mTimestamp": 1753948049645.7712, "chunkId": "assets/scripts/utils/FixedScreenAdapter.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 100}, "end": {"line": 1, "column": 104}}}, {"value": "../config/GameConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/utils/Modal.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 3, "column": 51}, "end": {"line": 3, "column": 55}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/utils/ListLogic.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 141}, "end": {"line": 1, "column": 145}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts": {"mTimestamp": 1753947519357.4048, "chunkId": "assets/scripts/utils/ResponsiveAdapter.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 115}, "end": {"line": 1, "column": 119}}}, {"value": "../config/GameConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/utils/LocalCache.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 20}, "end": {"line": 1, "column": 24}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts": {"mTimestamp": 1753943952622.2349, "chunkId": "assets/scripts/utils/ScreenAdapter.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 110}, "end": {"line": 1, "column": 114}}}, {"value": "../config/GameConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/utils/Toast.js", "imports": [{"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 45}, "end": {"line": 1, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts": {"mTimestamp": 1753947558524.117, "chunkId": "assets/scripts/utils/SimpleScreenAdapter.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "cc", "loc": {"start": {"line": 1, "column": 100}, "end": {"line": 1, "column": 104}}}, {"value": "../config/GameConfig", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 49}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts"}]}, "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts": {"mTimestamp": 1717254922000, "chunkId": "assets/scripts/utils/Tools.js", "imports": [{"value": "cce:/internal/code-quality/cr.mjs", "rewritten": "__unresolved_0"}, {"value": "cc"}, {"value": "./LocalCache", "rewritten": "__unresolved_1", "loc": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": 41}}}], "modLoMetadata": {"type": "esm"}, "resolvedImports": [{"isExternal": false, "url": "cce:/internal/code-quality/cr.mjs"}, {"isExternal": false, "url": "cce:/internal/x/cc"}, {"isExternal": false, "url": "file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts"}]}}