{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts"], "names": ["_decorator", "Component", "Node", "<PERSON><PERSON>", "Label", "EditBox", "<PERSON><PERSON>", "UITransform", "Color", "Sprite", "view", "screen", "SimpleLoginUI", "ccclass", "property", "SimpleLoginTest", "onLoad", "console", "log", "createTestUI", "printDebugInfo", "canvas", "getComponent", "addComponent", "alignCanvasWithScreen", "node", "setPosition", "uiTransform", "windowSize", "setContentSize", "width", "height", "setAnchorPoint", "createBackground", "createTestButton", "createStatusLabel", "addSwitchToLoginUIButton", "bgNode", "setParent", "bgTransform", "bgSprite", "color", "buttonNode", "buttonTransform", "buttonSprite", "testButton", "transition", "Transition", "COLOR", "normalColor", "pressedColor", "hoverColor", "disabledColor", "labelNode", "labelTransform", "label", "string", "clickCount", "fontSize", "on", "EventType", "CLICK", "onTestButtonClick", "test<PERSON><PERSON><PERSON>", "buttonLabel", "getComponentInChildren", "printClickInfo", "visibleSize", "getVisibleSize", "visible<PERSON><PERSON>in", "getVisibleOrigin", "designSize", "getDesignResolutionSize", "toFixed", "x", "y", "addInputTest", "inputNode", "inputTransform", "inputSprite", "borderNode", "borderTransform", "borderSprite", "editBox", "placeholder", "fontColor", "placeholderFontColor", "button", "createActualLoginUI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loginUINode", "loginUI"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAgCC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACvHC,MAAAA,a,iBAAAA,a;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;AAE9B;AACA;AACA;AACA;;iCAEae,e,WADZF,OAAO,CAAC,iBAAD,C,yBAAR,MACaE,eADb,SACqCd,SADrC,CAC+C;AAAA;AAAA;;AAAA,8CAEd,IAFc;;AAAA,6CAGhB,IAHgB;;AAAA,8CAId,CAJc;AAAA;;AAMjCe,QAAAA,MAAM,GAAS;AACrBC,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ,EADqB,CAGrB;;AACA,eAAKC,YAAL,GAJqB,CAMrB;;AACA,eAAKC,cAAL;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,YAAY,GAAS;AACzB;AACA,cAAIE,MAAM,GAAG,KAAKC,YAAL,CAAkBhB,MAAlB,CAAb;;AACA,cAAI,CAACe,MAAL,EAAa;AACTA,YAAAA,MAAM,GAAG,KAAKE,YAAL,CAAkBjB,MAAlB,CAAT;AACH,WALwB,CAOzB;;;AACAe,UAAAA,MAAM,CAACG,qBAAP,GAA+B,KAA/B;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EATyB,CAWzB;;AACA,gBAAMC,WAAW,GAAG,KAAKL,YAAL,CAAkBf,WAAlB,CAApB;;AACA,cAAIoB,WAAJ,EAAiB;AACb,kBAAMC,UAAU,GAAGjB,MAAM,CAACiB,UAA1B;AACAD,YAAAA,WAAW,CAACE,cAAZ,CAA2BD,UAAU,CAACE,KAAtC,EAA6CF,UAAU,CAACG,MAAxD;AACAJ,YAAAA,WAAW,CAACK,cAAZ,CAA2B,GAA3B,EAAgC,GAAhC;AACAf,YAAAA,OAAO,CAACC,GAAR,CAAa,kCAAiCU,UAAU,CAACE,KAAM,IAAGF,UAAU,CAACG,MAAO,EAApF;AACH,WAlBwB,CAoBzB;;;AACA,eAAKE,gBAAL,GArByB,CAuBzB;;AACA,eAAKC,gBAAL,GAxByB,CA0BzB;;AACA,eAAKC,iBAAL,GA3ByB,CA6BzB;;AACA,eAAKC,wBAAL;AAEAnB,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACH;AAED;AACJ;AACA;;;AACYe,QAAAA,gBAAgB,GAAS;AAC7B,gBAAMI,MAAM,GAAG,IAAInC,IAAJ,CAAS,YAAT,CAAf;AACAmC,UAAAA,MAAM,CAACC,SAAP,CAAiB,KAAKb,IAAtB;AAEA,gBAAMc,WAAW,GAAGF,MAAM,CAACd,YAAP,CAAoBhB,WAApB,CAApB;AACA,gBAAMqB,UAAU,GAAGjB,MAAM,CAACiB,UAA1B;AACAW,UAAAA,WAAW,CAACV,cAAZ,CAA2BD,UAAU,CAACE,KAAtC,EAA6CF,UAAU,CAACG,MAAxD;AACAQ,UAAAA,WAAW,CAACP,cAAZ,CAA2B,GAA3B,EAAgC,GAAhC;AAEA,gBAAMQ,QAAQ,GAAGH,MAAM,CAACd,YAAP,CAAoBd,MAApB,CAAjB;AACA+B,UAAAA,QAAQ,CAACC,KAAT,GAAiB,IAAIjC,KAAJ,CAAU,EAAV,EAAc,EAAd,EAAkB,EAAlB,EAAsB,GAAtB,CAAjB,CAV6B,CAUgB;;AAE7C6B,UAAAA,MAAM,CAACX,WAAP,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB;AAEAT,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACH;AAED;AACJ;AACA;;;AACYgB,QAAAA,gBAAgB,GAAS;AAC7B,gBAAMQ,UAAU,GAAG,IAAIxC,IAAJ,CAAS,YAAT,CAAnB;AACAwC,UAAAA,UAAU,CAACJ,SAAX,CAAqB,KAAKb,IAA1B,EAF6B,CAI7B;;AACA,gBAAMkB,eAAe,GAAGD,UAAU,CAACnB,YAAX,CAAwBhB,WAAxB,CAAxB;AACAoC,UAAAA,eAAe,CAACd,cAAhB,CAA+B,GAA/B,EAAoC,EAApC;AACAc,UAAAA,eAAe,CAACX,cAAhB,CAA+B,GAA/B,EAAoC,GAApC;AACAU,UAAAA,UAAU,CAAChB,WAAX,CAAuB,CAAvB,EAA0B,CAA1B,EAA6B,CAA7B,EAR6B,CAQI;AAEjC;;AACA,gBAAMkB,YAAY,GAAGF,UAAU,CAACnB,YAAX,CAAwBd,MAAxB,CAArB;AACAmC,UAAAA,YAAY,CAACH,KAAb,GAAqB,IAAIjC,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,GAAlB,EAAuB,GAAvB,CAArB,CAZ6B,CAYqB;AAElD;;AACA,eAAKqC,UAAL,GAAkBH,UAAU,CAACnB,YAAX,CAAwBpB,MAAxB,CAAlB;AACA,eAAK0C,UAAL,CAAgBC,UAAhB,GAA6B3C,MAAM,CAAC4C,UAAP,CAAkBC,KAA/C;AACA,eAAKH,UAAL,CAAgBI,WAAhB,GAA8B,IAAIzC,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,GAAlB,EAAuB,GAAvB,CAA9B;AACA,eAAKqC,UAAL,CAAgBK,YAAhB,GAA+B,IAAI1C,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,GAAlB,EAAuB,GAAvB,CAA/B;AACA,eAAKqC,UAAL,CAAgBM,UAAhB,GAA6B,IAAI3C,KAAJ,CAAU,EAAV,EAAc,GAAd,EAAmB,GAAnB,EAAwB,GAAxB,CAA7B;AACA,eAAKqC,UAAL,CAAgBO,aAAhB,GAAgC,IAAI5C,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAhC,CApB6B,CAsB7B;;AACA,gBAAM6C,SAAS,GAAG,IAAInD,IAAJ,CAAS,OAAT,CAAlB;AACAmD,UAAAA,SAAS,CAACf,SAAV,CAAoBI,UAApB;AAEA,gBAAMY,cAAc,GAAGD,SAAS,CAAC9B,YAAV,CAAuBhB,WAAvB,CAAvB;AACA+C,UAAAA,cAAc,CAACzB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAyB,UAAAA,cAAc,CAACtB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAqB,UAAAA,SAAS,CAAC3B,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AAEA,gBAAM6B,KAAK,GAAGF,SAAS,CAAC9B,YAAV,CAAuBnB,KAAvB,CAAd;AACAmD,UAAAA,KAAK,CAACC,MAAN,GAAgB,SAAQ,KAAKC,UAAW,GAAxC;AACAF,UAAAA,KAAK,CAACG,QAAN,GAAiB,EAAjB;AACAH,UAAAA,KAAK,CAACd,KAAN,GAAc,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAd,CAlC6B,CAoC7B;;AACA,eAAKqC,UAAL,CAAgBpB,IAAhB,CAAqBkC,EAArB,CAAwBxD,MAAM,CAACyD,SAAP,CAAiBC,KAAzC,EAAgD,KAAKC,iBAArD,EAAwE,IAAxE;AAEA7C,UAAAA,OAAO,CAACC,GAAR,CAAY,0CAAZ;AACH;AAED;AACJ;AACA;;;AACYiB,QAAAA,iBAAiB,GAAS;AAC9B,gBAAMkB,SAAS,GAAG,IAAInD,IAAJ,CAAS,aAAT,CAAlB;AACAmD,UAAAA,SAAS,CAACf,SAAV,CAAoB,KAAKb,IAAzB;AAEA,gBAAM6B,cAAc,GAAGD,SAAS,CAAC9B,YAAV,CAAuBhB,WAAvB,CAAvB;AACA+C,UAAAA,cAAc,CAACzB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAyB,UAAAA,cAAc,CAACtB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAqB,UAAAA,SAAS,CAAC3B,WAAV,CAAsB,CAAtB,EAAyB,CAAC,GAA1B,EAA+B,CAA/B,EAP8B,CAOK;;AAEnC,eAAKqC,SAAL,GAAiBV,SAAS,CAAC9B,YAAV,CAAuBnB,KAAvB,CAAjB;AACA,eAAK2D,SAAL,CAAeP,MAAf,GAAwB,WAAxB;AACA,eAAKO,SAAL,CAAeL,QAAf,GAA0B,EAA1B;AACA,eAAKK,SAAL,CAAetB,KAAf,GAAuB,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAvB;AAEAS,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACH;AAED;AACJ;AACA;;;AACY4C,QAAAA,iBAAiB,GAAS;AAC9B,eAAKL,UAAL;AAEAxC,UAAAA,OAAO,CAACC,GAAR,CAAa,iCAAgC,KAAKuC,UAAW,EAA7D,EAH8B,CAK9B;;AACA,gBAAMO,WAAW,GAAG,KAAKnB,UAAL,CAAgBpB,IAAhB,CAAqBwC,sBAArB,CAA4C7D,KAA5C,CAApB;;AACA,cAAI4D,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACR,MAAZ,GAAsB,SAAQ,KAAKC,UAAW,GAA9C;AACH,WAT6B,CAW9B;;;AACA,cAAI,KAAKM,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeP,MAAf,GAAyB,cAAa,KAAKC,UAAW,qBAAtD;AACA,iBAAKM,SAAL,CAAetB,KAAf,GAAuB,IAAIjC,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,CAAlB,EAAqB,GAArB,CAAvB,CAFgB,CAEkC;AACrD,WAf6B,CAiB9B;;;AACA,eAAK0D,cAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,cAAc,GAAS;AAC3B,gBAAMtC,UAAU,GAAGjB,MAAM,CAACiB,UAA1B;AACA,gBAAMuC,WAAW,GAAGzD,IAAI,CAAC0D,cAAL,EAApB;AACA,gBAAMC,aAAa,GAAG3D,IAAI,CAAC4D,gBAAL,EAAtB;AACA,gBAAMC,UAAU,GAAG7D,IAAI,CAAC8D,uBAAL,EAAnB;AAEAvD,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQ,KAAKuC,UAAW,EAArC;AACAxC,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQU,UAAU,CAACE,KAAM,IAAGF,UAAU,CAACG,MAAO,EAA3D;AACAd,UAAAA,OAAO,CAACC,GAAR,CAAa,UAASqD,UAAU,CAACzC,KAAM,IAAGyC,UAAU,CAACxC,MAAO,EAA5D;AACAd,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQiD,WAAW,CAACrC,KAAZ,CAAkB2C,OAAlB,CAA0B,CAA1B,CAA6B,IAAGN,WAAW,CAACpC,MAAZ,CAAmB0C,OAAnB,CAA2B,CAA3B,CAA8B,EAAnF;AACAxD,UAAAA,OAAO,CAACC,GAAR,CAAa,UAASmD,aAAa,CAACK,CAAd,CAAgBD,OAAhB,CAAwB,CAAxB,CAA2B,KAAIJ,aAAa,CAACM,CAAd,CAAgBF,OAAhB,CAAwB,CAAxB,CAA2B,GAAhF;AACAxD,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACH;AAED;AACJ;AACA;;;AACYE,QAAAA,cAAc,GAAS;AAC3B,gBAAMQ,UAAU,GAAGjB,MAAM,CAACiB,UAA1B;AACA,gBAAMuC,WAAW,GAAGzD,IAAI,CAAC0D,cAAL,EAApB;AACA,gBAAMC,aAAa,GAAG3D,IAAI,CAAC4D,gBAAL,EAAtB;AACA,gBAAMC,UAAU,GAAG7D,IAAI,CAAC8D,uBAAL,EAAnB;AAEAvD,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ;AACAD,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQU,UAAU,CAACE,KAAM,IAAGF,UAAU,CAACG,MAAO,EAA3D;AACAd,UAAAA,OAAO,CAACC,GAAR,CAAa,UAASqD,UAAU,CAACzC,KAAM,IAAGyC,UAAU,CAACxC,MAAO,EAA5D;AACAd,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQiD,WAAW,CAACrC,KAAZ,CAAkB2C,OAAlB,CAA0B,CAA1B,CAA6B,IAAGN,WAAW,CAACpC,MAAZ,CAAmB0C,OAAnB,CAA2B,CAA3B,CAA8B,EAAnF;AACAxD,UAAAA,OAAO,CAACC,GAAR,CAAa,UAASmD,aAAa,CAACK,CAAd,CAAgBD,OAAhB,CAAwB,CAAxB,CAA2B,KAAIJ,aAAa,CAACM,CAAd,CAAgBF,OAAhB,CAAwB,CAAxB,CAA2B,GAAhF,EAV2B,CAY3B;;AACA,gBAAMpD,MAAM,GAAG,KAAKC,YAAL,CAAkBhB,MAAlB,CAAf;;AACA,cAAIe,MAAJ,EAAY;AACRJ,YAAAA,OAAO,CAACC,GAAR,CAAa,iCAAgCG,MAAM,CAACG,qBAAsB,EAA1E;AACH;;AAEDP,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;AACH;AAED;AACJ;AACA;;;AACW0D,QAAAA,YAAY,GAAS;AACxB,gBAAMC,SAAS,GAAG,IAAI3E,IAAJ,CAAS,WAAT,CAAlB;AACA2E,UAAAA,SAAS,CAACvC,SAAV,CAAoB,KAAKb,IAAzB;AAEA,gBAAMqD,cAAc,GAAGD,SAAS,CAACtD,YAAV,CAAuBhB,WAAvB,CAAvB;AACAuE,UAAAA,cAAc,CAACjD,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAiD,UAAAA,cAAc,CAAC9C,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACA6C,UAAAA,SAAS,CAACnD,WAAV,CAAsB,CAAtB,EAAyB,GAAzB,EAA8B,CAA9B,EAPwB,CAOU;AAElC;;AACA,gBAAMqD,WAAW,GAAGF,SAAS,CAACtD,YAAV,CAAuBd,MAAvB,CAApB;AACAsE,UAAAA,WAAW,CAACtC,KAAZ,GAAoB,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAApB,CAXwB,CAaxB;;AACA,gBAAMwE,UAAU,GAAG,IAAI9E,IAAJ,CAAS,QAAT,CAAnB;AACA8E,UAAAA,UAAU,CAAC1C,SAAX,CAAqBuC,SAArB;AACA,gBAAMI,eAAe,GAAGD,UAAU,CAACzD,YAAX,CAAwBhB,WAAxB,CAAxB;AACA0E,UAAAA,eAAe,CAACpD,cAAhB,CAA+B,GAA/B,EAAoC,EAApC;AACAoD,UAAAA,eAAe,CAACjD,cAAhB,CAA+B,GAA/B,EAAoC,GAApC;AACAgD,UAAAA,UAAU,CAACtD,WAAX,CAAuB,CAAvB,EAA0B,CAA1B,EAA6B,CAAC,CAA9B;AACA,gBAAMwD,YAAY,GAAGF,UAAU,CAACzD,YAAX,CAAwBd,MAAxB,CAArB;AACAyE,UAAAA,YAAY,CAACzC,KAAb,GAAqB,IAAIjC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,GAAnB,CAArB;AAEA,gBAAM2E,OAAO,GAAGN,SAAS,CAACtD,YAAV,CAAuBlB,OAAvB,CAAhB;AACA8E,UAAAA,OAAO,CAAC3B,MAAR,GAAiB,EAAjB;AACA2B,UAAAA,OAAO,CAACC,WAAR,GAAsB,YAAtB;AACAD,UAAAA,OAAO,CAACzB,QAAR,GAAmB,EAAnB;AACAyB,UAAAA,OAAO,CAACE,SAAR,GAAoB,IAAI7E,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,GAAnB,CAApB,CA3BwB,CA2BqB;;AAC7C2E,UAAAA,OAAO,CAACG,oBAAR,GAA+B,IAAI9E,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAA/B,CA5BwB,CA4BsC;AAE9D;;AACA,gBAAM6C,SAAS,GAAG,IAAInD,IAAJ,CAAS,YAAT,CAAlB;AACAmD,UAAAA,SAAS,CAACf,SAAV,CAAoB,KAAKb,IAAzB;AACA,gBAAM6B,cAAc,GAAGD,SAAS,CAAC9B,YAAV,CAAuBhB,WAAvB,CAAvB;AACA+C,UAAAA,cAAc,CAACzB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAyB,UAAAA,cAAc,CAACtB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAqB,UAAAA,SAAS,CAAC3B,WAAV,CAAsB,CAAtB,EAAyB,GAAzB,EAA8B,CAA9B;AAEA,gBAAM6B,KAAK,GAAGF,SAAS,CAAC9B,YAAV,CAAuBnB,KAAvB,CAAd;AACAmD,UAAAA,KAAK,CAACC,MAAN,GAAe,QAAf;AACAD,UAAAA,KAAK,CAACG,QAAN,GAAiB,EAAjB;AACAH,UAAAA,KAAK,CAACd,KAAN,GAAc,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAd;AAEAS,UAAAA,OAAO,CAACC,GAAR,CAAY,6BAAZ;AACH;AAED;AACJ;AACA;;;AACYkB,QAAAA,wBAAwB,GAAS;AACrC,gBAAMM,UAAU,GAAG,IAAIxC,IAAJ,CAAS,qBAAT,CAAnB;AACAwC,UAAAA,UAAU,CAACJ,SAAX,CAAqB,KAAKb,IAA1B;AAEA,gBAAMkB,eAAe,GAAGD,UAAU,CAACnB,YAAX,CAAwBhB,WAAxB,CAAxB;AACAoC,UAAAA,eAAe,CAACd,cAAhB,CAA+B,GAA/B,EAAoC,EAApC;AACAc,UAAAA,eAAe,CAACX,cAAhB,CAA+B,GAA/B,EAAoC,GAApC;AACAU,UAAAA,UAAU,CAAChB,WAAX,CAAuB,CAAvB,EAA0B,CAAC,GAA3B,EAAgC,CAAhC,EAPqC,CASrC;;AACA,gBAAMkB,YAAY,GAAGF,UAAU,CAACnB,YAAX,CAAwBd,MAAxB,CAArB;AACAmC,UAAAA,YAAY,CAACH,KAAb,GAAqB,IAAIjC,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,CAAlB,EAAqB,GAArB,CAArB,CAXqC,CAWW;AAEhD;;AACA,gBAAM+E,MAAM,GAAG7C,UAAU,CAACnB,YAAX,CAAwBpB,MAAxB,CAAf;AACAoF,UAAAA,MAAM,CAACzC,UAAP,GAAoB3C,MAAM,CAAC4C,UAAP,CAAkBC,KAAtC;AACAuC,UAAAA,MAAM,CAACtC,WAAP,GAAqB,IAAIzC,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,CAAlB,EAAqB,GAArB,CAArB;AACA+E,UAAAA,MAAM,CAACrC,YAAP,GAAsB,IAAI1C,KAAJ,CAAU,CAAV,EAAa,GAAb,EAAkB,CAAlB,EAAqB,GAArB,CAAtB;AACA+E,UAAAA,MAAM,CAACpC,UAAP,GAAoB,IAAI3C,KAAJ,CAAU,EAAV,EAAc,GAAd,EAAmB,EAAnB,EAAuB,GAAvB,CAApB,CAlBqC,CAoBrC;;AACA,gBAAM6C,SAAS,GAAG,IAAInD,IAAJ,CAAS,OAAT,CAAlB;AACAmD,UAAAA,SAAS,CAACf,SAAV,CAAoBI,UAApB;AAEA,gBAAMY,cAAc,GAAGD,SAAS,CAAC9B,YAAV,CAAuBhB,WAAvB,CAAvB;AACA+C,UAAAA,cAAc,CAACzB,cAAf,CAA8B,GAA9B,EAAmC,EAAnC;AACAyB,UAAAA,cAAc,CAACtB,cAAf,CAA8B,GAA9B,EAAmC,GAAnC;AACAqB,UAAAA,SAAS,CAAC3B,WAAV,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AAEA,gBAAM6B,KAAK,GAAGF,SAAS,CAAC9B,YAAV,CAAuBnB,KAAvB,CAAd;AACAmD,UAAAA,KAAK,CAACC,MAAN,GAAe,SAAf;AACAD,UAAAA,KAAK,CAACG,QAAN,GAAiB,EAAjB;AACAH,UAAAA,KAAK,CAACd,KAAN,GAAc,IAAIjC,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,GAApB,EAAyB,GAAzB,CAAd,CAhCqC,CAkCrC;;AACA+E,UAAAA,MAAM,CAAC9D,IAAP,CAAYkC,EAAZ,CAAexD,MAAM,CAACyD,SAAP,CAAiBC,KAAhC,EAAuC,KAAK2B,mBAA5C,EAAiE,IAAjE;AAEAvE,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ;AACH;AAED;AACJ;AACA;;;AACWsE,QAAAA,mBAAmB,GAAS;AAC/BvE,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAD+B,CAG/B;;AACA,eAAKO,IAAL,CAAUgE,iBAAV,GAJ+B,CAM/B;;AACA,gBAAMC,WAAW,GAAG,IAAIxF,IAAJ,CAAS,SAAT,CAApB;AACAwF,UAAAA,WAAW,CAACpD,SAAZ,CAAsB,KAAKb,IAA3B,EAR+B,CAU/B;;AACA,gBAAMkE,OAAO,GAAGD,WAAW,CAACnE,YAAZ;AAAA;AAAA,6CAAhB;AAEAN,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;AACH;;AA1T0C,O", "sourcesContent": ["import { _decorator, Component, Node, Button, Label, EditBox, Canvas, UITransform, Color, Sprite, SpriteFrame, resources, view, screen } from 'cc';\nimport { SimpleLoginUI } from '../login/SimpleLoginUI';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 简单登录测试组件\n * 用于测试点击响应是否正常\n */\n@ccclass('SimpleLoginTest')\nexport class SimpleLoginTest extends Component {\n    \n    private testButton: Button = null;\n    private testLabel: Label = null;\n    private clickCount: number = 0;\n    \n    protected onLoad(): void {\n        console.log('[SimpleLoginTest] 开始创建简单登录测试界面');\n        \n        // 创建测试UI\n        this.createTestUI();\n        \n        // 打印调试信息\n        this.printDebugInfo();\n    }\n    \n    /**\n     * 创建测试UI\n     */\n    private createTestUI(): void {\n        // 确保当前节点有Canvas组件\n        let canvas = this.getComponent(Canvas);\n        if (!canvas) {\n            canvas = this.addComponent(Canvas);\n        }\n        \n        // 设置Canvas\n        canvas.alignCanvasWithScreen = false;\n        this.node.setPosition(0, 0, 0);\n        \n        // 设置UITransform\n        const uiTransform = this.getComponent(UITransform);\n        if (uiTransform) {\n            const windowSize = screen.windowSize;\n            uiTransform.setContentSize(windowSize.width, windowSize.height);\n            uiTransform.setAnchorPoint(0.5, 0.5);\n            console.log(`[SimpleLoginTest] Canvas尺寸设置为: ${windowSize.width}x${windowSize.height}`);\n        }\n        \n        // 创建背景\n        this.createBackground();\n        \n        // 创建测试按钮\n        this.createTestButton();\n        \n        // 创建状态标签\n        this.createStatusLabel();\n\n        // 添加切换到实际登录UI的按钮\n        this.addSwitchToLoginUIButton();\n\n        console.log('[SimpleLoginTest] 测试UI创建完成');\n    }\n    \n    /**\n     * 创建背景\n     */\n    private createBackground(): void {\n        const bgNode = new Node('Background');\n        bgNode.setParent(this.node);\n        \n        const bgTransform = bgNode.addComponent(UITransform);\n        const windowSize = screen.windowSize;\n        bgTransform.setContentSize(windowSize.width, windowSize.height);\n        bgTransform.setAnchorPoint(0.5, 0.5);\n        \n        const bgSprite = bgNode.addComponent(Sprite);\n        bgSprite.color = new Color(50, 50, 50, 255); // 深灰色背景\n        \n        bgNode.setPosition(0, 0, 0);\n        \n        console.log('[SimpleLoginTest] 背景创建完成');\n    }\n    \n    /**\n     * 创建测试按钮\n     */\n    private createTestButton(): void {\n        const buttonNode = new Node('TestButton');\n        buttonNode.setParent(this.node);\n        \n        // 设置按钮尺寸和位置\n        const buttonTransform = buttonNode.addComponent(UITransform);\n        buttonTransform.setContentSize(200, 60);\n        buttonTransform.setAnchorPoint(0.5, 0.5);\n        buttonNode.setPosition(0, 0, 0); // 屏幕中央\n        \n        // 添加按钮背景\n        const buttonSprite = buttonNode.addComponent(Sprite);\n        buttonSprite.color = new Color(0, 150, 255, 255); // 蓝色按钮\n        \n        // 添加按钮组件\n        this.testButton = buttonNode.addComponent(Button);\n        this.testButton.transition = Button.Transition.COLOR;\n        this.testButton.normalColor = new Color(0, 150, 255, 255);\n        this.testButton.pressedColor = new Color(0, 100, 200, 255);\n        this.testButton.hoverColor = new Color(50, 180, 255, 255);\n        this.testButton.disabledColor = new Color(100, 100, 100, 255);\n        \n        // 添加按钮文字\n        const labelNode = new Node('Label');\n        labelNode.setParent(buttonNode);\n        \n        const labelTransform = labelNode.addComponent(UITransform);\n        labelTransform.setContentSize(200, 60);\n        labelTransform.setAnchorPoint(0.5, 0.5);\n        labelNode.setPosition(0, 0, 0);\n        \n        const label = labelNode.addComponent(Label);\n        label.string = `点击测试 (${this.clickCount})`;\n        label.fontSize = 24;\n        label.color = new Color(255, 255, 255, 255);\n        \n        // 绑定点击事件\n        this.testButton.node.on(Button.EventType.CLICK, this.onTestButtonClick, this);\n        \n        console.log('[SimpleLoginTest] 测试按钮创建完成，位置: (0, 0, 0)');\n    }\n    \n    /**\n     * 创建状态标签\n     */\n    private createStatusLabel(): void {\n        const labelNode = new Node('StatusLabel');\n        labelNode.setParent(this.node);\n        \n        const labelTransform = labelNode.addComponent(UITransform);\n        labelTransform.setContentSize(400, 100);\n        labelTransform.setAnchorPoint(0.5, 0.5);\n        labelNode.setPosition(0, -150, 0); // 按钮下方\n        \n        this.testLabel = labelNode.addComponent(Label);\n        this.testLabel.string = '等待点击测试...';\n        this.testLabel.fontSize = 18;\n        this.testLabel.color = new Color(255, 255, 255, 255);\n        \n        console.log('[SimpleLoginTest] 状态标签创建完成');\n    }\n    \n    /**\n     * 测试按钮点击事件\n     */\n    private onTestButtonClick(): void {\n        this.clickCount++;\n        \n        console.log(`[SimpleLoginTest] 按钮被点击！点击次数: ${this.clickCount}`);\n        \n        // 更新按钮文字\n        const buttonLabel = this.testButton.node.getComponentInChildren(Label);\n        if (buttonLabel) {\n            buttonLabel.string = `点击测试 (${this.clickCount})`;\n        }\n        \n        // 更新状态标签\n        if (this.testLabel) {\n            this.testLabel.string = `✅ 点击成功！总计: ${this.clickCount} 次\\n点击响应正常，UI适配工作正常`;\n            this.testLabel.color = new Color(0, 255, 0, 255); // 绿色表示成功\n        }\n        \n        // 打印详细的点击信息\n        this.printClickInfo();\n    }\n    \n    /**\n     * 打印点击信息\n     */\n    private printClickInfo(): void {\n        const windowSize = screen.windowSize;\n        const visibleSize = view.getVisibleSize();\n        const visibleOrigin = view.getVisibleOrigin();\n        const designSize = view.getDesignResolutionSize();\n        \n        console.log('=== 点击测试成功信息 ===');\n        console.log(`点击次数: ${this.clickCount}`);\n        console.log(`窗口尺寸: ${windowSize.width}x${windowSize.height}`);\n        console.log(`设计分辨率: ${designSize.width}x${designSize.height}`);\n        console.log(`可视区域: ${visibleSize.width.toFixed(1)}x${visibleSize.height.toFixed(1)}`);\n        console.log(`可视原点: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);\n        console.log('========================');\n    }\n    \n    /**\n     * 打印调试信息\n     */\n    private printDebugInfo(): void {\n        const windowSize = screen.windowSize;\n        const visibleSize = view.getVisibleSize();\n        const visibleOrigin = view.getVisibleOrigin();\n        const designSize = view.getDesignResolutionSize();\n        \n        console.log('=== 简单登录测试调试信息 ===');\n        console.log(`窗口尺寸: ${windowSize.width}x${windowSize.height}`);\n        console.log(`设计分辨率: ${designSize.width}x${designSize.height}`);\n        console.log(`可视区域: ${visibleSize.width.toFixed(1)}x${visibleSize.height.toFixed(1)}`);\n        console.log(`可视原点: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);\n        \n        // 检查Canvas设置\n        const canvas = this.getComponent(Canvas);\n        if (canvas) {\n            console.log(`Canvas alignCanvasWithScreen: ${canvas.alignCanvasWithScreen}`);\n        }\n        \n        console.log('============================');\n    }\n    \n    /**\n     * 添加一个简单的输入框测试\n     */\n    public addInputTest(): void {\n        const inputNode = new Node('TestInput');\n        inputNode.setParent(this.node);\n\n        const inputTransform = inputNode.addComponent(UITransform);\n        inputTransform.setContentSize(300, 50);\n        inputTransform.setAnchorPoint(0.5, 0.5);\n        inputNode.setPosition(0, 100, 0); // 按钮上方\n\n        // 添加白色背景\n        const inputSprite = inputNode.addComponent(Sprite);\n        inputSprite.color = new Color(255, 255, 255, 255);\n\n        // 添加黑色边框\n        const borderNode = new Node('Border');\n        borderNode.setParent(inputNode);\n        const borderTransform = borderNode.addComponent(UITransform);\n        borderTransform.setContentSize(304, 54);\n        borderTransform.setAnchorPoint(0.5, 0.5);\n        borderNode.setPosition(0, 0, -1);\n        const borderSprite = borderNode.addComponent(Sprite);\n        borderSprite.color = new Color(0, 0, 0, 255);\n\n        const editBox = inputNode.addComponent(EditBox);\n        editBox.string = '';\n        editBox.placeholder = '点击这里输入测试文字';\n        editBox.fontSize = 18;\n        editBox.fontColor = new Color(0, 0, 0, 255); // 黑色文字\n        editBox.placeholderFontColor = new Color(128, 128, 128, 255); // 灰色占位符\n\n        // 添加输入框标签\n        const labelNode = new Node('InputLabel');\n        labelNode.setParent(this.node);\n        const labelTransform = labelNode.addComponent(UITransform);\n        labelTransform.setContentSize(300, 30);\n        labelTransform.setAnchorPoint(0.5, 0.5);\n        labelNode.setPosition(0, 140, 0);\n\n        const label = labelNode.addComponent(Label);\n        label.string = '输入框测试：';\n        label.fontSize = 16;\n        label.color = new Color(255, 255, 255, 255);\n\n        console.log('[SimpleLoginTest] 输入框测试添加完成');\n    }\n\n    /**\n     * 添加切换到实际登录UI的按钮\n     */\n    private addSwitchToLoginUIButton(): void {\n        const buttonNode = new Node('SwitchToLoginButton');\n        buttonNode.setParent(this.node);\n\n        const buttonTransform = buttonNode.addComponent(UITransform);\n        buttonTransform.setContentSize(200, 50);\n        buttonTransform.setAnchorPoint(0.5, 0.5);\n        buttonNode.setPosition(0, -200, 0);\n\n        // 按钮背景\n        const buttonSprite = buttonNode.addComponent(Sprite);\n        buttonSprite.color = new Color(0, 200, 0, 255); // 绿色\n\n        // 按钮组件\n        const button = buttonNode.addComponent(Button);\n        button.transition = Button.Transition.COLOR;\n        button.normalColor = new Color(0, 200, 0, 255);\n        button.pressedColor = new Color(0, 150, 0, 255);\n        button.hoverColor = new Color(50, 220, 50, 255);\n\n        // 按钮文字\n        const labelNode = new Node('Label');\n        labelNode.setParent(buttonNode);\n\n        const labelTransform = labelNode.addComponent(UITransform);\n        labelTransform.setContentSize(200, 50);\n        labelTransform.setAnchorPoint(0.5, 0.5);\n        labelNode.setPosition(0, 0, 0);\n\n        const label = labelNode.addComponent(Label);\n        label.string = '切换到登录界面';\n        label.fontSize = 18;\n        label.color = new Color(255, 255, 255, 255);\n\n        // 绑定点击事件\n        button.node.on(Button.EventType.CLICK, this.createActualLoginUI, this);\n\n        console.log('[SimpleLoginTest] 切换按钮添加完成');\n    }\n\n    /**\n     * 创建实际的登录UI（替代测试UI）\n     */\n    public createActualLoginUI(): void {\n        console.log('[SimpleLoginTest] 开始创建实际登录UI');\n\n        // 清除当前的测试UI\n        this.node.removeAllChildren();\n\n        // 创建登录UI节点\n        const loginUINode = new Node('LoginUI');\n        loginUINode.setParent(this.node);\n\n        // 添加SimpleLoginUI组件\n        const loginUI = loginUINode.addComponent(SimpleLoginUI);\n\n        console.log('[SimpleLoginTest] 实际登录UI创建完成');\n    }\n}\n"]}