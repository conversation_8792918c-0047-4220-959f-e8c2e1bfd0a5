{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts"], "names": ["HuftList", "next", "list", "HuftNode", "e", "b", "n", "t", "HuftBuild", "s", "d", "mm", "BMAX", "N_MAX", "status", "root", "m", "a", "c", "el", "f", "g", "h", "i", "j", "k", "lx", "p", "pidx", "q", "r", "u", "v", "w", "x", "xp", "y", "z", "o", "tail", "GET_BYTE", "inflate_data", "length", "inflate_pos", "NEEDBITS", "bit_len", "bit_buf", "GETBITS", "MASK_BITS", "DUMPBITS", "inflate_codes", "buff", "off", "size", "bl", "tl", "wp", "WSIZE", "slide", "copy_leng", "bd", "td", "copy_dist", "method", "inflate_stored", "inflate_fixed", "fixed_tl", "l", "fixed_bl", "cplens", "cplext", "console", "error", "fixed_bd", "cpdist", "cpdext", "fixed_td", "inflate_dynamic", "nb", "nl", "nd", "ll", "border", "lbits", "dbits", "inflate_start", "eof", "inflate_internal", "STORED_BLOCK", "STATIC_TREES", "DYN_TREES", "inflate", "arr"], "mappings": ";;;;;AAkFA;AAEA,WAASA,QAAT,GAAoB;AACnB,SAAKC,IAAL,GAAY,IAAZ;AACA,SAAKC,IAAL,GAAY,IAAZ;AACA;;AAED,WAASC,QAAT,GAAoB;AACnB,SAAKC,CAAL,GAAS,CAAT,CADmB,CACP;;AACZ,SAAKC,CAAL,GAAS,CAAT,CAFmB,CAEP;AAEZ;;AACA,SAAKC,CAAL,GAAS,CAAT,CALmB,CAKP;;AACZ,SAAKC,CAAL,GAAS,IAAT,CANmB,CAMJ;AACf;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASC,SAAT,CAAmBH,CAAnB,EAAsBC,CAAtB,EAAyBG,CAAzB,EAA4BC,CAA5B,EAA+BN,CAA/B,EAAkCO,EAAlC,EAAsC;AACrC,SAAKC,IAAL,GAAY,EAAZ,CADqC,CACrB;;AAChB,SAAKC,KAAL,GAAa,GAAb,CAFqC,CAEnB;;AAClB,SAAKC,MAAL,GAAc,CAAd,CAHqC,CAGpB;;AACjB,SAAKC,IAAL,GAAY,IAAZ,CAJqC,CAInB;;AAClB,SAAKC,CAAL,GAAS,CAAT,CALqC,CAKzB;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACC,QAAIC,CAAJ,CAfqC,CAe9B;;AACP,QAAIC,CAAC,GAAG,EAAR;AACA,QAAIC,EAAJ,CAjBqC,CAiB7B;;AACR,QAAIC,CAAJ,CAlBqC,CAkB9B;;AACP,QAAIC,CAAJ,CAnBqC,CAmB9B;;AACP,QAAIC,CAAJ,CApBqC,CAoB9B;;AACP,QAAIC,CAAJ,CArBqC,CAqB9B;;AACP,QAAIC,CAAJ,CAtBqC,CAsB9B;;AACP,QAAIC,CAAJ,CAvBqC,CAuB9B;;AACP,QAAIC,EAAE,GAAG,EAAT;AACA,QAAIC,CAAJ,CAzBqC,CAyB9B;;AACP,QAAIC,IAAJ,CA1BqC,CA0B3B;;AACV,QAAIC,CAAJ,CA3BqC,CA2B9B;;AACP,QAAIC,CAAC,GAAG,IAAI3B,QAAJ,EAAR,CA5BqC,CA4Bb;;AACxB,QAAI4B,CAAC,GAAG,EAAR;AACA,QAAIC,CAAC,GAAG,EAAR;AACA,QAAIC,CAAJ;AACA,QAAIC,CAAC,GAAG,EAAR;AACA,QAAIC,EAAJ,CAjCqC,CAiC7B;;AACR,QAAIC,CAAJ,CAlCqC,CAkC9B;;AACP,QAAIC,CAAJ,CAnCqC,CAmC9B;;AACP,QAAIC,CAAJ;AACA,QAAIC,IAAJ,CArCqC,CAqC3B;;AAEVA,IAAAA,IAAI,GAAG,KAAKxB,IAAL,GAAY,IAAnB,CAvCqC,CAyCrC;;AACA,SAAKQ,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKX,IAAL,GAAY,CAA5B,EAA+BW,CAAC,EAAhC,EAAoC;AACnCL,MAAAA,CAAC,CAACK,CAAD,CAAD,GAAO,CAAP;AACA,KA5CoC,CA6CrC;;;AACA,SAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKX,IAAL,GAAY,CAA5B,EAA+BW,CAAC,EAAhC,EAAoC;AACnCG,MAAAA,EAAE,CAACH,CAAD,CAAF,GAAQ,CAAR;AACA,KAhDoC,CAiDrC;;;AACA,SAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKX,IAArB,EAA2BW,CAAC,EAA5B,EAAgC;AAC/BQ,MAAAA,CAAC,CAACR,CAAD,CAAD,GAAO,IAAP;AACA,KApDoC,CAqDrC;;;AACA,SAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKV,KAArB,EAA4BU,CAAC,EAA7B,EAAiC;AAChCS,MAAAA,CAAC,CAACT,CAAD,CAAD,GAAO,CAAP;AACA,KAxDoC,CAyDrC;;;AACA,SAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKX,IAAL,GAAY,CAA5B,EAA+BW,CAAC,EAAhC,EAAoC;AACnCW,MAAAA,CAAC,CAACX,CAAD,CAAD,GAAO,CAAP;AACA,KA5DoC,CA8DrC;;;AACAJ,IAAAA,EAAE,GAAGb,CAAC,GAAG,GAAJ,GAAUD,CAAC,CAAC,GAAD,CAAX,GAAmB,KAAKO,IAA7B,CA/DqC,CA+DF;;AACnCe,IAAAA,CAAC,GAAGtB,CAAJ;AAAOuB,IAAAA,IAAI,GAAG,CAAP;AACPL,IAAAA,CAAC,GAAGjB,CAAJ;;AACA,OAAG;AACFY,MAAAA,CAAC,CAACS,CAAC,CAACC,IAAD,CAAF,CAAD,GADE,CACY;;AACdA,MAAAA,IAAI;AACJ,KAHD,QAGS,EAAEL,CAAF,GAAM,CAHf;;AAIA,QAAIL,CAAC,CAAC,CAAD,CAAD,KAASZ,CAAb,EAAgB;AAAE;AACjB,WAAKS,IAAL,GAAY,IAAZ;AACA,WAAKC,CAAL,GAAS,CAAT;AACA,WAAKF,MAAL,GAAc,CAAd;AACA;AACA,KA3EoC,CA6ErC;;;AACA,SAAKU,CAAC,GAAG,CAAT,EAAYA,CAAC,IAAI,KAAKZ,IAAtB,EAA4BY,CAAC,EAA7B,EAAiC;AAChC,UAAIN,CAAC,CAACM,CAAD,CAAD,KAAS,CAAb,EAAgB;AACf;AACA;AACD;;AACDC,IAAAA,CAAC,GAAGD,CAAJ,CAnFqC,CAmF9B;;AACP,QAAIb,EAAE,GAAGa,CAAT,EAAY;AACXb,MAAAA,EAAE,GAAGa,CAAL;AACA;;AACD,SAAKD,CAAC,GAAG,KAAKX,IAAd,EAAoBW,CAAC,KAAK,CAA1B,EAA6BA,CAAC,EAA9B,EAAkC;AACjC,UAAIL,CAAC,CAACK,CAAD,CAAD,KAAS,CAAb,EAAgB;AACf;AACA;AACD;;AACDF,IAAAA,CAAC,GAAGE,CAAJ,CA5FqC,CA4F9B;;AACP,QAAIZ,EAAE,GAAGY,CAAT,EAAY;AACXZ,MAAAA,EAAE,GAAGY,CAAL;AACA,KA/FoC,CAiGrC;;;AACA,SAAKa,CAAC,GAAG,KAAKZ,CAAd,EAAiBA,CAAC,GAAGD,CAArB,EAAwBC,CAAC,IAAIY,CAAC,KAAK,CAAnC,EAAsC;AACrC,UAAI,CAACA,CAAC,IAAIlB,CAAC,CAACM,CAAD,CAAP,IAAc,CAAlB,EAAqB;AACpB,aAAKV,MAAL,GAAc,CAAd,CADoB,CACH;;AACjB,aAAKE,CAAL,GAASL,EAAT;AACA;AACA;AACD;;AACD,QAAI,CAACyB,CAAC,IAAIlB,CAAC,CAACK,CAAD,CAAP,IAAc,CAAlB,EAAqB;AACpB,WAAKT,MAAL,GAAc,CAAd;AACA,WAAKE,CAAL,GAASL,EAAT;AACA;AACA;;AACDO,IAAAA,CAAC,CAACK,CAAD,CAAD,IAAQa,CAAR,CA9GqC,CAgHrC;;AACAF,IAAAA,CAAC,CAAC,CAAD,CAAD,GAAOV,CAAC,GAAG,CAAX;AACAG,IAAAA,CAAC,GAAGT,CAAJ;AACAU,IAAAA,IAAI,GAAG,CAAP;AACAO,IAAAA,EAAE,GAAG,CAAL;;AACA,WAAO,EAAEZ,CAAF,GAAM,CAAb,EAAgB;AAAE;AACjBW,MAAAA,CAAC,CAACC,EAAE,EAAH,CAAD,GAAWX,CAAC,IAAIG,CAAC,CAACC,IAAI,EAAL,CAAjB;AACA,KAvHoC,CAyHrC;;;AACAD,IAAAA,CAAC,GAAGtB,CAAJ;AAAOuB,IAAAA,IAAI,GAAG,CAAP;AACPL,IAAAA,CAAC,GAAG,CAAJ;;AACA,OAAG;AACF,UAAI,CAACC,CAAC,GAAGG,CAAC,CAACC,IAAI,EAAL,CAAN,MAAoB,CAAxB,EAA2B;AAC1BI,QAAAA,CAAC,CAACE,CAAC,CAACV,CAAD,CAAD,EAAD,CAAD,GAAYD,CAAZ;AACA;AACD,KAJD,QAIS,EAAEA,CAAF,GAAMjB,CAJf;;AAKAA,IAAAA,CAAC,GAAG4B,CAAC,CAACb,CAAD,CAAL,CAjIqC,CAiI3B;AAEV;;AACAa,IAAAA,CAAC,CAAC,CAAD,CAAD,GAAOX,CAAC,GAAG,CAAX,CApIqC,CAoIvB;;AACdI,IAAAA,CAAC,GAAGK,CAAJ;AAAOJ,IAAAA,IAAI,GAAG,CAAP,CArI8B,CAqIpB;;AACjBN,IAAAA,CAAC,GAAG,CAAC,CAAL,CAtIqC,CAsI7B;;AACRW,IAAAA,CAAC,GAAGP,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAZ,CAvIqC,CAuItB;;AACfG,IAAAA,CAAC,GAAG,IAAJ,CAxIqC,CAwI3B;;AACVQ,IAAAA,CAAC,GAAG,CAAJ,CAzIqC,CAyI9B;AAEP;;AACA,SAAK,IAAL,EAAWZ,CAAC,IAAIJ,CAAhB,EAAmBI,CAAC,EAApB,EAAwB;AACvBR,MAAAA,CAAC,GAAGC,CAAC,CAACO,CAAD,CAAL;;AACA,aAAOR,CAAC,KAAK,CAAb,EAAgB;AACf;AACA;AACA,eAAOQ,CAAC,GAAGQ,CAAC,GAAGP,EAAE,CAAC,IAAIJ,CAAL,CAAjB,EAA0B;AACzBW,UAAAA,CAAC,IAAIP,EAAE,CAAC,IAAIJ,CAAL,CAAP,CADyB,CACT;;AAChBA,UAAAA,CAAC,GAFwB,CAIzB;;AACAe,UAAAA,CAAC,GAAG,CAACA,CAAC,GAAGhB,CAAC,GAAGY,CAAT,IAActB,EAAd,GAAmBA,EAAnB,GAAwB0B,CAA5B,CALyB,CAKM;;AAC/B,cAAI,CAACjB,CAAC,GAAG,MAAMI,CAAC,GAAGC,CAAC,GAAGQ,CAAd,CAAL,IAAyBhB,CAAC,GAAG,CAAjC,EAAoC;AAAE;AACrC;AACAG,YAAAA,CAAC,IAAIH,CAAC,GAAG,CAAT,CAFmC,CAEvB;;AACZkB,YAAAA,EAAE,GAAGV,CAAL;;AACA,mBAAO,EAAED,CAAF,GAAMa,CAAb,EAAgB;AAAE;AACjB,kBAAI,CAACjB,CAAC,KAAK,CAAP,KAAaF,CAAC,CAAC,EAAEiB,EAAH,CAAlB,EAA0B;AACzB,sBADyB,CAClB;AACP;;AACDf,cAAAA,CAAC,IAAIF,CAAC,CAACiB,EAAD,CAAN,CAJe,CAIH;AACZ;AACD;;AACD,cAAIF,CAAC,GAAGT,CAAJ,GAAQL,EAAR,IAAcc,CAAC,GAAGd,EAAtB,EAA0B;AACzBK,YAAAA,CAAC,GAAGL,EAAE,GAAGc,CAAT,CADyB,CACb;AACZ;;AACDI,UAAAA,CAAC,GAAG,KAAKb,CAAT,CApByB,CAoBb;;AACZE,UAAAA,EAAE,CAAC,IAAIJ,CAAL,CAAF,GAAYE,CAAZ,CArByB,CAqBV;AAEf;;AACAK,UAAAA,CAAC,GAAG,EAAJ;;AACA,eAAKS,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGD,CAAhB,EAAmBC,CAAC,EAApB,EAAwB;AACvBT,YAAAA,CAAC,CAACS,CAAD,CAAD,GAAO,IAAInC,QAAJ,EAAP;AACA;;AAED,cAAI,CAACoC,IAAL,EAAW;AACVA,YAAAA,IAAI,GAAG,KAAKxB,IAAL,GAAY,IAAIf,QAAJ,EAAnB;AACA,WAFD,MAEO;AACNuC,YAAAA,IAAI,GAAGA,IAAI,CAACtC,IAAL,GAAY,IAAID,QAAJ,EAAnB;AACA;;AACDuC,UAAAA,IAAI,CAACtC,IAAL,GAAY,IAAZ;AACAsC,UAAAA,IAAI,CAACrC,IAAL,GAAY2B,CAAZ;AACAE,UAAAA,CAAC,CAACT,CAAD,CAAD,GAAOO,CAAP,CApCyB,CAoCf;;AAEV;;AACA,cAAIP,CAAC,GAAG,CAAR,EAAW;AACVY,YAAAA,CAAC,CAACZ,CAAD,CAAD,GAAOC,CAAP,CADU,CACA;;AACVO,YAAAA,CAAC,CAACzB,CAAF,GAAMqB,EAAE,CAACJ,CAAD,CAAR,CAFU,CAEG;;AACbQ,YAAAA,CAAC,CAAC1B,CAAF,GAAM,KAAKoB,CAAX,CAHU,CAGI;;AACdM,YAAAA,CAAC,CAACvB,CAAF,GAAMsB,CAAN,CAJU,CAID;;AACTL,YAAAA,CAAC,GAAG,CAACD,CAAC,GAAI,CAAC,KAAKU,CAAN,IAAW,CAAjB,KAAyBA,CAAC,GAAGP,EAAE,CAACJ,CAAD,CAAnC;AACAS,YAAAA,CAAC,CAACT,CAAC,GAAG,CAAL,CAAD,CAASE,CAAT,EAAYpB,CAAZ,GAAgB0B,CAAC,CAAC1B,CAAlB;AACA2B,YAAAA,CAAC,CAACT,CAAC,GAAG,CAAL,CAAD,CAASE,CAAT,EAAYnB,CAAZ,GAAgByB,CAAC,CAACzB,CAAlB;AACA0B,YAAAA,CAAC,CAACT,CAAC,GAAG,CAAL,CAAD,CAASE,CAAT,EAAYlB,CAAZ,GAAgBwB,CAAC,CAACxB,CAAlB;AACAyB,YAAAA,CAAC,CAACT,CAAC,GAAG,CAAL,CAAD,CAASE,CAAT,EAAYjB,CAAZ,GAAgBuB,CAAC,CAACvB,CAAlB;AACA;AACD,SArDc,CAuDf;;;AACAuB,QAAAA,CAAC,CAACzB,CAAF,GAAMoB,CAAC,GAAGQ,CAAV;;AACA,YAAIL,IAAI,IAAItB,CAAZ,EAAe;AACdwB,UAAAA,CAAC,CAAC1B,CAAF,GAAM,EAAN,CADc,CACJ;AACV,SAFD,MAEO,IAAIuB,CAAC,CAACC,IAAD,CAAD,GAAUnB,CAAd,EAAiB;AACvBqB,UAAAA,CAAC,CAAC1B,CAAF,GAAOuB,CAAC,CAACC,IAAD,CAAD,GAAU,GAAV,GAAgB,EAAhB,GAAqB,EAA5B,CADuB,CACU;;AACjCE,UAAAA,CAAC,CAACxB,CAAF,GAAMqB,CAAC,CAACC,IAAI,EAAL,CAAP,CAFuB,CAEN;AACjB,SAHM,MAGA;AACNE,UAAAA,CAAC,CAAC1B,CAAF,GAAMA,CAAC,CAACuB,CAAC,CAACC,IAAD,CAAD,GAAUnB,CAAX,CAAP,CADM,CACgB;;AACtBqB,UAAAA,CAAC,CAACxB,CAAF,GAAMI,CAAC,CAACiB,CAAC,CAACC,IAAI,EAAL,CAAD,GAAYnB,CAAb,CAAP;AACA,SAjEc,CAmEf;;;AACAW,QAAAA,CAAC,GAAG,KAAMK,CAAC,GAAGQ,CAAd;;AACA,aAAKT,CAAC,GAAGD,CAAC,IAAIU,CAAd,EAAiBT,CAAC,GAAGa,CAArB,EAAwBb,CAAC,IAAIJ,CAA7B,EAAgC;AAC/BS,UAAAA,CAAC,CAACL,CAAD,CAAD,CAAKpB,CAAL,GAAS0B,CAAC,CAAC1B,CAAX;AACAyB,UAAAA,CAAC,CAACL,CAAD,CAAD,CAAKnB,CAAL,GAASyB,CAAC,CAACzB,CAAX;AACAwB,UAAAA,CAAC,CAACL,CAAD,CAAD,CAAKlB,CAAL,GAASwB,CAAC,CAACxB,CAAX;AACAuB,UAAAA,CAAC,CAACL,CAAD,CAAD,CAAKjB,CAAL,GAASuB,CAAC,CAACvB,CAAX;AACA,SA1Ec,CA4Ef;;;AACA,aAAKiB,CAAC,GAAG,KAAMC,CAAC,GAAG,CAAnB,EAAuB,CAACF,CAAC,GAAGC,CAAL,MAAY,CAAnC,EAAsCA,CAAC,KAAK,CAA5C,EAA+C;AAC9CD,UAAAA,CAAC,IAAIC,CAAL;AACA;;AACDD,QAAAA,CAAC,IAAIC,CAAL,CAhFe,CAkFf;;AACA,eAAO,CAACD,CAAC,GAAI,CAAC,KAAKU,CAAN,IAAW,CAAjB,MAAyBC,CAAC,CAACZ,CAAD,CAAjC,EAAsC;AACrCW,UAAAA,CAAC,IAAIP,EAAE,CAACJ,CAAD,CAAP,CADqC,CACzB;;AACZA,UAAAA,CAAC;AACD;AACD;AACD;AAED;;;AACA,SAAKN,CAAL,GAASU,EAAE,CAAC,CAAD,CAAX;AAEA;;AACA,SAAKZ,MAAL,GAAgBsB,CAAC,KAAK,CAAN,IAAWf,CAAC,KAAK,CAAlB,GAAuB,CAAvB,GAA2B,CAA1C;AACA;AAGD;;;AAEA,WAASmB,QAAT,GAAoB;AACnB,QAAIC,YAAY,CAACC,MAAb,KAAwBC,WAA5B,EAAyC;AACxC,aAAO,CAAC,CAAR;AACA;;AACD,WAAOF,YAAY,CAACE,WAAW,EAAZ,CAAZ,GAA8B,IAArC;AACA;;AAED,WAASC,QAAT,CAAkBtC,CAAlB,EAAqB;AACpB,WAAOuC,OAAO,GAAGvC,CAAjB,EAAoB;AACnBwC,MAAAA,OAAO,IAAIN,QAAQ,MAAMK,OAAzB;AACAA,MAAAA,OAAO,IAAI,CAAX;AACA;AACD;;AAED,WAASE,OAAT,CAAiBzC,CAAjB,EAAoB;AACnB,WAAOwC,OAAO,GAAGE,SAAS,CAAC1C,CAAD,CAA1B;AACA;;AAED,WAAS2C,QAAT,CAAkB3C,CAAlB,EAAqB;AACpBwC,IAAAA,OAAO,KAAKxC,CAAZ;AACAuC,IAAAA,OAAO,IAAIvC,CAAX;AACA;;AAED,WAAS4C,aAAT,CAAuBC,IAAvB,EAA6BC,GAA7B,EAAkCC,IAAlC,EAAwC;AACvC;AACA;AACA,QAAIjD,CAAJ,CAHuC,CAGhC;;AACP,QAAIG,CAAJ,CAJuC,CAIhC;;AACP,QAAID,CAAJ;;AAEA,QAAI+C,IAAI,KAAK,CAAb,EAAgB;AACf,aAAO,CAAP;AACA,KATsC,CAWvC;;;AACA/C,IAAAA,CAAC,GAAG,CAAJ;;AACA,aAAS;AAAE;AACVsC,MAAAA,QAAQ,CAACU,EAAD,CAAR;AACA/C,MAAAA,CAAC,GAAGgD,EAAE,CAACrD,IAAH,CAAQ6C,OAAO,CAACO,EAAD,CAAf,CAAJ;AACAlD,MAAAA,CAAC,GAAGG,CAAC,CAACH,CAAN;;AACA,aAAOA,CAAC,GAAG,EAAX,EAAe;AACd,YAAIA,CAAC,KAAK,EAAV,EAAc;AACb,iBAAO,CAAC,CAAR;AACA;;AACD6C,QAAAA,QAAQ,CAAC1C,CAAC,CAACF,CAAH,CAAR;AACAD,QAAAA,CAAC,IAAI,EAAL;AACAwC,QAAAA,QAAQ,CAACxC,CAAD,CAAR;AACAG,QAAAA,CAAC,GAAGA,CAAC,CAACA,CAAF,CAAIwC,OAAO,CAAC3C,CAAD,CAAX,CAAJ;AACAA,QAAAA,CAAC,GAAGG,CAAC,CAACH,CAAN;AACA;;AACD6C,MAAAA,QAAQ,CAAC1C,CAAC,CAACF,CAAH,CAAR;;AAEA,UAAID,CAAC,KAAK,EAAV,EAAc;AAAE;AACfoD,QAAAA,EAAE,IAAIC,KAAK,GAAG,CAAd;AACAN,QAAAA,IAAI,CAACC,GAAG,GAAG9C,CAAC,EAAR,CAAJ,GAAkBoD,KAAK,CAACF,EAAE,EAAH,CAAL,GAAcjD,CAAC,CAACD,CAAlC;;AACA,YAAIA,CAAC,KAAK+C,IAAV,EAAgB;AACf,iBAAOA,IAAP;AACA;;AACD;AACA,OAvBO,CAyBR;;;AACA,UAAIjD,CAAC,KAAK,EAAV,EAAc;AACb;AACA,OA5BO,CA8BR;AAEA;;;AACAwC,MAAAA,QAAQ,CAACxC,CAAD,CAAR;AACAuD,MAAAA,SAAS,GAAGpD,CAAC,CAACD,CAAF,GAAMyC,OAAO,CAAC3C,CAAD,CAAzB;AACA6C,MAAAA,QAAQ,CAAC7C,CAAD,CAAR,CAnCQ,CAqCR;;AACAwC,MAAAA,QAAQ,CAACgB,EAAD,CAAR;AACArD,MAAAA,CAAC,GAAGsD,EAAE,CAAC3D,IAAH,CAAQ6C,OAAO,CAACa,EAAD,CAAf,CAAJ;AACAxD,MAAAA,CAAC,GAAGG,CAAC,CAACH,CAAN;;AAEA,aAAOA,CAAC,GAAG,EAAX,EAAe;AACd,YAAIA,CAAC,KAAK,EAAV,EAAc;AACb,iBAAO,CAAC,CAAR;AACA;;AACD6C,QAAAA,QAAQ,CAAC1C,CAAC,CAACF,CAAH,CAAR;AACAD,QAAAA,CAAC,IAAI,EAAL;AACAwC,QAAAA,QAAQ,CAACxC,CAAD,CAAR;AACAG,QAAAA,CAAC,GAAGA,CAAC,CAACA,CAAF,CAAIwC,OAAO,CAAC3C,CAAD,CAAX,CAAJ;AACAA,QAAAA,CAAC,GAAGG,CAAC,CAACH,CAAN;AACA;;AACD6C,MAAAA,QAAQ,CAAC1C,CAAC,CAACF,CAAH,CAAR;AACAuC,MAAAA,QAAQ,CAACxC,CAAD,CAAR;AACA0D,MAAAA,SAAS,GAAGN,EAAE,GAAGjD,CAAC,CAACD,CAAP,GAAWyC,OAAO,CAAC3C,CAAD,CAA9B;AACA6C,MAAAA,QAAQ,CAAC7C,CAAD,CAAR,CAvDQ,CAyDR;;AACA,aAAOuD,SAAS,GAAG,CAAZ,IAAiBrD,CAAC,GAAG+C,IAA5B,EAAkC;AACjCM,QAAAA,SAAS;AACTG,QAAAA,SAAS,IAAIL,KAAK,GAAG,CAArB;AACAD,QAAAA,EAAE,IAAIC,KAAK,GAAG,CAAd;AACAN,QAAAA,IAAI,CAACC,GAAG,GAAG9C,CAAC,EAAR,CAAJ,GAAkBoD,KAAK,CAACF,EAAE,EAAH,CAAL,GAAcE,KAAK,CAACI,SAAS,EAAV,CAArC;AACA;;AAED,UAAIxD,CAAC,KAAK+C,IAAV,EAAgB;AACf,eAAOA,IAAP;AACA;AACD;;AAEDU,IAAAA,MAAM,GAAG,CAAC,CAAV,CAnFuC,CAmF1B;;AACb,WAAOzD,CAAP;AACA;;AAED,WAAS0D,cAAT,CAAwBb,IAAxB,EAA8BC,GAA9B,EAAmCC,IAAnC,EAAyC;AACxC;AACA,QAAI/C,CAAJ,CAFwC,CAIxC;;AACAA,IAAAA,CAAC,GAAGuC,OAAO,GAAG,CAAd;AACAI,IAAAA,QAAQ,CAAC3C,CAAD,CAAR,CANwC,CAQxC;;AACAsC,IAAAA,QAAQ,CAAC,EAAD,CAAR;AACAtC,IAAAA,CAAC,GAAGyC,OAAO,CAAC,EAAD,CAAX;AACAE,IAAAA,QAAQ,CAAC,EAAD,CAAR;AACAL,IAAAA,QAAQ,CAAC,EAAD,CAAR;;AACA,QAAItC,CAAC,MAAO,CAACwC,OAAF,GAAa,MAAnB,CAAL,EAAiC;AAChC,aAAO,CAAC,CAAR,CADgC,CACrB;AACX;;AACDG,IAAAA,QAAQ,CAAC,EAAD,CAAR,CAhBwC,CAkBxC;;AACAU,IAAAA,SAAS,GAAGrD,CAAZ;AAEAA,IAAAA,CAAC,GAAG,CAAJ;;AACA,WAAOqD,SAAS,GAAG,CAAZ,IAAiBrD,CAAC,GAAG+C,IAA5B,EAAkC;AACjCM,MAAAA,SAAS;AACTH,MAAAA,EAAE,IAAIC,KAAK,GAAG,CAAd;AACAb,MAAAA,QAAQ,CAAC,CAAD,CAAR;AACAO,MAAAA,IAAI,CAACC,GAAG,GAAG9C,CAAC,EAAR,CAAJ,GAAkBoD,KAAK,CAACF,EAAE,EAAH,CAAL,GAAcT,OAAO,CAAC,CAAD,CAAvC;AACAE,MAAAA,QAAQ,CAAC,CAAD,CAAR;AACA;;AAED,QAAIU,SAAS,KAAK,CAAlB,EAAqB;AACpBI,MAAAA,MAAM,GAAG,CAAC,CAAV,CADoB,CACP;AACb;;AACD,WAAOzD,CAAP;AACA;;AAED,WAAS2D,aAAT,CAAuBd,IAAvB,EAA6BC,GAA7B,EAAkCC,IAAlC,EAAwC;AACvC;AACA;AACA;AAEA;AACA,QAAI,CAACa,QAAL,EAAe;AACd,UAAI3C,CAAJ,CADc,CACP;;AACP,UAAI4C,CAAC,GAAG,EAAR,CAFc,CAEF;;AACZ,UAAI7C,CAAJ,CAHc,CAGP;AAEP;;AACA,WAAKC,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,GAAhB,EAAqBA,CAAC,EAAtB,EAA0B;AACzB4C,QAAAA,CAAC,CAAC5C,CAAD,CAAD,GAAO,CAAP;AACA;;AACD,WAAK,IAAL,EAAWA,CAAC,GAAG,GAAf,EAAoBA,CAAC,EAArB,EAAyB;AACxB4C,QAAAA,CAAC,CAAC5C,CAAD,CAAD,GAAO,CAAP;AACA;;AACD,WAAK,IAAL,EAAWA,CAAC,GAAG,GAAf,EAAoBA,CAAC,EAArB,EAAyB;AACxB4C,QAAAA,CAAC,CAAC5C,CAAD,CAAD,GAAO,CAAP;AACA;;AACD,WAAK,IAAL,EAAWA,CAAC,GAAG,GAAf,EAAoBA,CAAC,EAArB,EAAyB;AAAE;AAC1B4C,QAAAA,CAAC,CAAC5C,CAAD,CAAD,GAAO,CAAP;AACA;;AACD6C,MAAAA,QAAQ,GAAG,CAAX;AAEA9C,MAAAA,CAAC,GAAG,IAAId,SAAJ,CAAc2D,CAAd,EAAiB,GAAjB,EAAsB,GAAtB,EAA2BE,MAA3B,EAAmCC,MAAnC,EAA2CF,QAA3C,CAAJ;;AACA,UAAI9C,CAAC,CAACR,MAAF,KAAa,CAAjB,EAAoB;AACnByD,QAAAA,OAAO,CAACC,KAAR,CAAc,qBAAqBlD,CAAC,CAACR,MAArC;AACA,eAAO,CAAC,CAAR;AACA;;AACDoD,MAAAA,QAAQ,GAAG5C,CAAC,CAACP,IAAb;AACAqD,MAAAA,QAAQ,GAAG9C,CAAC,CAACN,CAAb,CA1Bc,CA4Bd;;AACA,WAAKO,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,EAAhB,EAAoBA,CAAC,EAArB,EAAyB;AAAE;AAC1B4C,QAAAA,CAAC,CAAC5C,CAAD,CAAD,GAAO,CAAP;AACA;;AACDkD,MAAAA,QAAQ,GAAG,CAAX;AAEAnD,MAAAA,CAAC,GAAG,IAAId,SAAJ,CAAc2D,CAAd,EAAiB,EAAjB,EAAqB,CAArB,EAAwBO,MAAxB,EAAgCC,MAAhC,EAAwCF,QAAxC,CAAJ;;AACA,UAAInD,CAAC,CAACR,MAAF,GAAW,CAAf,EAAkB;AACjBoD,QAAAA,QAAQ,GAAG,IAAX;AACAK,QAAAA,OAAO,CAACC,KAAR,CAAc,qBAAqBlD,CAAC,CAACR,MAArC;AACA,eAAO,CAAC,CAAR;AACA;;AACD8D,MAAAA,QAAQ,GAAGtD,CAAC,CAACP,IAAb;AACA0D,MAAAA,QAAQ,GAAGnD,CAAC,CAACN,CAAb;AACA;;AAEDuC,IAAAA,EAAE,GAAGW,QAAL;AACAL,IAAAA,EAAE,GAAGe,QAAL;AACAtB,IAAAA,EAAE,GAAGc,QAAL;AACAR,IAAAA,EAAE,GAAGa,QAAL;AACA,WAAOvB,aAAa,CAACC,IAAD,EAAOC,GAAP,EAAYC,IAAZ,CAApB;AACA;;AAED,WAASwB,eAAT,CAAyB1B,IAAzB,EAA+BC,GAA/B,EAAoCC,IAApC,EAA0C;AACzC;AACA,QAAI9B,CAAJ,CAFyC,CAElC;;AACP,QAAIC,CAAJ;AACA,QAAI2C,CAAJ,CAJyC,CAIlC;;AACP,QAAI7D,CAAJ,CALyC,CAKlC;;AACP,QAAIC,CAAJ,CANyC,CAMlC;;AACP,QAAIuE,EAAJ,CAPyC,CAOjC;;AACR,QAAIC,EAAJ,CARyC,CAQjC;;AACR,QAAIC,EAAJ,CATyC,CASjC;;AACR,QAAIC,EAAE,GAAG,EAAT;AACA,QAAI3D,CAAJ,CAXyC,CAWlC;AAEP;;AACA,SAAKC,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,MAAM,EAAtB,EAA0BA,CAAC,EAA3B,EAA+B;AAC9B0D,MAAAA,EAAE,CAAC1D,CAAD,CAAF,GAAQ,CAAR;AACA,KAhBwC,CAkBzC;;;AACAqB,IAAAA,QAAQ,CAAC,CAAD,CAAR;AACAmC,IAAAA,EAAE,GAAG,MAAMhC,OAAO,CAAC,CAAD,CAAlB,CApByC,CAoBlB;;AACvBE,IAAAA,QAAQ,CAAC,CAAD,CAAR;AACAL,IAAAA,QAAQ,CAAC,CAAD,CAAR;AACAoC,IAAAA,EAAE,GAAG,IAAIjC,OAAO,CAAC,CAAD,CAAhB,CAvByC,CAuBpB;;AACrBE,IAAAA,QAAQ,CAAC,CAAD,CAAR;AACAL,IAAAA,QAAQ,CAAC,CAAD,CAAR;AACAkC,IAAAA,EAAE,GAAG,IAAI/B,OAAO,CAAC,CAAD,CAAhB,CA1ByC,CA0BpB;;AACrBE,IAAAA,QAAQ,CAAC,CAAD,CAAR;;AACA,QAAI8B,EAAE,GAAG,GAAL,IAAYC,EAAE,GAAG,EAArB,EAAyB;AACxB,aAAO,CAAC,CAAR,CADwB,CACb;AACX,KA9BwC,CAgCzC;;;AACA,SAAKxD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGsD,EAAhB,EAAoBtD,CAAC,EAArB,EAAyB;AACxBoB,MAAAA,QAAQ,CAAC,CAAD,CAAR;AACAqC,MAAAA,EAAE,CAACC,MAAM,CAAC1D,CAAD,CAAP,CAAF,GAAgBuB,OAAO,CAAC,CAAD,CAAvB;AACAE,MAAAA,QAAQ,CAAC,CAAD,CAAR;AACA;;AACD,SAAK,IAAL,EAAWzB,CAAC,GAAG,EAAf,EAAmBA,CAAC,EAApB,EAAwB;AACvByD,MAAAA,EAAE,CAACC,MAAM,CAAC1D,CAAD,CAAP,CAAF,GAAgB,CAAhB;AACA,KAxCwC,CA0CzC;;;AACA8B,IAAAA,EAAE,GAAG,CAAL;AACAhC,IAAAA,CAAC,GAAG,IAAId,SAAJ,CAAcyE,EAAd,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,IAA1B,EAAgC,IAAhC,EAAsC3B,EAAtC,CAAJ;;AACA,QAAIhC,CAAC,CAACR,MAAF,KAAa,CAAjB,EAAoB;AACnB,aAAO,CAAC,CAAR,CADmB,CACR;AACX;;AAEDyC,IAAAA,EAAE,GAAGjC,CAAC,CAACP,IAAP;AACAuC,IAAAA,EAAE,GAAGhC,CAAC,CAACN,CAAP,CAlDyC,CAoDzC;;AACAV,IAAAA,CAAC,GAAGyE,EAAE,GAAGC,EAAT;AACAzD,IAAAA,CAAC,GAAG4C,CAAC,GAAG,CAAR;;AACA,WAAO5C,CAAC,GAAGjB,CAAX,EAAc;AACbsC,MAAAA,QAAQ,CAACU,EAAD,CAAR;AACA/C,MAAAA,CAAC,GAAGgD,EAAE,CAACrD,IAAH,CAAQ6C,OAAO,CAACO,EAAD,CAAf,CAAJ;AACA9B,MAAAA,CAAC,GAAGjB,CAAC,CAACF,CAAN;AACA4C,MAAAA,QAAQ,CAACzB,CAAD,CAAR;AACAA,MAAAA,CAAC,GAAGjB,CAAC,CAACD,CAAN;;AACA,UAAIkB,CAAC,GAAG,EAAR,EAAY;AAAE;AACbyD,QAAAA,EAAE,CAAC1D,CAAC,EAAF,CAAF,GAAU4C,CAAC,GAAG3C,CAAd,CADW,CACM;AACjB,OAFD,MAEO,IAAIA,CAAC,KAAK,EAAV,EAAc;AAAE;AACtBoB,QAAAA,QAAQ,CAAC,CAAD,CAAR;AACApB,QAAAA,CAAC,GAAG,IAAIuB,OAAO,CAAC,CAAD,CAAf;AACAE,QAAAA,QAAQ,CAAC,CAAD,CAAR;;AACA,YAAI1B,CAAC,GAAGC,CAAJ,GAAQlB,CAAZ,EAAe;AACd,iBAAO,CAAC,CAAR;AACA;;AACD,eAAOkB,CAAC,KAAK,CAAb,EAAgB;AACfyD,UAAAA,EAAE,CAAC1D,CAAC,EAAF,CAAF,GAAU4C,CAAV;AACA;AACD,OAVM,MAUA,IAAI3C,CAAC,KAAK,EAAV,EAAc;AAAE;AACtBoB,QAAAA,QAAQ,CAAC,CAAD,CAAR;AACApB,QAAAA,CAAC,GAAG,IAAIuB,OAAO,CAAC,CAAD,CAAf;AACAE,QAAAA,QAAQ,CAAC,CAAD,CAAR;;AACA,YAAI1B,CAAC,GAAGC,CAAJ,GAAQlB,CAAZ,EAAe;AACd,iBAAO,CAAC,CAAR;AACA;;AACD,eAAOkB,CAAC,KAAK,CAAb,EAAgB;AACfyD,UAAAA,EAAE,CAAC1D,CAAC,EAAF,CAAF,GAAU,CAAV;AACA;;AACD4C,QAAAA,CAAC,GAAG,CAAJ;AACA,OAXM,MAWA;AAAE;AACRvB,QAAAA,QAAQ,CAAC,CAAD,CAAR;AACApB,QAAAA,CAAC,GAAG,KAAKuB,OAAO,CAAC,CAAD,CAAhB;AACAE,QAAAA,QAAQ,CAAC,CAAD,CAAR;;AACA,YAAI1B,CAAC,GAAGC,CAAJ,GAAQlB,CAAZ,EAAe;AACd,iBAAO,CAAC,CAAR;AACA;;AACD,eAAOkB,CAAC,KAAK,CAAb,EAAgB;AACfyD,UAAAA,EAAE,CAAC1D,CAAC,EAAF,CAAF,GAAU,CAAV;AACA;;AACD4C,QAAAA,CAAC,GAAG,CAAJ;AACA;AACD,KAhGwC,CAkGzC;;;AACAb,IAAAA,EAAE,GAAG6B,KAAL;AACA7D,IAAAA,CAAC,GAAG,IAAId,SAAJ,CAAcyE,EAAd,EAAkBF,EAAlB,EAAsB,GAAtB,EAA2BV,MAA3B,EAAmCC,MAAnC,EAA2ChB,EAA3C,CAAJ;;AACA,QAAIA,EAAE,KAAK,CAAX,EAAc;AAAE;AACfhC,MAAAA,CAAC,CAACR,MAAF,GAAW,CAAX;AACA;;AACD,QAAIQ,CAAC,CAACR,MAAF,KAAa,CAAjB,EAAoB;AACnB,UAAIQ,CAAC,CAACR,MAAF,KAAa,CAAjB,EAAoB;AACnB,eAAO,CAAC,CAAR,CADmB,CACR;AACX,OAHkB,CAInB;;AACA;;AACDyC,IAAAA,EAAE,GAAGjC,CAAC,CAACP,IAAP;AACAuC,IAAAA,EAAE,GAAGhC,CAAC,CAACN,CAAP;;AAEA,SAAKO,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGyD,EAAhB,EAAoBzD,CAAC,EAArB,EAAyB;AACxB0D,MAAAA,EAAE,CAAC1D,CAAD,CAAF,GAAQ0D,EAAE,CAAC1D,CAAC,GAAGwD,EAAL,CAAV;AACA;;AACDnB,IAAAA,EAAE,GAAGwB,KAAL;AACA9D,IAAAA,CAAC,GAAG,IAAId,SAAJ,CAAcyE,EAAd,EAAkBD,EAAlB,EAAsB,CAAtB,EAAyBN,MAAzB,EAAiCC,MAAjC,EAAyCf,EAAzC,CAAJ;AACAC,IAAAA,EAAE,GAAGvC,CAAC,CAACP,IAAP;AACA6C,IAAAA,EAAE,GAAGtC,CAAC,CAACN,CAAP;;AAEA,QAAI4C,EAAE,KAAK,CAAP,IAAYmB,EAAE,GAAG,GAArB,EAA0B;AAAE;AAC3B;AACA,aAAO,CAAC,CAAR;AACA;AACF;AACA;AACA;AACA;AACA;;;AACC,QAAIzD,CAAC,CAACR,MAAF,KAAa,CAAjB,EAAoB;AACnB,aAAO,CAAC,CAAR;AACA,KApIwC,CAsIzC;;;AACA,WAAOoC,aAAa,CAACC,IAAD,EAAOC,GAAP,EAAYC,IAAZ,CAApB;AACA;;AAED,WAASgC,aAAT,GAAyB;AACxB,QAAI,CAAC3B,KAAL,EAAY;AACXA,MAAAA,KAAK,GAAG,EAAR,CADW,CACC;AACZ;;AACDF,IAAAA,EAAE,GAAG,CAAL;AACAV,IAAAA,OAAO,GAAG,CAAV;AACAD,IAAAA,OAAO,GAAG,CAAV;AACAkB,IAAAA,MAAM,GAAG,CAAC,CAAV;AACAuB,IAAAA,GAAG,GAAG,KAAN;AACA3B,IAAAA,SAAS,GAAGG,SAAS,GAAG,CAAxB;AACAP,IAAAA,EAAE,GAAG,IAAL;AACA;;AAED,WAASgC,gBAAT,CAA0BpC,IAA1B,EAAgCC,GAAhC,EAAqCC,IAArC,EAA2C;AAC1C;AACA,QAAI/C,CAAJ,EAAOiB,CAAP;AAEAjB,IAAAA,CAAC,GAAG,CAAJ;;AACA,WAAOA,CAAC,GAAG+C,IAAX,EAAiB;AAChB,UAAIiC,GAAG,IAAIvB,MAAM,KAAK,CAAC,CAAvB,EAA0B;AACzB,eAAOzD,CAAP;AACA;;AAED,UAAIqD,SAAS,GAAG,CAAhB,EAAmB;AAClB,YAAII,MAAM,KAAKyB,YAAf,EAA6B;AAC5B;AACA,iBAAO7B,SAAS,GAAG,CAAZ,IAAiBrD,CAAC,GAAG+C,IAA5B,EAAkC;AACjCM,YAAAA,SAAS;AACTG,YAAAA,SAAS,IAAIL,KAAK,GAAG,CAArB;AACAD,YAAAA,EAAE,IAAIC,KAAK,GAAG,CAAd;AACAN,YAAAA,IAAI,CAACC,GAAG,GAAG9C,CAAC,EAAR,CAAJ,GAAkBoD,KAAK,CAACF,EAAE,EAAH,CAAL,GAAcE,KAAK,CAACI,SAAS,EAAV,CAArC;AACA;AACD,SARD,MAQO;AACN,iBAAOH,SAAS,GAAG,CAAZ,IAAiBrD,CAAC,GAAG+C,IAA5B,EAAkC;AACjCM,YAAAA,SAAS;AACTH,YAAAA,EAAE,IAAIC,KAAK,GAAG,CAAd;AACAb,YAAAA,QAAQ,CAAC,CAAD,CAAR;AACAO,YAAAA,IAAI,CAACC,GAAG,GAAG9C,CAAC,EAAR,CAAJ,GAAkBoD,KAAK,CAACF,EAAE,EAAH,CAAL,GAAcT,OAAO,CAAC,CAAD,CAAvC;AACAE,YAAAA,QAAQ,CAAC,CAAD,CAAR;AACA;;AACD,cAAIU,SAAS,KAAK,CAAlB,EAAqB;AACpBI,YAAAA,MAAM,GAAG,CAAC,CAAV,CADoB,CACP;AACb;AACD;;AACD,YAAIzD,CAAC,KAAK+C,IAAV,EAAgB;AACf,iBAAO/C,CAAP;AACA;AACD;;AAED,UAAIyD,MAAM,KAAK,CAAC,CAAhB,EAAmB;AAClB,YAAIuB,GAAJ,EAAS;AACR;AACA,SAHiB,CAKlB;;;AACA1C,QAAAA,QAAQ,CAAC,CAAD,CAAR;;AACA,YAAIG,OAAO,CAAC,CAAD,CAAP,KAAe,CAAnB,EAAsB;AACrBuC,UAAAA,GAAG,GAAG,IAAN;AACA;;AACDrC,QAAAA,QAAQ,CAAC,CAAD,CAAR,CAVkB,CAYlB;;AACAL,QAAAA,QAAQ,CAAC,CAAD,CAAR;AACAmB,QAAAA,MAAM,GAAGhB,OAAO,CAAC,CAAD,CAAhB;AACAE,QAAAA,QAAQ,CAAC,CAAD,CAAR;AACAM,QAAAA,EAAE,GAAG,IAAL;AACAI,QAAAA,SAAS,GAAG,CAAZ;AACA;;AAED,cAAQI,MAAR;AACA,aAAKyB,YAAL;AACCjE,UAAAA,CAAC,GAAGyC,cAAc,CAACb,IAAD,EAAOC,GAAG,GAAG9C,CAAb,EAAgB+C,IAAI,GAAG/C,CAAvB,CAAlB;AACA;;AAED,aAAKmF,YAAL;AACC,cAAIlC,EAAJ,EAAQ;AACPhC,YAAAA,CAAC,GAAG2B,aAAa,CAACC,IAAD,EAAOC,GAAG,GAAG9C,CAAb,EAAgB+C,IAAI,GAAG/C,CAAvB,CAAjB;AACA,WAFD,MAEO;AACNiB,YAAAA,CAAC,GAAG0C,aAAa,CAACd,IAAD,EAAOC,GAAG,GAAG9C,CAAb,EAAgB+C,IAAI,GAAG/C,CAAvB,CAAjB;AACA;;AACD;;AAED,aAAKoF,SAAL;AACC,cAAInC,EAAJ,EAAQ;AACPhC,YAAAA,CAAC,GAAG2B,aAAa,CAACC,IAAD,EAAOC,GAAG,GAAG9C,CAAb,EAAgB+C,IAAI,GAAG/C,CAAvB,CAAjB;AACA,WAFD,MAEO;AACNiB,YAAAA,CAAC,GAAGsD,eAAe,CAAC1B,IAAD,EAAOC,GAAG,GAAG9C,CAAb,EAAgB+C,IAAI,GAAG/C,CAAvB,CAAnB;AACA;;AACD;;AAED;AAAS;AACRiB,UAAAA,CAAC,GAAG,CAAC,CAAL;AACA;AAvBD;;AA0BA,UAAIA,CAAC,KAAK,CAAC,CAAX,EAAc;AACb,YAAI+D,GAAJ,EAAS;AACR,iBAAO,CAAP;AACA;;AACD,eAAO,CAAC,CAAR;AACA;;AACDhF,MAAAA,CAAC,IAAIiB,CAAL;AACA;;AACD,WAAOjB,CAAP;AACA;;AAEM,WAASqF,OAAT,CAAiBC,GAAjB,EAAsB;AAC5B,QAAIzC,IAAI,GAAG,EAAX;AAAA,QAAe5B,CAAf;AAEA8D,IAAAA,aAAa;AACb5C,IAAAA,YAAY,GAAGmD,GAAf;AACAjD,IAAAA,WAAW,GAAG,CAAd;;AAEA,OAAG;AACFpB,MAAAA,CAAC,GAAGgE,gBAAgB,CAACpC,IAAD,EAAOA,IAAI,CAACT,MAAZ,EAAoB,IAApB,CAApB;AACA,KAFD,QAESnB,CAAC,GAAG,CAFb;;AAGAkB,IAAAA,YAAY,GAAG,IAAf,CAV4B,CAUP;;AACrB,WAAOU,IAAP;AACA;;qBAZewC,O;;;;;;;;;;;AA3xBhB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACIlC,MAAAA,K,GAAQ,K;AACX+B,MAAAA,Y,GAAe,C;AACfC,MAAAA,Y,GAAe,C;AACfC,MAAAA,S,GAAY,C;AAGZP,MAAAA,K,GAAQ,C;AACRC,MAAAA,K,GAAQ,C;AAKRlB,MAAAA,Q,GAAW,I;AAoBXlB,MAAAA,S,GAAY,CACX,MADW,EAEX,MAFW,EAEH,MAFG,EAEK,MAFL,EAEa,MAFb,EAEqB,MAFrB,EAE6B,MAF7B,EAEqC,MAFrC,EAE6C,MAF7C,EAGX,MAHW,EAGH,MAHG,EAGK,MAHL,EAGa,MAHb,EAGqB,MAHrB,EAG6B,MAH7B,EAGqC,MAHrC,EAG6C,MAH7C,C;AAOZqB,MAAAA,M,GAAS,CACR,CADQ,EACL,CADK,EACF,CADE,EACC,CADD,EACI,CADJ,EACO,CADP,EACU,CADV,EACa,EADb,EACiB,EADjB,EACqB,EADrB,EACyB,EADzB,EAC6B,EAD7B,EACiC,EADjC,EACqC,EADrC,EACyC,EADzC,EAC6C,EAD7C,EAER,EAFQ,EAEJ,EAFI,EAEA,EAFA,EAEI,EAFJ,EAEQ,EAFR,EAEY,EAFZ,EAEgB,EAFhB,EAEoB,GAFpB,EAEyB,GAFzB,EAE8B,GAF9B,EAEmC,GAFnC,EAEwC,GAFxC,EAE6C,GAF7C,EAEkD,CAFlD,EAEqD,CAFrD,C;AAMTC,MAAAA,M,GAAS,CACR,CADQ,EACL,CADK,EACF,CADE,EACC,CADD,EACI,CADJ,EACO,CADP,EACU,CADV,EACa,CADb,EACgB,CADhB,EACmB,CADnB,EACsB,CADtB,EACyB,CADzB,EAC4B,CAD5B,EAC+B,CAD/B,EACkC,CADlC,EACqC,CADrC,EAER,CAFQ,EAEL,CAFK,EAEF,CAFE,EAEC,CAFD,EAEI,CAFJ,EAEO,CAFP,EAEU,CAFV,EAEa,CAFb,EAEgB,CAFhB,EAEmB,CAFnB,EAEsB,CAFtB,EAEyB,CAFzB,EAE4B,CAF5B,EAE+B,EAF/B,EAEmC,EAFnC,CAEsC;AAFtC,O;AAKTI,MAAAA,M,GAAS,CACR,CADQ,EACL,CADK,EACF,CADE,EACC,CADD,EACI,CADJ,EACO,CADP,EACU,CADV,EACa,EADb,EACiB,EADjB,EACqB,EADrB,EACyB,EADzB,EAC6B,EAD7B,EACiC,EADjC,EACqC,EADrC,EACyC,GADzC,EAC8C,GAD9C,EAER,GAFQ,EAEH,GAFG,EAEE,GAFF,EAEO,GAFP,EAEY,IAFZ,EAEkB,IAFlB,EAEwB,IAFxB,EAE8B,IAF9B,EAEoC,IAFpC,EAE0C,IAF1C,EAGR,IAHQ,EAGF,KAHE,EAGK,KAHL,EAGY,KAHZ,C;AAMTC,MAAAA,M,GAAS,CACR,CADQ,EACL,CADK,EACF,CADE,EACC,CADD,EACI,CADJ,EACO,CADP,EACU,CADV,EACa,CADb,EACgB,CADhB,EACmB,CADnB,EACsB,CADtB,EACyB,CADzB,EAC4B,CAD5B,EAC+B,CAD/B,EACkC,CADlC,EACqC,CADrC,EAER,CAFQ,EAEL,CAFK,EAEF,CAFE,EAEC,CAFD,EAEI,CAFJ,EAEO,CAFP,EAEU,EAFV,EAEc,EAFd,EAEkB,EAFlB,EAEsB,EAFtB,EAGR,EAHQ,EAGJ,EAHI,EAGA,EAHA,EAGI,EAHJ,C;AAMTO,MAAAA,M,GAAS,CACR,EADQ,EACJ,EADI,EACA,EADA,EACI,CADJ,EACO,CADP,EACU,CADV,EACa,CADb,EACgB,CADhB,EACmB,EADnB,EACuB,CADvB,EAC0B,EAD1B,EAC8B,CAD9B,EACiC,EADjC,EACqC,CADrC,EACwC,EADxC,EAC4C,CAD5C,EAC+C,EAD/C,EACmD,CADnD,EACsD,EADtD,C", "sourcesContent": ["/*\n * $Id: rawinflate.js,v 0.2 2009/03/01 18:32:24 dankogai Exp $\n *\n * original:\n * http://www.onicos.com/staff/iz/amuse/javascript/expert/inflate.txt\n */\n\n/* Copyright (C) 1999 <PERSON><PERSON><PERSON> <<EMAIL>>\n * Version: *******\n * LastModified: Dec 25 1999\n */\n\n/* Interface:\n * data = inflate(src);\n */\n\n/* constant parameters */\nvar WSIZE = 32768, // Sliding Window size\n\tSTORED_BLOCK = 0,\n\tSTATIC_TREES = 1,\n\tDYN_TREES = 2,\n\n/* for inflate */\n\tlbits = 9, // bits in base literal/length lookup table\n\tdbits = 6, // bits in base distance lookup table\n\n/* variables (inflate) */\n\tslide,\n\twp, // current position in slide\n\tfixed_tl = null, // inflate static\n\tfixed_td, // inflate static\n\tfixed_bl, // inflate static\n\tfixed_bd, // inflate static\n\tbit_buf, // bit buffer\n\tbit_len, // bits in bit buffer\n\tmethod,\n\teof,\n\tcopy_leng,\n\tcopy_dist,\n\ttl, // literal length decoder table\n\ttd, // literal distance decoder table\n\tbl, // number of bits decoded by tl\n\tbd, // number of bits decoded by td\n\n\tinflate_data,\n\tinflate_pos,\n\n\n/* constant tables (inflate) */\n\tMASK_BITS = [\n\t\t0x0000,\n\t\t0x0001, 0x0003, 0x0007, 0x000f, 0x001f, 0x003f, 0x007f, 0x00ff,\n\t\t0x01ff, 0x03ff, 0x07ff, 0x0fff, 0x1fff, 0x3fff, 0x7fff, 0xffff\n\t],\n\t// Tables for deflate from PKZIP's appnote.txt.\n\t// Copy lengths for literal codes 257..285\n\tcplens = [\n\t\t3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31,\n\t\t35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0\n\t],\n/* note: see note #13 above about the 258 in this list. */\n\t// Extra bits for literal codes 257..285\n\tcplext = [\n\t\t0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2,\n\t\t3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, 99, 99 // 99==invalid\n\t],\n\t// Copy offsets for distance codes 0..29\n\tcpdist = [\n\t\t1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193,\n\t\t257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145,\n\t\t8193, 12289, 16385, 24577\n\t],\n\t// Extra bits for distance codes\n\tcpdext = [\n\t\t0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6,\n\t\t7, 7, 8, 8, 9, 9, 10, 10, 11, 11,\n\t\t12, 12, 13, 13\n\t],\n\t// Order of the bit length code lengths\n\tborder = [\n\t\t16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15\n\t];\n/* objects (inflate) */\n\nfunction HuftList() {\n\tthis.next = null;\n\tthis.list = null;\n}\n\nfunction HuftNode() {\n\tthis.e = 0; // number of extra bits or operation\n\tthis.b = 0; // number of bits in this code or subcode\n\n\t// union\n\tthis.n = 0; // literal, length base, or distance base\n\tthis.t = null; // (HuftNode) pointer to next level of table\n}\n\n/*\n\t* @param b-  code lengths in bits (all assumed <= BMAX)\n\t* @param n- number of codes (assumed <= N_MAX)\n\t* @param s- number of simple-valued codes (0..s-1)\n\t* @param d- list of base values for non-simple codes\n\t* @param e- list of extra bits for non-simple codes\n\t* @param mm- maximum lookup bits\n\t*/\nfunction HuftBuild(b, n, s, d, e, mm) {\n\tthis.BMAX = 16; // maximum bit length of any code\n\tthis.N_MAX = 288; // maximum number of codes in any set\n\tthis.status = 0; // 0: success, 1: incomplete table, 2: bad input\n\tthis.root = null; // (HuftList) starting table\n\tthis.m = 0; // maximum lookup bits, returns actual\n\n/* Given a list of code lengths and a maximum table size, make a set of\n\ttables to decode that set of codes. Return zero on success, one if\n\tthe given code set is incomplete (the tables are still built in this\n\tcase), two if the input is invalid (all zero length codes or an\n\toversubscribed set of lengths), and three if not enough memory.\n\tThe code with value 256 is special, and the tables are constructed\n\tso that no bits beyond that code are fetched when that code is\n\tdecoded. */\n\tvar a; // counter for codes of length k\n\tvar c = [];\n\tvar el; // length of EOB code (value 256)\n\tvar f; // i repeats in table every f entries\n\tvar g; // maximum code length\n\tvar h; // table level\n\tvar i; // counter, current code\n\tvar j; // counter\n\tvar k; // number of bits in current code\n\tvar lx = [];\n\tvar p; // pointer into c[], b[], or v[]\n\tvar pidx; // index of p\n\tvar q; // (HuftNode) points to current table\n\tvar r = new HuftNode(); // table entry for structure assignment\n\tvar u = [];\n\tvar v = [];\n\tvar w;\n\tvar x = [];\n\tvar xp; // pointer into x or c\n\tvar y; // number of dummy codes added\n\tvar z; // number of entries in current table\n\tvar o;\n\tvar tail; // (HuftList)\n\n\ttail = this.root = null;\n\n\t// bit length count table\n\tfor (i = 0; i < this.BMAX + 1; i++) {\n\t\tc[i] = 0;\n\t}\n\t// stack of bits per table\n\tfor (i = 0; i < this.BMAX + 1; i++) {\n\t\tlx[i] = 0;\n\t}\n\t// HuftNode[BMAX][]  table stack\n\tfor (i = 0; i < this.BMAX; i++) {\n\t\tu[i] = null;\n\t}\n\t// values in order of bit length\n\tfor (i = 0; i < this.N_MAX; i++) {\n\t\tv[i] = 0;\n\t}\n\t// bit offsets, then code stack\n\tfor (i = 0; i < this.BMAX + 1; i++) {\n\t\tx[i] = 0;\n\t}\n\n\t// Generate counts for each bit length\n\tel = n > 256 ? b[256] : this.BMAX; // set length of EOB code, if any\n\tp = b; pidx = 0;\n\ti = n;\n\tdo {\n\t\tc[p[pidx]]++; // assume all entries <= BMAX\n\t\tpidx++;\n\t} while (--i > 0);\n\tif (c[0] === n) { // null input--all zero length codes\n\t\tthis.root = null;\n\t\tthis.m = 0;\n\t\tthis.status = 0;\n\t\treturn;\n\t}\n\n\t// Find minimum and maximum length, bound *m by those\n\tfor (j = 1; j <= this.BMAX; j++) {\n\t\tif (c[j] !== 0) {\n\t\t\tbreak;\n\t\t}\n\t}\n\tk = j; // minimum code length\n\tif (mm < j) {\n\t\tmm = j;\n\t}\n\tfor (i = this.BMAX; i !== 0; i--) {\n\t\tif (c[i] !== 0) {\n\t\t\tbreak;\n\t\t}\n\t}\n\tg = i; // maximum code length\n\tif (mm > i) {\n\t\tmm = i;\n\t}\n\n\t// Adjust last length count to fill out codes, if needed\n\tfor (y = 1 << j; j < i; j++, y <<= 1) {\n\t\tif ((y -= c[j]) < 0) {\n\t\t\tthis.status = 2; // bad input: more codes than bits\n\t\t\tthis.m = mm;\n\t\t\treturn;\n\t\t}\n\t}\n\tif ((y -= c[i]) < 0) {\n\t\tthis.status = 2;\n\t\tthis.m = mm;\n\t\treturn;\n\t}\n\tc[i] += y;\n\n\t// Generate starting offsets into the value table for each length\n\tx[1] = j = 0;\n\tp = c;\n\tpidx = 1;\n\txp = 2;\n\twhile (--i > 0) { // note that i == g from above\n\t\tx[xp++] = (j += p[pidx++]);\n\t}\n\n\t// Make a table of values in order of bit lengths\n\tp = b; pidx = 0;\n\ti = 0;\n\tdo {\n\t\tif ((j = p[pidx++]) !== 0) {\n\t\t\tv[x[j]++] = i;\n\t\t}\n\t} while (++i < n);\n\tn = x[g]; // set n to length of v\n\n\t// Generate the Huffman codes and for each, make the table entries\n\tx[0] = i = 0; // first Huffman code is zero\n\tp = v; pidx = 0; // grab values in bit order\n\th = -1; // no tables yet--level -1\n\tw = lx[0] = 0; // no bits decoded yet\n\tq = null; // ditto\n\tz = 0; // ditto\n\n\t// go through the bit lengths (k already is bits in shortest code)\n\tfor (null; k <= g; k++) {\n\t\ta = c[k];\n\t\twhile (a-- > 0) {\n\t\t\t// here i is the Huffman code of length k bits for value p[pidx]\n\t\t\t// make tables up to required level\n\t\t\twhile (k > w + lx[1 + h]) {\n\t\t\t\tw += lx[1 + h]; // add bits already decoded\n\t\t\t\th++;\n\n\t\t\t\t// compute minimum size table less than or equal to *m bits\n\t\t\t\tz = (z = g - w) > mm ? mm : z; // upper limit\n\t\t\t\tif ((f = 1 << (j = k - w)) > a + 1) { // try a k-w bit table\n\t\t\t\t\t// too few codes for k-w bit table\n\t\t\t\t\tf -= a + 1; // deduct codes from patterns left\n\t\t\t\t\txp = k;\n\t\t\t\t\twhile (++j < z) { // try smaller tables up to z bits\n\t\t\t\t\t\tif ((f <<= 1) <= c[++xp]) {\n\t\t\t\t\t\t\tbreak; // enough codes to use up j bits\n\t\t\t\t\t\t}\n\t\t\t\t\t\tf -= c[xp]; // else deduct codes from patterns\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (w + j > el && w < el) {\n\t\t\t\t\tj = el - w; // make EOB code end at table\n\t\t\t\t}\n\t\t\t\tz = 1 << j; // table entries for j-bit table\n\t\t\t\tlx[1 + h] = j; // set table size in stack\n\n\t\t\t\t// allocate and link in new table\n\t\t\t\tq = [];\n\t\t\t\tfor (o = 0; o < z; o++) {\n\t\t\t\t\tq[o] = new HuftNode();\n\t\t\t\t}\n\n\t\t\t\tif (!tail) {\n\t\t\t\t\ttail = this.root = new HuftList();\n\t\t\t\t} else {\n\t\t\t\t\ttail = tail.next = new HuftList();\n\t\t\t\t}\n\t\t\t\ttail.next = null;\n\t\t\t\ttail.list = q;\n\t\t\t\tu[h] = q; // table starts after link\n\n\t\t\t\t/* connect to last table, if there is one */\n\t\t\t\tif (h > 0) {\n\t\t\t\t\tx[h] = i; // save pattern for backing up\n\t\t\t\t\tr.b = lx[h]; // bits to dump before this table\n\t\t\t\t\tr.e = 16 + j; // bits in this table\n\t\t\t\t\tr.t = q; // pointer to this table\n\t\t\t\t\tj = (i & ((1 << w) - 1)) >> (w - lx[h]);\n\t\t\t\t\tu[h - 1][j].e = r.e;\n\t\t\t\t\tu[h - 1][j].b = r.b;\n\t\t\t\t\tu[h - 1][j].n = r.n;\n\t\t\t\t\tu[h - 1][j].t = r.t;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// set up table entry in r\n\t\t\tr.b = k - w;\n\t\t\tif (pidx >= n) {\n\t\t\t\tr.e = 99; // out of values--invalid code\n\t\t\t} else if (p[pidx] < s) {\n\t\t\t\tr.e = (p[pidx] < 256 ? 16 : 15); // 256 is end-of-block code\n\t\t\t\tr.n = p[pidx++]; // simple code is just the value\n\t\t\t} else {\n\t\t\t\tr.e = e[p[pidx] - s]; // non-simple--look up in lists\n\t\t\t\tr.n = d[p[pidx++] - s];\n\t\t\t}\n\n\t\t\t// fill code-like entries with r //\n\t\t\tf = 1 << (k - w);\n\t\t\tfor (j = i >> w; j < z; j += f) {\n\t\t\t\tq[j].e = r.e;\n\t\t\t\tq[j].b = r.b;\n\t\t\t\tq[j].n = r.n;\n\t\t\t\tq[j].t = r.t;\n\t\t\t}\n\n\t\t\t// backwards increment the k-bit code i\n\t\t\tfor (j = 1 << (k - 1); (i & j) !== 0; j >>= 1) {\n\t\t\t\ti ^= j;\n\t\t\t}\n\t\t\ti ^= j;\n\n\t\t\t// backup over finished tables\n\t\t\twhile ((i & ((1 << w) - 1)) !== x[h]) {\n\t\t\t\tw -= lx[h]; // don't need to update q\n\t\t\t\th--;\n\t\t\t}\n\t\t}\n\t}\n\n\t/* return actual size of base table */\n\tthis.m = lx[1];\n\n\t/* Return true (1) if we were given an incomplete table */\n\tthis.status = ((y !== 0 && g !== 1) ? 1 : 0);\n}\n\n\n/* routines (inflate) */\n\nfunction GET_BYTE() {\n\tif (inflate_data.length === inflate_pos) {\n\t\treturn -1;\n\t}\n\treturn inflate_data[inflate_pos++] & 0xff;\n}\n\nfunction NEEDBITS(n) {\n\twhile (bit_len < n) {\n\t\tbit_buf |= GET_BYTE() << bit_len;\n\t\tbit_len += 8;\n\t}\n}\n\nfunction GETBITS(n) {\n\treturn bit_buf & MASK_BITS[n];\n}\n\nfunction DUMPBITS(n) {\n\tbit_buf >>= n;\n\tbit_len -= n;\n}\n\nfunction inflate_codes(buff, off, size) {\n\t// inflate (decompress) the codes in a deflated (compressed) block.\n\t// Return an error code or zero if it all goes ok.\n\tvar e; // table entry flag/number of extra bits\n\tvar t; // (HuftNode) pointer to table entry\n\tvar n;\n\n\tif (size === 0) {\n\t\treturn 0;\n\t}\n\n\t// inflate the coded data\n\tn = 0;\n\tfor (;;) { // do until end of block\n\t\tNEEDBITS(bl);\n\t\tt = tl.list[GETBITS(bl)];\n\t\te = t.e;\n\t\twhile (e > 16) {\n\t\t\tif (e === 99) {\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\tDUMPBITS(t.b);\n\t\t\te -= 16;\n\t\t\tNEEDBITS(e);\n\t\t\tt = t.t[GETBITS(e)];\n\t\t\te = t.e;\n\t\t}\n\t\tDUMPBITS(t.b);\n\n\t\tif (e === 16) { // then it's a literal\n\t\t\twp &= WSIZE - 1;\n\t\t\tbuff[off + n++] = slide[wp++] = t.n;\n\t\t\tif (n === size) {\n\t\t\t\treturn size;\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\t// exit if end of block\n\t\tif (e === 15) {\n\t\t\tbreak;\n\t\t}\n\n\t\t// it's an EOB or a length\n\n\t\t// get length of block to copy\n\t\tNEEDBITS(e);\n\t\tcopy_leng = t.n + GETBITS(e);\n\t\tDUMPBITS(e);\n\n\t\t// decode distance of block to copy\n\t\tNEEDBITS(bd);\n\t\tt = td.list[GETBITS(bd)];\n\t\te = t.e;\n\n\t\twhile (e > 16) {\n\t\t\tif (e === 99) {\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\tDUMPBITS(t.b);\n\t\t\te -= 16;\n\t\t\tNEEDBITS(e);\n\t\t\tt = t.t[GETBITS(e)];\n\t\t\te = t.e;\n\t\t}\n\t\tDUMPBITS(t.b);\n\t\tNEEDBITS(e);\n\t\tcopy_dist = wp - t.n - GETBITS(e);\n\t\tDUMPBITS(e);\n\n\t\t// do the copy\n\t\twhile (copy_leng > 0 && n < size) {\n\t\t\tcopy_leng--;\n\t\t\tcopy_dist &= WSIZE - 1;\n\t\t\twp &= WSIZE - 1;\n\t\t\tbuff[off + n++] = slide[wp++] = slide[copy_dist++];\n\t\t}\n\n\t\tif (n === size) {\n\t\t\treturn size;\n\t\t}\n\t}\n\n\tmethod = -1; // done\n\treturn n;\n}\n\nfunction inflate_stored(buff, off, size) {\n\t/* \"decompress\" an inflated type 0 (stored) block. */\n\tvar n;\n\n\t// go to byte boundary\n\tn = bit_len & 7;\n\tDUMPBITS(n);\n\n\t// get the length and its complement\n\tNEEDBITS(16);\n\tn = GETBITS(16);\n\tDUMPBITS(16);\n\tNEEDBITS(16);\n\tif (n !== ((~bit_buf) & 0xffff)) {\n\t\treturn -1; // error in compressed data\n\t}\n\tDUMPBITS(16);\n\n\t// read and output the compressed data\n\tcopy_leng = n;\n\n\tn = 0;\n\twhile (copy_leng > 0 && n < size) {\n\t\tcopy_leng--;\n\t\twp &= WSIZE - 1;\n\t\tNEEDBITS(8);\n\t\tbuff[off + n++] = slide[wp++] = GETBITS(8);\n\t\tDUMPBITS(8);\n\t}\n\n\tif (copy_leng === 0) {\n\t\tmethod = -1; // done\n\t}\n\treturn n;\n}\n\nfunction inflate_fixed(buff, off, size) {\n\t// decompress an inflated type 1 (fixed Huffman codes) block.  We should\n\t// either replace this with a custom decoder, or at least precompute the\n\t// Huffman tables.\n\n\t// if first time, set up tables for fixed blocks\n\tif (!fixed_tl) {\n\t\tvar i; // temporary variable\n\t\tvar l = []; // 288 length list for huft_build (initialized below)\n\t\tvar h; // HuftBuild\n\n\t\t// literal table\n\t\tfor (i = 0; i < 144; i++) {\n\t\t\tl[i] = 8;\n\t\t}\n\t\tfor (null; i < 256; i++) {\n\t\t\tl[i] = 9;\n\t\t}\n\t\tfor (null; i < 280; i++) {\n\t\t\tl[i] = 7;\n\t\t}\n\t\tfor (null; i < 288; i++) { // make a complete, but wrong code set\n\t\t\tl[i] = 8;\n\t\t}\n\t\tfixed_bl = 7;\n\n\t\th = new HuftBuild(l, 288, 257, cplens, cplext, fixed_bl);\n\t\tif (h.status !== 0) {\n\t\t\tconsole.error(\"HufBuild error: \" + h.status);\n\t\t\treturn -1;\n\t\t}\n\t\tfixed_tl = h.root;\n\t\tfixed_bl = h.m;\n\n\t\t// distance table\n\t\tfor (i = 0; i < 30; i++) { // make an incomplete code set\n\t\t\tl[i] = 5;\n\t\t}\n\t\tfixed_bd = 5;\n\n\t\th = new HuftBuild(l, 30, 0, cpdist, cpdext, fixed_bd);\n\t\tif (h.status > 1) {\n\t\t\tfixed_tl = null;\n\t\t\tconsole.error(\"HufBuild error: \" + h.status);\n\t\t\treturn -1;\n\t\t}\n\t\tfixed_td = h.root;\n\t\tfixed_bd = h.m;\n\t}\n\n\ttl = fixed_tl;\n\ttd = fixed_td;\n\tbl = fixed_bl;\n\tbd = fixed_bd;\n\treturn inflate_codes(buff, off, size);\n}\n\nfunction inflate_dynamic(buff, off, size) {\n\t// decompress an inflated type 2 (dynamic Huffman codes) block.\n\tvar i; // temporary variables\n\tvar j;\n\tvar l; // last length\n\tvar n; // number of lengths to get\n\tvar t; // (HuftNode) literal/length code table\n\tvar nb; // number of bit length codes\n\tvar nl; // number of literal/length codes\n\tvar nd; // number of distance codes\n\tvar ll = [];\n\tvar h; // (HuftBuild)\n\n\t// literal/length and distance code lengths\n\tfor (i = 0; i < 286 + 30; i++) {\n\t\tll[i] = 0;\n\t}\n\n\t// read in table lengths\n\tNEEDBITS(5);\n\tnl = 257 + GETBITS(5); // number of literal/length codes\n\tDUMPBITS(5);\n\tNEEDBITS(5);\n\tnd = 1 + GETBITS(5); // number of distance codes\n\tDUMPBITS(5);\n\tNEEDBITS(4);\n\tnb = 4 + GETBITS(4); // number of bit length codes\n\tDUMPBITS(4);\n\tif (nl > 286 || nd > 30) {\n\t\treturn -1; // bad lengths\n\t}\n\n\t// read in bit-length-code lengths\n\tfor (j = 0; j < nb; j++) {\n\t\tNEEDBITS(3);\n\t\tll[border[j]] = GETBITS(3);\n\t\tDUMPBITS(3);\n\t}\n\tfor (null; j < 19; j++) {\n\t\tll[border[j]] = 0;\n\t}\n\n\t// build decoding table for trees--single level, 7 bit lookup\n\tbl = 7;\n\th = new HuftBuild(ll, 19, 19, null, null, bl);\n\tif (h.status !== 0) {\n\t\treturn -1; // incomplete code set\n\t}\n\n\ttl = h.root;\n\tbl = h.m;\n\n\t// read in literal and distance code lengths\n\tn = nl + nd;\n\ti = l = 0;\n\twhile (i < n) {\n\t\tNEEDBITS(bl);\n\t\tt = tl.list[GETBITS(bl)];\n\t\tj = t.b;\n\t\tDUMPBITS(j);\n\t\tj = t.n;\n\t\tif (j < 16) { // length of code in bits (0..15)\n\t\t\tll[i++] = l = j; // save last length in l\n\t\t} else if (j === 16) { // repeat last length 3 to 6 times\n\t\t\tNEEDBITS(2);\n\t\t\tj = 3 + GETBITS(2);\n\t\t\tDUMPBITS(2);\n\t\t\tif (i + j > n) {\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\twhile (j-- > 0) {\n\t\t\t\tll[i++] = l;\n\t\t\t}\n\t\t} else if (j === 17) { // 3 to 10 zero length codes\n\t\t\tNEEDBITS(3);\n\t\t\tj = 3 + GETBITS(3);\n\t\t\tDUMPBITS(3);\n\t\t\tif (i + j > n) {\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\twhile (j-- > 0) {\n\t\t\t\tll[i++] = 0;\n\t\t\t}\n\t\t\tl = 0;\n\t\t} else { // j === 18: 11 to 138 zero length codes\n\t\t\tNEEDBITS(7);\n\t\t\tj = 11 + GETBITS(7);\n\t\t\tDUMPBITS(7);\n\t\t\tif (i + j > n) {\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\twhile (j-- > 0) {\n\t\t\t\tll[i++] = 0;\n\t\t\t}\n\t\t\tl = 0;\n\t\t}\n\t}\n\n\t// build the decoding tables for literal/length and distance codes\n\tbl = lbits;\n\th = new HuftBuild(ll, nl, 257, cplens, cplext, bl);\n\tif (bl === 0) { // no literals or lengths\n\t\th.status = 1;\n\t}\n\tif (h.status !== 0) {\n\t\tif (h.status !== 1) {\n\t\t\treturn -1; // incomplete code set\n\t\t}\n\t\t// **incomplete literal tree**\n\t}\n\ttl = h.root;\n\tbl = h.m;\n\n\tfor (i = 0; i < nd; i++) {\n\t\tll[i] = ll[i + nl];\n\t}\n\tbd = dbits;\n\th = new HuftBuild(ll, nd, 0, cpdist, cpdext, bd);\n\ttd = h.root;\n\tbd = h.m;\n\n\tif (bd === 0 && nl > 257) { // lengths but no distances\n\t\t// **incomplete distance tree**\n\t\treturn -1;\n\t}\n/*\n\tif (h.status === 1) {\n\t\t// **incomplete distance tree**\n\t}\n*/\n\tif (h.status !== 0) {\n\t\treturn -1;\n\t}\n\n\t// decompress until an end-of-block code\n\treturn inflate_codes(buff, off, size);\n}\n\nfunction inflate_start() {\n\tif (!slide) {\n\t\tslide = []; // new Array(2 * WSIZE); // slide.length is never called\n\t}\n\twp = 0;\n\tbit_buf = 0;\n\tbit_len = 0;\n\tmethod = -1;\n\teof = false;\n\tcopy_leng = copy_dist = 0;\n\ttl = null;\n}\n\nfunction inflate_internal(buff, off, size) {\n\t// decompress an inflated entry\n\tvar n, i;\n\n\tn = 0;\n\twhile (n < size) {\n\t\tif (eof && method === -1) {\n\t\t\treturn n;\n\t\t}\n\n\t\tif (copy_leng > 0) {\n\t\t\tif (method !== STORED_BLOCK) {\n\t\t\t\t// STATIC_TREES or DYN_TREES\n\t\t\t\twhile (copy_leng > 0 && n < size) {\n\t\t\t\t\tcopy_leng--;\n\t\t\t\t\tcopy_dist &= WSIZE - 1;\n\t\t\t\t\twp &= WSIZE - 1;\n\t\t\t\t\tbuff[off + n++] = slide[wp++] = slide[copy_dist++];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\twhile (copy_leng > 0 && n < size) {\n\t\t\t\t\tcopy_leng--;\n\t\t\t\t\twp &= WSIZE - 1;\n\t\t\t\t\tNEEDBITS(8);\n\t\t\t\t\tbuff[off + n++] = slide[wp++] = GETBITS(8);\n\t\t\t\t\tDUMPBITS(8);\n\t\t\t\t}\n\t\t\t\tif (copy_leng === 0) {\n\t\t\t\t\tmethod = -1; // done\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (n === size) {\n\t\t\t\treturn n;\n\t\t\t}\n\t\t}\n\n\t\tif (method === -1) {\n\t\t\tif (eof) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\t// read in last block bit\n\t\t\tNEEDBITS(1);\n\t\t\tif (GETBITS(1) !== 0) {\n\t\t\t\teof = true;\n\t\t\t}\n\t\t\tDUMPBITS(1);\n\n\t\t\t// read in block type\n\t\t\tNEEDBITS(2);\n\t\t\tmethod = GETBITS(2);\n\t\t\tDUMPBITS(2);\n\t\t\ttl = null;\n\t\t\tcopy_leng = 0;\n\t\t}\n\n\t\tswitch (method) {\n\t\tcase STORED_BLOCK:\n\t\t\ti = inflate_stored(buff, off + n, size - n);\n\t\t\tbreak;\n\n\t\tcase STATIC_TREES:\n\t\t\tif (tl) {\n\t\t\t\ti = inflate_codes(buff, off + n, size - n);\n\t\t\t} else {\n\t\t\t\ti = inflate_fixed(buff, off + n, size - n);\n\t\t\t}\n\t\t\tbreak;\n\n\t\tcase DYN_TREES:\n\t\t\tif (tl) {\n\t\t\t\ti = inflate_codes(buff, off + n, size - n);\n\t\t\t} else {\n\t\t\t\ti = inflate_dynamic(buff, off + n, size - n);\n\t\t\t}\n\t\t\tbreak;\n\n\t\tdefault: // error\n\t\t\ti = -1;\n\t\t\tbreak;\n\t\t}\n\n\t\tif (i === -1) {\n\t\t\tif (eof) {\n\t\t\t\treturn 0;\n\t\t\t}\n\t\t\treturn -1;\n\t\t}\n\t\tn += i;\n\t}\n\treturn n;\n}\n\nexport function inflate(arr) {\n\tvar buff = [], i;\n\n\tinflate_start();\n\tinflate_data = arr;\n\tinflate_pos = 0;\n\n\tdo {\n\t\ti = inflate_internal(buff, buff.length, 1024);\n\t} while (i > 0);\n\tinflate_data = null; // G.C.\n\treturn buff;\n}"]}