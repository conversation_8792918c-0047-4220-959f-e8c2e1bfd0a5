{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts"], "names": ["_decorator", "Component", "Label", "Sprite", "SpriteAtlas", "Node", "DateUtil", "MapCommand", "EventMgr", "LogicEvent", "ccclass", "property", "CityLogic", "onLoad", "_limitTime", "getInstance", "proxy", "getWarFree", "onDestroy", "onEnable", "on", "unionChange", "onUnionChange", "onDisable", "_data", "unscheduleAllCallbacks", "targetOff", "rid", "unionId", "parentId", "updateUI", "setCityData", "data", "console", "log", "labelName", "string", "name", "buildProxy", "myId", "upSpr", "spriteFrame", "resourceAtlas", "getSpriteFrame", "downSpr", "myUnionId", "myParentId", "diff", "getServerTime", "occupyTime", "mian<PERSON>ode", "active", "stopCountDown", "schedule", "countDown"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAGrDC,MAAAA,Q;;AAEAC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBASTY,S,WADpBF,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACP,WAAD,C,UAERO,QAAQ,CAACN,IAAD,C,oCAVb,MACqBO,SADrB,SACuCX,SADvC,CACiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAWd,IAXc;;AAAA,8CAYd,CAZc;AAAA;;AAanCY,QAAAA,MAAM,GAAS;AACrB,eAAKC,UAAL,GAAkB;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,KAAzB,CAA+BC,UAA/B,EAAlB;AACH;;AACSC,QAAAA,SAAS,GAAS,CAE3B;;AACSC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AACH;;AACSC,QAAAA,SAAS,GAAS;AACxB,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKC,sBAAL;AACA;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACSJ,QAAAA,aAAa,CAACK,GAAD,EAAcC,OAAd,EAA+BC,QAA/B,EAAuD;AAC1E,cAAI,KAAKL,KAAL,CAAWG,GAAX,IAAkBA,GAAtB,EAA2B;AAC3B,iBAAKH,KAAL,CAAWI,OAAX,GAAqBA,OAArB;AACA,iBAAKJ,KAAL,CAAWK,QAAX,GAAsBA,QAAtB;AACC;;AACD,eAAKC,QAAL;AACH;;AACMC,QAAAA,WAAW,CAACC,IAAD,EAA0B;AACxC,eAAKR,KAAL,GAAaQ,IAAb;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BF,IAA5B;AACA,eAAKF,QAAL;AACH;;AACMA,QAAAA,QAAQ,GAAS;AACpB,cAAI,KAAKN,KAAT,EAAgB;AAChB,iBAAKW,SAAL,CAAeC,MAAf,GAAwB,KAAKZ,KAAL,CAAWa,IAAnC;;AAEA,gBAAI,KAAKb,KAAL,CAAWG,GAAX,IAAkB;AAAA;AAAA,0CAAWZ,WAAX,GAAyBuB,UAAzB,CAAoCC,IAA1D,EAAgE;AAChE,mBAAKC,KAAL,CAAWC,WAAX,GAAyB,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,UAAlC,CAAzB;AACA,mBAAKC,OAAL,CAAaH,WAAb,GAA2B,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,UAAlC,CAA3B;AACC,aAHD,MAGO,IAAI,KAAKnB,KAAL,CAAWI,OAAX,GAAqB,CAArB,IAA0B,KAAKJ,KAAL,CAAWI,OAAX,IAAsB;AAAA;AAAA,0CAAWb,WAAX,GAAyBuB,UAAzB,CAAoCO,SAAxF,EAAmG;AAC1G,mBAAKL,KAAL,CAAWC,WAAX,GAAyB,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,WAAlC,CAAzB;AACA,mBAAKC,OAAL,CAAaH,WAAb,GAA2B,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,WAAlC,CAA3B;AACC,aAHM,MAGD,IAAI,KAAKnB,KAAL,CAAWI,OAAX,GAAqB,CAArB,IAA0B,KAAKJ,KAAL,CAAWI,OAAX,IAAsB;AAAA;AAAA,0CAAWb,WAAX,GAAyBuB,UAAzB,CAAoCQ,UAAxF,EAAoG;AAC1G,mBAAKN,KAAL,CAAWC,WAAX,GAAyB,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAAzB;AACA,mBAAKC,OAAL,CAAaH,WAAb,GAA2B,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAA3B;AACC,aAHK,MAGC,IAAI,KAAKnB,KAAL,CAAWK,QAAX,GAAsB,CAAtB,IAA2B,KAAKL,KAAL,CAAWK,QAAX,IAAuB;AAAA;AAAA,0CAAWd,WAAX,GAAyBuB,UAAzB,CAAoCO,SAA1F,EAAqG;AAC5G,mBAAKL,KAAL,CAAWC,WAAX,GAAyB,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAAzB;AACA,mBAAKC,OAAL,CAAaH,WAAb,GAA2B,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,YAAlC,CAA3B;AACC,aAHM,MAGD;AACN,mBAAKH,KAAL,CAAWC,WAAX,GAAyB,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,SAAlC,CAAzB;AACA,mBAAKC,OAAL,CAAaH,WAAb,GAA2B,KAAKC,aAAL,CAAmBC,cAAnB,CAAkC,SAAlC,CAA3B;AACC;;AAED,gBAAII,IAAI,GAAG;AAAA;AAAA,sCAASC,aAAT,KAA2B,KAAKxB,KAAL,CAAWyB,UAAjD;;AACAhB,YAAAA,OAAO,CAACC,GAAR,CAAY,MAAZ,EAAoBa,IAApB,EAA0B,KAAKjC,UAA/B;;AACA,gBAAI,KAAKU,KAAL,CAAWK,QAAX,GAAsB,CAAtB,IAA2BkB,IAAI,GAAC,KAAKjC,UAAzC,EAAoD;AACpD,mBAAKoC,QAAL,CAAcC,MAAd,GAAuB,IAAvB;AACA,mBAAKC,aAAL;AACA,mBAAKC,QAAL,CAAc,KAAKC,SAAnB,EAA8B,GAA9B;AACC,aAJD,MAIK;AACL,mBAAKJ,QAAL,CAAcC,MAAd,GAAuB,KAAvB;AACC;AACA;AACJ;;AACMG,QAAAA,SAAS,GAAG;AACf,cAAIP,IAAI,GAAG;AAAA;AAAA,oCAASC,aAAT,KAA2B,KAAKxB,KAAL,CAAWyB,UAAjD;;AACA,cAAIF,IAAI,GAAC,KAAKjC,UAAd,EAAyB;AACzB,iBAAKsC,aAAL;AACA,iBAAKF,QAAL,CAAcC,MAAd,GAAuB,KAAvB;AACC;AACJ;;AACMC,QAAAA,aAAa,GAAG;AACnB,eAAK3B,sBAAL;AACH;;AAhF4C,O;;;;;iBAEnB,I;;;;;;;iBAEH,I;;;;;;;iBAEE,I;;;;;;;iBAEW,I;;;;;;;iBAEZ,I", "sourcesContent": ["import { _decorator, Component, Label, Sprite, SpriteAtlas, Node } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport DateUtil from \"../../utils/DateUtil\";\nimport { MapCityData } from \"../MapCityProxy\";\nimport MapCommand from \"../MapCommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('CityLogic')\nexport default class CityLogic extends Component {\n    @property(Label)\n    labelName: Label | null = null;\n    @property(Sprite)\n    upSpr: Sprite | null = null;\n    @property(Sprite)\n    downSpr: Sprite | null = null;\n    @property(SpriteAtlas)\n    resourceAtlas: SpriteAtlas | null = null;\n    @property(Node)\n    mianNode: Node | null = null;\n    protected _data: MapCityData = null;\n    protected _limitTime: number = 0;\n    protected onLoad(): void {\n        this._limitTime = MapCommand.getInstance().proxy.getWarFree();\n    }\n    protected onDestroy(): void {\n\n    }\n    protected onEnable(): void {\n        EventMgr.on(LogicEvent.unionChange, this.onUnionChange, this);\n    }\n    protected onDisable(): void {\n        this._data = null;\n        this.unscheduleAllCallbacks();\n        EventMgr.targetOff(this);\n    }\n    protected onUnionChange(rid: number, unionId: number, parentId: number): void {\n        if (this._data.rid == rid ){\n        this._data.unionId = unionId;\n        this._data.parentId = parentId;\n        }\n        this.updateUI();\n    }\n    public setCityData(data: MapCityData): void {\n        this._data = data;\n        console.log(\"setCityData:\", data);\n        this.updateUI();\n    }\n    public updateUI(): void {\n        if (this._data) {\n        this.labelName.string = this._data.name;\n\n        if (this._data.rid == MapCommand.getInstance().buildProxy.myId) {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"blue_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"blue_1_3\");\n        } else if (this._data.unionId > 0 && this._data.unionId == MapCommand.getInstance().buildProxy.myUnionId) {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"green_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"green_1_3\");\n        }else if (this._data.unionId > 0 && this._data.unionId == MapCommand.getInstance().buildProxy.myParentId) {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"purple_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"purple_1_3\");\n        } else if (this._data.parentId > 0 && this._data.parentId == MapCommand.getInstance().buildProxy.myUnionId) {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"yellow_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"yellow_1_3\");\n        }else {\n        this.upSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"red_2_3\");\n        this.downSpr.spriteFrame = this.resourceAtlas.getSpriteFrame(\"red_1_3\");\n        }\n\n        var diff = DateUtil.getServerTime() - this._data.occupyTime;\n        console.log(\"diff\", diff, this._limitTime);\n        if (this._data.parentId > 0 && diff<this._limitTime){\n        this.mianNode.active = true;\n        this.stopCountDown();\n        this.schedule(this.countDown, 1.0);\n        }else{\n        this.mianNode.active = false;\n        }\n        }\n    }\n    public countDown() {\n        var diff = DateUtil.getServerTime() - this._data.occupyTime;\n        if (diff>this._limitTime){\n        this.stopCountDown();\n        this.mianNode.active = false;\n        }\n    }\n    public stopCountDown() {\n        this.unscheduleAllCallbacks();\n    }\n}\n\n"]}