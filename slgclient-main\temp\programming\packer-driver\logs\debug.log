debug: Thu, 31 Jul 2025 23:24:22 GMT
debug: Project: D:\CocosSLG\slgclient-main
debug: Targets: editor,preview
debug: Incremental file seems great.
debug: Engine path: D:\Software\Cocos3.4.0\Creator\3.4.0\resources\resources\3d\engine
debug: Initializing target [Editor]
debug: Loading cache
debug: Loading cache costs 7.49210000000312ms.
debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,3d,2d,ui,particle,particle-2d,physics-framework,physics-2d-framework,intersection-2d,primitive,profiler,audio,video,terrain,webview,tween,tiled-map,spine,dragon-bones,marionette
debug: Initializing target [Preview]
debug: Loading cache
debug: Loading cache costs 5.924200000008568ms.
debug: Sync engine features: base,gfx-webgl,gfx-webgl2,2d,ui,physics-2d-box2d,intersection-2d,profiler,particle-2d,audio,video,webview,tween,tiled-map,spine,dragon-bones,marionette
debug: Pulling asset-db.
debug: Fetch asset-db cost: 33.766499999997905ms.
debug: Build iteration starts.
Number of accumulated asset changes: 154
Feature changed: false
debug: Target(editor) build started.
debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:05 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:05 GMT+0800 (中国标准时间)
debug: Inspect cce:/internal/x/cc
debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:03:06 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:09 GMT+0800 (中国标准时间)
debug: Inspect cce:/internal/x/prerequisite-imports
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as cce:/internal/x/cc.
debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Fri Aug 01 2025 02:14:56 GMT+0800 (中国标准时间), Current mtime: Fri Aug 01 2025 07:24:17 GMT+0800 (中国标准时间)
debug: Inspect cce:/internal/code-quality/cr.mjs
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as cce:/internal/x/cc.
debug: Resolve cc/env from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as external dependency cc/env.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc/env from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as external dependency cc/env.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts as cce:/internal/x/cc.
debug: Resolve ./general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ./config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ./login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./core/LoaderManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts.
debug: Resolve ./general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ./map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./network/http/HttpManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts.
debug: Resolve ./map/ui/MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./network/socket/NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ./network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ./network/socket/NetNode from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts.
debug: Resolve ./utils/Toast from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts.
debug: Resolve ./skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ./utils/Tools from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Resolve ./common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./test/SimpleLoginTest from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts.
debug: Resolve ./utils/FixedScreenAdapter from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts.
debug: Resolve ./utils/ClickTestHelper from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./ChatProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ./ChatCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../utils/LocalCache from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../core/coreEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ./ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ./GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ./GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./coreEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ./GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./deflate-js from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts.
debug: Resolve ./crc32 from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ./LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./rawinflate from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts.
debug: Resolve ./rawdeflate from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts.
debug: Resolve ../libs/NameDict from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../config/HttpConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts.
debug: Resolve ../network/http/HttpManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../network/socket/NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../utils/Tools from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Resolve ./LoginProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts.
debug: Resolve ../utils/LocalCache from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../libs/crypto/md5 from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts.
debug: Resolve ./LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../utils/LocalCache from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ./entries/ArmyLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./entries/BuildTagLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./entries/BuildTipsLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./entries/CityLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapBuildProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapCityProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts.
debug: Resolve ./MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./entries/FacilityBuildLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapBuildProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts.
debug: Resolve ./ui/MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./MapCityProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./entries/ResBuildLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./entries/ResLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./MapClickUILogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./entries/SysCityLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./ArmySelectItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./CityArmyItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts.
debug: Resolve ../MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/Tools from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./CityGeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./FacilityDesLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts.
debug: Resolve ./FacilityItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./MapUIProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./FacilityAdditionItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ./CityArmyItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./SkillIconLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./MapUIProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./CityArmySettingLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts.
debug: Resolve ./ArmySelectNodeLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts.
debug: Resolve ../../union/UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ./FacilityListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts.
debug: Resolve ./Dialog from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./FortressAbout from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts.
debug: Resolve ../../utils/Tools from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Resolve ./CityAboutLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts.
debug: Resolve ./TransformLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts.
debug: Resolve ./GeneralListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts.
debug: Resolve ./WarReportLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts.
debug: Resolve ./GeneralInfoLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts.
debug: Resolve ./DrawRLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts.
debug: Resolve ./SkillInfoLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts.
debug: Resolve ./SkillLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./RightArmyItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./RightCityItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts.
debug: Resolve ./RightTagItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./SkillIconLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./SkillIconLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../../union/UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../skill/SkillProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../config/skill/Skill from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./WarReportDesItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../socket/NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ./WarReportDesLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./WebSock from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts.
debug: Resolve ./NetTimer from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts.
debug: Resolve ../map/MapResBuildLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts.
debug: Resolve ./NetNode from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts.
debug: Resolve ../map/MapBuildTipsLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts.
debug: Resolve ./HttpInvoke from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts.
debug: Resolve ../map/MapCityLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../map/MapLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts.
debug: Resolve ../map/MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../map/MapResLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts.
debug: Resolve ../map/MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../map/MapFacilityBuildLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../core/coreEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts.
debug: Resolve ../map/MapBuildTagLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts.
debug: Resolve ../map/MapSysCityLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./SkillProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts.
debug: Resolve ./NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../login/SimpleLoginUI from file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts.
debug: Resolve ../../libs/crypto/crypto from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts.
debug: Resolve ../../libs/convert from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts.
debug: Resolve ../../libs/gzip/gzip from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../network/socket/NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../map/MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../core/LoaderManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts.
debug: Resolve ../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../map/ui/GeneralRosterLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ./UnionProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../libs/NameDict from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./FixedScreenAdapter from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./UnionMemberItemOpLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ../config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ../config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ./LocalCache from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Target(editor) ends with cost 5982.272899999982ms.
debug: Target(preview) build started.
debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:05 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:05 GMT+0800 (中国标准时间)
debug: Inspect cce:/internal/x/cc
debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:03:06 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:15 GMT+0800 (中国标准时间)
debug: Inspect cce:/internal/x/prerequisite-imports
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts.
debug: Resolve file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts from cce:/internal/x/prerequisite-imports as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts
debug: Inspect file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts as cce:/internal/x/cc.
debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Fri Aug 01 2025 02:14:56 GMT+0800 (中国标准时间), Current mtime: Fri Aug 01 2025 07:24:17 GMT+0800 (中国标准时间)
debug: Inspect cce:/internal/code-quality/cr.mjs
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/cloud/CloudAni.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/DialogOut.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/PanelOut.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/Basci.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/CoreEvent.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as cce:/internal/x/cc.
debug: Resolve cc/env from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as external dependency cc/env.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts as cce:/internal/x/cc.
debug: Resolve cc/env from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as external dependency cc/env.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/BgScale.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasAdapter.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/CanvasFixer.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Modal.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts as cce:/internal/x/cc.
debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts as cce:/internal/code-quality/cr.mjs.
debug: Resolve cc from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts as cce:/internal/x/cc.
debug: Resolve ./config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ./core/LoaderManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts.
debug: Resolve ./general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ./general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./map/ui/MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./network/http/HttpManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts.
debug: Resolve ./skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ./network/socket/NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ./network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ./network/socket/NetNode from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts.
debug: Resolve ./utils/Toast from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Toast.ts.
debug: Resolve ./common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./utils/Tools from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Resolve ./utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./utils/ClickTestHelper from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts.
debug: Resolve ./common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ./utils/FixedScreenAdapter from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts.
debug: Resolve ./test/SimpleLoginTest from file:///D:/CocosSLG/slgclient-main/assets/scripts/Main.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./ChatProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatProxy.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./ChatCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatCommand.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/chat/ChatLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../core/coreEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../utils/LocalCache from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ./GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ./GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./coreEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ./GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./rawinflate from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawinflate.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ./LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./rawdeflate from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/rawdeflate.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../libs/NameDict from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/CreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts.
debug: Resolve ./crc32 from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/crc32.ts.
debug: Resolve ./deflate-js from file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/deflate-js.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginFormController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginDialogController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/LocalCache from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Resolve ../config/HttpConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/HttpConfig.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../network/http/HttpManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts.
debug: Resolve ../utils/Tools from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Resolve ./LoginProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts.
debug: Resolve ../network/socket/NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/LocalCache from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Resolve ../libs/crypto/md5 from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/md5.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ./entries/ArmyLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts.
debug: Resolve ../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/MainMenuController.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./entries/BuildTagLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./entries/BuildTipsLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./entries/CityLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapBuildProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts.
debug: Resolve ./MapCityProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts.
debug: Resolve ./MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ./ui/MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapBuildProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildProxy.ts.
debug: Resolve ./MapCityProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ./entries/FacilityBuildLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./entries/ResBuildLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./entries/ResLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./MapBaseLayerLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBaseLayerLogic.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./entries/SysCityLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ArmyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ./MapClickUILogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapTouchLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTipsLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/BuildTagLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/CityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/FacilityBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResBuildLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/ResLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/entries/SysCityLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./ArmySelectItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectItemLogic.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ./CityArmyItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./CityGeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/Tools from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CollectLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityGeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./MapUIProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./FacilityAdditionItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityAdditionItemLogic.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./FacilityDesLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityDesLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./FacilityItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityItemLogic.ts.
debug: Resolve ./CityArmyItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmyItemLogic.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralAddPrLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralComposeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralConvertLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./SkillIconLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterListLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../general/GeneralProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralProxy.ts.
debug: Resolve ../../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ../../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./MapUIProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./CityArmySettingLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityArmySettingLogic.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../union/UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ./ArmySelectNodeLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/ArmySelectNodeLogic.ts.
debug: Resolve ./FacilityListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FacilityListLogic.ts.
debug: Resolve ./Dialog from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Dialog.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/Tools from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts.
debug: Resolve ./FortressAbout from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/FortressAbout.ts.
debug: Resolve ./CityAboutLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts.
debug: Resolve ./TransformLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts.
debug: Resolve ./GeneralListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralListLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./GeneralInfoLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralInfoLogic.ts.
debug: Resolve ./SkillLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./WarReportLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts.
debug: Resolve ./DrawRLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts.
debug: Resolve ./SkillInfoLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../general/ArmyProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyProxy.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUILogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ./GeneralHeadLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../general/ArmyCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/ArmyCommand.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./RightArmyItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightArmyItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ./RightTagItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts.
debug: Resolve ./RightCityItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightInfoNodeLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightCityItemLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/RightTagItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/Setting.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ./SkillIconLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillInfoLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../skill/SkillProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ./SkillIconLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillIconLogic.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SkillItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../union/UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/UnionButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/TransformLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./WarReportDesItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../skill/SkillCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../config/skill/Skill from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/skill/Skill.ts.
debug: Resolve ../../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarButtonLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ./WarReportDesLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportDesLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/SmallMapLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../socket/NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ./MapUICommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUICommand.ts.
debug: Resolve ../../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./GeneralItemLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralItemLogic.ts.
debug: Resolve ../../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/WarReportItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./HttpInvoke from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts.
debug: Resolve ./NetNode from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./WebSock from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts.
debug: Resolve ./NetTimer from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetNode.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts.
debug: Resolve ./NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetTimer.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../network/socket/NetInterface from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetInterface.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/LoginScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../map/MapResBuildLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResBuildLogic.ts.
debug: Resolve ../map/MapBuildTipsLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTipsLogic.ts.
debug: Resolve ../map/MapCityLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCityLogic.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../map/MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../map/MapLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapLogic.ts.
debug: Resolve ../map/MapUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapUtil.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../map/MapResLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapResLogic.ts.
debug: Resolve ../map/MapSysCityLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapSysCityLogic.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../map/MapFacilityBuildLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapFacilityBuildLogic.ts.
debug: Resolve ../map/MapBuildTagLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapBuildTagLogic.ts.
debug: Resolve ../core/coreEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/scene/MapScene.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/coreEvent.ts.
debug: Resolve ../../libs/crypto/crypto from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/crypto/crypto.ts.
debug: Resolve ../../libs/gzip/gzip from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/gzip/gzip.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../../libs/convert from file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/convert.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ./SkillProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillProxy.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/skill/SkillCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../core/LoaderManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/core/LoaderManager.ts.
debug: Resolve ../general/GeneralCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/general/GeneralCommand.ts.
debug: Resolve ../map/ui/GeneralRosterLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/GeneralTool.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralRosterLogic.ts.
debug: Resolve ../map/MapProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/tools/MapTool.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapProxy.ts.
debug: Resolve ../login/SimpleLoginUI from file:///D:/CocosSLG/slgclient-main/assets/scripts/test/SimpleLoginTest.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/SimpleLoginUI.ts.
debug: Resolve ../network/socket/NetManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/NetManager.ts.
debug: Resolve ./UnionProxy from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../config/ServerConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../libs/NameDict from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/libs/NameDict.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCreateLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionApplyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLobbyLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/DateUtil from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/DateUtil.ts.
debug: Resolve ../utils/ListLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ListLogic.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMainLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemItemLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../login/LoginCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ../map/MapCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapCommand.ts.
debug: Resolve ./UnionCommand from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionCommand.ts.
debug: Resolve ../utils/EventMgr from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/EventMgr.ts.
debug: Resolve ./UnionMemberItemOpLogic from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberItemOpLogic.ts.
debug: Resolve ./FixedScreenAdapter from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ClickTestHelper.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts.
debug: Resolve ../common/AudioManager from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/AudioManager.ts.
debug: Resolve ../common/LogicEvent from file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionMemberLogic.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts.
debug: Resolve ../config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/FixedScreenAdapter.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ../config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ResponsiveAdapter.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ../config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/ScreenAdapter.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ../config/GameConfig from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/SimpleScreenAdapter.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts.
debug: Resolve ./LocalCache from file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/Tools.ts as file:///D:/CocosSLG/slgclient-main/assets/scripts/utils/LocalCache.ts.
debug: Target(preview) ends with cost 6238.361499999999ms.
