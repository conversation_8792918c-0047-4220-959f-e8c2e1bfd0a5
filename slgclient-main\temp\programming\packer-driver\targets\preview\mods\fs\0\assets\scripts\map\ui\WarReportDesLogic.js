System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, ScrollView, Node, instantiate, AudioManager, WarReportDesItemLogic, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _temp, _crd, ccclass, property, WarReportDesLogic;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfAudioManager(extras) {
    _reporterNs.report("AudioManager", "../../common/AudioManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWarReport(extras) {
    _reporterNs.report("WarReport", "./MapUIProxy", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWarReportDesItemLogic(extras) {
    _reporterNs.report("WarReportDesItemLogic", "./WarReportDesItemLogic", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      ScrollView = _cc.ScrollView;
      Node = _cc.Node;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      AudioManager = _unresolved_2.AudioManager;
    }, function (_unresolved_3) {
      WarReportDesItemLogic = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3afac34OzVAlI0uuCzbHccw", "WarReportDesLogic", undefined);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", WarReportDesLogic = (_dec = ccclass('WarReportDesLogic'), _dec2 = property(ScrollView), _dec3 = property(Node), _dec(_class = (_class2 = (_temp = class WarReportDesLogic extends Component {
        constructor() {
          super(...arguments);

          _defineProperty(this, "_curData", null);

          _initializerDefineProperty(this, "scrollView", _descriptor, this);

          _initializerDefineProperty(this, "item", _descriptor2, this);

          _defineProperty(this, "_lastY", 0);

          _defineProperty(this, "_curNum", 0);
        }

        onLoad() {
          this.item.active = false;
          this.scrollView.node.on("scroll-to-bottom", this.scrollToBottom, this);
        }

        onEnable() {
          this.scrollView.scrollToTop();
        }

        setData(data) {
          this.scrollView.content.removeAllChildren();
          this._curData = data;
          this._curNum = 0;
          this.make();
          this.scrollView.scrollToTop();
        }

        make() {
          var max = Math.min(6, this._curData.rounds.length - this._curNum);

          for (var index = this._curNum; index < this._curNum + max; index++) {
            var r = this._curData.rounds[index];
            var item = instantiate(this.item);
            item.active = true;
            item.parent = this.scrollView.content;
            item.getComponent(_crd && WarReportDesItemLogic === void 0 ? (_reportPossibleCrUseOfWarReportDesItemLogic({
              error: Error()
            }), WarReportDesItemLogic) : WarReportDesItemLogic).setData(r, this._curData, index == this._curData.rounds.length - 1);
          }

          this._curNum += max;
        }

        onClickClose() {
          this.node.active = false;
          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick();
        }

        scrollToBottom() {
          console.log("scrollToBottom");
          this.make();
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "scrollView", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "item", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=WarReportDesLogic.js.map