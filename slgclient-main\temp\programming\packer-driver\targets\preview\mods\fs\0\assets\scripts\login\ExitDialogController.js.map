{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/ExitDialogController.ts"], "names": ["_decorator", "Component", "Node", "<PERSON><PERSON>", "Label", "game", "AudioManager", "ccclass", "property", "ExitDialogController", "onLoad", "console", "log", "initializeDialog", "setupEventListeners", "start", "messageLabel", "string", "confirmButton", "node", "on", "EventType", "CLICK", "onConfirmButtonClick", "cancelButton", "onCancelButtonClick", "maskNode", "TOUCH_END", "onMaskClick", "instance", "playClick", "scheduleOnce", "exitGame", "destroy", "window", "close", "alert", "end", "onDestroy", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC5CC,MAAAA,Y,iBAAAA,Y;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAE9B;AACA;AACA;AACA;;sCAEaS,oB,WADZF,OAAO,CAAC,sBAAD,C,UAGHC,QAAQ,CAACJ,KAAD,C,UAGRI,QAAQ,CAACL,MAAD,C,UAGRK,QAAQ,CAACL,MAAD,C,UAGRK,QAAQ,CAACN,IAAD,C,oCAZb,MACaO,oBADb,SAC0CR,SAD1C,CACoD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAchDS,QAAAA,MAAM,GAAG;AACLC,UAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ;AACA,eAAKC,gBAAL;AACA,eAAKC,mBAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJJ,UAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ;AACH;AAED;AACJ;AACA;;;AACYC,QAAAA,gBAAgB,GAAS;AAC7B;AACA,cAAI,KAAKG,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBC,MAAlB,GAA2B,yBAA3B;AACH;AACJ;AAED;AACJ;AACA;;;AACYH,QAAAA,mBAAmB,GAAS;AAChC,cAAI,KAAKI,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBC,IAAnB,CAAwBC,EAAxB,CAA2BjB,MAAM,CAACkB,SAAP,CAAiBC,KAA5C,EAAmD,KAAKC,oBAAxD,EAA8E,IAA9E;AACAZ,YAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ;AACH;;AAED,cAAI,KAAKY,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBL,IAAlB,CAAuBC,EAAvB,CAA0BjB,MAAM,CAACkB,SAAP,CAAiBC,KAA3C,EAAkD,KAAKG,mBAAvD,EAA4E,IAA5E;AACAd,YAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ;AACH,WAT+B,CAWhC;;;AACA,cAAI,KAAKc,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAcN,EAAd,CAAiBlB,IAAI,CAACmB,SAAL,CAAeM,SAAhC,EAA2C,KAAKC,WAAhD,EAA6D,IAA7D;AACAjB,YAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACYW,QAAAA,oBAAoB,GAAS;AACjCZ,UAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ,EADiC,CAGjC;;AACA;AAAA;AAAA,4CAAaiB,QAAb,CAAsBC,SAAtB,GAJiC,CAMjC;;AACAnB,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ,EAPiC,CASjC;;AACA,eAAKmB,YAAL,CAAkB,MAAM;AACpB,iBAAKC,QAAL;AACH,WAFD,EAEG,GAFH;AAGH;AAED;AACJ;AACA;;;AACYP,QAAAA,mBAAmB,GAAS;AAChCd,UAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ,EADgC,CAGhC;;AACA;AAAA;AAAA,4CAAaiB,QAAb,CAAsBC,SAAtB,GAJgC,CAMhC;;AACA,eAAKX,IAAL,CAAUc,OAAV;AACH;AAED;AACJ;AACA;;;AACYL,QAAAA,WAAW,GAAS;AACxBjB,UAAAA,OAAO,CAACC,GAAR,CAAY,uCAAZ,EADwB,CAGxB;;AACA;AAAA;AAAA,4CAAaiB,QAAb,CAAsBC,SAAtB,GAJwB,CAMxB;;AACA,eAAKX,IAAL,CAAUc,OAAV;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,QAAQ,GAAS;AACrBrB,UAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ,EADqB,CAGrB;;AACA,cAAI,OAAOsB,MAAP,KAAkB,WAAtB,EAAmC;AAC/B;AACA,gBAAIA,MAAM,CAACC,KAAX,EAAkB;AACdD,cAAAA,MAAM,CAACC,KAAP;AACH,aAFD,MAEO;AACH;AACAC,cAAAA,KAAK,CAAC,aAAD,CAAL;AACH;AACJ,WARD,MAQO;AACH;AACA/B,YAAAA,IAAI,CAACgC,GAAL;AACH;AACJ;;AAEDC,QAAAA,SAAS,GAAG;AACR;AACA,cAAI,KAAKpB,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmBC,IAAnB,CAAwBoB,GAAxB,CAA4BpC,MAAM,CAACkB,SAAP,CAAiBC,KAA7C,EAAoD,KAAKC,oBAAzD,EAA+E,IAA/E;AACH;;AACD,cAAI,KAAKC,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBL,IAAlB,CAAuBoB,GAAvB,CAA2BpC,MAAM,CAACkB,SAAP,CAAiBC,KAA5C,EAAmD,KAAKG,mBAAxD,EAA6E,IAA7E;AACH;;AACD,cAAI,KAAKC,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAca,GAAd,CAAkBrC,IAAI,CAACmB,SAAL,CAAeM,SAAjC,EAA4C,KAAKC,WAAjD,EAA8D,IAA9D;AACH;;AACDjB,UAAAA,OAAO,CAACC,GAAR,CAAY,qCAAZ;AACH;;AApI+C,O;;;;;iBAG1B,I;;;;;;;iBAGE,I;;;;;;;iBAGD,I;;;;;;;iBAGN,I", "sourcesContent": ["import { _decorator, Component, Node, Button, Label, game } from 'cc';\nimport { AudioManager } from '../common/AudioManager';\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 离开游戏对话框控制器\n * 处理离开游戏确认功能\n */\n@ccclass('ExitDialogController')\nexport class ExitDialogController extends Component {\n    \n    @property(Label)\n    messageLabel: Label = null!;\n    \n    @property(Button)\n    confirmButton: Button = null!;\n    \n    @property(Button)\n    cancelButton: Button = null!;\n\n    @property(Node)\n    maskNode: Node = null!;\n\n    onLoad() {\n        console.log('[ExitDialogController] 离开游戏对话框控制器加载');\n        this.initializeDialog();\n        this.setupEventListeners();\n    }\n\n    start() {\n        console.log('[ExitDialogController] 离开游戏对话框控制器启动');\n    }\n\n    /**\n     * 初始化对话框\n     */\n    private initializeDialog(): void {\n        // 设置询问文本\n        if (this.messageLabel) {\n            this.messageLabel.string = '确定要离开游戏吗？\\n离开后当前进度可能会丢失';\n        }\n    }\n\n    /**\n     * 设置事件监听器\n     */\n    private setupEventListeners(): void {\n        if (this.confirmButton) {\n            this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmButtonClick, this);\n            console.log('[ExitDialogController] 确认按钮事件监听器已设置');\n        }\n\n        if (this.cancelButton) {\n            this.cancelButton.node.on(Button.EventType.CLICK, this.onCancelButtonClick, this);\n            console.log('[ExitDialogController] 取消按钮事件监听器已设置');\n        }\n\n        // 设置遮罩点击事件（点击对话框外区域关闭）\n        if (this.maskNode) {\n            this.maskNode.on(Node.EventType.TOUCH_END, this.onMaskClick, this);\n            console.log('[ExitDialogController] 遮罩点击事件监听器已设置');\n        }\n    }\n\n    /**\n     * 确认离开按钮点击事件\n     */\n    private onConfirmButtonClick(): void {\n        console.log('[ExitDialogController] 确认离开按钮被点击');\n        \n        // 播放点击音效\n        AudioManager.instance.playClick();\n        \n        // 显示离开提示\n        console.log('[ExitDialogController] 玩家确认离开游戏');\n        \n        // 延迟一下再退出，让音效播放完\n        this.scheduleOnce(() => {\n            this.exitGame();\n        }, 0.2);\n    }\n\n    /**\n     * 再玩一会按钮点击事件\n     */\n    private onCancelButtonClick(): void {\n        console.log('[ExitDialogController] 再玩一会按钮被点击');\n\n        // 播放点击音效\n        AudioManager.instance.playClick();\n\n        // 关闭对话框\n        this.node.destroy();\n    }\n\n    /**\n     * 遮罩点击事件（点击对话框外区域关闭）\n     */\n    private onMaskClick(): void {\n        console.log('[ExitDialogController] 点击对话框外区域，关闭对话框');\n\n        // 播放点击音效\n        AudioManager.instance.playClick();\n\n        // 关闭对话框\n        this.node.destroy();\n    }\n\n    /**\n     * 退出游戏\n     */\n    private exitGame(): void {\n        console.log('[ExitDialogController] 正在退出游戏...');\n        \n        // 在不同平台上退出游戏\n        if (typeof window !== 'undefined') {\n            // Web平台\n            if (window.close) {\n                window.close();\n            } else {\n                // 如果无法关闭窗口，显示提示\n                alert('请手动关闭浏览器标签页');\n            }\n        } else {\n            // 原生平台\n            game.end();\n        }\n    }\n\n    onDestroy() {\n        // 清理事件监听器\n        if (this.confirmButton) {\n            this.confirmButton.node.off(Button.EventType.CLICK, this.onConfirmButtonClick, this);\n        }\n        if (this.cancelButton) {\n            this.cancelButton.node.off(Button.EventType.CLICK, this.onCancelButtonClick, this);\n        }\n        if (this.maskNode) {\n            this.maskNode.off(Node.EventType.TOUCH_END, this.onMaskClick, this);\n        }\n        console.log('[ExitDialogController] 离开游戏对话框控制器销毁');\n    }\n}\n"]}