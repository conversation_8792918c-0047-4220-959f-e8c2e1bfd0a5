{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawLogic.ts"], "names": ["_decorator", "Component", "Label", "GeneralCommand", "LoginCommand", "MapUICommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "DrawLogic", "onEnable", "on", "upateMyRoleRes", "updateRoleRes", "updateMyGenerals", "onDisable", "targetOff", "onClickClose", "node", "active", "instance", "playClick", "commonCfg", "getInstance", "proxy", "getCommonCfg", "roleResData", "getRoleResData", "labelOnce", "string", "draw_general_cost", "gold", "labelTen", "basic", "getBasicGeneral", "cnt", "getMyActiveGeneralCnt", "cntLab", "limit", "drawGeneralOnce", "drawGenerals", "emit", "showWaiting", "drawGeneralTen"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AAGzBC,MAAAA,c;;AAEAC,MAAAA,Y;;AACAC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OARH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;yBAWTW,S,WADpBF,OAAO,CAAC,WAAD,C,UAIHC,QAAQ,CAACR,KAAD,C,UAGRQ,QAAQ,CAACR,KAAD,C,UAGRQ,QAAQ,CAACR,KAAD,C,oCAVb,MACqBS,SADrB,SACuCV,SADvC,CACiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAanCW,QAAAA,QAAQ,GAAO;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,cAAvB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,gBAAvB,EAAyC,KAAKD,aAA9C,EAA6D,IAA7D;AACA,eAAKA,aAAL;AACH;;AAGSE,QAAAA,SAAS,GAAO;AACtB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESC,QAAAA,YAAY,GAAS;AAC3B,eAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;AACH;;AAGSR,QAAAA,aAAa,GAAO;AAC1B,cAAIS,SAA8B,GAAG;AAAA;AAAA,gDAAeC,WAAf,GAA6BC,KAA7B,CAAmCC,YAAnC,EAArC;AACA,cAAIC,WAAW,GAAG;AAAA;AAAA,4CAAaH,WAAb,GAA2BC,KAA3B,CAAiCG,cAAjC,EAAlB;AACA,eAAKC,SAAL,CAAeC,MAAf,GAAwB,QAAMP,SAAS,CAACQ,iBAAhB,GAAmC,GAAnC,GAAwCJ,WAAW,CAACK,IAA5E;AACA,eAAKC,QAAL,CAAcH,MAAd,GAAuB,QAAMP,SAAS,CAACQ,iBAAV,GAA8B,EAApC,GAAwC,GAAxC,GAA6CJ,WAAW,CAACK,IAAhF;AAEA,cAAIE,KAAK,GAAG;AAAA;AAAA,4CAAaV,WAAb,GAA2BC,KAA3B,CAAiCU,eAAjC,EAAZ;AACA,cAAIC,GAAG,GAAG;AAAA;AAAA,gDAAeZ,WAAf,GAA6BC,KAA7B,CAAmCY,qBAAnC,EAAV;AACA,eAAKC,MAAL,CAAYR,MAAZ,GAAqB,MAAMM,GAAN,GAAY,GAAZ,GAAkBF,KAAK,CAACK,KAAxB,GAAgC,GAArD;AACH;;AAISC,QAAAA,eAAe,GAAO;AAC5B;AAAA;AAAA,4CAAanB,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,gDAAeE,WAAf,GAA6BiB,YAA7B;AAEA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,WAAzB;AACH;;AAESC,QAAAA,cAAc,GAAO;AAC3B;AAAA;AAAA,4CAAavB,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,gDAAeE,WAAf,GAA6BiB,YAA7B,CAA0C,EAA1C;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,WAAzB;AACH;;AAtD4C,O;;;;;iBAI1B,I;;;;;;;iBAGD,I;;;;;;;iBAGF,I", "sourcesContent": ["\nimport { _decorator, Component, Label } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport GeneralCommand from \"../../general/GeneralCommand\";\nimport { GeneralCommonConfig } from \"../../general/GeneralProxy\";\nimport LoginCommand from \"../../login/LoginCommand\";\nimport Map<PERSON>Command from \"./MapUICommand\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('DrawLogic')\nexport default class DrawLogic extends Component {\n\n\n    @property(Label)\n    labelOnce: Label = null;\n\n    @property(Label)\n    labelTen: Label = null;\n\n    @property(Label)\n    cntLab: Label = null;\n    \n\n    protected onEnable():void{\n        EventMgr.on(LogicEvent.upateMyRoleRes, this.updateRoleRes, this);\n        EventMgr.on(LogicEvent.updateMyGenerals, this.updateRoleRes, this);\n        this.updateRoleRes();\n    }\n\n\n    protected onDisable():void{\n        EventMgr.targetOff(this);\n    }\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n\n    protected updateRoleRes():void{\n        let commonCfg: GeneralCommonConfig = GeneralCommand.getInstance().proxy.getCommonCfg();\n        var roleResData = LoginCommand.getInstance().proxy.getRoleResData();\n        this.labelOnce.string = \"消耗:\"+commonCfg.draw_general_cost +\"/\"+ roleResData.gold;\n        this.labelTen.string = \"消耗:\"+commonCfg.draw_general_cost * 10 +\"/\"+ roleResData.gold;\n\n        var basic = MapUICommand.getInstance().proxy.getBasicGeneral();\n        var cnt = GeneralCommand.getInstance().proxy.getMyActiveGeneralCnt();\n        this.cntLab.string = \"(\" + cnt + \"/\" + basic.limit + \")\";\n    }\n\n\n\n    protected drawGeneralOnce():void{\n        AudioManager.instance.playClick();\n        GeneralCommand.getInstance().drawGenerals();\n\n        EventMgr.emit(LogicEvent.showWaiting);\n    }\n\n    protected drawGeneralTen():void{\n        AudioManager.instance.playClick();\n        GeneralCommand.getInstance().drawGenerals(10);\n        EventMgr.emit(LogicEvent.showWaiting);\n    }\n\n \n}\n"]}