{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/MapUIProxy.ts"], "names": ["CityAddition", "CityAdditionType", "Facility", "FacilityConfig", "FacilityAdditionCfg", "FacilityOpenCondition", "FacilityUpLevel", "BasicGeneral", "WarReport", "WarReportSkill", "WarReportRound", "MapUIProxy", "LoginCommand", "DateUtil", "MapUICommand", "clear", "cost", "armyCnt", "vanguardCnt", "soldierCnt", "han", "qun", "wei", "shu", "wu", "durable", "isUping", "upLastTime", "isNeedUpdateLevel", "upTime", "cfg", "getInstance", "proxy", "getFacilityCfgByType", "type", "costTime", "upLevels", "level", "time", "leftTime", "Map", "clearData", "_warReport", "_myFacility", "_additions", "updateMyFacilityList", "cityId", "datas", "console", "log", "list", "set", "i", "length", "obj", "up_time", "updateMyFacility", "data", "has", "get", "facilityData", "updateMyCityAdditions", "addition", "for<PERSON>ach", "addValue", "index", "additions", "indexOf", "ArmyTeams", "values", "Cost", "SoldierLimit", "VanguardLimit", "HanAddition", "QunAddition", "WeiAddition", "ShuAddition", "WuAddition", "DealTaxRate", "taxRate", "Durable", "getMyFacilitys", "getMyAllFacilitys", "getMyFacilityByType", "getMyCityAddition", "getMyCityCost", "_basic", "city", "getMyCityMaxDurable", "getTransformRate", "transform_rate", "setAllFacilityCfg", "jsonAssets", "_facilityCfg", "main<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "otherJsons", "_name", "json", "push", "cfgData", "name", "_facilityAdditionCfg", "des", "value", "jsonList", "getFacilityUpLevelJsonList", "conditions", "j", "conditionData", "levels", "k", "upLevelData", "wood", "need", "iron", "grain", "stone", "key", "undefined", "getFacilityAdditionCfgByType", "isFacilityUnlock", "isUnlock", "setBasic", "getConscriptBaseCost", "conscript", "getDefenseSoldiers", "npc", "soilders", "getBasicGeneral", "general", "updateWarReports", "createWarReprot", "id", "updateWarReport", "attack_rid", "a_rid", "defense_rid", "d_rid", "beg_attack_general", "arrayToObject", "JSON", "parse", "b_a_general", "end_attack_general", "e_a_general", "end_attack_army", "e_a_army", "beg_attack_army", "b_a_army", "beg_defense_army", "b_d_army", "end_defense_army", "e_d_army", "beg_defense_general", "b_d_general", "end_defense_general", "e_d_general", "rounds", "createRoundsData", "error", "result", "defense_is_read", "d_is_read", "attack_is_read", "a_is_read", "is_read", "isReadObj", "destroy_durable", "destroy", "occupy", "x", "y", "ctime", "attack_generals", "defense_generals", "generals", "concat", "_list", "round", "b", "turn", "attack_id", "a_id", "defense_id", "d_id", "defense_loss", "d_loss", "attack", "getMatchGeneral", "defense", "isAttack", "defenseLoss", "a_bs", "s", "wrs", "fromId", "f_id", "toId", "t_id", "cfgId", "c_id", "lv", "includeEffect", "i_e", "effectValue", "e_v", "effectRound", "e_r", "kill", "attackBefore", "a_as", "attackAfter", "d_as", "defenseAfter", "arr", "temp", "physical_power", "order", "exp", "curArms", "hasPrPoint", "attack_distance", "force_added", "strategy_added", "defense_added", "speed_added", "destroy_added", "star_lv", "star", "updateWarRead", "isRead", "roleData", "getRoleData", "rid", "updateAllWarRead", "element", "getWarReport", "Array", "from", "sort", "sortIsRead", "backArr", "a", "isReadNum", "num"], "mappings": ";;;oEAMaA,Y,EA6BAC,gB,EA6BAC,Q,EAyBAC,c,EAUAC,mB,EAOAC,qB,EAMAC,e,EAWAC,Y,EAKAC,S,EA6BAC,c,EAWAC,c,EAeQC,U;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArLdC,MAAAA,Y;;AACAC,MAAAA,Q;;AACAC,MAAAA,Y;;;;;;;8BAEMd,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,wCACP,CADO;;AAAA,2CAEJ,CAFI;;AAAA;;AAAA;;AAAA,uCAKR,CALQ;;AAAA,uCAMR,CANQ;;AAAA,uCAOR,CAPQ;;AAAA,uCAQR,CARQ;;AAAA,sCAST,CATS;;AAAA,2CAUL,CAVK;;AAAA,2CAWL,CAXK;AAAA;;AAWH;AAGZe,QAAAA,KAAK,GAAS;AACjB,eAAKC,IAAL,GAAY,CAAZ;AACA,eAAKC,OAAL,GAAe,CAAf;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,GAAL,GAAW,CAAX;AACA,eAAKC,GAAL,GAAW,CAAX;AACA,eAAKC,GAAL,GAAW,CAAX;AACA,eAAKC,GAAL,GAAW,CAAX;AACA,eAAKC,EAAL,GAAU,CAAV;AACA,eAAKC,OAAL,GAAe,CAAf;AACH;;AAzBqB,O;;AA0BzB;AAED;;kCACaxB,gB,GAAN,MAAMA,gBAAN,CAAuB,E;AA4B9B;;;sBA5BaA,gB,aACgB,C;;sBADhBA,gB,UAEa,C;;sBAFbA,gB,eAGkB,C;;sBAHlBA,gB,WAIc,C;;sBAJdA,gB,aAKgB,C;;sBALhBA,gB,cAMiB,C;;sBANjBA,gB,WAOc,C;;sBAPdA,gB,mBAQsB,C;;sBARtBA,gB,kBASqB,C;;sBATrBA,gB,YAUe,E;;sBAVfA,gB,iBAWoB,E;;sBAXpBA,gB,iBAYoB,E;;sBAZpBA,gB,iBAaoB,E;;sBAbpBA,gB,iBAcoB,E;;sBAdpBA,gB,gBAemB,E;;sBAfnBA,gB,iBAgBoB,E;;sBAhBpBA,gB,UAiBa,E;;sBAjBbA,gB,UAkBa,E;;sBAlBbA,gB,WAmBc,E;;sBAnBdA,gB,WAoBc,E;;sBApBdA,gB,SAqBY,E;;sBArBZA,gB,iBAsBoB,E;;sBAtBpBA,gB,oBAuBuB,E;;sBAvBvBA,gB,kBAwBqB,E;;sBAxBrBA,gB,mBAyBsB,E;;0BAItBC,Q,GAAN,MAAMA,QAAN,CAAe;AAAA;AAAA,yCACF,CADE;;AAAA,wCAEH,CAFG;;AAAA,0CAGF,CAHE;AAAA;;AAGE;AAEbwB,QAAAA,OAAO,GAAW;AACrB,iBAAO,KAAKC,UAAL,KAAoB,CAA3B;AACH;;AAEMC,QAAAA,iBAAiB,GAAW;AAC/B,iBAAO,KAAKD,UAAL,KAAoB,CAA3B;AACH;;AAEMA,QAAAA,UAAU,GAAU;AACvB,cAAG,KAAKE,MAAL,GAAc,CAAjB,EAAmB;AACf,gBAAIC,GAAkB,GAAG;AAAA;AAAA,8CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,oBAAjC,CAAsD,KAAKC,IAA3D,CAAzB;AACA,gBAAIC,QAAQ,GAAGL,GAAG,CAACM,QAAJ,CAAa,KAAKC,KAAlB,EAAyBC,IAAxC;AACA,mBAAO;AAAA;AAAA,sCAASC,QAAT,CAAkB,CAAC,KAAKV,MAAL,GAAYM,QAAb,IAAuB,IAAzC,CAAP;AACH,WAJD,MAIK;AACD,mBAAO,CAAP;AACH;AACJ;;AArBiB,O;AAwBtB;;;gCACahC,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,wCACT,EADS;;AAAA,wCAET,CAFS;;AAAA,uCAGV,EAHU;;AAAA,6CAIF,EAJE;;AAAA,8CAKc,EALd;;AAAA,4CAMM,EANN;AAAA;;AAAA,O;AAS5B;;;qCACaC,mB,GAAN,MAAMA,mBAAN,CAA0B;AAAA;AAAA,wCACd,CADc;;AAAA,uCAEf,EAFe;;AAAA,yCAGb,EAHa;AAAA;;AAAA,O;AAMjC;;;uCACaC,qB,GAAN,MAAMA,qBAAN,CAA4B;AAAA;AAAA,wCAChB,CADgB;;AAAA,yCAEf,CAFe;AAAA;;AAAA,O;AAKnC;;;iCACaC,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAAA,yCACT,CADS;;AAAA,0CAEN,EAFM;;AAAA,wCAGV,CAHU;;AAAA,wCAIV,CAJU;;AAAA,yCAKT,CALS;;AAAA,yCAMT,CANS;;AAAA,0CAOR,CAPQ;;AAAA,wCAQX,CARW;AAAA;;AAAA,O;;8BAWhBC,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,yCACN,CADM;AAAA;;AAAA,O;;2BAKbC,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,sCACN,CADM;;AAAA,8CAEE,CAFF;;AAAA,+CAGG,CAHH;;AAAA,mDAKI,EALJ;;AAAA,oDAMK,EANL;;AAAA,mDAOI,EAPJ;;AAAA,oDAQK,EARL;;AAAA,0CAUF,CAVE;;AAAA,0CAWL,EAXK;;AAAA,kDAYO,KAZP;;AAAA,mDAaQ,KAbR;;AAAA,mDAcO,CAdP;;AAAA,0CAeF,CAfE;;AAAA,qCAgBP,CAhBO;;AAAA,qCAiBP,CAjBO;;AAAA,yCAkBH,CAlBG;;AAAA,sDAmBS,EAnBT;;AAAA,uDAoBU,EApBV;;AAAA,sDAsBS,EAtBT;;AAAA,uDAuBU,EAvBV;;AAAA,2CAyBA,KAzBA;AAAA;;AAAA,O;;gCA6BVC,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA,UAQV;;;AARU,O;;gCAWfC,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,sCACX,CADW;;AAAA,4CAEJ,KAFI;;AAAA,0CAGV,EAHU;;AAAA,2CAIT,EAJS;;AAAA,+CAMF,CANE;;AAAA,yCAOR,CAPQ;;AAAA,wCAQT,CARS;;AAAA,gDASQ,EATR;;AAAA,+CAUO,EAVP;;AAAA,gDAWQ,EAXR;AAAA;;AAAA,O;;yBAePC,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,+CACgC,IAAI6B,GAAJ,EADhC;;AAAA,gDAE0B,IAAIA,GAAJ,EAF1B;;AAAA,wDAGuC,IAAIA,GAAJ,EAHvC;;AAAA,8CAImB,IAAIA,GAAJ,EAJnB;;AAAA,8CAKsB,IAAIA,GAAJ,EALtB;;AAAA;AAAA;;AAQrBC,QAAAA,SAAS,GAAS;AACrB,eAAKC,UAAL,CAAgB3B,KAAhB;;AACA,eAAK4B,WAAL,CAAiB5B,KAAjB;;AACA,eAAK6B,UAAL,CAAgB7B,KAAhB;AACH;AAED;AACJ;AACA;AACA;;;AACW8B,QAAAA,oBAAoB,CAACC,MAAD,EAAiBC,KAAjB,EAAqC;AAC5DC,UAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCF,KAArC;AACA,cAAIG,IAA2B,GAAG,IAAIV,GAAJ,EAAlC;;AACA,eAAKG,WAAL,CAAiBQ,GAAjB,CAAqBL,MAArB,EAA6BI,IAA7B;;AACA,eAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,KAAK,CAACM,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACnC,gBAAIE,GAAG,GAAG,IAAIpD,QAAJ,EAAV;AACAoD,YAAAA,GAAG,CAACjB,KAAJ,GAAYU,KAAK,CAACK,CAAD,CAAL,CAASf,KAArB;AACAiB,YAAAA,GAAG,CAACpB,IAAJ,GAAWa,KAAK,CAACK,CAAD,CAAL,CAASlB,IAApB;AACAoB,YAAAA,GAAG,CAACzB,MAAJ,GAAakB,KAAK,CAACK,CAAD,CAAL,CAASG,OAAtB;AACAL,YAAAA,IAAI,CAACC,GAAL,CAASG,GAAG,CAACpB,IAAb,EAAmBoB,GAAnB;AACH;;AAEDN,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBC,IAArB;AACH;;AAEMM,QAAAA,gBAAgB,CAACV,MAAD,EAAiBW,IAAjB,EAAsC;AACzDT,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCQ,IAAjC;;AACA,cAAI,KAAKd,WAAL,CAAiBe,GAAjB,CAAqBZ,MAArB,CAAJ,EAAkC;AAC9B,gBAAII,IAA2B,GAAG,KAAKP,WAAL,CAAiBgB,GAAjB,CAAqBb,MAArB,CAAlC;;AACA,gBAAIc,YAAsB,GAAGV,IAAI,CAACS,GAAL,CAASF,IAAI,CAACvB,IAAd,CAA7B;;AACA,gBAAI0B,YAAY,IAAI,IAApB,EAA0B;AACtBA,cAAAA,YAAY,GAAG,IAAI1D,QAAJ,EAAf;AACAgD,cAAAA,IAAI,CAACC,GAAL,CAASM,IAAI,CAACvB,IAAd,EAAoB0B,YAApB;AACH;;AACDA,YAAAA,YAAY,CAACvB,KAAb,GAAqBoB,IAAI,CAACpB,KAA1B;AACAuB,YAAAA,YAAY,CAAC1B,IAAb,GAAoBuB,IAAI,CAACvB,IAAzB;AACA0B,YAAAA,YAAY,CAAC/B,MAAb,GAAsB4B,IAAI,CAACF,OAA3B;AACA,mBAAOK,YAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;;;AACOC,QAAAA,qBAAqB,CAACf,MAAD,EAA+B;AACvD,cAAI,KAAKH,WAAL,CAAiBe,GAAjB,CAAqBZ,MAArB,CAAJ,EAAkC;AAC9B,gBAAIgB,QAAsB,GAAG,IAA7B;;AACA,gBAAI,KAAKlB,UAAL,CAAgBc,GAAhB,CAAoBZ,MAApB,CAAJ,EAAiC;AAC7BgB,cAAAA,QAAQ,GAAG,KAAKlB,UAAL,CAAgBe,GAAhB,CAAoBb,MAApB,CAAX;AACH,aAFD,MAEO;AACHgB,cAAAA,QAAQ,GAAG,IAAI9D,YAAJ,EAAX;;AACA,mBAAK4C,UAAL,CAAgBO,GAAhB,CAAoBL,MAApB,EAA4BgB,QAA5B;AACH;;AACDA,YAAAA,QAAQ,CAAC/C,KAAT,GAR8B,CAQb;;AACjB,gBAAImC,IAA2B,GAAG,KAAKP,WAAL,CAAiBgB,GAAjB,CAAqBb,MAArB,CAAlC;;AACAI,YAAAA,IAAI,CAACa,OAAL,CAAa,CAACN,IAAD,EAAiBvB,IAAjB,KAAkC;AAC3C,kBAAIuB,IAAI,CAACpB,KAAL,GAAa,CAAjB,EAAoB;AAChB,oBAAIP,GAAmB,GAAG,KAAKG,oBAAL,CAA0BwB,IAAI,CAACvB,IAA/B,CAA1B;;AACA,oBAAIJ,GAAJ,EAAS;AACL,sBAAIkC,QAAgB,GAAG,CAAvB;AACA,sBAAIC,KAAa,GAAG,CAAC,CAArB;AACAA,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAACmE,SAAvC,CAAR;;AACA,sBAAIH,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAAC7C,OAAT,IAAoB+C,QAApB;AACH;;AACDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAACqE,IAAvC,CAAR;;AACA,sBAAIL,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAAC9C,IAAT,IAAiBgD,QAAjB;AACH;;AACDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAACsE,YAAvC,CAAR;;AACA,sBAAIN,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAAC3C,UAAT,IAAuB6C,QAAvB;AACH;;AACDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAACuE,aAAvC,CAAR;;AACA,sBAAIP,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAAC5C,WAAT,IAAwB8C,QAAxB;AACH;;AACDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAACwE,WAAvC,CAAR;;AACA,sBAAIR,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAAC1C,GAAT,IAAgB4C,QAAhB;AACH;;AACDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAACyE,WAAvC,CAAR;;AACA,sBAAIT,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAACzC,GAAT,IAAgB2C,QAAhB;AACH;;AACDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAAC0E,WAAvC,CAAR;;AACA,sBAAIV,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAACxC,GAAT,IAAgB0C,QAAhB;AACH;;AACDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAAC2E,WAAvC,CAAR;;AACA,sBAAIX,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAACvC,GAAT,IAAgByC,QAAhB;AACH;;AACDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAAC4E,UAAvC,CAAR;;AACA,sBAAIZ,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAACtC,EAAT,IAAewC,QAAf;AACH;;AAEDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAAC6E,WAAvC,CAAR;;AACA,sBAAIb,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAACiB,OAAT,IAAoBf,QAApB;AACH;;AAEDC,kBAAAA,KAAK,GAAGnC,GAAG,CAACoC,SAAJ,CAAcC,OAAd,CAAsBlE,gBAAgB,CAAC+E,OAAvC,CAAR;;AACA,sBAAIf,KAAK,IAAI,CAAC,CAAd,EAAiB;AACb;AACA;AACAD,oBAAAA,QAAQ,GAAGlC,GAAG,CAACM,QAAJ,CAAaqB,IAAI,CAACpB,KAAL,GAAa,CAA1B,EAA6BgC,MAA7B,CAAoCJ,KAApC,CAAX;AACAH,oBAAAA,QAAQ,CAACrC,OAAT,IAAoBuC,QAApB;AACH;AACJ;AACJ;AACJ,aA7ED;AA8EAhB,YAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCH,MAArC,EAA6CgB,QAA7C;AACA,mBAAOA,QAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWmB,QAAAA,cAAc,CAACnC,MAAc,GAAG,CAAlB,EAA4C;AAC7D,iBAAO,KAAKH,WAAL,CAAiBgB,GAAjB,CAAqBb,MAArB,CAAP;AACH;;AAEMoC,QAAAA,iBAAiB,GAAuC;AAC3D,iBAAO,KAAKvC,WAAZ;AACH;AAED;;;AACOwC,QAAAA,mBAAmB,CAACrC,MAAc,GAAG,CAAlB,EAAqBZ,IAAY,GAAG,CAApC,EAAiD;AACvE,cAAI,KAAKS,WAAL,CAAiBe,GAAjB,CAAqBZ,MAArB,CAAJ,EAAkC;AAC9B,gBAAII,IAA2B,GAAG,KAAKP,WAAL,CAAiBgB,GAAjB,CAAqBb,MAArB,CAAlC;;AACA,gBAAII,IAAI,CAACQ,GAAL,CAASxB,IAAT,CAAJ,EAAoB;AAChB,qBAAOgB,IAAI,CAACS,GAAL,CAASzB,IAAT,CAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;AAED;;;AACOkD,QAAAA,iBAAiB,CAACtC,MAAD,EAA+B;AACnD,cAAIgB,QAAsB,GAAG,IAA7B;;AACA,cAAI,KAAKlB,UAAL,CAAgBc,GAAhB,CAAoBZ,MAApB,CAAJ,EAAiC;AAC7BgB,YAAAA,QAAQ,GAAG,KAAKlB,UAAL,CAAgBe,GAAhB,CAAoBb,MAApB,CAAX;AACH,WAFD,MAEO;AACHgB,YAAAA,QAAQ,GAAG,IAAI9D,YAAJ,EAAX;;AACA,iBAAK4C,UAAL,CAAgBO,GAAhB,CAAoBL,MAApB,EAA4BgB,QAA5B;AACH;;AACD,iBAAOA,QAAP;AACH;;AAEMuB,QAAAA,aAAa,CAACvC,MAAD,EAAuB;AACvC,cAAIgB,QAAQ,GAAG,KAAKsB,iBAAL,CAAuBtC,MAAvB,CAAf;AACAE,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BH,MAA9B,EAAsCgB,QAAtC,EAAgD,KAAKwB,MAAL,CAAYC,IAAZ,CAAiBvE,IAAjE;AACA,iBAAO8C,QAAQ,CAAC9C,IAAT,GAAgB,KAAKsE,MAAL,CAAYC,IAAZ,CAAiBvE,IAAxC;AACH,SAzL2B,CA2L5B;;;AACOwE,QAAAA,mBAAmB,CAAC1C,MAAD,EAAuB;AAC7C,cAAIgB,QAAQ,GAAG,KAAKsB,iBAAL,CAAuBtC,MAAvB,CAAf;AACAE,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoCH,MAApC,EAA4CgB,QAA5C,EAAsD,KAAKwB,MAAL,CAAYC,IAAZ,CAAiB9D,OAAvE;AACA,iBAAOqC,QAAQ,CAACrC,OAAT,GAAmB,KAAK6D,MAAL,CAAYC,IAAZ,CAAiB9D,OAA3C;AACH;;AAEMgE,QAAAA,gBAAgB,GAAU;AAC7B,iBAAO,KAAKH,MAAL,CAAYC,IAAZ,CAAiBG,cAAxB;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,iBAAiB,CAACC,UAAD,EAA0B;AAC9C,eAAKC,YAAL,GAAoB,IAAIrD,GAAJ,EAApB;AAEA,cAAIsD,QAAa,GAAG,IAApB,CAH8C,CAGrB;;AACzB,cAAIC,YAAiB,GAAG,IAAxB,CAJ8C,CAIjB;;AAC7B,cAAIC,UAAiB,GAAG,EAAxB,CAL8C,CAKnB;;AAE3B,eAAK,IAAI5C,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGwC,UAAU,CAACvC,MAAvC,EAA+CD,CAAC,EAAhD,EAAoD;AAChD,gBAAIwC,UAAU,CAACxC,CAAD,CAAV,CAAc6C,KAAd,IAAuB,UAA3B,EAAuC;AACnCH,cAAAA,QAAQ,GAAGF,UAAU,CAACxC,CAAD,CAAV,CAAc8C,IAAzB;AACH,aAFD,MAEO,IAAIN,UAAU,CAACxC,CAAD,CAAV,CAAc6C,KAAd,IAAuB,mBAA3B,EAAgD;AACnDF,cAAAA,YAAY,GAAGH,UAAU,CAACxC,CAAD,CAAV,CAAc8C,IAA7B;AACH,aAFM,MAEA;AACHF,cAAAA,UAAU,CAACG,IAAX,CAAgBP,UAAU,CAACxC,CAAD,CAAV,CAAc8C,IAA9B;AACH;AACJ;;AACDlD,UAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwB6C,QAAxB,EAAkCC,YAAlC;;AACA,cAAID,QAAQ,IAAI,IAAZ,IAAoBC,YAAY,IAAI,IAAxC,EAA8C;AAC1C;AACA,iBAAKF,YAAL,CAAkB9E,KAAlB;;AACA,iBAAK,IAAIqC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG0C,QAAQ,CAAC5C,IAAT,CAAcG,MAA1C,EAAkDD,CAAC,EAAnD,EAAuD;AACnD,kBAAIgD,OAAuB,GAAG,IAAIjG,cAAJ,EAA9B;AACAiG,cAAAA,OAAO,CAAClE,IAAR,GAAe4D,QAAQ,CAAC5C,IAAT,CAAcE,CAAd,EAAiBlB,IAAhC;AACAkE,cAAAA,OAAO,CAACC,IAAR,GAAeP,QAAQ,CAAC5C,IAAT,CAAcE,CAAd,EAAiBiD,IAAhC;;AACA,mBAAKR,YAAL,CAAkB1C,GAAlB,CAAsBiD,OAAO,CAAClE,IAA9B,EAAoCkE,OAApC;AACH;;AAED,iBAAKE,oBAAL,CAA0BvF,KAA1B;;AACA,iBAAK,IAAIqC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG2C,YAAY,CAAC7C,IAAb,CAAkBG,MAA9C,EAAsDD,CAAC,EAAvD,EAA2D;AACvD,kBAAIgD,OAA4B,GAAG,IAAIhG,mBAAJ,EAAnC;AACAgG,cAAAA,OAAO,CAAClE,IAAR,GAAe6D,YAAY,CAAC7C,IAAb,CAAkBE,CAAlB,EAAqBlB,IAApC;AACAkE,cAAAA,OAAO,CAACG,GAAR,GAAcR,YAAY,CAAC7C,IAAb,CAAkBE,CAAlB,EAAqBmD,GAAnC;AACAH,cAAAA,OAAO,CAACI,KAAR,GAAgBT,YAAY,CAAC7C,IAAb,CAAkBE,CAAlB,EAAqBoD,KAArC;;AACA,mBAAKF,oBAAL,CAA0BnD,GAA1B,CAA8BiD,OAAO,CAAClE,IAAtC,EAA4CkE,OAA5C;AACH;;AAED,gBAAIK,QAAe,GAAG,EAAtB;;AACA,iBAAK,IAAIrD,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG4C,UAAU,CAAC3C,MAAvC,EAA+CD,CAAC,EAAhD,EAAoD;AAChD,mBAAKsD,0BAAL,CAAgCV,UAAU,CAAC5C,CAAD,CAA1C,EAA+CqD,QAA/C;AACH;;AACDzD,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBwD,QAAxB,EAAkCT,UAAlC;;AAEA,iBAAK,IAAI5C,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGqD,QAAQ,CAACpD,MAArC,EAA6CD,CAAC,EAA9C,EAAkD;AAC9C,kBAAIlB,IAAY,GAAGuE,QAAQ,CAACrD,CAAD,CAAR,CAAYlB,IAA/B;;AACA,kBAAIkE,OAAuB,GAAG,KAAKP,YAAL,CAAkBlC,GAAlB,CAAsBzB,IAAtB,CAA9B;;AACA,kBAAIkE,OAAJ,EAAa;AACT;AACAA,gBAAAA,OAAO,CAACG,GAAR,GAAcE,QAAQ,CAACrD,CAAD,CAAR,CAAYmD,GAA1B;AACAH,gBAAAA,OAAO,CAAClC,SAAR,GAAoBuC,QAAQ,CAACrD,CAAD,CAAR,CAAYc,SAAhC;AACAkC,gBAAAA,OAAO,CAACO,UAAR,GAAqB,EAArB;;AACA,qBAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGH,QAAQ,CAACrD,CAAD,CAAR,CAAYuD,UAAZ,CAAuBtD,MAAnD,EAA2DuD,CAAC,EAA5D,EAAgE;AAC5D,sBAAIC,aAAoC,GAAG,IAAIxG,qBAAJ,EAA3C;AACAwG,kBAAAA,aAAa,CAAC3E,IAAd,GAAqBuE,QAAQ,CAACrD,CAAD,CAAR,CAAYuD,UAAZ,CAAuBC,CAAvB,EAA0B1E,IAA/C;AACA2E,kBAAAA,aAAa,CAACxE,KAAd,GAAsBoE,QAAQ,CAACrD,CAAD,CAAR,CAAYuD,UAAZ,CAAuBC,CAAvB,EAA0BvE,KAAhD;AACA+D,kBAAAA,OAAO,CAACO,UAAR,CAAmBR,IAAnB,CAAwBU,aAAxB;AACH;;AAEDT,gBAAAA,OAAO,CAAChE,QAAR,CAAiBiB,MAAjB,GAA0BoD,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBzD,MAA7C;;AACA,qBAAK,IAAI0D,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGN,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBzD,MAA/C,EAAuD0D,CAAC,EAAxD,EAA4D;AACxD,sBAAIC,WAA4B,GAAG,IAAI1G,eAAJ,EAAnC;AACA0G,kBAAAA,WAAW,CAAC3E,KAAZ,GAAoBoE,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBC,CAAnB,EAAsB1E,KAA1C;AACA2E,kBAAAA,WAAW,CAAC3C,MAAZ,GAAqBoC,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBC,CAAnB,EAAsB1C,MAA3C;AACA2C,kBAAAA,WAAW,CAACC,IAAZ,GAAmBR,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBC,CAAnB,EAAsBG,IAAtB,CAA2BD,IAA9C;AACAD,kBAAAA,WAAW,CAACG,IAAZ,GAAmBV,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBC,CAAnB,EAAsBG,IAAtB,CAA2BC,IAA9C;AACAH,kBAAAA,WAAW,CAACI,KAAZ,GAAoBX,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBC,CAAnB,EAAsBG,IAAtB,CAA2BE,KAA/C;AACAJ,kBAAAA,WAAW,CAACC,IAAZ,GAAmBR,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBC,CAAnB,EAAsBG,IAAtB,CAA2BD,IAA9C;AACAD,kBAAAA,WAAW,CAACK,KAAZ,GAAoBZ,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBC,CAAnB,EAAsBG,IAAtB,CAA2BG,KAA/C;AACAL,kBAAAA,WAAW,CAAC1E,IAAZ,GAAmBmE,QAAQ,CAACrD,CAAD,CAAR,CAAY0D,MAAZ,CAAmBC,CAAnB,EAAsBzE,IAAzC;AACA8D,kBAAAA,OAAO,CAAChE,QAAR,CAAiB4E,WAAW,CAAC3E,KAAZ,GAAoB,CAArC,IAA0C2E,WAA1C,CAVwD,CAYxD;AACH;AACJ;AACJ;AACJ;;AACD,eAAKnB,YAAL,CAAkB9B,OAAlB,CAA0B,CAACyC,KAAD,EAAwBc,GAAxB,KAAwC;AAC9DtE,YAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCqE,GAAjC,EAAsCd,KAAtC;AACH,WAFD;AAGH;;AAESE,QAAAA,0BAA0B,CAACjD,IAAD,EAAYP,IAAZ,EAA+B;AAC/D,cAAI,OAAQO,IAAR,IAAiB,QAAjB,IAA6B,OAAQA,IAAR,IAAiB,QAAlD,EAA4D;AACxD;AACH;;AACD,cAAIA,IAAI,CAACvB,IAAL,IAAaqF,SAAb,IAA0B9D,IAAI,CAACS,SAAL,IAAkBqD,SAAhD,EAA2D;AACvD;AACArE,YAAAA,IAAI,CAACiD,IAAL,CAAU1C,IAAV;AACH,WAHD,MAGO;AACH;AACA,iBAAK,IAAI6D,GAAT,IAAgB7D,IAAhB,EAAsB;AAClB,mBAAKiD,0BAAL,CAAgCjD,IAAI,CAAC6D,GAAD,CAApC,EAA2CpE,IAA3C;AACH;AACJ;AACJ;;AAEMjB,QAAAA,oBAAoB,CAACC,IAAY,GAAG,CAAhB,EAAmC;AAC1D,iBAAO,KAAK2D,YAAL,CAAkBlC,GAAlB,CAAsBzB,IAAtB,CAAP;AACH;;AAEMsF,QAAAA,4BAA4B,CAACtF,IAAY,GAAG,CAAhB,EAAwC;AACvE,iBAAO,KAAKoE,oBAAL,CAA0B3C,GAA1B,CAA8BzB,IAA9B,CAAP;AACH;AAED;;;AACOuF,QAAAA,gBAAgB,CAAC3E,MAAD,EAAiBZ,IAAjB,EAAwC;AAC3D,cAAIwF,QAAiB,GAAG,IAAxB;AACA,cAAI5F,GAAmB,GAAG,KAAKG,oBAAL,CAA0BC,IAA1B,CAA1B;;AACA,eAAK,IAAIkB,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGtB,GAAG,CAAC6E,UAAJ,CAAetD,MAA3C,EAAmDD,CAAC,EAApD,EAAwD;AACpD,gBAAIK,IAAc,GAAG,KAAK0B,mBAAL,CAAyBrC,MAAzB,EAAiChB,GAAG,CAAC6E,UAAJ,CAAevD,CAAf,EAAkBlB,IAAnD,CAArB;;AACA,gBAAIuB,IAAI,IAAIA,IAAI,CAACpB,KAAL,GAAaP,GAAG,CAAC6E,UAAJ,CAAevD,CAAf,EAAkBf,KAA3C,EAAkD;AAC9CqF,cAAAA,QAAQ,GAAG,KAAX;AACA;AACH;AACJ;;AACD,iBAAOA,QAAP;AACH;;AAEMC,QAAAA,QAAQ,CAAClE,IAAD,EAAkB;AAC7B,eAAK6B,MAAL,GAAc7B,IAAI,CAACyC,IAAnB;AACAlD,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAKqC,MAA9B;AACH;;AAGMsC,QAAAA,oBAAoB,GAAc;AACrC,iBAAO,KAAKtC,MAAL,CAAYuC,SAAnB;AACH;;AAEMC,QAAAA,kBAAkB,CAACzF,KAAD,EAAuB;AAC5CW,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCZ,KAAnC;AACA,iBAAO,KAAKiD,MAAL,CAAYyC,GAAZ,CAAgBjB,MAAhB,CAAuBzE,KAAK,GAAC,CAA7B,EAAgC2F,QAAvC;AACH;;AAEMC,QAAAA,eAAe,GAAY;AAC9B,iBAAO,KAAK3C,MAAL,CAAY4C,OAAnB;AACH;;AAGMC,QAAAA,gBAAgB,CAAC1E,IAAD,EAAkB;AACrC,cAAIP,IAAI,GAAGO,IAAI,CAACP,IAAhB;;AACA,eAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAACG,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;AAClC,gBAAIE,GAAc,GAAG,KAAK8E,eAAL,CAAqBlF,IAAI,CAACE,CAAD,CAAzB,CAArB;;AACA,iBAAKV,UAAL,CAAgBS,GAAhB,CAAoBG,GAAG,CAAC+E,EAAxB,EAA4B/E,GAA5B;AACH;AACJ;;AAEMgF,QAAAA,eAAe,CAAC7E,IAAD,EAAkB;AACpC,cAAIH,GAAc,GAAG,KAAK8E,eAAL,CAAqB3E,IAArB,CAArB;;AACA,eAAKf,UAAL,CAAgBS,GAAhB,CAAoBG,GAAG,CAAC+E,EAAxB,EAA4B/E,GAA5B;AACH;;AAGS8E,QAAAA,eAAe,CAAC3E,IAAD,EAAuB;AAE5C,cAAIH,GAAG,GAAG,IAAI9C,SAAJ,EAAV;AACA8C,UAAAA,GAAG,CAAC+E,EAAJ,GAAS5E,IAAI,CAAC4E,EAAd;AACA/E,UAAAA,GAAG,CAACiF,UAAJ,GAAiB9E,IAAI,CAAC+E,KAAtB;AACAlF,UAAAA,GAAG,CAACmF,WAAJ,GAAkBhF,IAAI,CAACiF,KAAvB;AAEApF,UAAAA,GAAG,CAACqF,kBAAJ,GAAyB,KAAKC,aAAL,CAAmBC,IAAI,CAACC,KAAL,CAAWrF,IAAI,CAACsF,WAAhB,CAAnB,CAAzB;AACAzF,UAAAA,GAAG,CAAC0F,kBAAJ,GAAyB,KAAKJ,aAAL,CAAmBC,IAAI,CAACC,KAAL,CAAWrF,IAAI,CAACwF,WAAhB,CAAnB,CAAzB;AACA3F,UAAAA,GAAG,CAAC4F,eAAJ,GAAsBL,IAAI,CAACC,KAAL,CAAWrF,IAAI,CAAC0F,QAAhB,CAAtB;AACA7F,UAAAA,GAAG,CAAC8F,eAAJ,GAAsBP,IAAI,CAACC,KAAL,CAAWrF,IAAI,CAAC4F,QAAhB,CAAtB;;AAEA,cAAI;AACA/F,YAAAA,GAAG,CAACgG,gBAAJ,GAAuBT,IAAI,CAACC,KAAL,CAAWrF,IAAI,CAAC8F,QAAhB,CAAvB;AACAjG,YAAAA,GAAG,CAACkG,gBAAJ,GAAuBX,IAAI,CAACC,KAAL,CAAWrF,IAAI,CAACgG,QAAhB,CAAvB;AACAnG,YAAAA,GAAG,CAACoG,mBAAJ,GAA0B,KAAKd,aAAL,CAAmBC,IAAI,CAACC,KAAL,CAAWrF,IAAI,CAACkG,WAAhB,CAAnB,CAA1B;AACArG,YAAAA,GAAG,CAACsG,mBAAJ,GAA0B,KAAKhB,aAAL,CAAmBC,IAAI,CAACC,KAAL,CAAWrF,IAAI,CAACoG,WAAhB,CAAnB,CAA1B;AACAvG,YAAAA,GAAG,CAACwG,MAAJ,GAAa,KAAKC,gBAAL,CAAsBtG,IAAI,CAACqG,MAA3B,EAAmCxG,GAAG,CAACqF,kBAAvC,EAA2DrF,GAAG,CAACoG,mBAA/D,CAAb;AACH,WAND,CAME,OAAOM,KAAP,EAAc;AACZhH,YAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC+G,KAAhC;AACH;;AAGD1G,UAAAA,GAAG,CAAC2G,MAAJ,GAAaxG,IAAI,CAACwG,MAAlB;AACA3G,UAAAA,GAAG,CAAC4G,eAAJ,GAAsBzG,IAAI,CAAC0G,SAA3B;AACA7G,UAAAA,GAAG,CAAC8G,cAAJ,GAAqB3G,IAAI,CAAC4G,SAA1B;AAEA/G,UAAAA,GAAG,CAACgH,OAAJ,GAAc,KAAKC,SAAL,CAAejH,GAAf,CAAd;AACAA,UAAAA,GAAG,CAACkH,eAAJ,GAAsB/G,IAAI,CAACgH,OAA3B;AACAnH,UAAAA,GAAG,CAACoH,MAAJ,GAAajH,IAAI,CAACiH,MAAlB;AACApH,UAAAA,GAAG,CAACqH,CAAJ,GAAQlH,IAAI,CAACkH,CAAb;AACArH,UAAAA,GAAG,CAACsH,CAAJ,GAAQnH,IAAI,CAACmH,CAAb;AACAtH,UAAAA,GAAG,CAACuH,KAAJ,GAAYpH,IAAI,CAACoH,KAAjB;AAEA,iBAAOvH,GAAP;AACH;;AAGOyG,QAAAA,gBAAgB,CAACtG,IAAD,EAAeqH,eAAf,EAAuCC,gBAAvC,EAAuE;AAC3F,cAAIC,QAAe,GAAGF,eAAe,CAACG,MAAhB,CAAuBF,gBAAvB,CAAtB;AAEA,cAAIG,KAAY,GAAG,EAAnB;AACA,cAAIpB,MAAa,GAAGjB,IAAI,CAACC,KAAL,CAAWrF,IAAX,CAApB;;AACA,eAAK,IAAIL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0G,MAAM,CAACzG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACpC,gBAAI+H,KAAY,GAAGrB,MAAM,CAAC1G,CAAD,CAAN,CAAUgI,CAA7B;;AAEA,gBAAG,CAACD,KAAJ,EAAU;AACN;AACH;;AAED,iBAAK,IAAIvE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuE,KAAK,CAAC9H,MAA1B,EAAkCuD,CAAC,EAAnC,EAAuC;AACnC,kBAAIyE,IAAI,GAAGF,KAAK,CAACvE,CAAD,CAAhB;AACA,kBAAI0E,SAAS,GAAGD,IAAI,CAACE,IAArB;AACA,kBAAIC,UAAU,GAAGH,IAAI,CAACI,IAAtB;AACA,kBAAIC,YAAY,GAAGL,IAAI,CAACM,MAAxB;AAEA,kBAAIrI,GAAG,GAAG,IAAI5C,cAAJ,EAAV;AACA4C,cAAAA,GAAG,CAACsI,MAAJ,GAAa,KAAKC,eAAL,CAAqBb,QAArB,EAA+BM,SAA/B,CAAb;AACAhI,cAAAA,GAAG,CAACwI,OAAJ,GAAc,KAAKD,eAAL,CAAqBb,QAArB,EAA+BQ,UAA/B,CAAd;AACAlI,cAAAA,GAAG,CAACyI,QAAJ,GAAe,KAAKF,eAAL,CAAqBf,eAArB,EAAsCQ,SAAtC,KAAoD,IAAnE;AACAhI,cAAAA,GAAG,CAAC0I,WAAJ,GAAkBN,YAAlB;;AAEA,kBAAGL,IAAI,CAACY,IAAR,EAAa;AACT,oBAAIA,IAAI,GAAGZ,IAAI,CAACY,IAAhB;;AACA,qBAAK,IAAIhI,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGgI,IAAI,CAAC5I,MAAjC,EAAyCY,KAAK,EAA9C,EAAkD;AAC9C,sBAAIiI,CAAC,GAAGD,IAAI,CAAChI,KAAD,CAAZ;AACA,sBAAIkI,GAAG,GAAG,IAAI1L,cAAJ,EAAV;AACA0L,kBAAAA,GAAG,CAACC,MAAJ,GAAaF,CAAC,CAACG,IAAf;AACAF,kBAAAA,GAAG,CAACG,IAAJ,GAAWJ,CAAC,CAACK,IAAb;AACAJ,kBAAAA,GAAG,CAACK,KAAJ,GAAYN,CAAC,CAACO,IAAd;AACAN,kBAAAA,GAAG,CAACO,EAAJ,GAASR,CAAC,CAACQ,EAAX;AACAP,kBAAAA,GAAG,CAACQ,aAAJ,GAAoBT,CAAC,CAACU,GAAtB;AACAT,kBAAAA,GAAG,CAACU,WAAJ,GAAkBX,CAAC,CAACY,GAApB;AACAX,kBAAAA,GAAG,CAACY,WAAJ,GAAkBb,CAAC,CAACc,GAApB;AACAb,kBAAAA,GAAG,CAACc,IAAJ,GAAWf,CAAC,CAACe,IAAb;AACA3J,kBAAAA,GAAG,CAAC4J,YAAJ,CAAiB/G,IAAjB,CAAsBgG,GAAtB;AACH;AAEJ;;AAED,kBAAGd,IAAI,CAAC8B,IAAR,EAAa;AACT,oBAAIA,IAAI,GAAG9B,IAAI,CAAC8B,IAAhB;;AACA,qBAAK,IAAIlJ,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGkJ,IAAI,CAAC9J,MAAjC,EAAyCY,KAAK,EAA9C,EAAkD;AAC9C,sBAAIiI,CAAC,GAAGiB,IAAI,CAAClJ,KAAD,CAAZ;AACA,sBAAIkI,GAAG,GAAG,IAAI1L,cAAJ,EAAV;AACA0L,kBAAAA,GAAG,CAACC,MAAJ,GAAaF,CAAC,CAACG,IAAf;AACAF,kBAAAA,GAAG,CAACG,IAAJ,GAAWJ,CAAC,CAACK,IAAb;AACAJ,kBAAAA,GAAG,CAACK,KAAJ,GAAYN,CAAC,CAACO,IAAd;AACAN,kBAAAA,GAAG,CAACO,EAAJ,GAASR,CAAC,CAACQ,EAAX;AACAP,kBAAAA,GAAG,CAACQ,aAAJ,GAAoBT,CAAC,CAACU,GAAtB;AACAT,kBAAAA,GAAG,CAACU,WAAJ,GAAkBX,CAAC,CAACY,GAApB;AACAX,kBAAAA,GAAG,CAACY,WAAJ,GAAkBb,CAAC,CAACc,GAApB;AACAb,kBAAAA,GAAG,CAACc,IAAJ,GAAWf,CAAC,CAACe,IAAb;AACA3J,kBAAAA,GAAG,CAAC8J,WAAJ,CAAgBjH,IAAhB,CAAqBgG,GAArB;AACH;AACJ;;AAED,kBAAGd,IAAI,CAACgC,IAAR,EAAa;AACT,oBAAIA,IAAI,GAAGhC,IAAI,CAACgC,IAAhB;;AACA,qBAAK,IAAIpJ,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGoJ,IAAI,CAAChK,MAAjC,EAAyCY,KAAK,EAA9C,EAAkD;AAC9C,sBAAIiI,CAAC,GAAGmB,IAAI,CAACpJ,KAAD,CAAZ;AACA,sBAAIkI,GAAG,GAAG,IAAI1L,cAAJ,EAAV;AACA0L,kBAAAA,GAAG,CAACC,MAAJ,GAAaF,CAAC,CAACG,IAAf;AACAF,kBAAAA,GAAG,CAACG,IAAJ,GAAWJ,CAAC,CAACK,IAAb;AACAJ,kBAAAA,GAAG,CAACK,KAAJ,GAAYN,CAAC,CAACO,IAAd;AACAN,kBAAAA,GAAG,CAACO,EAAJ,GAASR,CAAC,CAACQ,EAAX;AACAP,kBAAAA,GAAG,CAACQ,aAAJ,GAAoBT,CAAC,CAACU,GAAtB;AACAT,kBAAAA,GAAG,CAACU,WAAJ,GAAkBX,CAAC,CAACY,GAApB;AACAX,kBAAAA,GAAG,CAACY,WAAJ,GAAkBb,CAAC,CAACc,GAApB;AACAb,kBAAAA,GAAG,CAACc,IAAJ,GAAWf,CAAC,CAACe,IAAb;AACA3J,kBAAAA,GAAG,CAACgK,YAAJ,CAAiBnH,IAAjB,CAAsBgG,GAAtB;AACH;AACJ;;AAED7I,cAAAA,GAAG,CAAC6H,KAAJ,GAAY/H,CAAC,GAAG,CAAhB;AACAE,cAAAA,GAAG,CAAC+H,IAAJ,GAAWzE,CAAC,GAAG,CAAf;;AACAsE,cAAAA,KAAK,CAAC/E,IAAN,CAAW7C,GAAX;AACH;AAEJ;;AAGD,iBAAO4H,KAAP;AACH;;AAGStC,QAAAA,aAAa,CAAC2E,GAAD,EAAgB;AACnC,cAAIC,IAAS,GAAG,EAAhB;;AACA,eAAK,IAAIpK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmK,GAAG,CAAClK,MAAxB,EAAgCD,CAAC,EAAjC,EAAqC;AACjC,gBAAIK,IAAI,GAAG8J,GAAG,CAACnK,CAAD,CAAd;AAEA,gBAAIE,GAAQ,GAAG,EAAf;AACAA,YAAAA,GAAG,CAAC+E,EAAJ,GAAS5E,IAAI,CAAC,CAAD,CAAb;AACAH,YAAAA,GAAG,CAACkJ,KAAJ,GAAY/I,IAAI,CAAC,CAAD,CAAhB;AACAH,YAAAA,GAAG,CAACmK,cAAJ,GAAqBhK,IAAI,CAAC,CAAD,CAAzB;AACAH,YAAAA,GAAG,CAACoK,KAAJ,GAAYjK,IAAI,CAAC,CAAD,CAAhB;AACAH,YAAAA,GAAG,CAACjB,KAAJ,GAAYoB,IAAI,CAAC,CAAD,CAAhB;AACAH,YAAAA,GAAG,CAACqK,GAAJ,GAAUlK,IAAI,CAAC,CAAD,CAAd;AACAH,YAAAA,GAAG,CAACR,MAAJ,GAAaW,IAAI,CAAC,CAAD,CAAjB;AACAH,YAAAA,GAAG,CAACsK,OAAJ,GAAcnK,IAAI,CAAC,CAAD,CAAlB;AACAH,YAAAA,GAAG,CAACuK,UAAJ,GAAiBpK,IAAI,CAAC,CAAD,CAArB;AACAH,YAAAA,GAAG,CAACwK,eAAJ,GAAsBrK,IAAI,CAAC,CAAD,CAA1B;AACAH,YAAAA,GAAG,CAACyK,WAAJ,GAAkBtK,IAAI,CAAC,EAAD,CAAtB;AACAH,YAAAA,GAAG,CAAC0K,cAAJ,GAAqBvK,IAAI,CAAC,EAAD,CAAzB;AACAH,YAAAA,GAAG,CAAC2K,aAAJ,GAAoBxK,IAAI,CAAC,EAAD,CAAxB;AACAH,YAAAA,GAAG,CAAC4K,WAAJ,GAAkBzK,IAAI,CAAC,EAAD,CAAtB;AACAH,YAAAA,GAAG,CAAC6K,aAAJ,GAAoB1K,IAAI,CAAC,EAAD,CAAxB;AACAH,YAAAA,GAAG,CAAC8K,OAAJ,GAAc3K,IAAI,CAAC,EAAD,CAAlB;AACAH,YAAAA,GAAG,CAAC+K,IAAJ,GAAW5K,IAAI,CAAC,EAAD,CAAf;AAGA+J,YAAAA,IAAI,CAACrH,IAAL,CAAU7C,GAAV;AACH;;AAED,iBAAOkK,IAAP;AACH;;AAES3B,QAAAA,eAAe,CAACb,QAAD,EAAkB3C,EAAU,GAAG,CAA/B,EAAuC;AAC5D,eAAK,IAAIjF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4H,QAAQ,CAAC3H,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;AACtC,gBAAI4H,QAAQ,CAAC5H,CAAD,CAAR,CAAYiF,EAAZ,IAAkBA,EAAtB,EAA0B;AACtB,qBAAO2C,QAAQ,CAAC5H,CAAD,CAAf;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;;AAEMkL,QAAAA,aAAa,CAACjG,EAAU,GAAG,CAAd,EAAiBkG,MAAe,GAAG,IAAnC,EAAyC;AACzD,cAAI9K,IAAI,GAAG,KAAKf,UAAL,CAAgBiB,GAAhB,CAAoB0E,EAApB,CAAX;;AACA,cAAImG,QAAQ,GAAG;AAAA;AAAA,4CAAazM,WAAb,GAA2BC,KAA3B,CAAiCyM,WAAjC,EAAf;;AACA,cAAIhL,IAAJ,EAAU;AACN,gBAAIA,IAAI,CAACgF,WAAL,IAAoB+F,QAAQ,CAACE,GAAjC,EAAsC;AAClCjL,cAAAA,IAAI,CAACyG,eAAL,GAAuBqE,MAAvB;AACA9K,cAAAA,IAAI,CAAC6G,OAAL,GAAeiE,MAAf;AACH;;AAED,gBAAI9K,IAAI,CAAC8E,UAAL,IAAmBiG,QAAQ,CAACE,GAAhC,EAAqC;AACjCjL,cAAAA,IAAI,CAAC2G,cAAL,GAAsBmE,MAAtB;AACA9K,cAAAA,IAAI,CAAC6G,OAAL,GAAeiE,MAAf;AACH;;AAGD,iBAAK7L,UAAL,CAAgBS,GAAhB,CAAoBkF,EAApB,EAAwB5E,IAAxB;AACH;AACJ;;AAEMkL,QAAAA,gBAAgB,CAACJ,MAAe,GAAG,IAAnB,EAAyB;AAC5C,eAAK7L,UAAL,CAAgBqB,OAAhB,CAAwB6K,OAAO,IAAI;AAC/B,iBAAKN,aAAL,CAAmBM,OAAO,CAACvG,EAA3B,EAA+BkG,MAA/B;AACH,WAFD;AAGH;;AAGMA,QAAAA,MAAM,CAAClG,EAAU,GAAG,CAAd,EAA0B;AACnC,cAAI5E,IAAI,GAAG,KAAKf,UAAL,CAAgBiB,GAAhB,CAAoB0E,EAApB,CAAX;;AACA,cAAImG,QAAQ,GAAG;AAAA;AAAA,4CAAazM,WAAb,GAA2BC,KAA3B,CAAiCyM,WAAjC,EAAf;;AACA,cAAIhL,IAAJ,EAAU;AACN,gBAAIA,IAAI,CAACgF,WAAL,IAAoB+F,QAAQ,CAACE,GAAjC,EAAsC;AAClC,qBAAOjL,IAAI,CAACyG,eAAZ;AACH;;AAED,gBAAIzG,IAAI,CAAC8E,UAAL,IAAmBiG,QAAQ,CAACE,GAAhC,EAAqC;AACjC,qBAAOjL,IAAI,CAAC2G,cAAZ;AACH;AAEJ;;AAED,iBAAO,KAAP;AACH;;AAEMG,QAAAA,SAAS,CAACjH,GAAD,EAAoB;AAChC,cAAIkL,QAAQ,GAAG;AAAA;AAAA,4CAAazM,WAAb,GAA2BC,KAA3B,CAAiCyM,WAAjC,EAAf;;AACA,cAAInL,GAAG,CAACmF,WAAJ,IAAmB+F,QAAQ,CAACE,GAAhC,EAAqC;AACjC,mBAAOpL,GAAG,CAAC4G,eAAX;AACH;;AAED,cAAI5G,GAAG,CAACiF,UAAJ,IAAkBiG,QAAQ,CAACE,GAA/B,EAAoC;AAChC,mBAAOpL,GAAG,CAAC8G,cAAX;AACH;;AAED,iBAAO,KAAP;AACH;;AAEMyE,QAAAA,YAAY,GAAgB;AAC/B,cAAItB,GAAgB,GAAGuB,KAAK,CAACC,IAAN,CAAW,KAAKrM,UAAL,CAAgB2B,MAAhB,EAAX,CAAvB;AACAkJ,UAAAA,GAAG,GAAGA,GAAG,CAACyB,IAAJ,CAAS,KAAKC,UAAd,CAAN;AACA1B,UAAAA,GAAG,GAAGA,GAAG,CAACtC,MAAJ,EAAN;AAEA,cAAIiE,OAAoB,GAAG,EAA3B;AACAA,UAAAA,OAAO,GAAG3B,GAAG,CAACtC,MAAJ,CAAWiE,OAAX,CAAV,CAN+B,CAO/B;;AACA,iBAAOA,OAAP;AACH;;AAGMD,QAAAA,UAAU,CAACE,CAAD,EAAS/D,CAAT,EAAyB;AACtC,cAAI+D,CAAC,CAAC9G,EAAF,GAAO+C,CAAC,CAAC/C,EAAb,EAAiB;AACb,mBAAO,CAAP;AACH;;AAED,iBAAO,CAAC,CAAR;AACH;;AAGM+G,QAAAA,SAAS,GAAW;AACvB,cAAIC,GAAG,GAAG,CAAV;AACA,cAAI9B,GAAG,GAAG,KAAKsB,YAAL,EAAV;;AACA,eAAK,IAAIzL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmK,GAAG,CAAClK,MAAxB,EAAgCD,CAAC,EAAjC,EAAqC;AACjC,gBAAI,CAAC,KAAKmL,MAAL,CAAYhB,GAAG,CAACnK,CAAD,CAAH,CAAOiF,EAAnB,CAAL,EAA6B;AACzBgH,cAAAA,GAAG;AACN;AACJ;;AAED,iBAAOA,GAAP;AACH;;AA9lB2B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { Basic, Conscript, General } from \"../../config/Basci\";\nimport LoginCommand from \"../../login/LoginCommand\";\nimport DateUtil from \"../../utils/DateUtil\";\nimport MapUICommand from \"./MapUICommand\";\n\nexport class CityAddition {\n    cost: number = 0;\n    armyCnt: number = 0;//军队数量\n    vanguardCnt: 0;//军队前锋数量 默认每队只有两个位置 前锋数量影响第三个位置的开启\n    soldierCnt: 0;//带兵数加成\n    han: number = 0;//汉阵营加成\n    qun: number = 0;//群阵营加成\n    wei: number = 0;//魏阵营加成\n    shu: number = 0;//蜀阵营加成\n    wu: number = 0;//吴阵营加成\n    taxRate:number = 0;//交换的税率\n    durable:number = 0;//耐久\n\n\n    public clear(): void {\n        this.cost = 0;\n        this.armyCnt = 0;\n        this.vanguardCnt = 0;\n        this.soldierCnt = 0;\n        this.han = 0;\n        this.qun = 0;\n        this.wei = 0;\n        this.shu = 0;\n        this.wu = 0;\n        this.durable = 0;\n    }\n};\n\n/**城池加成类型*/\nexport class CityAdditionType {\n    static Durable: number = 1;//耐久\n    static Cost: number = 2;\n    static ArmyTeams: number = 3;//队伍数量\n    static Speed: number = 4;//速度\n    static Defense: number = 5;//防御\n    static Strategy: number = 6;\t//谋略\n    static Force: number = 7;\t//攻击武力\n    static ConscriptTime: number = 8;//征兵时间\n    static ReserveLimit: number = 9;//预备役上限\n    static Unkonw: number = 10;\n    static HanAddition: number = 11;\n    static QunAddition: number = 12;\n    static WeiAddition: number = 13;\n    static ShuAddition: number = 14;\n    static WuAddition: number = 15;\n    static DealTaxRate: number = 16;//交易税率\n    static Wood: number = 17;\n    static Iron: number = 18;\n    static Grain: number = 19;\n    static Stone: number = 20;\n    static Tax: number = 21;//税收\n    static ExtendTimes: number = 22;//扩建次数\n    static WarehouseLimit: number = 23;//仓库容量\n    static SoldierLimit: number = 24;//带兵数量\n    static VanguardLimit: number = 25;//前锋数量\n}\n\n/**设施*/\nexport class Facility {\n    level: number = 0;\n    type: number = 0;\n    upTime:number = 0;  //升级的时间，0为该等级已经升级成功\n\n    public isUping(): boolean{\n        return this.upLastTime() > 0\n    }\n\n    public isNeedUpdateLevel(): boolean{\n        return this.upLastTime() < 0\n    }\n\n    public upLastTime(): number{\n        if(this.upTime > 0){\n            let cfg:FacilityConfig = MapUICommand.getInstance().proxy.getFacilityCfgByType(this.type);\n            var costTime = cfg.upLevels[this.level].time;\n            return DateUtil.leftTime((this.upTime+costTime)*1000);\n        }else{\n            return 0;\n        }\n    }\n}\n\n/**设施(配置)*/\nexport class FacilityConfig {\n    name: string = \"\";\n    type: number = 0;\n    des: string = \"\";\n    additions: number[] = [];\n    conditions: FacilityOpenCondition[] = [];\n    upLevels: FacilityUpLevel[] = [];\n}\n\n/**设施加成类型配置*/\nexport class FacilityAdditionCfg {\n    type: number = 0;\n    des: string = \"\";\n    value: string = \"\";\n}\n\n/**设施开启条件配置*/\nexport class FacilityOpenCondition {\n    type: number = 0;\n    level: number = 0;\n}\n\n/**设施升级配置*/\nexport class FacilityUpLevel {\n    level: number = 0;\n    values: number[] = [];\n    wood: number = 0;\n    iron: number = 0;\n    stone: number = 0;\n    grain: number = 0;\n    decree: number = 0;\n    time:number = 0;\n}\n\nexport class BasicGeneral {\n    limit: number = 0;\n}\n\n\nexport class WarReport {\n    id: number = 0;\n    attack_rid: number = 0;\n    defense_rid: number = 0;\n\n    beg_attack_army: any = {};\n    beg_defense_army: any = {};\n    end_attack_army: any = {};\n    end_defense_army: any = {};\n\n    result: number = 0;\n    rounds: any = {};\n    attack_is_read: boolean = false;\n    defense_is_read: boolean = false;\n    destroy_durable: number = 0;\n    occupy: number = 0;\n    x: number = 0;\n    y: number = 0;\n    ctime: number = 0;\n    beg_attack_general: any[] = [];\n    beg_defense_general: any[] = [];\n\n    end_attack_general: any[] = [];\n    end_defense_general: any[] = [];\n\n    is_read: boolean = false;\n\n}\n\nexport class WarReportSkill {\n    fromId:number\n    toId:number[]\n    cfgId:number\n    lv:number\n    includeEffect:number[]\n    effectValue:number[]//效果值\n\teffectRound:number[]//效果持续回合数\n    kill:number[] //技能死数量\n}\n\nexport class WarReportRound {\n    id: number = 0;\n    isAttack: boolean = false;\n    attack: any = {};\n    defense: any = {};\n\n    defenseLoss: number = 0;\n    round: number = 0;\n    turn: number = 0;\n    attackBefore:WarReportSkill[] = [];\n    attackAfter:WarReportSkill[] = [];\n    defenseAfter:WarReportSkill[] = [];\n}\n\n\nexport default class MapUIProxy {\n    protected _myFacility: Map<number, Map<number, Facility>> = new Map<number, Map<number, Facility>>();//城市设施\n    protected _facilityCfg: Map<number, FacilityConfig> = new Map<number, FacilityConfig>();//设施配置\n    protected _facilityAdditionCfg: Map<number, FacilityAdditionCfg> = new Map<number, FacilityAdditionCfg>();//升级加成配置\n    protected _warReport: Map<number, WarReport> = new Map<number, WarReport>();\n    protected _additions: Map<number, CityAddition> = new Map<number, CityAddition>();\n    protected _basic: Basic\n\n    public clearData(): void {\n        this._warReport.clear();\n        this._myFacility.clear();\n        this._additions.clear();\n    }\n\n    /**\n     * 当前城市的设施\n     * @param data \n     */\n    public updateMyFacilityList(cityId: number, datas: any[]): void {\n        console.log(\"updateMyFacilityList:\", datas);\n        let list: Map<number, Facility> = new Map<number, Facility>();\n        this._myFacility.set(cityId, list);\n        for (var i = 0; i < datas.length; i++) {\n            var obj = new Facility();\n            obj.level = datas[i].level;\n            obj.type = datas[i].type;\n            obj.upTime = datas[i].up_time;\n            list.set(obj.type, obj);\n        }\n\n        console.log(\"list:\", list);\n    }\n\n    public updateMyFacility(cityId: number, data: any): Facility {\n        console.log(\"updateMyFacility:\", data);\n        if (this._myFacility.has(cityId)) {\n            let list: Map<number, Facility> = this._myFacility.get(cityId);\n            let facilityData: Facility = list.get(data.type);\n            if (facilityData == null) {\n                facilityData = new Facility();\n                list.set(data.type, facilityData);\n            }\n            facilityData.level = data.level;\n            facilityData.type = data.type;\n            facilityData.upTime = data.up_time;\n            return facilityData;\n        }\n        return null;\n    }\n\n    /**更新设施加成数据*/\n    public updateMyCityAdditions(cityId: number): CityAddition {\n        if (this._myFacility.has(cityId)) {\n            let addition: CityAddition = null;\n            if (this._additions.has(cityId)) {\n                addition = this._additions.get(cityId);\n            } else {\n                addition = new CityAddition();\n                this._additions.set(cityId, addition);\n            }\n            addition.clear();//清除旧数据 重新计算\n            let list: Map<number, Facility> = this._myFacility.get(cityId);\n            list.forEach((data: Facility, type: number) => {\n                if (data.level > 0) {\n                    let cfg: FacilityConfig = this.getFacilityCfgByType(data.type);\n                    if (cfg) {\n                        let addValue: number = 0;\n                        let index: number = -1;\n                        index = cfg.additions.indexOf(CityAdditionType.ArmyTeams);\n                        if (index != -1) {\n                            //军队数量加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.armyCnt += addValue;\n                        }\n                        index = cfg.additions.indexOf(CityAdditionType.Cost);\n                        if (index != -1) {\n                            //cost加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.cost += addValue;\n                        }\n                        index = cfg.additions.indexOf(CityAdditionType.SoldierLimit);\n                        if (index != -1) {\n                            //带兵数加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.soldierCnt += addValue;\n                        }\n                        index = cfg.additions.indexOf(CityAdditionType.VanguardLimit);\n                        if (index != -1) {\n                            //带兵数加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.vanguardCnt += addValue;\n                        }\n                        index = cfg.additions.indexOf(CityAdditionType.HanAddition);\n                        if (index != -1) {\n                            //汉阵营加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.han += addValue;\n                        }\n                        index = cfg.additions.indexOf(CityAdditionType.QunAddition);\n                        if (index != -1) {\n                            //群阵营加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.qun += addValue;\n                        }\n                        index = cfg.additions.indexOf(CityAdditionType.WeiAddition);\n                        if (index != -1) {\n                            //魏阵营加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.wei += addValue;\n                        }\n                        index = cfg.additions.indexOf(CityAdditionType.ShuAddition);\n                        if (index != -1) {\n                            //蜀阵营加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.shu += addValue;\n                        }\n                        index = cfg.additions.indexOf(CityAdditionType.WuAddition);\n                        if (index != -1) {\n                            //吴阵营加成\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.wu += addValue;\n                        }\n\n                        index = cfg.additions.indexOf(CityAdditionType.DealTaxRate);\n                        if (index != -1) {\n                            //交易税收\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.taxRate += addValue;\n                        }\n\n                        index = cfg.additions.indexOf(CityAdditionType.Durable);\n                        if (index != -1) {\n                            //console.log(\"CityAdditionType.Durable:\", cfg.upLevels, addValue, index);\n                            //耐久\n                            addValue = cfg.upLevels[data.level - 1].values[index];\n                            addition.durable += addValue;\n                        }\n                    }\n                }\n            });\n            console.log(\"updateMyCityAdditions\", cityId, addition);\n            return addition;\n        }\n        return null;\n    }\n\n    /**\n     * 获取当前拥有的设施\n     * @param cityId \n     */\n    public getMyFacilitys(cityId: number = 0): Map<number, Facility> {\n        return this._myFacility.get(cityId);\n    }\n\n    public getMyAllFacilitys(): Map<number, Map<number, Facility>> {\n        return this._myFacility;\n    }\n\n    /**获取指定的设施数据*/\n    public getMyFacilityByType(cityId: number = 0, type: number = 0): Facility {\n        if (this._myFacility.has(cityId)) {\n            let list: Map<number, Facility> = this._myFacility.get(cityId);\n            if (list.has(type)) {\n                return list.get(type);\n            }\n        }\n        return null;\n    }\n\n    /**获取城池的加成数据*/\n    public getMyCityAddition(cityId: number): CityAddition {\n        let addition: CityAddition = null;\n        if (this._additions.has(cityId)) {\n            addition = this._additions.get(cityId);\n        } else {\n            addition = new CityAddition();\n            this._additions.set(cityId, addition);\n        }\n        return addition;\n    }\n\n    public getMyCityCost(cityId: number):number{\n        let addition = this.getMyCityAddition(cityId);\n        console.log(\"getMyCityCost:\", cityId, addition, this._basic.city.cost);\n        return addition.cost + this._basic.city.cost;\n    }\n\n    //最大耐久\n    public getMyCityMaxDurable(cityId: number):number{\n        let addition = this.getMyCityAddition(cityId);\n        console.log(\"getMyCityMaxDurable:\", cityId, addition, this._basic.city.durable);\n        return addition.durable + this._basic.city.durable;\n    }\n\n    public getTransformRate():number {\n        return this._basic.city.transform_rate\n    }\n\n    /**\n     * 全部设施配置\n     * @param jsonAsset \n     */\n    public setAllFacilityCfg(jsonAssets: any[]): void {\n        this._facilityCfg = new Map();\n\n        let mainJson: any = null;//设施类型配置\n        let additionJson: any = null;//升级类型配置\n        let otherJsons: any[] = [];//具体升级配置\n\n        for (let i: number = 0; i < jsonAssets.length; i++) {\n            if (jsonAssets[i]._name == \"facility\") {\n                mainJson = jsonAssets[i].json;\n            } else if (jsonAssets[i]._name == \"facility_addition\") {\n                additionJson = jsonAssets[i].json;\n            } else {\n                otherJsons.push(jsonAssets[i].json);\n            }\n        }\n        console.log(\"mainJson\", mainJson, additionJson);\n        if (mainJson != null && additionJson != null) {\n            //主配置存在才处理配置文件\n            this._facilityCfg.clear();\n            for (let i: number = 0; i < mainJson.list.length; i++) {\n                let cfgData: FacilityConfig = new FacilityConfig();\n                cfgData.type = mainJson.list[i].type;\n                cfgData.name = mainJson.list[i].name;\n                this._facilityCfg.set(cfgData.type, cfgData);\n            }\n\n            this._facilityAdditionCfg.clear();\n            for (let i: number = 0; i < additionJson.list.length; i++) {\n                let cfgData: FacilityAdditionCfg = new FacilityAdditionCfg();\n                cfgData.type = additionJson.list[i].type;\n                cfgData.des = additionJson.list[i].des;\n                cfgData.value = additionJson.list[i].value;\n                this._facilityAdditionCfg.set(cfgData.type, cfgData);\n            }\n\n            let jsonList: any[] = [];\n            for (let i: number = 0; i < otherJsons.length; i++) {\n                this.getFacilityUpLevelJsonList(otherJsons[i], jsonList);\n            }\n            console.log(\"jsonList\", jsonList, otherJsons);\n\n            for (let i: number = 0; i < jsonList.length; i++) {\n                let type: number = jsonList[i].type;\n                let cfgData: FacilityConfig = this._facilityCfg.get(type);\n                if (cfgData) {\n                    //存在主配置 才加入升级配置\n                    cfgData.des = jsonList[i].des;\n                    cfgData.additions = jsonList[i].additions;\n                    cfgData.conditions = [];\n                    for (let j: number = 0; j < jsonList[i].conditions.length; j++) {\n                        let conditionData: FacilityOpenCondition = new FacilityOpenCondition();\n                        conditionData.type = jsonList[i].conditions[j].type;\n                        conditionData.level = jsonList[i].conditions[j].level;\n                        cfgData.conditions.push(conditionData);\n                    }\n\n                    cfgData.upLevels.length = jsonList[i].levels.length;\n                    for (let k: number = 0; k < jsonList[i].levels.length; k++) {\n                        let upLevelData: FacilityUpLevel = new FacilityUpLevel();\n                        upLevelData.level = jsonList[i].levels[k].level;\n                        upLevelData.values = jsonList[i].levels[k].values;\n                        upLevelData.wood = jsonList[i].levels[k].need.wood;\n                        upLevelData.iron = jsonList[i].levels[k].need.iron;\n                        upLevelData.grain = jsonList[i].levels[k].need.grain;\n                        upLevelData.wood = jsonList[i].levels[k].need.wood;\n                        upLevelData.stone = jsonList[i].levels[k].need.stone;\n                        upLevelData.time = jsonList[i].levels[k].time;\n                        cfgData.upLevels[upLevelData.level - 1] = upLevelData;\n\n                        //console.log(\"upLevelData:\", upLevelData)\n                    }\n                }\n            }\n        }\n        this._facilityCfg.forEach((value: FacilityConfig, key: number) => {\n            console.log(\"this._facilityCfg\", key, value);\n        });\n    }\n\n    protected getFacilityUpLevelJsonList(data: any, list: any[]): void {\n        if (typeof (data) == \"string\" || typeof (data) == \"number\") {\n            return;\n        }\n        if (data.type != undefined && data.additions != undefined) {\n            //代表是需要的数据\n            list.push(data);\n        } else {\n            //代表有多条数据在更里层\n            for (let key in data) {\n                this.getFacilityUpLevelJsonList(data[key], list);\n            }\n        }\n    }\n\n    public getFacilityCfgByType(type: number = 0): FacilityConfig {\n        return this._facilityCfg.get(type);\n    }\n\n    public getFacilityAdditionCfgByType(type: number = 0): FacilityAdditionCfg {\n        return this._facilityAdditionCfg.get(type);\n    }\n\n    /**设施是否解锁*/\n    public isFacilityUnlock(cityId: number, type: number): boolean {\n        let isUnlock: boolean = true;\n        let cfg: FacilityConfig = this.getFacilityCfgByType(type);\n        for (let i: number = 0; i < cfg.conditions.length; i++) {\n            let data: Facility = this.getMyFacilityByType(cityId, cfg.conditions[i].type);\n            if (data && data.level < cfg.conditions[i].level) {\n                isUnlock = false;\n                break;\n            }\n        }\n        return isUnlock;\n    }\n\n    public setBasic(data: any): void {\n        this._basic = data.json;\n        console.log(\"setBasic:\", this._basic);\n    }\n\n\n    public getConscriptBaseCost(): Conscript {\n        return this._basic.conscript;\n    }\n\n    public getDefenseSoldiers(level:number): number {\n        console.log(\"getDefenseSoldiers:\", level);\n        return this._basic.npc.levels[level-1].soilders\n    }\n\n    public getBasicGeneral(): General {\n        return this._basic.general;\n    }\n\n\n    public updateWarReports(data: any): void {\n        var list = data.list;\n        for (var i = 0; i < list.length; i++) {\n            var obj: WarReport = this.createWarReprot(list[i]);\n            this._warReport.set(obj.id, obj);\n        }\n    }\n\n    public updateWarReport(data: any): void {\n        var obj: WarReport = this.createWarReprot(data);\n        this._warReport.set(obj.id, obj);\n    }\n\n\n    protected createWarReprot(data: any): WarReport {\n       \n        var obj = new WarReport();\n        obj.id = data.id;\n        obj.attack_rid = data.a_rid;\n        obj.defense_rid = data.d_rid;\n\n        obj.beg_attack_general = this.arrayToObject(JSON.parse(data.b_a_general));\n        obj.end_attack_general = this.arrayToObject(JSON.parse(data.e_a_general));\n        obj.end_attack_army = JSON.parse(data.e_a_army);\n        obj.beg_attack_army = JSON.parse(data.b_a_army);\n      \n        try {\n            obj.beg_defense_army = JSON.parse(data.b_d_army);\n            obj.end_defense_army = JSON.parse(data.e_d_army);\n            obj.beg_defense_general = this.arrayToObject(JSON.parse(data.b_d_general));\n            obj.end_defense_general = this.arrayToObject(JSON.parse(data.e_d_general));\n            obj.rounds = this.createRoundsData(data.rounds, obj.beg_attack_general, obj.beg_defense_general)\n        } catch (error) {\n            console.log(\"createWarReprot:\", error);\n        } \n        \n\n        obj.result = data.result;\n        obj.defense_is_read = data.d_is_read;\n        obj.attack_is_read = data.a_is_read;\n\n        obj.is_read = this.isReadObj(obj);\n        obj.destroy_durable = data.destroy;\n        obj.occupy = data.occupy;\n        obj.x = data.x;\n        obj.y = data.y;\n        obj.ctime = data.ctime;\n\n        return obj;\n    }\n\n\n    private createRoundsData(data: string, attack_generals: any[], defense_generals: any[]): any[] {\n        var generals: any[] = attack_generals.concat(defense_generals);\n\n        var _list: any[] = [];\n        var rounds: any[] = JSON.parse(data);\n        for (var i = 0; i < rounds.length; i++) {\n            var round: any[] = rounds[i].b;\n            \n            if(!round){\n                continue;\n            }\n\n            for (var j = 0; j < round.length; j++) {\n                var turn = round[j];\n                var attack_id = turn.a_id;\n                var defense_id = turn.d_id;\n                var defense_loss = turn.d_loss;\n\n                var obj = new WarReportRound();\n                obj.attack = this.getMatchGeneral(generals, attack_id);\n                obj.defense = this.getMatchGeneral(generals, defense_id);\n                obj.isAttack = this.getMatchGeneral(attack_generals, attack_id) != null;\n                obj.defenseLoss = defense_loss;\n\n                if(turn.a_bs){\n                    var a_bs = turn.a_bs;\n                    for (let index = 0; index < a_bs.length; index++) {\n                        let s = a_bs[index];\n                        let wrs = new WarReportSkill();\n                        wrs.fromId = s.f_id;\n                        wrs.toId = s.t_id;\n                        wrs.cfgId = s.c_id;\n                        wrs.lv = s.lv;\n                        wrs.includeEffect = s.i_e;\n                        wrs.effectValue = s.e_v;\n                        wrs.effectRound = s.e_r;\n                        wrs.kill = s.kill;\n                        obj.attackBefore.push(wrs);\n                    }\n                   \n                }\n\n                if(turn.a_as){\n                    var a_as = turn.a_as;\n                    for (let index = 0; index < a_as.length; index++) {\n                        let s = a_as[index];\n                        let wrs = new WarReportSkill();\n                        wrs.fromId = s.f_id;\n                        wrs.toId = s.t_id;\n                        wrs.cfgId = s.c_id;\n                        wrs.lv = s.lv;\n                        wrs.includeEffect = s.i_e;\n                        wrs.effectValue = s.e_v;\n                        wrs.effectRound = s.e_r;\n                        wrs.kill = s.kill;\n                        obj.attackAfter.push(wrs);\n                    }\n                }\n\n                if(turn.d_as){\n                    var d_as = turn.d_as;\n                    for (let index = 0; index < d_as.length; index++) {\n                        let s = d_as[index];\n                        let wrs = new WarReportSkill();\n                        wrs.fromId = s.f_id;\n                        wrs.toId = s.t_id;\n                        wrs.cfgId = s.c_id;\n                        wrs.lv = s.lv;\n                        wrs.includeEffect = s.i_e;\n                        wrs.effectValue = s.e_v;\n                        wrs.effectRound = s.e_r;\n                        wrs.kill = s.kill;\n                        obj.defenseAfter.push(wrs);\n                    }\n                }\n\n                obj.round = i + 1;\n                obj.turn = j + 1;\n                _list.push(obj);\n            }\n\n        }\n\n  \n        return _list;\n    }\n\n\n    protected arrayToObject(arr: any): any {\n        let temp: any = [];\n        for (var i = 0; i < arr.length; i++) {\n            var data = arr[i];\n\n            var obj: any = {}\n            obj.id = data[0];\n            obj.cfgId = data[1];\n            obj.physical_power = data[2];\n            obj.order = data[3];\n            obj.level = data[4];\n            obj.exp = data[5];\n            obj.cityId = data[6];\n            obj.curArms = data[7];\n            obj.hasPrPoint = data[8];\n            obj.attack_distance = data[9];\n            obj.force_added = data[10];\n            obj.strategy_added = data[11];\n            obj.defense_added = data[12];\n            obj.speed_added = data[13];\n            obj.destroy_added = data[14];\n            obj.star_lv = data[15];\n            obj.star = data[16];\n\n\n            temp.push(obj);\n        }\n\n        return temp;\n    }\n\n    protected getMatchGeneral(generals: any[], id: number = 0): any {\n        for (var i = 0; i < generals.length; i++) {\n            if (generals[i].id == id) {\n                return generals[i];\n            }\n        }\n        return null;\n    }\n\n    public updateWarRead(id: number = 0, isRead: boolean = true) {\n        var data = this._warReport.get(id);\n        var roleData = LoginCommand.getInstance().proxy.getRoleData();\n        if (data) {\n            if (data.defense_rid == roleData.rid) {\n                data.defense_is_read = isRead;\n                data.is_read = isRead;\n            }\n\n            if (data.attack_rid == roleData.rid) {\n                data.attack_is_read = isRead;\n                data.is_read = isRead;\n            }\n\n\n            this._warReport.set(id, data);\n        }\n    }\n\n    public updateAllWarRead(isRead: boolean = true) {\n        this._warReport.forEach(element => {\n            this.updateWarRead(element.id, isRead)\n        });\n    }\n\n\n    public isRead(id: number = 0): boolean {\n        var data = this._warReport.get(id);\n        var roleData = LoginCommand.getInstance().proxy.getRoleData();\n        if (data) {\n            if (data.defense_rid == roleData.rid) {\n                return data.defense_is_read;\n            }\n\n            if (data.attack_rid == roleData.rid) {\n                return data.attack_is_read;\n            }\n\n        }\n\n        return false;\n    }\n\n    public isReadObj(obj: any): boolean {\n        var roleData = LoginCommand.getInstance().proxy.getRoleData();\n        if (obj.defense_rid == roleData.rid) {\n            return obj.defense_is_read;\n        }\n\n        if (obj.attack_rid == roleData.rid) {\n            return obj.attack_is_read;\n        }\n\n        return false;\n    }\n\n    public getWarReport(): WarReport[] {\n        var arr: WarReport[] = Array.from(this._warReport.values());\n        arr = arr.sort(this.sortIsRead);\n        arr = arr.concat();\n\n        var backArr: WarReport[] = [];\n        backArr = arr.concat(backArr);\n        // backArr = backArr.reverse();\n        return backArr;\n    }\n\n\n    public sortIsRead(a: any, b: any): number {\n        if (a.id < b.id) {\n            return 1;\n        }\n\n        return -1;\n    }\n\n\n    public isReadNum(): number {\n        var num = 0;\n        var arr = this.getWarReport();\n        for (var i = 0; i < arr.length; i++) {\n            if (!this.isRead(arr[i].id)) {\n                num++;\n            }\n        }\n\n        return num;\n    }\n\n}\n"]}