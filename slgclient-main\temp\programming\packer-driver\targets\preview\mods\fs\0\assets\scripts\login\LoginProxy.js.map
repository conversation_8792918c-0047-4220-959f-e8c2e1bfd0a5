{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginProxy.ts"], "names": ["Role", "LoginProxy", "clear", "_loginData", "_roleData", "_roleResData", "_token", "saveEnterData", "data", "role", "setRoleData", "role_res", "setRoleResData", "token", "rid", "uid", "nick<PERSON><PERSON>", "sex", "sid", "balance", "headId", "profile", "getRoleData", "getRoleResData", "saveLoginData", "getLoginData", "getToken", "getSession", "session"], "mappings": ";;;iBAAaA,I,EAaQC,U;;;;;;;;;;;;;;;;;;sBAbRD,I,GAAN,MAAMA,IAAN,CAAU;AAAA;AAAA,uCACA,CADA;;AAAA,uCAEA,CAFA;;AAAA,4CAGK,EAHL;;AAAA,uCAIA,CAJA;;AAAA,uCAKA,CALA;;AAAA,2CAMI,CANJ;;AAAA,0CAOG,CAPH;;AAAA,2CAQI,EARJ;AAAA;;AAAA,O;;yBAaIC,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,8CAEF,IAFE;;AAAA,4CAGH,CAHG;;AAAA,6CAOF,IAPE;;AAAA,gDAUD,IAVC;;AAAA,0CAYJ,IAZI;AAAA;;AAcrBC,QAAAA,KAAK,GAAG;AACX,eAAKC,UAAL,GAAkB,IAAlB;AACA,eAAKC,SAAL,GAAiB,IAAjB;AACA,eAAKC,YAAL,GAAoB,IAApB;AACA,eAAKC,MAAL,GAAc,EAAd;AACH;;AAGMC,QAAAA,aAAa,CAACC,IAAD,EAAe;AAC/B,cAAGA,IAAI,CAACC,IAAR,EAAa;AACT,iBAAKC,WAAL,CAAiBF,IAAI,CAACC,IAAtB;AACH;;AAED,cAAGD,IAAI,CAACG,QAAR,EAAiB;AACb,iBAAKC,cAAL,CAAoBJ,IAAI,CAACG,QAAzB;AACH;;AAED,cAAGH,IAAI,CAACK,KAAR,EAAc;AACV,iBAAKP,MAAL,GAAcE,IAAI,CAACK,KAAnB;AACH;AAEJ;;AAEMD,QAAAA,cAAc,CAACJ,IAAD,EAAe;AAChC,eAAKH,YAAL,GAAoBG,IAApB;AACH;;AAGME,QAAAA,WAAW,CAACF,IAAD,EAAe;AAC7B,cAAG,CAAC,KAAKJ,SAAT,EAAmB;AACf,iBAAKA,SAAL,GAAiB,IAAIJ,IAAJ,EAAjB;AACH;;AACD,eAAKI,SAAL,CAAeU,GAAf,GAAqBN,IAAI,CAACM,GAA1B;AACA,eAAKV,SAAL,CAAeW,GAAf,GAAqBP,IAAI,CAACO,GAA1B;AACA,eAAKX,SAAL,CAAeY,QAAf,GAA0BR,IAAI,CAACQ,QAA/B;AACA,eAAKZ,SAAL,CAAea,GAAf,GAAqBT,IAAI,CAACS,GAA1B;AACA,eAAKb,SAAL,CAAec,GAAf,GAAqBV,IAAI,CAACU,GAA1B;AACA,eAAKd,SAAL,CAAee,OAAf,GAAyBX,IAAI,CAACW,OAA9B;AACA,eAAKf,SAAL,CAAegB,MAAf,GAAwBZ,IAAI,CAACY,MAA7B;AACA,eAAKhB,SAAL,CAAeiB,OAAf,GAAyBb,IAAI,CAACa,OAA9B;AACH;;AAGMC,QAAAA,WAAW,GAAO;AACrB,iBAAO,KAAKlB,SAAZ;AACH;;AAGMmB,QAAAA,cAAc,GAAM;AACvB,iBAAO,KAAKlB,YAAZ;AACH;;AAGMmB,QAAAA,aAAa,CAAChB,IAAD,EAAe;AAC/B,eAAKL,UAAL,GAAkBK,IAAlB;AACH;;AAEMiB,QAAAA,YAAY,GAAM;AACrB,iBAAO,KAAKtB,UAAZ;AACH;;AAEMuB,QAAAA,QAAQ,GAAS;AACpB,iBAAO,KAAKpB,MAAZ;AACH;;AAEMqB,QAAAA,UAAU,GAAS;AACtB,iBAAO,KAAKxB,UAAL,CAAgByB,OAAvB;AACH;;AAjF2B,O", "sourcesContent": ["export class Role{\n    rid:number = 0;\n    uid:number = 0;\n    nickName:string = \"\";\n    sex:number = 0;\n    sid:number = 0;\n    balance:number = 0;\n    headId:number = 0;\n    profile:string = \"\";\n}\n\n\n\nexport default class LoginProxy {\n    //登录数据\n    private _loginData: any = null;\n    public serverId:number = 0;\n\n\n    //角色数据\n    private _roleData :Role = null;\n\n    //角色资源\n    private _roleResData:any = null;\n\n    private _token:string = null;\n\n    public clear() {\n        this._loginData = null;\n        this._roleData = null;\n        this._roleResData = null;\n        this._token = \"\"\n    }\n\n\n    public saveEnterData(data:any):void{\n        if(data.role){\n            this.setRoleData(data.role);\n        }\n        \n        if(data.role_res){\n            this.setRoleResData(data.role_res);\n        }\n        \n        if(data.token){\n            this._token = data.token\n        }\n        \n    }\n\n    public setRoleResData(data:any):void{\n        this._roleResData = data;\n    }\n\n\n    public setRoleData(data:any):void{\n        if(!this._roleData){\n            this._roleData = new Role();\n        }\n        this._roleData.rid = data.rid;\n        this._roleData.uid = data.uid;\n        this._roleData.nickName = data.nickName;\n        this._roleData.sex = data.sex;\n        this._roleData.sid = data.sid;\n        this._roleData.balance = data.balance;\n        this._roleData.headId = data.headId;\n        this._roleData.profile = data.profile;\n    }\n\n\n    public getRoleData():Role{\n        return this._roleData;\n    }\n\n\n    public getRoleResData():any{\n        return this._roleResData;\n    }\n\n\n    public saveLoginData(data:any):void{\n        this._loginData = data;\n    }\n\n    public getLoginData():any{\n        return this._loginData;\n    }\n\n    public getToken():string{\n        return this._token;\n    }\n\n    public getSession():string{\n        return this._loginData.session;\n    }\n}\n"]}