import { _decorator, Component, Canvas, view, sys, director, Scene, screen, ResolutionPolicy } from 'cc';
import { GameConfig } from '../config/GameConfig';

const { ccclass, property } = _decorator;

/**
 * 简单屏幕适配器
 * 专门解决点击偏移问题的轻量级方案
 */
@ccclass('SimpleScreenAdapter')
export class SimpleScreenAdapter extends Component {
    
    private static _instance: SimpleScreenAdapter = null;
    
    public static get instance(): SimpleScreenAdapter {
        return SimpleScreenAdapter._instance;
    }
    
    protected onLoad(): void {
        SimpleScreenAdapter._instance = this;
        console.log('[SimpleScreenAdapter] 简单屏幕适配器初始化');
    }
    
    protected onDestroy(): void {
        if (SimpleScreenAdapter._instance === this) {
            SimpleScreenAdapter._instance = null;
        }
    }
    
    /**
     * 初始化屏幕适配
     */
    public init(): void {
        console.log('[SimpleScreenAdapter] 开始初始化屏幕适配...');
        
        // 获取屏幕信息
        const windowSize = screen.windowSize;
        const screenWidth = windowSize.width;
        const screenHeight = windowSize.height;
        const screenRatio = screenWidth / screenHeight;
        
        console.log(`[SimpleScreenAdapter] 屏幕尺寸: ${screenWidth}x${screenHeight}`);
        console.log(`[SimpleScreenAdapter] 屏幕比例: ${screenRatio.toFixed(3)}`);
        
        // 使用SHOW_ALL策略，确保内容完整显示且不变形
        const designWidth = GameConfig.screen.baseWidth;
        const designHeight = GameConfig.screen.baseHeight;
        
        view.setDesignResolutionSize(designWidth, designHeight, ResolutionPolicy.SHOW_ALL);
        
        console.log(`[SimpleScreenAdapter] 设计分辨率: ${designWidth}x${designHeight} (SHOW_ALL)`);
        
        // 修复所有Canvas设置
        this.fixAllCanvas();
        
        // 监听场景切换
        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onSceneLaunched, this);
        
        // 监听屏幕尺寸变化
        this.setupResizeListener();
        
        // 打印最终信息
        this.scheduleOnce(() => {
            this.printFinalInfo();
        }, 0.1);
        
        console.log('[SimpleScreenAdapter] 屏幕适配初始化完成');
    }
    
    /**
     * 场景启动后的回调
     */
    private onSceneLaunched(scene: Scene): void {
        this.scheduleOnce(() => {
            this.fixAllCanvas();
        }, 0);
    }
    
    /**
     * 修复所有Canvas设置
     */
    private fixAllCanvas(): void {
        const scene = director.getScene();
        if (!scene) return;
        
        const canvasComponents = scene.getComponentsInChildren(Canvas);
        
        canvasComponents.forEach((canvas, index) => {
            console.log(`[SimpleScreenAdapter] 修复Canvas[${index}]`);
            
            // 关键设置：禁用自动对齐屏幕
            canvas.alignCanvasWithScreen = false;
            
            // 确保Canvas位置为原点
            canvas.node.setPosition(0, 0, 0);
            
            console.log(`[SimpleScreenAdapter] Canvas[${index}] 修复完成: alignCanvasWithScreen=false, position=(0,0,0)`);
        });
    }
    
    /**
     * 设置屏幕尺寸变化监听
     */
    private setupResizeListener(): void {
        // 监听窗口尺寸变化
        window.addEventListener('resize', () => {
            this.scheduleOnce(() => {
                console.log('[SimpleScreenAdapter] 检测到窗口尺寸变化，重新适配...');
                this.handleResize();
            }, 0.1);
        });
        
        // 监听屏幕方向变化（移动设备）
        if (sys.isMobile) {
            window.addEventListener('orientationchange', () => {
                this.scheduleOnce(() => {
                    console.log('[SimpleScreenAdapter] 检测到屏幕方向变化，重新适配...');
                    this.handleResize();
                }, 0.2);
            });
        }
    }
    
    /**
     * 处理屏幕尺寸变化
     */
    private handleResize(): void {
        // 重新初始化适配
        this.init();
    }
    
    /**
     * 打印最终适配信息
     */
    private printFinalInfo(): void {
        if (!GameConfig.screen.enableDebugInfo) return;
        
        const windowSize = screen.windowSize;
        const visibleSize = view.getVisibleSize();
        const visibleOrigin = view.getVisibleOrigin();
        const designSize = view.getDesignResolutionSize();
        
        console.log('=== 简单屏幕适配最终信息 ===');
        console.log(`窗口尺寸: ${windowSize.width}x${windowSize.height}`);
        console.log(`设计分辨率: ${designSize.width}x${designSize.height}`);
        console.log(`可视区域: ${visibleSize.width.toFixed(1)}x${visibleSize.height.toFixed(1)}`);
        console.log(`可视原点: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);
        
        // 计算缩放比例
        const scaleX = visibleSize.width / designSize.width;
        const scaleY = visibleSize.height / designSize.height;
        console.log(`缩放比例: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);
        
        // 检查是否有异常
        if (Math.abs(visibleOrigin.x) > 1 || Math.abs(visibleOrigin.y) > 1) {
            console.warn(`⚠️ 可视原点偏移异常: (${visibleOrigin.x.toFixed(1)}, ${visibleOrigin.y.toFixed(1)})`);
        }
        
        if (scaleX < 0.5 || scaleY < 0.5 || scaleX > 3 || scaleY > 3) {
            console.warn(`⚠️ 缩放比例异常: X=${scaleX.toFixed(3)}, Y=${scaleY.toFixed(3)}`);
        }
        
        console.log('============================');
    }
    
    /**
     * 获取当前适配信息
     */
    public getAdaptInfo(): any {
        const windowSize = screen.windowSize;
        const visibleSize = view.getVisibleSize();
        const visibleOrigin = view.getVisibleOrigin();
        const designSize = view.getDesignResolutionSize();
        
        return {
            windowSize,
            designSize,
            visibleSize,
            visibleOrigin,
            scaleX: visibleSize.width / designSize.width,
            scaleY: visibleSize.height / designSize.height
        };
    }
    
    /**
     * 手动触发适配信息打印
     */
    public printDebugInfo(): void {
        this.printFinalInfo();
    }
}
