[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "RightTagItem", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}, {"__id__": 33}, {"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}, {"__id__": 24}, {"__id__": 26}], "_prefab": {"__id__": 28}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "labelInfo", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": -110, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "位置名字", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 18, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 18, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40sp7cDOBLRo8sexdx5W77"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d/sOfbcJJnogi9TPW5zNE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 22.68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d82IWlqVxMQp7uN4bhdOLB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24AnVJfDBGqJYFWoUXlYZG"}, {"__type__": "cc.Node", "_name": "labelPos", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": 110, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 13}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(0,0)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 18, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 18, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79fYmVsNZFCLghXRF4rJ+0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 15}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9PIOsHfxNOJUGTK2uWwnV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 17}, "_contentSize": {"__type__": "cc.Size", "width": 37.01, "height": 22.68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fnPhTJyxFWqSTooqzi9g8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22IyBWud1Co6q6sCgGLshB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 20}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "91c3a02a-a4ef-442a-9e69-847a1f31ac7f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1C+yie/BFiKuh8W8+/Y8E"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 22}, "clickEvents": [{"__id__": 23}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "91c3a02a-a4ef-442a-9e69-847a1f31ac7f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 2}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cUm0RquFMiqWuvCEDCbAa"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "46c1eMoU2VKDKQR/S8Jlcu9", "handler": "onClickBg", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 25}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1RREdCv5DHYln+8z591Ei"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 27}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "857LDSOB9Pa6ylAsryaaaA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a29yD/kmtBopRzkXyHT2e7"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 30}, "_resizeMode": 0, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdK2zoziNOyqAMttcLQbyS"}, {"__type__": "46c1eMoU2VKDKQR/S8Jlcu9", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 32}, "labelInfo": {"__id__": 4}, "labelPos": {"__id__": 12}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bwJ49pIdL15G/THlchWXM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 34}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81+gr0JaZDiaquvDjN6hSB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 36}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08jM5+ulNOMLaJ2l6cstYW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dbeab68rVKSogxJjGXqnUJ"}]