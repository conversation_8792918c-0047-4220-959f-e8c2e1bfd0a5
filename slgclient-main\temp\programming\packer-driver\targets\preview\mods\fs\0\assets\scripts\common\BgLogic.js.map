{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/common/BgLogic.ts"], "names": ["_decorator", "Component", "Enum", "Widget", "UITransform", "view", "ccclass", "property", "BgScaleType", "BgAlignmentType", "BgLogic", "type", "onLoad", "_realW", "node", "getComponent", "width", "_realH", "height", "updateFrameSize", "scaleW", "getVisibleSize", "scaleH", "scaleX", "scaleY", "scaleType", "SCALE_BY_WIDTH", "SCALE_BY_HEIGHT", "SCALE_ONLY_WIDTH", "SCALE_ONLY_HEIGHT", "Math", "max", "widget", "addComponent", "alignmentType", "BOTTOM", "isAlignHorizontalCenter", "isAlignBottom", "bottom", "TOP", "isAlignTop", "top", "LEFT", "isAlignVerticalCenter", "isAlignLeft", "left", "RIGHT", "isAlignRight", "right", "CENTER", "FULL_SCREEN"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,I,OAAAA,I;;;;;;;OAC3D;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;iBAElBQ,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;SAAAA,W,2BAAAA,W;;AAMX,O,CACD;;iBACYC,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;SAAAA,e,+BAAAA,e;;AAMX;;yBAGoBC,O,WADpBJ,OAAO,CAAC,SAAD,C,UAEHC,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAET,IAAI,CAACM,WAAD;AAAZ,OAAD,C,UAERD,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAET,IAAI,CAACO,eAAD;AAAZ,OAAD,C,oCAJb,MACqBC,OADrB,SACqCT,SADrC,CAC+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA,0CAKhB,CALgB;;AAAA,0CAMhB,CANgB;AAAA;;AAOjCW,QAAAA,MAAM,GAAS;AACrB,eAAKC,MAAL,GAAc,KAAKC,IAAL,CAAUC,YAAV,CAAuBX,WAAvB,EAAoCY,KAAlD;AACA,eAAKC,MAAL,GAAc,KAAKH,IAAL,CAAUC,YAAV,CAAuBX,WAAvB,EAAoCc,MAAlD;AAEA,eAAKC,eAAL;AACH;;AAGSA,QAAAA,eAAe,GAAS;AAE9B,cAAIC,MAAc,GAAGf,IAAI,CAACgB,cAAL,GAAsBL,KAAtB,GAA8B,KAAKH,MAAxD;;AACA,cAAIS,MAAc,GAAGjB,IAAI,CAACgB,cAAL,GAAsBH,MAAtB,GAA+B,KAAKD,MAAzD;;AACA,cAAIM,MAAc,GAAG,CAArB;AACA,cAAIC,MAAc,GAAG,CAArB;;AACA,cAAI,KAAKC,SAAL,IAAkBjB,WAAW,CAACkB,cAAlC,EAAkD;AAC9CH,YAAAA,MAAM,GAAGC,MAAM,GAAGJ,MAAlB;AACH,WAFD,MAEO,IAAI,KAAKK,SAAL,IAAkBjB,WAAW,CAACmB,eAAlC,EAAmD;AACtDJ,YAAAA,MAAM,GAAGC,MAAM,GAAGF,MAAlB;AACH,WAFM,MAEA,IAAI,KAAKG,SAAL,IAAkBjB,WAAW,CAACoB,gBAAlC,EAAoD;AACvDL,YAAAA,MAAM,GAAGH,MAAT;AACAI,YAAAA,MAAM,GAAG,CAAT;AACH,WAHM,MAGA,IAAI,KAAKC,SAAL,IAAkBjB,WAAW,CAACqB,iBAAlC,EAAqD;AACxDN,YAAAA,MAAM,GAAG,CAAT;AACAC,YAAAA,MAAM,GAAGF,MAAT;AACH,WAHM,MAGA;AACHC,YAAAA,MAAM,GAAGC,MAAM,GAAGM,IAAI,CAACC,GAAL,CAASX,MAAT,EAAiBE,MAAjB,CAAlB;AACH;;AAED,eAAKR,IAAL,CAAUC,YAAV,CAAuBX,WAAvB,EAAoCY,KAApC,GAA4C,KAAKH,MAAL,GAAcU,MAA1D;AACA,eAAKT,IAAL,CAAUC,YAAV,CAAuBX,WAAvB,EAAoCc,MAApC,GAA6C,KAAKD,MAAL,GAAcO,MAA3D;AAEA,cAAIQ,MAAc,GAAG,KAAKlB,IAAL,CAAUC,YAAV,CAAuBZ,MAAvB,CAArB;;AAGA,cAAI6B,MAAM,IAAI,IAAd,EAAoB;AAChBA,YAAAA,MAAM,GAAG,KAAKlB,IAAL,CAAUmB,YAAV,CAAuB9B,MAAvB,CAAT;AACH;;AAGD,cAAI,KAAK+B,aAAL,IAAsBzB,eAAe,CAAC0B,MAA1C,EAAkD;AAC9CH,YAAAA,MAAM,CAACI,uBAAP,GAAiC,IAAjC;AACAJ,YAAAA,MAAM,CAACK,aAAP,GAAuB,IAAvB;AACAL,YAAAA,MAAM,CAACM,MAAP,GAAgB,CAAhB;AACH,WAJD,MAIO,IAAI,KAAKJ,aAAL,IAAsBzB,eAAe,CAAC8B,GAA1C,EAA+C;AAClDP,YAAAA,MAAM,CAACI,uBAAP,GAAiC,IAAjC;AACAJ,YAAAA,MAAM,CAACQ,UAAP,GAAoB,IAApB;AACAR,YAAAA,MAAM,CAACS,GAAP,GAAa,CAAb;AACH,WAJM,MAIA,IAAI,KAAKP,aAAL,IAAsBzB,eAAe,CAACiC,IAA1C,EAAgD;AACnDV,YAAAA,MAAM,CAACW,qBAAP,GAA+B,IAA/B;AACAX,YAAAA,MAAM,CAACY,WAAP,GAAqB,IAArB;AACAZ,YAAAA,MAAM,CAACa,IAAP,GAAc,CAAd;AACH,WAJM,MAIA,IAAI,KAAKX,aAAL,IAAsBzB,eAAe,CAACqC,KAA1C,EAAiD;AACpDd,YAAAA,MAAM,CAACW,qBAAP,GAA+B,IAA/B;AACAX,YAAAA,MAAM,CAACe,YAAP,GAAsB,IAAtB;AACAf,YAAAA,MAAM,CAACgB,KAAP,GAAe,CAAf;AACH,WAJM,MAIA,IAAI,KAAKd,aAAL,IAAsBzB,eAAe,CAACwC,MAA1C,EAAkD;AACrDjB,YAAAA,MAAM,CAACI,uBAAP,GAAiC,IAAjC;AACAJ,YAAAA,MAAM,CAACW,qBAAP,GAA+B,IAA/B;AACH;AACJ;;AAlE0C,O;;;;;iBAElBnC,WAAW,CAAC0C,W;;;;;;;iBAEJzC,eAAe,CAACwC,M", "sourcesContent": ["// //因为适配的原因，背景和界面其他元素是分离的，\n// //那么背景的缩放包括场景图片背景和弹窗半透明黑色背景都可以挂这个脚本进行缩放\n\nimport { _decorator, Component, Enum, Widget, UITransform, game, view, Canvas } from 'cc';\nconst { ccclass, property } = _decorator;\n\nexport enum BgScaleType {\n    FULL_SCREEN = 1,\n    SCALE_BY_WIDTH = 2,\n    SCALE_BY_HEIGHT = 3,\n    SCALE_ONLY_WIDTH = 4,\n    SCALE_ONLY_HEIGHT = 5,\n};\n// //bg对齐方位\nexport enum BgAlignmentType {\n    TOP = 1,\n    BOTTOM = 2,\n    CENTER = 3,\n    LEFT = 4,\n    RIGHT = 5\n};\n\n@ccclass('BgLogic')\nexport default class BgLogic extends Component {\n    @property({ type: Enum(BgScaleType) })\n    scaleType: BgScaleType = BgScaleType.FULL_SCREEN;\n    @property({ type: Enum(BgAlignmentType) })\n    alignmentType: BgAlignmentType = BgAlignmentType.CENTER;\n    protected _realW: number = 0;\n    protected _realH: number = 0;\n    protected onLoad(): void {\n        this._realW = this.node.getComponent(UITransform).width;\n        this._realH = this.node.getComponent(UITransform).height;\n\n        this.updateFrameSize();\n    }\n\n\n    protected updateFrameSize(): void {\n\n        let scaleW: number = view.getVisibleSize().width / this._realW;\n        let scaleH: number = view.getVisibleSize().height / this._realH;\n        let scaleX: number = 1;\n        let scaleY: number = 1;\n        if (this.scaleType == BgScaleType.SCALE_BY_WIDTH) {\n            scaleX = scaleY = scaleW;\n        } else if (this.scaleType == BgScaleType.SCALE_BY_HEIGHT) {\n            scaleX = scaleY = scaleH;\n        } else if (this.scaleType == BgScaleType.SCALE_ONLY_WIDTH) {\n            scaleX = scaleW;\n            scaleY = 1;\n        } else if (this.scaleType == BgScaleType.SCALE_ONLY_HEIGHT) {\n            scaleX = 1;\n            scaleY = scaleH;\n        } else {\n            scaleX = scaleY = Math.max(scaleW, scaleH);\n        }\n\n        this.node.getComponent(UITransform).width = this._realW * scaleX;\n        this.node.getComponent(UITransform).height = this._realH * scaleY;\n\n        let widget: Widget = this.node.getComponent(Widget);\n        \n       \n        if (widget == null) {\n            widget = this.node.addComponent(Widget);\n        }\n      \n\n        if (this.alignmentType == BgAlignmentType.BOTTOM) {\n            widget.isAlignHorizontalCenter = true;\n            widget.isAlignBottom = true;\n            widget.bottom = 0;\n        } else if (this.alignmentType == BgAlignmentType.TOP) {\n            widget.isAlignHorizontalCenter = true;\n            widget.isAlignTop = true;\n            widget.top = 0;\n        } else if (this.alignmentType == BgAlignmentType.LEFT) {\n            widget.isAlignVerticalCenter = true;\n            widget.isAlignLeft = true;\n            widget.left = 0;\n        } else if (this.alignmentType == BgAlignmentType.RIGHT) {\n            widget.isAlignVerticalCenter = true;\n            widget.isAlignRight = true;\n            widget.right = 0;\n        } else if (this.alignmentType == BgAlignmentType.CENTER) {\n            widget.isAlignHorizontalCenter = true;\n            widget.isAlignVerticalCenter = true;\n        }\n    }\n}\n\n"]}