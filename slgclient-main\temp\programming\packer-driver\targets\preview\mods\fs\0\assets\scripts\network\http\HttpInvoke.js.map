{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/network/http/HttpInvoke.ts"], "names": ["HttpInvoke", "NetEvent", "EventMgr", "HttpInvokeType", "init", "name", "_otherData", "_name", "onComplete", "data", "json", "JSON", "parse", "responseText", "e", "console", "log", "emit", "ServerRequestSucess", "doSend", "url", "params", "type", "xhr", "XMLHttpRequest", "timeout", "_receiveTime", "self", "p", "Promise", "resolve", "reject", "onreadystatechange", "readyState", "status", "ontimeout", "onerror", "GET", "open", "send", "POST", "setRequestHeader"], "mappings": ";;;kDAWaA,U;;;;;;;;;;;;;;;;;;;;;;;AAVJC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;iBAGGC,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;SAAAA,c,8BAAAA,c;;4BAMCH,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,gDAEa,KAFb;;AAAA,yCAGK,EAHL;;AAAA,8CAIO,IAJP;AAAA;;AAObI,QAAAA,IAAI,CAACC,IAAD,EAAaC,UAAb,EAAwC;AAAA,cAA3BA,UAA2B;AAA3BA,YAAAA,UAA2B,GAAV,IAAU;AAAA;;AAC/C,eAAKC,KAAL,GAAaF,IAAb;AACA,eAAKC,UAAL,GAAkBA,UAAlB;AACH;;AAIOE,QAAAA,UAAU,CAACC,IAAD,EAAe;AAC7B,cAAIC,IAAI,GAAG,EAAX;;AACA,cAAGD,IAAH,EAAQ;AACJ,gBAAI;AACAC,cAAAA,IAAI,GAAGC,IAAI,CAACC,KAAL,CAAWH,IAAI,CAACI,YAAhB,CAAP;AACH,aAFD,CAEE,OAAOC,CAAP,EAAU;AACRC,cAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA6BF,CAA7B;AACH;AACJ;;AACD;AAAA;AAAA,oCAASG,IAAT,CAAc,KAAKV,KAAnB,EAA0BG,IAA1B,EAA+B,KAAKJ,UAApC;AACA;AAAA;AAAA,oCAASW,IAAT,CAAc;AAAA;AAAA,oCAASC,mBAAvB,EAA2CR,IAA3C;AAEH;;AAGMS,QAAAA,MAAM,CAACC,GAAD,EAAYC,MAAZ,EAAuBC,IAAvB,EAAwD;AAEjE,cAAIC,GAAG,GAAG,IAAIC,cAAJ,EAAV;AACAD,UAAAA,GAAG,CAACE,OAAJ,GAAc,KAAKC,YAAnB;AACA,cAAIC,IAAI,GAAG,IAAX;AAEA,cAAIC,CAAC,GAAG,IAAIC,OAAJ,CAAY,UAASC,OAAT,EAAkBC,MAAlB,EAAyB;AAEzCR,YAAAA,GAAG,CAACS,kBAAJ,GAAyB,YAAY;AACjC,kBAAIT,GAAG,CAACU,UAAJ,IAAkB,CAAtB,EAAyB;AACrBlB,gBAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAkCO,GAAlC;;AACA,oBAAIA,GAAG,CAACW,MAAJ,IAAc,GAAd,IAAqBX,GAAG,CAACW,MAAJ,GAAa,GAAtC,EAA2C;AACvCP,kBAAAA,IAAI,CAACnB,UAAL,CAAgBe,GAAhB;AACAO,kBAAAA,OAAO,CAACP,GAAD,CAAP;AACH,iBAHD,MAGO;AACHI,kBAAAA,IAAI,CAACnB,UAAL,CAAgB,IAAhB;AACAO,kBAAAA,OAAO,CAACC,GAAR,CAAY,yBAAZ;AACAc,kBAAAA,OAAO,CAACP,GAAD,CAAP;AACH;AACJ;AAEJ,aAbD;;AAcAA,YAAAA,GAAG,CAACY,SAAJ,GAAgB,YAAY;AACxBpB,cAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACAW,cAAAA,IAAI,CAACnB,UAAL,CAAgB,IAAhB;AACAsB,cAAAA,OAAO,CAAC,IAAD,CAAP;AACH,aAJD;;AAKAP,YAAAA,GAAG,CAACa,OAAJ,GAAc,UAAUtB,CAAV,EAAa;AACvBC,cAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BO,GAAG,CAACU,UAAhC,EAA4CV,GAAG,CAACW,MAAhD,EAAwDpB,CAAxD;AACAa,cAAAA,IAAI,CAACnB,UAAL,CAAgB,IAAhB;AACAsB,cAAAA,OAAO,CAAC,IAAD,CAAP;AACH,aAJD;;AAOA,gBAAGR,IAAI,IAAInB,cAAc,CAACkC,GAA1B,EAA8B;AAC1BjB,cAAAA,GAAG,IAAG,MAAKC,MAAX;AACAE,cAAAA,GAAG,CAACe,IAAJ,CAAS,KAAT,EAAelB,GAAf,EAAqB,IAArB;AACAG,cAAAA,GAAG,CAACgB,IAAJ;AACH,aAJD,MAIM,IAAGjB,IAAI,IAAInB,cAAc,CAACqC,IAA1B,EAA+B;AACjCjB,cAAAA,GAAG,CAACe,IAAJ,CAAS,MAAT,EAAiBlB,GAAjB,EAAsB,IAAtB;AACAG,cAAAA,GAAG,CAACkB,gBAAJ,CAAqB,cAArB,EAAqC,oCAArC;AACAlB,cAAAA,GAAG,CAACgB,IAAJ,CAASlB,MAAT;AACH;AACJ,WArCO,CAAR;AAwCA,iBAAOO,CAAP;AAEH;;AA7EmB,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { NetEvent } from \"../socket/NetInterface\";\nimport { EventMgr } from '../../utils/EventMgr';\n\n\nexport enum HttpInvokeType {\n    GET,\n    POST\n}\n\n\nexport class HttpInvoke {\n    \n    protected _receiveTime: number = 15000;               // 多久没收到数据断开\n    protected _name:string = \"\";\n    protected _otherData:any = null;\n\n\n    public init(name:string,_otherData:any = null):void{\n        this._name = name;\n        this._otherData = _otherData;\n    }\n\n\n\n    private onComplete(data:any):void{\n        var json = {};\n        if(data){\n            try {\n                json = JSON.parse(data.responseText);\n            } catch (e) {\n                console.log(\"onComplete--e:\",e)\n            }\n        }\n        EventMgr.emit(this._name, json,this._otherData);\n        EventMgr.emit(NetEvent.ServerRequestSucess,json);\n        \n    }\n\n\n    public doSend(url:string,params:any,type:HttpInvokeType):Promise<any>{\n       \n        let xhr = new XMLHttpRequest();\n        xhr.timeout = this._receiveTime;\n        var self = this;\n        \n        let p = new Promise(function(resolve, reject){\n            \n            xhr.onreadystatechange = function () {\n                if (xhr.readyState == 4) {\n                    console.log(\"onreadystatechange:\",xhr);\n                    if (xhr.status >= 200 && xhr.status < 400) {\n                        self.onComplete(xhr);\n                        resolve(xhr);\n                    } else {\n                        self.onComplete(null);\n                        console.log(\"onreadystatechange 1111\");\n                        resolve(xhr);\n                    }\n                }\n    \n            };\n            xhr.ontimeout = function () {\n                console.log(\"xhr.ontimeout\");\n                self.onComplete(null);\n                resolve(null);\n            };\n            xhr.onerror = function (e) {\n                console.log(\"xhr.onerror:\", xhr.readyState, xhr.status, e);\n                self.onComplete(null);\n                resolve(null);\n            };\n            \n            \n            if(type == HttpInvokeType.GET){\n                url +=\"?\"+ params;\n                xhr.open(\"GET\",url , true);\n                xhr.send();\n            }else if(type == HttpInvokeType.POST){\n                xhr.open(\"POST\", url, true);\n                xhr.setRequestHeader(\"Content-Type\", \"application/x-www-form-urlencoded;\");\n                xhr.send(params);\n            }\n        });\n         \n       \n        return p;\n\n    }\n}\n"]}