{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/union/UnionProxy.ts"], "names": ["Apply", "Member", "Union", "UnionProxy", "LoginCommand", "MapCommand", "titleDes", "title", "<PERSON><PERSON><PERSON><PERSON>", "major", "concat", "i", "length", "getVice<PERSON>hairman", "<PERSON><PERSON><PERSON><PERSON>", "rid", "Map", "clearData", "_unionMap", "clear", "_menberMap", "updateUnionList", "data", "obj", "createUnion", "set", "id", "name", "cnt", "notice", "createMember", "x", "y", "getUnionList", "Array", "from", "values", "updateMemberList", "member", "union", "get", "push", "updateNotice", "unionid", "text", "updateApplyList", "apply", "_applyMap", "updateApply", "t", "<PERSON><PERSON><PERSON><PERSON>", "isMeInUnion", "city", "getInstance", "cityProxy", "getMyMainCity", "unionId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleData", "proxy", "getRoleData", "getMemberList", "getUnion", "getApplyCnt", "console", "log"], "mappings": ";;;wDAMaA,K,EAOAC,M,EAqBAC,K,EAwCQC,U;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAzEdC,MAAAA,Y;;AAGAC,MAAAA,U;;;;;;;uBAEML,K,GAAN,MAAMA,KAAN,CAAY;AAAA;AAAA,sCACF,CADE;;AAAA,uCAED,CAFC;;AAAA,6CAGK,EAHL;AAAA;;AAAA,O;;wBAONC,M,GAAN,MAAMA,MAAN,CAAa;AAAA;AAAA,uCACF,CADE;;AAAA,wCAED,EAFC;;AAAA,yCAGA,CAHA;;AAAA,qCAeL,CAfK;;AAAA,qCAgBL,CAhBK;AAAA;;AAIG,YAARK,QAAQ,GAAY;AAC3B,cAAG,KAAKC,KAAL,IAAc,CAAjB,EAAmB;AACf,mBAAO,IAAP;AACH;;AAED,cAAG,KAAKA,KAAL,IAAc,CAAjB,EAAmB;AACf,mBAAO,KAAP;AACH;;AAED,iBAAO,MAAP;AACH;;AAde,O;;uBAqBPL,K,GAAN,MAAMA,KAAN,CAAY;AAAA;AAAA,sCACH,CADG;;AAAA,wCAEA,EAFA;;AAAA,uCAGD,CAHC;;AAAA,0CAIE,EAJF;;AAAA,yCAKE,EALF;AAAA;;AAQRM,QAAAA,WAAW,GAAS;AACvB,cAAIC,KAAc,GAAG,KAAKA,KAAL,CAAWC,MAAX,EAArB;;AACA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAcA,CAAC,GAAGF,KAAK,CAACG,MAAxB,EAA+BD,CAAC,EAAhC,EAAmC;AAC/B,gBAAGF,KAAK,CAACE,CAAD,CAAL,CAASJ,KAAT,IAAkB,CAArB,EAAuB;AACnB,qBAAOE,KAAK,CAACE,CAAD,CAAZ;AACH;AACJ;;AACD,iBAAO,IAAIV,MAAJ,EAAP;AACH;;AAEMY,QAAAA,eAAe,GAAS;AAC3B,cAAIJ,KAAc,GAAG,KAAKA,KAAL,CAAWC,MAAX,EAArB;;AACA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAcA,CAAC,GAAGF,KAAK,CAACG,MAAxB,EAA+BD,CAAC,EAAhC,EAAmC;AAC/B,gBAAGF,KAAK,CAACE,CAAD,CAAL,CAASJ,KAAT,IAAkB,CAArB,EAAuB;AACnB,qBAAOE,KAAK,CAACE,CAAD,CAAZ;AACH;AACJ;;AACD,iBAAO,IAAIV,MAAJ,EAAP;AACH;;AAEMa,QAAAA,OAAO,CAACC,GAAD,EAAoB;AAC9B,cAAIN,KAAc,GAAG,KAAKA,KAAL,CAAWC,MAAX,EAArB;;AACA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAcA,CAAC,GAAGF,KAAK,CAACG,MAAxB,EAA+BD,CAAC,EAAhC,EAAmC;AAC/B,gBAAGF,KAAK,CAACE,CAAD,CAAL,CAASI,GAAT,IAAgBA,GAAnB,EAAuB;AACnB,qBAAO,IAAP;AACH;AACJ;;AACD,iBAAO,KAAP;AACH;;AApCc,O;;yBAwCEZ,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,6CAEU,IAAIa,GAAJ,EAFV;;AAAA,8CAGc,IAAIA,GAAJ,EAHd;;AAAA,6CAIY,IAAIA,GAAJ,EAJZ;AAAA;;AAMrBC,QAAAA,SAAS,GAAS;AACrB,eAAKC,SAAL,CAAeC,KAAf;;AACA,eAAKC,UAAL,CAAgBD,KAAhB;AACH;;AAEME,QAAAA,eAAe,CAACC,IAAD,EAAiB;AACnC,eAAKJ,SAAL,CAAeC,KAAf;;AACA,eAAI,IAAIR,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGW,IAAI,CAACV,MAAxB,EAAgCD,CAAC,EAAjC,EAAoC;AAChC,gBAAIY,GAAG,GAAG,KAAKC,WAAL,CAAiBF,IAAI,CAACX,CAAD,CAArB,CAAV;;AACA,iBAAKO,SAAL,CAAeO,GAAf,CAAmBF,GAAG,CAACG,EAAvB,EAA0BH,GAA1B;AACH;AACJ;;AAESC,QAAAA,WAAW,CAACF,IAAD,EAAgB;AACjC,cAAIC,GAAG,GAAG,IAAIrB,KAAJ,EAAV;AACAqB,UAAAA,GAAG,CAACG,EAAJ,GAASJ,IAAI,CAACI,EAAd;AACAH,UAAAA,GAAG,CAACI,IAAJ,GAAWL,IAAI,CAACK,IAAhB;AACAJ,UAAAA,GAAG,CAACK,GAAJ,GAAUN,IAAI,CAACM,GAAf;AACAL,UAAAA,GAAG,CAACM,MAAJ,GAAaP,IAAI,CAACO,MAAlB;AACAN,UAAAA,GAAG,CAACd,KAAJ,GAAYa,IAAI,CAACb,KAAL,CAAWC,MAAX,EAAZ;AACA,iBAAOa,GAAP;AACH;;AAESO,QAAAA,YAAY,CAACR,IAAD,EAAiB;AACnC,cAAIC,GAAG,GAAG,IAAItB,MAAJ,EAAV;AACAsB,UAAAA,GAAG,CAACR,GAAJ,GAAUO,IAAI,CAACP,GAAf;AACAQ,UAAAA,GAAG,CAACI,IAAJ,GAAWL,IAAI,CAACK,IAAhB;AACAJ,UAAAA,GAAG,CAAChB,KAAJ,GAAYe,IAAI,CAACf,KAAjB;AACAgB,UAAAA,GAAG,CAACQ,CAAJ,GAAQT,IAAI,CAACS,CAAb;AACAR,UAAAA,GAAG,CAACS,CAAJ,GAAQV,IAAI,CAACU,CAAb;AACA,iBAAOT,GAAP;AACH;;AAGMU,QAAAA,YAAY,GAAU;AACzB,iBAAOC,KAAK,CAACC,IAAN,CAAW,KAAKjB,SAAL,CAAekB,MAAf,EAAX,CAAP;AACH;;AAGMC,QAAAA,gBAAgB,CAACX,EAAD,EAAWJ,IAAX,EAA2B;AAC9C,cAAIgB,MAAe,GAAG,EAAtB;;AACA,cAAIC,KAAK,GAAG,KAAKrB,SAAL,CAAesB,GAAf,CAAmBd,EAAnB,CAAZ;;AACA,cAAGa,KAAK,IAAI,IAAZ,EAAiB;AACbA,YAAAA,KAAK,CAAC9B,KAAN,GAAc,EAAd;AACH;;AACD,eAAI,IAAIE,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGW,IAAI,CAACV,MAAxB,EAAgCD,CAAC,EAAjC,EAAoC;AAChC,gBAAIY,GAAG,GAAG,KAAKO,YAAL,CAAkBR,IAAI,CAACX,CAAD,CAAtB,CAAV;AACA2B,YAAAA,MAAM,CAACG,IAAP,CAAYlB,GAAZ;;AAEA,gBAAGA,GAAG,CAAChB,KAAJ,IAAa,CAAb,IAAkBgB,GAAG,CAAChB,KAAJ,IAAa,CAAlC,EAAoC;AAChCgC,cAAAA,KAAK,CAAC9B,KAAN,CAAYgC,IAAZ,CAAiBlB,GAAjB;AACH;AACJ;;AACD,eAAKH,UAAL,CAAgBK,GAAhB,CAAoBC,EAApB,EAAuBY,MAAvB;AACH;;AAEMI,QAAAA,YAAY,CAACC,OAAD,EAAgBC,IAAhB,EAAiC;AAChD,cAAIL,KAAW,GAAG,KAAKrB,SAAL,CAAesB,GAAf,CAAmBG,OAAnB,CAAlB;;AACA,cAAGJ,KAAH,EAAS;AACLA,YAAAA,KAAK,CAACV,MAAN,GAAee,IAAf;AACH;AACJ;;AAEMC,QAAAA,eAAe,CAACnB,EAAD,EAAWJ,IAAX,EAA2B;AAC7C,cAAIwB,KAAa,GAAG,EAApB;;AACA,eAAI,IAAInC,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGW,IAAI,CAACV,MAAxB,EAAgCD,CAAC,EAAjC,EAAoC;AAChC,gBAAIY,GAAG,GAAGD,IAAI,CAACX,CAAD,CAAd;AACAmC,YAAAA,KAAK,CAACL,IAAN,CAAWlB,GAAX;AACH;;AACD,eAAKwB,SAAL,CAAetB,GAAf,CAAmBC,EAAnB,EAAsBoB,KAAtB;AACH;;AAEME,QAAAA,WAAW,CAACtB,EAAD,EAAWJ,IAAX,EAAyB;AACvC,cAAIwB,KAAK,GAAG,KAAKC,SAAL,CAAeP,GAAf,CAAmBd,EAAnB,CAAZ;;AACA,cAAIoB,KAAK,IAAI,IAAb,EAAmB;AACfA,YAAAA,KAAK,CAACL,IAAN,CAAWnB,IAAX;;AACA,iBAAKyB,SAAL,CAAetB,GAAf,CAAmBC,EAAnB,EAAuBoB,KAAvB;AACH,WAHD,MAGK;AACD,gBAAIG,CAAC,GAAG,EAAR;AACAA,YAAAA,CAAC,CAACR,IAAF,CAAOnB,IAAP;AACA,iBAAKuB,eAAL,CAAqBnB,EAArB,EAAyBuB,CAAzB;AACH;AACJ;;AAEMC,QAAAA,UAAU,CAACP,OAAD,EAAgB5B,GAAhB,EAAmC;AAChD,cAAIwB,KAAW,GAAG,KAAKrB,SAAL,CAAesB,GAAf,CAAmBG,OAAnB,CAAlB;;AACA,cAAG,CAACJ,KAAJ,EAAU;AACN,mBAAO,KAAP;AACH;;AAED,cAAI9B,KAAc,GAAG8B,KAAK,CAAC9B,KAAN,CAAYC,MAAZ,EAArB;;AACA,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAcA,CAAC,GAAGF,KAAK,CAACG,MAAxB,EAA+BD,CAAC,EAAhC,EAAmC;AAC/B,gBAAGF,KAAK,CAACE,CAAD,CAAL,CAASI,GAAT,IAAgBA,GAAhB,IAAuBN,KAAK,CAACE,CAAD,CAAL,CAASJ,KAAT,IAAkB,CAA5C,EAA8C;AAC1C,qBAAO,IAAP;AACH;AACJ;;AAED,iBAAO,KAAP;AACH;;AAEM4C,QAAAA,WAAW,GAAU;AACxB,cAAIC,IAAgB,GAAG;AAAA;AAAA,wCAAWC,WAAX,GAAyBC,SAAzB,CAAmCC,aAAnC,EAAvB;AACA,iBAAOH,IAAI,CAACI,OAAL,GAAe,CAAf,GAAiB,IAAjB,GAAsB,KAA7B;AACH;;AAEMC,QAAAA,YAAY,CAACd,OAAD,EAAwB;AACvC,cAAIe,QAAa,GAAG;AAAA;AAAA,4CAAaL,WAAb,GAA2BM,KAA3B,CAAiCC,WAAjC,EAApB;AACA,iBAAO,KAAKV,UAAL,CAAgBP,OAAhB,EAAwBe,QAAQ,CAAC3C,GAAjC,CAAP;AACH;;AAEM8C,QAAAA,aAAa,CAACnC,EAAD,EAAoB;AACpC,iBAAO,KAAKN,UAAL,CAAgBoB,GAAhB,CAAoBd,EAApB,CAAP;AACH;;AAEMoC,QAAAA,QAAQ,CAACpC,EAAD,EAAqB;AAAA,cAApBA,EAAoB;AAApBA,YAAAA,EAAoB,GAAR,CAAQ;AAAA;;AAChC,iBAAO,KAAKR,SAAL,CAAesB,GAAf,CAAmBd,EAAnB,CAAP;AACH;;AAEMqC,QAAAA,WAAW,CAACrC,EAAD,EAAsB;AAAA,cAArBA,EAAqB;AAArBA,YAAAA,EAAqB,GAAT,CAAS;AAAA;;AACpC,cAAIuB,CAAC,GAAG,KAAKF,SAAL,CAAeP,GAAf,CAAmBd,EAAnB,CAAR;;AACA,cAAIuB,CAAC,IAAI,IAAT,EAAc;AACV,mBAAO,CAAP;AACH;;AACDe,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BhB,CAA5B;AACA,iBAAOA,CAAC,CAACrC,MAAT;AACH;;AAnI2B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport LoginCommand from \"../login/LoginCommand\";\nimport { Role } from \"../login/LoginProxy\";\nimport { MapCityData } from \"../map/MapCityProxy\";\nimport MapCommand from \"../map/MapCommand\";\n\nexport class Apply {\n    id: number = 0;\n    rid: number = 0;\n    nick_name: string = \"\";\n}\n\n\nexport class Member {\n    rid: number = 0;\n    name: string = \"\";\n    title: number = 0;\n    public get titleDes() : string {\n        if(this.title == 0){\n            return \"盟主\";\n        }\n\n        if(this.title == 1){\n            return \"副盟主\";\n        }\n\n        return \"普通成员\"\n    }\n    x:number = 0;\n    y:number = 0;\n    \n}\n\n\nexport class Union {\n    id:number = 0;\n    name: string = \"\";\n    cnt: number = 0;\n    notice: string = \"\";\n    major:Member[] = [];\n\n\n    public getChairman():Member{\n        var major:Member[] = this.major.concat();\n        for(var i = 0;i < major.length;i++){\n            if(major[i].title == 0){\n                return major[i];\n            }\n        }\n        return new Member()\n    }\n\n    public getViceChairman():Member{\n        var major:Member[] = this.major.concat();\n        for(var i = 0;i < major.length;i++){\n            if(major[i].title == 1){\n                return major[i];\n            }\n        }\n        return new Member()\n    }\n\n    public isMajor(rid:number):boolean{\n        var major:Member[] = this.major.concat();\n        for(var i = 0;i < major.length;i++){\n            if(major[i].rid == rid){\n                return true\n            }\n        }\n        return false\n    }\n}\n\n\nexport default class UnionProxy {\n\n    private _unionMap:Map<number,Union> = new Map<number,Union>();\n    private _menberMap:Map<number,Member[]> = new Map<number,Member[]>();\n    private _applyMap:Map<number,Apply[]> = new Map<number,Apply[]>();\n\n    public clearData(): void {\n        this._unionMap.clear();\n        this._menberMap.clear();\n    }\n\n    public updateUnionList(data:any[]):void{\n        this._unionMap.clear();\n        for(var i = 0; i < data.length ;i++){\n            var obj = this.createUnion(data[i]);\n            this._unionMap.set(obj.id,obj);\n        }\n    }\n\n    protected createUnion(data:any):Union{\n        var obj = new Union();\n        obj.id = data.id;\n        obj.name = data.name;\n        obj.cnt = data.cnt;\n        obj.notice = data.notice;\n        obj.major = data.major.concat();\n        return obj\n    }\n\n    protected createMember(data:any):Member{\n        var obj = new Member();\n        obj.rid = data.rid;\n        obj.name = data.name;\n        obj.title = data.title;\n        obj.x = data.x;\n        obj.y = data.y;\n        return obj\n    }\n\n\n    public getUnionList():Union[]{\n        return Array.from(this._unionMap.values());\n    }\n\n\n    public updateMemberList(id:number,data:any[]):void{\n        var member:Member[] = [];\n        var union = this._unionMap.get(id);\n        if(union != null){\n            union.major = []\n        }\n        for(var i = 0; i < data.length ;i++){\n            var obj = this.createMember(data[i]);\n            member.push(obj);\n\n            if(obj.title == 0 || obj.title == 1){\n                union.major.push(obj)\n            }\n        }\n        this._menberMap.set(id,member);\n    }\n\n    public updateNotice(unionid:number,text:string):void{\n        let union:Union = this._unionMap.get(unionid);\n        if(union){\n            union.notice = text\n        }\n    }\n\n    public updateApplyList(id:number,data:any[]):void{\n        var apply:Apply[] = [];\n        for(var i = 0; i < data.length ;i++){\n            var obj = data[i]\n            apply.push(obj);\n        }\n        this._applyMap.set(id,apply);\n    }\n\n    public updateApply(id:number,data:any):void{\n        var apply = this._applyMap.get(id);\n        if (apply != null) {\n            apply.push(data)\n            this._applyMap.set(id, apply)\n        }else{\n            var t = []\n            t.push(data)\n            this.updateApplyList(id, t)\n        }\n    }\n\n    public isChairman(unionid:number,rid:number):boolean{\n        let union:Union = this._unionMap.get(unionid);\n        if(!union){\n            return false;\n        }\n\n        var major:Member[] = union.major.concat();\n        for(var i = 0;i < major.length;i++){\n            if(major[i].rid == rid && major[i].title == 0){\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    public isMeInUnion():boolean{\n        let city:MapCityData = MapCommand.getInstance().cityProxy.getMyMainCity();\n        return city.unionId > 0?true:false; \n    }\n\n    public isMeChairman(unionid:number):boolean{\n        var roleData:Role = LoginCommand.getInstance().proxy.getRoleData();\n        return this.isChairman(unionid,roleData.rid);\n    }\n\n    public getMemberList(id:number):Member[]{\n        return this._menberMap.get(id);\n    }\n\n    public getUnion(id:number = 0):Union{\n        return this._unionMap.get(id);\n    }\n\n    public getApplyCnt(id:number = 0):number{\n        let t = this._applyMap.get(id);\n        if (t == null){\n            return 0\n        }\n        console.log(\"getApplyCnt:\", t)\n        return t.length\n    }\n}\n"]}