{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/DrawRLogic.ts"], "names": ["_decorator", "Component", "Prefab", "Layout", "instantiate", "Vec3", "GeneralItemLogic", "GeneralItemType", "AudioManager", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "i", "_maxSize", "_generalNode", "generalItemPrefab", "parent", "tenLayout", "node", "scale", "_scale", "active", "oneLayout", "setData", "data", "length", "children", "com", "getComponent", "GeneralNoThing", "child", "onClickClose", "instance", "playClick"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAGtDC,MAAAA,gB;AAAoBC,MAAAA,e,iBAAAA,e;;AAClBC,MAAAA,Y,iBAAAA,Y;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;yBAMTW,U,WADpBF,OAAO,CAAC,YAAD,C,UAIHC,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACP,MAAD,C,oCAVb,MACqBQ,UADrB,SACwCV,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,4CAYpB,EAZoB;;AAAA,0CAatB,GAbsB;AAAA;;AAepCW,QAAAA,MAAM,GAAO;AAEnB,eAAI,IAAIC,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAG,KAAKC,QAAxB,EAAiCD,CAAC,EAAlC,EAAqC;AACjC,gBAAIE,YAAY,GAAGX,WAAW,CAAC,KAAKY,iBAAN,CAA9B;;AACAD,YAAAA,YAAY,CAACE,MAAb,GAAsB,KAAKC,SAAL,CAAeC,IAArC;AACAJ,YAAAA,YAAY,CAACK,KAAb,GAAqB,IAAIf,IAAJ,CAAS,KAAKgB,MAAd,EAAsB,KAAKA,MAA3B,EAAmC,KAAKA,MAAxC,CAArB;AACAN,YAAAA,YAAY,CAACO,MAAb,GAAsB,KAAtB;AACH;;AAGD,cAAIP,YAAY,GAAGX,WAAW,CAAC,KAAKY,iBAAN,CAA9B;;AACAD,UAAAA,YAAY,CAACE,MAAb,GAAsB,KAAKM,SAAL,CAAeJ,IAArC;AACAJ,UAAAA,YAAY,CAACK,KAAb,GAAqB,IAAIf,IAAJ,CAAS,KAAKgB,MAAd,EAAsB,KAAKA,MAA3B,EAAmC,KAAKA,MAAxC,CAArB;AACAN,UAAAA,YAAY,CAACO,MAAb,GAAsB,KAAtB;AAEH;;AAIME,QAAAA,OAAO,CAACC,IAAD,EAAe;AACzB,eAAKP,SAAL,CAAeC,IAAf,CAAoBG,MAApB,GAA6B,KAAKC,SAAL,CAAeJ,IAAf,CAAoBG,MAApB,GAA6B,KAA1D;;AACA,cAAGG,IAAI,CAACC,MAAL,IAAe,CAAlB,EAAoB;AAChB,iBAAKH,SAAL,CAAeJ,IAAf,CAAoBG,MAApB,GAA6B,IAA7B;AACA,gBAAIK,QAAQ,GAAG,KAAKJ,SAAL,CAAeJ,IAAf,CAAoBQ,QAAnC;AACA,gBAAIC,GAAG,GAAGD,QAAQ,CAAC,CAAD,CAAR,CAAYE,YAAZ;AAAA;AAAA,qDAAV;AACAF,YAAAA,QAAQ,CAAC,CAAD,CAAR,CAAYL,MAAZ,GAAqB,IAArB;;AACA,gBAAGM,GAAH,EAAO;AACHA,cAAAA,GAAG,CAACJ,OAAJ,CAAYC,IAAI,CAAC,CAAD,CAAhB,EAAoB;AAAA;AAAA,sDAAgBK,cAApC;AACH;AAEJ,WATD,MASK;AACD,iBAAKZ,SAAL,CAAeC,IAAf,CAAoBG,MAApB,GAA6B,IAA7B;AACA,gBAAIK,QAAQ,GAAG,KAAKT,SAAL,CAAeC,IAAf,CAAoBQ,QAAnC;;AACA,iBAAI,IAAId,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAG,KAAKC,QAAxB,EAAiCD,CAAC,EAAlC,EAAqC;AACjC,kBAAIkB,KAAK,GAAGJ,QAAQ,CAACd,CAAD,CAApB;;AACA,kBAAGY,IAAI,CAACZ,CAAD,CAAP,EAAW;AACPkB,gBAAAA,KAAK,CAACT,MAAN,GAAe,IAAf;AACA,oBAAIM,GAAG,GAAGG,KAAK,CAACF,YAAN;AAAA;AAAA,yDAAV;;AACA,oBAAGD,GAAH,EAAO;AACHA,kBAAAA,GAAG,CAACJ,OAAJ,CAAYC,IAAI,CAACZ,CAAD,CAAhB,EAAoB;AAAA;AAAA,0DAAgBiB,cAApC;AACH;AACJ,eAND,MAOI;AACAC,gBAAAA,KAAK,CAACT,MAAN,GAAe,KAAf;AACH;AACJ;AACJ;AAEJ;;AAGSU,QAAAA,YAAY,GAAS;AAC3B,eAAKb,IAAL,CAAUG,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,4CAAaW,QAAb,CAAsBC,SAAtB;AACH;;AArE6C,O;;;;;iBAIlB,I;;;;;;;iBAGT,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Prefab, Layout, instantiate, Vec3 } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport GeneralItemLogic, { GeneralItemType } from \"./GeneralItemLogic\";\nimport { AudioManager } from '../../common/AudioManager';\n\n@ccclass('DrawRLogic')\nexport default class DrawRLogic extends Component {\n\n\n    @property(Prefab)\n    generalItemPrefab: Prefab = null;\n\n    @property(Layout)\n    tenLayout:Layout = null;\n\n    @property(Layout)\n    oneLayout:Layout = null;\n\n    private _maxSize:number = 10;\n    private _scale:number = 0.4;\n\n    protected onLoad():void{\n\n        for(var i = 0; i < this._maxSize;i++){\n            let _generalNode = instantiate(this.generalItemPrefab);\n            _generalNode.parent = this.tenLayout.node;\n            _generalNode.scale = new Vec3(this._scale, this._scale, this._scale);\n            _generalNode.active = false;\n        }\n\n\n        let _generalNode = instantiate(this.generalItemPrefab);\n        _generalNode.parent = this.oneLayout.node;\n        _generalNode.scale = new Vec3(this._scale, this._scale, this._scale);\n        _generalNode.active = false;\n\n    }\n\n\n\n    public setData(data:any):void{\n        this.tenLayout.node.active = this.oneLayout.node.active = false;\n        if(data.length == 1){\n            this.oneLayout.node.active = true;\n            var children = this.oneLayout.node.children;\n            let com = children[0].getComponent(GeneralItemLogic);\n            children[0].active = true;\n            if(com){\n                com.setData(data[0],GeneralItemType.GeneralNoThing);\n            }\n\n        }else{\n            this.tenLayout.node.active = true;\n            var children = this.tenLayout.node.children;\n            for(var i = 0; i < this._maxSize;i++){\n                var child = children[i];\n                if(data[i]){\n                    child.active = true;\n                    let com = child.getComponent(GeneralItemLogic);\n                    if(com){\n                        com.setData(data[i],GeneralItemType.GeneralNoThing);\n                    }\n                }\n                else{\n                    child.active = false;\n                }\n            }\n        }\n\n    }\n\n\n    protected onClickClose(): void {\n        this.node.active = false;\n        AudioManager.instance.playClick();\n    }\n\n\n\n\n\n}\n"]}