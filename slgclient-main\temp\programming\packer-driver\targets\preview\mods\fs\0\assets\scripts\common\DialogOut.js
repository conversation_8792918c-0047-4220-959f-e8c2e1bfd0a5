System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _decorator, Component, Vec3, tween, _dec, _class, _crd, ccclass, property, DialogOut;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec3 = _cc.Vec3;
      tween = _cc.tween;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3ccd1gkd7pMvrVSNZjjYInt", "DialogOut", undefined);

      ({
        ccclass,
        property
      } = _decorator);

      _export("DialogOut", DialogOut = (_dec = ccclass('DialogOut'), _dec(_class = class DialogOut extends Component {
        onEnable() {
          console.log("DialogOut onEnable");
          this.node.setScale(new Vec3(0.2, 0.2, 0.2));
          var lt = tween(this.node).to(0.2, {
            scale: new Vec3(1, 1, 1)
          }, {
            easing: 'sineOut'
          });
          lt.start();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=DialogOut.js.map