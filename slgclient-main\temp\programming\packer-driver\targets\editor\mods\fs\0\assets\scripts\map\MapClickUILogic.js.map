{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/MapClickUILogic.ts"], "names": ["_decorator", "Component", "Node", "Label", "ProgressBar", "<PERSON><PERSON>", "tween", "UIOpacity", "UITransform", "ArmyCmd", "DateUtil", "MapBuildData", "MapCityData", "MapCommand", "MapResData", "MapResType", "MapUICommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "MapClickUILogic", "onLoad", "onDestroy", "_data", "_pixelPos", "onEnable", "on", "updateBuild", "onUpdateBuild", "uiOpacity", "bgSelect", "getComponent", "opacity", "t", "to", "repeatF<PERSON><PERSON>", "start", "_t", "onDisable", "targetOff", "stop", "stopCountDown", "data", "x", "y", "setCellData", "onClickEnter", "console", "log", "instance", "playClick", "emit", "openFortressAbout", "openCityAbout", "node", "parent", "onClickReclaim", "openArmySelectUi", "Reclaim", "onClickGiveUp", "getInstance", "giveUpBuild", "onClickBuild", "build", "FORTRESS", "onClickTransfer", "Transfer", "onClickMove", "isCanMoveCell", "Garrison", "onTagAdd", "opPosTag", "labelName", "string", "onTagRemove", "onClickOccupy", "isCanOccupyCell", "Attack", "pixelPos", "labelPos", "leftInfoNode", "active", "btnReclaim", "btnEnter", "width", "height", "isTag", "proxy", "isPosTag", "btnTagAdd", "btnTagRemove", "btnMove", "btnOccupy", "btnGiveUp", "btnBuild", "btnTransfer", "durableNode", "rid", "buildProxy", "myId", "isInGiveUp", "isResBuild", "isWarFree", "isBuilding", "isSysCity", "isSysFortress", "unionId", "myUnionId", "parentId", "labelDurable", "Math", "ceil", "curDurable", "maxDurable", "progressBarDurable", "progress", "cityProxy", "setContentSize", "type", "SYS_CITY", "level", "resData", "getResData", "id", "resCfg", "getResConfig", "soldiers", "getDefenseSoldiers", "labelYield", "getResYieldDesList", "join", "labelSoldierCnt", "nick<PERSON><PERSON>", "name", "labelUnion", "unionName", "labelLunxian", "limitTime", "getWarFree", "diff", "getServerTime", "occupyTime", "bgMain", "labelMian", "schedule", "countDown", "str", "converSecondStr", "unscheduleAllCallbacks"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAkBC,MAAAA,W,OAAAA,W;;AAGxFC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;AACEC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,W,iBAAAA,W;;AACFC,MAAAA,U;;AACgBC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,U,iBAAAA,U;;AAC5BC,MAAAA,Y;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;OAXH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBrB,U;;yBAcTsB,e,WADpBF,OAAO,CAAC,iBAAD,C,UAEHC,QAAQ,CAACnB,IAAD,C,UAERmB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAAClB,KAAD,C,UAERkB,QAAQ,CAACnB,IAAD,C,UAGRmB,QAAQ,CAACnB,IAAD,C,WAERmB,QAAQ,CAAClB,KAAD,C,WAERkB,QAAQ,CAACjB,WAAD,C,WAERiB,QAAQ,CAACnB,IAAD,C,WAERmB,QAAQ,CAAClB,KAAD,C,WAERkB,QAAQ,CAAClB,KAAD,C,WAERkB,QAAQ,CAAChB,MAAD,C,WAERgB,QAAQ,CAAChB,MAAD,C,WAERgB,QAAQ,CAAChB,MAAD,C,WAERgB,QAAQ,CAAChB,MAAD,C,WAERgB,QAAQ,CAAChB,MAAD,C,WAERgB,QAAQ,CAAChB,MAAD,C,WAERgB,QAAQ,CAAChB,MAAD,C,WAGRgB,QAAQ,CAAChB,MAAD,C,WAERgB,QAAQ,CAAChB,MAAD,C,oCA9Cb,MACqBiB,eADrB,SAC6CrB,SAD7C,CACuD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,yCAgD5B,IAhD4B;;AAAA,6CAiDvB,IAjDuB;;AAAA,sCAkDpC,IAlDoC;AAAA;;AAoDzCsB,QAAAA,MAAM,GAAS,CAExB;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKC,SAAL,GAAiB,IAAjB;AACH;;AAESC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,WAAvB,EAAoC,KAAKC,aAAzC,EAAwD,IAAxD;AAEA,cAAIC,SAAS,GAAG,KAAKC,QAAL,CAAcC,YAAd,CAA2B1B,SAA3B,CAAhB;AACAwB,UAAAA,SAAS,CAACG,OAAV,GAAoB,GAApB;AAEA,cAAIC,CAAC,GAAG7B,KAAK,CAACyB,SAAD,CAAL,CAAiBK,EAAjB,CAAoB,GAApB,EAAyB;AAAEF,YAAAA,OAAO,EAAE;AAAX,WAAzB,EAAyCE,EAAzC,CAA4C,GAA5C,EAAiD;AAAEF,YAAAA,OAAO,EAAE;AAAX,WAAjD,CAAR;AACAC,UAAAA,CAAC,GAAGA,CAAC,CAACE,aAAF,CAAgBF,CAAhB,CAAJ;AACAA,UAAAA,CAAC,CAACG,KAAF;AAEA,eAAKC,EAAL,GAAUJ,CAAV;AAEH;;AAESK,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;;AACA,eAAKF,EAAL,CAAQG,IAAR;;AACA,eAAKC,aAAL;AACH;;AAESb,QAAAA,aAAa,CAACc,IAAD,EAA2B;AAC9C,cAAI,KAAKnB,KAAL,IACG,KAAKA,KAAL;AAAA;AAAA,2CADH,IAEG,KAAKA,KAAL,CAAWoB,CAAX,IAAgBD,IAAI,CAACC,CAFxB,IAGG,KAAKpB,KAAL,CAAWqB,CAAX,IAAgBF,IAAI,CAACE,CAH5B,EAG+B;AAC3B,iBAAKC,WAAL,CAAiBH,IAAjB,EAAuB,KAAKlB,SAA5B;AACH;AACJ;;AAESsB,QAAAA,YAAY,GAAS;AAC3BC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB;;AAEA,cAAI,KAAK3B,KAAL;AAAA;AAAA,2CAAJ,EAAuC;AACnC;AAAA;AAAA,sCAAS4B,IAAT,CAAc;AAAA;AAAA,0CAAWC,iBAAzB,EAA4C,KAAK7B,KAAjD;AACH,WAFD,MAEM,IAAI,KAAKA,KAAL;AAAA;AAAA,yCAAJ,EAAsC;AACxC;AAAA;AAAA,sCAAS4B,IAAT,CAAc;AAAA;AAAA,0CAAWE,aAAzB,EAAwC,KAAK9B,KAA7C;AACH;;AAED,eAAK+B,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAESC,QAAAA,cAAc,GAAS;AAC7B;AAAA;AAAA,4CAAaP,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWM,gBAAzB,EAA2C;AAAA;AAAA,kCAAQC,OAAnD,EAA4D,KAAKnC,KAAL,CAAWoB,CAAvE,EAA0E,KAAKpB,KAAL,CAAWqB,CAArF;AACA,eAAKU,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAESI,QAAAA,aAAa,GAAS;AAC5B;AAAA;AAAA,4CAAaV,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,wCAAWU,WAAX,GAAyBC,WAAzB,CAAqC,KAAKtC,KAAL,CAAWoB,CAAhD,EAAmD,KAAKpB,KAAL,CAAWqB,CAA9D;AACA,eAAKU,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAESO,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAab,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,wCAAWU,WAAX,GAAyBG,KAAzB,CAA+B,KAAKxC,KAAL,CAAWoB,CAA1C,EAA6C,KAAKpB,KAAL,CAAWqB,CAAxD,EAA2D;AAAA;AAAA,wCAAWoB,QAAtE;AACA,eAAKV,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAESU,QAAAA,eAAe,GAAQ;AAC7B;AAAA;AAAA,4CAAahB,QAAb,CAAsBC,SAAtB;AACAH,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ;AACA,eAAKM,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACA;AAAA;AAAA,oCAASJ,IAAT,CAAc;AAAA;AAAA,wCAAWM,gBAAzB,EAA2C;AAAA;AAAA,kCAAQS,QAAnD,EAA6D,KAAK3C,KAAL,CAAWoB,CAAxE,EAA2E,KAAKpB,KAAL,CAAWqB,CAAtF;AACH;;AAESuB,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAalB,QAAb,CAAsBC,SAAtB;;AACA,cAAI;AAAA;AAAA,wCAAWU,WAAX,GAAyBQ,aAAzB,CAAuC,KAAK7C,KAAL,CAAWoB,CAAlD,EAAqD,KAAKpB,KAAL,CAAWqB,CAAhE,CAAJ,EAAwE;AACpE;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,0CAAWM,gBAAzB,EAA2C;AAAA;AAAA,oCAAQY,QAAnD,EAA6D,KAAK9C,KAAL,CAAWoB,CAAxE,EAA2E,KAAKpB,KAAL,CAAWqB,CAAtF;AACH,WAFD,MAEO;AACHG,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACH;;AACD,eAAKM,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAGSe,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,4CAAarB,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,wCAAWU,WAAX,GAAyBW,QAAzB,CAAkC,CAAlC,EAAqC,KAAKhD,KAAL,CAAWoB,CAAhD,EAAmD,KAAKpB,KAAL,CAAWqB,CAA9D,EAAiE,KAAK4B,SAAL,CAAeC,MAAhF;AACA,eAAKnB,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAESmB,QAAAA,WAAW,GAAS;AAC1B;AAAA;AAAA,4CAAazB,QAAb,CAAsBC,SAAtB;AACA;AAAA;AAAA,wCAAWU,WAAX,GAAyBW,QAAzB,CAAkC,CAAlC,EAAqC,KAAKhD,KAAL,CAAWoB,CAAhD,EAAmD,KAAKpB,KAAL,CAAWqB,CAA9D;AACA,eAAKU,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAESoB,QAAAA,aAAa,GAAS;AAC5B;AAAA;AAAA,4CAAa1B,QAAb,CAAsBC,SAAtB;;AACA,cAAI;AAAA;AAAA,wCAAWU,WAAX,GAAyBgB,eAAzB,CAAyC,KAAKrD,KAAL,CAAWoB,CAApD,EAAuD,KAAKpB,KAAL,CAAWqB,CAAlE,CAAJ,EAA0E;AACtE;AAAA;AAAA,sCAASO,IAAT,CAAc;AAAA;AAAA,0CAAWM,gBAAzB,EAA2C;AAAA;AAAA,oCAAQoB,MAAnD,EAA2D,KAAKtD,KAAL,CAAWoB,CAAtE,EAAyE,KAAKpB,KAAL,CAAWqB,CAApF;AACH,WAFD,MAEO;AACHG,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ;AACH;;AAED,eAAKM,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAEMV,QAAAA,WAAW,CAACH,IAAD,EAAYoC,QAAZ,EAAkC;AAChD,eAAKvD,KAAL,GAAamB,IAAb;AACA,eAAKlB,SAAL,GAAiBsD,QAAjB;AACA,eAAKC,QAAL,CAAcN,MAAd,GAAuB,MAAM/B,IAAI,CAACC,CAAX,GAAe,IAAf,GAAsBD,IAAI,CAACE,CAA3B,GAA+B,GAAtD;AACA,eAAKoC,YAAL,CAAkBC,MAAlB,GAA2B,IAA3B;AACA,eAAKC,UAAL,CAAgB5B,IAAhB,CAAqB2B,MAArB,GAA8B,KAA9B;AACA,eAAKE,QAAL,CAAc7B,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,eAAKnD,QAAL,CAAcC,YAAd,CAA2BzB,WAA3B,EAAwC8E,KAAxC,GAAgD,GAAhD;AACA,eAAKtD,QAAL,CAAcC,YAAd,CAA2BzB,WAA3B,EAAwC+E,MAAxC,GAAiD,GAAjD;AAEA,cAAIC,KAAK,GAAG;AAAA;AAAA,wCAAW1B,WAAX,GAAyB2B,KAAzB,CAA+BC,QAA/B,CAAwC,KAAKjE,KAAL,CAAWoB,CAAnD,EAAsD,KAAKpB,KAAL,CAAWqB,CAAjE,CAAZ,CAVgD,CAYhD;;AAEA,eAAK6C,SAAL,CAAenC,IAAf,CAAoB2B,MAApB,GAA6B,CAACK,KAA9B;AACA,eAAKI,YAAL,CAAkBpC,IAAlB,CAAuB2B,MAAvB,GAAgCK,KAAhC;;AAEA,cAAI,KAAK/D,KAAL;AAAA;AAAA,uCAAJ,EAAsC;AAClC;AACA,iBAAKoE,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,KAA3B;AACA,iBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,IAA7B;AACA,iBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,iBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,iBAAKc,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACA,iBAAKe,WAAL,CAAiBf,MAAjB,GAA0B,KAA1B;AAEH,WATD,MASO,IAAI,KAAK1D,KAAL;AAAA;AAAA,2CAAJ,EAAwC;AAC3C;AACA,gBAAK,KAAKA,KAAN,CAA6B0E,GAA7B,IAAoC;AAAA;AAAA,0CAAWrC,WAAX,GAAyBsC,UAAzB,CAAoCC,IAA5E,EAAkF;AAC9E;AACA,mBAAKR,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,IAA3B;AACA,mBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,CAAC,KAAK1D,KAAL,CAAW6E,UAAX,EAA9B;AACA,mBAAKlB,UAAL,CAAgB5B,IAAhB,CAAqB2B,MAArB,GAA8B,KAAK1D,KAAL,CAAW8E,UAAX,EAA9B;AACA,mBAAKP,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,CAAC,KAAK1D,KAAL,CAAW+E,SAAX,EAA7B,CAN8E,CAQ9E;;AACA,kBAAG,KAAK/E,KAAL,CAAW8E,UAAX,EAAH,EAA2B;AACvB,qBAAKN,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACA,qBAAKE,QAAL,CAAc7B,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,qBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,CAAC,KAAK1D,KAAL,CAAWgF,UAAX,EAA7B;AACH,eAJD,MAIM,IAAG,KAAKhF,KAAL,CAAWiF,SAAX,EAAH,EAA0B;AAC5B,qBAAKT,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACA,qBAAKE,QAAL,CAAc7B,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,qBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACH,eAJK,MAIA,IAAG,KAAK1D,KAAL,CAAWkF,aAAd,EAA4B;AAC9B,qBAAKV,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,IAA/B;AACA,qBAAKE,QAAL,CAAc7B,IAAd,CAAmB2B,MAAnB,GAA4B,IAA5B;AACA,qBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACH;;AAED,kBAAI,KAAK1D,KAAL,CAAW6E,UAAX,EAAJ,EAA4B;AACxB,qBAAKN,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACH;;AAED,kBAAI,KAAK1D,KAAL,CAAW+E,SAAX,EAAJ,EAA2B;AACvB,qBAAKT,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACH;AAEJ,aA/BD,MA+BO,IAAK,KAAK1D,KAAN,CAA6BmF,OAA7B,GAAuC,CAAvC,IACH,KAAKnF,KAAN,CAA6BmF,OAA7B,IAAwC;AAAA;AAAA,0CAAW9C,WAAX,GAAyBsC,UAAzB,CAAoCS,SAD5E,EACuF;AAC1F;AACA,mBAAKhB,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,IAA3B;AACA,mBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,mBAAKc,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACH,aARM,MAQA,IAAK,KAAK1D,KAAN,CAA6BqF,QAA7B,GAAwC,CAAxC,IACP,KAAKrF,KAAN,CAA6BqF,QAA7B,IAAyC;AAAA;AAAA,0CAAWhD,WAAX,GAAyBsC,UAAzB,CAAoCS,SADzE,EACoF;AACvF;AACA,mBAAKhB,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,IAA3B;AACA,mBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,mBAAKc,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACH,aARM,MAQD;AACF,mBAAKU,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,KAA3B;AACA,mBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,IAA7B;AACA,mBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,mBAAKc,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACH;;AACD,iBAAKe,WAAL,CAAiBf,MAAjB,GAA0B,IAA1B;AACA,iBAAK4B,YAAL,CAAkBpC,MAAlB,GAA2BqC,IAAI,CAACC,IAAL,CAAU,KAAKxF,KAAL,CAAWyF,UAAX,GAAsB,GAAhC,IAAuC,GAAvC,GAA8CF,IAAI,CAACC,IAAL,CAAU,KAAKxF,KAAL,CAAW0F,UAAX,GAAsB,GAAhC,CAAzE;AACA,iBAAKC,kBAAL,CAAwBC,QAAxB,GAAmC,KAAK5F,KAAL,CAAWyF,UAAX,GAAwB,KAAKzF,KAAL,CAAW0F,UAAtE;AACH,WA3DM,MA2DA,IAAI,KAAK1F,KAAL;AAAA;AAAA,yCAAJ,EAAuC;AAC1C;AACA,gBAAI,KAAKA,KAAL,CAAW0E,GAAX,IAAkB;AAAA;AAAA,0CAAWrC,WAAX,GAAyBwD,SAAzB,CAAmCjB,IAAzD,EAA+D;AAC3D;AACA,mBAAKhB,QAAL,CAAc7B,IAAd,CAAmB2B,MAAnB,GAA4B,IAA5B;AACA,mBAAKU,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,KAA3B;AACA,mBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,mBAAKc,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACA,mBAAKQ,SAAL,CAAenC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKS,YAAL,CAAkBpC,IAAlB,CAAuB2B,MAAvB,GAAgC,KAAhC;AAEH,aAXD,MAWO,IAAK,KAAK1D,KAAN,CAA4BmF,OAA5B,GAAsC,CAAtC,IACH,KAAKnF,KAAN,CAA4BmF,OAA5B,IAAuC;AAAA;AAAA,0CAAW9C,WAAX,GAAyBwD,SAAzB,CAAmCT,SAD1E,EACqF;AACxF;AACA,mBAAKhB,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,IAA3B;AACA,mBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,mBAAKc,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACH,aARM,MAQD,IAAK,KAAK1D,KAAN,CAA4BqF,QAA5B,GAAuC,CAAvC,IACF,KAAKrF,KAAN,CAA4BqF,QAA5B,IAAwC;AAAA;AAAA,0CAAWhD,WAAX,GAAyBwD,SAAzB,CAAmCT,SAD5E,EACuF;AACzF;AACA,mBAAKhB,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,IAA3B;AACA,mBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,mBAAKc,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACH,aARK,MAQA;AACF,mBAAKU,OAAL,CAAarC,IAAb,CAAkB2B,MAAlB,GAA2B,KAA3B;AACA,mBAAKW,SAAL,CAAetC,IAAf,CAAoB2B,MAApB,GAA6B,IAA7B;AACA,mBAAKY,SAAL,CAAevC,IAAf,CAAoB2B,MAApB,GAA6B,KAA7B;AACA,mBAAKa,QAAL,CAAcxC,IAAd,CAAmB2B,MAAnB,GAA4B,KAA5B;AACA,mBAAKc,WAAL,CAAiBzC,IAAjB,CAAsB2B,MAAtB,GAA+B,KAA/B;AACH;;AACD,iBAAKnD,QAAL,CAAcC,YAAd,CAA2BzB,WAA3B,EAAwC+G,cAAxC,CAAuD,GAAvD,EAA4D,GAA5D;AACA,iBAAKrC,YAAL,CAAkBC,MAAlB,GAA2B,KAA3B;AACA,iBAAKe,WAAL,CAAiBf,MAAjB,GAA0B,IAA1B;AACA,iBAAK4B,YAAL,CAAkBpC,MAAlB,GAA2BqC,IAAI,CAACC,IAAL,CAAU,KAAKxF,KAAL,CAAWyF,UAAX,GAAsB,GAAhC,IAAuC,GAAvC,GAA8CF,IAAI,CAACC,IAAL,CAAU,KAAKxF,KAAL,CAAW0F,UAAX,GAAsB,GAAhC,CAAzE;AACA,iBAAKC,kBAAL,CAAwBC,QAAxB,GAAmC,KAAK5F,KAAL,CAAWyF,UAAX,GAAwB,KAAKzF,KAAL,CAAW0F,UAAtE;AAEH;;AAID,cAAG,KAAK1F,KAAL,CAAW+F,IAAX,IAAmB;AAAA;AAAA,wCAAWC,QAAjC,EAA0C;AAEtC,gBAAG,KAAKhG,KAAL,CAAWiG,KAAX,IAAoB,CAAvB,EAAyB;AACrB,mBAAK1F,QAAL,CAAcC,YAAd,CAA2BzB,WAA3B,EAAwC+G,cAAxC,CAAuD,MAAI,GAA3D,EAAgE,MAAI,GAApE;AACH,aAFD,MAEM,IAAG,KAAK9F,KAAL,CAAWiG,KAAX,IAAoB,CAAvB,EAAyB;AAC3B,mBAAK1F,QAAL,CAAcC,YAAd,CAA2BzB,WAA3B,EAAwC+G,cAAxC,CAAuD,GAAvD,EAA4D,GAA5D;AACH,aAFK,MAEA;AACF,mBAAKvF,QAAL,CAAcC,YAAd,CAA2BzB,WAA3B,EAAwC+G,cAAxC,CAAuD,MAAI,GAA3D,EAAgE,MAAI,GAApE;AACH;AACJ;;AAGD,cAAI,KAAKrC,YAAL,CAAkBC,MAAtB,EAA+B;AAE3B,gBAAIwC,OAAmB,GAAG;AAAA;AAAA,0CAAW7D,WAAX,GAAyB2B,KAAzB,CAA+BmC,UAA/B,CAA0C,KAAKnG,KAAL,CAAWoG,EAArD,CAA1B;AACA,gBAAIC,MAAoB,GAAG;AAAA;AAAA,0CAAWhE,WAAX,GAAyB2B,KAAzB,CAA+BsC,YAA/B,CAA4CJ,OAAO,CAACH,IAApD,EAA0DG,OAAO,CAACD,KAAlE,CAA3B;AAEA,gBAAIM,QAAQ,GAAG;AAAA;AAAA,8CAAalE,WAAb,GAA2B2B,KAA3B,CAAiCwC,kBAAjC,CAAoDN,OAAO,CAACD,KAA5D,CAAf;AACA,iBAAKQ,UAAL,CAAgBvD,MAAhB,GAAyB;AAAA;AAAA,0CAAWb,WAAX,GAAyB2B,KAAzB,CAA+B0C,kBAA/B,CAAkDL,MAAlD,EAA0DM,IAA1D,CAA+D,IAA/D,CAAzB;AACA,iBAAKC,eAAL,CAAqB1D,MAArB,GAA8B,UAAUqD,QAAQ,GAAC,CAAjD;;AAEA,gBAAI,KAAKvG,KAAL,CAAW6G,QAAf,EAAwB;AACpB,mBAAK5D,SAAL,CAAeC,MAAf,GAAwB,KAAKlD,KAAL,CAAW6G,QAAX,GAAsB,GAAtB,GAA4B,KAAK7G,KAAL,CAAW8G,IAA/D;AACH,aAFD,MAEK;AACD,mBAAK7D,SAAL,CAAeC,MAAf,GAAwBmD,MAAM,CAACS,IAA/B;AACH;AACJ,WAdD,MAcO;AACH,iBAAK7D,SAAL,CAAeC,MAAf,GAAwB,KAAKlD,KAAL,CAAW8G,IAAnC;AACH,WA/J+C,CAiKhD;;;AACA,cAAI,KAAK9G,KAAL,CAAW0E,GAAX,IAAkB,IAAlB,IAA0B,KAAK1E,KAAL,CAAW0E,GAAX,IAAkB,CAAhD,EAAkD;AAC9C,iBAAKqC,UAAL,CAAgB7D,MAAhB,GAAyB,KAAzB;AACH,WAFD,MAEK;AACD,gBAAI,KAAKlD,KAAL,CAAWmF,OAAX,GAAqB,CAAzB,EAA2B;AACvB,mBAAK4B,UAAL,CAAgB7D,MAAhB,GAAyB,KAAKlD,KAAL,CAAWgH,SAApC;AACH,aAFD,MAEK;AACD,mBAAKD,UAAL,CAAgB7D,MAAhB,GAAyB,IAAzB;AACH;AACJ;;AAED,cAAI,KAAKlD,KAAL,CAAWqF,QAAX,GAAsB,CAA1B,EAA4B;AACxB,iBAAK4B,YAAL,CAAkB/D,MAAlB,GAA2B,IAA3B;AACH,WAFD,MAEK;AACD,iBAAK+D,YAAL,CAAkB/D,MAAlB,GAA2B,EAA3B;AACH,WAhL+C,CAmLhD;;;AACA,cAAIgE,SAAS,GAAG;AAAA;AAAA,wCAAW7E,WAAX,GAAyB2B,KAAzB,CAA+BmD,UAA/B,EAAhB;;AACA,cAAIC,IAAI,GAAG;AAAA;AAAA,oCAASC,aAAT,KAA2B,KAAKrH,KAAL,CAAWsH,UAAjD;;AACA,cAAI,KAAKtH,KAAL;AAAA;AAAA,2CAAJ,EAAuC;AACnC,gBAAGoH,IAAI,GAAGF,SAAV,EAAoB;AAChB,mBAAKK,MAAL,CAAY7D,MAAZ,GAAqB,KAArB;AACA,mBAAK8D,SAAL,CAAetE,MAAf,GAAwB,EAAxB;AACH,aAHD,MAGK;AACD,mBAAKqE,MAAL,CAAY7D,MAAZ,GAAqB,IAArB;AACA,mBAAK+D,QAAL,CAAc,KAAKC,SAAnB,EAA8B,CAA9B;AACA,mBAAKA,SAAL;AACH;AAEJ,WAVD,MAUM,IAAG,KAAK1H,KAAL;AAAA;AAAA,yCAAH,EAAqC;AACvC,gBAAGoH,IAAI,GAAGF,SAAP,IAAoB,KAAKlH,KAAL,CAAWqF,QAAX,GAAsB,CAA7C,EAA+C;AAC3C,mBAAKkC,MAAL,CAAY7D,MAAZ,GAAqB,IAArB;AACA,mBAAK+D,QAAL,CAAc,KAAKC,SAAnB,EAA8B,CAA9B;AACA,mBAAKA,SAAL;AACH,aAJD,MAIK;AACD,mBAAKH,MAAL,CAAY7D,MAAZ,GAAqB,KAArB;AACA,mBAAK8D,SAAL,CAAetE,MAAf,GAAwB,EAAxB;AACH;AACJ;AACJ;;AAEMwE,QAAAA,SAAS,GAAG;AACf,cAAIN,IAAI,GAAG;AAAA;AAAA,oCAASC,aAAT,KAA2B,KAAKrH,KAAL,CAAWsH,UAAjD;;AACA,cAAIJ,SAAS,GAAG;AAAA;AAAA,wCAAW7E,WAAX,GAAyB2B,KAAzB,CAA+BmD,UAA/B,EAAhB;;AACA,cAAIC,IAAI,GAACF,SAAT,EAAmB;AACf,iBAAKhG,aAAL;AAEH,WAHD,MAGK;AACD,gBAAIyG,GAAG,GAAG;AAAA;AAAA,sCAASC,eAAT,CAAyBV,SAAS,GAACE,IAAnC,CAAV;AACA,iBAAKI,SAAL,CAAetE,MAAf,GAAwB,QAAQyE,GAAhC;AACH;AACJ;;AAEMzG,QAAAA,aAAa,GAAG;AACnB,eAAK2G,sBAAL;AACA,eAAKL,SAAL,CAAetE,MAAf,GAAwB,EAAxB;AACA,eAAKqE,MAAL,CAAY7D,MAAZ,GAAqB,KAArB;AACH;;AA9XkD,O;;;;;iBAElC,I;;;;;;;iBAEE,I;;;;;;;iBAEC,I;;;;;;;iBAEE,I;;;;;;;iBAEJ,I;;;;;;;iBAEC,I;;;;;;;iBAEJ,I;;;;;;;iBAGK,I;;;;;;;iBAEE,I;;;;;;;iBAEY,I;;;;;;;iBAEb,I;;;;;;;iBAED,I;;;;;;;iBAEK,I;;;;;;;iBAEP,I;;;;;;;iBAEE,I;;;;;;;iBAEA,I;;;;;;;iBAEC,I;;;;;;;iBAEF,I;;;;;;;iBAEA,I;;;;;;;iBAEG,I;;;;;;;iBAGF,I;;;;;;;iBAEG,I", "sourcesContent": ["import { _decorator, Component, Node, Label, ProgressBar, Button, Vec2, tween, UIOpacity, Tween, UITransform, UIRenderable } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport { ArmyCmd } from \"../general/ArmyProxy\";\nimport DateUtil from \"../utils/DateUtil\";\nimport { MapBuildData } from \"./MapBuildProxy\";\nimport { MapCityData } from \"./MapCityProxy\";\nimport MapCommand from \"./MapCommand\";\nimport { MapResConfig, MapResData, MapResType } from \"./MapProxy\";\nimport MapUICommand from \"./ui/MapUICommand\";\nimport { EventMgr } from '../utils/EventMgr';\nimport { AudioManager } from '../common/AudioManager';\nimport { LogicEvent } from '../common/LogicEvent';\n\n@ccclass('MapClickUILogic')\nexport default class MapClickUILogic extends Component {\n    @property(Node)\n    bgSelect: Node = null;\n    @property(Label)\n    labelName: Label = null;\n    @property(Label)\n    labelUnion: Label = null;\n    @property(Label)\n    labelLunxian: Label = null;\n    @property(Label)\n    labelPos: Label = null;\n    @property(Label)\n    labelMian: Label = null;\n    @property(Node)\n    bgMain: Node = null;\n\n    @property(Node)\n    durableNode: Node = null;\n    @property(Label)\n    labelDurable: Label = null;\n    @property(ProgressBar)\n    progressBarDurable: ProgressBar = null;\n    @property(Node)\n    leftInfoNode: Node = null;\n    @property(Label)\n    labelYield: Label = null;\n    @property(Label)\n    labelSoldierCnt: Label = null;\n    @property(Button)\n    btnMove: Button = null;\n    @property(Button)\n    btnOccupy: Button = null;\n    @property(Button)\n    btnGiveUp: Button = null;\n    @property(Button)\n    btnReclaim: Button = null;\n    @property(Button)\n    btnEnter: Button = null;\n    @property(Button)\n    btnBuild: Button = null;\n    @property(Button)\n    btnTransfer: Button = null;\n\n    @property(Button)\n    btnTagAdd: Button = null;\n    @property(Button)\n    btnTagRemove: Button = null;\n\n    protected _data: any = null;\n    protected _pixelPos: Vec2 = null;\n    protected _t = null;\n    \n    protected onLoad(): void {\n\n    }\n\n    protected onDestroy(): void {\n        this._data = null;\n        this._pixelPos = null;\n    }\n\n    protected onEnable(): void {\n        EventMgr.on(LogicEvent.updateBuild, this.onUpdateBuild, this);\n\n        var uiOpacity = this.bgSelect.getComponent(UIOpacity);\n        uiOpacity.opacity = 255;\n\n        let t = tween(uiOpacity).to(0.8, { opacity: 0 }).to(0.8, { opacity: 255 });\n        t = t.repeatForever(t);\n        t.start();\n\n        this._t = t;\n    \n    }\n\n    protected onDisable(): void {\n        EventMgr.targetOff(this);\n        this._t.stop();\n        this.stopCountDown();\n    }\n\n    protected onUpdateBuild(data: MapBuildData): void {\n        if (this._data\n            && this._data instanceof MapBuildData\n            && this._data.x == data.x\n            && this._data.y == data.y) {\n            this.setCellData(data, this._pixelPos);\n        }\n    }\n\n    protected onClickEnter(): void {\n        console.log(\"onClickEnter\");\n        AudioManager.instance.playClick();\n\n        if (this._data instanceof MapBuildData){\n            EventMgr.emit(LogicEvent.openFortressAbout, this._data);\n        }else if (this._data instanceof MapCityData){\n            EventMgr.emit(LogicEvent.openCityAbout, this._data);\n        }\n       \n        this.node.parent = null;\n    }\n\n    protected onClickReclaim(): void {\n        AudioManager.instance.playClick();\n        EventMgr.emit(LogicEvent.openArmySelectUi, ArmyCmd.Reclaim, this._data.x, this._data.y);\n        this.node.parent = null;\n    }\n\n    protected onClickGiveUp(): void {\n        AudioManager.instance.playClick();\n        MapCommand.getInstance().giveUpBuild(this._data.x, this._data.y);\n        this.node.parent = null;\n    }\n\n    protected onClickBuild(): void {\n        AudioManager.instance.playClick();\n        MapCommand.getInstance().build(this._data.x, this._data.y, MapResType.FORTRESS);\n        this.node.parent = null;\n    }\n\n    protected onClickTransfer(): void{\n        AudioManager.instance.playClick();\n        console.log(\"onClickTransfer\");\n        this.node.parent = null;\n        EventMgr.emit(LogicEvent.openArmySelectUi, ArmyCmd.Transfer, this._data.x, this._data.y);\n    }\n\n    protected onClickMove(): void {\n        AudioManager.instance.playClick();\n        if (MapCommand.getInstance().isCanMoveCell(this._data.x, this._data.y)) {\n            EventMgr.emit(LogicEvent.openArmySelectUi, ArmyCmd.Garrison, this._data.x, this._data.y);\n        } else {\n            console.log(\"只能驻军自己占领的地\");\n        }\n        this.node.parent = null;\n    }\n\n\n    protected onTagAdd(): void {\n        AudioManager.instance.playClick();\n        MapCommand.getInstance().opPosTag(1, this._data.x, this._data.y, this.labelName.string);\n        this.node.parent = null;\n    }\n\n    protected onTagRemove(): void {\n        AudioManager.instance.playClick();\n        MapCommand.getInstance().opPosTag(0, this._data.x, this._data.y);\n        this.node.parent = null;\n    }\n\n    protected onClickOccupy(): void {\n        AudioManager.instance.playClick();\n        if (MapCommand.getInstance().isCanOccupyCell(this._data.x, this._data.y)) {\n            EventMgr.emit(LogicEvent.openArmySelectUi, ArmyCmd.Attack, this._data.x, this._data.y);\n        } else {\n            console.log(\"只能占领自己相邻的地\");\n        }\n\n        this.node.parent = null;\n    }\n\n    public setCellData(data: any, pixelPos: Vec2): void {\n        this._data = data;\n        this._pixelPos = pixelPos;\n        this.labelPos.string = \"(\" + data.x + \", \" + data.y + \")\";\n        this.leftInfoNode.active = true;\n        this.btnReclaim.node.active = false;\n        this.btnEnter.node.active = false;\n        this.bgSelect.getComponent(UITransform).width = 200;\n        this.bgSelect.getComponent(UITransform).height = 100;\n    \n        var isTag = MapCommand.getInstance().proxy.isPosTag(this._data.x, this._data.y);\n\n        // console.log(\"isTag:\", isTag);\n\n        this.btnTagAdd.node.active = !isTag;\n        this.btnTagRemove.node.active = isTag;\n        \n        if (this._data instanceof MapResData) {\n            //点击的是野外\n            this.btnMove.node.active = false;\n            this.btnOccupy.node.active = true;\n            this.btnGiveUp.node.active = false;\n            this.btnBuild.node.active = false;\n            this.btnTransfer.node.active = false;\n            this.durableNode.active = false;\n\n        } else if (this._data instanceof MapBuildData) {\n            //点击的是占领地\n            if ((this._data as MapBuildData).rid == MapCommand.getInstance().buildProxy.myId) {\n                //我自己的地\n                this.btnMove.node.active = true;\n                this.btnOccupy.node.active = false;\n                this.btnGiveUp.node.active = !this._data.isInGiveUp();\n                this.btnReclaim.node.active = this._data.isResBuild();\n                this.btnBuild.node.active = !this._data.isWarFree();\n\n                //是资源地\n                if(this._data.isResBuild()){\n                    this.btnTransfer.node.active = false;\n                    this.btnEnter.node.active = false;\n                    this.btnBuild.node.active = !this._data.isBuilding();\n                }else if(this._data.isSysCity()){\n                    this.btnTransfer.node.active = false;\n                    this.btnEnter.node.active = false;\n                    this.btnBuild.node.active = false;\n                }else if(this._data.isSysFortress){\n                    this.btnTransfer.node.active = true;\n                    this.btnEnter.node.active = true;\n                    this.btnBuild.node.active = false;\n                }\n\n                if (this._data.isInGiveUp()){\n                    this.btnBuild.node.active = false;\n                }\n\n                if (this._data.isWarFree()){\n                    this.btnGiveUp.node.active = false;\n                }\n\n            } else if ((this._data as MapBuildData).unionId > 0\n                && (this._data as MapBuildData).unionId == MapCommand.getInstance().buildProxy.myUnionId) {\n                //盟友的地\n                this.btnMove.node.active = true;\n                this.btnOccupy.node.active = false;\n                this.btnGiveUp.node.active = false;\n                this.btnBuild.node.active = false;\n                this.btnTransfer.node.active = false;\n            } else if ((this._data as MapBuildData).parentId > 0\n            && (this._data as MapBuildData).parentId == MapCommand.getInstance().buildProxy.myUnionId) {\n                //俘虏的地\n                this.btnMove.node.active = true;\n                this.btnOccupy.node.active = false;\n                this.btnGiveUp.node.active = false;\n                this.btnBuild.node.active = false;\n                this.btnTransfer.node.active = false;\n            }else {\n                this.btnMove.node.active = false;\n                this.btnOccupy.node.active = true;\n                this.btnGiveUp.node.active = false;\n                this.btnBuild.node.active = false;\n                this.btnTransfer.node.active = false;\n            }\n            this.durableNode.active = true;\n            this.labelDurable.string = Math.ceil(this._data.curDurable/100) + \"/\" +  Math.ceil(this._data.maxDurable/100);\n            this.progressBarDurable.progress = this._data.curDurable / this._data.maxDurable;\n        } else if (this._data instanceof MapCityData) {\n            //点击其他城市\n            if (this._data.rid == MapCommand.getInstance().cityProxy.myId) {\n                //我自己的城池\n                this.btnEnter.node.active = true;\n                this.btnMove.node.active = false;\n                this.btnOccupy.node.active = false;\n                this.btnGiveUp.node.active = false;\n                this.btnBuild.node.active = false;\n                this.btnTransfer.node.active = false;\n                this.btnTagAdd.node.active = false;\n                this.btnTagRemove.node.active = false;\n\n            } else if ((this._data as MapCityData).unionId > 0\n                && (this._data as MapCityData).unionId == MapCommand.getInstance().cityProxy.myUnionId) {\n                //盟友的城池\n                this.btnMove.node.active = true;\n                this.btnOccupy.node.active = false;\n                this.btnGiveUp.node.active = false;\n                this.btnBuild.node.active = false;\n                this.btnTransfer.node.active = false;\n            }else if ((this._data as MapCityData).parentId > 0\n                && (this._data as MapCityData).parentId == MapCommand.getInstance().cityProxy.myUnionId) {\n                //俘虏的城池\n                this.btnMove.node.active = true;\n                this.btnOccupy.node.active = false;\n                this.btnGiveUp.node.active = false;\n                this.btnBuild.node.active = false;\n                this.btnTransfer.node.active = false;\n            }else {\n                this.btnMove.node.active = false;\n                this.btnOccupy.node.active = true;\n                this.btnGiveUp.node.active = false;\n                this.btnBuild.node.active = false;\n                this.btnTransfer.node.active = false;\n            }\n            this.bgSelect.getComponent(UITransform).setContentSize(600, 300);\n            this.leftInfoNode.active = false;\n            this.durableNode.active = true;\n            this.labelDurable.string = Math.ceil(this._data.curDurable/100) + \"/\" +  Math.ceil(this._data.maxDurable/100);\n            this.progressBarDurable.progress = this._data.curDurable / this._data.maxDurable;\n      \n        }\n\n       \n\n        if(this._data.type == MapResType.SYS_CITY){\n\n            if(this._data.level >= 8){\n                this.bgSelect.getComponent(UITransform).setContentSize(960*1.5, 480*1.5);\n            }else if(this._data.level >= 5){\n                this.bgSelect.getComponent(UITransform).setContentSize(960, 480);\n            }else {\n                this.bgSelect.getComponent(UITransform).setContentSize(960*0.5, 480*0.5);\n            }\n        }\n\n\n        if (this.leftInfoNode.active ) {\n\n            let resData: MapResData = MapCommand.getInstance().proxy.getResData(this._data.id);\n            let resCfg: MapResConfig = MapCommand.getInstance().proxy.getResConfig(resData.type, resData.level);\n        \n            let soldiers = MapUICommand.getInstance().proxy.getDefenseSoldiers(resData.level);\n            this.labelYield.string = MapCommand.getInstance().proxy.getResYieldDesList(resCfg).join(\"\\n\");\n            this.labelSoldierCnt.string = \"守备兵力 \" + soldiers*3;\n            \n            if (this._data.nickName){\n                this.labelName.string = this._data.nickName + \":\" + this._data.name;\n            }else{\n                this.labelName.string = resCfg.name;\n            }\n        } else {\n            this.labelName.string = this._data.name;\n        }\n\n        //归属属性\n        if (this._data.rid == null || this._data.rid == 0){\n            this.labelUnion.string = \"未占领\";\n        }else{\n            if (this._data.unionId > 0){\n                this.labelUnion.string = this._data.unionName;\n            }else{\n                this.labelUnion.string = \"在野\";\n            }\n        }\n\n        if (this._data.parentId > 0){\n            this.labelLunxian.string = \"沦陷\";\n        }else{\n            this.labelLunxian.string = \"\";\n        }\n\n\n        //免战信息\n        var limitTime = MapCommand.getInstance().proxy.getWarFree();\n        var diff = DateUtil.getServerTime() - this._data.occupyTime;\n        if (this._data instanceof MapBuildData){\n            if(diff > limitTime){\n                this.bgMain.active = false;\n                this.labelMian.string = \"\";\n            }else{\n                this.bgMain.active = true;\n                this.schedule(this.countDown, 1);\n                this.countDown()\n            }\n\n        }else if(this._data instanceof MapCityData){\n            if(diff < limitTime && this._data.parentId > 0){\n                this.bgMain.active = true;\n                this.schedule(this.countDown, 1);\n                this.countDown()\n            }else{\n                this.bgMain.active = false;\n                this.labelMian.string = \"\";\n            }\n        }\n    }\n\n    public countDown() {\n        var diff = DateUtil.getServerTime() - this._data.occupyTime;\n        var limitTime = MapCommand.getInstance().proxy.getWarFree();\n        if (diff>limitTime){\n            this.stopCountDown();\n            \n        }else{\n            var str = DateUtil.converSecondStr(limitTime-diff);\n            this.labelMian.string = \"免战：\" + str;\n        }\n    }\n\n    public stopCountDown() {\n        this.unscheduleAllCallbacks();\n        this.labelMian.string = \"\";\n        this.bgMain.active = false;\n    }\n}\n"]}