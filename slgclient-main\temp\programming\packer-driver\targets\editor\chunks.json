{"assets/scripts/chat/ChatItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/chat/ChatItemLogic.js", "mTimestamp": 1754004266862}, "assets/scripts/chat/ChatCommand.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/network/socket/NetManager.js", "__unresolved_2": "assets/scripts/config/ServerConfig.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_3": "assets/scripts/chat/ChatProxy.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/chat/ChatCommand.js", "mTimestamp": 1754004266899}, "assets/scripts/chat/ChatProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/chat/ChatProxy.js", "mTimestamp": 1754004266831}, "assets/scripts/Main.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_3": "assets/scripts/general/ArmyCommand.js", "__unresolved_1": "assets/scripts/config/GameConfig.js", "__unresolved_5": "assets/scripts/login/LoginCommand.js", "__unresolved_2": "assets/scripts/core/LoaderManager.js", "__unresolved_4": "assets/scripts/general/GeneralCommand.js", "__unresolved_6": "assets/scripts/map/MapCommand.js", "__unresolved_8": "assets/scripts/network/http/HttpManager.js", "__unresolved_7": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_9": "assets/scripts/network/socket/NetInterface.js", "__unresolved_10": "assets/scripts/network/socket/NetManager.js", "__unresolved_11": "assets/scripts/network/socket/NetNode.js", "__unresolved_13": "assets/scripts/utils/Toast.js", "__unresolved_12": "assets/scripts/skill/SkillCommand.js", "__unresolved_14": "assets/scripts/utils/Tools.js", "__unresolved_16": "assets/scripts/common/AudioManager.js", "__unresolved_17": "assets/scripts/common/LogicEvent.js", "__unresolved_15": "assets/scripts/utils/EventMgr.js", "__unresolved_20": "assets/scripts/test/SimpleLoginTest.js", "__unresolved_18": "assets/scripts/utils/FixedScreenAdapter.js", "__unresolved_19": "assets/scripts/utils/ClickTestHelper.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/Main.js", "mTimestamp": 1754004266801}, "assets/scripts/chat/ChatLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/utils/ListLogic.js", "__unresolved_2": "assets/scripts/chat/ChatCommand.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/chat/ChatLogic.js", "mTimestamp": 1754004266959}, "assets/scripts/common/AudioManager.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/LocalCache.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/common/AudioManager.js", "mTimestamp": 1754004267012}, "assets/scripts/cloud/CloudAni.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/cloud/CloudAni.js", "mTimestamp": 1754004266915}, "assets/scripts/common/BgLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/common/BgLogic.js", "mTimestamp": 1754004267059}, "assets/scripts/common/DialogOut.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/common/DialogOut.js", "mTimestamp": 1754004267025}, "assets/scripts/common/LoadingLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_1": "assets/scripts/core/coreEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/common/LoadingLogic.js", "mTimestamp": 1754004267084}, "assets/scripts/common/LogicEvent.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/common/LogicEvent.js", "mTimestamp": 1754004266984}, "assets/scripts/common/PanelOut.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/common/PanelOut.js", "mTimestamp": 1754004266993}, "assets/scripts/config/GameConfig.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/config/GameConfig.js", "mTimestamp": 1754004267176}, "assets/scripts/config/ServerConfig.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/config/ServerConfig.js", "mTimestamp": 1754004267225}, "assets/scripts/config/skill/Skill.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/config/skill/Skill.js", "mTimestamp": 1754004267243}, "assets/scripts/core/LoaderManager.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/EventMgr.js", "__unresolved_2": "assets/scripts/core/coreEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/core/LoaderManager.js", "mTimestamp": 1754004267293}, "assets/scripts/config/HttpConfig.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/config/HttpConfig.js", "mTimestamp": 1754004267092}, "assets/scripts/config/Basci.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/config/Basci.js", "mTimestamp": 1754004267266}, "assets/scripts/core/CoreEvent.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/core/CoreEvent.js", "mTimestamp": 1754004267216}, "assets/scripts/general/ArmyCommand.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/network/socket/NetManager.js", "__unresolved_3": "assets/scripts/general/ArmyProxy.js", "__unresolved_1": "assets/scripts/config/ServerConfig.js", "__unresolved_5": "assets/scripts/general/GeneralProxy.js", "__unresolved_4": "assets/scripts/general/GeneralCommand.js", "__unresolved_6": "assets/scripts/utils/EventMgr.js", "__unresolved_7": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/general/ArmyCommand.js", "mTimestamp": 1754004267166}, "assets/scripts/general/ArmyProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/general/ArmyProxy.js", "mTimestamp": 1754004267207}, "assets/scripts/libs/convert.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/libs/convert.js", "mTimestamp": 1754004267408}, "assets/scripts/general/GeneralCommand.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/network/socket/NetManager.js", "__unresolved_1": "assets/scripts/config/ServerConfig.js", "__unresolved_3": "assets/scripts/general/GeneralProxy.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/general/GeneralCommand.js", "mTimestamp": 1754004267390}, "assets/scripts/libs/NameDict.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/libs/NameDict.js", "mTimestamp": 1754004267591}, "assets/scripts/general/GeneralProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/general/GeneralProxy.js", "mTimestamp": 1754004267340}, "assets/scripts/libs/crypto/md5.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/libs/crypto/md5.js", "mTimestamp": 1754004267484}, "assets/scripts/libs/gzip/crc32.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/libs/gzip/crc32.js", "mTimestamp": 1754004267628}, "assets/scripts/libs/gzip/deflate-js.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/libs/gzip/rawinflate.js", "__unresolved_2": "assets/scripts/libs/gzip/rawdeflate.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/libs/gzip/deflate-js.js", "mTimestamp": 1754004267837}, "assets/scripts/libs/gzip/gzip.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/libs/gzip/deflate-js.js", "__unresolved_1": "assets/scripts/libs/gzip/crc32.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/libs/gzip/gzip.js", "mTimestamp": 1754004267616}, "assets/scripts/libs/crypto/crypto.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/libs/crypto/crypto.js", "mTimestamp": 1754004267829}, "assets/scripts/libs/gzip/rawinflate.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/libs/gzip/rawinflate.js", "mTimestamp": 1754004268238}, "assets/scripts/login/CreateLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/config/ServerConfig.js", "__unresolved_2": "assets/scripts/login/LoginCommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/libs/NameDict.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/login/CreateLogic.js", "mTimestamp": 1754004267660}, "assets/scripts/login/ExitDialogController.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/login/ExitDialogController.js", "mTimestamp": 1754004267915}, "assets/scripts/libs/gzip/rawdeflate.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/libs/gzip/rawdeflate.js", "mTimestamp": 1754004268148}, "assets/scripts/login/LoginCommand.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/config/ServerConfig.js", "__unresolved_1": "assets/scripts/config/HttpConfig.js", "__unresolved_3": "assets/scripts/network/http/HttpManager.js", "__unresolved_8": "assets/scripts/map/MapCommand.js", "__unresolved_4": "assets/scripts/network/socket/NetManager.js", "__unresolved_7": "assets/scripts/network/socket/NetInterface.js", "__unresolved_5": "assets/scripts/utils/Tools.js", "__unresolved_6": "assets/scripts/login/LoginProxy.js", "__unresolved_9": "assets/scripts/utils/LocalCache.js", "__unresolved_10": "assets/scripts/utils/DateUtil.js", "__unresolved_13": "assets/scripts/common/LogicEvent.js", "__unresolved_12": "assets/scripts/libs/crypto/md5.js", "__unresolved_11": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/login/LoginCommand.js", "mTimestamp": 1754004268003}, "assets/scripts/login/LoginFormController.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/LoginCommand.js", "__unresolved_3": "assets/scripts/common/LogicEvent.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/login/LoginFormController.js", "mTimestamp": 1754004267957}, "assets/scripts/login/LoginDialogController.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/common/LogicEvent.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/login/LoginDialogController.js", "mTimestamp": 1754004267887}, "assets/scripts/login/LoginLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/login/LoginCommand.js", "__unresolved_4": "assets/scripts/common/AudioManager.js", "__unresolved_1": "assets/scripts/utils/LocalCache.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/login/LoginLogic.js", "mTimestamp": 1754004268034}, "assets/scripts/map/MapArmyLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/general/ArmyProxy.js", "__unresolved_1": "assets/scripts/general/ArmyCommand.js", "__unresolved_3": "assets/scripts/map/entries/ArmyLogic.js", "__unresolved_6": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/map/MapCommand.js", "__unresolved_5": "assets/scripts/map/MapUtil.js", "__unresolved_7": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapArmyLogic.js", "mTimestamp": 1754004268321}, "assets/scripts/login/MainMenuController.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/login/MainMenuController.js", "mTimestamp": 1754004268174}, "assets/scripts/login/LoginProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/login/LoginProxy.js", "mTimestamp": 1754004268387}, "assets/scripts/login/SimpleLoginUI.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/LoginCommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/login/SimpleLoginUI.js", "mTimestamp": 1754004268284}, "assets/scripts/map/MapBaseLayerLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapCommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapBaseLayerLogic.js", "mTimestamp": 1754004268352}, "assets/scripts/map/MapBuildTagLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/entries/BuildTagLogic.js", "__unresolved_2": "assets/scripts/map/MapBaseLayerLogic.js", "__unresolved_3": "assets/scripts/map/MapUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapBuildTagLogic.js", "mTimestamp": 1754004268369}, "assets/scripts/map/MapBuildProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_3": "assets/scripts/map/MapProxy.js", "__unresolved_5": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/map/MapUtil.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapBuildProxy.js", "mTimestamp": 1754004268531}, "assets/scripts/map/MapCityLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapBaseLayerLogic.js", "__unresolved_2": "assets/scripts/map/entries/CityLogic.js", "__unresolved_3": "assets/scripts/map/MapUtil.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapCityLogic.js", "mTimestamp": 1754004268450}, "assets/scripts/map/MapBuildTipsLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/entries/BuildTipsLogic.js", "__unresolved_2": "assets/scripts/map/MapBaseLayerLogic.js", "__unresolved_3": "assets/scripts/map/MapUtil.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapBuildTipsLogic.js", "mTimestamp": 1754004268421}, "assets/scripts/map/MapCityProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapUtil.js", "__unresolved_3": "assets/scripts/common/LogicEvent.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapCityProxy.js", "mTimestamp": 1754004268658}, "assets/scripts/map/MapClickUILogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyProxy.js", "__unresolved_2": "assets/scripts/utils/DateUtil.js", "__unresolved_3": "assets/scripts/map/MapBuildProxy.js", "__unresolved_7": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_4": "assets/scripts/map/MapCityProxy.js", "__unresolved_5": "assets/scripts/map/MapCommand.js", "__unresolved_8": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/map/MapProxy.js", "__unresolved_9": "assets/scripts/common/AudioManager.js", "__unresolved_10": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapClickUILogic.js", "mTimestamp": 1754004268792}, "assets/scripts/map/MapLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/common/LogicEvent.js", "__unresolved_2": "assets/scripts/map/MapUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapLogic.js", "mTimestamp": 1754004268833}, "assets/scripts/map/MapCommand.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/config/ServerConfig.js", "__unresolved_2": "assets/scripts/general/GeneralCommand.js", "__unresolved_3": "assets/scripts/network/socket/NetManager.js", "__unresolved_4": "assets/scripts/utils/DateUtil.js", "__unresolved_5": "assets/scripts/map/MapBuildProxy.js", "__unresolved_9": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/map/MapCityProxy.js", "__unresolved_7": "assets/scripts/map/MapProxy.js", "__unresolved_10": "assets/scripts/common/LogicEvent.js", "__unresolved_8": "assets/scripts/map/MapUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapCommand.js", "mTimestamp": 1754004268619}, "assets/scripts/map/MapProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_1": "assets/scripts/map/MapUtil.js", "__unresolved_3": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapProxy.js", "mTimestamp": 1754004268899}, "assets/scripts/map/MapFacilityBuildLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/entries/FacilityBuildLogic.js", "__unresolved_2": "assets/scripts/map/MapBaseLayerLogic.js", "__unresolved_3": "assets/scripts/map/MapUtil.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapFacilityBuildLogic.js", "mTimestamp": 1754004268678}, "assets/scripts/map/MapResBuildLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/entries/ResBuildLogic.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_2": "assets/scripts/map/MapBaseLayerLogic.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_3": "assets/scripts/map/MapUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapResBuildLogic.js", "mTimestamp": 1754004268853}, "assets/scripts/map/MapResLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/entries/ResLogic.js", "__unresolved_3": "assets/scripts/map/MapUtil.js", "__unresolved_2": "assets/scripts/map/MapBaseLayerLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapResLogic.js", "mTimestamp": 1754004268911}, "assets/scripts/map/MapTouchLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_1": "assets/scripts/map/MapClickUILogic.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/map/MapUtil.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapTouchLogic.js", "mTimestamp": 1754004268942}, "assets/scripts/map/MapSysCityLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapBaseLayerLogic.js", "__unresolved_3": "assets/scripts/map/entries/SysCityLogic.js", "__unresolved_2": "assets/scripts/map/MapUtil.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapSysCityLogic.js", "mTimestamp": 1754004268963}, "assets/scripts/map/MapUtil.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapCommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/MapUtil.js", "mTimestamp": 1754004268992}, "assets/scripts/map/entries/ArmyLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/map/MapUtil.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/entries/ArmyLogic.js", "mTimestamp": 1754004269040}, "assets/scripts/map/entries/BuildTagLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/common/LogicEvent.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/entries/BuildTagLogic.js", "mTimestamp": 1754004269063}, "assets/scripts/map/entries/FacilityBuildLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/map/MapProxy.js", "__unresolved_4": "assets/scripts/map/MapUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/entries/FacilityBuildLogic.js", "mTimestamp": 1754004269163}, "assets/scripts/map/entries/CityLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/entries/CityLogic.js", "mTimestamp": 1754004269098}, "assets/scripts/map/entries/ResBuildLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapCommand.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/entries/ResBuildLogic.js", "mTimestamp": 1754004269276}, "assets/scripts/map/entries/BuildTipsLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/entries/BuildTipsLogic.js", "mTimestamp": 1754004269014}, "assets/scripts/map/ui/ArmySelectItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyProxy.js", "__unresolved_2": "assets/scripts/general/GeneralCommand.js", "__unresolved_3": "assets/scripts/general/ArmyCommand.js", "__unresolved_4": "assets/scripts/map/ui/GeneralHeadLogic.js", "__unresolved_5": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/common/AudioManager.js", "__unresolved_7": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/ArmySelectItemLogic.js", "mTimestamp": 1754004269366}, "assets/scripts/map/entries/SysCityLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/map/MapProxy.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/entries/SysCityLogic.js", "mTimestamp": 1754004269132}, "assets/scripts/map/entries/ResLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapProxy.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/entries/ResLogic.js", "mTimestamp": 1754004269249}, "assets/scripts/map/ui/ArmySelectNodeLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyCommand.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/map/ui/ArmySelectItemLogic.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/ArmySelectNodeLogic.js", "mTimestamp": 1754004269189}, "assets/scripts/map/ui/CityAboutLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyCommand.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_2": "assets/scripts/map/ui/CityArmyItemLogic.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/CityAboutLogic.js", "mTimestamp": 1754004269221}, "assets/scripts/map/ui/CityArmyItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyProxy.js", "__unresolved_3": "assets/scripts/general/ArmyCommand.js", "__unresolved_2": "assets/scripts/general/GeneralCommand.js", "__unresolved_6": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_5": "assets/scripts/map/ui/GeneralHeadLogic.js", "__unresolved_7": "assets/scripts/common/AudioManager.js", "__unresolved_8": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/CityArmyItemLogic.js", "mTimestamp": 1754004269318}, "assets/scripts/map/ui/CityArmySettingLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyProxy.js", "__unresolved_2": "assets/scripts/general/GeneralCommand.js", "__unresolved_3": "assets/scripts/general/ArmyCommand.js", "__unresolved_5": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_4": "assets/scripts/general/GeneralProxy.js", "__unresolved_6": "assets/scripts/map/MapCommand.js", "__unresolved_8": "assets/scripts/login/LoginCommand.js", "__unresolved_7": "assets/scripts/map/ui/CityGeneralItemLogic.js", "__unresolved_9": "assets/scripts/utils/EventMgr.js", "__unresolved_10": "assets/scripts/common/AudioManager.js", "__unresolved_11": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/CityArmySettingLogic.js", "mTimestamp": 1754004269500}, "assets/scripts/map/ui/CityGeneralItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyCommand.js", "__unresolved_2": "assets/scripts/general/GeneralCommand.js", "__unresolved_3": "assets/scripts/general/GeneralProxy.js", "__unresolved_4": "assets/scripts/utils/DateUtil.js", "__unresolved_5": "assets/scripts/map/ui/GeneralHeadLogic.js", "__unresolved_7": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_8": "assets/scripts/common/AudioManager.js", "__unresolved_9": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/CityGeneralItemLogic.js", "mTimestamp": 1754004269562}, "assets/scripts/map/ui/CollectLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/LoginCommand.js", "__unresolved_2": "assets/scripts/utils/DateUtil.js", "__unresolved_4": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_3": "assets/scripts/utils/Tools.js", "__unresolved_5": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/common/AudioManager.js", "__unresolved_7": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/CollectLogic.js", "mTimestamp": 1754004269394}, "assets/scripts/map/ui/Dialog.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/Dialog.js", "mTimestamp": 1754004269609}, "assets/scripts/map/ui/DrawRLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/ui/GeneralItemLogic.js", "__unresolved_2": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/DrawRLogic.js", "mTimestamp": 1754004269634}, "assets/scripts/map/ui/DrawLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/GeneralCommand.js", "__unresolved_2": "assets/scripts/login/LoginCommand.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/DrawLogic.js", "mTimestamp": 1754004269589}, "assets/scripts/map/ui/FacilityAdditionItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_2": "assets/scripts/map/ui/MapUIProxy.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/FacilityAdditionItemLogic.js", "mTimestamp": 1754004269726}, "assets/scripts/map/ui/FacilityDesLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_2": "assets/scripts/login/LoginCommand.js", "__unresolved_3": "assets/scripts/utils/DateUtil.js", "__unresolved_4": "assets/scripts/map/ui/FacilityAdditionItemLogic.js", "__unresolved_5": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/FacilityDesLogic.js", "mTimestamp": 1754004269774}, "assets/scripts/map/ui/FacilityItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_3": "assets/scripts/common/AudioManager.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_4": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/FacilityItemLogic.js", "mTimestamp": 1754004269661}, "assets/scripts/map/ui/FacilityListLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/ui/FacilityDesLogic.js", "__unresolved_2": "assets/scripts/map/ui/FacilityItemLogic.js", "__unresolved_3": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/FacilityListLogic.js", "mTimestamp": 1754004269701}, "assets/scripts/map/ui/FortressAbout.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyCommand.js", "__unresolved_2": "assets/scripts/utils/DateUtil.js", "__unresolved_3": "assets/scripts/map/MapCommand.js", "__unresolved_4": "assets/scripts/map/MapProxy.js", "__unresolved_5": "assets/scripts/map/ui/CityArmyItemLogic.js", "__unresolved_6": "assets/scripts/utils/EventMgr.js", "__unresolved_7": "assets/scripts/common/AudioManager.js", "__unresolved_8": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/FortressAbout.js", "mTimestamp": 1754004269825}, "assets/scripts/map/ui/GeneralAddPrLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/general/GeneralCommand.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/general/GeneralProxy.js", "__unresolved_4": "assets/scripts/map/ui/GeneralItemLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralAddPrLogic.js", "mTimestamp": 1754004269874}, "assets/scripts/map/ui/GeneralComposeLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/GeneralCommand.js", "__unresolved_2": "assets/scripts/map/ui/GeneralItemLogic.js", "__unresolved_4": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_6": "assets/scripts/utils/ListLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralComposeLogic.js", "mTimestamp": 1754004269940}, "assets/scripts/map/ui/GeneralConvertLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/GeneralCommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_2": "assets/scripts/map/ui/GeneralItemLogic.js", "__unresolved_4": "assets/scripts/common/AudioManager.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_6": "assets/scripts/utils/ListLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralConvertLogic.js", "mTimestamp": 1754004269911}, "assets/scripts/map/ui/GeneralDesLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_3": "assets/scripts/skill/SkillCommand.js", "__unresolved_1": "assets/scripts/general/GeneralCommand.js", "__unresolved_2": "assets/scripts/general/GeneralProxy.js", "__unresolved_4": "assets/scripts/map/ui/GeneralItemLogic.js", "__unresolved_6": "assets/scripts/utils/EventMgr.js", "__unresolved_7": "assets/scripts/common/AudioManager.js", "__unresolved_5": "assets/scripts/map/ui/SkillIconLogic.js", "__unresolved_8": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralDesLogic.js", "mTimestamp": 1754004269997}, "assets/scripts/map/ui/GeneralItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/GeneralCommand.js", "__unresolved_2": "assets/scripts/general/GeneralProxy.js", "__unresolved_3": "assets/scripts/map/ui/GeneralHeadLogic.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralItemLogic.js", "mTimestamp": 1754004270081}, "assets/scripts/map/ui/GeneralInfoLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_2": "assets/scripts/common/LogicEvent.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralInfoLogic.js", "mTimestamp": 1754004270038}, "assets/scripts/map/ui/GeneralHeadLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/GeneralCommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralHeadLogic.js", "mTimestamp": 1754004270008}, "assets/scripts/map/ui/GeneralListLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/GeneralCommand.js", "__unresolved_2": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/utils/ListLogic.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralListLogic.js", "mTimestamp": 1754004270117}, "assets/scripts/map/ui/GeneralRosterLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/GeneralProxy.js", "__unresolved_2": "assets/scripts/map/ui/GeneralHeadLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralRosterLogic.js", "mTimestamp": 1754004270152}, "assets/scripts/map/ui/MapUILogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/LoginCommand.js", "__unresolved_3": "assets/scripts/map/ui/CityArmySettingLogic.js", "__unresolved_2": "assets/scripts/map/ui/ArmySelectNodeLogic.js", "__unresolved_7": "assets/scripts/union/UnionCommand.js", "__unresolved_4": "assets/scripts/map/ui/FacilityListLogic.js", "__unresolved_6": "assets/scripts/map/ui/Dialog.js", "__unresolved_5": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_8": "assets/scripts/map/MapCommand.js", "__unresolved_9": "assets/scripts/map/ui/FortressAbout.js", "__unresolved_13": "assets/scripts/utils/Tools.js", "__unresolved_10": "assets/scripts/map/ui/CityAboutLogic.js", "__unresolved_12": "assets/scripts/map/ui/TransformLogic.js", "__unresolved_11": "assets/scripts/map/ui/GeneralListLogic.js", "__unresolved_15": "assets/scripts/map/ui/WarReportLogic.js", "__unresolved_14": "assets/scripts/map/ui/GeneralInfoLogic.js", "__unresolved_16": "assets/scripts/map/ui/DrawRLogic.js", "__unresolved_18": "assets/scripts/map/ui/SkillInfoLogic.js", "__unresolved_17": "assets/scripts/map/ui/SkillLogic.js", "__unresolved_19": "assets/scripts/utils/EventMgr.js", "__unresolved_21": "assets/scripts/common/LogicEvent.js", "__unresolved_20": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/MapUILogic.js", "mTimestamp": 1754004270400}, "assets/scripts/map/ui/MapUICommand.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/config/ServerConfig.js", "__unresolved_3": "assets/scripts/network/socket/NetManager.js", "__unresolved_2": "assets/scripts/login/LoginCommand.js", "__unresolved_4": "assets/scripts/map/MapCommand.js", "__unresolved_5": "assets/scripts/map/ui/MapUIProxy.js", "__unresolved_6": "assets/scripts/utils/EventMgr.js", "__unresolved_7": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/MapUICommand.js", "mTimestamp": 1754004270221}, "assets/scripts/map/ui/GeneralRosterListLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/common/LogicEvent.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/general/GeneralCommand.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/utils/ListLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/GeneralRosterListLogic.js", "mTimestamp": 1754004270172}, "assets/scripts/map/ui/MapUIProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/LoginCommand.js", "__unresolved_2": "assets/scripts/utils/DateUtil.js", "__unresolved_3": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/MapUIProxy.js", "mTimestamp": 1754004270630}, "assets/scripts/map/ui/RightArmyItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/ArmyProxy.js", "__unresolved_2": "assets/scripts/general/GeneralCommand.js", "__unresolved_4": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/general/ArmyCommand.js", "__unresolved_5": "assets/scripts/utils/DateUtil.js", "__unresolved_6": "assets/scripts/map/ui/GeneralHeadLogic.js", "__unresolved_8": "assets/scripts/common/AudioManager.js", "__unresolved_7": "assets/scripts/utils/EventMgr.js", "__unresolved_9": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/RightArmyItemLogic.js", "mTimestamp": 1754004270267}, "assets/scripts/map/ui/Setting.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/Setting.js", "mTimestamp": 1754004270520}, "assets/scripts/map/ui/RightTagItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/common/LogicEvent.js", "__unresolved_2": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/RightTagItemLogic.js", "mTimestamp": 1754004270429}, "assets/scripts/map/ui/SkillIconLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/skill/SkillCommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/SkillIconLogic.js", "mTimestamp": 1754004270483}, "assets/scripts/map/ui/SkillItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/skill/SkillCommand.js", "__unresolved_2": "assets/scripts/map/ui/SkillIconLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/SkillItemLogic.js", "mTimestamp": 1754004270502}, "assets/scripts/map/ui/RightInfoNodeLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_3": "assets/scripts/map/ui/RightArmyItemLogic.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_1": "assets/scripts/general/ArmyCommand.js", "__unresolved_6": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/map/ui/RightCityItemLogic.js", "__unresolved_5": "assets/scripts/map/ui/RightTagItemLogic.js", "__unresolved_7": "assets/scripts/common/AudioManager.js", "__unresolved_8": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/RightInfoNodeLogic.js", "mTimestamp": 1754004270466}, "assets/scripts/map/ui/SkillInfoLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/general/GeneralCommand.js", "__unresolved_2": "assets/scripts/skill/SkillCommand.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/map/ui/SkillIconLogic.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/SkillInfoLogic.js", "mTimestamp": 1754004270671}, "assets/scripts/map/ui/TransformLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/LoginCommand.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/TransformLogic.js", "mTimestamp": 1754004270800}, "assets/scripts/map/ui/SmallMapLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_1": "assets/scripts/map/MapCommand.js", "__unresolved_4": "assets/scripts/common/AudioManager.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_2": "assets/scripts/map/MapUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/SmallMapLogic.js", "mTimestamp": 1754004270693}, "assets/scripts/map/ui/SkillLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/skill/SkillCommand.js", "__unresolved_4": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_2": "assets/scripts/skill/SkillProxy.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_5": "assets/scripts/utils/ListLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/SkillLogic.js", "mTimestamp": 1754004270759}, "assets/scripts/map/ui/WarButtonLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/common/AudioManager.js", "__unresolved_4": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/WarButtonLogic.js", "mTimestamp": 1754004270731}, "assets/scripts/map/ui/UnionButtonLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_4": "assets/scripts/common/AudioManager.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/UnionButtonLogic.js", "mTimestamp": 1754004270711}, "assets/scripts/map/ui/RightCityItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/EventMgr.js", "__unresolved_2": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/RightCityItemLogic.js", "mTimestamp": 1754004270414}, "assets/scripts/map/ui/WarReportDesLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_2": "assets/scripts/map/ui/WarReportDesItemLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/WarReportDesLogic.js", "mTimestamp": 1754004270935}, "assets/scripts/map/ui/WarReportDesItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/config/skill/Skill.js", "__unresolved_2": "assets/scripts/common/LogicEvent.js", "__unresolved_4": "assets/scripts/general/GeneralCommand.js", "__unresolved_5": "assets/scripts/skill/SkillCommand.js", "__unresolved_6": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/WarReportDesItemLogic.js", "mTimestamp": 1754004270869}, "assets/scripts/map/ui/WarReportItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/LoginCommand.js", "__unresolved_2": "assets/scripts/utils/DateUtil.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/common/AudioManager.js", "__unresolved_5": "assets/scripts/map/ui/GeneralItemLogic.js", "__unresolved_3": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_7": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/WarReportItemLogic.js", "mTimestamp": 1754004270910}, "assets/scripts/map/ui/WarReportLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_4": "assets/scripts/utils/ListLogic.js", "__unresolved_2": "assets/scripts/map/ui/WarReportDesLogic.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/map/ui/WarReportLogic.js", "mTimestamp": 1754004270961}, "assets/scripts/network/http/HttpInvoke.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_1": "assets/scripts/network/socket/NetInterface.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/network/http/HttpInvoke.js", "mTimestamp": 1754004270979}, "assets/scripts/network/http/HttpManager.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/network/http/HttpInvoke.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/network/http/HttpManager.js", "mTimestamp": 1754004271048}, "assets/scripts/network/socket/NetInterface.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/network/socket/NetInterface.js", "mTimestamp": 1754004271036}, "assets/scripts/network/socket/NetManager.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/network/socket/NetNode.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/network/socket/NetManager.js", "mTimestamp": 1754004271056}, "assets/scripts/scene/LoginScene.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_2": "assets/scripts/common/LogicEvent.js", "__unresolved_3": "assets/scripts/login/LoginCommand.js", "__unresolved_4": "assets/scripts/network/socket/NetInterface.js", "__unresolved_5": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/scene/LoginScene.js", "mTimestamp": 1754004271246}, "assets/scripts/scene/MapScene.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapResBuildLogic.js", "__unresolved_2": "assets/scripts/map/MapBuildTipsLogic.js", "__unresolved_3": "assets/scripts/map/MapCityLogic.js", "__unresolved_4": "assets/scripts/map/MapCommand.js", "__unresolved_5": "assets/scripts/map/MapLogic.js", "__unresolved_6": "assets/scripts/map/MapProxy.js", "__unresolved_7": "assets/scripts/map/MapResLogic.js", "__unresolved_8": "assets/scripts/map/MapUtil.js", "__unresolved_9": "assets/scripts/map/MapFacilityBuildLogic.js", "__unresolved_12": "assets/scripts/utils/EventMgr.js", "__unresolved_13": "assets/scripts/common/LogicEvent.js", "__unresolved_10": "assets/scripts/map/MapBuildTagLogic.js", "__unresolved_11": "assets/scripts/map/MapSysCityLogic.js", "__unresolved_14": "assets/scripts/core/coreEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/scene/MapScene.js", "mTimestamp": 1754004271110}, "assets/scripts/network/socket/NetNode.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/network/socket/NetInterface.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/network/socket/WebSock.js", "__unresolved_2": "assets/scripts/network/socket/NetTimer.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/network/socket/NetNode.js", "mTimestamp": 1754004271026}, "assets/scripts/network/socket/NetTimer.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/network/socket/NetInterface.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/network/socket/NetTimer.js", "mTimestamp": 1754004271141}, "assets/scripts/network/socket/WebSock.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/libs/crypto/crypto.js", "__unresolved_3": "assets/scripts/libs/convert.js", "__unresolved_2": "assets/scripts/libs/gzip/gzip.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/network/socket/WebSock.js", "mTimestamp": 1754004271209}, "assets/scripts/skill/SkillCommand.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/config/ServerConfig.js", "__unresolved_2": "assets/scripts/network/socket/NetManager.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_3": "assets/scripts/skill/SkillProxy.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/skill/SkillCommand.js", "mTimestamp": 1754004271128}, "assets/scripts/skill/SkillProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/skill/SkillProxy.js", "mTimestamp": 1754004271218}, "assets/scripts/test/SimpleLoginTest.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/SimpleLoginUI.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/test/SimpleLoginTest.js", "mTimestamp": 1754004271179}, "assets/scripts/tools/GeneralTool.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/core/LoaderManager.js", "__unresolved_2": "assets/scripts/general/GeneralCommand.js", "__unresolved_3": "assets/scripts/map/ui/GeneralRosterLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/tools/GeneralTool.js", "mTimestamp": 1754004271369}, "assets/scripts/tools/MapTool.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/map/MapProxy.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/tools/MapTool.js", "mTimestamp": 1754004271273}, "assets/scripts/union/UnionApplyItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_2": "assets/scripts/union/UnionCommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionApplyItemLogic.js", "mTimestamp": 1754004271314}, "assets/scripts/union/UnionCommand.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/network/socket/NetManager.js", "__unresolved_2": "assets/scripts/union/UnionProxy.js", "__unresolved_3": "assets/scripts/config/ServerConfig.js", "__unresolved_4": "assets/scripts/map/MapCommand.js", "__unresolved_5": "assets/scripts/utils/EventMgr.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionCommand.js", "mTimestamp": 1754004271428}, "assets/scripts/union/UnionItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/common/AudioManager.js", "__unresolved_4": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionItemLogic.js", "mTimestamp": 1754004271488}, "assets/scripts/union/UnionCreateLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/common/AudioManager.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_4": "assets/scripts/libs/NameDict.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionCreateLogic.js", "mTimestamp": 1754004271468}, "assets/scripts/union/UnionLogItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/DateUtil.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionLogItemLogic.js", "mTimestamp": 1754004271441}, "assets/scripts/union/UnionLobbyLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/common/LogicEvent.js", "__unresolved_3": "assets/scripts/utils/ListLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionLobbyLogic.js", "mTimestamp": 1754004271510}, "assets/scripts/union/UnionLogLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_4": "assets/scripts/common/LogicEvent.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_5": "assets/scripts/utils/ListLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionLogLogic.js", "mTimestamp": 1754004271528}, "assets/scripts/union/UnionMainLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_4": "assets/scripts/common/AudioManager.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionMainLogic.js", "mTimestamp": 1754004271600}, "assets/scripts/union/UnionLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_3": "assets/scripts/map/MapCommand.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_2": "assets/scripts/common/LogicEvent.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionLogic.js", "mTimestamp": 1754004271565}, "assets/scripts/union/UnionMemItemLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_2": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/common/AudioManager.js", "__unresolved_4": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionMemItemLogic.js", "mTimestamp": 1754004271657}, "assets/scripts/union/UnionMemberItemOpLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_1": "assets/scripts/common/AudioManager.js", "__unresolved_3": "assets/scripts/union/UnionCommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionMemberItemOpLogic.js", "mTimestamp": 1754004271627}, "assets/scripts/union/UnionProxy.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/login/LoginCommand.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionProxy.js", "mTimestamp": 1754004271792}, "assets/scripts/union/UnionMemberLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_4": "assets/scripts/utils/EventMgr.js", "__unresolved_3": "assets/scripts/union/UnionMemberItemOpLogic.js", "__unresolved_5": "assets/scripts/common/AudioManager.js", "__unresolved_6": "assets/scripts/common/LogicEvent.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionMemberLogic.js", "mTimestamp": 1754004271768}, "assets/scripts/utils/BgScale.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/BgScale.js", "mTimestamp": 1754004271842}, "assets/scripts/utils/CanvasAdapter.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/CanvasAdapter.js", "mTimestamp": 1754004271668}, "assets/scripts/utils/CanvasFixer.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/CanvasFixer.js", "mTimestamp": 1754004271675}, "assets/scripts/utils/DateUtil.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/DateUtil.js", "mTimestamp": 1754004271868}, "assets/scripts/utils/ClickTestHelper.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/FixedScreenAdapter.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/utils/ClickTestHelper.js", "mTimestamp": 1754004271719}, "assets/scripts/utils/EventMgr.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/EventMgr.js", "mTimestamp": 1754004271855}, "assets/scripts/utils/FixedScreenAdapter.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/config/GameConfig.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/utils/FixedScreenAdapter.js", "mTimestamp": 1754004271817}, "assets/scripts/utils/LocalCache.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/LocalCache.js", "mTimestamp": 1754004272092}, "assets/scripts/utils/ListLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/ListLogic.js", "mTimestamp": 1754004272047}, "assets/scripts/utils/Modal.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/Modal.js", "mTimestamp": 1754004272148}, "assets/scripts/utils/ResponsiveAdapter.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/config/GameConfig.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/utils/ResponsiveAdapter.js", "mTimestamp": 1754004271903}, "assets/scripts/utils/ScreenAdapter.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/config/GameConfig.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/utils/ScreenAdapter.js", "mTimestamp": 1754004271943}, "assets/scripts/utils/SimpleScreenAdapter.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/config/GameConfig.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/utils/SimpleScreenAdapter.js", "mTimestamp": 1754004272078}, "assets/scripts/utils/Toast.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js"}, "physicalLocation": "fs/0/assets/scripts/utils/Toast.js", "mTimestamp": 1754004272110}, "assets/scripts/union/UnionApplyLogic.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/union/UnionCommand.js", "__unresolved_3": "assets/scripts/utils/EventMgr.js", "__unresolved_2": "assets/scripts/map/MapCommand.js", "__unresolved_5": "assets/scripts/common/LogicEvent.js", "__unresolved_4": "assets/scripts/utils/ListLogic.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/union/UnionApplyLogic.js", "mTimestamp": 1754004271295}, "assets/scripts/utils/Tools.js": {"imports": {"cc": "quick-pack:/cce/internal/x/cc.js", "__unresolved_1": "assets/scripts/utils/LocalCache.js", "__unresolved_0": "quick-pack:/cce/internal/code-quality/cr.js"}, "physicalLocation": "fs/0/assets/scripts/utils/Tools.js", "mTimestamp": 1754004272130}, "assets/scripts/core/coreEvent.js": {"imports": {}, "physicalLocation": "fs/0/assets/scripts/core/coreEvent.js", "mTimestamp": 1753985711255}, "quick-pack:/cce/internal/x/cc.js": {"imports": {"__unresolved_0": {"external": true, "url": "cce:/internal/x/cc-fu/base"}, "__unresolved_1": {"external": true, "url": "cce:/internal/x/cc-fu/gfx-webgl"}, "__unresolved_2": {"external": true, "url": "cce:/internal/x/cc-fu/gfx-webgl2"}, "__unresolved_3": {"external": true, "url": "cce:/internal/x/cc-fu/3d"}, "__unresolved_4": {"external": true, "url": "cce:/internal/x/cc-fu/2d"}, "__unresolved_5": {"external": true, "url": "cce:/internal/x/cc-fu/ui"}, "__unresolved_6": {"external": true, "url": "cce:/internal/x/cc-fu/particle"}, "__unresolved_7": {"external": true, "url": "cce:/internal/x/cc-fu/particle-2d"}, "__unresolved_8": {"external": true, "url": "cce:/internal/x/cc-fu/physics-framework"}, "__unresolved_9": {"external": true, "url": "cce:/internal/x/cc-fu/physics-2d-framework"}, "__unresolved_10": {"external": true, "url": "cce:/internal/x/cc-fu/intersection-2d"}, "__unresolved_11": {"external": true, "url": "cce:/internal/x/cc-fu/primitive"}, "__unresolved_12": {"external": true, "url": "cce:/internal/x/cc-fu/profiler"}, "__unresolved_13": {"external": true, "url": "cce:/internal/x/cc-fu/audio"}, "__unresolved_14": {"external": true, "url": "cce:/internal/x/cc-fu/video"}, "__unresolved_15": {"external": true, "url": "cce:/internal/x/cc-fu/terrain"}, "__unresolved_16": {"external": true, "url": "cce:/internal/x/cc-fu/webview"}, "__unresolved_17": {"external": true, "url": "cce:/internal/x/cc-fu/tween"}, "__unresolved_18": {"external": true, "url": "cce:/internal/x/cc-fu/tiled-map"}, "__unresolved_19": {"external": true, "url": "cce:/internal/x/cc-fu/spine"}, "__unresolved_20": {"external": true, "url": "cce:/internal/x/cc-fu/dragon-bones"}}, "physicalLocation": "cce/internal/x/cc.js", "mTimestamp": 1754004266386, "isEntry": true, "exposeAs": "cce:/internal/x/cc"}, "quick-pack:/cce/internal/x/prerequisite-imports.js": {"imports": {"__unresolved_0": "assets/scripts/Main.js", "__unresolved_4": "assets/scripts/chat/ChatProxy.js", "__unresolved_1": "assets/scripts/chat/ChatCommand.js", "__unresolved_2": "assets/scripts/chat/ChatItemLogic.js", "__unresolved_5": "assets/scripts/cloud/CloudAni.js", "__unresolved_3": "assets/scripts/chat/ChatLogic.js", "__unresolved_10": "assets/scripts/common/LogicEvent.js", "__unresolved_11": "assets/scripts/common/PanelOut.js", "__unresolved_6": "assets/scripts/common/AudioManager.js", "__unresolved_8": "assets/scripts/common/DialogOut.js", "__unresolved_7": "assets/scripts/common/BgLogic.js", "__unresolved_9": "assets/scripts/common/LoadingLogic.js", "__unresolved_14": "assets/scripts/config/HttpConfig.js", "__unresolved_19": "assets/scripts/general/ArmyCommand.js", "__unresolved_13": "assets/scripts/config/GameConfig.js", "__unresolved_20": "assets/scripts/general/ArmyProxy.js", "__unresolved_17": "assets/scripts/core/CoreEvent.js", "__unresolved_15": "assets/scripts/config/ServerConfig.js", "__unresolved_16": "assets/scripts/config/skill/Skill.js", "__unresolved_12": "assets/scripts/config/Basci.js", "__unresolved_18": "assets/scripts/core/LoaderManager.js", "__unresolved_22": "assets/scripts/general/GeneralProxy.js", "__unresolved_24": "assets/scripts/libs/convert.js", "__unresolved_21": "assets/scripts/general/GeneralCommand.js", "__unresolved_26": "assets/scripts/libs/crypto/md5.js", "__unresolved_29": "assets/scripts/libs/gzip/gzip.js", "__unresolved_23": "assets/scripts/libs/NameDict.js", "__unresolved_27": "assets/scripts/libs/gzip/crc32.js", "__unresolved_32": "assets/scripts/login/CreateLogic.js", "__unresolved_28": "assets/scripts/libs/gzip/deflate-js.js", "__unresolved_35": "assets/scripts/login/LoginDialogController.js", "__unresolved_25": "assets/scripts/libs/crypto/crypto.js", "__unresolved_33": "assets/scripts/login/ExitDialogController.js", "__unresolved_36": "assets/scripts/login/LoginFormController.js", "__unresolved_34": "assets/scripts/login/LoginCommand.js", "__unresolved_37": "assets/scripts/login/LoginLogic.js", "__unresolved_30": "assets/scripts/libs/gzip/rawdeflate.js", "__unresolved_39": "assets/scripts/login/MainMenuController.js", "__unresolved_31": "assets/scripts/libs/gzip/rawinflate.js", "__unresolved_40": "assets/scripts/login/SimpleLoginUI.js", "__unresolved_41": "assets/scripts/map/MapArmyLogic.js", "__unresolved_42": "assets/scripts/map/MapBaseLayerLogic.js", "__unresolved_44": "assets/scripts/map/MapBuildTagLogic.js", "__unresolved_38": "assets/scripts/login/LoginProxy.js", "__unresolved_45": "assets/scripts/map/MapBuildTipsLogic.js", "__unresolved_46": "assets/scripts/map/MapCityLogic.js", "__unresolved_43": "assets/scripts/map/MapBuildProxy.js", "__unresolved_49": "assets/scripts/map/MapCommand.js", "__unresolved_47": "assets/scripts/map/MapCityProxy.js", "__unresolved_50": "assets/scripts/map/MapFacilityBuildLogic.js", "__unresolved_51": "assets/scripts/map/MapLogic.js", "__unresolved_48": "assets/scripts/map/MapClickUILogic.js", "__unresolved_53": "assets/scripts/map/MapResBuildLogic.js", "__unresolved_52": "assets/scripts/map/MapProxy.js", "__unresolved_54": "assets/scripts/map/MapResLogic.js", "__unresolved_56": "assets/scripts/map/MapTouchLogic.js", "__unresolved_55": "assets/scripts/map/MapSysCityLogic.js", "__unresolved_60": "assets/scripts/map/entries/BuildTipsLogic.js", "__unresolved_57": "assets/scripts/map/MapUtil.js", "__unresolved_58": "assets/scripts/map/entries/ArmyLogic.js", "__unresolved_59": "assets/scripts/map/entries/BuildTagLogic.js", "__unresolved_61": "assets/scripts/map/entries/CityLogic.js", "__unresolved_65": "assets/scripts/map/entries/SysCityLogic.js", "__unresolved_62": "assets/scripts/map/entries/FacilityBuildLogic.js", "__unresolved_67": "assets/scripts/map/ui/ArmySelectNodeLogic.js", "__unresolved_68": "assets/scripts/map/ui/CityAboutLogic.js", "__unresolved_64": "assets/scripts/map/entries/ResLogic.js", "__unresolved_69": "assets/scripts/map/ui/CityArmyItemLogic.js", "__unresolved_63": "assets/scripts/map/entries/ResBuildLogic.js", "__unresolved_66": "assets/scripts/map/ui/ArmySelectItemLogic.js", "__unresolved_72": "assets/scripts/map/ui/CollectLogic.js", "__unresolved_70": "assets/scripts/map/ui/CityArmySettingLogic.js", "__unresolved_71": "assets/scripts/map/ui/CityGeneralItemLogic.js", "__unresolved_74": "assets/scripts/map/ui/DrawLogic.js", "__unresolved_73": "assets/scripts/map/ui/Dialog.js", "__unresolved_75": "assets/scripts/map/ui/DrawRLogic.js", "__unresolved_78": "assets/scripts/map/ui/FacilityItemLogic.js", "__unresolved_79": "assets/scripts/map/ui/FacilityListLogic.js", "__unresolved_76": "assets/scripts/map/ui/FacilityAdditionItemLogic.js", "__unresolved_77": "assets/scripts/map/ui/FacilityDesLogic.js", "__unresolved_80": "assets/scripts/map/ui/FortressAbout.js", "__unresolved_81": "assets/scripts/map/ui/GeneralAddPrLogic.js", "__unresolved_83": "assets/scripts/map/ui/GeneralConvertLogic.js", "__unresolved_82": "assets/scripts/map/ui/GeneralComposeLogic.js", "__unresolved_85": "assets/scripts/map/ui/GeneralHeadLogic.js", "__unresolved_84": "assets/scripts/map/ui/GeneralDesLogic.js", "__unresolved_86": "assets/scripts/map/ui/GeneralInfoLogic.js", "__unresolved_87": "assets/scripts/map/ui/GeneralItemLogic.js", "__unresolved_88": "assets/scripts/map/ui/GeneralListLogic.js", "__unresolved_90": "assets/scripts/map/ui/GeneralRosterLogic.js", "__unresolved_89": "assets/scripts/map/ui/GeneralRosterListLogic.js", "__unresolved_91": "assets/scripts/map/ui/MapUICommand.js", "__unresolved_94": "assets/scripts/map/ui/RightArmyItemLogic.js", "__unresolved_92": "assets/scripts/map/ui/MapUILogic.js", "__unresolved_95": "assets/scripts/map/ui/RightCityItemLogic.js", "__unresolved_97": "assets/scripts/map/ui/RightTagItemLogic.js", "__unresolved_99": "assets/scripts/map/ui/SkillIconLogic.js", "__unresolved_96": "assets/scripts/map/ui/RightInfoNodeLogic.js", "__unresolved_98": "assets/scripts/map/ui/Setting.js", "__unresolved_101": "assets/scripts/map/ui/SkillItemLogic.js", "__unresolved_100": "assets/scripts/map/ui/SkillInfoLogic.js", "__unresolved_93": "assets/scripts/map/ui/MapUIProxy.js", "__unresolved_103": "assets/scripts/map/ui/SmallMapLogic.js", "__unresolved_105": "assets/scripts/map/ui/UnionButtonLogic.js", "__unresolved_106": "assets/scripts/map/ui/WarButtonLogic.js", "__unresolved_102": "assets/scripts/map/ui/SkillLogic.js", "__unresolved_104": "assets/scripts/map/ui/TransformLogic.js", "__unresolved_107": "assets/scripts/map/ui/WarReportDesItemLogic.js", "__unresolved_109": "assets/scripts/map/ui/WarReportItemLogic.js", "__unresolved_108": "assets/scripts/map/ui/WarReportDesLogic.js", "__unresolved_111": "assets/scripts/network/http/HttpInvoke.js", "__unresolved_110": "assets/scripts/map/ui/WarReportLogic.js", "__unresolved_115": "assets/scripts/network/socket/NetNode.js", "__unresolved_113": "assets/scripts/network/socket/NetInterface.js", "__unresolved_112": "assets/scripts/network/http/HttpManager.js", "__unresolved_114": "assets/scripts/network/socket/NetManager.js", "__unresolved_119": "assets/scripts/scene/MapScene.js", "__unresolved_120": "assets/scripts/skill/SkillCommand.js", "__unresolved_116": "assets/scripts/network/socket/NetTimer.js", "__unresolved_122": "assets/scripts/test/SimpleLoginTest.js", "__unresolved_117": "assets/scripts/network/socket/WebSock.js", "__unresolved_121": "assets/scripts/skill/SkillProxy.js", "__unresolved_118": "assets/scripts/scene/LoginScene.js", "__unresolved_124": "assets/scripts/tools/MapTool.js", "__unresolved_126": "assets/scripts/union/UnionApplyLogic.js", "__unresolved_123": "assets/scripts/tools/GeneralTool.js", "__unresolved_125": "assets/scripts/union/UnionApplyItemLogic.js", "__unresolved_127": "assets/scripts/union/UnionCommand.js", "__unresolved_131": "assets/scripts/union/UnionLogItemLogic.js", "__unresolved_128": "assets/scripts/union/UnionCreateLogic.js", "__unresolved_129": "assets/scripts/union/UnionItemLogic.js", "__unresolved_130": "assets/scripts/union/UnionLobbyLogic.js", "__unresolved_132": "assets/scripts/union/UnionLogLogic.js", "__unresolved_133": "assets/scripts/union/UnionLogic.js", "__unresolved_134": "assets/scripts/union/UnionMainLogic.js", "__unresolved_136": "assets/scripts/union/UnionMemberItemOpLogic.js", "__unresolved_135": "assets/scripts/union/UnionMemItemLogic.js", "__unresolved_140": "assets/scripts/utils/CanvasAdapter.js", "__unresolved_141": "assets/scripts/utils/CanvasFixer.js", "__unresolved_142": "assets/scripts/utils/ClickTestHelper.js", "__unresolved_137": "assets/scripts/union/UnionMemberLogic.js", "__unresolved_138": "assets/scripts/union/UnionProxy.js", "__unresolved_145": "assets/scripts/utils/FixedScreenAdapter.js", "__unresolved_139": "assets/scripts/utils/BgScale.js", "__unresolved_144": "assets/scripts/utils/EventMgr.js", "__unresolved_143": "assets/scripts/utils/DateUtil.js", "__unresolved_149": "assets/scripts/utils/ResponsiveAdapter.js", "__unresolved_150": "assets/scripts/utils/ScreenAdapter.js", "__unresolved_146": "assets/scripts/utils/ListLogic.js", "__unresolved_151": "assets/scripts/utils/SimpleScreenAdapter.js", "__unresolved_147": "assets/scripts/utils/LocalCache.js", "__unresolved_152": "assets/scripts/utils/Toast.js", "__unresolved_153": "assets/scripts/utils/Tools.js", "__unresolved_148": "assets/scripts/utils/Modal.js"}, "physicalLocation": "cce/internal/x/prerequisite-imports.js", "mTimestamp": 1754004266463, "isEntry": true, "exposeAs": "cce:/internal/x/prerequisite-imports"}, "quick-pack:/cce/internal/code-quality/cr.js": {"imports": {}, "physicalLocation": "cce/internal/code-quality/cr.js", "mTimestamp": 1754004272225}}