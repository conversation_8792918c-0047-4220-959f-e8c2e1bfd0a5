{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/config/GameConfig.ts"], "names": ["GameConfig", "serverUrl", "webUrl", "screen", "baseWidth", "baseHeight", "enableResponsiveDesign", "minScale", "maxScale", "enableSafeArea", "enableDebugInfo", "enableClickTest"], "mappings": ";;;;;;;;;;;;;;4BAGMA,U,GAAa;AACfC,QAAAA,SAAS,EAAE,qBADI;AAEfC,QAAAA,MAAM,EAAE,uBAFO;AAIf;AACAC,QAAAA,MAAM,EAAE;AACJ;AACAC,UAAAA,SAAS,EAAE,IAFP;AAEoB;AACxBC,UAAAA,UAAU,EAAE,IAHR;AAGoB;AAExB;AACAC,UAAAA,sBAAsB,EAAE,IANpB;AAM2B;AAE/B;AACAC,UAAAA,QAAQ,EAAE,GATN;AASoB;AACxBC,UAAAA,QAAQ,EAAE,GAVN;AAUoB;AAExB;AACAC,UAAAA,cAAc,EAAE,IAbZ;AAaoB;AACxBC,UAAAA,eAAe,EAAE,IAdb;AAcoB;AACxBC,UAAAA,eAAe,EAAE,IAfb,CAeoB;;AAfpB;AALO,O", "sourcesContent": ["// /**连接配置*/\n\nimport { _decorator } from 'cc';\nconst GameConfig = {\n    serverUrl: \"ws://127.0.0.1:8004\",\n    webUrl: \"http://127.0.0.1:8088\",\n\n    // 屏幕适配配置\n    screen: {\n        // 响应式设计基准尺寸\n        baseWidth: 1080,        // 基准宽度（用于计算缩放比例）\n        baseHeight: 1920,       // 基准高度（用于计算缩放比例）\n\n        // 响应式设计开关\n        enableResponsiveDesign: true,  // 启用响应式设计模式\n\n        // 缩放限制\n        minScale: 0.5,          // 最小缩放比例\n        maxScale: 2.0,          // 最大缩放比例\n\n        // 其他配置\n        enableSafeArea: true,   // 是否启用安全区域适配\n        enableDebugInfo: true,  // 是否显示适配调试信息（开发阶段开启，发布时关闭）\n        enableClickTest: true,  // 是否启用点击测试助手\n    }\n}\nexport { GameConfig };\n"]}