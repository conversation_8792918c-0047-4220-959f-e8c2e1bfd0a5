{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/GeneralHeadLogic.ts"], "names": ["_decorator", "Component", "Sprite", "SpriteFrame", "resources", "GeneralCommand", "ccclass", "GeneralHeadLogic", "setHeadId", "id", "sp", "node", "getComponent", "frame", "getInstance", "proxy", "getGeneralTex", "spriteFrame", "console", "log", "load", "finish", "total", "error", "asset", "message", "setGeneralTex"], "mappings": ";;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;;AAE9CC,MAAAA,c;;;;;;;OADD;AAACC,QAAAA;AAAD,O,GAAYN,U;;yBAIGO,gB,WADpBD,OAAO,CAAC,kBAAD,C,gBAAR,MACqBC,gBADrB,SAC8CN,SAD9C,CACwD;AAE7CO,QAAAA,SAAS,CAACC,EAAD,EAAY;AAExB,cAAIC,EAAE,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBV,MAAvB,CAAT;;AACA,cAAIQ,EAAJ,EAAO,CACH;AACH;;AAED,cAAIG,KAAK,GAAG;AAAA;AAAA,gDAAeC,WAAf,GAA6BC,KAA7B,CAAmCC,aAAnC,CAAiDP,EAAjD,CAAZ;;AACA,cAAGI,KAAH,EAAS;AACL,gBAAGH,EAAH,EAAM;AACFA,cAAAA,EAAE,CAACO,WAAH,GAAiBJ,KAAjB;AACH;AACJ,WAJD,MAIK;AAEDK,YAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BV,EAA/B;AACAL,YAAAA,SAAS,CAACgB,IAAV,CAAe,uBAAuBX,EAAvB,GAA4B,cAA3C,EAA2DN,WAA3D,EACI,CAACkB,MAAD,EAAiBC,KAAjB,KAAmC,CAClC,CAFL,EAGI,CAACC,KAAD,EAAeC,KAAf,KAA8B;AAC1B,kBAAID,KAAK,IAAI,IAAb,EAAmB;AACfL,gBAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCI,KAAK,CAACE,OAAtC;AACH,eAFD,MAEK;AACD,oBAAIZ,KAAK,GAAGW,KAAZ;;AACA,oBAAGd,EAAH,EAAM;AACFA,kBAAAA,EAAE,CAACO,WAAH,GAAiBJ,KAAjB;AACH;;AAED;AAAA;AAAA,sDAAeC,WAAf,GAA6BC,KAA7B,CAAmCW,aAAnC,CAAiDjB,EAAjD,EAAqDI,KAArD;AACH;AACJ,aAdL;AAgBH;AACJ;;AAlCmD,O", "sourcesContent": ["import { _decorator, Component, Sprite, SpriteFrame, resources } from 'cc';\nconst {ccclass} = _decorator;\nimport GeneralCommand from \"../../general/GeneralCommand\";\n\n@ccclass('GeneralHeadLogic')\nexport default class GeneralHeadLogic extends Component {\n    \n    public setHeadId(id:number) {\n\n        var sp = this.node.getComponent(Sprite);\n        if (sp){\n            //sp.spriteFrame = null;\n        }\n        \n        var frame = GeneralCommand.getInstance().proxy.getGeneralTex(id);\n        if(frame){\n            if(sp){\n                sp.spriteFrame = frame;\n            }\n        }else{\n\n            console.log(\"load setHeadId:\", id);\n            resources.load(\"./generalpic/card_\" + id + \"/spriteFrame\", SpriteFrame, \n                (finish: number, total: number) => {\n                },\n                (error: Error, asset: any) => {\n                    if (error != null) {\n                        console.log(\"setHeadId error:\", error.message);\n                    }else{\n                        var frame = asset as SpriteFrame;\n                        if(sp){\n                            sp.spriteFrame = frame;\n                        }\n\n                        GeneralCommand.getInstance().proxy.setGeneralTex(id, frame);\n                    }\n                });\n\n        }\n    }\n}\n"]}