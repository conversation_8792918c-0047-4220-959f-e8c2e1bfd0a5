[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "GeneralCompose", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 8}, {"__id__": 16}, {"__id__": 41}], "_active": true, "_components": [{"__id__": 87}, {"__id__": 89}, {"__id__": 91}], "_prefab": {"__id__": 93}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}], "_prefab": {"__id__": 7}, "_lpos": {"__type__": "cc.Vec3", "x": -312.27, "y": -44.408, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4zINrCI5HHbZ6bLCIHr0a"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 315, "height": 432}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bdFdW/CZAxoQSeuuVmYei"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dWJIdnrlHS6buK/i5627W"}, {"__type__": "cc.Node", "_name": "nameLab", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}, {"__id__": 11}, {"__id__": 13}], "_prefab": {"__id__": 15}, "_lpos": {"__type__": "cc.Vec3", "x": -305.799, "y": 220, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 10}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bO+shKWxC65QOhpFqNEbt"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 12}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74AeDgHNNKKLVKD1Ozeydw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 14}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21FNqBT2tB4o4jeY8bjsfA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "87Br5XtFNACZG9BtfM2V6J"}, {"__type__": "cc.Node", "_name": "New Button", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 17}], "_active": true, "_components": [{"__id__": 33}, {"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -289.778, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 16}, "_children": [{"__id__": 18}], "_active": true, "_components": [{"__id__": 26}, {"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}, {"__id__": 23}], "_prefab": {"__id__": 25}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "合成", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7MonjL5ZDNJA8frcIb2O2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cC7arw05EUIiTKWT8MiKK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 24}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75FMc5DZNNlrNzNS8uripZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8afA+GxZVE0r0L3Kp8Er0O"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 27}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a68bd8c2-5846-4bbe-b56a-eef66911be32@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79BlDZfrZJPLFsT497Hyjs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 29}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3AEYoI+dExpNoviJl6VG5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30wSdD7pZI872ab2YRQkIB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8f1wOrCCVB+IpXcoAUbxZU"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 34}, "clickEvents": [{"__id__": 35}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 17}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "257R77PFxKKaBhkbbWAHwg"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "146edJ13EBM/Zet/NdH3Y5F", "handler": "onCompose", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 37}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4yVNCqVZHkr6ds0G79Bo6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 39}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "290gjz1bVAMI9HgFKq/Gln"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79uMoAS8BOIrp7TpZsmCKU"}, {"__type__": "cc.Node", "_name": "New ScrollView", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 42}, {"__id__": 56}], "_active": true, "_components": [{"__id__": 53}, {"__id__": 78}, {"__id__": 80}, {"__id__": 82}, {"__id__": 84}], "_prefab": {"__id__": 86}, "_lpos": {"__type__": "cc.Vec3", "x": 245.683, "y": -5.853999999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 512, "_parent": {"__id__": 41}, "_children": [{"__id__": 43}], "_active": true, "_components": [{"__id__": 51}, {"__id__": 69}, {"__id__": 71}, {"__id__": 73}, {"__id__": 75}], "_prefab": {"__id__": 77}, "_lpos": {"__type__": "cc.Vec3", "x": 275, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 42}, "_children": [], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 50}, "_lpos": {"__type__": "cc.Vec3", "x": -1, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 45}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "afc47931-f066-46b0-90be-9fe61f213428@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03hQMsmEZEl4tqxnXdXPwU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 47}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5foMvK69ZBXY5mVYqc7NKo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 49}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dCu48rppAvYSPR6Cc1HUE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5ftukn6C5N5KWAsbx2RIZu"}, {"__type__": "<PERSON>.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 52}, "_scrollView": {"__id__": 53}, "_handle": {"__id__": 44}, "_direction": 1, "_enableAutoHide": true, "_autoHideTime": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7293uU3GRIY6OcWYkJN9Kg"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 54}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": false, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 55}, "_horizontalScrollBar": null, "_verticalScrollBar": {"__id__": 51}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29zYAQ5+5HZIXKJqGo2uAs"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 56}, "_children": [], "_active": true, "_components": [{"__id__": 64}, {"__id__": 66}], "_prefab": {"__id__": 68}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 41}, "_children": [{"__id__": 55}], "_active": true, "_components": [{"__id__": 57}, {"__id__": 59}, {"__id__": 61}], "_prefab": {"__id__": 63}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": {"__id__": 58}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_type": 0, "_inverted": false, "_segments": 64, "_spriteFrame": null, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03T4foTJ5IqY+mkepOcBms"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": {"__id__": 60}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61PfnN30NPN6y3cr8sPjyY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": {"__id__": 62}, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dsxcagZFFCrHhNh40hGBk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dqU+WbUhBX7QEoPXbFiep"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "__prefab": {"__id__": 65}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1AlRva3tEHrgRKCf9c41K"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "__prefab": {"__id__": 67}, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dxPbCTMdIZ7/3JtOeHQIv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d4hnO59bBAqIIjFnAo09jx"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 70}, "_alignFlags": 37, "_target": null, "_left": 350.07654921020657, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 237, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4djQg9RglJ2KA1rEVxCb/k"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 72}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ffb88a8f-af62-48f4-8f1d-3cb606443a43@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff8dpPfe5DUaDsZVNchM5M"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 74}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71ccZyacFF8brhRvE9Rd+n"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 76}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28BDy4mbVDwKEmwwhfpa81"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "761GrUP6hChoLEk8n4OD3J"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 79}, "_visFlags": 0, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d0741e0a-e2b2-43ee-852c-7c86613877f5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbqtW4xwxBK6BvwkoiO8JX"}, {"__type__": "551e9No1LFKz7GzS5YFonQR", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 81}, "scrollView": {"__id__": 53}, "itemPrefab": {"__uuid__": "474e595e-61cf-48e7-ba58-8e0aaaa96134", "__expectedType__": "cc.Prefab"}, "itemNode": null, "itemLogicScriptName": "GeneralItemLogic", "isHorizontal": false, "columnCount": 4, "autoColumnCount": false, "spaceColumn": 20, "spaceRow": 15, "updateInterval": 0.1, "scale": 0.3, "itemClickEvents": [], "isVirtual": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4TyI5DuVNeJ70SFN/4LWV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 83}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56OLoU6bBJzYFHBGhwzmXC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 85}, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8avpH5olE3aQsxAkAoORw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9fDWDV9pJh4WSJ+GO7OVP"}, {"__type__": "146edJ13EBM/Zet/NdH3Y5F", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 88}, "nameLab": {"__id__": 9}, "generalItemPrefab": {"__uuid__": "474e595e-61cf-48e7-ba58-8e0aaaa96134", "__expectedType__": "cc.Prefab"}, "generalItemParent": {"__id__": 2}, "scrollView": {"__id__": 53}, "composeNode": {"__id__": 16}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85IesKGhVA9rBXS7jdIYZq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 90}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dftG6SWN5KfLcyvH1yjLz7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 92}, "_contentSize": {"__type__": "cc.Size", "width": 1200, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56pVITeUFBPo1UKp3eq6Km"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28/gogGtpDD6pMcdR8bPim"}]