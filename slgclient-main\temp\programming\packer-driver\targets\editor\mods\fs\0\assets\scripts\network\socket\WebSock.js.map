{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/network/socket/WebSock.ts"], "names": ["WebSock", "crypto", "gzip", "convert", "onConnected", "event", "console", "log", "onJsonMessage", "data", "onGetKey", "onMessage", "msg", "ab", "view", "Uint8Array", "undata", "unzip", "c", "byteToString", "_key", "hand_data", "JSON", "parse", "name", "key", "error", "decrypted", "getAnddecrypt", "json", "onError", "onClosed", "connect", "options", "_ws", "readyState", "WebSocket", "CONNECTING", "url", "ip", "port", "protocol", "binaryType", "onmessage", "onopen", "onerror", "onclose", "send", "buffer", "OPEN", "close", "code", "reason", "packAndSend", "send_data", "encrypt", "zip", "level", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "i8arr", "Int8Array", "i", "get_msg", "decrypt", "enc", "Utf8", "iv", "stringify", "srcs", "encrypted", "AES", "mode", "CBC", "padding", "pad", "ZeroPadding", "ciphertext", "toString", "message", "encryptedHexStr", "Hex", "Base64", "decryptedStr", "str", "replaceAll"], "mappings": ";;;qDAOaA,O;;;;;;;;;;;;;;;;;;;;AANDC,MAAAA,M;;AACAC,MAAAA,I;;AACHC,MAAAA,O,iBAAAA,O;;;;;;;yBAIIH,O,GAAN,MAAMA,OAAN,CAAiC;AAAA;AAAA,uCACX,IADW;;AAAA,wCAEd,EAFc;AAAA;;AAKpCI,QAAAA,WAAW,CAACC,KAAD,EAAY;AACnBC,UAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAqCF,KAArC;AACH;;AAGDG,QAAAA,aAAa,CAACC,IAAD,EAAU,CAEtB;;AAGDC,QAAAA,QAAQ,GAAE,CAET;;AAGDC,QAAAA,SAAS,CAACC,GAAD,EAAU;AAEf;AACA,cAAIC,EAAE,GAAGD,GAAT;AACA,cAAIE,IAAI,GAAG,IAAIC,UAAJ,CAAeF,EAAf,CAAX;AACA,cAAIG,MAAM,GAAGd,IAAI,CAACe,KAAL,CAAWH,IAAX,CAAb;AACA,cAAII,CAAC,GAAG;AAAA;AAAA,mCAAR;AACAN,UAAAA,GAAG,GAAGM,CAAC,CAACC,YAAF,CAAeH,MAAf,CAAN,CAPe,CAQf;AAEA;;AACA,cAAG,KAAKI,IAAL,IAAa,EAAhB,EAAmB;AACf,gBAAI;AACA,kBAAIC,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWX,GAAX,CAAhB;AACAN,cAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAAyBc,SAAzB;;AACA,kBAAGA,SAAS,CAACG,IAAV,IAAkB,WAArB,EAAiC;AAC7B,qBAAKJ,IAAL,GAAYC,SAAS,CAACT,GAAV,CAAca,GAA1B;AACA,qBAAKf,QAAL;AACA;AACH;AACJ,aARD,CAQE,OAAOgB,KAAP,EAAc;AACZpB,cAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAqCmB,KAArC;AACH;AACJ,WAvBc,CAyBf;;;AAEA,cAAIC,SAAS,GAAG,IAAhB;;AACA,cAAI;AACAA,YAAAA,SAAS,GAAG,KAAKC,aAAL,CAAmBhB,GAAnB,CAAZ;AACH,WAFD,CAEE,OAAOc,KAAP,EAAc;AACZpB,YAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAoCmB,KAApC;AACH;;AAED,cAAGC,SAAS,IAAI,EAAhB,EAAmB;AACf,iBAAKP,IAAL,GAAY,EAAZ;AACH,WApCc,CAqCf;;;AACA,cAAGO,SAAH,EAAa;AACT,gBAAIE,IAAI,GAAGP,IAAI,CAACC,KAAL,CAAWI,SAAX,CAAX;AACA,iBAAKnB,aAAL,CAAmBqB,IAAnB;AACH;AAGJ;;AAEDC,QAAAA,OAAO,CAACzB,KAAD,EAAY;AACfC,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAiCF,KAAjC;AACH;;AAED0B,QAAAA,QAAQ,CAAC1B,KAAD,EAAY;AAChBC,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAkCF,KAAlC;AACH;;AAED2B,QAAAA,OAAO,CAACC,OAAD,EAAe;AAClB,cAAI,KAAKC,GAAT,EAAc;AACV,gBAAI,KAAKA,GAAL,CAASC,UAAT,KAAwBC,SAAS,CAACC,UAAtC,EAAkD;AAC9C/B,cAAAA,OAAO,CAACC,GAAR,CAAY,4CAAZ;AACA,qBAAO,KAAP;AACH;AACJ;;AAED,cAAI+B,GAAG,GAAG,IAAV;;AACA,cAAGL,OAAO,CAACK,GAAX,EAAgB;AACZA,YAAAA,GAAG,GAAGL,OAAO,CAACK,GAAd;AACH,WAFD,MAEO;AACH,gBAAIC,EAAE,GAAGN,OAAO,CAACM,EAAjB;AACA,gBAAIC,IAAI,GAAGP,OAAO,CAACO,IAAnB;AACA,gBAAIC,QAAQ,GAAGR,OAAO,CAACQ,QAAvB;AACAH,YAAAA,GAAG,GAAI,GAAEG,QAAS,MAAKF,EAAG,IAAGC,IAAK,EAAlC;AACH;;AACDlC,UAAAA,OAAO,CAACC,GAAR;AACA,eAAK2B,GAAL,GAAW,IAAIE,SAAJ,CAAcE,GAAd,CAAX;AACA,eAAKJ,GAAL,CAASQ,UAAT,GAAsBT,OAAO,CAACS,UAAR,GAAqBT,OAAO,CAACS,UAA7B,GAA0C,aAAhE;;AACA,eAAKR,GAAL,CAASS,SAAT,GAAsBtC,KAAD,IAAW;AAC5B,iBAAKM,SAAL,CAAeN,KAAK,CAACI,IAArB;AACH,WAFD;;AAIA,eAAKyB,GAAL,CAASU,MAAT,GAAkB,KAAKxC,WAAvB;AACA,eAAK8B,GAAL,CAASW,OAAT,GAAmB,KAAKf,OAAxB;AACA,eAAKI,GAAL,CAASY,OAAT,GAAmB,KAAKf,QAAxB;AAEA,iBAAO,IAAP;AACH;;AAEDgB,QAAAA,IAAI,CAACC,MAAD,EAAc;AACd,cAAI,KAAKd,GAAL,CAASC,UAAT,IAAuBC,SAAS,CAACa,IAArC,EACA;AACI,iBAAKf,GAAL,CAASa,IAAT,CAAcC,MAAd;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEDE,QAAAA,KAAK,CAACC,IAAD,EAAgBC,MAAhB,EAAiC;AAClC,eAAKhC,IAAL,GAAY,EAAZ;;AACA,eAAKc,GAAL,CAASgB,KAAT,CAAeC,IAAf,EAAqBC,MAArB;AACH;AAGD;AACJ;AACA;AACA;;;AACWC,QAAAA,WAAW,CAACC,SAAD,EAAe;AAC7B;AACA,cAAIC,OAAO,GAAG,KAAKnC,IAAL,IAAa,EAAb,GAAgBkC,SAAhB,GAA0B,KAAKC,OAAL,CAAaD,SAAb,CAAxC;AAEA,cAAI7C,IAAI,GAAGP,IAAI,CAACsD,GAAL,CAASD,OAAT,EAAkB;AAACE,YAAAA,KAAK,EAAC;AAAP,WAAlB,CAAX;AACA,cAAIT,MAAM,GAAG,IAAIU,WAAJ,CAAgBjD,IAAI,CAACkD,MAArB,CAAb;AACN,cAAIC,KAAK,GAAG,IAAIC,SAAJ,CAAcb,MAAd,CAAZ;;AACA,eAAI,IAAIc,CAAC,GAAG,CAAZ,EAAeA,CAAC,GAAGF,KAAK,CAACD,MAAzB,EAAiCG,CAAC,EAAlC,EAAqC;AACpCF,YAAAA,KAAK,CAACE,CAAD,CAAL,GAAUrD,IAAI,CAACqD,CAAD,CAAd;AACM,WAT4B,CAW7B;;;AACA,eAAKf,IAAL,CAAUa,KAAV;AAGH;AAED;AACJ;AACA;AACA;;;AACWhC,QAAAA,aAAa,CAACmC,OAAD,EAAa;AAC7B,cAAIC,OAAO,GAAG,KAAK5C,IAAL,IAAa,EAAb,GAAgB2C,OAAhB,GAAwB,KAAKC,OAAL,CAAaD,OAAb,CAAtC,CAD6B,CAE7B;;AACA,iBAAOC,OAAP;AACH;;AAGAT,QAAAA,OAAO,CAAC9C,IAAD,EAAW;AACf,cAAIgB,GAAG,GAAGxB,MAAM,CAACgE,GAAP,CAAWC,IAAX,CAAgB3C,KAAhB,CAAsB,KAAKH,IAA3B,CAAV;AACA,cAAI+C,EAAE,GAAIlE,MAAM,CAACgE,GAAP,CAAWC,IAAX,CAAgB3C,KAAhB,CAAsB,KAAKH,IAA3B,CAAV;;AAEA,cAAG,OAAOX,IAAP,IAAc,QAAjB,EAA0B;AACtBA,YAAAA,IAAI,GAAGa,IAAI,CAAC8C,SAAL,CAAe3D,IAAf,CAAP;AACH;;AACD,cAAI4D,IAAI,GAAGpE,MAAM,CAACgE,GAAP,CAAWC,IAAX,CAAgB3C,KAAhB,CAAsBd,IAAtB,CAAX;AACA,cAAI6D,SAAS,GAAGrE,MAAM,CAACsE,GAAP,CAAWhB,OAAX,CAAmBc,IAAnB,EAAyB5C,GAAzB,EAA8B;AAAE0C,YAAAA,EAAE,EAAEA,EAAN;AAAUK,YAAAA,IAAI,EAAEvE,MAAM,CAACuE,IAAP,CAAYC,GAA5B;AAAiCC,YAAAA,OAAO,EAAEzE,MAAM,CAAC0E,GAAP,CAAWC;AAArD,WAA9B,CAAhB;AAGA,iBAAON,SAAS,CAACO,UAAV,CAAqBC,QAArB,EAAP;AACH;;AAGDd,QAAAA,OAAO,CAACe,OAAD,EAAiB;AACpB,cAAItD,GAAG,GAAGxB,MAAM,CAACgE,GAAP,CAAWC,IAAX,CAAgB3C,KAAhB,CAAsB,KAAKH,IAA3B,CAAV;AACA,cAAI+C,EAAE,GAAIlE,MAAM,CAACgE,GAAP,CAAWC,IAAX,CAAgB3C,KAAhB,CAAsB,KAAKH,IAA3B,CAAV;AAEA,cAAI4D,eAAe,GAAG/E,MAAM,CAACgE,GAAP,CAAWgB,GAAX,CAAe1D,KAAf,CAAqBwD,OAArB,CAAtB;AACA,cAAIV,IAAI,GAAGpE,MAAM,CAACiF,MAAP,CAAcd,SAAd,CAAwBY,eAAxB,CAAX;AACA,cAAIhB,OAAO,GAAG/D,MAAM,CAACsE,GAAP,CAAWP,OAAX,CAAmBK,IAAnB,EAAyB5C,GAAzB,EAA8B;AAAE0C,YAAAA,EAAE,EAAEA,EAAN;AAAUK,YAAAA,IAAI,EAAEvE,MAAM,CAACuE,IAAP,CAAYC,GAA5B;AAAiCC,YAAAA,OAAO,EAAEzE,MAAM,CAAC0E,GAAP,CAAWC;AAArD,WAA9B,CAAd,CANoB,CAOpB;;AAEA,cAAIO,YAAY,GAAGnB,OAAO,CAACc,QAAR,CAAiB7E,MAAM,CAACgE,GAAP,CAAWC,IAA5B,CAAnB,CAToB,CAUpB;;AACA,cAAIkB,GAAG,GAAGD,YAAY,CAACE,UAAb,CAAwB,QAAxB,EAAkC,EAAlC,CAAV,CAXoB,CAapB;;AACA,iBAAOD,GAAP;AACH;;AAtLmC,O", "sourcesContent": ["import { ISocket } from \"./NetInterface\";\nimport * as crypto  from \"../../libs/crypto/crypto\"\nimport * as gzip from \"../../libs/gzip/gzip\";\nimport { convert } from \"../../libs/convert\";\n\n\n\nexport class WebSock implements ISocket {\n    private _ws: WebSocket = null;              // websocket对象\n    private _key:String = \"\";\n\n\n    onConnected(event):void{\n        console.log(\"websocket onConnected:\",event);\n    }\n\n\n    onJsonMessage(data:any){\n\n    }\n\n\n    onGetKey(){\n\n    }\n\n\n    onMessage(msg):void{\n    \n        // console.log(\"websocket onMessage0:\",msg)\n        var ab = msg\n        var view = new Uint8Array(ab)\n        var undata = gzip.unzip(view)\n        var c = new convert()\n        msg = c.byteToString(undata)\n        // console.log(\"websocket onMessage1:\",msg)\n\n        //第一次\n        if(this._key == \"\"){\n            try {\n                var hand_data = JSON.parse(msg);\n                console.log(\"hand_data:\",hand_data)\n                if(hand_data.name == \"handshake\"){\n                    this._key = hand_data.msg.key;\n                    this.onGetKey();                    \n                    return;\n                }\n            } catch (error) {\n                console.log(\"handshake parse error:\",error)\n            }\n        }\n\n        // console.log(\"websocket onMessage2:\",msg)\n\n        var decrypted = null;\n        try {\n            decrypted = this.getAnddecrypt(msg);\n        } catch (error) {\n            console.log(\"message ecrypt error:\",error)\n        }\n\n        if(decrypted == \"\"){\n            this._key = \"\";\n        }\n        // console.log(\"onMessage decrypted :\",decrypted);\n        if(decrypted){\n            var json = JSON.parse(decrypted);\n            this.onJsonMessage(json);\n        }\n\n\n    }\n\n    onError(event):void{\n        console.log(\"websocket onError:\",event)\n    }\n\n    onClosed(event):void{\n        console.log(\"websocket onClosed:\",event)\n    }\n\n    connect(options: any) {\n        if (this._ws) {\n            if (this._ws.readyState === WebSocket.CONNECTING) {\n                console.log(\"websocket connecting, wait for a moment...\")\n                return false;\n            }\n        }\n\n        let url = null;\n        if(options.url) {\n            url = options.url;\n        } else {\n            let ip = options.ip;\n            let port = options.port;\n            let protocol = options.protocol;\n            url = `${protocol}://${ip}:${port}`;    \n        }\n        console.log()\n        this._ws = new WebSocket(url);\n        this._ws.binaryType = options.binaryType ? options.binaryType : \"arraybuffer\";\n        this._ws.onmessage = (event) => {\n            this.onMessage(event.data);\n        };\n\n        this._ws.onopen = this.onConnected;\n        this._ws.onerror = this.onError;\n        this._ws.onclose = this.onClosed;\n\n        return true;\n    }\n\n    send(buffer: any) {\n        if (this._ws.readyState == WebSocket.OPEN)\n        {\n            this._ws.send(buffer);\n            return true;\n        }\n        return false;\n    }\n\n    close(code?: number, reason?: string) {\n        this._key = \"\";\n        this._ws.close(code, reason);\n    }\n\n\n    /**\n     * json 加密打包\n     * @param send_data \n     */\n    public packAndSend(send_data:any){\n        // console.log(\"packAndSend:\", send_data);\n        var encrypt = this._key == \"\"?send_data:this.encrypt(send_data);\n\n        var data = gzip.zip(encrypt, {level:9});\n        var buffer = new ArrayBuffer(data.length);\n\t\tvar i8arr = new Int8Array(buffer);\n\t\tfor(var i = 0; i < i8arr.length; i++){\n\t\t\ti8arr[i]= data[i];\n        }\n\n        // console.log(\"i8arr:\",i8arr)\n        this.send(i8arr)\n\n\n    }\n\n    /**\n     * 解密\n     * @param msg \n     */\n    public getAnddecrypt(get_msg:any){\n        var decrypt = this._key == \"\"?get_msg:this.decrypt(get_msg);\n        // console.log(\"decrypt:\",decrypt);\n        return decrypt\n    }\n\n\n     encrypt(data:any) {\n        var key = crypto.enc.Utf8.parse(this._key);\n        var iv  = crypto.enc.Utf8.parse(this._key);\n\n        if(typeof(data)=='object'){\n            data = JSON.stringify(data);\n        }\n        let srcs = crypto.enc.Utf8.parse(data);\n        let encrypted = crypto.AES.encrypt(srcs, key, { iv: iv, mode: crypto.mode.CBC, padding: crypto.pad.ZeroPadding });\n    \n    \n        return encrypted.ciphertext.toString()\n    }\n\n\n    decrypt(message:string) {\n        var key = crypto.enc.Utf8.parse(this._key);\n        var iv  = crypto.enc.Utf8.parse(this._key);\n\n        let encryptedHexStr = crypto.enc.Hex.parse(message);\n        let srcs = crypto.Base64.stringify(encryptedHexStr);\n        let decrypt = crypto.AES.decrypt(srcs, key, { iv: iv, mode: crypto.mode.CBC, padding: crypto.pad.ZeroPadding });\n        // console.log(\"decrypt:\", decrypt);\n\n        let decryptedStr = decrypt.toString(crypto.enc.Utf8);\n        // console.log(\"decryptedStr 1111:\", typeof(decryptedStr), decryptedStr, decryptedStr.length);\n        var str = decryptedStr.replaceAll(\"\\u0000\", \"\")\n\n        // console.log(\"decryptedStr 2222:\", typeof(str), str, str.length);\n        return str;\n    }\n\n}\n"]}