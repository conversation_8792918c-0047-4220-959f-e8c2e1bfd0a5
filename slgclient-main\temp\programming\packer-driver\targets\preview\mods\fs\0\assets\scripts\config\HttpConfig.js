System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, HttpConfig;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5c375uHRpBIkqgPh1TxDSdK", "HttpConfig", undefined);

      _export("HttpConfig", HttpConfig = {
        register: {
          name: "register",
          url: "/account/register"
        }
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=HttpConfig.js.map