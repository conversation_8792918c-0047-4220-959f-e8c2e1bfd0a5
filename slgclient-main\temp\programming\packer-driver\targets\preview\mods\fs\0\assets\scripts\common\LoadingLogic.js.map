{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LoadingLogic.ts"], "names": ["_decorator", "Component", "ProgressBar", "CoreEvent", "EventMgr", "ccclass", "property", "LoadingLogic", "onLoad", "bar", "progress", "on", "loadProgress", "onProgress", "loadComplete", "onComplete", "onDestroy", "targetOff", "precent", "node", "parent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;;AACvBC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;yBAGTO,Y,WADpBF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAACJ,WAAD,C,oCAFb,MACqBK,YADrB,SAC0CN,SAD1C,CACoD;AAAA;AAAA;;AAAA;AAAA;;AAGtCO,QAAAA,MAAM,GAAS;AACrB,eAAKC,GAAL,CAASC,QAAT,GAAoB,CAApB;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,YAAtB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,sCAAUG,YAAtB,EAAoC,KAAKC,UAAzC,EAAqD,IAArD;AACH;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AACSJ,QAAAA,UAAU,CAACK,OAAD,EAAwB;AACxC,eAAKT,GAAL,CAASC,QAAT,GAAoBQ,OAApB;AACH;;AACSH,QAAAA,UAAU,GAAS;AACzB,eAAKI,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH;;AAhB+C,O;;;;;iBAEtB,I", "sourcesContent": ["import { _decorator, Component, ProgressBar } from 'cc';\nimport { CoreEvent } from '../core/coreEvent';\nimport { EventMgr } from '../utils/EventMgr';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('LoadingLogic')\nexport default class LoadingLogic extends Component {\n    @property(ProgressBar)\n    bar: ProgressBar | null = null;\n    protected onLoad(): void {\n        this.bar.progress = 0;\n        EventMgr.on(CoreEvent.loadProgress, this.onProgress, this);\n        EventMgr.on(CoreEvent.loadComplete, this.onComplete, this);\n    }\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n    }\n    protected onProgress(precent: number): void {\n        this.bar.progress = precent;\n    }\n    protected onComplete(): void {\n        this.node.parent = null;\n    }\n}\n\n\n"]}