System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _decorator, Component, Node, Button, Label, game, AudioManager, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _temp, _crd, ccclass, property, ExitDialogController;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'proposal-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfAudioManager(extras) {
    _reporterNs.report("AudioManager", "../common/AudioManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Button = _cc.Button;
      Label = _cc.Label;
      game = _cc.game;
    }, function (_unresolved_2) {
      AudioManager = _unresolved_2.AudioManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "32d9fxiIb1C9LaYTIPYGUgX", "ExitDialogController", undefined);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 离开游戏对话框控制器
       * 处理离开游戏确认功能
       */

      _export("ExitDialogController", ExitDialogController = (_dec = ccclass('ExitDialogController'), _dec2 = property(Label), _dec3 = property(Button), _dec4 = property(Button), _dec5 = property(Node), _dec(_class = (_class2 = (_temp = class ExitDialogController extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "messageLabel", _descriptor, this);

          _initializerDefineProperty(this, "confirmButton", _descriptor2, this);

          _initializerDefineProperty(this, "cancelButton", _descriptor3, this);

          _initializerDefineProperty(this, "maskNode", _descriptor4, this);
        }

        onLoad() {
          console.log('[ExitDialogController] 离开游戏对话框控制器加载');
          this.initializeDialog();
          this.setupEventListeners();
        }

        start() {
          console.log('[ExitDialogController] 离开游戏对话框控制器启动');
        }
        /**
         * 初始化对话框
         */


        initializeDialog() {
          // 设置询问文本
          if (this.messageLabel) {
            this.messageLabel.string = '确定要离开游戏吗？\n离开后当前进度可能会丢失';
          }
        }
        /**
         * 设置事件监听器
         */


        setupEventListeners() {
          if (this.confirmButton) {
            this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmButtonClick, this);
            console.log('[ExitDialogController] 确认按钮事件监听器已设置');
          }

          if (this.cancelButton) {
            this.cancelButton.node.on(Button.EventType.CLICK, this.onCancelButtonClick, this);
            console.log('[ExitDialogController] 取消按钮事件监听器已设置');
          } // 设置遮罩点击事件（点击对话框外区域关闭）


          if (this.maskNode) {
            this.maskNode.on(Node.EventType.TOUCH_END, this.onMaskClick, this);
            console.log('[ExitDialogController] 遮罩点击事件监听器已设置');
          }
        }
        /**
         * 确认离开按钮点击事件
         */


        onConfirmButtonClick() {
          console.log('[ExitDialogController] 确认离开按钮被点击'); // 播放点击音效

          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick(); // 显示离开提示

          console.log('[ExitDialogController] 玩家确认离开游戏'); // 延迟一下再退出，让音效播放完

          this.scheduleOnce(() => {
            this.exitGame();
          }, 0.2);
        }
        /**
         * 再玩一会按钮点击事件
         */


        onCancelButtonClick() {
          console.log('[ExitDialogController] 再玩一会按钮被点击'); // 播放点击音效

          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick(); // 关闭对话框

          this.node.destroy();
        }
        /**
         * 遮罩点击事件（点击对话框外区域关闭）
         */


        onMaskClick() {
          console.log('[ExitDialogController] 点击对话框外区域，关闭对话框'); // 播放点击音效

          (_crd && AudioManager === void 0 ? (_reportPossibleCrUseOfAudioManager({
            error: Error()
          }), AudioManager) : AudioManager).instance.playClick(); // 关闭对话框

          this.node.destroy();
        }
        /**
         * 退出游戏
         */


        exitGame() {
          console.log('[ExitDialogController] 正在退出游戏...'); // 在不同平台上退出游戏

          if (typeof window !== 'undefined') {
            // Web平台
            if (window.close) {
              window.close();
            } else {
              // 如果无法关闭窗口，显示提示
              alert('请手动关闭浏览器标签页');
            }
          } else {
            // 原生平台
            game.end();
          }
        }

        onDestroy() {
          // 清理事件监听器
          if (this.confirmButton) {
            this.confirmButton.node.off(Button.EventType.CLICK, this.onConfirmButtonClick, this);
          }

          if (this.cancelButton) {
            this.cancelButton.node.off(Button.EventType.CLICK, this.onCancelButtonClick, this);
          }

          if (this.maskNode) {
            this.maskNode.off(Node.EventType.TOUCH_END, this.onMaskClick, this);
          }

          console.log('[ExitDialogController] 离开游戏对话框控制器销毁');
        }

      }, _temp), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "messageLabel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "confirmButton", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "cancelButton", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "maskNode", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ExitDialogController.js.map