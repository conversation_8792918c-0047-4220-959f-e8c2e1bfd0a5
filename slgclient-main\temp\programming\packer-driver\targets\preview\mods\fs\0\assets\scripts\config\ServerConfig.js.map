{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/config/ServerConfig.ts"], "names": ["ServerConfig", "account_login", "account_logout", "account_reLogin", "account_robLogin", "role_create", "role_roleList", "role_enterServer", "role_myCity", "role_myRoleRes", "role_myProperty", "role_upPosition", "role_posTagList", "role_opPosTag", "nationMap_config", "nationMap_scanBlock", "nationMap_giveUp", "nationMap_build", "nationMap_upBuild", "nationMap_delBuild", "city_facilities", "city_upFacility", "general_myGenerals", "general_drawGeneral", "general_composeGeneral", "general_addPrGeneral", "general_convert", "general_upSkill", "general_downSkill", "general_lvSkill", "army_myList", "army_myOne", "army_dispose", "army_conscript", "army_assign", "war_report", "war_read", "union_create", "union_join", "union_list", "union_member", "union_applyList", "union_dismiss", "union_verify", "union_exit", "union_kick", "union_appoint", "union_abdicate", "union_modNotice", "union_info", "union_log", "union_apply_push", "interior_collect", "interior_openCollect", "interior_transform", "war_reportPush", "general_push", "army_push", "roleBuild_push", "roleCity_push", "facility_push", "roleRes_push", "skill_list", "skill_push", "chat_login", "chat_chat", "chat_history", "chat_join", "chat_exit", "chat_push"], "mappings": ";;;;;;;;;;;;;;8BAGMA,Y,GAAe;AACjBC,QAAAA,aAAa,EAAE,eADE;AAEjBC,QAAAA,cAAc,EAAE,gBAFC;AAGjBC,QAAAA,eAAe,EAAE,iBAHA;AAIjBC,QAAAA,gBAAgB,EAAC,UAJA;AAMjBC,QAAAA,WAAW,EAAE,aANI;AAOjBC,QAAAA,aAAa,EAAE,eAPE;AAQjBC,QAAAA,gBAAgB,EAAE,kBARD;AASjBC,QAAAA,WAAW,EAAE,aATI;AAUjBC,QAAAA,cAAc,EAAE,gBAVC;AAWjBC,QAAAA,eAAe,EAAE,iBAXA;AAYjBC,QAAAA,eAAe,EAAC,iBAZC;AAajBC,QAAAA,eAAe,EAAC,iBAbC;AAcjBC,QAAAA,aAAa,EAAC,eAdG;AAgBjBC,QAAAA,gBAAgB,EAAE,kBAhBD;AAiBjBC,QAAAA,mBAAmB,EAAE,qBAjBJ;AAkBjBC,QAAAA,gBAAgB,EAAE,kBAlBD;AAmBjBC,QAAAA,eAAe,EAAE,iBAnBA;AAoBjBC,QAAAA,iBAAiB,EAAE,mBApBF;AAqBjBC,QAAAA,kBAAkB,EAAE,oBArBH;AAuBjBC,QAAAA,eAAe,EAAE,iBAvBA;AAwBjBC,QAAAA,eAAe,EAAE,iBAxBA;AA2BjBC,QAAAA,kBAAkB,EAAE,oBA3BH;AA4BjBC,QAAAA,mBAAmB,EAAE,qBA5BJ;AA6BjBC,QAAAA,sBAAsB,EAAE,wBA7BP;AA8BjBC,QAAAA,oBAAoB,EAAE,sBA9BL;AA+BjBC,QAAAA,eAAe,EAAE,iBA/BA;AAiCjBC,QAAAA,eAAe,EAAE,iBAjCA;AAkCjBC,QAAAA,iBAAiB,EAAE,mBAlCF;AAmCjBC,QAAAA,eAAe,EAAE,iBAnCA;AAqCjBC,QAAAA,WAAW,EAAE,aArCI;AAsCjBC,QAAAA,UAAU,EAAE,YAtCK;AAuCjBC,QAAAA,YAAY,EAAE,cAvCG;AAwCjBC,QAAAA,cAAc,EAAE,gBAxCC;AAyCjBC,QAAAA,WAAW,EAAE,aAzCI;AA2CjBC,QAAAA,UAAU,EAAC,YA3CM;AA4CjBC,QAAAA,QAAQ,EAAC,UA5CQ;AA8CjBC,QAAAA,YAAY,EAAC,cA9CI;AA+CjBC,QAAAA,UAAU,EAAC,YA/CM;AAgDjBC,QAAAA,UAAU,EAAC,YAhDM;AAiDjBC,QAAAA,YAAY,EAAC,cAjDI;AAkDjBC,QAAAA,eAAe,EAAC,iBAlDC;AAmDjBC,QAAAA,aAAa,EAAC,eAnDG;AAoDjBC,QAAAA,YAAY,EAAC,cApDI;AAqDjBC,QAAAA,UAAU,EAAC,YArDM;AAsDjBC,QAAAA,UAAU,EAAC,YAtDM;AAuDjBC,QAAAA,aAAa,EAAC,eAvDG;AAwDjBC,QAAAA,cAAc,EAAC,gBAxDE;AAyDjBC,QAAAA,eAAe,EAAC,iBAzDC;AA0DjBC,QAAAA,UAAU,EAAC,YA1DM;AA2DjBC,QAAAA,SAAS,EAAC,WA3DO;AA4DjBC,QAAAA,gBAAgB,EAAE,iBA5DD;AA8DjBC,QAAAA,gBAAgB,EAAE,kBA9DD;AA+DjBC,QAAAA,oBAAoB,EAAE,sBA/DL;AAgEjBC,QAAAA,kBAAkB,EAAE,oBAhEH;AAkEjBC,QAAAA,cAAc,EAAC,gBAlEE;AAmEjBC,QAAAA,YAAY,EAAE,cAnEG;AAoEjBC,QAAAA,SAAS,EAAE,WApEM;AAqEjBC,QAAAA,cAAc,EAAC,gBArEE;AAsEjBC,QAAAA,aAAa,EAAC,eAtEG;AAuEjBC,QAAAA,aAAa,EAAC,eAvEG;AAwEjBC,QAAAA,YAAY,EAAC,cAxEI;AA0EjBC,QAAAA,UAAU,EAAC,YA1EM;AA2EjBC,QAAAA,UAAU,EAAC,YA3EM;AA6EjBC,QAAAA,UAAU,EAAC,YA7EM;AA8EjBC,QAAAA,SAAS,EAAC,WA9EO;AA+EjBC,QAAAA,YAAY,EAAC,cA/EI;AAgFjBC,QAAAA,SAAS,EAAC,WAhFO;AAiFjBC,QAAAA,SAAS,EAAC,WAjFO;AAkFjBC,QAAAA,SAAS,EAAC;AAlFO,O", "sourcesContent": ["// /**服务器接口配置*/\nimport { _decorator } from 'cc';\n\nconst ServerConfig = {\n    account_login: \"account.login\",\n    account_logout: \"account.logout\",\n    account_reLogin: \"account.reLogin\",\n    account_robLogin:\"robLogin\",\n    \n    role_create: \"role.create\",\n    role_roleList: \"role.roleList\",\n    role_enterServer: \"role.enterServer\",\n    role_myCity: \"role.myCity\",\n    role_myRoleRes: \"role.myRoleRes\",\n    role_myProperty: \"role.myProperty\",\n    role_upPosition:\"role.upPosition\",\n    role_posTagList:\"role.posTagList\",\n    role_opPosTag:\"role.opPosTag\",\n\n    nationMap_config: \"nationMap.config\",\n    nationMap_scanBlock: \"nationMap.scanBlock\",\n    nationMap_giveUp: \"nationMap.giveUp\",\n    nationMap_build: \"nationMap.build\",\n    nationMap_upBuild: \"nationMap.upBuild\",\n    nationMap_delBuild: \"nationMap.delBuild\",\n\n    city_facilities: \"city.facilities\",\n    city_upFacility: \"city.upFacility\",\n\n\n    general_myGenerals: \"general.myGenerals\",\n    general_drawGeneral: \"general.drawGeneral\",\n    general_composeGeneral: \"general.composeGeneral\",\n    general_addPrGeneral: \"general.addPrGeneral\",\n    general_convert: \"general.convert\",\n\n    general_upSkill: \"general.upSkill\",\n    general_downSkill: \"general.downSkill\",\n    general_lvSkill: \"general.lvSkill\",\n\n    army_myList: \"army.myList\",\n    army_myOne: \"army.myOne\",\n    army_dispose: \"army.dispose\",\n    army_conscript: \"army.conscript\",\n    army_assign: \"army.assign\",\n\n    war_report:\"war.report\",\n    war_read:\"war.read\",\n\n    union_create:\"union.create\",\n    union_join:\"union.join\",\n    union_list:\"union.list\",\n    union_member:\"union.member\",\n    union_applyList:\"union.applyList\",\n    union_dismiss:\"union.dismiss\",\n    union_verify:\"union.verify\",\n    union_exit:\"union.exit\",\n    union_kick:\"union.kick\",\n    union_appoint:\"union.appoint\",\n    union_abdicate:\"union.abdicate\",\n    union_modNotice:\"union.modNotice\",\n    union_info:\"union.info\",\n    union_log:\"union.log\",\n    union_apply_push: \"unionApply.push\",\n    \n    interior_collect: \"interior.collect\",\n    interior_openCollect: \"interior.openCollect\",\n    interior_transform: \"interior.transform\",\n\n    war_reportPush:\"warReport.push\",\n    general_push: \"general.push\",\n    army_push: \"army.push\",\n    roleBuild_push:\"roleBuild.push\",\n    roleCity_push:\"roleCity.push\",\n    facility_push:\"facility.push\",\n    roleRes_push:\"roleRes.push\",\n\n    skill_list:\"skill.list\",\n    skill_push:\"skill.push\",\n\n    chat_login:\"chat.login\",\n    chat_chat:\"chat.chat\",\n    chat_history:\"chat.history\",\n    chat_join:\"chat.join\",\n    chat_exit:\"chat.exit\",\n    chat_push:\"chat.push\",\n}\n\n\nexport { ServerConfig };\n"]}