{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/map/ui/CityAboutLogic.ts"], "names": ["_decorator", "Component", "Node", "Prefab", "instantiate", "ArmyCommand", "CityArmyItemLogic", "MapUICommand", "EventMgr", "AudioManager", "LogicEvent", "ccclass", "property", "CityAboutLogic", "onEnable", "initView", "on", "updateCityAddition", "onUpdateCityAdditon", "onDisable", "targetOff", "armyLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_armyComps", "i", "_armyCnt", "item", "armyItem", "parent", "comp", "getComponent", "order", "push", "cityId", "_cityData", "updateArmyList", "additon", "getInstance", "proxy", "getMyCityAddition", "armyList", "getArmyList", "length", "armyCnt", "isOpenedArmy", "setArmyData", "setData", "data", "qryCityFacilities", "onClickFacility", "instance", "playClick", "emit", "openFacility", "onClickClose", "node", "active", "closeCityAbout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAGvCC,MAAAA,W;;AAGAC,MAAAA,iB;;AACAC,MAAAA,Y;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;OAVH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAaTa,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACT,MAAD,C,oCAJb,MACqBU,cADrB,SAC4CZ,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,4CAMrB,CANqB;;AAAA,6CAOf,IAPe;;AAAA,8CAQN,EARM;AAAA;;AAUxCa,QAAAA,QAAQ,GAAS;AACvB,eAAKC,QAAL;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,wCAAWC,kBAAvB,EAA2C,KAAKC,mBAAhD,EAAqE,IAArE;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAESL,QAAAA,QAAQ,GAAS;AACvB,eAAKM,SAAL,CAAeC,iBAAf;AACA,eAAKC,UAAL,GAAkB,EAAlB;;AAEA,eAAK,IAAIC,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKC,QAAjC,EAA2CD,CAAC,EAA5C,EAAgD;AAC5C,gBAAIE,IAAI,GAAGtB,WAAW,CAAC,KAAKuB,QAAN,CAAtB;AACAD,YAAAA,IAAI,CAACE,MAAL,GAAc,KAAKP,SAAnB;AACA,gBAAIQ,IAAuB,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,uDAA9B;AACAD,YAAAA,IAAI,CAACE,KAAL,GAAaP,CAAC,GAAG,CAAjB;;AACA,iBAAKD,UAAL,CAAgBS,IAAhB,CAAqBH,IAArB;AACH;AACJ;;AAESX,QAAAA,mBAAmB,CAACe,MAAD,EAAuB;AAChD,cAAI,KAAKC,SAAL,CAAeD,MAAf,IAAyBA,MAA7B,EAAqC;AACjC,iBAAKE,cAAL;AACH;AACJ;;AAESA,QAAAA,cAAc,GAAS;AAC7B,cAAIC,OAAqB,GAAG;AAAA;AAAA,4CAAaC,WAAb,GAA2BC,KAA3B,CAAiCC,iBAAjC,CAAmD,KAAKL,SAAL,CAAeD,MAAlE,CAA5B;AACA,cAAIO,QAAoB,GAAG;AAAA;AAAA,0CAAYH,WAAZ,GAA0BC,KAA1B,CAAgCG,WAAhC,CAA4C,KAAKP,SAAL,CAAeD,MAA3D,CAA3B;;AACA,eAAK,IAAIT,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG,KAAKD,UAAL,CAAgBmB,MAA5C,EAAoDlB,CAAC,EAArD,EAAyD;AACrD,gBAAIA,CAAC,IAAIY,OAAO,CAACO,OAAjB,EAA0B;AACtB;AACA,mBAAKpB,UAAL,CAAgBC,CAAhB,EAAmBoB,YAAnB,CAAgC,KAAhC,EAAuC,KAAvC;AACH,aAHD,MAGO;AACH;AACA,mBAAKrB,UAAL,CAAgBC,CAAhB,EAAmBoB,YAAnB,CAAgC,IAAhC,EAAsC,KAAtC;;AACA,mBAAKrB,UAAL,CAAgBC,CAAhB,EAAmBqB,WAAnB,CAA+B,KAAKX,SAAL,CAAeD,MAA9C,EAAsDO,QAAQ,CAAChB,CAAD,CAA9D;AACH;AACJ;AACJ;;AAEMsB,QAAAA,OAAO,CAACC,IAAD,EAA0B;AACpC,eAAKb,SAAL,GAAiBa,IAAjB;AACA,eAAKZ,cAAL;AACA;AAAA;AAAA,4CAAaE,WAAb,GAA2BW,iBAA3B,CAA6C,KAAKd,SAAL,CAAeD,MAA5D;AACH;;AAGSgB,QAAAA,eAAe,GAAS;AAC9B;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,SAAtB,GAD8B,CAE9B;;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,YAAzB,EAAuC,KAAKnB,SAA5C;AACH;;AAGSoB,QAAAA,YAAY,GAAS;AAC3B;AAAA;AAAA,4CAAaJ,QAAb,CAAsBC,SAAtB;AACA,eAAKI,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AAEA;AAAA;AAAA,oCAASJ,IAAT,CAAc;AAAA;AAAA,wCAAWK,cAAzB,EAAyC,KAAKvB,SAA9C;AAEH;;AAzEiD,O;;;;;iBAEhC,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Component, Node, Prefab, instantiate } from 'cc';\nconst { ccclass, property } = _decorator;\n\nimport ArmyCommand from \"../../general/ArmyCommand\";\nimport { ArmyData } from \"../../general/ArmyProxy\";\nimport { MapCityData } from \"../MapCityProxy\";\nimport CityArmyItemLogic from \"./CityArmyItemLogic\";\nimport MapUICommand from \"./MapUICommand\";\nimport { CityAddition } from \"./MapUIProxy\";\nimport { EventMgr } from '../../utils/EventMgr';\nimport { AudioManager } from '../../common/AudioManager';\nimport { LogicEvent } from '../../common/LogicEvent';\n\n@ccclass('CityAboutLogic')\nexport default class CityAboutLogic extends Component {\n    @property(Node)\n    armyLayer: Node = null;\n    @property(Prefab)\n    armyItem: Prefab = null;\n\n    protected _armyCnt: number = 5;//队伍数量 固定值\n    protected _cityData: MapCityData = null;\n    protected _armyComps: CityArmyItemLogic[] = [];\n\n    protected onEnable(): void {\n        this.initView();\n        EventMgr.on(LogicEvent.updateCityAddition, this.onUpdateCityAdditon, this);\n    }\n\n    protected onDisable(): void {\n        EventMgr.targetOff(this);\n    }\n\n    protected initView(): void {\n        this.armyLayer.removeAllChildren();\n        this._armyComps = [];\n\n        for (let i: number = 0; i < this._armyCnt; i++) {\n            let item = instantiate(this.armyItem);\n            item.parent = this.armyLayer;\n            let comp: CityArmyItemLogic = item.getComponent(CityArmyItemLogic);\n            comp.order = i + 1;\n            this._armyComps.push(comp);\n        }\n    }\n\n    protected onUpdateCityAdditon(cityId: number): void {\n        if (this._cityData.cityId == cityId) {\n            this.updateArmyList();\n        }\n    }\n\n    protected updateArmyList(): void {\n        let additon: CityAddition = MapUICommand.getInstance().proxy.getMyCityAddition(this._cityData.cityId);\n        let armyList: ArmyData[] = ArmyCommand.getInstance().proxy.getArmyList(this._cityData.cityId);\n        for (let i: number = 0; i < this._armyComps.length; i++) {\n            if (i >= additon.armyCnt) {\n                //未开启\n                this._armyComps[i].isOpenedArmy(false, false);\n            } else {\n                //已开启\n                this._armyComps[i].isOpenedArmy(true, false);\n                this._armyComps[i].setArmyData(this._cityData.cityId, armyList[i]);\n            }\n        }\n    }\n\n    public setData(data: MapCityData): void {\n        this._cityData = data;\n        this.updateArmyList();\n        MapUICommand.getInstance().qryCityFacilities(this._cityData.cityId);\n    }\n\n\n    protected onClickFacility(): void {\n        AudioManager.instance.playClick();\n        //设施\n        EventMgr.emit(LogicEvent.openFacility, this._cityData);\n    }\n\n\n    protected onClickClose(): void {\n        AudioManager.instance.playClick();\n        this.node.active = false;\n\n        EventMgr.emit(LogicEvent.closeCityAbout, this._cityData);\n        \n    }\n}\n"]}