{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/common/LogicEvent.ts"], "names": ["LogicEvent"], "mappings": ";;;iBAEaA,U;;;;;;;;;;;;;;;4BAAAA,U,GAAN,MAAMA,UAAN,CAAiB,E;;sBAAXA,U,gBAEyB,Y;;sBAFzBA,U,cAGuB,W;;sBAHvBA,U,gBAIyB,a;;sBAJzBA,U,eAKwB,Y;;sBALxBA,U,iBAM0B,c;;sBAN1BA,U,iBAO0B,c;;sBAP1BA,U,aAQsB,U;;sBARtBA,U,gBAUyB,Y;;sBAVzBA,U,yBAWkC,qB;;sBAXlCA,U,mBAY4B,gB;;sBAZ5BA,U,mBAe4B,kB;;sBAf5BA,U,cAkBuB,W;;sBAlBvBA,U,aAmBsB,U;;sBAnBtBA,U,uBAoBgC,sB;;sBApBhCA,U,qBAqB8B,mB;;sBArB9BA,U,iBAsB0B,e;;sBAtB1BA,U,uBAuBgC,sB;;sBAvBhCA,U,gBAyByB,a;;sBAzBzBA,U,oBA0B6B,kB;;sBA1B7BA,U,eA2BwB,Y;;sBA3BxBA,U,kBA4B2B,e;;sBA5B3BA,U,iBA6B0B,c;;sBA7B1BA,U,iBA8B0B,c;;sBA9B1BA,U,iBA+B0B,c;;sBA/B1BA,U,gBAgCyB,a;;sBAhCzBA,U,uBAiCgC,qB;;sBAjChCA,U,iBAkC0B,c;;sBAlC1BA,U,mBAmC4B,iB;;sBAnC5BA,U,oBAoC6B,kB;;sBApC7BA,U,uBAqCgC,qB;;sBArChCA,U,kBAsC2B,e;;sBAtC3BA,U,qBAuC8B,mB;;sBAvC9BA,U,oBAwC6B,kB;;sBAxC7BA,U,oBAyC6B,kB;;sBAzC7BA,U,uBA0CgC,qB;;sBA1ChCA,U,sBA2C+B,qB;;sBA3C/BA,U,oBA4C6B,kB;;sBA5C7BA,U,qBA6C8B,kB;;sBA7C9BA,U,yBA8CkC,sB;;sBA9ClCA,U,wBA+CiC,sB;;sBA/CjCA,U,uBAgDgC,qB;;sBAhDhCA,U,iBAiD0B,c;;sBAjD1BA,U,eAkDwB,Y;;sBAlDxBA,U,gBAmDyB,a;;sBAnDzBA,U,mBAoD4B,gB;;sBApD5BA,U,uBAqDgC,sB;;sBArDhCA,U,wBAsDiC,sB;;sBAtDjCA,U,sBAuD+B,oB;;sBAvD/BA,U,wBAwDiC,sB;;sBAxDjCA,U,wBAyDiC,sB;;sBAzDjCA,U,uBA0DgC,qB;;sBA1DhCA,U,uBA2DgC,qB;;sBA3DhCA,U,mBA4D4B,gB;;sBA5D5BA,U,oBA6D6B,iB;;sBA7D7BA,U,sBA8D+B,oB;;sBA9D/BA,U,uBA+DgC,qB;;sBA/DhCA,U,mBAgE4B,gB;;sBAhE5BA,U,mBAiE4B,iB;;sBAjE5BA,U,gBAkEyB,a;;sBAlEzBA,U,iBAmE0B,e;;sBAnE1BA,U,yBAoEkC,uB;;sBApElCA,U,wBAqEiC,sB;;sBArEjCA,U,sBAsE+B,oB;;sBAtE/BA,U,eAuEwB,Y;;sBAvExBA,U,iBAwE0B,c;;sBAxE1BA,U,cAyEuB,W;;sBAzEvBA,U,oBA0E6B,kB;;sBA1E7BA,U,oBA2E6B,kB;;sBA3E7BA,U,iBA4E0B,c;;sBA5E1BA,U,wBA6EiC,sB;;sBA7EjCA,U,sBA8E+B,oB;;sBA9E/BA,U,kBA+E2B,e;;sBA/E3BA,U,uBAgFgC,qB;;sBAhFhCA,U,mBAiF4B,gB;;sBAjF5BA,U,0BAkFmC,sB;;sBAlFnCA,U,qBAmF8B,mB", "sourcesContent": ["\n\nexport class LogicEvent {\n\n    public static robLoginUI:string = \"robLogin<PERSON>\";\n    public static enterMap:string = \"enter_map\";\n    public static enterLogin:string = \"enter_login\";\n    public static showToast:string = \"show_toast\";\n    public static showWaiting:string = \"show_waiting\";\n    public static hideWaiting:string = \"hide_waiting\";\n    public static showTip:string = \"show_tip\";\n\n    public static createRole:string = \"createRole\";\n    public static enterServerComplete:string = \"enterServerComplete\";\n    public static loginComplete:string = \"login_complete\";\n\n    // 新增事件，用于解决循环依赖\n    public static getMyMainCity:string = \"get_my_main_city\";\n\n\n    public static touchMap:string = \"touch_map\";\n    public static moveMap:string = \"move_map\";\n    public static beforeScrollToMap:string = \"before_scroll_to_map\";\n    public static mapEenterChange:string = \"map_center_change\";\n    public static scrollToMap:string = \"scroll_to_map\";\n    public static mapShowAreaChange:string = \"map_show_area_change\";\n\n    public static updateArmy:string = \"update_army\";\n    public static updateArmyList:string = \"update_army_list\";\n    public static updateTag:string = \"update_tag\";\n    public static updateBuilds:string = \"update_builds\";\n    public static updateBuild:string = \"update_build\";\n    public static deleteBuild:string = \"delete_build\";\n    public static updateCitys:string = \"update_citys\";\n    public static updateCity:string = \"update_city\";\n    public static updateChatHistory:string = \"update_chat_history\";\n    public static unionChange:string = \"union_change\";\n    public static openCityAbout:string = \"open_city_about\";\n    public static closeCityAbout:string = \"close_city_about\";\n    public static openFortressAbout:string = \"open_fortress_about\";\n    public static openFacility:string = \"open_facility\";\n    public static openArmySetting:string = \"open_army_setting\";\n    public static upateMyRoleRes:string = \"upate_my_roleRes\";\n    public static openGeneralDes:string = \"open_general_des\";\n    public static openGeneralChoose:string = \"open_general_choose\";\n    public static openArmySelectUi:string = \"open_army_select_ui\";\n    public static openDrawResult:string = \"open_draw_result\";\n    public static interiorCollect:string = \"interior_collect\";\n    public static interiorOpenCollect:string = \"interior_openCollect\";\n    public static openGeneralConvert:string = \"open_general_convert\";\n    public static openGeneralRoster:string = \"open_general_roster\";\n    public static openGeneral:string = \"open_general\";\n    public static openSkill:string = \"open_skill\";\n    public static closeSkill:string = \"close_skill\";\n    public static openSkillInfo:string = \"open_skillInfo\";\n    public static closeArmyAelectUi:string = \"close_army_select_ui\";\n    public static updateCityAddition:string = \"update_city_addition\";\n    public static updateMyFacility:string = \"update_my_facility\";\n    public static updateMyFacilities:string = \"update_my_facilities\";\n    public static selectFacilityItem:string = \"select_facility_item\";\n    public static openGeneralSelect:string = \"open_general_select\";\n    public static openArmyConscript:string = \"open_army_conscript\";\n    public static chosedGeneral:string = \"chosed_general\";\n    public static generalConvert:string = \"general_convert\";\n    public static updateMyGenerals:string = \"update_my_generals\";\n    public static updateOneGenerals:string = \"update_one_generals\";\n    public static updateGeneral:string = \"update_general\";\n    public static skillListInfo:string = \"skill_list_info\";\n    public static closeUnion:string = \"close_union\";\n    public static openMyUnion:string = \"open_my_union\";\n    public static dismissUnionSuccess:string = \"dismiss_union_success\";\n    public static createUnionSuccess:string = \"create_union_success\";\n    public static updateUnionApply:string = \"update_union_apply\";\n    public static unionInfo:string = \"union_info\";\n    public static unionNotice:string = \"union_notice\";\n    public static unionLog:string = \"union_log\";\n    public static upateWarReport:string = \"upate_war_report\";\n    public static clickWarReport:string = \"click_war_report\";\n    public static closeReport:string = \"close_report\";\n    public static verifyUnionSuccess:string = \"verify_union_success\";\n    public static kickUnionSuccess:string = \"kick_union_success\";\n    public static unionAppoint:string = \"union_appoint\";\n    public static updateUnionMember:string = \"update_union_member\";\n    public static unionAbdicate:string = \"union_abdicate\";\n    public static clickUnionMemberItem:string = \"clickUnionMemberItem\";\n    public static updateUnionList:string = \"update_union_list\";\n   \n    \n}"]}