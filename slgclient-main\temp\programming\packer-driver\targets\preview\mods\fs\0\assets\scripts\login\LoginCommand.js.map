{"version": 3, "sources": ["file:///D:/CocosSLG/slgclient-main/assets/scripts/login/LoginCommand.ts"], "names": ["LoginCommand", "HttpConfig", "ServerConfig", "HttpManager", "NetManager", "Tools", "LoginProxy", "NetEvent", "MapCommand", "LocalCache", "DateUtil", "EventMgr", "Md5", "LogicEvent", "getInstance", "_instance", "destory", "onDestory", "constructor", "on", "ServerCheckLogin", "onServerConneted", "register", "name", "onRegister", "account_login", "onA<PERSON>unt<PERSON><PERSON><PERSON>", "role_enterServer", "onEnterServer", "account_reLogin", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role_create", "onRoleCreate", "account_logout", "onAccountLogout", "account_robLogin", "onAccountRobLogin", "chat_login", "onChatLogin", "targetOff", "console", "log", "emit", "robLoginUI", "data", "otherData", "code", "accountLogin", "username", "password", "setLoginValidation", "_proxy", "saveLoginData", "msg", "getSession", "loginComplete", "isLoadMap", "createRole", "setServerTime", "time", "saveEnterData", "enterMap", "enterServerComplete", "ServerHandShake", "loginData", "getLoginData", "roleData", "getRoleData", "session", "clear", "enterLogin", "proxy", "pwd", "encrypt", "params", "getUUID", "doGet", "url", "api_name", "send_data", "hardware", "send", "uid", "nick<PERSON><PERSON>", "sex", "sid", "headId", "chatLogin", "rid", "token", "nick_name"], "mappings": ";;;2KAcqBA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdZC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,U;;AACEC,MAAAA,U,kBAAAA,U;;AACFC,MAAAA,Q;;AACEC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,G,kBAAAA,G;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;yBAEYb,Y,GAAN,MAAMA,YAAN,CAAmB;AAC9B;AAEyB,eAAXc,WAAW,GAAiB;AACtC,cAAI,KAAKC,SAAL,IAAkB,IAAtB,EAA4B;AACxB,iBAAKA,SAAL,GAAiB,IAAIf,YAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKe,SAAZ;AACH;;AAEoB,eAAPC,OAAO,GAAY;AAC7B,cAAI,KAAKD,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeE,SAAf;;AACA,iBAAKF,SAAL,GAAiB,IAAjB;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH,SAjB6B,CAmB9B;;;AAGAG,QAAAA,WAAW,GAAG;AAAA,0CAFiB;AAAA;AAAA,yCAEjB;;AACV;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,oCAASC,gBAArB,EAAuC,KAAKC,gBAA5C,EAA8D,IAA9D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,wCAAWG,QAAX,CAAoBC,IAAhC,EAAsC,KAAKC,UAA3C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASL,EAAT,CAAY;AAAA;AAAA,4CAAaM,aAAzB,EAAwC,KAAKC,cAA7C,EAA6D,IAA7D;AACA;AAAA;AAAA,oCAASP,EAAT,CAAY;AAAA;AAAA,4CAAaQ,gBAAzB,EAA2C,KAAKC,aAAhD,EAA+D,IAA/D;AACA;AAAA;AAAA,oCAAST,EAAT,CAAY;AAAA;AAAA,4CAAaU,eAAzB,EAA0C,KAAKC,gBAA/C,EAAiE,IAAjE;AACA;AAAA;AAAA,oCAASX,EAAT,CAAY;AAAA;AAAA,4CAAaY,WAAzB,EAAsC,KAAKC,YAA3C,EAAyD,IAAzD;AACA;AAAA;AAAA,oCAASb,EAAT,CAAY;AAAA;AAAA,4CAAac,cAAzB,EAAyC,KAAKC,eAA9C,EAA+D,IAA/D;AACA;AAAA;AAAA,oCAASf,EAAT,CAAY;AAAA;AAAA,4CAAagB,gBAAzB,EAA2C,KAAKC,iBAAhD,EAAmE,IAAnE;AACA;AAAA;AAAA,oCAASjB,EAAT,CAAY;AAAA;AAAA,4CAAakB,UAAzB,EAAqC,KAAKC,WAA1C,EAAuD,IAAvD;AAEH;;AAEMrB,QAAAA,SAAS,GAAS;AACrB;AAAA;AAAA,oCAASsB,SAAT,CAAmB,IAAnB;AACH,SArC6B,CAuC9B;;;AACQH,QAAAA,iBAAiB,GAAQ;AAC7BI,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,wCAAWC,UAAzB;AACH;AAED;;;AACQnB,QAAAA,UAAU,CAACoB,IAAD,EAAYC,SAAZ,EAAkC;AAChDL,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoCG,IAApC,EAA0CC,SAA1C;;AACA,cAAID,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKC,YAAL,CAAkBF,SAAS,CAACG,QAA5B,EAAsCH,SAAS,CAACI,QAAhD;AACA;AAAA;AAAA,0CAAWC,kBAAX,CAA8BL,SAA9B;AACH;AACJ;AAED;;;AACQnB,QAAAA,cAAc,CAACkB,IAAD,EAAYC,SAAZ,EAAiC;AACnDL,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCG,IAAlC,EAAyCC,SAAzC;;AACA,cAAID,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB;AACA,iBAAKK,MAAL,CAAYC,aAAZ,CAA0BR,IAAI,CAACS,GAA/B;;AACA;AAAA;AAAA,0CAAWH,kBAAX,CAA8BL,SAA9B;AAGA,iBAAKlB,gBAAL,CAAsB,KAAKwB,MAAL,CAAYG,UAAZ,EAAtB;AACA;AAAA;AAAA,sCAASZ,IAAT,CAAc;AAAA;AAAA,0CAAWa,aAAzB,EAAwCX,IAAI,CAACE,IAA7C;AACH;AAEJ;AAED;;;AACQlB,QAAAA,aAAa,CAACgB,IAAD,EAAWY,SAAX,EAAoC;AACrDhB,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCG,IAAlC,EAAuCY,SAAvC,EADqD,CAErD;;AACA,cAAIZ,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB;AAAA;AAAA,sCAASJ,IAAT,CAAc;AAAA;AAAA,0CAAWe,UAAzB;AACA;AAAA;AAAA,sCAASC,aAAT,CAAuBd,IAAI,CAACS,GAAL,CAASM,IAAhC;AACH,WAHD,MAGO;AACH,gBAAGf,IAAI,CAACE,IAAL,IAAa,CAAhB,EAAkB;AACd,mBAAKK,MAAL,CAAYS,aAAZ,CAA0BhB,IAAI,CAACS,GAA/B;;AACA;AAAA;AAAA,wCAASK,aAAT,CAAuBd,IAAI,CAACS,GAAL,CAASM,IAAhC,EAFc,CAId;AACA;AAEC;;AACD,kBAAGH,SAAS,IAAI,IAAhB,EAAqB;AACjBhB,gBAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ;AACA;AAAA;AAAA,8CAAW3B,WAAX,GAAyB+C,QAAzB;AACA;AAAA;AAAA,0CAASnB,IAAT,CAAc;AAAA;AAAA,8CAAWoB,mBAAzB;AACH,eAJD,MAIK;AACD;AAAA;AAAA,0CAASpB,IAAT,CAAc;AAAA;AAAA,0CAASqB,eAAvB;AACH;AAEJ;AACJ;AACJ;AAED;;;AACQ1C,QAAAA,gBAAgB,GAAS;AAC7B;AACA,cAAI2C,SAAS,GAAG,KAAKb,MAAL,CAAYc,YAAZ,EAAhB;;AACA,cAAIC,QAAQ,GAAG,KAAKf,MAAL,CAAYgB,WAAZ,EAAf;;AACA3B,UAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCuB,SAArC,EAA+CE,QAA/C;;AAEA,cAAIF,SAAJ,EAAe;AACX,iBAAKnC,eAAL,CAAqBmC,SAAS,CAACI,OAA/B;AACH,WAFD,MAEK;AACD;AAAA;AAAA,sCAAS1B,IAAT,CAAc;AAAA;AAAA,sCAASqB,eAAvB;AACH;AACJ;AAED;;;AACQjC,QAAAA,gBAAgB,CAACc,IAAD,EAAkB;AACtC;AACAJ,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoCG,IAApC;;AACA,cAAGA,IAAI,CAACE,IAAL,IAAa,CAAhB,EAAkB;AACd;AACA,iBAAKnB,gBAAL,CAAsB,KAAKwB,MAAL,CAAYG,UAAZ,EAAtB,EAA+C,KAA/C;AACH;AACJ;AAED;;;AACQtB,QAAAA,YAAY,CAACY,IAAD,EAAkB;AAClC;AACA,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKnB,gBAAL,CAAsB,KAAKwB,MAAL,CAAYG,UAAZ,EAAtB;AACH;AACJ;AAID;;;AACQpB,QAAAA,eAAe,CAACU,IAAD,EAAkB;AACrC;AACA,cAAIA,IAAI,CAACE,IAAL,IAAa,CAAjB,EAAoB;AAChB,iBAAKK,MAAL,CAAYkB,KAAZ;;AACA;AAAA;AAAA,sCAAS3B,IAAT,CAAc;AAAA;AAAA,0CAAW4B,UAAzB;AACH;AACJ,SA1I6B,CA+I9B;;;AACQhC,QAAAA,WAAW,CAACM,IAAD,EAAiB;AAChCJ,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA2BG,IAA3B;AACH;;AAEe,YAAL2B,KAAK,GAAe;AAC3B,iBAAO,KAAKpB,MAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACW7B,QAAAA,QAAQ,CAACC,IAAD,EAAe0B,QAAf,EAAiC;AAE5C,cAAIuB,GAAG,GAAI;AAAA;AAAA,0BAAIC,OAAJ,CAAYxB,QAAZ,CAAX;AACA,cAAIyB,MAAM,GAAG,cAAcnD,IAAd,GACP,YADO,GACQiD,GADR,GAEP,YAFO,GAEQ;AAAA;AAAA,8BAAMG,OAAN,EAFrB;AAIAnC,UAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyBiC,MAAzB;AACA,cAAI7B,SAAS,GAAG;AAAEG,YAAAA,QAAQ,EAAEzB,IAAZ;AAAkB0B,YAAAA,QAAQ,EAAEA;AAA5B,WAAhB;AACA;AAAA;AAAA,0CAAYnC,WAAZ,GAA0B8D,KAA1B,CAAgC;AAAA;AAAA,wCAAWtD,QAAX,CAAoBC,IAApD,EAA0D;AAAA;AAAA,wCAAWD,QAAX,CAAoBuD,GAA9E,EAAmFH,MAAnF,EAA2F7B,SAA3F;AACH;AAED;AACJ;AACA;AACA;;;AACWE,QAAAA,YAAY,CAACxB,IAAD,EAAe0B,QAAf,EAAiC;AAEhD,cAAI6B,QAAQ,GAAG;AAAA;AAAA,4CAAarD,aAA5B;AACA,cAAI+C,GAAG,GAAI;AAAA;AAAA,0BAAIC,OAAJ,CAAYxB,QAAZ,CAAX;AAEA,cAAI8B,SAAS,GAAG;AACZxD,YAAAA,IAAI,EAAEuD,QADM;AAEZzB,YAAAA,GAAG,EAAE;AACDL,cAAAA,QAAQ,EAAEzB,IADT;AAED0B,cAAAA,QAAQ,EAAEuB,GAFT;AAGDQ,cAAAA,QAAQ,EAAE;AAAA;AAAA,kCAAML,OAAN;AAHT;AAFO,WAAhB;AASAnC,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BsC,SAA7B;AACA,cAAIlC,SAAS,GAAG;AAAEG,YAAAA,QAAQ,EAAEzB,IAAZ;AAAkB0B,YAAAA,QAAQ,EAAEA;AAA5B,WAAhB;AACA;AAAA;AAAA,wCAAWnC,WAAX,GAAyBmE,IAAzB,CAA8BF,SAA9B,EAAwClC,SAAxC;AACH;AAGD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACWd,QAAAA,WAAW,CAACmD,GAAD,EAAcC,QAAd,EAAgCC,GAAhC,EAAiDC,GAAjD,EAAkEC,MAAlE,EAAsF;AAAA,cAAtDF,GAAsD;AAAtDA,YAAAA,GAAsD,GAAxC,CAAwC;AAAA;;AAAA,cAArCC,GAAqC;AAArCA,YAAAA,GAAqC,GAAvB,CAAuB;AAAA;;AAAA,cAApBC,MAAoB;AAApBA,YAAAA,MAAoB,GAAH,CAAG;AAAA;;AACpG,cAAIR,QAAQ,GAAG;AAAA;AAAA,4CAAa/C,WAA5B;AACA,cAAIgD,SAAS,GAAG;AACZxD,YAAAA,IAAI,EAAEuD,QADM;AAEZzB,YAAAA,GAAG,EAAE;AACD6B,cAAAA,GAAG,EAAEA,GADJ;AAEDC,cAAAA,QAAQ,EAAEA,QAFT;AAGDC,cAAAA,GAAG,EAAEA,GAHJ;AAIDC,cAAAA,GAAG,EAAEA,GAJJ;AAKDC,cAAAA,MAAM,EAAEA;AALP;AAFO,WAAhB;AAUA;AAAA;AAAA,wCAAWxE,WAAX,GAAyBmE,IAAzB,CAA8BF,SAA9B;AACH;;AAGMpD,QAAAA,gBAAgB,CAACyC,OAAD,EAAiBZ,SAAjB,EAA2C;AAAA,cAA1BA,SAA0B;AAA1BA,YAAAA,SAA0B,GAAN,IAAM;AAAA;;AAC9D,cAAIsB,QAAQ,GAAG;AAAA;AAAA,4CAAanD,gBAA5B;AACA,cAAIoD,SAAS,GAAG;AACZxD,YAAAA,IAAI,EAAEuD,QADM;AAEZzB,YAAAA,GAAG,EAAE;AACDe,cAAAA,OAAO,EAAEA;AADR;AAFO,WAAhB;AAMA;AAAA;AAAA,wCAAWtD,WAAX,GAAyBmE,IAAzB,CAA8BF,SAA9B,EAAwCvB,SAAxC;AACH;AAED;AACJ;AACA;AACA;;;AACW3B,QAAAA,eAAe,CAACuC,OAAD,EAAkB;AACpC,cAAIU,QAAQ,GAAG;AAAA;AAAA,4CAAajD,eAA5B;AACA,cAAIkD,SAAS,GAAG;AACZxD,YAAAA,IAAI,EAAEuD,QADM;AAEZzB,YAAAA,GAAG,EAAE;AACDe,cAAAA,OAAO,EAAEA,OADR;AAEDY,cAAAA,QAAQ,EAAE;AAAA;AAAA,kCAAML,OAAN;AAFT;AAFO,WAAhB;AAOA;AAAA;AAAA,wCAAW7D,WAAX,GAAyBmE,IAAzB,CAA8BF,SAA9B;AACH;AAGD;AACJ;AACA;;;AACW9C,QAAAA,cAAc,GAAO;AACxB,cAAI6C,QAAQ,GAAG;AAAA;AAAA,4CAAa7C,cAA5B;AACA,cAAI8C,SAAS,GAAG;AACZxD,YAAAA,IAAI,EAAEuD,QADM;AAEZzB,YAAAA,GAAG,EAAE;AAFO,WAAhB;AAMA;AAAA;AAAA,wCAAWvC,WAAX,GAAyBmE,IAAzB,CAA8BF,SAA9B;AACH;;AAGMQ,QAAAA,SAAS,CAACC,GAAD,EAAaC,KAAb,EAA4BC,SAA5B,EAAuD;AAAA,cAA3BA,SAA2B;AAA3BA,YAAAA,SAA2B,GAAR,EAAQ;AAAA;;AACnE,cAAIZ,QAAQ,GAAG;AAAA;AAAA,4CAAazC,UAA5B;AACA,cAAI0C,SAAS,GAAG;AACZxD,YAAAA,IAAI,EAAEuD,QADM;AAEZzB,YAAAA,GAAG,EAAE;AACDmC,cAAAA,GAAG,EAACA,GADH;AAEDC,cAAAA,KAAK,EAACA,KAFL;AAGDN,cAAAA,QAAQ,EAACO;AAHR;AAFO,WAAhB;AASAlD,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BsC,SAA1B;AACA;AAAA;AAAA,wCAAWjE,WAAX,GAAyBmE,IAAzB,CAA8BF,SAA9B;AACH;;AAhR6B,O;;sBAAb/E,Y", "sourcesContent": ["import { HttpConfig } from \"../config/HttpConfig\";\nimport { ServerConfig } from \"../config/ServerConfig\";\nimport { HttpManager } from \"../network/http/HttpManager\";\nimport { NetManager } from \"../network/socket/NetManager\";\nimport { Tools } from \"../utils/Tools\";\nimport LoginProxy from \"./LoginProxy\";\nimport { NetEvent } from \"../network/socket/NetInterface\";\nimport MapCommand from \"../map/MapCommand\";\nimport { LocalCache } from \"../utils/LocalCache\";\nimport DateUtil from \"../utils/DateUtil\";\nimport { EventMgr } from \"../utils/EventMgr\";\nimport { Md5 } from \"../libs/crypto/md5\";\nimport { LogicEvent } from \"../common/LogicEvent\";\n\nexport default class LoginCommand {\n    //单例\n    protected static _instance: LoginCommand;\n    public static getInstance(): LoginCommand {\n        if (this._instance == null) {\n            this._instance = new LoginCommand();\n        }\n        return this._instance;\n    }\n\n    public static destory(): boolean {\n        if (this._instance) {\n            this._instance.onDestory();\n            this._instance = null;\n            return true;\n        }\n        return false;\n    }\n\n    //数据model\n    protected _proxy: LoginProxy = new LoginProxy();\n\n    constructor() {\n        EventMgr.on(NetEvent.ServerCheckLogin, this.onServerConneted, this);\n        EventMgr.on(HttpConfig.register.name, this.onRegister, this);\n        EventMgr.on(ServerConfig.account_login, this.onAccountLogin, this);\n        EventMgr.on(ServerConfig.role_enterServer, this.onEnterServer, this);\n        EventMgr.on(ServerConfig.account_reLogin, this.onAccountRelogin, this);\n        EventMgr.on(ServerConfig.role_create, this.onRoleCreate, this);\n        EventMgr.on(ServerConfig.account_logout, this.onAccountLogout, this);\n        EventMgr.on(ServerConfig.account_robLogin, this.onAccountRobLogin, this)\n        EventMgr.on(ServerConfig.chat_login, this.onChatLogin, this)\n\n    }\n\n    public onDestory(): void {\n        EventMgr.targetOff(this);\n    }\n\n    //抢登录\n    private onAccountRobLogin(): void{\n        console.log(\"onAccountRobLogin\")\n        EventMgr.emit(LogicEvent.robLoginUI);\n    }\n\n    /**注册回调*/\n    private onRegister(data: any, otherData: any): void {\n        console.log(\"LoginProxy register:\", data, otherData);\n        if (data.code == 0) {\n            this.accountLogin(otherData.username, otherData.password);\n            LocalCache.setLoginValidation(otherData);\n        }\n    }\n\n    /**登录回调*/\n    private onAccountLogin(data: any, otherData:any): void {\n        console.log(\"LoginProxy  login:\", data , otherData);\n        if (data.code == 0) {\n            // this._proxy.loginData = data.msg;\n            this._proxy.saveLoginData(data.msg);\n            LocalCache.setLoginValidation(otherData);\n\n\n            this.role_enterServer(this._proxy.getSession());           \n            EventMgr.emit(LogicEvent.loginComplete, data.code);\n        }\n        \n    }\n\n    /**进入服务器回调*/\n    private onEnterServer(data: any,isLoadMap:boolean): void {\n        console.log(\"LoginProxy  enter:\", data,isLoadMap);\n        //没有创建打开创建\n        if (data.code == 9) {\n            EventMgr.emit(LogicEvent.createRole);\n            DateUtil.setServerTime(data.msg.time);\n        } else {\n            if(data.code == 0){\n                this._proxy.saveEnterData(data.msg);\n                DateUtil.setServerTime(data.msg.time);\n\n                // var roleData = this._proxy.getRoleData();\n                // this.chatLogin(roleData.rid, data.msg.token, roleData.nickName);\n\n                 //进入游戏\n                if(isLoadMap == true){\n                    console.log(\"enterServerComplete\");\n                    MapCommand.getInstance().enterMap();\n                    EventMgr.emit(LogicEvent.enterServerComplete);\n                }else{\n                    EventMgr.emit(NetEvent.ServerHandShake);\n                }\n\n            }\n        }\n    }\n\n    /**重连回调*/\n    private onServerConneted(): void {\n        //重新连接成功 重新登录\n        var loginData = this._proxy.getLoginData();\n        var roleData = this._proxy.getRoleData();\n        console.log(\"LoginProxy  conneted:\", loginData,roleData);\n        \n        if (loginData) {\n            this.account_reLogin(loginData.session);\n        }else{\n            EventMgr.emit(NetEvent.ServerHandShake);\n        }\n    }\n\n    /**重新登录回调回调*/\n    private onAccountRelogin(data: any): void {\n        //断线重新登录\n        console.log(\"LoginProxy  relogin:\", data);\n        if(data.code == 0){\n            // EventMgr.emit(NetEvent.ServerHandShake);\n            this.role_enterServer(this._proxy.getSession(),false);\n        }\n    }\n\n    /**创建角色回调*/\n    private onRoleCreate(data: any): void {\n        //重换成功再次调用\n        if (data.code == 0) {\n            this.role_enterServer(this._proxy.getSession());\n        }\n    }\n\n\n    \n    /**登出回调*/\n    private onAccountLogout(data: any): void {\n        //重换成功再次调用\n        if (data.code == 0) {\n            this._proxy.clear();\n            EventMgr.emit(LogicEvent.enterLogin);\n        }\n    }\n\n\n\n\n    //聊天登录\n    private onChatLogin(data: any): void{\n        console.log(\"onChatLogin:\",data);\n    }\n\n    public get proxy(): LoginProxy {\n        return this._proxy;\n    }\n\n    /**\n     * register\n     * @param data \n     */\n    public register(name: string, password: string) {\n\n        var pwd =  Md5.encrypt(password);\n        var params = \"username=\" + name\n            + \"&password=\" + pwd\n            + \"&hardware=\" + Tools.getUUID();\n\n        console.log(\"register:\", params);\n        var otherData = { username: name, password: password };\n        HttpManager.getInstance().doGet(HttpConfig.register.name, HttpConfig.register.url, params, otherData);\n    }\n\n    /**\n     * login\n     * @param data \n     */\n    public accountLogin(name: string, password: string) {\n        \n        var api_name = ServerConfig.account_login;\n        var pwd =  Md5.encrypt(password);\n\n        var send_data = {\n            name: api_name,\n            msg: {\n                username: name,\n                password: pwd,\n                hardware: Tools.getUUID()\n            }\n        };\n\n        console.log(\"accountLogin:\", send_data);\n        var otherData = { username: name, password: password };\n        NetManager.getInstance().send(send_data,otherData);\n    }\n\n\n    /**\n     * create\n     * @param uid \n     * @param nickName \n     * @param sex \n     * @param sid \n     * @param headId \n     */\n    public role_create(uid: string, nickName: string, sex: number = 0, sid: number = 0, headId: number = 0) {\n        var api_name = ServerConfig.role_create;\n        var send_data = {\n            name: api_name,\n            msg: {\n                uid: uid,\n                nickName: nickName,\n                sex: sex,\n                sid: sid,\n                headId: headId\n            }\n        };\n        NetManager.getInstance().send(send_data);\n    }\n\n\n    public role_enterServer(session: string,isLoadMap:boolean = true) {\n        var api_name = ServerConfig.role_enterServer;\n        var send_data = {\n            name: api_name,\n            msg: {\n                session: session,\n            }\n        };\n        NetManager.getInstance().send(send_data,isLoadMap);\n    }\n\n    /**\n     * 重新登录\n     * @param session \n     */\n    public account_reLogin(session: string) {\n        var api_name = ServerConfig.account_reLogin;\n        var send_data = {\n            name: api_name,\n            msg: {\n                session: session,\n                hardware: Tools.getUUID()\n            }\n        };\n        NetManager.getInstance().send(send_data);\n    }\n\n\n    /**\n     * logout\n     */\n    public account_logout():void{\n        var api_name = ServerConfig.account_logout;\n        var send_data = {\n            name: api_name,\n            msg: {\n\n            }\n        };\n        NetManager.getInstance().send(send_data);\n    }\n\n\n    public chatLogin(rid:number, token: string, nick_name:string = ''):void{\n        var api_name = ServerConfig.chat_login;\n        var send_data = {\n            name: api_name,\n            msg: {\n                rid:rid,\n                token:token,\n                nickName:nick_name\n            }\n        };\n\n        console.log(\"send_data:\", send_data);\n        NetManager.getInstance().send(send_data);\n    }\n}\n"]}